/**********************************************************************/
/**
 * @file yx_debug.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-11
 * @version V1.0
 * @brief 调试打印接口
 **********************************************************************/
#ifndef YX_DEBUG_H
#define YX_DEBUG_H

#include "yx_type.h"

/**
 * @addtogroup yx_debug
 * @{
 */

#ifndef YX_DEBUG
#    define YX_DEBUG 0x1F /**< 如果没有定义 YX_DEBUG，则默认开启所有调试信息 */
#endif

#define YX_DBG_TO_RS485 0 /**< 调试信息输出到RS485 */
#define YX_DBG_TO_DEBUG 0 /**< 调试信息输出到DEBUG */

#if defined(YX_DEBUG) && (YX_DEBUG & 0x01)
#    define YX_LOGD(fmt, ...) yx_debug_printf("D", __FILE__, __LINE__, fmt, ##__VA_ARGS__) /**< 调试信息字符串打印 */
#    define YX_LOGD_HEX(hex, len) yx_debug_printf_hex(hex, len, "D", __FILE__, __LINE__)   /**< 调试信息hex打印 */
#    define YX_LOGD_MEM(mem, len) yx_debug_printf_mem(mem, len, "D", __FILE__, __LINE__)   /**< 调试信息字符数据打印 */
#else
#    define YX_LOGD(fmt, ...)
#    define YX_LOGD_HEX(hex, len)
#    define YX_LOGD_MEM(mem, len)
#endif

#if defined(YX_DEBUG) && (YX_DEBUG & 0x02)
#    define YX_LOGI(fmt, ...) yx_debug_printf("I", __FILE__, __LINE__, fmt, ##__VA_ARGS__) /**< 一般信息字符串打印 */
#    define YX_LOGI_HEX(hex, len) yx_debug_printf_hex(hex, len, "I", __FILE__, __LINE__)   /**< 一般信息hex打印 */
#    define YX_LOGI_MEM(mem, len) yx_debug_printf_mem(mem, len, "I", __FILE__, __LINE__)   /**< 一般信息字符数据打印 */
#else
#    define YX_LOGI(fmt, ...)
#    define YX_LOGI_HEX(hex, len)
#    define YX_LOGI_MEM(mem, len)
#endif

#if defined(YX_DEBUG) && (YX_DEBUG & 0x04)
#    define YX_LOGW(fmt, ...) yx_debug_printf("W", __FILE__, __LINE__, fmt, ##__VA_ARGS__) /**< 告警信息字符串打印 */
#    define YX_LOGW_HEX(hex, len) yx_debug_printf_hex(hex, len, "W", __FILE__, __LINE__)   /**< 告警信息hex打印 */
#    define YX_LOGW_MEM(mem, len) yx_debug_printf_mem(mem, len, "W", __FILE__, __LINE__)   /**< 告警信息字符数据打印 */
#else
#    define YX_LOGW(fmt, ...)
#    define YX_LOGW_HEX(hex, len)
#    define YX_LOGW_MEM(mem, len)
#endif

#if defined(YX_DEBUG) && (YX_DEBUG & 0x08)
#    define YX_LOGE(fmt, ...) yx_debug_printf("E", __FILE__, __LINE__, fmt, ##__VA_ARGS__) /**< 异常信息字符串打印 */
#    define YX_LOGE_HEX(hex, len) yx_debug_printf_hex(hex, len, "E", __FILE__, __LINE__)   /**< 异常信息hex打印 */
#    define YX_LOGE_MEM(mem, len) yx_debug_printf_mem(mem, len, "E", __FILE__, __LINE__)   /**< 异常信息字符数据打印 */
#else
#    define YX_LOGE(fmt, ...)
#    define YX_LOGE_HEX(hex, len)
#    define YX_LOGE_MEM(mem, len)
#endif

#if defined(YX_DEBUG) && (YX_DEBUG & 0x10)
#    define YX_FUNC_ENTER yx_debug_printf("FUNC", __FILE__, __LINE__, "%s enter", __func__); /**< 函数进入打印 */
#    define YX_FUNC_LEAVE yx_debug_printf("FUNC", __FILE__, __LINE__, "%s leave", __func__); /**< 函数离开打印 */
#else
#    define YX_FUNC_ENTER
#    define YX_FUNC_LEAVE
#endif
/** @} */  // 结束 yx_debug 组

/**
 * @brief 实现与printf类似的打印输出功能
 * @ingroup yx_debug
 * @param level 打印等级标签
 * @param file 文件名
 * @param line 行号
 * @param fmt 打印格式, 参见printf使用说明
 * @return 无
 */
VOID yx_debug_printf(CHAR* level, CHAR* file, INT32U line, const CHAR* fmt, ...);

/**
 * @brief 将缓冲区中的数据按hex格式输出
 * @ingroup yx_debug
 * @param ptr 数据缓冲区
 * @param len 数据缓冲区长度
 * @param level 打印等级标签
 * @param file 文件名
 * @param line 行号
 * @return 无
 */
VOID yx_debug_printf_hex(const INT8U* ptr, INT16U len, CHAR* level, CHAR* file, INT32U line);

/**
 * @brief 将字符数组输出
 * @ingroup yx_debug
 * @param ptr 数据缓冲区
 * @param len 数据缓冲区长度
 * @param level 打印等级标签
 * @param file 文件名
 * @param line 行号
 * @return 无
 */
VOID yx_debug_printf_mem(const INT8U* ptr, INT16U len, CHAR* level, char* file, INT32U line);

/**
 * @brief 注册调试打印回调函数
 * @param callback 回调函数指针
 *        - level_set: 需要的日志等级（bit0-debug，bit1-info，bit2-warning，bit3-error，bit4-func，按位控制，31全开）
 *        - data: 日志内容
 *        - len: 日志长度
 * @return 注册成功返回 0，失败返回 -1
 */
INT32S yx_debug_reg_callback(INT32U level_set, VOID (*print_cb)(CHAR* level, INT8U* data, INT16U len));

/**
 * @brief 初始化调试模块
 * @return 成功返回 0，失败返回 -1
 */
INT32S yx_debug_init(VOID);

/**
 * @brief 反初始化调试模块
 * @return 成功返回 0，失败返回 -1
 */
INT32S yx_debug_uninit(VOID);

#endif
