/**********************************************************************/
/**
 * @file yx_ia_crypto.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-15
 * @version V1.0
 * @brief 加解密模块接口适配
 **********************************************************************/
#ifndef YX_IA_CRYPTO_H
#define YX_IA_CRYPTO_H

#include "yx_type.h"
#define IA_CRYPTO_ERRNO            ((INT32U)(0x8000 + 0x61) << 16)

/**
 * @brief 错误码枚举
 * @ingroup ia_crypto
 */
typedef enum {
    IA_CRYPTO_SUUCESS = RTN_OK,                                /**< 成功 */
    IA_CRYPTO_INVALID_PARAM_ERR   = IA_CRYPTO_ERRNO | 0x01,    /**< 无效参数 */
    IA_CRYPTO_MISSING_PARAM_ERR   = IA_CRYPTO_ERRNO | 0x02,    /**< 缺失参数 */
    IA_CRYPTO_ADAPTER_PARAM_ERR   = IA_CRYPTO_ERRNO | 0x03,    /**< 适配错误 */

    IA_CRYPTO_MALLOC_FAILED       = IA_CRYPTO_ERRNO | 0x10,    /**< 内存失败 */
    IA_CRYPTO_EXECUTE_FAILED      = IA_CRYPTO_ERRNO | 0x11,    /**< 执行失败 */

    IA_CRYPTO_CFGTASK_FAILED      = IA_CRYPTO_ERRNO | 0x20,    /**< 任务失败 */
    IA_CRYPTO_CFGTYPE_FAILED      = IA_CRYPTO_ERRNO | 0x21,    /**< 类型失败 */
    IA_CRYPTO_CFGMODE_FAILED      = IA_CRYPTO_ERRNO | 0x22,    /**< 模式失败 */
    IA_CRYPTO_CFG_KEY_FAILED      = IA_CRYPTO_ERRNO | 0x23,    /**< 密钥失败 */
    IA_CRYPTO_CFG_PD_FAILED       = IA_CRYPTO_ERRNO | 0x24,    /**< 填充失败 */
    IA_CRYPTO_CFG_IV_FAILED       = IA_CRYPTO_ERRNO | 0x25,    /**< 矢量失败 */

    //密文相关处理
    IA_CRYPTO_CIPHER_FAILED       = IA_CRYPTO_ERRNO | 0x30,    /**< 密文失败 */
    IA_CRYPTO_MD_FAILED           = IA_CRYPTO_ERRNO | 0x40,    /**< 摘要失败 */
    IA_CRYPTO_CTRDRBG_FAILED      = IA_CRYPTO_ERRNO | 0x50,    /**< 伪随机数失败 */
} ia_crypto_errno_e;

/**
 * @brief 加解密句柄类型定义
 * @ingroup ia_crypto
 */
typedef void* yx_crypto_handler;       /**< 加解密句柄 */

/**
 * @brief 加解密任务类型枚举定义
 * @ingroup ia_crypto
 */
typedef enum {
    CRYPTO_TASK_NULL = 0,
	CRYPTO_TASK_PEMKEY,             /**< 非对称 */
	CRYPTO_TASK_CIPHER,             /**< 对称 */
	CRYPTO_TASK_HASH,               /**< 哈希 */
	CRYPTO_TASK_HMAC,               /**< 哈希 */
    CRYPTO_TASK_MAX
} yx_ia_crypto_task_e;

/**
 * @brief 加解密密文类型枚举定义
 * @ingroup ia_crypto
 */
typedef enum {
    CRYPTO_CIPHER_TYPE_NULL = 0,
    CRYPTO_CIPHER_TYPE_AES,         /**< AES */
    CRYPTO_CIPHER_TYPE_DES,         /**< DES */
    CRYPTO_CIPHER_TYPE_3DES,        /**< 3DES */
    CRYPTO_CIPHER_TYPE_MAX
} yx_ia_crypto_type_e;

/**
 * @brief 加解密摘要类型枚举定义
 * @ingroup ia_crypto
 */
typedef enum {
	CRYPTO_MD_MD5 = 0,              /**< MD5 */
	CRYPTO_MD_SHA1,                 /**< SHA1 */
	CRYPTO_MD_SHA224,               /**< SHA224 */
	CRYPTO_MD_SHA256,               /**< SHA256 */
	CRYPTO_MD_SHA384,               /**< SHA384 */
	CRYPTO_MD_SHA512,               /**< SHA512 */
    CRYPTO_MD_TYPE_MAX
} yx_ia_crypto_md_type_e;

/**
 * @brief 加解密密文模式枚举定义
 * @ingroup ia_crypto
 */
typedef enum {
    CRYPTO_CIPHER_MODE_ECB = 0,     /**< ECB */
    CRYPTO_CIPHER_MODE_CBC,         /**< CBC */
    CRYPTO_CIPHER_MODE_CFB,         /**< CFB */
    CRYPTO_CIPHER_MODE_OFB,         /**< OFB */
    CRYPTO_CIPHER_MODE_CTR,         /**< CTR */
    CRYPTO_CIPHER_MODE_MAX
} yx_ia_crypto_mode_e;

/**
 * @brief 加解密密文填充枚举定义
 * @ingroup ia_crypto
 */
typedef enum {
    CRYPTO_PADDING_PKCS7 = 0,       /**< PKCS7 */
    CRYPTO_PADDING_ONE_AND_ZEROS,   /**< ONE_AND_ZEROS */
    CRYPTO_PADDING_ZEROS_AND_LEN,   /**< ZEROS_AND_LEN */
    CRYPTO_PADDING_ZEROS,           /**< ZEROS */
    CRYPTO_PADDING_MAX
} yx_ia_crypto_padding_e;

/**
 * @brief 加解密密钥字节枚举定义
 * @ingroup ia_crypto
 */
typedef enum {
    CRYPTO_KEY_BYTE0  = 0,
    CRYPTO_KEY_BYTE8  = 8,          /**< 8字节密钥 */
    CRYPTO_KEY_BYTE16 = 16,         /**< 16字节密钥 */
    CRYPTO_KEY_BYTE24 = 24,         /**< 24字节密钥 */
    CRYPTO_KEY_BYTE32 = 32,         /**< 32字节密钥 */
    CRYPTO_KEY_BYTE48 = 48,         /**< 48字节密钥 */
    CRYPTO_KEY_BYTE64 = 64,         /**< 64字节密钥 */
    CRYPTO_KEY_BYTE_MAX
} yx_ia_crypto_key_byte_e;

/**
 * @brief 加解密伪随机数类型枚举定义
 * @ingroup ia_crypto
 */
typedef enum {
    CRYPTO_TLS_PRF_NONE,            /**< 无 */
    CRYPTO_TLS_PRF_SHA256,          /**< SHA256 */
    CRYPTO_TLS_PRF_SHA384,          /**< SHA384 */
	CRYPTO_TLS_PRF_TYPE_MAX
} yx_ia_crypto_tls_prf_type_e;

/**
 * @brief 创建加解密处理器
 * @ingroup ia_crypto
 * @param[in] task 任务类型 @see yx_ia_crypto_task_e
 * @return yx_crypto_handler
 * @retval 非NULL 处理器句柄
 * @retval NULL 创建失败
 */
yx_crypto_handler yx_ia_crypto_create(INT32U task);

/**
 * @brief 设置密码算法类型
 * @ingroup ia_crypto
 * @param[in] hdl 处理器句柄
 * @param[in] type 算法类型 @see yx_ia_crypto_type_e
 * @retval IA_CRYPTO_SUUCESS(0):成功 其它值:失败 @see ia_crypto_errno_e
 */
INT32S yx_ia_crypto_set_type(yx_crypto_handler hdl, INT32U type);

/**
 * @brief 设置哈希算法类型
 * @ingroup ia_crypto
 * @param[in] hdl 处理器句柄
 * @param[in] type 哈希类型 @see yx_ia_crypto_md_type_e
 * @retval IA_CRYPTO_SUUCESS(0):成功 其它值:失败 @see ia_crypto_errno_e
 */
INT32S yx_ia_crypto_set_hash_type(yx_crypto_handler hdl, INT32U type);

/**
 * @brief 设置加密模式
 * @ingroup ia_crypto
 * @param[in] hdl 处理器句柄
 * @param[in] mode 加密模式 @see yx_ia_crypto_mode_e
 * @retval IA_CRYPTO_SUUCESS(0):成功 其它值:失败 @see ia_crypto_errno_e
 */
INT32S yx_ia_crypto_set_mode(yx_crypto_handler hdl, INT32U mode);

/**
 * @brief 设置填充模式
 * @ingroup ia_crypto
 * @param[in] hdl 处理器句柄
 * @param[in] padding 填充模式 @see yx_ia_crypto_padding_e
 * @retval IA_CRYPTO_SUUCESS(0):成功 其它值:失败 @see ia_crypto_errno_e
 */
INT32S yx_ia_crypto_set_padding(yx_crypto_handler hdl, INT32U padding);

/**
 * @brief 设置密钥
 * @ingroup ia_crypto
 * @param[in] hdl 处理器句柄
 * @param[in] key 密钥数据指针
 * @param[in] keybyte 密钥长度（字节）
 * @retval IA_CRYPTO_SUUCESS(0):成功 其它值:失败 @see ia_crypto_errno_e
 */
INT32S yx_ia_crypto_set_key(yx_crypto_handler hdl, INT8U *key, INT32U keybyte);

/**
 * @brief 设置初始向量
 * @ingroup ia_crypto
 * @param[in] hdl 处理器句柄
 * @param[in] iv 初始向量指针
 * @param[in] ivsize 向量长度（必须为16字节）
 * @retval IA_CRYPTO_SUUCESS(0):成功 其它值:失败 @see ia_crypto_errno_e
 */
INT32S yx_ia_crypto_set_iv(yx_crypto_handler hdl, INT8U *iv, INT8U ivsize);

/**
 * @brief 执行加密操作
 * @ingroup ia_crypto
 * @param[in] hdl 处理器句柄
 * @param[in] src 源数据指针
 * @param[in] srclen 源数据长度
 * @param[out] dst 目标缓冲区指针
 * @param[in,out] dstlen 输入时表示目标缓冲区最大长度，输出时返回实际数据长度
 * @retval IA_CRYPTO_SUUCESS(0):成功 其它值:失败 @see ia_crypto_errno_e
 */
INT32S yx_ia_crypto_encrypt(yx_crypto_handler hdl, INT8U *src, INT32U srclen, 
                           INT8U *dst, INT32U *dstlen);

/**
 * @brief 执行解密操作
 * @ingroup ia_crypto
 * @param[in] hdl 处理器句柄
 * @param[in] src 源数据指针
 * @param[in] srclen 源数据长度
 * @param[out] dst 目标缓冲区指针
 * @param[in,out] dstlen 输入时表示目标缓冲区最大长度，输出时返回实际数据长度
 * @retval IA_CRYPTO_SUUCESS(0):成功 其它值:失败 @see ia_crypto_errno_e
 */
INT32S yx_ia_crypto_decrypt(yx_crypto_handler hdl, INT8U *src, INT32U srclen,
                           INT8U *dst, INT32U *dstlen);

/**
 * @brief 计算哈希值
 * @ingroup ia_crypto
 * @param[in] hdl 处理器句柄
 * @param[in] src 源数据指针
 * @param[in] srclen 源数据长度
 * @param[out] dst 目标缓冲区指针
 * @param[out] dstlen 输出哈希值长度
 * @retval IA_CRYPTO_SUUCESS(0):成功 其它值:失败 @see ia_crypto_errno_e
 */
INT32S yx_ia_crypto_calc_hash(yx_crypto_handler hdl, INT8U *src, INT32U srclen,
                             INT8U *dst, INT32U *dstlen);

/**
 * @brief 销毁加解密处理器
 * @ingroup ia_crypto
 * @param[in] phdl 处理器句柄指针（二级指针）
 * @retval IA_CRYPTO_SUUCESS(0):成功 其它值:失败 @see ia_crypto_errno_e
 */
INT32S yx_ia_crypto_delete(yx_crypto_handler *phdl);

/**
 * @brief 生成TLS伪随机函数
 * @ingroup ia_crypto
 * @param[in] type PRF类型 @see yx_ia_crypto_tls_prf_e
 * @param[in] secret 密钥数据指针
 * @param[in] slen 密钥长度
 * @param[in] label 标签字符串
 * @param[in] random 随机数指针
 * @param[in] rlen 随机数长度
 * @param[out] output 输出缓冲区指针
 * @param[in] dlen 期望输出长度
 * @retval IA_CRYPTO_SUUCESS(0):成功 其它值:失败 @see ia_crypto_errno_e
 */
INT32S yx_ia_crypto_generate_tls_prf(INT32U type, const INT8U *secret, INT32U slen,
                                    const char *label, const INT8U *random, INT32U rlen,
                                    INT8U *output, INT32U dlen);

/**
 * @brief 生成随机数
 * @ingroup ia_crypto
 * @param[in] label 随机数标签（可选）
 * @param[out] output 输出缓冲区指针
 * @param[in] dlen 期望随机数长度
 * @retval IA_CRYPTO_SUUCESS(0):成功 其它值:失败 @see ia_crypto_errno_e
 */
INT32S yx_ia_crypto_generate_random(const char *label, INT8U *output, INT32U dlen);

#endif /* YX_IA_CRYPTO_H */