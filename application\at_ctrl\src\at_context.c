/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "at_context.h"
#include "at_com.h"
#include "at_ctl.h"
#include "xy_system.h"

/*******************************************************************************
 *						   Global variable definitions						   *
 ******************************************************************************/
/*at_send_wait_rsp接口使用，供多应用线程同时调用以创建动态的临时上下文*/
at_context_dync_t *g_at_ctx_dync_head = NULL;
osMutexId_t g_at_dync_ctx_mux = NULL;
/*AT框架默认存在的物理通道上下文，通过at_tty_ctx_init注册即可，但物理通道自身支持热插拔*/
at_context_t g_at_tty_ctx[AT_FD_MAX] = {0};

/*******************************************************************************
 *						Global function implementations 					   *
 ******************************************************************************/
void reset_ctx(at_context_t *ctx)
{
    memset(ctx->at_cmd_prefix, 0, AT_CMD_PREFIX);
	ctx->at_type = AT_CMD_INVALID;
	ctx->at_proc = NULL;

	if (ctx->at_param != NULL)
	{
		xy_free(ctx->at_param);
		ctx->at_param = NULL;
	}

    ctx->state = REQ_IDLE;
}

/*只有应用线程使用at_send_wait_rsp时，才准动态注册；真实的物理通道皆静态注册，包括USB通道*/
bool regist_at_dync_ctx(at_context_t *ctx)
{
	xy_assert(ctx != NULL);

	osMutexAcquire(g_at_dync_ctx_mux, osWaitForever);

	at_context_dync_t *temp = xy_malloc(sizeof(at_context_dync_t));
	temp->node = ctx;
	temp->next = g_at_ctx_dync_head;
	g_at_ctx_dync_head = temp;

	osMutexRelease(g_at_dync_ctx_mux);
	return 0;
}

/*只有动态上下文才行销毁，例如at_send_wait_rsp应用线程使用的*/
bool dereg_at_dync_ctx(int fd)
{
	xy_assert(g_at_ctx_dync_head != NULL && (fd >= FARPS_USER_MIN && fd < FARPS_USER_MAX));

	osMutexAcquire(g_at_dync_ctx_mux, osWaitForever);
	at_context_dync_t *temp = NULL;
	at_context_dync_t *pre = NULL;
	temp = g_at_ctx_dync_head;
	do
	{
		if (temp->node->fd == fd)
		{
            xy_printf(0, PLATFORM_AP, INFO_LOG, "deregister at context fd:%d from at dict", fd);

            /*非第一个节点匹配成功*/
			if (pre != NULL)
			{
				pre->next = temp->next;
			}
			/*第一个节点匹配成功*/
			else
				g_at_ctx_dync_head = temp->next;
			xy_free(temp);
			osMutexRelease(g_at_dync_ctx_mux);
			return 1;
		}
		else
		{
			pre = temp;
			temp = temp->next;
		}

	} while (temp != NULL);

	osMutexRelease(g_at_dync_ctx_mux);
	return 0;
}

at_context_t *search_dync_ctx(int fd)
{
	osMutexAcquire(g_at_dync_ctx_mux, osWaitForever);

	xy_assert(fd >= FARPS_USER_MIN && fd < FARPS_USER_MAX);

	at_context_dync_t *temp = g_at_ctx_dync_head;

    if (temp == NULL)
    {
        goto SEARCH_END;
    }

	do
	{
		if (temp->node->fd == fd)
		{
			osMutexRelease(g_at_dync_ctx_mux);
			return temp->node;
		}
		else
		{
			temp = temp->next;
		}
	} while (temp != NULL);

SEARCH_END:
	osMutexRelease(g_at_dync_ctx_mux);
	return NULL;
}

/*仅供用户线程调用同步接口at_send_wait_rsp专用*/
at_context_t* get_dync_context()
{
	osMutexAcquire(g_at_dync_ctx_mux, osWaitForever);

	at_context_t *ctx = NULL;
	int fd;

	/*at_proxy服务端调用at_send_wait_rsp*/
	if (strstr((char *)(osThreadGetName(osThreadGetId())), AT_PROXY_THREAD_NAME))
	{
		if (search_dync_ctx(FARPS_XY_PROXY) == NULL)
		{
			ctx = xy_malloc(sizeof(at_context_t));
			memset(ctx, 0, sizeof(at_context_t));
			ctx->fd = FARPS_XY_PROXY;
            regist_at_dync_ctx(ctx);
		}
		goto END_GET_CTX;
	}

	for (fd = FARPS_XY_PROXY + 1; fd <= FARPS_USER_MAX; fd++)
	{
		if (search_dync_ctx(fd) == NULL)
		{
			ctx = xy_malloc(sizeof(at_context_t));
			memset(ctx, 0, sizeof(at_context_t));
			ctx->fd = fd;
			regist_at_dync_ctx(ctx);
			goto END_GET_CTX;
		}
	}
END_GET_CTX:
	osMutexRelease(g_at_dync_ctx_mux);
	return ctx;
}

/*仅供用户线程调用同步接口at_send_wait_rsp专用*/
osMessageQueueId_t get_dync_queue_by_timer(osTimerId_t timerId)
{
	osMutexAcquire(g_at_dync_ctx_mux, osWaitForever);

	at_context_dync_t *temp = NULL;
	temp = g_at_ctx_dync_head;
	do
	{
		if (temp->node->ctx_tmr == timerId)
		{
			osMutexRelease(g_at_dync_ctx_mux);
			return temp->node->user_queue_Id;
		}
		else
		{
			temp = temp->next;
		}
	} while (temp != NULL);

    xy_assert(0);

	osMutexRelease(g_at_dync_ctx_mux);
	return NULL;
}

bool is_dync_ctx_fd(int srcFd)
{
    if (srcFd >= FARPS_USER_MIN && srcFd < FARPS_USER_MAX)
    {
        return true;
    }
    return false;
}
