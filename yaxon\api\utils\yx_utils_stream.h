/**********************************************************************/
/**
 * @file yx_utils_stream.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-11
 * @version V1.0
 * @brief 字节流操作封装接口
 **********************************************************************/
#ifndef YX_UTILS_STREAM_H
#define YX_UTILS_STREAM_H

#include "yx_type.h"

#define STREAMMEM INT8U /**< 字节流数据类型定义 */

/**
 * @brief 字节流大小端控制枚举定义
 * @ingroup yx_utils
 */
typedef enum
{
    ENDIAN_BIG,   /**< 大端模式 */
    ENDIAN_LITTLE /**< 小端模式 */
} ENDINA_TYPE_E;

/**
 * @brief 字节流句柄结构定义
 * @ingroup yx_utils
 */
typedef struct
{
    STREAMMEM* start_ptr; /**< 数据流起始地址 */
    STREAMMEM* cur_ptr;   /**< 数据流当前地址 */
    INT32U len;           /**< 数据流已用字节数 */
    INT32U max_len;       /**< 数据流的最大长度 */
} stream_t;

/**
 * @brief 初始化数据流
 * @ingroup yx_utils
 * @param sp 数据流指针（必须非空）
 * @param bp 数据流管理的缓冲区地址（必须非空）
 * @param max_len 缓冲区最大长度（必须 > 0）
 * @return TRUE-成功 / FALSE-参数非法
 * @note 初始化后流指针指向缓冲区起始位置
 */
BOOLEAN yx_utils_stream_init(stream_t* sp, STREAMMEM* bp, INT32U max_len);

/**
 * @brief 获取数据流中剩余的可用字节数
 * @ingroup yx_utils
 * @param sp 数据流指针
 * @return 数据流剩余的可用字节数
 */
INT32U yx_utils_stream_get_left_len(stream_t* sp);

/**
 * @brief 获取数据流中已用字节数
 * @ingroup yx_utils
 * @param sp 数据流指针
 * @return 数据流已用字节数
 */
INT32U yx_utils_stream_get_used_len(stream_t* sp);

/**
 * @brief 获取数据流缓存总长度
 * @ingroup yx_utils
 * @param sp 数据流指针
 * @return 数据流的最大长度
 */
INT32U yx_utils_stream_get_max_len(stream_t* sp);

/**
 * @brief 获取数据流当前读/写指针
 * @ingroup yx_utils
 * @param sp 数据流指针（必须非空）
 * @return 当前指针地址（流为空时返回NULL）
 */
STREAMMEM* yx_utils_stream_get_ptr(stream_t* sp);

/**
 * @brief 获取数据流所管理内存的地址
 * @ingroup yx_utils
 * @param sp 数据流指针
 * @return 所管理内存地址
 */
STREAMMEM* yx_utils_stream_get_start_ptr(stream_t* sp);

/**
 * @brief 移动数据流读/写指针
 * @ingroup yx_utils
 * @param sp 数据流指针（必须非空）
 * @param len 移动字节数（必须 <= 剩余空间）
 * @return 成功:RTN_OK, 失败:RTN_ERR
 * @note 若移动后超出缓冲区，已用长度会截断为max_len
 */
INT32S yx_utils_stream_move_ptr(stream_t* sp, INT32U len);

/**
 * @brief 往数据流中写入一个字节数据
 * @ingroup yx_utils
 * @param sp 数据流指针
 * @param writebyte 写入的字节数据
 * @return 成功:RTN_OK, 失败:RTN_ERR
 */
INT32S yx_utils_stream_write_byte(stream_t* sp, INT8U writebyte);

/**
 * @brief 往数据流中写入一个半字(16位)数据, 可控大小端
 * @ingroup yx_utils
 * @param sp 数据流指针
 * @param writeword 写入的半字数据
 * @param type 端序模式（ENDIAN_BIG/ENDIAN_LITTLE）
 * @return 成功:RTN_OK, 失败:RTN_ERR
 */
INT32S yx_utils_stream_write_hword(stream_t* sp, INT16U writeword, ENDINA_TYPE_E type);

/**
 * @brief 往数据流中写入一个字(32位)数据, 可控大小端
 * @ingroup yx_utils
 * @param sp 数据流指针（必须非空）
 * @param writelong 待写入的32位数据
 * @param type 端序模式（ENDIAN_BIG/ENDIAN_LITTLE）
 * @return 成功:RTN_OK, 失败:RTN_ERR
 * @note 大端模式按[B3][B2][B1][B0]写入，小端按[B0][B1][B2][B3]写入
 */
INT32S yx_utils_stream_write_word(stream_t* sp, INT32U writelong, ENDINA_TYPE_E type);

/**
 * @brief 往数据流中写入字符串
 * @ingroup yx_utils
 * @param sp 数据流指针
 * @param ptr 写入的字符串指针
 * @return 成功:RTN_OK, 失败:RTN_ERR
 */
INT32S yx_utils_stream_write_str(stream_t* sp, CHAR* ptr);

/**
 * @brief 往数据流中写入一块内存数据
 * @ingroup yx_utils
 * @param sp 数据流指针（必须非空）
 * @param ptr 写入的数据块地址（必须非空）
 * @param len 写入的数据块字节数
 * @return 成功:RTN_OK, 失败:RTN_ERR
 */
INT32S yx_utils_stream_write_data(stream_t* sp, INT8U* ptr, INT32U len);

/**
 * @brief 从数据流中读取一个字节
 * @ingroup yx_utils
 * @param sp 数据流指针
 * @return 读取到的字节
 */
INT8U yx_utils_stream_read_byte(stream_t* sp);

/**
 * @brief 从数据流中读取一个半字(16位)数据, 可控大小端
 * @ingroup yx_utils
 * @param sp 数据流指针
 * @param type 端序模式（ENDIAN_BIG/ENDIAN_LITTLE）
 * @return 读取到的半字数据(16位)
 */
INT16U yx_utils_stream_read_hword(stream_t* sp, ENDINA_TYPE_E type);

/**
 * @brief 从数据流中读取一个字(32位)数据, 可控大小端
 * @ingroup yx_utils
 * @param sp 数据流指针
 * @param type 端序模式（ENDIAN_BIG/ENDIAN_LITTLE）
 * @return 读取到的字数据(32位)
 */
INT32U yx_utils_stream_read_word(stream_t* sp, ENDINA_TYPE_E type);

/**
 * @brief 从数据流读取指定长度数据
 * @ingroup yx_utils
 * @param sp 数据流指针（必须非空）
 * @param ptr 数据存储地址（必须非空且足够容纳len字节）
 * @param len 待读取的字节数（必须 > 0）
 * @return 成功:RTN_OK, 失败:RTN_ERR
 */
INT32S yx_utils_stream_read_data(stream_t* sp, INT8U* ptr, INT32U len);

/**
 * @brief 往数据流中写入一个半字(16位)数据, 大端
 * @ingroup yx_utils
 */
#define YX_WRITE_HWORD_STREAM(sp, wdata) yx_utils_stream_write_hword(sp, (INT16U)(wdata), ENDIAN_BIG)

/**
 * @brief 往数据流中写入一个半字(16位)数据, 小端
 * @ingroup yx_utils
 */
#define YX_WRITE_LE_HWORD_STREAM(sp, wdata) yx_utils_stream_write_hword(sp, (INT16U)(wdata), ENDIAN_LITTLE)

/**
 * @brief 往数据流中写入一个字(32位)数据, 大端
 * @ingroup yx_utils
 */
#define YX_WRITE_WORD_STREAM(sp, wdata) yx_utils_stream_write_word(sp, (INT32U)(wdata), ENDIAN_BIG)
/**
 * @brief 往数据流中写入一个字(32位)数据, 小端
 * @ingroup yx_utils
 */
#define YX_WRITE_LE_WORD_STREAM(sp, wdata) yx_utils_stream_write_word(sp, (INT32U)(wdata), ENDIAN_LITTLE)

/**
 * @brief 往数据流中读出一个字(16位)数据, 大端
 * @ingroup yx_utils
 */
#define YX_READE_HWORD_STREAM(sp) yx_utils_stream_read_hword(sp, ENDIAN_BIG)

/**
 * @brief 往数据流中读出一个字(16位)数据, 小端
 * @ingroup yx_utils
 */
#define YX_READE_LE_HWORD_STREAM(sp) yx_utils_stream_read_hword(sp, ENDIAN_LITTLE)

/**
 * @brief 往数据流中读出一个字(32位)数据, 大端
 * @ingroup yx_utils
 */
#define YX_READE_WORD_STREAM(sp) yx_utils_stream_read_word(sp, ENDIAN_BIG)
/**
 * @brief 往数据流中读出一个字(32位)数据, 小端
 * @ingroup yx_utils
 */
#define YX_READE_LE_WORD_STREAM(sp) yx_utils_stream_read_word(sp, ENDIAN_LITTLE)

#endif
