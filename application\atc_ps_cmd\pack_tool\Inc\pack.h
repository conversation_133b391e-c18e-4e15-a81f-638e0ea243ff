#ifndef _PACK_H_
#define _PACK_H_

#ifndef NULL
#define NULL                         ((void*)0)
#endif

#define PACK_SUCC                     0
#define PACK_FAIL                     1

typedef struct pack_node_stru
{
    struct pack_node_stru*      next;
    struct pack_node_stru*      child;
    unsigned char               level;
    unsigned short              offset;
    unsigned short              stru_offset;
#define PACK_PARAM_TYPE_DATA_SIZE     1
#define PACK_PARAM_TYPE_PTR_U8        2
#define PACK_PARAM_TYPE_PTR_U16       3
#define PACK_PARAM_TYPE_PTR_U32       4
#define PACK_PARAM_TYPE_PTR_ARRAY     5
#define PACK_PARAM_TYPE_PTR64_ARRAY   6
    unsigned char               param_type;
    unsigned short              param;
    unsigned short              ext_param;
} pack_node_stru;


/*******************************************************************************
  MODULE    :clear_pack_node
  FUNCTION  :delete regiestered node list
  PARAM     :node  :the root node of node list
*******************************************************************************/
extern void clear_pack_node(pack_node_stru* node);

/*******************************************************************************
  MODULE    :pack_node_register
  FUNCTION  :structure membership registration
  PARAM     :peer_node  :pointer to previous peer node
             level      :pointer level
             offset     :The offset position of the pointer in the structure
             param_type :parameter type
             param      :parameter value
             ext_param  :extended parameter
  RETURE    :new node pointer
*******************************************************************************/
extern pack_node_stru* pack_node_register(pack_node_stru** peer_node, unsigned char level, unsigned short offset,
                                               unsigned char param_type, unsigned short param, unsigned short ext_param);

/*******************************************************************************
  MODULE    :pack
  FUNCTION  :Encoding function
  PARAM     :des        :Code stream pointer after encoding
             p_len      :The length of Code stream
             src_data   :Source struct pointer
             size       :Source struct size
             root_node  :the root node of regiestered node list
  RETURE    :PACK_SUCC(0)/PACK_FAIL(1)
*******************************************************************************/
extern unsigned char pack(unsigned char** des, unsigned short* p_len, unsigned char* src_data, unsigned short size, pack_node_stru* root_node);

/*******************************************************************************
  MODULE    :unpack
  FUNCTION  :Decodiong function
  PARAM     :des        :The pointer of decoded structure
             data       :Source Code stream
             len        :The length of source Code stream
  RETURE    :PACK_SUCC(0)/PACK_FAIL(1)
*******************************************************************************/
extern unsigned char unpack(unsigned char* des, unsigned short des_len, unsigned char* data, unsigned short data_len);
extern unsigned short get_code_stram_len(pack_node_stru* root_node, unsigned char* src_data);

#endif
