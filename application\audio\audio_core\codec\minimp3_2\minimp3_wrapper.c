//#define MINIMP3_ONLY_MP3
//#define MINIMP3_ONLY_SIMD
//#define MINIMP3_NO_SIMD
//#define MINIMP3_NONSTANDARD_BUT_LOGICAL
//#define MINIMP3_FLOAT_OUTPUT

#define MINIMP3_IMPLEMENTATION
#define MAX_FRAME_SYNC_MATCHES 3
#include "minimp3.h"
#include "minimp3_ex.h"
#undef MINIMP3_IMPLEMENTATION

#include "minimp3_wrapper.h"

// minimp3_wrapper_

void minimp3_wrapper_mp3dec_init(minimp3_wrapper_mp3dec_t *dec)
{
    mp3dec_init(dec);
}

int minimp3_wrapper_mp3dec_decode_frame(minimp3_wrapper_mp3dec_t *dec, const uint8_t *mp3, int mp3_bytes, minimp3_wrapper_mp3d_sample_t *pcm, minimp3_wrapper_mp3dec_frame_info_t *info)
{
    return mp3dec_decode_frame(dec, mp3, mp3_bytes, pcm, info);
}

int minimp3_wrapper_mp3dec_detect_buf(const uint8_t *buf, size_t buf_size)
{
    return mp3dec_detect_buf(buf, buf_size);
}

int minimp3_wrapper_mp3dec_detect_cb(minimp3_wrapper_mp3dec_io_t *io, uint8_t *buf, size_t buf_size)
{
    return mp3dec_detect_cb(io, buf, buf_size);
}

int minimp3_wrapper_mp3dec_iterate_buf(const uint8_t *buf, size_t buf_size, MINIMP3_WRAPPER_MP3D_ITERATE_CB callback, void *user_data)
{
    return mp3dec_iterate_buf(buf, buf_size, callback, user_data);
}

int minimp3_wrapper_mp3dec_iterate_cb(minimp3_wrapper_mp3dec_io_t *io, uint8_t *buf, size_t buf_size, MINIMP3_WRAPPER_MP3D_ITERATE_CB callback, void *user_data)
{
    return mp3dec_iterate_cb(io, buf, buf_size, callback, user_data);
}

