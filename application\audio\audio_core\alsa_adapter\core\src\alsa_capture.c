#include "alsa_config.h"

#ifdef ALSA_SUPPORT_CAPTURE
#include "alsa_capture.h"
#include <string.h>
#include <stdlib.h>
//#include "cmsis.h"
#include "alsa.h"
#include "alsa_extra.h"
#include "alsa_os.h"
#include "alsa_deamon.h"
#include "alsa_instance.h"
#include "hal_trace.h"
#include "plat_types.h"
#include "heap_api.h"

// #define ALSA_CAPTURE_DEBUG

struct alsa_capture
{
    alsa_instance_t *instance;
    alsa_instance_id_t id;
    uint32_t sample_rate;
    uint8_t channels;
    uint8_t bits;
    alsa_state_t state;
    alsa_os_mutex_t lock;
    alsa_pcm_state_callback_t cb;
    void *cb_arg;
    uint8_t ch_map;
    alsa_data_pull_push_cb_t data_pull_cb;
};


alsa_capture_t *alsa_capture_open(uint32_t sample_rate,
                                  uint8_t channels,
                                  uint8_t bits)
{
    alsa_capture_t *capture = NULL;
    alsa_instance_t *instance = NULL;
    alsa_os_mutex_t lock = NULL;

    capture = (alsa_capture_t *)audio_calloc(1, sizeof(alsa_capture_t));
    if (!capture)
    {
        TRACE(0, "%s capture alloc error", __func__);
        goto error_exit;
    }

    instance = alsa_instance_open(ALSA_INS_CAPTURE, 0);
    if (!instance)
    {
        TRACE(0, "%s instance open error", __func__);
        goto error_exit;
    }
    capture->instance = instance;
    capture->id = alsa_instance_get_id(instance);
    alsa_instance_set_state(instance, ALSA_STATE_OPEN);

    capture->sample_rate = sample_rate;

    capture->bits = bits;
    capture->channels = channels;

    lock = alsa_os_mutex_create();
    if (!lock)
    {
        TRACE(0, "%s lock create error", __func__);
        goto error_exit;
    }
    capture->lock = lock;

    alsa_os_mutex_wait(capture->lock, osWaitForever);

    if (0 != alsa_deamon_capture_open(capture->id,
                                      (enum AUD_SAMPRATE_T)sample_rate,
                                      (enum AUD_BITS_T)bits,
                                      (enum AUD_CHANNEL_NUM_T)channels))
    {
        TRACE(0, "%s alsa_deamon_playback_open error", __func__);
        goto error_exit;
    }

    capture->state = ALSA_STATE_OPEN;

    alsa_instance_id_bind_user_arg(capture->id, capture);

    alsa_os_mutex_release(capture->lock);

    return capture;

error_exit:

    if (capture)
    {
        audio_free(capture);
    }
    if (instance)
    {
        alsa_instance_close(instance);
    }
    if (lock)
    {
        alsa_os_mutex_delete(lock);
    }
    return NULL;
}

int alsa_capture_set_data_pull_callback(alsa_capture_t *capture, alsa_data_pull_push_cb_t cb)
{
    if (!capture)
    {
        TRACE(0, "%s capture NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(capture->lock, osWaitForever);
    capture->data_pull_cb = cb;
    alsa_os_mutex_release(capture->lock);

    return 0;
}

int alsa_capture_start(alsa_capture_t *capture)
{
    if (!capture)
    {
        TRACE(0, "%s capture NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(capture->lock, osWaitForever);

    if (capture->state != ALSA_STATE_OPEN && capture->state != ALSA_STATE_STOP)
    {
        TRACE(0, "%s state = %d error", __func__, capture->state);
        alsa_os_mutex_release(capture->lock);
        return -1;
    }

    if (0 != alsa_deamon_capture_start(capture->id))
    {
        TRACE(0, "%s alsa_deamon_capture_start error", __func__);
        alsa_os_mutex_release(capture->lock);
        return -1;
    }

    alsa_instance_set_state(capture->instance, ALSA_STATE_START);

    capture->state = ALSA_STATE_START;

    alsa_os_mutex_release(capture->lock);

    return 0;
}

int alsa_capture_pause(alsa_capture_t *capture)
{
    if (!capture)
    {
        TRACE(0, "%s capture NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(capture->lock, osWaitForever);

    if (capture->state != ALSA_STATE_START)
    {
        TRACE(0, "%s state = %d not need stop", __func__, capture->state);
        alsa_os_mutex_release(capture->lock);
        return -1;
    }

    if (0 != alsa_deamon_capture_stop(capture->id))
    {
        TRACE(0, "%s alsa_deamon_capture_stop error", __func__);
        alsa_os_mutex_release(capture->lock);
        return -1;
    }

    alsa_instance_set_state(capture->instance, ALSA_STATE_STOP);

    capture->state = ALSA_STATE_STOP;

    alsa_os_mutex_release(capture->lock);

    return 0;
}

int alsa_capture_stop(alsa_capture_t *capture)
{
    if (!capture)
    {
        TRACE(0, "%s capture NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(capture->lock, osWaitForever);

    if (capture->state != ALSA_STATE_START)
    {
        TRACE(0, "%s state = %d not need stop", __func__, capture->state);
        alsa_os_mutex_release(capture->lock);
        return -1;
    }

    if (0 != alsa_deamon_capture_stop(capture->id))
    {
        TRACE(0, "%s alsa_deamon_capture_stop error", __func__);
        alsa_os_mutex_release(capture->lock);
        return -1;
    }

    alsa_instance_set_state(capture->instance, ALSA_STATE_STOP);

    capture->state = ALSA_STATE_STOP;

    alsa_os_mutex_release(capture->lock);

    return 0;
}

int alsa_capture_flush(alsa_capture_t *capture)
{
    return 0;
}

int alsa_capture_close(alsa_capture_t *capture)
{
    if (!capture)
    {
        TRACE(0, "%s capture NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(capture->lock, osWaitForever);

    if (capture->state != ALSA_STATE_STOP && capture->state != ALSA_STATE_OPEN)
    {
        TRACE(0, "%s state = %d error", __func__, capture->state);
        alsa_os_mutex_release(capture->lock);
        return -1;
    }

    if (0 != alsa_deamon_capture_close(capture->id))
    {
        TRACE(0, "%s alsa_deamon_capture_close error", __func__);
        alsa_os_mutex_release(capture->lock);
        return -1;
    }

    alsa_instance_close(capture->instance);

    capture->state = ALSA_STATE_CLOSE;

    alsa_os_mutex_release(capture->lock);

    if (0 != alsa_os_mutex_delete(capture->lock))
    {
        ASSERT(0, "%s lock delete error", __func__);
    }

    audio_free(capture);

    return 0;
}

int alsa_capture_read(alsa_capture_t *capture, uint8_t *ptr, uint32_t length)
{
    if (!capture)
    {
        TRACE(0, "%s capture NULL error", __func__);
        return -1;
    }

    if (capture->data_pull_cb)
    {
        TRACE(0, "%s current in data pull mode, not support read, need use data pull callback", __func__);
        return -1;
    }

    int read_len = alsa_instance_read(capture->instance, ptr, length);

    // data_process

    return read_len;
}

uint32_t alsa_capture_data_to_user(uint8_t *data, uint32_t length)
{
    alsa_instance_t *alsa_instance = NULL;
    alsa_capture_t *capture = NULL;
    alsa_instance_id_t id = 1;

    capture = alsa_instance_get_user_arg_by_id(id);

    if (capture->data_pull_cb)
    {
        capture->data_pull_cb(data, length);
    }
    else
    {
        alsa_instance = alsa_get_instance_by_id(id);
        xy_assert((alsa_instance->buffer) != NULL);
        alsa_instance_write(alsa_instance, data, length);
    }

    return 0;
}

int alsa_capture_register_pcm_state_callback(alsa_capture_t *capture,
                                             alsa_pcm_state_callback_t cb,
                                             void *arg)
{
    return 0;
}
#endif