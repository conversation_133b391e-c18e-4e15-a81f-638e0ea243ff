
 libmad - MPEG audio decoder library
 Copyright (C) 2000-2004 Underbit Technologies, Inc.

 $Id: CREDITS,v 1.5 2004/02/17 02:02:03 rob Exp $

===============================================================================

AUTHOR

  Except where otherwise noted, all code was authored by:

      <PERSON> <<EMAIL>>

CONTRIBUTORS

  Significant contributions have been incorporated with thanks to:

      Anonymous
        2002/03/15: frame.c
          - Reported problem with use of reserved emphasis value.
        2003/08/31: layer12.c
          - Suggested support for certain disallowed bitrate/mode
            combinations.

      <PERSON>ek <PERSON>bers <<EMAIL>>
        2003/04/21: layer3.c
          - Reported runtime uninitialized use of `ptr' in designating
            ancillary bits after a decoding error.

      <PERSON> <<EMAIL>>
        2003/02/01: frame.c
          - Reported assertion failure in layer3.c due to an
            invalid/unsupported Layer III free format bitrate.

      <PERSON> <<EMAIL>>
        2001/01/30: fixed.h
          - Provided initial PowerPC fixed-point assembly.

      <PERSON> <<EMAIL>>
        2000/09/20: imdct_l_arm.S
          - Suggested patch for a.out compatibility.

      Brian <PERSON> <Brian.<PERSON>@sun.com>
        2003/07/02: huffman.c
          - Suggested changes for improved portability.

      Joshua Haberman <<EMAIL>>
        2001/08/10: decoder.c, huffman.c
          - Suggested portability fixes.

      Timothy King <<EMAIL>>
        2002/05/04: sf_table.dat, layer12.c
          - Reported problem with use of (missing) scalefactor index 63.

      Felix von Leitner <<EMAIL>>
        2003/01/21: fixed.h
          - Suggested Intel scaling alternative for possible speedup.

      Andre McCurdy <<EMAIL>>
        2000/08/10: imdct_l_arm.S
          - ARM optimized assembly replacement for III_imdct_l().
        2000/09/15: imdct_l_arm.S
          - Applied Nicolas Pitre's rounding optimisation in all remaining
            places.
        2001/02/10: layer3.c
          - Inspiration for Huffman decoding and requantization rewrite, and
            other miscellany.
        2001/03/24: imdct_l_arm.S
          - Corrected PIC unsafe code.
        2002/02/16: fixed.h
          - Discovered bug in ARM version of mad_f_scale64().

      Haruhiko OGASAWARA <<EMAIL>>
        2001/01/28: layer3.c
          - Reported discrepancy in alias reduction for mixed short blocks.

      Brett Paterson <<EMAIL>>
        2001/10/28: mad_global.h
          - Reported missing <assert.h> et al. under MS Embedded Visual C.

      Sean 'Shaleh' Perry <<EMAIL>>
        2000/04/04: fixed.h
          - Suggested use of size-dependent typedefs.
        2001/10/22: config.guess, config.sub
          - Keep up to date for proper Debian packaging.

      Bertrand Petit <<EMAIL>>
        2001/11/05: synth.h
          - Suggested PCM channel enumeration constants.
        2001/11/05: stream.h
          - Suggested MAD_ERROR_NONE enumeration constant.
        2001/11/05: stream.c
          - Suggested mad_stream_errorstr() function.

      Nicolas Pitre <<EMAIL>>
        2000/09/09: fixed.h
          - Parameterized all scaling for correct use of all multiplication
            methods within mad_synth_frame().
          - Rewrote the FPM_ARM version of mad_f_mul() so we have 64-bit
            multiplication result, rounding and scaling with 3 instructions.
        2000/09/09: imdct_l_arm.S
          - Optimized rounding + scaling operations.
        2000/09/17: synth.c
          - Changed D[] run-time shifts to compile-time.
          - Modified synthesis for better multiply/accumulate code output.
        2001/08/11: fixed.h, synth.c
          - Suggested 64-bit FPM negation and negative term factorization
            during synthesis.
        2001/08/11: fixed.h
          - Suggested unrounded behavior for FPM_DEFAULT when OPT_SPEED.
        2001/11/19: fixed.c
          - Suggested computation of any resampling ratio.

===============================================================================

