#ifndef __ALSA_CONFIG_H__
#define __ALSA_CONFIG_H__

#include "alsa_supported.h"

// #if XY_AUDIO_RECORD
#define ALSA_SUPPORT_CAPTURE
// #endif
#ifdef ALSA_SUPPORT_CAPTURE
#include "alsa_capture_config.h"
#else
#define ALSA_CAPTURE_INSTANCE_COUNT              0
#endif

//#define ALSA_PLAYBACK_SUPPORT_MULTI_INSTANCE
#ifdef ALSA_PLAYBACK_SUPPORT_MULTI_INSTANCE
#define ALSA_PLAYBACK_INSTANCE_COUNT             3
#else
#define ALSA_PLAYBACK_INSTANCE_COUNT             1
#endif

#define ALSA_INSTANCE_COUNT                      (ALSA_PLAYBACK_INSTANCE_COUNT + ALSA_CAPTURE_INSTANCE_COUNT)

#ifndef ALSA_PLAYBACK_PARAMETER_USER_SPECIFIED
#define ALSA_PLAYBACK_PARAMETER_FIXED
#endif

#ifdef ALSA_PLAYBACK_PARAMETER_FIXED
#define ALSA_PLAYBACK_SAMPLE_RATE                ALSA_SUPPORTED_SAMPLE_RATE_48000
#define ALSA_PLAYBACK_CHANNEL_NUM                ALSA_SUPPORTED_CHANNEL_NUM_2
#define ALSA_PLAYBACK_BITS                       ALSA_SUPPORTED_BITS_16
#define ALSA_PLAYBACK_QUEUE_SAMPLE_RATE          ALSA_SUPPORTED_SAMPLE_RATE_48000
#define ALSA_PLAYBACK_QUEUE_CHANNEL_NUM          ALSA_SUPPORTED_CHANNEL_NUM_2
#define ALSA_PLAYBACK_QUEUE_BITS                 ALSA_SUPPORTED_BITS_16
#if (ALSA_PLAYBACK_QUEUE_CHANNEL_NUM == ALSA_SUPPORTED_CHANNEL_NUM_2) && \
    (ALSA_PLAYBACK_CHANNEL_NUM == ALSA_SUPPORTED_CHANNEL_NUM_1)
#define ALSA_QUEUE_STEREO_PLAYBACK_MONO_EN
#endif
#else
#if defined(ALSA_PLAYBACK_SUPPORT_MULTI_INSTANCE)
#error "ALSA_PLAYBACK_PARAMETER_USER_SPECIFIED conflict with ALSA_PLAYBACK_SUPPORT_MULTI_INSTANCE"
#endif

#endif

#define ALSA_PLAYBACK_DMA_SLOT_MS                128
#ifdef ALSA_PLAYBACK_PARAMETER_FIXED
#define ALSA_PLAYBACK_DMA_ONE_SAMPLE_SIZE        (ALSA_PLAYBACK_BITS / 8)
#define ALSA_PLAYBACK_DMA_ONE_MS_PCM_SIZE        (ALSA_PLAYBACK_SAMPLE_RATE / 1000 *  \
                                                  ALSA_PLAYBACK_DMA_ONE_SAMPLE_SIZE * \
                                                  ALSA_PLAYBACK_CHANNEL_NUM)
#define ALSA_PLAYBACK_DMA_BUFFER_SIZE            (ALSA_PLAYBACK_DMA_ONE_MS_PCM_SIZE * ALSA_PLAYBACK_DMA_SLOT_MS * 2)
#else
#define ALSA_PLAYBACK_DMA_BUFFER_SIZE            24576
#endif

#ifdef ALSA_PLAYBACK_PARAMETER_FIXED
#define ALSA_PLAYBACK_QUEUE_TIME_MS              (ALSA_PLAYBACK_DMA_SLOT_MS * 2 * 2)
#define ALSA_PLAYBACK_QUEUE_ONE_SAMPLE_SIZE      (ALSA_PLAYBACK_BITS / 8)
#define ALSA_PLAYBACK_QUEUE_ONE_MS_PCM_SIZE      (ALSA_PLAYBACK_QUEUE_SAMPLE_RATE / 1000 * \
                                                  ALSA_PLAYBACK_QUEUE_ONE_SAMPLE_SIZE *    \
                                                  ALSA_PLAYBACK_QUEUE_CHANNEL_NUM)
#define ALSA_PLAYBACK_QUEUE_SIZE                 (ALSA_PLAYBACK_QUEUE_ONE_MS_PCM_SIZE * ALSA_PLAYBACK_QUEUE_TIME_MS)
#define ALSA_PLAYBACK_QUEUE_TRIGGER_SIZE         (ALSA_PLAYBACK_QUEUE_ONE_MS_PCM_SIZE * ALSA_PLAYBACK_DMA_SLOT_MS * 2)
#else
#define ALSA_PLAYBACK_QUEUE_SIZE                 (ALSA_PLAYBACK_DMA_BUFFER_SIZE * 4)
#define ALSA_PLAYBACK_QUEUE_TRIGGER_SIZE         (ALSA_PLAYBACK_DMA_BUFFER_SIZE * 2)
#endif

#define ALSA_WRITE_DATA_MAX_SIZE                 1024
#ifdef ALSA_PLAYBACK_PARAMETER_FIXED
#if (ALSA_PLAYBACK_QUEUE_CHANNEL_NUM == ALSA_SUPPORTED_CHANNEL_NUM_1)
#define ALSA_WRITE_CONVERT_STEREO_TO_MONO
#endif
#if (ALSA_PLAYBACK_QUEUE_CHANNEL_NUM == ALSA_SUPPORTED_CHANNEL_NUM_2)
#define ALSA_WRITE_CONVERT_MONO_TO_STEREO
#endif
#if (ALSA_PLAYBACK_QUEUE_BITS == ALSA_SUPPORTED_BITS_24)
#define ALSA_WRITE_CONVERT_16BIT_TO_24BIT
#endif
#endif

#define ALSA_PLAYBACK_DEFAULT_VOL                50
#define ALSA_PLAYBACK_MUTE_VOL                   0

#endif // __ALSA_CONFIG_H__