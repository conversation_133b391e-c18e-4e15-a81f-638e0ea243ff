/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "at_ps_proxy.h"
#include "at_ctl.h"
#include "xy_at_api.h"
#include "atc_ps.h"
#include "icc_msg.h"
#include "xy_atc_interface.h"

extern void xy_atc_data_req(unsigned short usDataLen, unsigned char*pucData, int ittyFd);

/*******************************************************************************
 *						Global function implementations						   *
 ******************************************************************************/
/* ATC回复的AT命令，发送给at框架，ttyFd为AT_FD_INVAILD时，表示URC上报 */
void SendAtInd2User(char *pAt, unsigned int ulAtLen, int ttyFd)
{
#if ATCTL_EN
    xy_printf(0, PLATFORM_AP, INFO_LOG, "[SendAtInd2User]:%s,ttyFd:%d", (const char *)pAt, ttyFd);
	debug_log_print("[SendAtInd2User]:%s,ttyFd:%d\n", (const char *)pAt, ttyFd); 
    if (ttyFd > AT_FD_INVAILD)
        send_msg_2_atctl(AT_MSG_RCV_STR_FROM_NEARPS, pAt, ulAtLen, ttyFd);
    else
        sysAtURC(pAt, ulAtLen);
#endif // ATCTL_EN
}

//PS实现，平台调用，发送从串口接收来的PS相关AT命令给ATC_AP
void SendAt2AtcAp(char *pAt, unsigned int ulAtLen, int ttyFd)
{
    xy_printf(0, PLATFORM_AP, INFO_LOG, "[SendAt2AtcAp]:%s,ttyFd:%d", pAt, ttyFd);
	debug_log_print("[SendAt2AtcAp]:%s,ttyFd:%d\n", pAt, ttyFd); 
    xy_atc_data_req(ulAtLen, (unsigned char*)pAt, ttyFd);
}

/* PS内部的ATC_AP与ATC_CP的跨核间通信 */
int send_ps_shm_msg(void *msg, int msg_len)
{
    icc_ps_t psMsg = {0};
    char *data = (char *)xy_malloc_align(msg_len);
    memcpy(data, msg, msg_len);
    psMsg.buf = (uint32_t)(size_t)data;
    psMsg.len = (uint32_t)msg_len;

    /* 执行cache clean将数据从cache回写到ram上 */
    csi_dcache_clean_range(data, msg_len);

    icc_at_channel_write(ICC_AT_PSMSG, &psMsg, sizeof(icc_ps_t));

    return XY_OK;
}

/*PS内部使用，实现ATC与CP核PS的内部零拷贝通信，不得使用二级指针*/
void SendShmMsg2AtcAp(char *pBuf, size_t BufLen)
{
    icc_ps_t *msg = (icc_ps_t *)pBuf;
    char *data = (char*)(size_t)(msg->buf);
	
    /* 保证at数据起始地址cache 64字节对齐 */
    xy_assert(IS_CACHE_ALIGNED(data));
    csi_dcache_invalid_range(data, msg->len);

    char *new = (char*)xy_malloc(msg->len);
    memcpy(new, data, msg->len);

    /* 核间零拷贝，通知CP核释放内存 */
    icc_at_channel_write(ICC_AT_FREE, (void *)&data, sizeof(msg->buf)); 

    ATC_AP_MSG_HEADER_STRU *pMsgHeader = (ATC_AP_MSG_HEADER_STRU *)new;

    xy_printf(0, PLATFORM_AP, INFO_LOG, "[SendShmMsg2AtcAp]:msgId=%d", pMsgHeader->ulMsgName);
	debug_log_print("[SendShmMsg2AtcAp]:msgId=%d\n", pMsgHeader->ulMsgName); 

    AtcAp_SendMsg2AtcAp(new, &g_AtcApInfo.msgInfo);
}
