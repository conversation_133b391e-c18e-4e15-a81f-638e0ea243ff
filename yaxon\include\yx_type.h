/**********************************************************************/
/**
 * @file yx_type.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-02-25
 * @version V1.0
 * @brief 基本数据类型宏定义，便于移植
 **********************************************************************/
#ifndef YX_TYPE_H
#define YX_TYPE_H

#include <stddef.h>

#define YX_UNUSED(var) do { (void)(var); } while (0)

#ifndef BOOLEAN
typedef unsigned char BOOLEAN; /**< 布尔类型 */
#endif

#ifndef INT8U
typedef unsigned char INT8U; /**< 8位无符号整形数 */
#endif

#ifndef INT8S
typedef signed char INT8S; /**< 8位有符号整形数 */
#endif

#ifndef INT16U
typedef unsigned short INT16U; /**< 16位无符号整形数 */
#endif

#ifndef INT16S
typedef signed short INT16S; /**< 16位有符号整形数 */
#endif

#ifndef INT32U
typedef unsigned int INT32U; /**< 32位无符号整形数 */
#endif

#ifndef INT32S
typedef signed int INT32S; /**< 32位有符号整形数 */
#endif

#ifndef INT64U
typedef unsigned long long INT64U; /**< 64位无符号整形数 */
#endif

#ifndef INT64S
typedef signed long long INT64S; /**< 64位有符号整形数 */
#endif

#ifndef FP32
typedef float FP32; /**< 32位浮点数 */
#endif

#ifndef FP64
typedef double FP64; /**< 64位浮点数 */
#endif

#ifndef CHAR
typedef char CHAR; /**< 有符号CHAR类型 */
#endif

#ifndef UCHAR
typedef unsigned char UCHAR; /**< 无符号CHAR类型 */
#endif

#ifndef TRUE
#define TRUE (BOOLEAN)1 /**< 布尔真 */
#endif

#ifndef FALSE
#define FALSE (BOOLEAN)0 /**< 布尔假 */
#endif

#ifndef NULL
#define NULL ((void*)0) /**< 空指针 */
#endif

#ifndef VOID
#define VOID void
#endif

#ifndef RTN_OK
#define RTN_OK 0 /**< 通用成功返回值 */
#endif

#ifndef RTN_ERR
#define RTN_ERR (-1) /**< 通用失败返回值 */
#endif

#endif