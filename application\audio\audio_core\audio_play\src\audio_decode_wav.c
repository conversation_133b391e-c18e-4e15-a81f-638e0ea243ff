#include "cmsis_os2.h"
#include "xy_system.h"
#include "audio_play.h"
#include "wav_header.h"

#define WAV_READ_PCM_SIZE 4096

static audio_sink_handle_t * sink_handle = NULL;

int audio_decode_wav_process(audio_play_t *audio_play)
{
    audio_error_t ret = AUDIO_ERR_OK;
    wav_header_t header;
    int32_t len;
    uint8_t *pcm_buf = NULL;
    audio_play_state_t state;

    len = audio_play->source->read(&header, sizeof(wav_header_t), audio_play->source->source_arg);
    if(len != sizeof(wav_header_t))
    {
        ret = AUDIO_ERR_READ;
        goto exit;
    }

    if((header.riff != WAV_TAG('R', 'I', 'F', 'F')) 
       || (header.wave != WAV_TAG('W', 'A', 'V', 'E')) 
       || (header.fmt != WAV_TAG('f', 'm', 't', ' ')) 
       || (header.data != WAV_TAG('d', 'a', 't', 'a')))
    {
        audio_play->source->seek(0, audio_play->source->source_arg);
        ret = AUDIO_ERR_FORMAT;
        goto exit;
    }

    if(header.bits_per_sample != 16)
    {
        ret = AUDIO_ERR_BITS;
        goto exit;
    }

    pcm_buf = (uint8_t *)xy_malloc(WAV_READ_PCM_SIZE);
    if(pcm_buf == NULL)
    {
        ret = AUDIO_ERR_MEMORY;
        goto exit;
    }

    if (sink_handle == NULL)
    {
        sink_handle = audio_play->sink->open(SINK_MODE_OUT, header.sample_rate, header.channels, header.bits_per_sample);
        if (sink_handle == NULL)
        {
            ret = AUDIO_ERR_SINK_OPEN;
            goto exit;
        }
    }
    else
    {
        xy_assert(0);
    }
    audio_play->sink->start(sink_handle);

    while(1)
    {
        len = audio_play->source->read(pcm_buf, WAV_READ_PCM_SIZE, audio_play->source->source_arg);
        if(len > 0)
        {
            state = audio_play->state_get();
            if (state == AUDIO_PLAY_STATE_STOP)
            {    
                ret = AUDIO_ERR_OK;
                goto exit;
            }
            else if (state == AUDIO_PLAY_STATE_PAUSE)
            {
                osSemaphoreAcquire(*(audio_play->sem), osWaitForever);
            }
            audio_play->sink->write(sink_handle, pcm_buf, len);
        }
        else
        {
            ret = AUDIO_ERR_OK;
            break;
        }
    }

exit:

    if (pcm_buf)
    {
        xy_free(pcm_buf);
    }

    if(sink_handle != NULL)
    {
        audio_play->sink->stop(sink_handle);
        audio_play->sink->close(sink_handle);
        sink_handle = NULL;
    }

    return ret;
}
