#pragma once

int at_NRB_req(char *at_buf, char **prsp_cmd);
int at_RESET_req(char *at_buf, char **prsp_cmd);
int at_LOCK_req(char *at_buf, char **prsp_cmd);
int at_IPR_req(char *at_buf, char **prsp_cmd);
int at_IFC_req(char *at_buf, char **prsp_cmd);
int at_ANDC_req(char *at_buf, char **prsp_cmd);
int at_ANDD_req(char *at_buf, char **prsp_cmd);
int at_NSET_req(char *at_buf, char **prsp_cmd);
int at_CTZU_req(char *at_buf, char **prsp_cmd);
int at_QNTP_req(char *at_buf, char **prsp_cmd);
int at_NV_req(char *at_buf, char **prsp_cmd);
int at_XYACT_req(char *at_buf, char **prsp_cmd);
int at_ANDF_req(char *at_buf, char **prsp_cmd);
int at_ANDW_req(char *at_buf, char **prsp_cmd);
int at_ANDV_req(char *at_buf, char **prsp_cmd);
int at_ATO_req(char *at_buf, char **prsp_cmd);
int at_ATZ_req(char *at_buf, char **prsp_cmd);
int at_ATE_req(char *at_buf, char **prsp_cmd);
int at_ATQ_req(char *at_buf, char **prsp_cmd);
int at_ATV_req(char *at_buf, char **prsp_cmd);
int at_ATS0_req(char *at_buf, char **prsp_cmd);
int at_ATS3_req(char *at_buf, char **prsp_cmd);
int at_ATS4_req(char *at_buf, char **prsp_cmd);
int at_ATS5_req(char *at_buf, char **prsp_cmd);
int at_QIURC_req(char *at_buf, char **prsp_cmd);
int at_QIMUX_req(char *at_buf, char **prsp_cmd);
int at_QIHEAD_req(char *at_buf, char **prsp_cmd);
int at_QIREGAPP_req(char *at_buf, char **prsp_cmd);
int at_QILOCIP_req(char *at_buf, char **prsp_cmd);
int at_QNETDEVCTL_req(char *at_buf, char **prsp_cmd);
int at_QSCLK_req(char *at_buf, char **prsp_cmd);
int at_QURCCFG_req(char *at_buf, char **prsp_cmd);
int at_QCFG_req(char *at_buf, char **prsp_cmd);
int at_QPOWD_req(char *at_buf, char **prsp_cmd);
int at_QLTS_req(char *at_buf, char **prsp_cmd);
int at_CCLK_req(char *at_buf, char **prsp_cmd);
int at_QIDNSGIP_req(char *at_buf, char **prsp_cmd);
int at_QIDNSCFG_req(char *at_buf, char **prsp_cmd);
int at_QIGETERROR_req(char *at_buf, char **prsp_cmd);
int at_QADC_req(char *at_buf, char **prsp_cmd);
int at_XYCNNT_req(char *at_buf, char **prsp_cmd);
int at_FORCEDL_req(char *at_buf, char **prsp_cmd);
int at_NUESTATS_req(char *at_buf, char **prsp_cmd);
int at_SRRC_req(char *at_buf, char **prsp_cmd);
int at_QRFTESTMODE_req(char *at_buf, char **prsp_cmd);
int at_HEAPINFO_req(char *at_buf, char **prsp_cmd);
int at_AUTHID_req(char *at_buf, char **prsp_cmd);
int at_GNSS_req(char *at_buf, char **prsp_cmd);

//入库配置处理
int at_NSET_req(char *at_buf,char **prsp_cmd);

/* Test CMD */
int at_TEST_req(char *at_buf, char **prsp_cmd);
int at_DEBUG_req(char *at_buf,char **prsp_cmd);
int at_FLASHTEST_req(char *at_buf, char **prsp_cmd);
int at_RTCTEST_req(char *at_buf, char **prsp_cmd);
int at_SOCKETTEST_req(char *at_buf, char **prsp_cmd);
int at_NETLOGCFG_req(char *at_buf, char **prsp_cmd);


int at_QCAMCFG_req(char *at_buf, char **prsp_cmd);
int at_REQLIC_req(char *at_buf, char **prsp_cmd);
int at_LIC_req(char *at_buf, char **prsp_cmd);
int at_TYAUTH_req(char *at_buf, char **prsp_cmd);
int at_MUECONFIG_req(char *at_buf, char **prsp_cmd);	
int at_ATS24_req(char *at_buf, char **prsp_cmd);
int at_CBAUD_req(char *at_buf, char **prsp_cmd);

int at_MDNSCFG_req(char *at_buf, char **prsp_cmd);
int at_MDNSGIP_req(char *at_buf, char **prsp_cmd);

int at_ECADC_req(char *at_buf, char **prsp_cmd);
int at_CIPGSMLOC_req(char *at_buf, char **prsp_cmd);

int at_LBSCFG_req(char *at_buf, char **prsp_cmd);
int at_LBSLOC_req(char *at_buf, char **prsp_cmd);

int at_MADC_req(char *at_buf, char **prsp_cmd);
int at_CMVERSION_req(char *at_buf, char **prsp_cmd); 
int at_MREBOOT_req(char *at_buf, char **prsp_cmd);
int at_MPRODUCTMODE_req(char *at_buf, char **prsp_cmd);
int at_MTSETID_req(char *at_buf, char **prsp_cmd);
int at_MLED_req(char *at_buf, char **prsp_cmd);

