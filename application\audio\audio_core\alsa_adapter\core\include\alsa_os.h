#ifndef __ALSA_OS_H__
#define __ALSA_OS_H__

#include "cmsis_os2.h"

typedef osStatus_t alsa_os_status_t;
typedef osThreadFunc_t alsa_os_thread_func_t;
typedef osPriority_t alsa_os_priority_t;
typedef osThreadId_t alsa_os_thread_t;
typedef osMutexId_t alsa_os_mutex_t;
typedef osMessageQueueId_t alsa_os_mq_t;
typedef osSemaphoreId_t alsa_os_semaphore_t;
typedef osMemoryPoolId_t alsa_os_mp_t;

#ifdef __cplusplus
extern "C"
{
#endif

    alsa_os_thread_t alsa_os_thread_create(
        alsa_os_thread_func_t func,
        const char *name,
        uint32_t stack_size,
        alsa_os_priority_t priority,
        void *arg);

    alsa_os_status_t alsa_os_thread_delete(alsa_os_thread_t thread_id);

    alsa_os_mutex_t alsa_os_mutex_create(void);

    alsa_os_status_t alsa_os_mutex_wait(alsa_os_mutex_t mutex_id, uint32_t millisec);

    alsa_os_status_t alsa_os_mutex_release(alsa_os_mutex_t mutex_id);

    alsa_os_status_t alsa_os_mutex_delete(alsa_os_mutex_t mutex_id);

    alsa_os_mq_t alsa_os_mq_create(uint32_t msg_count, uint32_t msg_size);

    alsa_os_status_t alsa_os_mq_get(alsa_os_mq_t mq_id, void *msg_ptr, uint32_t millisec);

    alsa_os_status_t alsa_os_mq_put(alsa_os_mq_t mq_id, void *msg);

    alsa_os_status_t alsa_os_mq_delete(alsa_os_mq_t mq_id);

    alsa_os_semaphore_t alsa_os_semaphore_create(uint32_t max_count, uint32_t initial_count);

    alsa_os_status_t alsa_os_semaphore_acquire(alsa_os_semaphore_t semaphore_id, uint32_t timeout);

    alsa_os_status_t alsa_os_semaphore_release(alsa_os_semaphore_t semaphore_id);

    uint32_t alsa_os_semaphore_get_count(alsa_os_semaphore_t semaphore_id);

    alsa_os_status_t alsa_os_semaphore_delete(alsa_os_semaphore_t semaphore_id);

#ifdef __cplusplus
}
#endif

#endif