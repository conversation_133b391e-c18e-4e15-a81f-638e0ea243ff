/**********************************************************************/
/**
 * @file yx_al_ver_if.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-02-25
 * @version V1.0
 * @brief al相关版本信息头文件
 **********************************************************************/
#ifndef YX_AL_VER_IF_H
#define YX_AL_VER_IF_H
#include "yx_ia_system.h"

#ifdef LOAD_APP_MODULE          // APP业务模块加载宏，在业务层定义，以实现与适配层相互独立
#    include "yx_app_ver_if.h"  // APP业务层版本号
#endif

// 平台SDK的版本号,如有变更需要手动更改
#define PLAT_BASELINE_MAJOR "TLS13_EXTWDG"           /* 基线号 */
#define PLAT_BASELINE_MINOR yx_ia_sys_get_modelstr() /* 平台版本号 */
#define PLAT_RES_VERSION "p1.00"                     /* 资源号 */
#define PLAT_VAR_VERSION yx_ia_sys_get_sdkver()      /* sdk版本号 */

#endif