/**********************************************************************/
/**
 * @file yx_ia_lbs.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-20
 * @version V1.0
 * @brief 基站定位模块接口适配
 **********************************************************************/
#ifndef YX_IA_LBS_H
#define YX_IA_LBS_H

#include "yx_type.h"
#include "yx_ia_system.h"

/**
 * @brief LBS 数据结构体
 * @ingroup ia_lbs
 */
typedef struct {
    FP32 longitude;
    FP32 latitude;
    INT16U accuracy;
    INT8U lbs_flag; //0 - 正常基站 1 – 无效基站
    CHAR date_time[32];
} yx_ia_lbs_info_t;  

/**
 * @brief LBS模块句柄类型定义
 * @ingroup ia_lbs
 */
typedef struct {
    //yx_mq_ref_t  msg_queue; /**< 定位数据消息队列 */
    VOID (*yx_ia_lbs_callback_t)(yx_ia_lbs_info_t *lbs_info);  /**< HTTP回调函数指针 */
} yx_ia_lbs_handle_t;

/**

 * @brief 初始化LBS模块
 * @ingroup ia_lbs
 * @param[in] msgq 消息队列引用
 * @return yx_ia_lbs_handle_t*
 * @retval 非NULL，成功，返回模块句柄
 * @retval NULL，失败
 */
yx_ia_lbs_handle_t* yx_ia_lbs_init(void (*callback)(yx_ia_lbs_info_t *lbs_info));

/**
 * @brief 发起LBS定位请求
 * @ingroup ia_lbs
 * @param[in] handle 模块句柄
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval 其他值 失败
 * @note 该函数返回值仅为发起请求是否成功，定位结果需要在绑定的消息队列获取。
 */
INT32S yx_ia_lbs_request(yx_ia_lbs_handle_t *handle);

#endif /* YX_IA_LBS_H */