#include "atc_ps.h"
#include "memmap.h"

int AtcAp_SendCmdEventToPs()
{
    unsigned char*          pCodeStream;
    unsigned short          usCodeStreamLen;
    int                     ret;

    if(NULL == g_AtcApInfo.pCurrEvent)
    {
        return ATC_AP_FALSE;
    }

    AtcAp_Encode_UN_ATC_CMD_EVENT((UN_ATC_CMD_EVENT*)g_AtcApInfo.pCurrEvent, &pCodeStream, &usCodeStreamLen);

    AtcAp_FreeEventBuffer((unsigned char*)g_AtcApInfo.pCurrEvent);
    g_AtcApInfo.pCurrEvent = NULL;
    
#if ATC_DSP
    ret = AtcAp_SndDataReqToPs(ATC_AP_TRUE, pCodeStream, usCodeStreamLen);
#else
    ret = AtcAp_SndDataReqToShm(ATC_AP_TRUE, pCodeStream, usCodeStreamLen);
#endif
    AtcAp_Free(pCodeStream);

    return ret;
}

void AtcAp_CascadeAtProc_NextAt()
{
    unsigned short i;
    unsigned short usLength = 0;
    unsigned char *pucData  = NULL;
    ST_ATC_CASCADE_AT_INFO *pCascadeAtInfo;

    if(ATC_FALSE == g_AtcApInfo.atCascateInfo.ucCascadeAtFlg)
    {
        return;
    }

    AtcAp_PrintLog(0, LRRC_THREAD_ID, WARN_LOG, "[AtcAp_CascadeAtProc_NextAt] ucCascadeAtFlg=%d, ucCascadeAtCnt=%d",
            g_AtcApInfo.atCascateInfo.ucCascadeAtFlg,
            g_AtcApInfo.atCascateInfo.ucCascadeAtCnt);

    pCascadeAtInfo = &g_AtcApInfo.atCascateInfo;
    if(NULL == pCascadeAtInfo->pCasaadeAtBuff)
    {
        AtcAp_PrintLog(0, LRRC_THREAD_ID, WARN_LOG, "[AtcAp_CascadeAtProc_NextAt]pCasaadeAtBuff : null");
        return;
    }

    for(i = 0; i < pCascadeAtInfo->usLen - pCascadeAtInfo->offset; i++)
    {
        if (D_ATC_N_SEMICOLON == pCascadeAtInfo->pCasaadeAtBuff[pCascadeAtInfo->offset + i])
        {
            if(0 == pCascadeAtInfo->offset)//first at
            {
                usLength = i + 1;
                pucData = (unsigned char *)AtcAp_Malloc(usLength);
                if(0 != i)
                {
                    AtcAp_MemCpy(pucData, pCascadeAtInfo->pCasaadeAtBuff + pCascadeAtInfo->offset, i);
                }
            }
            else
            {
                usLength = i + 3;
                pucData = (unsigned char *)AtcAp_Malloc(usLength);
                pucData[0] = 'A';
                pucData[1] = 'T';
                if(0 != i)
                {
                    AtcAp_MemCpy(pucData + 2, pCascadeAtInfo->pCasaadeAtBuff + pCascadeAtInfo->offset, i);
                }
            }
            pucData[usLength - 1] = D_ATC_DEFAULT_CR;
            pCascadeAtInfo->offset += (i + 1);
            
            if(pCascadeAtInfo->offset == pCascadeAtInfo->usLen)
            {
                AtcAp_Free(g_AtcApInfo.atCascateInfo.pCasaadeAtBuff);
                g_AtcApInfo.atCascateInfo.usLen = 0;
            }
            
            ATC_SendApDataReq(D_DATA_REQ_TYEP_AT_CMD, ATC_AP_FALSE, 0, usLength, pucData, g_AtcApInfo.ucAtChannelId);
            AtcAp_Free(pucData);
            
            return;
        }
    }

    //last at
    usLength = pCascadeAtInfo->usLen - pCascadeAtInfo->offset + 3;
    pucData = (unsigned char *)AtcAp_Malloc(usLength);
    pucData[0] = 'A';
    pucData[1] = 'T';
    AtcAp_MemCpy(pucData + 2, pCascadeAtInfo->pCasaadeAtBuff + pCascadeAtInfo->offset, pCascadeAtInfo->usLen - pCascadeAtInfo->offset);
    pucData[usLength - 1] = D_ATC_DEFAULT_CR;

    AtcAp_Free(g_AtcApInfo.atCascateInfo.pCasaadeAtBuff);
    g_AtcApInfo.atCascateInfo.usLen = 0;
    
    ATC_SendApDataReq(D_DATA_REQ_TYEP_AT_CMD, ATC_AP_FALSE, 0, usLength, pucData, g_AtcApInfo.ucAtChannelId);
    AtcAp_Free(pucData);

}

static unsigned char AtcAp_IsCascateAtChk(ATC_AP_MSG_DATA_REQ_STRU* pDataReq)
{
    unsigned short i;
    unsigned short usSendLen = 0;
    
    if(ATC_AP_FALSE == pDataReq->ucExternalFlg)
    {
        AtcAp_PrintLog(0, NAS_THREAD_ID, DEBUG_LOG, "[ATC_IsCascateAtChk] ucExternalFlg=0");
        return ATC_AP_FALSE;
    }

    if(ATC_AP_TRUE == g_AtcApInfo.atCascateInfo.ucCascadeAtFlg) //abnormal:waitting for cascade at to end
    {
        AtcAp_PrintLog(0, NAS_THREAD_ID, DEBUG_LOG, "[ATC_IsCascateAtChk] abnormal:waitting for cascade at to end");
        AtcAp_SendErrorRsp();
        return ATC_AP_TRUE;
    }

#ifdef LTE_SMS_FEATURE
    if(ATC_AP_TRUE == AtcAp_CurrEventChk_IsWaitSmsPdu())
    {
        return ATC_AP_FALSE;
    }
#endif
    
    g_AtcApInfo.atCascateInfo.ucCascadeAtCnt = 0;
    for(i = 0; i< pDataReq->usMsgLen; i++)
    {
        if (D_ATC_N_SEMICOLON == pDataReq->aucMsgData[i])
        {
            g_AtcApInfo.atCascateInfo.ucCascadeAtCnt++;
            usSendLen = i;
        }
    }

    if(usSendLen < pDataReq->usMsgLen)
    {
        g_AtcApInfo.atCascateInfo.ucCascadeAtCnt++;
    }

    if(g_AtcApInfo.atCascateInfo.ucCascadeAtCnt > 1)
    {
        g_AtcApInfo.atCascateInfo.ucCascadeAtFlg = ATC_AP_TRUE;
        g_AtcApInfo.atCascateInfo.pCasaadeAtBuff = (unsigned char*)AtcAp_Malloc(pDataReq->usMsgLen);
        AtcAp_MemCpy(g_AtcApInfo.atCascateInfo.pCasaadeAtBuff, pDataReq->aucMsgData, pDataReq->usMsgLen);
        g_AtcApInfo.atCascateInfo.usLen = pDataReq->usMsgLen;
        g_AtcApInfo.atCascateInfo.offset = 0;

        AtcAp_PrintLog(0, NAS_THREAD_ID, DEBUG_LOG, "[ATC_IsCascateAtChk] new cascade at");
        AtcAp_CascadeAtProc_NextAt();
        
        return ATC_AP_TRUE;
    }

    AtcAp_PrintLog(0, NAS_THREAD_ID, DEBUG_LOG, "[ATC_IsCascateAtChk] no cascade at");
    return ATC_AP_FALSE;
}

/*******************************************************************************
  MODULE    : ATC_DataReqResultHandle
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  GCM   2018.10.15   create
*******************************************************************************/
static void AtcAp_DataReqResultHandle(unsigned char ucResult)
{
    switch(ucResult)
    {
    case D_ATC_COMMAND_END:
        AtcAp_SendOkRsp();
        break;
    case D_ATC_COMMAND_DELIMITOR:
    case D_ATC_COMMAND_MODE_ERROR:
        AtcAp_SendErrorRsp();
        break;
    case D_ATC_COMMAND_SYNTAX_ERROR:
    case D_ATC_COMMAND_PARAMETER_ERROR:
    case D_ATC_COMMAND_TOO_MANY_PARAMETERS:   
        AtcAp_SendCmeeErr(D_ATC_AP_CME_INCORRECT_PARAMETERS);
        break;
    case D_ATC_COMMAND_ERROR:
        AtcAp_SendCmeeErr(D_ATC_AP_CME_OPER_NOT_SUPPORED);
        break;
    default:
        AtcAp_SendErrorRsp();
        break;    
    }
    return;
}


static unsigned char AtcAp_Command_Excute(unsigned char* aucAtcPrefix,ST_ATC_CMD_COM_EVENT* pCurrEvent)
{
    unsigned char i = 0;
    ST_ATC_CMD_COM_EVENT tCmdComEvent ={ 0 };
    ST_ATC_AP_STR_EVENT_TABLE *pWorkJumpTblPtr = NULL;
    ST_ATC_COMMAND_TEST_RSP_TABLE *pTestRspTbl = NULL;
    unsigned char*  pInd = NULL;

//test need send to cp
    pWorkJumpTblPtr = (ST_ATC_AP_STR_EVENT_TABLE *)ATC_CommandTestToPS_EventTable;
    for(i = 0; (pWorkJumpTblPtr + i)->pucStr != NULL; i++)
    {
        if(AtcAp_StrCaseCmp(aucAtcPrefix,pWorkJumpTblPtr[i].pucStr))
        {
            pCurrEvent->usEvent = pWorkJumpTblPtr[i].usStrVal;
            return D_ATC_COMMAND_OK;
        }
    }
#if (!AT_TEST_OFF)
//test AT return fixed str 

    pTestRspTbl = (ST_ATC_COMMAND_TEST_RSP_TABLE *)ATC_CommandTestRspTable;
    for(i = 0; (pTestRspTbl + i)->TestRspData != NULL; i++)
    {
        if(AtcAp_StrCaseCmp(aucAtcPrefix,(pTestRspTbl + i)->LetterData))
        {
            tCmdComEvent.usEvent = D_ATC_AP_AT_CMD_RST;
            if(0 != strcmp((pTestRspTbl + i)->TestRspData, D_ATC_OK_RSP))
            {
#ifdef LTE_SMS_FEATURE
                if(1 == g_SmsFormatMode)
                {
                    if(0 == strcmp((pTestRspTbl + i)->LetterData, ATC_CNMA_COMMAND_PREFIX))
                    {
                        AtcAp_SendOkRsp();
                        return D_ATC_COMMAND_END;
                    }
                    else if(0 == strcmp((pTestRspTbl + i)->LetterData, ATC_CMGL_COMMAND_PREFIX))
                    {
                        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+%s:%s\r\n",(pTestRspTbl + i)->LetterData, D_ATC_TEST_CMD_CMGL_STRRSP);
                        AtcAp_SendDataInd((unsigned char*)&tCmdComEvent);
                        AtcAp_SendOkRsp();
                        return D_ATC_COMMAND_END;
                    }
                }
#endif
                if(strlen((pTestRspTbl + i)->TestRspData) < D_ATC_RSP_MAX_BUF_SIZE)
                {
                    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+%s:%s\r\n",(pTestRspTbl + i)->LetterData,(pTestRspTbl + i)->TestRspData);
                    AtcAp_SendDataInd((unsigned char*)&tCmdComEvent);
                }
                else
                {
                    pInd = AtcAp_Malloc(strlen((pTestRspTbl + i)->TestRspData) + strlen((pTestRspTbl + i)->LetterData) + 16 + 1);
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pInd,"\r\n+%s:%s\r\n",(pTestRspTbl + i)->LetterData,(pTestRspTbl + i)->TestRspData);
                    AtcAp_SendLongDataInd((unsigned char*)&tCmdComEvent, &pInd, g_AtcApInfo.stAtRspInfo.usRspLen);
                    AtcAp_Free(pInd);
                }
            }
            AtcAp_SendOkRsp();
            return D_ATC_COMMAND_END;
        }
    }
#endif
    return D_ATC_COMMAND_ERROR;
}

void AtcAp_AtTypeAndCmdHandle(ATC_AP_MSG_DATA_REQ_STRU *pAtcDataReq)
{
    unsigned char           i;
    unsigned char*          pAtcParam;
    unsigned char           aucAtcPrefix[D_ATC_CMD_PREFIX_MAX_LEN] = {0};
    unsigned char*          pAtcCmd;
    unsigned char           ucResult = D_ATC_COMMAND_ERROR;
    ST_ATC_COMMAND_ANAL_TABLE *pWorkJumpTblPtr = NULL;

    pAtcCmd = pAtcDataReq->aucMsgData;
    pAtcParam = AtcAp_CmdGetPrefixAndType((unsigned char*)pAtcCmd, aucAtcPrefix, &g_AtcApInfo.ucATCCmdType);
    xy_printf(0,ATC_AP_T,INFO_LOG,"AtcAp_AtTypeAndCmdHandle: pAtcCmd:%s, aucAtcPrefix:%s, ucATCCmdType:%d",
        pAtcCmd, aucAtcPrefix, g_AtcApInfo.ucATCCmdType);
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_INVALID)
    {
        AtcAp_DataReqResultHandle(D_ATC_COMMAND_SYNTAX_ERROR);
        return;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_AT_REQ)
    {
        AtcAp_DataReqResultHandle(D_ATC_COMMAND_END);
        return;
    }

    if(NULL != g_AtcApInfo.pCurrEvent)
    {
        AtcAp_FreeEventBuffer((unsigned char*)g_AtcApInfo.pCurrEvent);
    }
    g_AtcApInfo.pCurrEvent = (ST_ATC_CMD_COM_EVENT*)AtcAp_Malloc(sizeof(UN_ATC_CMD_EVENT));

    if(g_AtcApInfo.ucATCCmdType >= D_ATC_CMD_REQ  && g_AtcApInfo.ucATCCmdType <= D_ATC_CMD_QUERY)
    {
        if(D_ATC_CMD_PLUS == g_AtcApInfo.ucATSymbolType)
        {
            pWorkJumpTblPtr = (ST_ATC_COMMAND_ANAL_TABLE *)ATC_Plus_CommandTable;
            for(i = 0; (pWorkJumpTblPtr + i)->CommandProc != NULL; i++)
            {
                if(AtcAp_StrCaseCmp(aucAtcPrefix,(pWorkJumpTblPtr + i)->LetterData))
                {
                    ucResult = (pWorkJumpTblPtr + i)->CommandProc(pAtcParam,(unsigned char*)g_AtcApInfo.pCurrEvent);
                    break;
                }
            }
        }
        else if(D_ATC_CMD_STAR == g_AtcApInfo.ucATSymbolType)
        {
            pWorkJumpTblPtr = (ST_ATC_COMMAND_ANAL_TABLE *)ATC_Star_CommandTable;
            for(i = 0; (pWorkJumpTblPtr + i)->CommandProc != NULL; i++)
            {
                if(AtcAp_StrCaseCmp(aucAtcPrefix,(pWorkJumpTblPtr + i)->LetterData))
                {
                    ucResult = (pWorkJumpTblPtr + i)->CommandProc(pAtcParam,(unsigned char*)g_AtcApInfo.pCurrEvent);
                    break;
                }
            }
        }
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_TEST)
    {
        ucResult = AtcAp_Command_Excute(aucAtcPrefix, g_AtcApInfo.pCurrEvent);
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_SYMBOL_REQ)
    {
        pWorkJumpTblPtr = (ST_ATC_COMMAND_ANAL_TABLE *)ATC_Symbol_CommandTable;
        for(i = 0; (pWorkJumpTblPtr + i)->CommandProc != NULL; i++)
        {
            if(AtcAp_StrCaseCmp(aucAtcPrefix,(pWorkJumpTblPtr + i)->LetterData))
            {
                ucResult = (pWorkJumpTblPtr + i)->CommandProc(pAtcParam,(unsigned char*)g_AtcApInfo.pCurrEvent);
                break;
            }
        }
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_SINGLE_REQ)
    {
        pWorkJumpTblPtr = (ST_ATC_COMMAND_ANAL_TABLE *)ATC_Single_CommandTable;
        for(i = 0; (pWorkJumpTblPtr + i)->CommandProc != NULL; i++)
        {
            if(AtcAp_StrCaseCmp(aucAtcPrefix,(pWorkJumpTblPtr + i)->LetterData))
            {
                ucResult = (pWorkJumpTblPtr + i)->CommandProc(pAtcParam,(unsigned char*)g_AtcApInfo.pCurrEvent);
                break;
            }
        }
    }

    if(D_ATC_COMMAND_END == ucResult)
    {
        return;
    }
    else if(D_ATC_COMMAND_OK != ucResult)
    {
        AtcAp_DataReqResultHandle(ucResult);
    }
    else
    {
        if(g_AtcApInfo.pCurrEvent == NULL)
        {
            return;
        }
        g_AtcApInfo.usCurrEvent = g_AtcApInfo.pCurrEvent->usEvent;
        if(AtcAp_SendCmdEventToPs() != ATC_AP_TRUE)
        {
            AtcAp_SendErrorRsp();
        }
    }
    return;
}

static void AtcAp_DataReqProc_AtCmd(ATC_AP_MSG_DATA_REQ_STRU* pAtcDataReq)
{
    if(ATC_AP_TRUE == AtcAp_IsCascateAtChk(pAtcDataReq))
    {
        return;
    }
    AtcAp_AtTypeAndCmdHandle(pAtcDataReq);
}

static void AtcAp_DataReqProc_InterfaceData(ATC_AP_MSG_DATA_REQ_STRU* pAtcDataReq)
{
#if ATC_DSP
    AtcAp_SndDataReqToPs(ATC_AP_FALSE, pAtcDataReq->aucMsgData, pAtcDataReq->usMsgLen);
#else
    AtcAp_SndDataReqToShm(ATC_AP_FALSE, pAtcDataReq->aucMsgData, pAtcDataReq->usMsgLen);
#endif
}

static void AtcAp_SaveAtcDataReq2List(ATC_AP_MSG_DATA_REQ_STRU* pAtcDataReq)
{
    ST_ATC_AP_ATC_DATA_REQ_NODE* pAtcDataReqNode;

    pAtcDataReqNode = (ST_ATC_AP_ATC_DATA_REQ_NODE*)AtcAp_Malloc(sizeof(ST_ATC_AP_ATC_DATA_REQ_NODE));
    pAtcDataReqNode->pDataReq = pAtcDataReq;
    
    AtcAp_LinkList_AddNode((ST_ATC_AP_LINK_NODE**)&g_AtcApInfo.stAtcDataReqList.pHead,
                           (ST_ATC_AP_LINK_NODE**)&g_AtcApInfo.stAtcDataReqList.pTail,
                           (ST_ATC_AP_LINK_NODE*)pAtcDataReqNode);
}

static void AtcAp_DeleteFirstAtcDataReqInList()
{
    ST_ATC_AP_ATC_DATA_REQ_NODE* pAtcDataReqNode;

    pAtcDataReqNode = g_AtcApInfo.stAtcDataReqList.pHead;
    AtcAp_LinkList_RemoveNode((ST_ATC_AP_LINK_NODE**)&g_AtcApInfo.stAtcDataReqList.pHead,
                              (ST_ATC_AP_LINK_NODE**)&g_AtcApInfo.stAtcDataReqList.pTail,
                              (ST_ATC_AP_LINK_NODE*)pAtcDataReqNode);

    AtcAp_Free(pAtcDataReqNode);
}

static ATC_AP_MSG_DATA_REQ_STRU* AtcAp_GetFirstAtcDataReqFromList()
{
    ATC_AP_MSG_DATA_REQ_STRU*    pDataReq;

    if(NULL == g_AtcApInfo.stAtcDataReqList.pHead)
    {
        return NULL;
    }

    pDataReq = g_AtcApInfo.stAtcDataReqList.pHead->pDataReq;

    AtcAp_DeleteFirstAtcDataReqInList();
    
    return pDataReq;
}

unsigned char AtcAp_IsCancelAtCmdSupportChk(ATC_AP_MSG_DATA_REQ_STRU* pAtcDataReq)
{
    if(ATC_AP_FALSE == pAtcDataReq->ucExternalFlg || 0 != pAtcDataReq->ulSemaId || ATC_AP_TRUE == g_AtcApInfo.atCascateInfo.ucCascadeAtFlg)
    {
        return ATC_AP_FALSE;
    }

    if(ATC_AP_FALSE == g_AtcApInfo.ucWaitOKOrErrorFlg || D_ATC_EVENT_COPS_T != g_AtcApInfo.usCurrEvent)
    {
        return ATC_AP_FALSE;
    }

    return ATC_AP_TRUE;
}

unsigned char AtcAp_IsCancelAtCmdChk(ATC_AP_MSG_DATA_REQ_STRU* pAtcDataReq)
{
    if(ATC_AP_FALSE == AtcAp_IsCancelAtCmdSupportChk(pAtcDataReq))
    {
        return ATC_AP_FALSE;
    }

    if(0 == strcmp((char*)pAtcDataReq->aucMsgData, (char*)"AT+ABORT\r"))
    {
        return ATC_AP_TRUE;
    }
    else
    {
        return ATC_AP_FALSE;
    }
}

void AtcAp_CancelAtCmdProc(ATC_AP_MSG_DATA_REQ_STRU* pAtcDataReq)
{
    ST_ATC_CMD_COM_EVENT *pCmdCom; 

    pCmdCom = (ST_ATC_CMD_COM_EVENT*)AtcAp_Malloc(sizeof(UN_ATC_CMD_EVENT));
    pCmdCom->usEvent = D_ATC_EVENT_AT_ABORT;
    
    g_AtcApInfo.pCurrEvent = pCmdCom;
    AtcAp_SendCmdEventToPs();
}

static unsigned char AtcAp_MsgProc_AtcApDataReq(ATC_AP_MSG_DATA_REQ_STRU* pAtcDataReq)
{
    xy_printf(0,ATC_AP_T,INFO_LOG,"AtcAp_MsgProc_AtcApDataReq: cmd=%s, ucExternalFlg=%d, ulSemaId=%d, ucAtChannelId=%d",
        pAtcDataReq->aucMsgData, pAtcDataReq->ucExternalFlg, pAtcDataReq->ulSemaId, pAtcDataReq->ucAtChannelId);

    if(ATC_AP_TRUE == AtcAp_IsCancelAtCmdChk(pAtcDataReq))
    {
        AtcAp_CancelAtCmdProc(pAtcDataReq);
        return ATC_AP_TRUE;
    }
                
    if(ATC_AP_TRUE == pAtcDataReq->ucExternalFlg)
    {
        if(ATC_AP_TRUE == g_AtcApInfo.ucWaitOKOrErrorFlg)
        {
            if(ATC_AP_TRUE == g_AtcApInfo.ucUserAtFlg)
            {
                if(ATC_AP_TRUE == AtcAp_CurrEventChk_IsWaitSmsPdu() && 0 != pAtcDataReq->ulSemaId)
                {
                    AtcAp_AddAppInterfaceInfo(pAtcDataReq->ulSemaId, ATC_AP_TRUE);
#ifdef LTE_SMS_FEATURE
                    if(g_SmsFormatMode == 0)
                    {
                        AtcAp_SmsPdu_Analysis((ATC_AP_MSG_DATA_REQ_STRU*)pAtcDataReq);
                    }
                    else
                    {
                        AtcAp_SmsText_Analysis((ATC_AP_MSG_DATA_REQ_STRU*)pAtcDataReq);
                    }
#endif
                    return ATC_AP_TRUE;
                }
                else
                {
                    AtcAp_SaveAtcDataReq2List(pAtcDataReq);
                    return ATC_AP_FALSE;
                }
            }
            else if(g_AtcApInfo.ucAtChannelId == pAtcDataReq->ucAtChannelId
                && ATC_AP_TRUE == AtcAp_CurrEventChk_IsWaitSmsPdu())
            {
#ifdef LTE_SMS_FEATURE
                if(g_SmsFormatMode == 0)
                {
                    AtcAp_SmsPdu_Analysis((ATC_AP_MSG_DATA_REQ_STRU*)pAtcDataReq);
                }
                else
                {
                    AtcAp_SmsText_Analysis((ATC_AP_MSG_DATA_REQ_STRU*)pAtcDataReq);
                }
#endif
                return ATC_AP_TRUE;
            }
            else if(g_AtcApInfo.ucAtChannelId != pAtcDataReq->ucAtChannelId)
            {
                AtcAp_SaveAtcDataReq2List(pAtcDataReq);
                return ATC_AP_FALSE;
            }
        }
        else
        {
            g_AtcApInfo.ucWaitOKOrErrorFlg = ATC_AP_TRUE;
        
            if(0 == pAtcDataReq->ulSemaId)
            {
                g_AtcApInfo.ucUserAtFlg = ATC_AP_FALSE;
                g_AtcApInfo.ucAtChannelId = pAtcDataReq->ucAtChannelId;
            }
            else
            {
                g_AtcApInfo.ucUserAtFlg = ATC_AP_TRUE;
                AtcAp_AddAppInterfaceInfo(pAtcDataReq->ulSemaId, ATC_AP_TRUE);
            }
        }
    }

    if(D_DATA_REQ_TYEP_AT_CMD == pAtcDataReq->ucReqType)
    {
        AtcAp_DataReqProc_AtCmd(pAtcDataReq);
    }
    else
    {
        AtcAp_DataReqProc_InterfaceData(pAtcDataReq);
    }
    
    return ATC_AP_TRUE;
}

static unsigned char AtcAp_MsgProc_UserAtCnf(ATC_MSG_DATA_IND_STRU* pAtcDataInd)
{
    ST_ATC_AP_CMD_RST*                pCmdRst = (ST_ATC_AP_CMD_RST*)(pAtcDataInd->aucMsgData);
    ST_ATC_AP_APP_INTERFACE_NODE*     pAppInterfaceInfo;
    
    if(0 == pAtcDataInd->ucSeqNum)
    {
        return ATC_AP_FALSE;
    }

    if(D_ATC_AP_AT_CMD_RST == pCmdRst->usEvent)
    {
        g_AtcApInfo.ucTempSeqNum = pAtcDataInd->ucSeqNum;
        AtcAp_MsgProc_AT_CMD_RST(pAtcDataInd->aucMsgData);
    }
    else if(D_ATC_AP_SMS_PDU_IND == pCmdRst->usEvent)
    {
        g_AtcApInfo.stAppInterfaceInfo.ucSeqNum = pAtcDataInd->ucSeqNum - 1;
        AtcAp_AppInterfaceInfo_CmdRstProc(pAtcDataInd->ucSeqNum, D_APP_INTERFACE_RESULT_SUCC);
    }
    else
    {
        osMutexAcquire(g_AtcApInfo.stAppInterfaceInfo.mutex, osWaitForever);
        pAppInterfaceInfo = AtcAp_GetAppInterfaceInfo_BySeqNum(pAtcDataInd->ucSeqNum);
        if(pAppInterfaceInfo == NULL)
        {
            osMutexRelease(g_AtcApInfo.stAppInterfaceInfo.mutex);
            return ATC_AP_FALSE;
        }
        
        pAppInterfaceInfo->usDataLen = pAtcDataInd->usMsgLen;
        if(0 != pAtcDataInd->usMsgLen)
        {
            if(pAppInterfaceInfo->pucData != NULL)
            {
                AtcAp_Free(pAppInterfaceInfo->pucData);
            }
            pAppInterfaceInfo->pucData = (unsigned char*)AtcAp_Malloc(pAtcDataInd->usMsgLen + 1);
            AtcAp_MemCpy(pAppInterfaceInfo->pucData, pAtcDataInd->aucMsgData, pAtcDataInd->usMsgLen);
        }
        osMutexRelease(g_AtcApInfo.stAppInterfaceInfo.mutex);
        
        if(D_HW_LM_INTERFACE_CNF == pCmdRst->usEvent)
        {
            osSemaphoreRelease((osSemaphoreId_t)pAppInterfaceInfo->ulSemaphore);
            g_AtcApInfo.ucWaitOKOrErrorFlg = ATC_AP_FALSE;
            AtcAp_AtcDataReqListProc();
        }
    }

    return ATC_AP_TRUE;
}

static unsigned char AtcAp_DataIndProc_IsRegEventIndChk(unsigned short usEvent, unsigned long* pulEventId)
{
    unsigned char i;

    if(D_ATC_AP_SIMST_IND == usEvent)
    {
        *((volatile unsigned int*)PS_URC_CTL) = *g_pRegEventId;
        xy_printf(0, ATC_AP_T, INFO_LOG, "[AtcAp_DataIndProc_IsRegEventIndChk]:g_pRegEventId:%x,PS_URC_CTL:%x", *g_pRegEventId, *((volatile unsigned int*)PS_URC_CTL));
        csi_dcache_clean_range((void*)PS_URC_CTL, 4);
    }
    
    for(i = 0; i < D_ATC_USER_REG_EVENT_TBL_SIZE; i++)
    {
        if(ATC_AP_PsRegEventIdMapTable[i][1] == usEvent)
        {
            if(0 != (ATC_AP_PsRegEventIdMapTable[i][0] & (*g_pRegEventId)))
            {
                *pulEventId = ATC_AP_PsRegEventIdMapTable[i][0];
                return ATC_AP_TRUE;
            }
            break;
        }
    }

    return ATC_AP_FALSE;
}

void  AtcAp_DataIndProc_RegEventInd(unsigned char* pRecvMsg, unsigned short usLen)
{
    ST_ATC_CMD_COM_EVENT*             pCmdEvent;
    unsigned long                     ulEvnetId;
    ST_ATC_AP_PS_EVENT_REGISTER_INFO* pNode;
    
    pCmdEvent = (ST_ATC_CMD_COM_EVENT*)pRecvMsg;
    if(ATC_AP_FALSE == AtcAp_DataIndProc_IsRegEventIndChk(pCmdEvent->usEvent, &ulEvnetId))
    {
        return;
    }

    osMutexAcquire(g_pRegEventId_Mutex, osWaitForever);
    pNode = g_AtcApInfo.pEventRegList;
    while(pNode != NULL)
    {
        if(pNode->eventId == ulEvnetId && NULL != pNode->callback)
        {
            pNode->callback(ulEvnetId, (void*)pRecvMsg, usLen);
        }
        pNode = (ST_ATC_AP_PS_EVENT_REGISTER_INFO*)(pNode->node.next);
    }
    osMutexRelease(g_pRegEventId_Mutex);
}

static void AtcAp_MsgProc_AtcApDataInd(ATC_MSG_DATA_IND_STRU* pAtcDataInd)
{
    ST_ATC_CMD_COM_EVENT*   pCmdEvent;

    pCmdEvent = (ST_ATC_CMD_COM_EVENT*) pAtcDataInd->aucMsgData;
    xy_printf(0, ATC_AP_T, INFO_LOG, "[AtcAp_MsgProc_AtcApDataInd]: ucSeqNum=%d, event=%d",pAtcDataInd->ucSeqNum, pCmdEvent->usEvent);

    if(ATC_AP_TRUE == AtcAp_MsgProc_UserAtCnf(pAtcDataInd))
    {
        xy_printf(0, ATC_AP_T, INFO_LOG, "[AtcAp_MsgProc_AtcApDataInd]: userAtCnf");
    }

    AtcAp_SendMsg2AtcAp((void*)pAtcDataInd, &g_AtcApInfo.MsgInfo_EventCb);
}

void AtcAp_AtcDataReqListProc()
{
    ATC_AP_MSG_DATA_REQ_STRU*    pDataReq;
    
    pDataReq = AtcAp_GetFirstAtcDataReqFromList();
    if(pDataReq != NULL)
    {
        if(ATC_AP_TRUE == AtcAp_MsgProc_AtcApDataReq(pDataReq))
        {
            AtcAp_Free(pDataReq);
        }
    }
}

unsigned char AtcAp_NormalMsgDistribute(unsigned char *pucRcvMsg)
{
    ATC_AP_MSG_HEADER_STRU         *pMsgHeader;
    unsigned char                   ucRtnCode   = ATC_AP_TRUE;

    osMutexAcquire(g_AtcApInfo.stAtRspInfo.mutex, osWaitForever);
    if(NULL == g_AtcApInfo.stAtRspInfo.aucAtcRspBuf)
    {
        g_AtcApInfo.stAtRspInfo.aucAtcRspBuf = (unsigned char*)AtcAp_Malloc(D_ATC_RSP_MAX_BUF_SIZE);
    }

    pMsgHeader = (ATC_AP_MSG_HEADER_STRU*)pucRcvMsg;
    if (D_ATC_AP_DATA_REQ == pMsgHeader->ulMsgName)
    {
        ucRtnCode = AtcAp_MsgProc_AtcApDataReq((ATC_AP_MSG_DATA_REQ_STRU*)pucRcvMsg);
    }
    else if(D_ATC_DATA_IND == pMsgHeader->ulMsgName)
    {
        AtcAp_MsgProc_AtcApDataInd((ATC_MSG_DATA_IND_STRU*)pucRcvMsg);
        ucRtnCode = ATC_AP_FALSE;
    }

    if(g_AtcApInfo.msgInfo.ucCnt == 0)
    {
        AtcAp_Free(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    }
    osMutexRelease(g_AtcApInfo.stAtRspInfo.mutex);
    
    return ucRtnCode;
}


