/**********************************************************************/
/**
 * @file yx_ia_mqtt.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-06-05
 * @version V1.0
 * @brief mqtt模块接口适配
 **********************************************************************/
#ifndef YX_IA_MQTT_H
#define YX_IA_MQTT_H

#include "yx_type.h"
#include "module_id_def.h"
#define IA_MQTT_ERRNO            (0x80 + YX_FUNC_MODULE_ID_MQTT)

/**
 * @brief 错误码枚举
 * @ingroup ia_mqtt
 */
typedef enum {
    IA_MQTT_SUCCESS             = RTN_OK,                                   /**< 成功 */
    IA_MQTT_INVALID_PARAM_ERR   = (IA_MQTT_ERRNO << 24) | 0x01,             /**< 参数错误 */
    IA_MQTT_MALLOC_ERR          = (IA_MQTT_ERRNO << 24) | 0x02,             /**< 内存分配失败 */
    IA_MQTT_INIT_ERR            = (IA_MQTT_ERRNO << 24) | 0x03,             /**< 初始化失败 */
    IA_MQTT_NOT_INIT_ERR        = (IA_MQTT_ERRNO << 24) | 0x04,             /**< 未初始化 */
    IA_MQTT_NOT_CONNECTED_ERR   = (IA_MQTT_ERRNO << 24) | 0x05,             /**< 未连接 */
} ia_mqtt_errno_e;

/**
 * @brief mqtt客户端句柄类型定义
 * @ingroup ia_mqtt
 */
typedef struct yx_ia_mqtt_client_t yx_ia_mqtt_client_t;     /**< mqtt客户端句柄 */

/**
 * @brief mqtt回调事件枚举定义
 * @ingroup ia_mqtt
 */
typedef enum{
    MQTT_CALLBACK_EVENT_CONNECT,             /**< 连接到平台 */
    MQTT_CALLBACK_EVENT_DISCONNECT,          /**< 主动与平台断开连接 */
    MQTT_CALLBACK_EVENT_CONNECTION_LOST,     /**< 连接异常丢失 */

    MQTT_CALLBACK_EVENT_SUBSCRIBE,           /**< 订阅 Topic */
    MQTT_CALLBACK_EVENT_UNSUBSCRIBE,         /**< 取消订阅 Topic */
    MQTT_CALLBACK_EVENT_PUBLISH,             /**< 发布消息 */

    MQTT_CALLBACK_EVENT_MESSAGE,             /**< 收到订阅的 Topic 消息 */
    MQTT_CALLBACK_EVENT_MAX                  /**< 最大事件数 */
}yx_ia_mqtt_client_callback_event_e;

/**
 * @brief ssl验证级别枚举定义
 * @ingroup ia_mqtt
 */
typedef enum {
    MQTT_SSL_NONE = 0,                      /**< 不使用SSL */
    MQTT_SSL_VERIFY_NONE,                   /**< 不验证服务器证书 */
    MQTT_SSL_VERIFY_SERVER,                 /**< 验证服务器证书 */
    MQTT_SSL_VERIFY_SERVER_CLIENT,          /**< 双向验证 */
    MQTT_SSL_VERIFY_MAX                     /**< 最大验证级别 */
} yx_ia_mqtt_ssl_verify_level_e;

/**
 * @brief ssl证书类型定义
 * @ingroup ia_mqtt
 */
typedef enum {
    MQTT_SSL_CERT_TYPE_NONE = 0,            /**< 不使用证书 */
    MQTT_SSL_CERT_TYPE_FILE,                /**< 文件证书 */
    MQTT_SSL_CERT_TYPE_BUFFER,              /**< 缓存证书 */
    MQTT_SSL_CERT_TYPE_MAX                  /**< 最大证书类型 */
} yx_ia_mqtt_ssl_cert_type_e;

/**
 * @brief mqtt连接参数结构体定义
 * @ingroup ia_mqtt
 */
typedef struct {
    const CHAR* client_id;                  /**< 客户端ID */
    const CHAR* client_user;                /**< 客户端用户名,不使用设为NULL */
    const CHAR* client_pass;                /**< 客户端密码,不使用设为NULL */
    INT16U keep_alive;                      /**< 保持连接的心跳间隔,单位秒，0为禁用 */
    INT8U pkt_timeout;                      /**< MQTT数据包超时时间,单位秒,范围1-60 */
    const CHAR* will_topic;                 /**< 遗嘱主题,不使用设为NULL */
    const CHAR* will_msg;                   /**< 遗嘱消息,不使用设为NULL */
    INT8U will_qos;                         /**< 遗嘱消息QoS等级,范围0-2 */
    INT8U will_retain;                      /**< 遗嘱消息保留标志,0为不保留,1为保留 */
    INT8U clean_session;                    /**< 是否清除会话状态,0为不清除,1为清除 */
} yx_ia_mqtt_connect_config_t;

/**
 * @brief ssl配置结构体定义
 * @ingroup ia_mqtt
 */
typedef struct {
    yx_ia_mqtt_ssl_verify_level_e verify_level; /**< SSL验证级别,见yx_ia_mqtt_ssl_verify_level_e */
    yx_ia_mqtt_ssl_cert_type_e cert_type;       /**< 证书类型,见yx_ia_mqtt_ssl_cert_type_e */
    INT8U ssl_contxtid;                         /**< SSL上下文ID（范围0-9,0为默认,不可用于mqtts） */

    const INT8U *root_crt_path;                 /**< CA证书路径 */
    const INT8U *client_crt_path;               /**< 客户端证书路径 */
    const INT8U *client_key_path;               /**< 客户端私钥路径 */

    CHAR* root_crt_buffer;                      /**< CA 证书buffer指针 */
    CHAR* client_crt_buffer;                    /**< 本地证书buffer指针 */
    CHAR* client_key_buffer;                    /**< 客户端私钥buffer指针 */

    CHAR* client_key_pwd;                       /**< 客户端私钥密码 */
} yx_ia_mqtt_ssl_config_t;

/**
 * @brief mqtt回调结果信息结构体定义
 * @ingroup ia_mqtt
 */
typedef struct {
    INT32S  result;         /**< 请求结果，RTN_OK表示成功，其他值表示失败 */
    INT32S  pkt_id;         /**< 消息ID */
    CHAR*  topic;           /**< 订阅/发布的Topic名称 */
    CHAR*  payload;         /**< 消息内容 */
    INT16U  payload_len;    /**< 消息长度 */
} yx_ia_mqtt_result_info_t;

/**
 * @brief MQTT客户端回调函数结构体定义
 * @ingroup ia_mqtt
 */
typedef struct {
    VOID (*mqtt_callback)(yx_ia_mqtt_client_callback_event_e event, yx_ia_mqtt_result_info_t *result_info);  /**< MQTT回调函数指针 */
} yx_ia_mqtt_callback_t;

/**
 * @brief 初始化MQTT客户端
 * @ingroup ia_mqtt
 * @param[in] callback MQTT回调函数结构体指针
 * @return yx_ia_mqtt_client_t*
 * @retval 非NULL，成功，返回客户端句柄
 * @retval NULL，失败
 */
yx_ia_mqtt_client_t* yx_ia_mqtt_init(yx_ia_mqtt_callback_t *callback);

/**
 * @brief 反初始化MQTT客户端并释放资源
 * @ingroup ia_mqtt
 * @param[in] client 客户端句柄
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval 其他值 失败
 */
INT32S yx_ia_mqtt_deinit(yx_ia_mqtt_client_t *client);

/**
 * @brief 连接MQTT服务器
 * @ingroup ia_mqtt
 * @param[in] client 客户端句柄
 * @param[in] host 服务器地址
 * @param[in] conn_config 连接参数结构体指针
 * @param[in] ssl_config SSL参数结构体指针（暂不支持，填NULL）
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval 其他值 失败
 */
INT32S yx_ia_mqtt_connect(yx_ia_mqtt_client_t *client, const CHAR *host, yx_ia_mqtt_connect_config_t *conn_config, yx_ia_mqtt_ssl_config_t *ssl_config);

/**
 * @brief 断开MQTT服务器连接
 * @ingroup ia_mqtt
 * @param[in] client 客户端句柄
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval 其他值 失败
 */
INT32S yx_ia_mqtt_disconnect(yx_ia_mqtt_client_t *client);

/**
 * @brief 订阅主题
 * @ingroup ia_mqtt
 * @param[in] client 客户端句柄
 * @param[in] topic 主题名称
 * @param[in] qos 服务质量等级（0/1/2）
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval 其他值 失败
 */
INT32S yx_ia_mqtt_sub(yx_ia_mqtt_client_t *client, const CHAR *topic, INT8U qos);

/**
 * @brief 取消订阅主题
 * @ingroup ia_mqtt
 * @param[in] client 客户端句柄
 * @param[in] topic 主题名称
 * @param[in] qos 服务质量等级（0/1/2）
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval 其他值 失败
 */
INT32S yx_ia_mqtt_unsub(yx_ia_mqtt_client_t *client, const CHAR *topic, INT8U qos);

/**
 * @brief 发布消息
 * @ingroup ia_mqtt
 * @param[in] client 客户端句柄
 * @param[in] topic 主题名称
 * @param[in] payload 消息内容指针
 * @param[in] payload_len 消息长度
 * @param[in] qos 服务质量等级（0/1/2）
 * @param[in] retain 是否保留消息（0/1）
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval 其他值 失败
 */
INT32S yx_ia_mqtt_publish(yx_ia_mqtt_client_t *client, const CHAR *topic, const VOID *payload, INT16U payload_len, INT8U qos, INT8U retain);

#endif /* YX_IA_MQTT_H */