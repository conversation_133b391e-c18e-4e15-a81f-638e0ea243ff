/**********************************************************************/
/**
 * @file yx_utils_str.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-11
 * @version V1.0
 * @brief 字符串处理封装接口
 **********************************************************************/
#ifndef YX_UTILS_STR_H
#define YX_UTILS_STR_H

#include <stdarg.h>

#include "yx_type.h"

////////////////////////////////////////////////////////////////////////
// 结构体及枚举定义

/**
 * @brief 校验和类型枚举
 * @ingroup yx_utils
 */
typedef enum
{
    CHECKSUM_ADD,          /**< 累加校验和 */
    CHECKSUM_ADD_INVERTED, /**< 累加校验和取反 */
    CHECKSUM_XOR           /**< 异或校验 */
} CHECKSUM_TYPE_E;

#ifdef __BIG_ENDIAN
#    undef __BIG_ENDIAN
#endif

/**
 * @brief 半字大小端定义
 * @ingroup yx_utils
 */
typedef union
{
    INT16U hword;
#ifdef __BIG_ENDIAN
    struct
    {
        INT8U high;
        INT8U low;
    } bytes;
#else
    struct
    {
        INT8U low;
        INT8U high;
    } bytes;
#endif
} hword_u;

/**
 * @brief 字大小端定义
 * @ingroup yx_utils
 */
typedef union
{
    INT32U ulong;
#ifdef __BIG_ENDIAN
    struct
    {
        INT8U byte1;
        INT8U byte2;
        INT8U byte3;
        INT8U byte4;
    } bytes;
#else
    struct
    {
        INT8U byte4;
        INT8U byte3;
        INT8U byte2;
        INT8U byte1;
    } bytes;
#endif
} lword_u;

/**
 * @brief 字节转义所用规则结构
 * @ingroup yx_utils
 */
typedef struct
{
    INT8U c_flags;    /**< c_convert0 + c_convert1 = c_flags */
    INT8U c_convert0; /**< c_convert0 + c_convert2 = c_convert0 */
    INT8U c_convert1;
    INT8U c_convert2;
} asm_rule_t;

/**
 * @brief 计算字符串的长度。（封装 strlen）
 * @ingroup yx_utils
 * @param str 需要计算长度的字符串指针。
 * @return 返回字符串的长度。如果 str 为 NULL，则返回错误码 RTN_ERR。
 */
INT32S yx_utils_str_strlen(const CHAR* str);

/**
 * @brief 安全地将源字符串复制到目标缓冲区
 * @ingroup yx_utils
 * @param dest 目标缓冲区指针，用于存储复制后的字符串
 * @param destsz 目标缓冲区的大小（字节数）
 * @param src 源字符串指针，需要被复制的字符串
 * @return 成功返回RTN_OK
 *         目标缓冲区或源字符串为空指针
 *         目标缓冲区大小为0或小于等于源字符串长度
 *         则返回RTN_ERR
 */
INT32S yx_utils_str_strcpy_s(CHAR* dest, const INT32U destsz, const CHAR* src);

/**
 * @brief 安全地将源字符串追加到目标缓冲区末尾
 * @ingroup yx_utils
 * @param dest 目标缓冲区指针，用于存储追加后的字符串
 * @param destsz 目标缓冲区的大小（字节数）
 * @param src 源字符串指针，需要被追加的字符串
 * @return 成功返回RTN_OK
 *         目标缓冲区或源字符串为空指针
 *         目标缓冲区大小为0
 *         目标缓冲区的大小 < (目标缓冲区已有长度 + 源字符串长度 + 1)
 *         则返回RTN_ERR
 */
INT32S yx_utils_str_strcat_s(CHAR* dest, INT32U destsz, const CHAR* src);

/**
 * @brief 比较两个字符串 s1 和 s2。
 *
 * 该函数首先检查输入的两个字符串指针是否为 NULL，然后根据情况返回相应的值。
 * 如果两个字符串都为 NULL，则认为它们相等，返回 0。
 * 如果只有 s1 为 NULL，则返回 -1。
 * 如果只有 s2 为 NULL，则返回 1。
 * 如果两个字符串都不为 NULL，则调用标准库函数 strcmp 进行比较，并返回其返回值。
 * @ingroup yx_utils
 * @param s1 第一个字符串的指针。
 * @param s2 第二个字符串的指针。
 * @return INT32S 返回值为 0 表示两个字符串相等，小于 0 表示 s1 小于 s2，大于 0 表示 s1 大于 s2。
 */
INT32S yx_utils_str_strcmp(const CHAR* s1, const CHAR* s2);

/**
 * @brief 格式化字符串到缓冲区（可变参数封装）
 * @ingroup yx_utils
 * @param str 目标缓冲区指针（调用者需确保空间足够）
 * @param format 格式字符串
 * @param ... 可变参数列表
 * @return 成功写入的字符数（不含终止符），失败返回-1
 * @note 保留vsprintf原生特性，仅添加：
 *        - 基础参数校验
 *        - 错误返回值统一
 */
INT32S yx_utils_str_sprintf(CHAR* str, CHAR* format, ...);

/**
 * @brief 安全格式化输出（封装 snprintf）
 * @ingroup yx_utils
 * @param dest 目标缓冲区
 * @param n 缓冲区大小
 * @param format 格式字符串
 * @param ... 可变参数
 * @return 成功写入的字符数（不含终止符），参数无效时返回 -1
 */
INT32S yx_utils_str_snprintf(CHAR* dest, INT32U n, const CHAR* format, ...);

/**
 * @brief 安全变参格式化输出（封装 vsnprintf）
 * @ingroup yx_utils
 * @param dest 目标缓冲区
 * @param n 缓冲区大小
 * @param format 格式字符串
 * @param args 变参列表
 * @return 成功写入的字符数（不含终止符），参数无效时返回 -1
 */
INT32S yx_utils_str_vsnprintf(CHAR* dest, INT32U n, const CHAR* format, va_list args);

/**
 * @brief 单字节校验码算法（累加和校验）
 * @ingroup yx_utils
 * @param[in] ptr 目标数据地址
 * @param[in] len 目标数据长度
 * @return 单字节校验码
 * @note 校验码计算方式：累加所有字节，高位溢出部分回卷到低位，最后结果+1
 */
INT8U yx_utils_str_chksum_1(CHAR* ptr, INT32U len);

/**
 * @brief 单字节反码校验和算法
 * @ingroup yx_utils
 * @param[in] ptr 目标数据地址
 * @param[in] len 目标数据长度
 * @return 单字节校验码
 * @note 计算过程：每个字节取反后累加，高位溢出回卷，最后结果+1
 */
INT8U yx_utils_str_chksum_1b(CHAR* ptr, INT32U len);

/**
 * @brief 双字节互补校验和算法
 * @ingroup yx_utils
 * @param[in] ptr 目标数据地址
 * @param[in] len 目标数据长度
 * @return 双字节校验码
 * @note 高字节为常规校验和，低字节为反码校验和，组合成16位校验值
 */
INT16U yx_utils_str_dual_comp_chksum(CHAR* ptr, INT32U len);

/**
 * @brief 通用校验和计算
 * @ingroup yx_utils
 * @param[in] dptr 数据区起始地址
 * @param[in] len 数据区长度
 * @param[in] type 校验类型：
 *                 - CHECKSUM_ADD: 累加校验
 *                 - CHECKSUM_ADD_INVERTED: 累加后取反
 *                 - CHECKSUM_XOR: 异或校验
 * @return 计算后的校验字节
 */
INT8U yx_utils_str_checksum(CHAR* dptr, INT16U len, CHECKSUM_TYPE_E type);

/**
 * @brief 通用校验和计算（16位）
 * @param[in] dptr 数据区起始地址
 * @param[in] len 数据区长度
 * @return 计算后的校验字节
 */
INT16U yx_utils_str_checksum_16(CHAR* dptr, INT16U len);

/**
 * @brief 比较两个字符串，可选择是否区分大小写。
 * @ingroup yx_utils
 * @param matchcase 是否区分大小写：TRUE 表示区分大小写，FALSE 表示不区分大小写。
 * @param ptr1 第一个字符串的指针。
 * @param ptr2 第二个字符串的指针。
 * @param len1 第一个字符串的长度。
 * @param len2 第二个字符串的长度。
 * @return INT8U 返回比较结果： 0 表示两个字符串相等，
 *                             1 表示 ptr1 大于 ptr2，
 *                             2 表示 ptr1 小于 ptr2。
 * 如果字符串长度不同，则返回较短的字符串为较小的结果。
 */
INT8U yx_utils_str_cmp_str(BOOLEAN matchcase, const CHAR* ptr1, const CHAR* ptr2, INT32U len1, INT32U len2);

/**
 * @brief 将4位十六进制数转换为可打印ASCII字符
 * @ingroup yx_utils
 * @param hex 输入数值（仅低4位有效，0x00-0x0F）
 * @return ASCII字符（'0'-'9' 或 'A'-'F'）
 */
CHAR yx_utils_str_hex_to_char(INT8U hex);

/**
 * @brief 将ASCII字符转换为4位十六进制数值
 * @ingroup yx_utils
 * @param schar 输入字符（支持大小写，'0'-'9'/'A'-'F'/'a'-'f'）
 * @return 对应的十六进制值（0x00-0x0F），无效字符返回RTN_ERR
 */
INT32S yx_utils_str_char_to_hex(CHAR schar);

/**
 * @brief 十进制数值转ASCII字符串，如0x1122(十进制值为4386),则转换ASCII后为"4386"
 * @ingroup yx_utils
 * @param[out] dptr 输出缓冲区地址
 * @param[in] data 待转换的数值
 * @param[in] reflen 格式化长度：
 *                 - 0: 按实际位数输出
 *                 - >0: 固定长度输出（不足补前导零）
 * @return 转换后的字符数量（包含结束符）
 * @retval 0 表示转换失败
 */
INT8U yx_utils_str_dec_to_ascii(CHAR* dptr, INT32U data, INT8U reflen);

/**
 * @brief 将ASCII字符串转换为十进制数值，如ASCII为"4386",转换后为0x1122(十进制值为4386)
 * @ingroup yx_utils
 * @param[in] sptr 输入字符串指针（ASCII字符数组）
 * @param[in] slen 输入字符串长度
 * @return 转换后的十进制数值
 * @warning
 *       - 输入字符串必须为纯数字字符（'0'-'9'），否则结果可能不正确。
 *       - 未处理字符串长度超过 `INT32U` 范围的情况。
 */
INT32U yx_utils_str_ascii_to_dec(CHAR* sptr, INT8U slen);

/**
 * @brief 将浮点ASCII字符串转换为浮点型数值，如ASCII为"116.298513",转换后为116.298513
 * @param[in] sptr 输入字符串指针（ASCII字符数组）
 * @param[in] slen 输入字符串长度
 * @return 转换后的浮点数值
 * @warning
 *       - 输入字符串必须为纯数字字符（'0'-'9'），否则结果可能不正确。
 *       - 未处理字符串长度超过 `FP64` 范围的情况。
 */
FP64 yx_utils_str_fp_ascii_to_dec(CHAR* sptr, INT8U slen);

/**
 * @brief 字符串搜索
 * @ingroup yx_utils
 * @param[in] ptr 目标数据地址
 * @param[in] maxlen 最大搜索长度
 * @param[in] sptr 待搜索字符串（以'\0'结尾）
 * @retval TRUE 找到完整匹配
 * @retval FALSE 未找到匹配
 * @note 支持部分匹配检测，时间复杂度O(n)
 */
BOOLEAN yx_utils_str_search_keyword(CHAR* ptr, INT16U maxlen, CHAR* sptr);

/**
 * @brief 在字符串中搜索指定标志字符后的数字字符串，并将其转换为数值
 * @ingroup yx_utils
 * @param[in] ptr 输入字符串指针
 * @param[in] maxlen 输入字符串的最大长度
 * @param[in] flagCHAR 标志字符，用于定位数字字符串的起始位置
 * @param[in] numflag 标志字符的出现次数，用于确定搜索的目标位置
 * @return 转换后的数值：
 *         - 成功：返回数字字符串对应的数值
 *         - 失败：返回 0xFFFF
 * @note 实现逻辑：
 *       1. 遍历字符串，查找第 `numflag` 次出现的 `flagCHAR`。
 *       2. 从 `flagCHAR` 后开始搜索连续的数字字符。
 *       3. 将找到的数字字符转换为数值。
 * @warning
 *       - 如果未找到数字字符串或转换失败，返回 0xFFFF。
 *       - 输入字符串必须以 `flagCHAR` 和数字字符为有效内容。
 */
INT16U yx_utils_str_search_digital_str(CHAR* ptr, INT16U maxlen, CHAR flagCHAR, INT8U numflag);

/**
 * @brief 在字符串中查找指定字符的位置
 * @ingroup yx_utils
 * @param[in] sptr 输入字符串指针
 * @param[in] findchar 需要查找的字符
 * @param[in] numchar 查找的次数（0 表示第一次出现，1 表示第二次出现，依此类推）
 * @param[in] maxlen 输入字符串的最大长度
 * @return 字符的位置（从 0 开始计数），如果未找到则返回字符串长度
 * @note 实现逻辑：
 *       1. 遍历字符串，查找第 `numchar` 次出现的 `findchar`。
 *       2. 返回字符在字符串中的位置（从 0 开始计数）。
 *       3. 如果未找到字符，则返回字符串长度。
 * @warning
 *       - 如果 `maxlen` 为 0，函数直接返回 0。
 *       - 查找次数 `numchar` 从 0 开始计数。
 */
INT16U yx_utils_str_find_char_pos(CHAR* sptr, CHAR findchar, INT8U numchar, INT16U maxlen);

/**
 * @brief 根据规则对输入数据进行解包操作
 * @ingroup yx_utils
 * @param[out] dptr 输出数据缓冲区指针
 * @param[in] sptr 输入数据缓冲区指针
 * @param[in] len 输入数据长度
 * @param[in] rule 解包规则结构体指针
 * @return 解包后的数据长度，如果解包失败则返回 0
 * @note 实现逻辑：
 *       1. 遍历输入数据，根据规则进行解包。
 *       2. 如果当前字符与前一个字符匹配规则，则进行解包操作。
 *       3. 解包规则：
 *          - 如果 `prechar == c_convert0` 且 `curchar == c_convert1`，则输出 `c_flags`。
 *          - 如果 `prechar == c_convert0` 且 `curchar == c_convert2`，则输出 `c_convert0`。
 *          - 否则，直接输出当前字符。
 * @warning
 *       - 如果 `rule` 为 NULL，函数直接返回 0。
 *       - 输入数据必须符合规则，否则解包失败。
 */
INT16U yx_utils_str_disassemble_by_rules(INT8U* dptr, INT8U* sptr, INT16U len, asm_rule_t* rule);

/**
 * @brief 根据规则对输入数据进行打包操作
 * @ingroup yx_utils
 * @param[out] dptr 输出数据缓冲区指针
 * @param[in] sptr 输入数据缓冲区指针
 * @param[in] len 输入数据长度
 * @param[in] rule 打包规则结构体指针
 * @return 打包后的数据长度，如果打包失败则返回 0
 * @note 实现逻辑：
 *       1. 遍历输入数据，根据规则进行打包。
 *       2. 如果当前字符匹配规则，则进行打包操作。
 *       3. 打包规则：
 *          - 如果 `temp == c_flags`，则输出 `c_convert0` 和 `c_convert1`。
 *          - 如果 `temp == c_convert0`，则输出 `c_convert0` 和 `c_convert2`。
 *          - 否则，直接输出当前字符。
 *       4. 在输出数据末尾添加 `c_flags` 作为结束标志。
 * @warning
 *       - 如果 `rule` 为 NULL，函数直接返回 0。
 *       - 输入数据必须符合规则，否则打包失败。
 */
INT16U yx_utils_str_assemble_by_rules(INT8U* dptr, INT8U* sptr, INT16U len, asm_rule_t* rule);

/**
 * @brief 将IPv4字符串转换为32位整数和子网位数，如ip_string为：***********，则转换后的ip_long为0x0B0C0D0E
 * @ingroup yx_utils
 * @param[out]   ip_long   存储32位IP地址的指针（主机字节序表示）
 * @param[out]   sbits     存储计算得到的子网位数的指针(8/16/24)
 * @param[in]    ip_string 输入的IP地址字符串（格式："xxx.xxx.xxx.xxx"）
 *
 * @return       0：成功
 */
INT32S yx_utils_str_conv_ipstr_to_hex(INT32U* ip_long, CHAR* sbits, CHAR* ip_string);

/**
 * @brief 将 INT32U 类型的 IP 地址转换为点分十进制格式的字符串，如0x11223344，则转换后为"***********"
 * @ingroup yx_utils
 * @param[in] ip     需要转换的 INT32U 类型 IP 地址
 * @param[out] dptr  目标缓冲区地址，用于存储转换后的字符串
 * @param[in] maxlen 目标缓冲区的最大长度，应不小于 16 字节
 * @return 成功返回转换后的字符串长度（包含结束符），失败返回 0
 * @note 实现逻辑：
 *       1. 将 IP 地址拆分为 4 个字节（byte1, byte2, byte3, byte4）。
 *       2. 分别将每个字节转换为 ASCII 字符串。
 *       3. 在每个字节之间插入点号（`.`），形成点分十进制格式。
 *       4. 在字符串末尾添加结束符（`\0`）。
 * @warning
 *       - 如果 `maxlen` 小于 16，函数可能无法正确存储转换后的字符串。
 *       - 目标缓冲区 `dptr` 必须足够大，以确保能够容纳完整的 IP 地址字符串。
 */
INT8U yx_utils_str_conv_iphex_to_str(INT32U ip, CHAR* dptr, INT8U maxlen);

/**
 * @brief 将十六进制数据转换为BCD码, 如0x0B 0x16 0x21,则转换为0x11 0x22 0x33
 * @ingroup yx_utils
 * @param[out] dptr 目标缓冲区地址，用于存储转换后的BCD码
 * @param[in] sptr 源数据缓冲区地址，包含需要转换的十六进制数据
 * @param[in] slen 源数据缓冲区的长度
 * @note 实现逻辑：
 *       1. 遍历源数据缓冲区中的每个字节。
 *       2. 将每个字节的高4位和低4位分别转换为BCD码。
 *       3. 将转换后的BCD码存储到目标缓冲区中。
 * @warning
 *       - 目标缓冲区 `dptr` 必须足够大，以容纳转换后的BCD码。
 *       - 源数据缓冲区 `sptr` 中的每个字节应为有效的十六进制数据（0x00-0xFF）。
 */
VOID yx_utils_str_hex_to_bcd(CHAR* dptr, CHAR* sptr, INT32U slen);

/**
 * @brief 将BCD编码的字符串转换为十六进制表示的字符串
 *
 * 将给定BCD编码的字符串转换为对应的十六进制表示的字符串。
 *
 * @param dptr 目标字符串的指针，用于存储转换后的十六进制字符串
 * @param sptr 源字符串的指针，指向BCD编码的字符串
 * @param slen 源字符串的长度
 */
VOID yx_utils_str_bcd_to_hex(CHAR* dptr, CHAR* sptr, INT32U slen);

/**
 * @brief 将十六进制数据转换为十进制ASCII字符串
 * @ingroup yx_utils
 * @param[out] dptr 目标缓冲区地址，用于存储转换后的ASCII字符串
 * @param[in] sptr 源数据缓冲区地址，包含需要转换的十六进制数据
 * @param[in] len 源数据缓冲区的长度
 * @return 转换后的ASCII字符串长度（字节数）
 * @note 实现逻辑：
 *       1. 遍历源数据缓冲区中的每个字节。
 *       2. 将每个字节的高4位和低4位分别转换为十进制ASCII字符。
 *       3. 将转换后的ASCII字符存储到目标缓冲区中。
 * @warning
 *       - 目标缓冲区 `dptr` 必须足够大，以容纳转换后的ASCII字符串。
 *       - 源数据缓冲区 `sptr` 中的每个字节应为有效的十六进制数据（0x00-0xFF）。
 */
INT16U yx_utils_str_hex_to_dec(CHAR* dptr, CHAR* sptr, INT16U len);

/**
 * @brief 检查字符串是否全部由数字字符组成
 * @ingroup yx_utils
 * @param[in] str 输入字符串指针
 * @return 检查结果：
 *         - TRUE: 字符串全部由数字字符组成（'0'-'9'）
 *         - FALSE: 字符串中包含非数字字符
 * @note 实现逻辑：
 *       1. 遍历字符串中的每个字符。
 *       2. 如果发现字符不在 '0' 到 '9' 范围内，则返回 FALSE。
 *       3. 如果遍历完成后未发现非数字字符，则返回 TRUE。
 * @warning
 *       - 输入字符串 `str` 必须以空字符（'\0'）结尾，否则可能导致未定义行为。
 */
BOOLEAN yx_utils_str_is_all_digits(CHAR* str);

/**
 * @brief 将 ASCII 字符串转换为 BCD 码
 *
 * 将给定的 ASCII 字符串转换为 BCD 码，并存储在目标指针指向的内存中。
 *
 * @param dptr 目标指针，指向存储转换后 BCD 码的内存位置
 * @param sptr 源指针，指向要转换的 ASCII 字符串
 * @param len ASCII 字符串的长度
 *
 * @return 转换后的 BCD 码长度
 *
 * 如果输入字符串长度为奇数，则第一个字符将被单独转换为一个字节的 BCD 码，并存储在目标内存位置。
 * 随后，每两个字符将转换为一个字节的 BCD 码，并按顺序存储在目标内存中。
 * 如果输入字符串长度为偶数，则每两个字符转换为一个字节的 BCD 码，并按顺序存储在目标内存中。
 * 如果输入字符串长度为 0，则不进行任何操作，并返回 0。
 */
INT8U yx_utils_str_ascii_to_bcd(CHAR* dptr, CHAR* sptr, INT8U len);

#endif
