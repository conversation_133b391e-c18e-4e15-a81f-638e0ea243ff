#include "cmsis_os2.h"
#include "xy_system.h"
#include "heap_api.h"
#include "audio_play.h"
#include "interf_dec.h"
#include "dec_if.h"

// #define SUPPORT_AMRWB

static audio_sink_handle_t * sink_handle = NULL;

typedef int (*amr_frame_bytes_t)[16];
static const int amr_frame_bytes[][16] = {
    { 12, 13, 15, 17, 19, 20, 26, 31, 5, 6, 5, 5, 0, 0, 0, 0 }, 
#ifdef SUPPORT_AMRWB
    { 17, 23, 32, 36, 40, 46, 50, 58, 60, 5, -1, -1, -1, -1, -1, 0 }
#endif
};

typedef enum
{
    AMR_TYPE_NB = 0,
    AMR_TYPE_WB,

} amr_type_t;

typedef void* (*amr_decode_init_t)(void);
typedef void (*amr_decode_t)(void* state, const unsigned char* in, short* out, int bfi);
typedef void (*amr_decode_exit_t)(void* state);

typedef struct _amr_decode_op{
    amr_decode_init_t init;
    amr_decode_t decode;
    amr_decode_exit_t exit;
}amr_decode_op_t;

static amr_decode_op_t amr_decode_ops[] = {
    {Decoder_Interface_init, Decoder_Interface_Decode, Decoder_Interface_exit},
#ifdef SUPPORT_AMRWB
    {D_IF_init, D_IF_decode, D_IF_exit},
#endif
};

int audio_decode_amr_process(audio_play_t *audio_play)
{
    audio_error_t ret = AUDIO_ERR_OK;
    char header[9];
    uint8_t * in_buf = NULL;
    uint8_t * out_buf = NULL;
    uint32_t out_buf_size;
    int32_t len;
    amr_frame_bytes_t frame_bytes;
    int bytes;
    amr_decode_op_t * amr_op = NULL;
    int sample_rate;
    void * amr = NULL;
    audio_play_state_t state;

    len = audio_play->source->read(header, 6, audio_play->source->source_arg);
    if (len != 6)
    {
        ret = AUDIO_ERR_READ;
        goto exit;
    }
	if (memcmp(header, "#!AMR\n", 6) == 0) 
    {
        frame_bytes = (amr_frame_bytes_t)amr_frame_bytes[AMR_TYPE_NB];
        amr_op = &amr_decode_ops[AMR_TYPE_NB];
        sample_rate = 8000;
        out_buf_size = 320;
	}
    else
    {
		len = audio_play->source->read(header + 6, 3, audio_play->source->source_arg);
        if (len != 3)
        {
            ret = AUDIO_ERR_READ;
            goto exit;
        }
        if (memcmp(header, "#!AMR-WB\n", 9)) 
        {
            audio_play->source->seek(0, audio_play->source->source_arg);
            ret = AUDIO_ERR_FORMAT;
            goto exit;
        }
        frame_bytes = (amr_frame_bytes_t)amr_frame_bytes[AMR_TYPE_WB];
        amr_op = &amr_decode_ops[AMR_TYPE_WB];
        sample_rate = 16000;
        out_buf_size = 640;
    }

    in_buf = (uint8_t*)xy_malloc(64);
    if (in_buf == NULL)
    {
        ret = AUDIO_ERR_MEMORY;
        goto exit;
    }

    out_buf = (uint8_t*)xy_malloc(out_buf_size);
    if (out_buf == NULL)
    {
        ret = AUDIO_ERR_MEMORY;
        goto exit;
    }

    if (sink_handle == NULL)
    {
        sink_handle = audio_play->sink->open(SINK_MODE_OUT, sample_rate, 1, 16);
        if (sink_handle == NULL)
        {
            ret = AUDIO_ERR_SINK_OPEN;
            goto exit;
        }
    }
    else
    {
        xy_assert(0);
    }
    audio_play->sink->start(sink_handle);

	amr = amr_op->init();
	while (1) {
		len = audio_play->source->read(in_buf, 1, audio_play->source->source_arg);
        if (len != 1)
        {
            ret = AUDIO_ERR_OK;
            goto exit;
        }
        bytes = (*frame_bytes)[(in_buf[0] >> 3) & 0x0f];
		len = audio_play->source->read(in_buf + 1, bytes, audio_play->source->source_arg);
        if (len != bytes)
        {
            ret = AUDIO_ERR_OK;
            goto exit;
        }

		/* Decode the packet */
		amr_op->decode(amr, in_buf, (uint16_t *)out_buf, 0);

        state = audio_play->state_get();
        if (state == AUDIO_PLAY_STATE_STOP)
        {    
            ret = AUDIO_ERR_OK;
            goto exit;
        }
        else if (state == AUDIO_PLAY_STATE_PAUSE)
        {
            osSemaphoreAcquire(*(audio_play->sem), osWaitForever);
        }

        audio_play->sink->write(sink_handle, out_buf, out_buf_size);
	}

exit:

    if(in_buf)
    {
        xy_free(in_buf);
    }
    if(out_buf)
    {
        xy_free(out_buf);
    }

    if(sink_handle != NULL)
    {
        audio_play->sink->stop(sink_handle);
        audio_play->sink->close(sink_handle);
        sink_handle = NULL;
    }

    if(amr)
    {
        amr_op->exit(amr);
    }

    return ret;
}
