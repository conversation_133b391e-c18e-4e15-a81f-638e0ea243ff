/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "at_cmd_basic.h"
#include "atc_ps.h"
#include "main_proxy.h"
#include "ps_netif_api.h"
#include "xy_at_api.h"
#include "xy_atc_interface.h"
#include "xy_defwan_api.h"
#include "xy_system.h"
#include "xy_walltime.h"
#include "at_ps_api.h"

/*******************************************************************************
 *                       Global function implementations	                   *
 ******************************************************************************/

/*获取SIM卡状态。0：有卡；    1：无卡；2：卡无效,如卡欠费等*/
uint8_t gSimStatus = 0;

/*指示当前是否进入OOS*/
bool g_OOS_flag = false;

/*指示PS是否处于测试模式，0表示正常模式，1表示入库或者仪表环境测试模式*/
uint8_t g_ps_test_mode = 0;



/*指示PS是否处于测试模式，0表示正常模式，1表示入库或者仪表环境测试模式*/
uint8_t get_ps_test_mode()
{
	return g_ps_test_mode;
}

/*获取SIM卡状态。0：有卡；    1：无卡；2：卡无效,如卡欠费等*/
uint8_t GetSimStatus()
{
	return gSimStatus;
}

void set_oos_state(bool isOOS)
{
    g_OOS_flag = isOOS;
    xy_printf(0, PLATFORM_AP, WARN_LOG, "[set_oos_state]state:%d", isOOS);
}

/**
 * @brief 指示PS协议栈是否进入OOS
 * @warning 一旦进入OOS，此时无线通信无法正常进行，待发送的上行数据包会缓存在PS内部，
 * 当堆积到一定数量后则会触发流控丢包，是否进入流控请参见is_Uplink_FlowCtl_Open()
 */
bool ps_is_oos()
{
    if (g_OOS_flag == 1)
        xy_printf(0, PLATFORM_AP, WARN_LOG, "OOS will drop packet!");

    return g_OOS_flag;
}


//+CGEV: ME PDN DEACT 0
void urc_CGEV_Callback(unsigned long eventId, void *param, int paramLen)
{
	UNUSED_ARG(eventId);
	xy_assert(paramLen == sizeof(ATC_MSG_CGEV_IND_STRU));
	ATC_MSG_CGEV_IND_STRU *cgev_urc = (ATC_MSG_CGEV_IND_STRU*)param;
	uint8_t cid = cgev_urc->stCgevPara.ucCid;

    xy_printf(0, PLATFORM_AP, INFO_LOG, "[urc_CGEV_Callback]eventId:%d,cid:%d", cgev_urc->ucCgevEventId, cgev_urc->stCgevPara.ucCid);

    switch(cgev_urc->ucCgevEventId)
	{
		case D_ATC_CGEV_ME_PDN_ACT:
		{
            set_oos_state(false);
        }
			break;
		case D_ATC_CGEV_NW_PDN_DEACT:
		case D_ATC_CGEV_ME_PDN_DEACT:
		{
			set_oos_state(false);
            send_msg_2_proxy(PROXY_MSG_PS_PDP_DEACT, &cid, sizeof(cid), osWaitForever);
        }
			break;
		case D_ATC_CGEV_OOS:
		{
			set_oos_state(true);
			data_call_status_ind(EVENT_PS_ENTER_OOS, cid);
		}
			break;
		case D_ATC_CGEV_IS:
		{
			set_oos_state(false);
			data_call_status_ind(EVENT_PS_ENTER_IS, cid);
		}
			break;
		default:
			break;
	}

}

void urc_XYIPDNS_Callback(unsigned long eventId, void *param, int paramLen)
{
	UNUSED_ARG(eventId);
	xy_assert(param != NULL && paramLen == sizeof(ATC_MSG_XYIPDNS_IND_STRU));
	ATC_MSG_XYIPDNS_IND_STRU *xyipdns_urc = (ATC_MSG_XYIPDNS_IND_STRU*)param;

	DataCall_Info_T pdp_info = {0, 0, IP_TYPE_INVALID, 0, 0, {ip_addr_any, ip_addr_any, ip_addr_any}, {ip6_addr_any, ip6_addr_any, ip6_addr_any}};

	pdp_info.psIpType = xyipdns_urc->stPara.ucPdpType;
	pdp_info.cid = xyipdns_urc->stPara.ucCid;
	pdp_info.ipv4_mtu = xyipdns_urc->stPara.usIPv4MTU;

	xy_printf(0, PLATFORM_AP, INFO_LOG, "[urc_XYIPDNS_Callback]cid:%u,psIpType:%u,ipv4Mtu:%u", pdp_info.cid, pdp_info.psIpType, pdp_info.ipv4_mtu);

	if(g_softap_fac_nv->test == 9)
	{
		xyipdns_urc->stPara.stDnsAddr.ucPriDnsAddr_IPv4Flg = 0;
		xyipdns_urc->stPara.stDnsAddr.ucSecDnsAddr_IPv4Flg = 0;
		xyipdns_urc->stPara.stDnsAddr.ucPriDnsAddr_IPv6Flg = 0;
		xyipdns_urc->stPara.stDnsAddr.ucSecDnsAddr_IPv6Flg = 0;
	}

	switch (pdp_info.psIpType)
	{
		case D_PDP_TYPE_IPV4:
		case D_PDP_TYPE_IPV4V6:
		{
			memcpy(&pdp_info.ipv4_info.ip, (ip4_addr_t *)xyipdns_urc->stPara.aucIPv4Addr, sizeof(xyipdns_urc->stPara.aucIPv4Addr));

			if (ip_addr_isany(&pdp_info.ipv4_info.ip))
			{
				xy_printf(0, PLATFORM_AP, WARN_LOG, "[urc_XYIPDNS_Callback]ipv4 addr is any");
				return;
			}

            if (xyipdns_urc->stPara.stDnsAddr.ucPriDnsAddr_IPv4Flg)
            {
                memcpy(&pdp_info.ipv4_info.pridns.u_addr.ip4, (ip4_addr_t *)xyipdns_urc->stPara.stDnsAddr.ucPriDnsAddr_IPv4, sizeof(xyipdns_urc->stPara.stDnsAddr.ucPriDnsAddr_IPv4));
            }
            if (xyipdns_urc->stPara.stDnsAddr.ucSecDnsAddr_IPv4Flg)
            {
                memcpy(&pdp_info.ipv4_info.secdns.u_addr.ip4, (ip4_addr_t *)xyipdns_urc->stPara.stDnsAddr.ucSecDnsAddr_IPv4, sizeof(xyipdns_urc->stPara.stDnsAddr.ucSecDnsAddr_IPv4));
            }

			if (pdp_info.psIpType == D_PDP_TYPE_IPV4)
				break;
		}
		case D_PDP_TYPE_IPV6:
		{
			memcpy(&pdp_info.ipv6_info.ip, (ip6_addr_t*)(xyipdns_urc->stPara.aucIPv6Addr), sizeof(xyipdns_urc->stPara.aucIPv6Addr));

			if (ip_addr_isany(&pdp_info.ipv6_info.ip))
			{
				xy_printf(0, PLATFORM_AP, WARN_LOG, "[urc_XYIPDNS_Callback]ipv6 addr is any");
				return;
			}

            if (xyipdns_urc->stPara.stDnsAddr.ucPriDnsAddr_IPv6Flg)
            {
                memcpy(&pdp_info.ipv6_info.pridns.u_addr.ip6, (ip6_addr_t*)(xyipdns_urc->stPara.stDnsAddr.ucPriDnsAddr_IPv6), sizeof(xyipdns_urc->stPara.stDnsAddr.ucPriDnsAddr_IPv6));
            }
            if (xyipdns_urc->stPara.stDnsAddr.ucSecDnsAddr_IPv6Flg)
            {
                memcpy(&pdp_info.ipv6_info.secdns.u_addr.ip6, (ip6_addr_t*)(xyipdns_urc->stPara.stDnsAddr.ucSecDnsAddr_IPv6), sizeof(xyipdns_urc->stPara.stDnsAddr.ucSecDnsAddr_IPv6));
            }
		}
		break;
		default:
		{
			xy_printf(0, PLATFORM_AP, WARN_LOG, "[urc_XYIPDNS_Callback]psIpType %u not support", pdp_info.psIpType);
			return;
		}
		break;
	}

	send_msg_2_proxy(PROXY_MSG_PS_PDP_ACT, &pdp_info, sizeof(DataCall_Info_T), osWaitForever);
}

void urc_FLOWCTRL_Callback(unsigned long eventId, void *param, int paramLen)
{
	UNUSED_ARG(eventId);
	xy_assert(paramLen == sizeof(ATC_MSG_IPSN_IND_STRU));

    xy_printf(0, PLATFORM_AP, INFO_LOG, "[urc_FLOWCTRL_Callback]state:%u", ((ATC_MSG_IPSN_IND_STRU*)param)->ucFlowCtrlStatus);
	g_data_flowctl = ((ATC_MSG_IPSN_IND_STRU*)param)->ucFlowCtrlStatus;
}


/*对应"^SIMST"*/
void urc_SIMST_Callback(unsigned long eventId, void *param, int paramLen)
{
	UNUSED_ARG(eventId);
	xy_assert(paramLen == sizeof(ATC_MSG_SIMST_IND_STRU));
	extern uint8_t g_ps_test_mode;
	g_ps_test_mode = ((ATC_MSG_SIMST_IND_STRU*)param)->ucTestModeFlg;
	xy_printf(0, PLATFORM_AP, INFO_LOG, "[urc_SIMST_Callback]ps test mode:%u", g_ps_test_mode);

	/*参考xy_simst_read()*/
	if(((ATC_MSG_SIMST_IND_STRU*)param)->ucSimStatus == 0)
		gSimStatus = 1;
	else if(((ATC_MSG_SIMST_IND_STRU*)param)->ucSimStatus == 1)
		gSimStatus = 0;
}

/*+CEREG:*/
void urc_CEREG_Callback(unsigned long eventId, void *param, int paramLen)
{
	UNUSED_ARG(eventId);
	xy_assert(paramLen == sizeof(ATC_MSG_CEREG_IND_STRU));							
	ATC_MSG_CEREG_IND_STRU *pCeregInd = (ATC_MSG_CEREG_IND_STRU *)param;

	/*参考xy_simst_read()*/
	if((pCeregInd->stPara.OP_RejectCause==1) && (LTE_REJECT_EMM_CAUSE_VALUE==pCeregInd->stPara.ucCauseType))
	{
		if(pCeregInd->stPara.ucRejectCause < 9 && pCeregInd->stPara.ucRejectCause != 5)
		{
			xy_printf(0, PLATFORM_AP, WARN_LOG, "[PS]SIM ERR! Attach reject :%d",pCeregInd->stPara.ucRejectCause);
			gSimStatus = 2;
		}
	}
}


void ps_urc_register_callback_init()
{
	xy_atc_registerPSEventCallback(D_XY_PS_REG_EVENT_XYIPDNS, urc_XYIPDNS_Callback);
	xy_atc_registerPSEventCallback(D_XY_PS_REG_EVENT_CGEV, urc_CGEV_Callback);
	xy_atc_registerPSEventCallback(D_XY_PS_REG_EVENT_LOCALTIMEINFO, urc_CTZEU_Callback);
	xy_atc_registerPSEventCallback(D_XY_PS_REG_EVENT_IPSN, urc_FLOWCTRL_Callback);   
	xy_atc_registerPSEventCallback(D_XY_PS_REG_EVENT_SIMST, urc_SIMST_Callback);   
	xy_atc_registerPSEventCallback(D_XY_PS_REG_EVENT_CEREG, urc_CEREG_Callback); // EPS网络注册状态
}
