#include "alsa_config.h"
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include "hal_trace.h"
#include "alsa_extra.h"

// #define ALSA_TRACE_USE_HEAP

#define ALSA_TRACE_MAX_LENGTH 128

static alsa_trace_thirdparty_func_t alsa_trace_thirdparty_func = NULL;

void alsa_trace_thirdparty_func_register(alsa_trace_thirdparty_func_t func)
{
    alsa_trace_thirdparty_func = func;
}

int alsa_trace_thirdparty(uint8_t attr, const char *fmt, ...)
{
    va_list args;

#ifdef ALSA_TRACE_USE_HEAP
    uint8_t *buf = (uint8_t *)audio_calloc(ALSA_TRACE_MAX_LENGTH, sizeof(uint8_t));
    if (!buf)
        return -1;
#else
    uint8_t buf[ALSA_TRACE_MAX_LENGTH] = {0};
#endif
    va_start(args, fmt);
    vsnprintf((char *)buf, ALSA_TRACE_MAX_LENGTH, fmt, args);
    va_end(args);
    if (alsa_trace_thirdparty_func)
    {
        alsa_trace_thirdparty_func((const char *)buf);
    }
    else
    {
        TRACE(0, "%s", buf);
    }
#ifdef ALSA_TRACE_USE_HEAP
    if (buf)
        audio_free(buf);
#endif
    return 0;
}