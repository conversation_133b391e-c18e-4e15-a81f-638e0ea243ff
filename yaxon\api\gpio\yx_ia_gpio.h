/**********************************************************************/
/**
 * @file yx_ia_gpio.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-18
 * @version V1.0
 * @brief gpio接口适配
 **********************************************************************/
#ifndef YX_IA_GPIO_H
#define YX_IA_GPIO_H

#include "yx_type.h"

/**
 * @brief gpio中断回调函数
 * @ingroup ia_gpio
 * @param[in] ctx 见yx_ia_gpio_init_irq/yx_ia_gpio_init_wakeup的ctx参数
 * @param[in] level 电平状态
 * @retval void
 */
typedef void (*eint_callback)(void *ctx, INT32U level);

/**
 * @brief 初始化gpio为输入模式
 * @ingroup ia_gpio
 * @param[in] gpio 对应平台的gpio号，或者pin号，具体看平台定义
 * @return INT32S
 * @retval 0 成功
 * @retval 负数 失败
 */
INT32S yx_ia_gpio_init_input(INT32U gpio);

/**
 * @brief 初始化gpio为输出模式
 * @ingroup ia_gpio
 * @param[in] gpio 对应平台的gpio号，或者pin号，具体看平台定义
 * @return INT32S
 * @retval 0 成功
 * @retval 负数 失败
 */
INT32S yx_ia_gpio_init_output(INT32U gpio, INT32U init_level);

/**
 * @brief 初始化gpio中断
 * @ingroup ia_gpio
 * @param[in] gpio 对应平台的gpio号，或者pin号，具体看平台定义
 * @param[in] trigger 中断触发方式 0-关闭中断, 1-上升沿, 2-下降沿, 3-双边沿
 * @param[in] handler 中断回调函数
 * @param[in] ctx 中断回调函数参数
 * @return INT32S
 * @retval 0 成功
 * @retval 负数 失败
 */
INT32S yx_ia_gpio_init_irq(INT32U gpio, INT32U trigger, eint_callback handler, void *ctx);

/**
 * @brief 初始化gpio唤醒功能
 * @ingroup ia_gpio
 * @param[in] gpio 对应平台的gpio号，或者pin号，具体看平台定义
 * @param[in] trigger 唤醒触发方式 0-关闭中断, 1-上升沿, 2-下降沿, 3-双边沿
 * @param[in] ctx 中断回调函数参数
 * @param[in] handler 唤醒处理函数
 * @return INT32S
 * @retval 0 成功
 * @retval 负数 失败
 */
INT32S yx_ia_gpio_init_wakeup(INT32U gpio, INT32U trigger, eint_callback handler, void *ctx);

/**
 * @brief 设置gpio输出
 * @ingroup ia_gpio
 * @param[in] gpio 对应平台的gpio号，或者pin号，具体看平台定义
 * @param[in] on 电平
 * @return INT32S
 * @retval 0 成功
 * @retval 负数 失败
 */
INT32S yx_ia_gpio_output(INT32U gpio, BOOLEAN on);

/**
 * @brief 读取gpio输入
 * @ingroup ia_gpio
 * @param[in] gpio 对应平台的gpio号，或者pin号，具体看平台定义
 * @return INT32S
 * @retval 0 低电平
 * @retval 1 高电平
 * @retval 负数 失败
 */
INT32S yx_ia_gpio_input(INT32U gpio);

#endif /* YX_IA_GPIO_H */