/**********************************************************************/
/**
 * @file yx_ia_gprs.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-14
 * @version V1.0
 * @brief 分组无线业务(GPRS)接口适配
 **********************************************************************/
#ifndef YX_IA_GPRS_H
#define YX_IA_GPRS_H

#include "yx_type.h"
#define GPRS_ERRNO            (0x80 + 0x08)
#define DNS_IP_MAX            4/**< 域名解析最大存储IP个数 */

/**
 * @brief 错误码枚举
 * @ingroup ia_gprs
 */
typedef enum {
    IA_GPRS_SUUCESS = 0,                                     /**< 成功 */
    IA_GPRS_INVALID_PARAM_ERR = (GPRS_ERRNO << 24) | 0x01,   /**< 参数错误 */
    IA_GPRS_ADAPTER_PARAM_ERR = (GPRS_ERRNO << 24) | 0x02,   /**< 适配错误 */

    IA_GPRS_EXECUTE_FAILED    = (GPRS_ERRNO << 24) | 0x03,   /**< 执行失败 */
} ia_gprs_errno_e;

/**
 * @brief 上下文通道枚举
 * @ingroup ia_gprs
 */
typedef enum {
    GPRS_CID_0 = 0,           /**< 通道0 */
    GPRS_CID_1,               /**< 通道1 */
    GPRS_CID_2,               /**< 通道2 */
    GPRS_CID_3,               /**< 通道3 */
    GPRS_CID_4,               /**< 通道4 */
    GPRS_CID_5,               /**< 通道5 */
    GPRS_CID_MAX
} ia_gprs_cid_e;

typedef struct {
    INT8U  ip_num;            /**< IP个数 */
    INT32U ip[DNS_IP_MAX];    /**< IP数组 ip[0]=0x11223344 表示 *********** */
} ia_dns_info_t;

/**
 * @brief 注册事件回调枚举
 * @ingroup ia_gprs
 */
typedef enum {
    GPRS_EVENT_ACTINE = 0,    /**< 激活事件 */
    GPRS_EVENT_DEACTINE,      /**< 失活事件 */
} ia_gprs_evt_e;

/**
 * @brief 注册GPRS事件回调
 *
 * @param contxtid [in]上下文通道 @see ia_gprs_cid_e
 * @param event [in]事件类型 @see ia_gprs_evt_e
 * @param result [in]执行结果 @see ia_gprs_errno_e
 * @retval VOID
 * @ingroup ia_gprs
 */
typedef VOID (*GPRS_EVENT_CB)(INT8U contxtid, INT8U event, INT32U result);

/**
 * @brief GPRS事件注册
 * 
 * yx_ia_gprs_active/deactive 接口阻塞, 本接口预留给异步方式
 *
 * @param contxtid [in]上下文通道 @see ia_gprs_cid_e
 * @param callback [in]事件回调函数 @see GPRS_EVENT_CB
 * @retval IA_GPRS_SUUCESS(0):成功 其它值:失败 @see ia_gprs_errno_e
 * @ingroup ia_gprs
 */
INT32U yx_ia_gprs_register(INT8U contxtid, GPRS_EVENT_CB callback);

/**
 * @brief 激活GPRS上下文, 阻塞直到激活成功或失败
 *
 * @param contxtid [in]上下文通道 @see ia_gprs_cid_e
 * @retval IA_GPRS_SUUCESS(0):成功 其它值:失败 @see ia_gprs_errno_e
 * @ingroup ia_gprs
 */
INT32U yx_ia_gprs_active(INT8U contxtid);

/**
 * @brief 失活GPRS上下文, 阻塞直到失活成功或失败
 *
 * @param contxtid [in]上下文通道 @see ia_gprs_cid_e
 * @retval IA_GPRS_SUUCESS(0):成功 其它值:失败 @see ia_gprs_errno_e
 * @ingroup ia_gprs
 */
INT32U yx_ia_gprs_deactive(INT8U contxtid);

/**
 * @brief 域名解析, 阻塞直到解析成功或失败
 *
 * @param contxtid [in]上下文通道 @see ia_gprs_cid_e
 * @param domainname [in]域名名称
 * @param pinfo [out]解析结果 @see ia_dns_info_t
 * @retval IA_GPRS_SUUCESS(0):成功 其它值:失败 @see ia_gprs_errno_e
 * @note 执行前先初始化pinfo指针
 * @ingroup ia_gprs
 */
INT32U yx_ia_gprs_dns_resolution(INT8U contxtid, CHAR *domainname, ia_dns_info_t *pinfo);

/**
 * @brief 设置APN, 非阻塞
 *
 * @param contxtid [in]上下文通道 @see ia_gprs_cid_e
 * @param apn [in] APN
 * @param username [in] 用户名
 * @param password [in] 密码
 * @retval IA_GPRS_SUUCESS(0):成功 其它值:失败 @see ia_gprs_errno_e
 * @ingroup ia_gprs
 */
INT32U yx_ia_set_apn(INT8U contxtid, char *apn, char *username, char *password);


#endif