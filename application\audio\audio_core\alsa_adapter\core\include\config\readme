alsa_config_0 is a basic and sample config for multi-instance playback.

alsa_config_1 is a basic config for one-instace playback.
              It's playback arguments are specified by user, alsa internal not modify them such as bits, sample rate and channels.

alsa_config_2 is a project config for YUNQUE and BAILU.

alsa_config_3 is a project config for TG.

alsa_config_4 is a mixer project, this enable a limiter process always.

alsa_config_5 is bt watch project, this support capture and support set dma buffer size and dma callback support notify user.

Another alsa_configs may be used for other projects in the futrue.
