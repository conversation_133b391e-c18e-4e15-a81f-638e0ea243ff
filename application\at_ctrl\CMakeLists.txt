get_sub_header_dirs(INCLUDE_AT_CTRL_DIRS ${CMAKE_CURRENT_LIST_DIR})

if(OPEN)
file(GLOB_RECURSE AT_CTRL_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/at_ctrl/at_ctl_basic.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/at_ctrl/ps_adapt.c"
    )
else()
file(GLOB_RECURSE AT_CTRL_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/at_ctrl/*.c"
    )
endif()

shorten_src_file_macro(${AT_CTRL_SOURCES})

target_sources(application
    PRIVATE 
        ${AT_CTRL_SOURCES}
)

# 2M版本放PSRAM做压缩
if(SPACE_OPTIMIZATION)
    relocate_code(CODE_LOCATION PSRAM CODE_TARGET application SOURCES_LIST ${AT_CTRL_SOURCES})
endif()
