#ifndef __APLAYER_H__
#define __APLAYER_H__

#include <stdint.h>
#include <stdlib.h>

enum
{
    APLAYER_SUCC = 0,
    APLAYER_MEM_ERROR = 1,
    APLAYER_ARG_ERROR = 2,
    APLAYER_FORMAT_ERROR = 3,
    APLAYER_STATE_ERROR = 4,
    APLAYER_OS_ERROR = 5,
    APLAYER_DEC_ERROR = 6,
};

typedef enum
{
    APLAYER_CH_MONO = 1,
    APLAYER_CH_STEREO = 2,

    APLAYER_CH_NO_SUPPORT_VAL,
} aplayer_channel_t;

typedef enum
{
    APLAYER_BITS_16 = 16,

    APLAYER_BITS_NO_SUPPORT_VAL,
} aplayer_bits_t;

typedef struct
{
    uint32_t sample_rate;
    aplayer_channel_t channel_num;
    aplayer_bits_t bit_num;
    void *user_arg;
} aplyer_raw_data_arg_t;

typedef enum
{
    APLAYER_FORMAT_UNKNOWN = 0,

    /* raw format, user need offer audio arg and other necessary arg */
    // APLAYER_FORMAT_RAW_PCM,
    // APLAYER_FORMAT_RAW_AAC_ADTS,
    // APLAYER_FORMAT_RAW_OPUS,

    /* format with encapsulation, aplayer will decoder it */
    APLAYER_FORMAT_WAV,
    APLAYER_FORMAT_MP3,
    // APLAYER_FORMAT_M4A,
    // APLAYER_FORMAT_OGG,

    /* used to judge error */
    APLAYER_FORMAT_NO_SUPPORT_VAL,
} aplayer_format_t;

typedef struct aplayer_handle aplayer_t;

aplayer_t *aplayer_open(aplayer_format_t format);
int aplayer_close(aplayer_t *ap);
int aplayer_write(aplayer_t *ap, uint8_t *buf, uint32_t len);
int aplayer_play(aplayer_t *ap);
int aplayer_stop(aplayer_t *ap);
int aplayer_fifo_is_full(aplayer_t *ap);
#endif