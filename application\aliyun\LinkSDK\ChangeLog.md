# 文件说明

记录`Link SDK-4.x`的更新历史

# 更新内容

+ 2020-05-06: SDK 4.0.0版本正式发布
+ 2021-04-20: SDK 4.1.0版本正式发布
  +  新增安全隧道和远程调试功能
  +  新增AT模组驱动框架,支持模组快速适配
  +  mbedtls安全层抽象
  +  支持单报文多url的OTA
  +  新增基于mqtt的动态注册功能
  +  支持MQTT 5.0协议
+ 2022-10-28:
  +  支持coap协议接入及动态注册
  +  mbedtls版本升级至2.13.1

# 模块状态


| 模块名                              | 更新时间    | Commit ID
|-------------------------------------|-------------|---------------------------------------------
| 核心模块(core)                      | zhijian     | 725cb39c12a9fb8c92d7b909eee3d2f0758bd331
| 固件升级模块(components/ota)        | xicai.cxc   | 98afe07e9b330015f62f067cf3599d82a0d9de3f
| 物模型模块(components/data-model)   | 2021-09-06  | 1d9270de816f7ff0f60c0b2a53d08ca4da8bab66



