/*******************************************************************************
 *                           Include header files                              *
 ******************************************************************************/
#include "xy_ps_api.h"
#include "xy_atc_interface.h"
#include "atc_ps_def.h"
#include "xy_system.h"
#include "ps_netif_api.h"
#include "atc_ps.h"
#include "xy_defwan_api.h"

typedef struct
{
    unsigned char                       ucSetVal;
    unsigned char                       aucBitStr[5];
    unsigned long                       ulMS;
} ST_XY_ATC_EDRX_VALUE_TABLE;

ST_XY_ATC_EDRX_VALUE_TABLE xy_atc_eDrxValue_Tab[14] = 
{
    { 0,  "0000", 5120  },
    { 1,  "0001", 10240  },
    { 2,  "0010", 20480  },
    { 3,  "0011", 40960  },
    { 4,  "0100", 61440  },
    { 5,  "0101", 81920  },
    { 6,  "0110", 102400  },
    { 7,  "0111", 122880 },
    { 8,  "1000", 143360 },
    { 9,  "1001", 163840 },
    { 10, "1010", 327680 },
    { 11, "1011", 655360 },
    { 12, "1100", 1310720 },
    { 13, "1101", 2621440 },
    // { 14, "1110", 5242880 },
    // { 15, "1111", 10485760 },
};

ST_XY_ATC_EDRX_VALUE_TABLE xy_atc_eDrxPtw_Tab[16] = 
{
    { 0,  "0000", 1280  },
    { 1,  "0001", 2560  },
    { 2,  "0010", 3840  },
    { 3,  "0011", 5120  },
    { 4,  "0100", 6400  },
    { 5,  "0101", 7680  },
    { 6,  "0110", 8960  },
    { 7,  "0111", 10240 },
    { 8,  "1000", 11520 },
    { 9,  "1001", 12800 },
    { 10, "1010", 14080 },
    { 11, "1011", 15360 },
    { 12, "1100", 16640 },
    { 13, "1101", 17920 },
    { 14, "1110", 19200 },
    { 15, "1111", 20480 },
};

static char* xy_atc_GeteDrxValueBitStr(unsigned long ulMs)
{
    int i;

    for(i = 0; i < (int)(sizeof(xy_atc_eDrxValue_Tab)/sizeof(ST_XY_ATC_EDRX_VALUE_TABLE)); i++)
    {
        if(ulMs == xy_atc_eDrxValue_Tab[i].ulMS)
        {
            return xy_atc_eDrxValue_Tab[i].aucBitStr;
        }
    }

    return NULL;
}


/*******************************************************************************
 *                      Global function implementations                        *
 ******************************************************************************/
int xy_cfun_excute(int status)
{
    char aucCmd[20] = { 0 };

    sprintf(aucCmd, "AT+CFUN=%d\r\n", status);
    return xy_atc_interface_call(aucCmd, NULL, (void*)NULL);
}

int xy_cfun_read(int *cfun)
{
    ATC_MSG_CFUN_R_CNF_STRU tCfunRCnf = { 0 };

    if(cfun == NULL)
    {
        return XY_ERR;
    }

    if(xy_atc_interface_call("AT+CFUN?\r\n", NULL, (void*)&tCfunRCnf) == XY_ERR)
    {
        return XY_ERR;
    }

    *cfun = tCfunRCnf.ucFunMode;
    return XY_OK;
}

char xy_get_CGATT_state() //get cat1 net state  
{
    char    cResult = XY_ERR;
    ATC_MSG_CGATT_R_CNF_STRU tCgattCfn = { 0 };

    if(xy_atc_interface_call("AT+CGATT?\r\n", NULL, (void*)&tCgattCfn) == XY_ERR)
    {
        return cResult;
    }

    cResult = tCgattCfn.ucState;
    
    xy_printf(0,ATC_AP_T,INFO_LOG,"xy_get_CGATT_state: %d",cResult);
    return cResult;
}

int xy_get_CGACT(int *cgact)
{
    ATC_MSG_CGACT_R_CNF_STRU tCgactRCnf = { 0 };

    if(cgact == NULL)
    {
        return XY_ERR;
    }
    
    if(xy_atc_interface_call("AT+CGACT?\r\n", NULL, (void*)&tCgactRCnf) == XY_ERR)
        return XY_ERR;

    *cgact = tCgactRCnf.stState.aCidSta[0].ucState;
    return XY_OK;
}

int xy_cid_active_qiact(char cid)
{
    char    aucCmd[24] = { 0 };
    
    sprintf(aucCmd, "AT+QIACT=%d\r\n", cid);
    
    if(xy_atc_interface_call(aucCmd, NULL, NULL) == XY_ERR)
        return XY_ERR;

    return XY_OK;
}

int xy_get_cid_state(char cid ,char *cid_state)
{
    unsigned char               i = 0;
    ATC_MSG_CGACT_R_CNF_STRU    tCgactRCnf = { 0 };

#if USR_CUSTOM2
    if(cid > 10)
#else
    if((cid > 15) || (cid <= 0))
#endif
    {
        return XY_ERR;
    }
    
    if(xy_atc_interface_call("AT+CGACT?\r\n", NULL, (void*)&tCgactRCnf) == XY_ERR)
        return XY_ERR;

    for(i = 0;i < tCgactRCnf.stState.ucValidNum; i++)
    {
        if(cid == tCgactRCnf.stState.aCidSta[i].ucCid)
        {
            *cid_state = tCgactRCnf.stState.aCidSta[i].ucState;
            return XY_OK;
        }
    }

    return XY_ERR;
}

int xy_cereg_read(int *cereg)
{
    ATC_MSG_CEREG_R_CNF_STRU tCeregRCnf = { 0 };
    
    if(xy_atc_interface_call("AT+CEREG?\r\n", NULL, (void*)&tCeregRCnf) == XY_ERR)
        return XY_ERR;

    *cereg = tCeregRCnf.tRegisterState.ucEPSRegStatus;
    return XY_OK;
}


int xy_get_SN(char *sn, int len)
{
    ATC_MSG_CGSN_CNF_STRU* pCgsnCnf;

    if (len < SN_LEN || sn == NULL)
    {
        return XY_ERR;
    }

    pCgsnCnf = (ATC_MSG_CGSN_CNF_STRU*)AtcAp_MallocWithClean(sizeof(ATC_MSG_CGSN_CNF_STRU) + NVM_MAX_SN_LEN + 1- 4);
    if(XY_OK != xy_atc_interface_call("AT+CGSN=0\r\n", (func_AppInterfaceCallback)NULL, (void*)pCgsnCnf))
    {
        // softap_printf(USER_LOG, WARN_LOG, "get sn fail!!!");
        AtcAp_Free(pCgsnCnf);
        return XY_ERR;
    }

    if(0 != pCgsnCnf->ucLen)
    {
        strncpy(sn, pCgsnCnf->aucData, pCgsnCnf->ucLen);
        xy_assert(strlen(sn) <= NVM_MAX_SN_LEN);
    }
    AtcAp_Free(pCgsnCnf);

    return XY_OK;
}

char xy_set_band(char cBandNum, char* pBandList)
{
    char    cResult = XY_ERR;
    char*    pcStrBand;

    if(cBandNum > D_ATC_NBAND_MAX_BNAD_NUM)
    {
        return XY_ERR;
    }
    if(xy_atc_interface_call("AT+CFUN=0\r\n", NULL, (void*)NULL) == XY_ERR)
    {
        return XY_ERR;
    }
    pcStrBand = AtcAp_Malloc(D_ATC_NBAND_MAX_BNAD_NUM * 3 + 20);
#if USR_CUSTOM2
    sprintf(pcStrBand, "AT+NBAND=%s\r\n", pBandList);
#else
    sprintf(pcStrBand, "AT+MBAND=%s\r\n", pBandList);
#endif
    if(xy_atc_interface_call(pcStrBand, NULL, NULL) == XY_ERR)
    {
        AtcAp_Free(pcStrBand);
        return XY_ERR;
    }
    AtcAp_Free(pcStrBand);
    return XY_OK;
}

char xy_creat_bandstr(char* pBandBuf)
{
    unsigned char cBandNum = 1;
    unsigned char  i = 0;
    if(pBandBuf == NULL || strlen(pBandBuf) > 255)
    {
        return XY_ERR;
    }

    for(i = 0; i < (unsigned char)strlen(pBandBuf); i++)
    {
        //xy_printf(0,ATC_AP_T,INFO_LOG,"xy_creat_bandstr: %d, %s, %d", cBandNum, pBandBuf + i, pBandBuf[i]);
        if(pBandBuf[i] == 58)// ":"  change to ","
        {
            pBandBuf[i] = D_ATC_N_COMMA;
            //xy_printf(0,ATC_AP_T,INFO_LOG,"xy_creat_bandstr: %s", pBandBuf);
            cBandNum ++;
        }
    }
    return xy_set_band(cBandNum, pBandBuf);
}

char xy_get_band(char* pBandNum, char* pBandBuf, char cSize)
{
    char    cResult = XY_ERR;
    unsigned char    i = 0;
    ATC_MSG_NBAND_R_CNF_STRU tNbandRCnf = {0};

    if(pBandBuf == NULL)
    {
        return cResult;
    }
#if USR_CUSTOM2
    if(xy_atc_interface_call("AT+NBAND?\r\n", NULL, &tNbandRCnf) == XY_ERR)
        return XY_ERR;
#else
    if(xy_atc_interface_call("AT+MBAND?\r\n", NULL, &tNbandRCnf) == XY_ERR)
        return XY_ERR;
#endif
    if(cSize < tNbandRCnf.stSupportBandList.ucSupportBandNum)
    {
        *pBandNum = cSize;
        xy_printf(0,ATC_AP_T,INFO_LOG,"xy_get_band:size < real size");
    }
    else
    {
        *pBandNum = tNbandRCnf.stSupportBandList.ucSupportBandNum;
    }
    
    for(i = 0; i < (*pBandNum); i++)
    {
        *(pBandBuf + i)= tNbandRCnf.stSupportBandList.aucSuppBand[i];
        xy_printf(0,ATC_AP_T,INFO_LOG,"xy_get_band:%d", tNbandRCnf.stSupportBandList.aucSuppBand[i]);
    }
    return XY_OK;
}

char xy_switch_sim(char card_slot)
{
    char    cResult = XY_ERR;
    char    aucCmd[24] = { 0 };

    if(card_slot > 1)
    {
        return cResult;
    }
    
    sprintf( aucCmd, "AT+QSIMSWITCH=%d\r\n", card_slot);
    if(xy_atc_interface_call(aucCmd, NULL, NULL) == XY_ERR)
        cResult = XY_ERR;
    cResult = XY_OK;
    return cResult;
}

char xy_get_sim_slot(char* pcurrent_slot)
{
    ATC_MSG_SIMSWICH_R_CNF_STRU tSimswitchRCnf = { 0 };
    
    if(xy_atc_interface_call("AT+QSIMSWITCH?\r\n", NULL, (void*)&tSimswitchRCnf) == XY_ERR)
        return XY_ERR;

    *pcurrent_slot = tSimswitchRCnf.ucValue;
    
    xy_printf(0,ATC_AP_T,INFO_LOG,"xy_get_sim_slot:%d", *pcurrent_slot);
    return XY_OK;
}

char xy_get_sim_state()
{
    char    cResult = 2;
    ATC_MSG_SIMST_IND_STRU tSimCnf = { 0 };
    
    if(xy_atc_interface_call("AT+SIMST\r\n", NULL, (void*)&tSimCnf) == XY_ERR)
        return cResult;

    if(tSimCnf.ucSimStatus == 0)
    {
        cResult = 1;
    }
    else
    {
        cResult = 0;
    }
    xy_printf(0,ATC_AP_T,INFO_LOG,"xy_get_sim_state:%d", cResult);
    return cResult;
}

int xy_set_IMEI(char* Imei)
{
    char *pucCmd;
    int   ret;

    if(NULL == Imei || strlen(Imei) != 15)
    {
        return XY_ERR;
    }

    pucCmd = (char*)AtcAp_MallocWithClean(60);
    sprintf(pucCmd, "AT+NTSETID=1,%s\r\n", Imei);
    ret = xy_atc_interface_call(pucCmd, (func_AppInterfaceCallback)NULL, (void*)NULL);
    AtcAp_Free(pucCmd);
   
    return ret;
}

int xy_get_IMEI(char *imei, int len)
{
     ATC_MSG_CGSN_CNF_STRU*      pCgsnCnf;
     char                 s_Imei[IMEI_LEN]  = { 0 };
     
    if (len < IMEI_LEN || imei == NULL)
    {
        return XY_ERR;    
    }

    pCgsnCnf = (ATC_MSG_CGSN_CNF_STRU*)AtcAp_MallocWithClean(sizeof(ATC_MSG_CGSN_CNF_STRU) + IMEI_LEN + 1 - 4);
    if(xy_atc_interface_call("AT+CGSN=1\r\n", (func_AppInterfaceCallback)NULL, (void*)pCgsnCnf) != XY_OK)
    {
        AtcAp_Free(pCgsnCnf);
        return XY_ERR;
    }
    if(0 != pCgsnCnf->ucLen)
    {
        strcpy(s_Imei, (char*)pCgsnCnf->aucData);
        xy_assert(strlen(s_Imei) < IMEI_LEN);

        strcpy(imei, s_Imei);    
        xy_assert(strlen(imei) < IMEI_LEN);
    }
    AtcAp_Free(pCgsnCnf);
    return XY_OK;
}

int xy_get_IMSI(char *imsi, int len)
{
    ATC_MSG_CIMI_CNF_STRU tImsiCnf          = { 0 };
    char           s_Imsi[IMSI_LEN]  = { 0 };
    
    if (len < IMSI_LEN || imsi == NULL)
    {
        return XY_ERR;
    }   

    if(xy_atc_interface_call("AT+CIMI\r\n", (func_AppInterfaceCallback)NULL, (void*)&tImsiCnf) == XY_ERR)
    {
        return XY_ERR;
    }
    strcpy(s_Imsi, tImsiCnf.stImsi.aucImsi);
    xy_assert(strlen(s_Imsi) < IMSI_LEN);
    
    strcpy(imsi, s_Imsi);
    xy_assert(strlen(imsi) < IMSI_LEN);

    return XY_OK;
}

int xy_get_CELLID(int *cell_id)
{
    ATC_MSG_NGACTR_R_CNF_STRU tNgactrRCnf = { 0 };

    if(cell_id == NULL)
    {
        return XY_ERR;
    }
    
    if(xy_atc_interface_call("AT+NGACTR?\r\n", NULL, (void*)&tNgactrRCnf) != XY_OK)
    {
        return XY_ERR;
    }

    *cell_id = tNgactrRCnf.stRegContext.ulCellId;
    return XY_OK;
}

int xy_get_NCCID(char *ccid, int len)
{
    char             s_Iccid[UCCID_LEN]  = { 0 };
    ATC_MSG_UICCID_CNF_STRU tUccidCnf           = { 0 };
    
    if (len < UCCID_LEN)
    {
        return XY_ERR;    
    }   


#if USR_CUSTOM2
    if(XY_OK != xy_atc_interface_call("AT+NCCID\r\n", NULL, (void*)&tUccidCnf))
#else
    if(XY_OK != xy_atc_interface_call("AT+MCCID\r\n", NULL, (void*)&tUccidCnf))
#endif
    {
        return XY_ERR;
    }
    strcpy(s_Iccid, tUccidCnf.aucICCIDstring);
    xy_assert(strlen(s_Iccid) < UCCID_LEN);

    strcpy(ccid, s_Iccid);
    xy_assert(strlen(ccid) < UCCID_LEN);
    
    return XY_OK;
}

int xy_get_PDP_APN(char *apn_buf, int len, int cid)
{
    ATC_MSG_CGCONTRDP_CNF_STRU  *pCgcontrdpCnf;
    char                         aucAtCmd[20]  = { 0 };
    int                          query_cid     = 0;

    if (len < APN_LEN || apn_buf == NULL)
    {   
        return XY_ERR;
    }

    query_cid = (cid == -1 ? xy_get_DefWan_Cid() : cid);
    pCgcontrdpCnf = (ATC_MSG_CGCONTRDP_CNF_STRU*)AtcAp_MallocWithClean(sizeof(ATC_MSG_CGCONTRDP_CNF_STRU));
    sprintf(aucAtCmd, "AT+CGCONTRDP=%d\r\n", query_cid);   
    if(xy_atc_interface_call(aucAtCmd, NULL, (void*)pCgcontrdpCnf) != XY_OK)
    {
        AtcAp_Free(pCgcontrdpCnf);
        return XY_ERR;
    }

    if(0 != pCgcontrdpCnf->stPara.ucValidNum && 0 != pCgcontrdpCnf->stPara.aucPdpDynamicInfo[0].ucApnLen)
    {
        strncpy(apn_buf, pCgcontrdpCnf->stPara.aucPdpDynamicInfo[0].aucApn, pCgcontrdpCnf->stPara.aucPdpDynamicInfo[0].ucApnLen);
        xy_assert(strlen(apn_buf) <= pCgcontrdpCnf->stPara.aucPdpDynamicInfo[0].ucApnLen);
    }

    AtcAp_Free(pCgcontrdpCnf);
    return XY_OK;
}

int xy_get_T_ACT(int *t3324)
{
    ATC_MSG_NGACTR_R_CNF_STRU tNgactrRCnf = { 0 };

    if(t3324 == NULL)
    {
        return  XY_ERR;
    }
    
    if(xy_atc_interface_call("AT+NGACTR?\r\n", NULL, (void*)&tNgactrRCnf) != XY_OK)
    {
        return XY_ERR;
    }
    
    *t3324 = tNgactrRCnf.stRegContext.ulActTime;
    return XY_OK;
}

int xy_get_T_TAU(int *tau)
{
    ATC_MSG_NGACTR_R_CNF_STRU tNgactrRCnf = { 0 };

    if(tau == NULL)
    {
        return  XY_ERR;
    }

    if(xy_atc_interface_call("AT+NGACTR?\r\n", NULL, (void*)&tNgactrRCnf) != XY_OK)
    {
        return XY_ERR;
    }

    *tau = tNgactrRCnf.stRegContext.ulTauTime;
    return XY_OK;
}

int xy_set_PSM_Enable(char cValue)
{
    char *pucCmd;
    int   ret;

    pucCmd = (char*)AtcAp_MallocWithClean(20);
    sprintf(pucCmd, "AT+CPSMS=%d\r\n", cValue);
    ret = xy_atc_interface_call(pucCmd, (func_AppInterfaceCallback)NULL, (void*)NULL);
    AtcAp_Free(pucCmd);
   
    return ret;
}

int xy_get_PSM_Enable(char* cValue)
{
     ATC_MSG_CPSMS_R_CNF_STRU*      pCpsmsCnf;

    pCpsmsCnf = (ATC_MSG_CPSMS_R_CNF_STRU*)AtcAp_MallocWithClean(sizeof(ATC_MSG_CPSMS_R_CNF_STRU) + 1);
    if(xy_atc_interface_call("AT+CPSMS?\r\n", (func_AppInterfaceCallback)NULL, (void*)pCpsmsCnf) != XY_OK)
    {
        AtcAp_Free(pCpsmsCnf);
        return XY_ERR;
    }
    *cValue = pCpsmsCnf->ucMode;
    AtcAp_Free(pCpsmsCnf);
    return XY_OK;
}

int xy_get_UICC_TYPE(int *uicc_type)
{
    ATC_MSG_NGACTR_R_CNF_STRU tNgactrRCnf = { 0 };
    int                s_uiccType  = 0;

    if(uicc_type == NULL)
    {
        return  XY_ERR;
    }

    if(xy_atc_interface_call("AT+NGACTR?\r\n", NULL, (void*)&tNgactrRCnf) != XY_OK)
    {
        return XY_ERR;
    }
    s_uiccType = tNgactrRCnf.usOperType + 1;

    *uicc_type = s_uiccType;
    return XY_OK;
}



#ifndef _FLASH_OPTIMIZE_

static unsigned long xy_atc_GeteDrxValueMS(unsigned char uceDrxValue)
{
    int i;

    for(i = 0; i < sizeof(xy_atc_eDrxValue_Tab)/sizeof(ST_XY_ATC_EDRX_VALUE_TABLE); i++)
    {
        if(uceDrxValue == xy_atc_eDrxValue_Tab[i].ucSetVal)
        {
            return xy_atc_eDrxValue_Tab[i].ulMS;
        }
    }

    return 0;
}

static unsigned long xy_atc_GetPtwValueMS(unsigned char ucPtwValue)
{
    int i;

    for(i = 0; i < sizeof(xy_atc_eDrxPtw_Tab)/sizeof(ST_XY_ATC_EDRX_VALUE_TABLE); i++)
    {
        if(ucPtwValue == xy_atc_eDrxPtw_Tab[i].ucSetVal)
        {
            return xy_atc_eDrxPtw_Tab[i].ulMS;
        }
    }

    return 0;
}

int xy_get_eDRX_value_MS(unsigned char* pucActType, unsigned long* pulEDRXValue, unsigned long* pulPtwValue)
{
    ATC_MSG_CEDRXRDP_CNF_STRU tCderxrdpCnf = { 0 };

    if(pucActType == NULL || pulEDRXValue == NULL || pulPtwValue == NULL)
    {
        return XY_ERR;
    }
    
    if(XY_OK != xy_atc_interface_call("AT+CEDRXRDP\r\n", (func_AppInterfaceCallback)NULL, (void*)&tCderxrdpCnf))
    {
        return XY_ERR;
    }

    if(tCderxrdpCnf.stPara.ucActType == LTE_EDRX_NOT_USING)
    {
        *pucActType = tCderxrdpCnf.stPara.ucActType;
        *pulEDRXValue = 0;
        *pulPtwValue = 0;
    }
    else
    {
        *pucActType = tCderxrdpCnf.stPara.ucActType;
        *pulEDRXValue = xy_atc_GeteDrxValueMS(tCderxrdpCnf.stPara.ucNWeDRXValue);
        *pulPtwValue = xy_atc_GetPtwValueMS(tCderxrdpCnf.stPara.ucPagingTimeWin);
    }
    return XY_OK;
}

int xy_get_eDRX_value(float *eDRX_value, float *ptw_value)
{
    unsigned long ulEDRXValue;
    unsigned long ulPtwValue;
    unsigned char ucActType;

    if(eDRX_value == NULL || ptw_value == NULL)
    {
        return XY_ERR;
    }

    if(xy_get_eDRX_value_MS(&ucActType, &ulEDRXValue, &ulPtwValue) != XY_OK)
    {
        return XY_ERR;
    }

    *eDRX_value = ulEDRXValue / 1000;
    *ptw_value = ulPtwValue / 1000;
    
    return XY_OK;
}

#endif
int xy_set_eDRX_value(unsigned char modeVal, unsigned char actType, unsigned long ulDrxValue)
{
    char*             pDrxValueBitStr;
    char*             pucCmd;
    int               ret;

    pDrxValueBitStr = xy_atc_GeteDrxValueBitStr(ulDrxValue);
    if(pDrxValueBitStr == NULL)
    {
        return XY_ERR;
    }
   
    pucCmd = (char*)AtcAp_MallocWithClean(30);
    sprintf(pucCmd, "AT+CEDRXS=%d,%d,\"%s\"\r\n", modeVal, actType, pDrxValueBitStr);
    ret = xy_atc_interface_call(pucCmd, (func_AppInterfaceCallback)NULL, (void*)NULL);
    AtcAp_Free(pucCmd);
    
    return ret;
}

int xy_get_RSSI(int *rssi)
{
    ATC_MSG_CSQ_CNF_STRU tCsqCnf = { 0 };

    if (rssi == NULL)
    {
        return XY_ERR;
    }
    
    if(xy_atc_interface_call("AT+CSQ\r\n", NULL, (void*)&tCsqCnf) != XY_OK)
    {
        return XY_ERR;
    }

    *rssi = tCsqCnf.ucRxlev;
    return XY_OK;
}

char xy_get_net_state()
{
    char    cState = 0;
    int     cCeregState = 0;

    if(xy_get_sim_state() == 0)
    {
        cState = 1;
    }
    else
    {
#ifndef PS_INTEGRATION_TEST
        return cState;
#endif
    }
    xy_cereg_read(&cCeregState);

    switch(cCeregState)
    {
    case 2: //searching
        cState = 2;
        break;
    case 1:
    case 5: //register
        cState = 5;
        break;
    case 0: //disconnected
        cState = 7;
        break;
    default:
        break;
    }
    xy_printf(0,ATC_AP_T,INFO_LOG,"xy_get_net_state:%d", cState);
    return cState;
}

int xy_get_servingcell_info(ril_serving_cell_info_t *rcv_serving_cell_info)
{
    ATC_MSG_MUESTATS_CNF_STRU* pNueStatsCnf;

    xy_assert(rcv_serving_cell_info != NULL);

    pNueStatsCnf = (ATC_MSG_MUESTATS_CNF_STRU*)AtcAp_Malloc(sizeof(ATC_MSG_MUESTATS_CNF_STRU));
    if(XY_OK != xy_atc_interface_call("AT+MUESTATS=radio\r\n", (func_AppInterfaceCallback)NULL, (void*)pNueStatsCnf))
    {
        AtcAp_Free(pNueStatsCnf);
        return XY_ERR;
    }

    memset(rcv_serving_cell_info, 0, sizeof(ril_serving_cell_info_t));
    
    rcv_serving_cell_info->Signalpower = pNueStatsCnf->stRadio.rsrp;
    rcv_serving_cell_info->Totalpower = pNueStatsCnf->stRadio.rssi;
    rcv_serving_cell_info->TXpower = pNueStatsCnf->stRadio.current_tx_power_level;
    rcv_serving_cell_info->CellID  = pNueStatsCnf->stRadio.last_cell_ID;
    rcv_serving_cell_info->ECL = pNueStatsCnf->stRadio.last_ECL_value;
    rcv_serving_cell_info->SNR = pNueStatsCnf->stRadio.last_snr_value;
    rcv_serving_cell_info->EARFCN = pNueStatsCnf->stRadio.last_earfcn_value;
    rcv_serving_cell_info->PCI = pNueStatsCnf->stRadio.last_pci_value;
    rcv_serving_cell_info->RSRQ = pNueStatsCnf->stRadio.rsrq;
    sprintf(rcv_serving_cell_info->tac, "%d", pNueStatsCnf->stRadio.current_tac);
    rcv_serving_cell_info->sband = pNueStatsCnf->stRadio.band;
    rcv_serving_cell_info->plmn = pNueStatsCnf->stRadio.current_plmn;

    AtcAp_Free(pNueStatsCnf);
    return XY_OK;
}

int xy_get_neighborcell_info(ril_neighbor_cell_info_t *ril_neighbor_cell_info)
{
    ATC_MSG_MUESTATS_CNF_STRU* pNueStatsCnf;
    unsigned char                        i = 0,j = 0;
    unsigned char                aucSelectPlmn[7];

    xy_assert(ril_neighbor_cell_info != NULL);

    pNueStatsCnf = (ATC_MSG_MUESTATS_CNF_STRU*)AtcAp_Malloc(sizeof(ATC_MSG_MUESTATS_CNF_STRU));
    if(XY_OK != xy_atc_interface_call("AT+MUESTATS=cell\r\n", (func_AppInterfaceCallback)NULL, (void*)pNueStatsCnf))
    {
        AtcAp_Free(pNueStatsCnf);
        return XY_ERR;
    }
    if(pNueStatsCnf->type != ATC_NUESTATS_TYPE_CELL)
    {
        AtcAp_Free(pNueStatsCnf);
        return XY_ERR;
    }
    for(i = 1; (i < (pNueStatsCnf->stCell.stCellList.ucCellNum)); i++)
    {
        AtcAp_MemSet(aucSelectPlmn, 0, 7);
        AtcAp_IntegerToPlmn(pNueStatsCnf->stCell.stCellList.aNCell[i].ulPlmn, aucSelectPlmn);
        strncpy((char*)ril_neighbor_cell_info->neighbor_cell_info[j].auc_mcc, (char*)aucSelectPlmn, 3);
        strncpy((char*)ril_neighbor_cell_info->neighbor_cell_info[j].auc_mnc, (char*)aucSelectPlmn + 3, 3);

        ril_neighbor_cell_info->neighbor_cell_info[j].nc_earfcn = pNueStatsCnf->stCell.stCellList.aNCell[i].ulDlEarfcn;
        ril_neighbor_cell_info->neighbor_cell_info[j].nc_pci = pNueStatsCnf->stCell.stCellList.aNCell[i].usPhyCellId;
        ril_neighbor_cell_info->neighbor_cell_info[j].nc_rsrp = pNueStatsCnf->stCell.stCellList.aNCell[i].sRsrp;
        ril_neighbor_cell_info->neighbor_cell_info[j].nc_CellId = pNueStatsCnf->stCell.stCellList.aNCell[i].ulCellId;
        ril_neighbor_cell_info->neighbor_cell_info[j].nc_Tac = pNueStatsCnf->stCell.stCellList.aNCell[i].usTac;
        //signal :[-113,0]
        if((pNueStatsCnf->stCell.stCellList.aNCell[i].sRssi/10) < -113)
        {
            ril_neighbor_cell_info->neighbor_cell_info[j].nc_signal = -113;
        }
        else
        {
            ril_neighbor_cell_info->neighbor_cell_info[j].nc_signal = pNueStatsCnf->stCell.stCellList.aNCell[i].sRssi / 10;
        }
        ril_neighbor_cell_info->nc_num++;
        j++;
    }
    AtcAp_Free(pNueStatsCnf);
    return XY_OK;
}

int xy_get_phy_info(ril_phy_info_t *rcv_phy_info)
{
    ATC_MSG_MUESTATS_CNF_STRU* pNueStatsCnf;

    xy_assert(rcv_phy_info != NULL);

    pNueStatsCnf = (ATC_MSG_MUESTATS_CNF_STRU*)AtcAp_Malloc(sizeof(ATC_MSG_MUESTATS_CNF_STRU));
    if(XY_OK != xy_atc_interface_call("AT+MUESTATS=bler\r\n", (func_AppInterfaceCallback)NULL, (void*)pNueStatsCnf))
    {
        AtcAp_Free(pNueStatsCnf);
        return XY_ERR;
    }
#ifndef PS_INTEGRATION_TEST
    if(pNueStatsCnf->type != ATC_NUESTATS_TYPE_BLER)
    {
        AtcAp_Free(pNueStatsCnf);
        return XY_ERR;
    }
#endif
    rcv_phy_info->RLC_UL_BLER = pNueStatsCnf->stBler.rlc_ul_bler;
    rcv_phy_info->RLC_DL_BLER = pNueStatsCnf->stBler.rlc_dl_bler;
    rcv_phy_info->MAC_UL_BLER = pNueStatsCnf->stBler.mac_ul_bler;
    rcv_phy_info->MAC_DL_BLER = pNueStatsCnf->stBler.mac_dl_bler;
    rcv_phy_info->MAC_UL_total_bytes = pNueStatsCnf->stBler.total_bytes_transmit;
    rcv_phy_info->MAC_DL_total_bytes = pNueStatsCnf->stBler.total_bytes_receive;
    rcv_phy_info->MAC_UL_total_HARQ_TX = pNueStatsCnf->stBler.transport_blocks_send;
    rcv_phy_info->MAC_DL_total_HARQ_TX = pNueStatsCnf->stBler.transport_blocks_receive;
    rcv_phy_info->MAC_UL_HARQ_re_TX = pNueStatsCnf->stBler.transport_blocks_retrans;
    rcv_phy_info->MAC_DL_HARQ_re_TX = pNueStatsCnf->stBler.total_ackOrNack_msg_receive;

    //AT+NUESTATS=THP
    memset(pNueStatsCnf, 0, sizeof(ATC_MSG_MUESTATS_CNF_STRU));
    if(XY_OK != xy_atc_interface_call("AT+MUESTATS=thp\r\n", (func_AppInterfaceCallback)NULL, (void*)pNueStatsCnf))
    {
        AtcAp_Free(pNueStatsCnf);
        return XY_ERR;
    }
#ifndef PS_INTEGRATION_TEST
    if(pNueStatsCnf->type != ATC_NUESTATS_TYPE_THP)
    {
        AtcAp_Free(pNueStatsCnf);
        return XY_ERR;
    }
#endif
    rcv_phy_info->RLC_UL_tput = pNueStatsCnf->stThp.rlc_ul;
    rcv_phy_info->RLC_DL_tput = pNueStatsCnf->stThp.rlc_dl;
    rcv_phy_info->MAC_UL_tput = pNueStatsCnf->stThp.mac_ul;
    rcv_phy_info->MAC_DL_tput = pNueStatsCnf->stThp.mac_dl;

    AtcAp_Free(pNueStatsCnf);
    return XY_OK;
}

int xy_get_radio_info(ril_radio_info_t *rcv_radio_info)
{
    xy_assert(rcv_radio_info != NULL);
    memset(rcv_radio_info, 0, sizeof(ril_radio_info_t));

    if (xy_get_servingcell_info(&rcv_radio_info->serving_cell_info) != XY_OK)
    {
        return XY_ERR;
    }

    if (xy_get_neighborcell_info(&rcv_radio_info->neighbor_cell_info) != XY_OK)
    {
        return XY_ERR;
    }

    if (xy_get_phy_info(&rcv_radio_info->phy_info) != XY_OK)
    {
        return XY_ERR;
    }
    return XY_OK;
}

int xy_get_neighborcell_info_qcell(ril_neighbor_cell_info_t_qcell *ril_neighbor_cell_info)
{
    ATC_MSG_QCELL_R_CNF_STRU* pQcellCnf;
    unsigned char                        i = 0;
    unsigned char                aucSelectPlmn[7];

    xy_assert(ril_neighbor_cell_info != NULL);

    pQcellCnf = (ATC_MSG_QCELL_R_CNF_STRU*)AtcAp_Malloc(sizeof(ATC_MSG_QCELL_R_CNF_STRU));
    if(XY_OK != xy_atc_interface_call("AT+QCELL?\r\n", (func_AppInterfaceCallback)NULL, (void*)pQcellCnf))
    {
        AtcAp_Free(pQcellCnf);
        return XY_ERR;
    }

    for(i = 0; (i < (pQcellCnf->tCellInfo.ucNum)); i++)
    {
        AtcAp_MemSet(aucSelectPlmn, 0, 7);
        AtcAp_IntegerToPlmn(pQcellCnf->tCellInfo.aCellInfo[i].ulPlmn, aucSelectPlmn);
        strncpy((char*)ril_neighbor_cell_info->neighbor_cell_info[i].auc_mcc, (char*)aucSelectPlmn, 3);
        strncpy((char*)ril_neighbor_cell_info->neighbor_cell_info[i].auc_mnc, (char*)aucSelectPlmn + 3, 3);

        ril_neighbor_cell_info->neighbor_cell_info[i].nc_CellId = pQcellCnf->tCellInfo.aCellInfo[i].ulCell_ID;
        ril_neighbor_cell_info->neighbor_cell_info[i].nc_Tac = pQcellCnf->tCellInfo.aCellInfo[i].usTac;

        ril_neighbor_cell_info->neighbor_cell_info[i].nc_signal = pQcellCnf->tCellInfo.aCellInfo[i].ucRxLev - 111;

        ril_neighbor_cell_info->nc_num++;

    }
    AtcAp_Free(pQcellCnf);
    return XY_OK;
}


int xy_simst_read()
{
    ATC_MSG_SIMST_IND_STRU tSIMRCnf = { 0 };
    ATC_MSG_CEER_IND_STRU  tCeerCnf = { 0 };
    int simst = 3;

    if(xy_atc_interface_call("AT+SIMST\r\n", NULL, (void*)&tSIMRCnf) == XY_ERR)
        return simst;

    if(D_ATC_SIMST_NOT_PRESENT == tSIMRCnf.ucSimStatus)
    {
        simst = 1;
    }
#ifndef PS_INTEGRATION_TEST
    else
#endif
    {
        if(xy_atc_interface_call("AT+CEER\r\n", NULL, (void*)&tCeerCnf) == XY_ERR)
            return simst;
        if(D_FAIL_CAUSE_TYPE_EMM == tCeerCnf.ucType)
        {
            if(tCeerCnf.ucCause < 9 && tCeerCnf.ucCause != 5)
            {
                simst = 2;
            }
        }
        else
        {
            simst = 0;
        }
    }
    return simst;
}

int xy_wifiscan_cfg(char* fuction , char value)
{
    char    aucCmd[28] = { 0 };

    sprintf( aucCmd, "AT+MWIFISCANCFG=%s,%d\r\n", fuction, value);
    return xy_atc_interface_call(aucCmd, NULL, NULL);
}

int xy_wifiscan_start(int Timeout,char ScanRound, char WifiNum, char ScanTime, char Priority)
{
    char    aucCmd[40] = { 0 };

    if(Timeout == 0)
    {
        sprintf( aucCmd, "AT+MWIFISCANSTART=%d,%d\r\n", ScanRound, ScanTime);
    }
    else
    {
        sprintf( aucCmd, "AT+QWIFISCAN=%d,%d,%d,%d,%d\r\n", Timeout, ScanRound, WifiNum, ScanTime, Priority);
    }
    return xy_atc_interface_call(aucCmd, NULL, NULL);
}

int xy_wifiscan_stop()
{
    return xy_atc_interface_call("AT+MWIFISCANSTOP\r\n", NULL, NULL);
}

int xy_wifiscan_que(ATC_MSG_MWIFISCANQUERT_CNF_STRU* pWifiScanInfo,char cSort)
{
    char    aucCmd[28] = { 0 };
    
    sprintf( aucCmd, "AT+MWIFISCANQUERY=%d\r\n", cSort);
    return xy_atc_interface_call(aucCmd, NULL, pWifiScanInfo);
}

int xy_set_rfmode(int value)
{
    char    aucCmd[28] = { 0 };
    
    sprintf( aucCmd, "AT+NSET=\"RFMODE\",%d\r\n", value);
    return xy_atc_interface_call(aucCmd, NULL, NULL);
}

int xy_get_rfmode(int* value)
{
    ATC_MSG_NSET_R_CNF_STRU tRfmode = {0};

    if(xy_atc_interface_call("AT+NSET=\"RFMODE\"\r\n", NULL, &tRfmode) == XY_ERR)
        return XY_ERR;
    *value = tRfmode.u.ulValue;

    return XY_OK;
}

int xy_get_ipv4_mtu(unsigned char cid, unsigned short* IPV4_MTU)
{
    ATC_MSG_CGCONTRDP_CNF_STRU  *pCgcontrdpCnf;
    char                         aucAtCmd[20]  = { 0 };

    pCgcontrdpCnf = (ATC_MSG_CGCONTRDP_CNF_STRU*)AtcAp_Malloc(sizeof(ATC_MSG_CGCONTRDP_CNF_STRU));
    sprintf(aucAtCmd, "AT+CGCONTRDP=%d\r\n", cid);   
    if(xy_atc_interface_call(aucAtCmd, NULL, (void*)pCgcontrdpCnf) != XY_OK)
    {
        AtcAp_Free(pCgcontrdpCnf);
        return ATC_AP_FALSE;
    }

    if(0 != pCgcontrdpCnf->stPara.ucValidNum && 0 != pCgcontrdpCnf->stPara.aucPdpDynamicInfo[0].ucIPv4MTUFlag)
    {
        *IPV4_MTU = pCgcontrdpCnf->stPara.aucPdpDynamicInfo[0].usIPv4MTU;
        AtcAp_Free(pCgcontrdpCnf);
        return ATC_AP_TRUE;
    }

    AtcAp_Free(pCgcontrdpCnf);
    return ATC_AP_FALSE;
}
#if VER_CM
void urc_cid_limit_handle(unsigned long eventId, void *param, int paramLen)
{
    UNUSED_ARG(eventId);
    xy_assert(paramLen == sizeof(ATC_MSG_CGEV_IND_STRU));
    ATC_MSG_CGEV_IND_STRU *cgev_urc = (ATC_MSG_CGEV_IND_STRU*)param;
    
    switch(cgev_urc->ucCgevEventId)
    {
        case D_ATC_CGEV_NW_MODIFY:
        case D_ATC_CGEV_ME_MODIFY:
        case D_ATC_CGEV_OOS:
        case D_ATC_CGEV_IS:
            break;
        default:
            if(xy_get_cid_limit_flg())
            {
                xy_set_cid_limit_flg(0);
            }
            break;
    }
    
}

unsigned char xy_get_cid_limit_flg()
{
    unsigned char   ucFlg = 0;
    osMutexAcquire(g_AtcApInfo.stCidlimitInfo.mutex, osWaitForever);
    
    ucFlg = g_AtcApInfo.stCidlimitInfo.ucCidlimitFlg;
    
    osMutexRelease(g_AtcApInfo.stCidlimitInfo.mutex);

    return ucFlg;
}

void xy_set_cid_limit_flg( unsigned char ucFlg)
{
    static unsigned char    ucRegisterFlg;
    osMutexAcquire(g_AtcApInfo.stCidlimitInfo.mutex, osWaitForever);
    
    g_AtcApInfo.stCidlimitInfo.ucCidlimitFlg = ucFlg;
    
    osMutexRelease(g_AtcApInfo.stCidlimitInfo.mutex);
    if (ucFlg && ucRegisterFlg == 0)
    {
        xy_atc_registerPSEventCallback(D_XY_PS_REG_EVENT_CGEV, urc_cid_limit_handle);
        ucRegisterFlg = 1;
    }
    return;
}
#endif
int xy_activate_cid(char cid, char mipcallflg)
{
    char                         aucAtCmd[24]  = { 0 };
    sprintf(aucAtCmd, "AT+CIDACT=%d,%d\r\n", cid,mipcallflg);
    xy_printf(0,ATC_AP_T,INFO_LOG,"xy_activate_cid");
    if(xy_atc_interface_call(aucAtCmd, NULL, NULL) == XY_ERR)
        return XY_ERR;
#if VER_CM
    xy_set_cid_limit_flg(0);
#endif
    return XY_OK;
}

int xy_deactivate_cid(char cid)
{
    char                         aucAtCmd[24]  = { 0 };
    sprintf(aucAtCmd, "AT+QIDEACTEX=%d\r\n", cid);
    xy_printf(0,ATC_AP_T,INFO_LOG,"xy_deactivate_cid");
    if(xy_atc_interface_call(aucAtCmd, NULL, NULL) == XY_ERR)
        return XY_ERR;
    return XY_OK;
}

int xy_activate_cid_QIACTEX(char cid ,char ViewMode)
{
    char                         aucAtCmd[24]  = { 0 };
    sprintf(aucAtCmd, "AT+QIACTEX=%d,%d\r\n", cid, ViewMode);
    xy_printf(0,ATC_AP_T,INFO_LOG,"xy_activate_cid");
    if(xy_atc_interface_call(aucAtCmd, NULL, NULL) == XY_ERR)
        return XY_ERR;
    return XY_OK;
}

#if VER_CM
void xy_get_cid_state_info(unsigned char ucCid, ST_ATC_AP_CID_INFO* stCidInfo)
{
    unsigned char i = 0;
    osMutexAcquire(g_AtcApInfo.stCidStateInfo.mutex, osWaitForever);
    
    for(i = 0 ;i < D_MAX_CNT_CID;i++)
    {
        if(g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucIsUsed == ATC_AP_TRUE
            && g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCid == ucCid)
        {
            AtcAp_MemCpy(stCidInfo, &(g_AtcApInfo.stCidStateInfo.stCidStateInfo[i]), sizeof(ST_ATC_AP_CID_INFO));
            break;
        }
    }
    xy_printf(0,ATC_AP_T,INFO_LOG,"xy_get_cid_state_info:cid:%d,Mipcall:%d,state:%d",
                g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCid,
                g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucMipcallFlg,
                g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCidState);
    osMutexRelease(g_AtcApInfo.stCidStateInfo.mutex);
    return;
}

void xy_set_cid_state_mipcallflg( unsigned char ucCid, unsigned char ucMipcallFlg )
{
    unsigned char i = 0;
    osMutexAcquire(g_AtcApInfo.stCidStateInfo.mutex, osWaitForever);
    for(i = 0 ;i < D_MAX_CNT_CID;i++)
    {
        if(g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucIsUsed == ATC_AP_TRUE
            && g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCid == ucCid)
        {
            g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucMipcallFlg = ucMipcallFlg;
            break;
        }
    }
    if(i == D_MAX_CNT_CID 
        && ucMipcallFlg != 0)
    {
        for(i = 0;i < D_MAX_CNT_CID;i++)
        {
            if(g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucIsUsed == ATC_AP_FALSE)
            {
                g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucIsUsed = ATC_AP_TRUE;
                g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCid = ucCid;
                g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucMipcallFlg = ucMipcallFlg;
                break;
            }
        }
    }
    xy_printf(0,ATC_AP_T,INFO_LOG,"xy_set_cid_state_mipcallflg:cid:%d,Mipcall:%d,state:%d",
            g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCid,
            g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucMipcallFlg,
            g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCidState);
    osMutexRelease(g_AtcApInfo.stCidStateInfo.mutex);
    return;
}

void xy_set_cid_state_info( unsigned char ucCid, unsigned char ucstate,unsigned char ucType )
{
    unsigned char i = 0;
    osMutexAcquire(g_AtcApInfo.stCidStateInfo.mutex, osWaitForever);
    for(i = 0 ;i < D_MAX_CNT_CID;i++)
    {
        if(g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucIsUsed == ATC_AP_TRUE
            && g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCid == ucCid)
        {
            if(D_CID_STATE_DEACTIVE == ucstate)
            {
                AtcAp_MemSet(&g_AtcApInfo.stCidStateInfo.stCidStateInfo[i], 0, sizeof(ST_ATC_AP_CID_INFO));
            }
            else
            {
                g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCidState = ucstate;
                g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCidType = ucType;
            }
            break;
        }
    }
    if(i == D_MAX_CNT_CID && D_CID_STATE_ACTIVE == ucstate)
    {
        for(i = 0;i < D_MAX_CNT_CID;i++)
        {
            if(g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucIsUsed == ATC_AP_FALSE)
            {
                g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucIsUsed = ATC_AP_TRUE;
                g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCid = ucCid;
                g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCidState = ucstate;
                g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCidType = ucType;
                break;
            }
        }
    }
    xy_printf(0,ATC_AP_T,INFO_LOG,"xy_set_cid_state_info:cid:%d,Mipcall:%d,state:%d",
            g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCid,
            g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucMipcallFlg,
            g_AtcApInfo.stCidStateInfo.stCidStateInfo[i].ucCidState);
    osMutexRelease(g_AtcApInfo.stCidStateInfo.mutex);
    return;
}

void xy_clean_cid_state_info(  )
{
    static unsigned char    ucRegisterFlg;
    osMutexAcquire(g_AtcApInfo.stCidlimitInfo.mutex, osWaitForever);
    
    AtcAp_MemSet(g_AtcApInfo.stCidStateInfo.stCidStateInfo, 0 ,sizeof(ST_ATC_AP_CID_INFO) * D_MAX_CNT_CID);
    
    osMutexRelease(g_AtcApInfo.stCidlimitInfo.mutex);
    return;
}

void urc_cid_state_handle(unsigned long eventId, void *param, int paramLen)
{
    UNUSED_ARG(eventId);
    xy_assert(paramLen == sizeof(ATC_MSG_CGEV_IND_STRU));
    ATC_MSG_CGEV_IND_STRU *cgev_urc = (ATC_MSG_CGEV_IND_STRU*)param;
    switch(cgev_urc->ucCgevEventId)
    {
    case D_ATC_CGEV_NW_DETACH:
    case D_ATC_CGEV_ME_DETACH:
        xy_clean_cid_state_info();
        break; 
    /* +CGEV: ME PDN ACT <cid>[,<reason>[,<cid_other>]][,<WLAN_Offload>] */
    case D_ATC_CGEV_ME_PDN_ACT:
        xy_set_cid_state_info(cgev_urc->stCgevPara.ucCid, D_CID_STATE_ACTIVE, D_CID_TYPE_DEFAULT);
        break;

   /* +CGEV: NW PDN DEACT <cid>[,<WLAN_Offload>] */
    case D_ATC_CGEV_NW_PDN_DEACT:
   /* +CGEV: ME PDN DEACT <cid>[,<WLAN_Offload>] */
    case D_ATC_CGEV_ME_PDN_DEACT:
        xy_set_cid_state_info(cgev_urc->stCgevPara.ucCid, D_CID_STATE_DEACTIVE, D_CID_TYPE_DEFAULT);
        break;
    
    /* +CGEV: NW DEACT <p_cid>, <cid>, <event_type>[,<WLAN_Offload>] */
    case D_ATC_CGEV_NW_DEACT:
    /* +CGEV: NW DEACT <p_cid>, <cid>, <event_type>[,<WLAN_Offload>] */
    case D_ATC_CGEV_ME_DEACT :
        break;
    
    /* +CGEV: NW ACT <p_cid>, <cid>, <event_type>[,<WLAN_Offload>] */
    case D_ATC_CGEV_NW_ACT:
    /* +CGEV: ME ACT <p_cid>, <cid>, <event_type>[,<WLAN_Offload>] */
    case D_ATC_CGEV_ME_ACT:
        break;
    
    case D_ATC_CGEV_NW_MODIFY:
    case D_ATC_CGEV_ME_MODIFY:
    case D_ATC_CGEV_OOS:
    case D_ATC_CGEV_IS:
    default:
        break;
    }

}

#endif

int xy_set_ppp_param(char cid, char* ip_type, char *apn)
{
    char    aucCmd[60] = { 0 };

    xy_assert(ip_type != NULL && strlen(ip_type) <= 8);
    xy_assert(apn != NULL && strlen(apn) <= 30);

    sprintf(aucCmd, "AT+CGDCONT=%d,%s,%s\r\n", cid, ip_type, apn);

    return xy_atc_interface_call(aucCmd, NULL, NULL);
}

int xy_set_ppp_auth_param(char cid, char auth_prot, char *username, char* passwd)
{
    char    aucCmd[60] = { 0 };

    xy_assert(username != NULL && strlen(username) <= 16);
    xy_assert(passwd != NULL && strlen(passwd) <= 16);

    sprintf(aucCmd, "AT+CGAUTH=%d,%d,%s,%s\r\n", cid, auth_prot, username, passwd);

    return xy_atc_interface_call(aucCmd, NULL, NULL);
}
