#if PS_TEST_MODE
#if !ATC_DSP
//#include "xy_memmap.h"
#include "xy_system.h"
#endif
#include "cmsis_os2.h"
#include "xy_atc_interface.h"

#include "pstest.h"
#include "pstest_api.h"

#include "atc_ps_def.h"
#include "atc_ps.h"

ST_PS_TEST_DEMO_INFO g_PSTestDemoInfo;
ST_PS_TEST_SAVE_SD_INFO g_PSTestSDInfo;

 ST_PS_TEST_MSG_RCV_PROC_TABLE PsTestMsgRcvProcTable[PS_TEST_MSG_MAX] =
{
    { PS_TEST_AT_MSG,                      Ps_Test_Msg_Check              },
    { PS_TEST_AT_TIME_OUT,                 Ps_Test_AT_Time_Out            },
    { PS_TEST_TIME_OUT,                    Ps_Test_CASE_Time_Out          },
};

 ST_PS_TEST_MSG_RCV_PROC_TABLE PsTestEventTable[PS_TEST_EVENT_MAX] =
{
    { START_PS_TEST_FLG,                   Ps_Test_Start              },
//    { RESTART_PS_TEST_FLG,                 Ps_Test_Restart            },
//    { END_PS_TEST_FLG,                     Ps_Test_End                },
};

void ps_test_init()
{
    // g_softap_fac_nv->ucPSTestStartFlg = Test_False;
    g_softap_fac_nv->ucPSTestNum = 0;
    g_softap_fac_nv->ucPSTestTimers = 0;
    // SAVE_FAC_PARAM(ucPSTestStartFlg);
    SAVE_FAC_PARAM(ucPSTestNum);
    SAVE_FAC_PARAM(ucPSTestTimers);
}

void Ps_Send_to_Test_Msg(void* pMsg, ST_TEST_AP_MSG_INFO* pMsgInfo)
{
    ST_TEST_AP_MSG_NODE* pAddNode;

    pAddNode = (ST_TEST_AP_MSG_NODE*)AtcAp_Malloc(sizeof(ST_TEST_AP_MSG_NODE));
    pAddNode->ulMsg = (unsigned long)pMsg;
    pAddNode->next = NULL;

    osMutexAcquire(pMsgInfo->msgMutex, osWaitForever);
    pMsgInfo->ucCnt++;
    
    if(NULL == pMsgInfo->last)
    {
        pMsgInfo->head = pAddNode;
        pMsgInfo->last = pAddNode;
    }
    else
    {
        pMsgInfo->last->next = pAddNode;
        pMsgInfo->last = pAddNode;
    }
    osMutexRelease(pMsgInfo->msgMutex);

    osSemaphoreRelease(pMsgInfo->msgSemaPhore);
}

static signed long Ps_Test_Recv_Msg(unsigned long* pRecvMsg, ST_TEST_AP_MSG_INFO* pMsgInfo)
{
    ST_TEST_AP_MSG_NODE* pCurrNode;
  
    osSemaphoreAcquire(pMsgInfo->msgSemaPhore, osWaitForever);
    osMutexAcquire(pMsgInfo->msgMutex, osWaitForever);
    
    if(NULL == pMsgInfo->head)
    {
        osMutexRelease(pMsgInfo->msgMutex);
        return -1;
    }

    pCurrNode = pMsgInfo->head; 
    *pRecvMsg = pCurrNode->ulMsg;

    pMsgInfo->head = pCurrNode->next;
    if(NULL == pMsgInfo->head)
    {
        pMsgInfo->last = NULL;
    }
    pMsgInfo->ucCnt--;
    osMutexRelease(pMsgInfo->msgMutex);

    AtcAp_Free(pCurrNode);
    
    return 0;
}

void PS_Test_TaskEntry(void* pArgs)
{
    Unused_para(pArgs);
    unsigned long          RecvMsg;
    unsigned char           i;
    unsigned char*           event;
    
    xy_atc_registerPSEventCallback(D_XY_PS_REG_EVENT_CESQ_Ind, urc_cesq_Callback);
    for (;;)
    {
        if (-1 != Ps_Test_Recv_Msg(&RecvMsg, &g_PSTestDemoInfo.stTestSendMsgInfo))
        {
            event = (unsigned char*)RecvMsg;
            for(i = 0; i < PS_TEST_EVENT_MAX; i++)
            {
                if(PsTestEventTable[i].ucEventId == ((ST_TEST_EVENT_HEAD*)event)->ucEventID)
                {
                    PsTestEventTable[i].MsgProc(event);
                    break;
                }
            }
            if(i >= PS_TEST_EVENT_MAX)
            {
                xy_assert(0);
            }
            if(NULL != event)
            {
                AtcAp_Free(event);
            }
        }
    }
}

void PS_Test_Receive_TaskEntry(void* pArgs)
{
    Unused_para(pArgs);
    unsigned long          RecvMsg;
    unsigned char           i;
    unsigned char*           event;

    for (;;)
    {
        if (-1 != Ps_Test_Recv_Msg(&RecvMsg, &g_PSTestDemoInfo.stTestReceiveMsgInfo))
        {
            event = (unsigned char*)RecvMsg;
            for(i = 0; i < PS_TEST_MSG_MAX; i++)
            {
                if(PsTestMsgRcvProcTable[i].ucEventId == ((ST_TEST_EVENT_HEAD*)event)->ucEventID)
                {
                    PsTestMsgRcvProcTable[i].MsgProc(event);
                    break;
                }
            }
            if(i >= PS_TEST_MSG_MAX)
            {
                xy_assert(0);
            }
            if(NULL != event)
            {
                AtcAp_Free(event);
            }
        }
    }
}
#include "diag_item_struct.h"
#define p_diag_filter_info_1              ((FilterInfo *)(DIAG_FILTER_BITMAP))
void close_phy_log(void)
{
    #if (DIAG_MASTER_CONTROL_CORE == 1)
    {
        p_diag_filter_info_1->log_lev_bitmap = 0xFFFF;
        p_diag_filter_info_1->sig_pri_bitmap = 0xFFFF;
        p_diag_filter_info_1->src_id_bitmap = 0xFFFEFB1F;
        DIAG_CACHE_CLEAN(p_diag_filter_info_1, sizeof(FilterInfo));
    }
    #endif  /* DIAG_MASTER_CONTROL_CORE == 1 */
}

void PS_TEST_TASK_INIT()
{
    osThreadAttr_t thread_attr1 = {0};
    osThreadAttr_t thread_attr2 = {0};
    osTimerAttr_t timer_attr1 = {0};
    osTimerAttr_t timer_attr2 = {0};
    
    if(g_softap_fac_nv->ucPSTestDemoFlg != 1)
    {
        return;
    }

    AtcAp_MemSet(&g_PSTestDemoInfo, 0, sizeof(ST_PS_TEST_DEMO_INFO));
    AtcAp_MemSet(&g_PSTestSDInfo, 0, sizeof(ST_PS_TEST_SAVE_SD_INFO));

    g_PSTestDemoInfo.stTestShareInfo.Mutex = osMutexNew(NULL);
    g_PSTestSDInfo.stTestQuestInfo.msgMutex = osMutexNew(NULL);
    
    g_PSTestDemoInfo.stTestSendMsgInfo.msgSemaPhore = osSemaphoreNew(0xFFFF, 0, NULL);
    g_PSTestDemoInfo.stTestSendMsgInfo.msgMutex = osMutexNew(NULL);
    
    g_PSTestDemoInfo.stTestReceiveMsgInfo.msgSemaPhore = osSemaphoreNew(0xFFFF, 0, NULL);
    g_PSTestDemoInfo.stTestReceiveMsgInfo.msgMutex = osMutexNew(NULL);

    g_PSTestDemoInfo.ATSemaPhore = osSemaphoreNew(0xFFFF, 0, NULL);
    
    g_PSTestDemoInfo.stTestWaitAtKey.mutex = osMutexNew(NULL);
    g_PSTestDemoInfo.stTestMSGRcv.mutex = osMutexNew(NULL);
    thread_attr1.name       = "PS_TEST_AP";
    thread_attr1.priority   = D_THEARD_TEST_CMD_PRIO;
    thread_attr1.stack_size = D_THEARD_TEST_CMD_STACK;
    osThreadNew((osThreadFunc_t)(PS_Test_TaskEntry), NULL, &thread_attr1);

    thread_attr2.name       = "PS_TEST_RECEIVE_HANDLE";
    thread_attr2.priority   = D_THEARD_TEST_RECEIVE_PRIO;
    thread_attr2.stack_size = D_THEARD_TEST_RECEIVE_STACK;
    osThreadNew((osThreadFunc_t)(PS_Test_Receive_TaskEntry), NULL, &thread_attr2);

    timer_attr1.name = "wait_case_sucess";
    g_PSTestDemoInfo.tTestCaseTime = osTimerNew((osTimerFunc_t)(ps_test_case_timer_callback), osTimerOnce, NULL, &timer_attr1);
    xy_assert(g_PSTestDemoInfo.tTestCaseTime != NULL);

    timer_attr2.name = "wait_at_rsp";
    g_PSTestDemoInfo.tTestAtTime = osTimerNew((osTimerFunc_t)(ps_test_at_timer_callback), osTimerOnce, NULL, &timer_attr2);
    xy_assert(g_PSTestDemoInfo.tTestAtTime != NULL);

    if(g_softap_fac_nv->ucPSTestStartFlg == 1)
    {
        close_phy_log();
    }
}

#endif
