#pragma once

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

/*******************************************************************************
 *                             Type definitions                                *
 ******************************************************************************/
/* PS通道核间信息结构体 */
typedef struct
{
    uint32_t     len;
    uint32_t     buf;
} icc_ps_t;

/*******************************************************************************
 *                       Global function declarations                          *
 ******************************************************************************/
/**
 * @brief 平台实现，PS调用，发送PS相关的URC及结果码
 */
void SendAtInd2User(char *pAt, unsigned int ulAtLen, int srcFd);

/**
 * @brief PS实现，平台调用，发送从串口接收来的PS相关AT命令给ATC
 */
void SendAt2AtcAp(char *pAt, unsigned int ulAtLen, int srcFd);

/**
 * @brief PS实现，平台调用，用于发送从DSP接收到的核间消息给ATC
 */
void SendShmMsg2AtcAp(char *pBuf, size_t BufLen);

/**
 * @brief 由PS调用，透传PS的跨核消息给对方核，内部使用ICM_PS_SHM_MSG
 */
int send_ps_shm_msg(void *msg, int msg_len);

bool at_send_to_ps_test(void *buf, size_t size);

 bool at_recv_from_ps_test(char *data, size_t size);

