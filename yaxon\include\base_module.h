/**********************************************************************/
/**
 * @file base_module.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-02-25
 * @version V1.0
 * @brief 模块基本结构，每个模块都应该用此结构进行申明和注册
 **********************************************************************/
#ifndef BASE_MODULE_H
#define BASE_MODULE_H

#include "yx_type.h"

/**
 * @brief 模块类型
 *
 * @ingroup cm_fw
 */
typedef enum
{
    MODULE_TYPE_APP = 0x00,  /**< 业务模块 */
    MODULE_TYPE_FUNC = 0x01, /**< 功能封装模块 */
} module_tpye_t;

typedef INT8U module_id_t; /**< 模块ID类型 */

/**
 * @brief 模块基本数据结构
 *
 * @ingroup cm_fw
 */
typedef struct
{
    module_id_t id;        /**< 模块ID */
    INT32U ver;            /**< 模块版本，如：0x010000，即：（1.0.0）主.副.补丁 */
    INT32S (*init)(VOID);  /**< 初始化函数 */
    INT32S (*start)(VOID); /**< 启动函数 */
    VOID (*uninit)(VOID);  /**< 卸载函数 */
} base_module_t;

/** 模块声明宏 */
#define DECLARE_MODULE(name) extern const base_module_t name##_module

/** 模块创建宏 */
#define CREATE_MODULE(name, mod_id, ver_num, init_func, start_func, uninit_func) \
    const base_module_t name##_module = { .id = mod_id, .ver = ver_num, .init = init_func, .start = start_func, .uninit = uninit_func }

/** 模块注册宏 */
#define REG_APP_MODULE(name) yx_fw_reg_module(MODULE_TYPE_APP, &name##_module)
#define REG_FUNC_MODULE(name) yx_fw_reg_module(MODULE_TYPE_FUNC, &name##_module)

/**
 * @brief 注册单个模块
 *
 * @param module_tpye 模块类型
 * @param module 模块对象指针
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 * @ingroup cm_fw
 */
INT32S yx_fw_reg_module(module_tpye_t module_tpye, const base_module_t* module);

/**
 * @brief 遍历执行所有模块的卸载
 *
 * @return VOID
 * @ingroup cm_fw
 */
VOID yx_fw_uninit(VOID);

#endif