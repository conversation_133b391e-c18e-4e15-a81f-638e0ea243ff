/**
 * @file at_error.c
 * @brief 基础平台拓展AT命令错误码
 * @version 1.0
 * @date 2023-04-20
 * @copyright Copyright (c) 2023  芯翼信息科技有限公司
 * 
 */

#include "at_error.h"
#include "at_com.h"
#include "at_context.h"
#include "at_ctl.h"
#include "factory_nv.h"
#include "net_adapt.h"
#include "xy_system.h"

/*******************************************************************************
 *						Global function implementations 					   *
 ******************************************************************************/
char* get_plat_err_info(int err_no)
{
    switch (err_no)
    {
    case XY_OK:
        return "Operation success";
    case XY_Err_DropMore:
        return "AT data recv err";
    case XY_Err_Netif_NotReady:
        return "Netif is not ready";
    case XY_Err_Prefix:
        return "AT prefix err";
    case XY_Err_InProgress:
        return "Operation busy";
    case XY_Err_Timeout:
        return "Operation timeout";
    case XY_Err_NotAllowed:
        return "Operation not allowed";
    case XY_Err_NotSupport:
        return "Operation not supported";
    case XY_Err_FlowCtrl:
        return "Ps upload data control";
    case XY_Err_OOS:
        return "Ps in out of service";
    case XY_Err_NoMemory:
        return "Memory not enough";
    case XY_Err_Parameter:
        return "Invalid parameters";
    case XY_Err_SockCreate:
        return "Socket creation failed";
    case XY_Err_SockBind:
        return "Socket bind failed";
    case XY_Err_SockConnect:
        return "Socket connect failed";
    case XY_Err_SockListen:
        return "Socket listen failed";
    case XY_Err_SockAccept:
        return "Socket accept failed";
    case XY_Err_SockWrite:
        return "Socket write failed";
    case XY_Err_SockRead:
        return "Socket read failed";
    case XY_Err_SockInuse:
        return "Socket in used";
    case XY_Err_SockClosed:
        return "Socket has been closed";
    case XY_Err_DnsFail:
        return "DNS parse failed";
    case XY_Err_DnsBusy:
        return "DNS busy";
    case XY_Err_PdpOpen:
        return "PDP context opening failed";
    case XY_Err_PdpBroken:
        return "PDP context broken down";
    default:
        return "Unknown error";
    }
}

extern unsigned char api_GetCmeeValue();
char *at_plat_err_build(int err_no, bool bCme, char *file, int line)
{
	char *at_str = xy_malloc(64);

    if (err_no == XY_OK)
    {
        snprintf(at_str, 64, AT_RSP_OK);
        return at_str;
    }

	if(file != NULL)
	{
	    ATCTRL_NET_LOG("AT err:%d,line:%d,file:%s", err_no, line, file);               
	    xy_printf(0, PLATFORM_AP, WARN_LOG, "AT err:%d,line:%d,file:%s", err_no, line, file);
		debug_log_print("[at_plat_err_build]err:%d,line:%d,file:%s\n", err_no, line, file);
	}

    if (api_GetCmeeValue() == 0 || !bCme)
    {
        //只显示ERROR
        snprintf(at_str, 64, AT_RSP_ERR);
    }
    else if (api_GetCmeeValue() == 1)
    {
        //显示ERROR Code
        snprintf(at_str, 64, "\r\n+CME ERROR: %d\r\n", err_no);
    }
    else if (api_GetCmeeValue() == 2)
    {
#if (FLASH_LIMIT != 1)
        //显示ERROR详细提示
        snprintf(at_str, 64, "\r\n+CME ERROR: %s\r\n", get_plat_err_info(err_no));
#else
        //显示ERROR Code
        snprintf(at_str, 64, "\r\n+CME ERROR: %d\r\n", err_no);   
#endif /* FLASH_LIMIT */
    }
	return at_str;
}

char *atTcpipErrorBuild(int err_no, char *file, uint32_t line)
{
	char *outStr = xy_malloc(32);
    set_at_tcpip_err(err_no);
    if (err_no == XY_OK)
    {
        snprintf(outStr, 32, AT_RSP_OK);
    }
    else
	{
		// 只显示ERROR
		snprintf(outStr, 32, AT_RSP_ERR);
        if (file != NULL)
        {
			xy_printf(0, PLATFORM_AP, WARN_LOG, "atTcpipErrorBuild line:%d,file:%s,err:%d\n", line, file, err_no);
        }
  	}

	return outStr;
}

void send_err_rsp_2_tty(int err_no, int srcFd, char *file, int line)
{
    if (g_softap_fac_nv->at_err_report == 1)
    {
        char *err_str = at_plat_err_build(err_no, true, file, line);
        at_posix_write(srcFd, err_str, strlen(err_str));
        xy_free(err_str);
    }
}