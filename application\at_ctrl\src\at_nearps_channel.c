/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "at_ctl.h"
#include "xy_at_api.h"
#include "csi_device.h"
#include "icc_msg.h"
#include "xy_system.h"

/*******************************************************************************
 *						Global function implementations						   *
 ******************************************************************************/
/*非PS的CP核AT命令，通常为RF命令*/
void at_recv_from_CP(void *buf, size_t len)
{
    xy_assert(len == sizeof(icc_at_t));
    icc_at_t *msg = (icc_at_t *)buf;
    /* 保证at数据起始地址cache 64字节对齐 */
    xy_assert(msg->buf % 64 == 0);

    char *data = (char*)(size_t)(msg->buf);
    csi_dcache_invalid_range(data, msg->len);
	
	xy_assert(IS_CP_MEM(data));
    xy_assert(strlen(data) != 0 && msg->len == strlen(data) + 1);

    if (msg->len <= AT_DEBUG_LOG_MAX_LEN)
    {
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_recv_from_CP] recv %s from cp to tty(%d)", data, msg->ttyFd);
		debug_log_print("[at_recv_from_CP] recv %s from cp to tty(%d)\n", data, msg->ttyFd);
    }
    else
    {
        char at_rsp[32] = {0};
        memcpy(at_rsp, data, 31);
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_recv_from_CP] recv long %s len %d from cp to tty(%d)", at_rsp, msg->len, msg->ttyFd);
		debug_log_print("[at_recv_from_CP] recv long %s len %d from cp to tty(%d)\n", at_rsp, msg->len, msg->ttyFd);
    }
    
    /* 不包含末尾'\0'的长度 */
    if (msg->ttyFd != AT_FD_INVAILD)
    {
        send_msg_2_atctl(AT_MSG_RCV_STR_FROM_NEARPS, data, (int)(msg->len - 1), msg->ttyFd);
    }
    else
    {
        sysAtURC(data, msg->len);
    }

    /* 零拷贝:通知CP核释放内存 */
    icc_at_channel_write(ICC_AT_FREE, (void *)&data, sizeof(msg->buf));
}

/*非PS的CP核AT命令，通常为RF命令*/
int at_send_to_CP(void *buf, size_t size, int ttyFd)
{
    xy_assert(buf != NULL && strlen(buf) == size);
    int result = XY_OK;
    icc_at_t msg = {0};

    char *at_cmd = (char *)xy_malloc_align(size + 1);
	
    memcpy(at_cmd, buf, size);
    *(at_cmd + size) = '\0';
    msg.buf = (uint32_t)(size_t)at_cmd;
    msg.len = (uint32_t)(size + 1);
    msg.ttyFd = ttyFd;

    /* cache writeback 将数据从cache缓存写入ram */
    csi_dcache_clean_range(at_cmd, size + 1);

    icc_at_channel_write(ICC_AT_CMD, &msg, sizeof(icc_at_t));

    if (size <= AT_DEBUG_LOG_MAX_LEN)
    {
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_send_to_CP] send %s to cp from tty(%d)", (char *)buf, ttyFd);
		debug_log_print("[at_send_to_CP] send %s to cp from tty(%d)\n", (char *)buf, ttyFd);        
    }
    else
    {
        char at_req[32] = {0};
        memcpy(at_req, buf, 31);
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_send_to_CP] send long %s len %d to cp from tty(%d)", at_req, size, ttyFd);
		debug_log_print("[at_send_to_CP] send long %s len %d to cp from tty(%d)\n", at_req, size, ttyFd); 
    }

    return result;
}
