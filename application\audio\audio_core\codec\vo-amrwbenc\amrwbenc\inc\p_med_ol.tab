
/*
 ** Copyright 2003-2010, VisualOn, Inc.
 **
 ** Licensed under the Apache License, Version 2.0 (the "License");
 ** you may not use this file except in compliance with the License.
 ** You may obtain a copy of the License at
 **
 **     http://www.apache.org/licenses/LICENSE-2.0
 **
 ** Unless required by applicable law or agreed to in writing, software
 ** distributed under the License is distributed on an "AS IS" BASIS,
 ** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 ** See the License for the specific language governing permissions and
 ** limitations under the License.
 */


/*-----------------------------------------------------*
 | Table for function Pitch_med_ol()				   |
 *-----------------------------------------------------*/

 static Word16 corrweight[199]= {

 10772, 10794, 10816, 10839, 10862, 10885, 10908, 10932, 10955, 10980,
 11004, 11029, 11054, 11079, 11105, 11131, 11157, 11183, 11210, 11238,
 11265, 11293, 11322, 11350, 11379, 11409, 11439, 11469, 11500, 11531,
 11563, 11595, 11628, 11661, 11694, 11728, 11763, 11798, 11834, 11870,
 11907, 11945, 11983, 12022, 12061, 12101, 12142, 12184, 12226, 12270,
 12314, 12358, 12404, 12451, 12498, 12547, 12596, 12647, 12699, 12751,
 12805, 12861, 12917, 12975, 13034, 13095, 13157, 13221, 13286, 13353,
 13422, 13493, 13566, 13641, 13719, 13798, 13880, 13965, 14053, 14143,
 14237, 14334, 14435, 14539, 14648, 14761, 14879, 15002, 15130, 15265,
 15406, 15554, 15710, 15874, 16056, 16384, 16384, 16384, 16384, 16384,
 16384, 16384, 16056, 15874, 15710, 15554, 15406, 15265, 15130, 15002,
 14879, 14761, 14648, 14539, 14435, 14334, 14237, 14143, 14053, 13965,
 13880, 13798, 13719, 13641, 13566, 13493, 13422, 13353, 13286, 13221,
 13157, 13095, 13034, 12975, 12917, 12861, 12805, 12751, 12699, 12647,
 12596, 12547, 12498, 12451, 12404, 12358, 12314, 12270, 12226, 12184,
 12142, 12101, 12061, 12022, 11983, 11945, 11907, 11870, 11834, 11798,
 11763, 11728, 11694, 11661, 11628, 11595, 11563, 11531, 11500, 11469,
 11439, 11409, 11379, 11350, 11322, 11293, 11265, 11238, 11210, 11183,
 11157, 11131, 11105, 11079, 11054, 11029, 11004, 10980, 10955, 10932,
 10908, 10885, 10862, 10839, 10816, 10794, 10772, 10750, 10728};



