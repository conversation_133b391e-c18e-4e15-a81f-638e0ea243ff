/**********************************************************************/
/**
 * @file yx_ia_system.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-02-25
 * @version V1.0
 * @brief 系统控制接口适配
 **********************************************************************/
#ifndef YX_IA_SYSTEM_H
#define YX_IA_SYSTEM_H

#include "yx_type.h"

#define YX_IA_SYS_TIMER_TICK_MS  (1)  /**< 定时器周期 */

typedef VOID    *yx_task_ref_t;     /**< 任务指针 */
typedef VOID    *yx_timer_ref_t;    /**< 定时器指针 */
typedef VOID    *yx_mutex_ref_t;    /**< 互斥锁指针 */
typedef VOID    *yx_sem_ref_t;      /**< 互斥锁指针 */
typedef VOID    *yx_mq_ref_t;       /**< 消息队列指针 */

/**
 * @brief 任务优先级 数值越小优先级越高
 * @ingroup ia_sys
*/
typedef enum {
    YX_TSK_PRIORITY_TIMER = 0 , //定时器任务优先级最高
    YX_TSK_PRIORITY_MAIN,       //主任务优先级,其他业务不可主任务高
    YX_TSK_PRIORITY_HIGH,       //业务高优先级
    YX_TSK_PRIORITY_MID,        //业务中优先级
    YX_TSK_PRIORITY_LOW,        //业务低优先级
} YX_TSk_PRIORITY_E;

/**
 * @brief 雅迅日期时间数据结构体
 * @ingroup ia_sys
 */
typedef struct {
    INT8U year;     /**< 年 [从2000年起]*/
    INT8U month;    /**< 月 [1,12]*/
    INT8U day;      /**< 日 [1,31]*/
    INT8U hour;     /**< 时 [0,23]*/
    INT8U minute;   /**< 分 [0,59]*/
    INT8U second;   /**< 秒 [0,59]*/
    INT8U week;     /**< 星期 [星期天=0]*/
} yx_ia_sys_time_t;

#define YA_IA_SYS_BLOCK         (-1)
#define YA_IA_SYS_NO_BLOCK      (0)

/**
 * @brief 获取平台名称
 * @ingroup ia_sys
 * @return char * 返回平台名称
 */
CHAR *yx_ia_sys_get_platname(void);

/**
 * @brief 获取固件版本
 * @ingroup ia_sys
 * @return char * 返回固件版本
 * @retval NULL 失败
 * @retval 非NULL 成功，返回固件版本
 */
CHAR *yx_ia_sys_get_firmware(void);

/**
 * @brief 获取sdk版本
 * @ingroup ia_sys
 * @return char * 返回固件版本
 * @retval NULL 失败
 * @retval 非NULL 成功，获取sdk版本
 */
CHAR *yx_ia_sys_get_sdkver(void);

/**
 * @brief 获取平台信息
 * @ingroup ia_sys
 * @return char * 获取平台信息
 * @retval NULL 失败
 * @retval 非NULL 成功，返回平台信息
 */
CHAR *yx_ia_sys_get_modelstr(void);


/**
 * @brief RTOS任务创建
 * @ingroup ia_sys
 * @param[out] task_ref_ptr 返回任务对象指针
 * @param[in] stack_addr 栈地址
 * @param[in] stack_size 栈大小
 * @param[in] priority 优先级
 * @param[in] task_name 任务名称
 * @param[in] task_run 任务执行函数
 * @param[in] param 任务执行函数参数
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_task_create(yx_task_ref_t *task_ref_ptr, VOID *stack_addr,
    INT32U stack_size, INT8U priority, CHAR *task_name,
    VOID (*task_run)(VOID *), VOID *param);

/**
 * @brief RTOS任务删除
 * @ingroup ia_sys
 * @param[in] task_ref 任务对象指针
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_task_delete(yx_task_ref_t task_ref);

/**
 * @brief RTOS任务挂起
 * @ingroup ia_sys
 * @param[in] task_ref 任务对象指针
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_task_suspend(yx_task_ref_t task_ref);

/**
 * @brief RTOS任务恢复
 * @ingroup ia_sys
 * @param[in] task_ref 任务对象指针
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_task_resume(yx_task_ref_t task_ref);

/**
 * @brief RTOS任务休眠
 * @ingroup ia_sys
 * @param[in] ticks 滴答数（一个滴答5ms）
 * @return VOID
 */
VOID yx_ia_sys_task_sleep(INT32U ticks);

/**
 * @brief RTOS任务的优先级设置
 * @ingroup ia_sys
 * @param[in] task_ref 任务对象指针
 * @param[in] new_priority 新的优先级
 * @param[out] old_priority 之前的优先级
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_task_set_priority(yx_task_ref_t task_ref, INT8U new_priority,
    INT8U *old_priority);

/**
 * @brief RTOS任务当前任务对象指针获取
 * @ingroup ia_sys
 * @param[out] task_ref_ptr 返回任务对象指针
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_task_get_cur_ref(yx_task_ref_t *task_ref_ptr);

/**
 * @brief 获取当前RTOS任务的任务对象指针
 * @ingroup ia_sys
 * @param[in] task_ref 任务对象指针
 * @param[out] param 返回创建任务时传入的参数
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_task_get_entry_param(yx_task_ref_t task_ref, void **param);

/**
 * @brief RTOS定时器创建
 * @ingroup ia_sys
 * @param[out] timer_ref_ptr 返回定时器指针
 * @param[in] timer_task 定时器任务处理函数
 * @param[in] param 定时器任务处理函数参数
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_timer_create(yx_timer_ref_t *timer_ref_ptr,
    VOID (*timer_task)(void *), void *param);

/**
 * @brief RTOS定时器删除
 * @ingroup ia_sys
 * @param[in] timer_ref 定时器指针
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_timer_delete(yx_timer_ref_t timer_ref);

/**
 * @brief RTOS定时器启动
 * @ingroup ia_sys
 * @param[in] timer_ref 定时器指针
 * @param[in] init 初始定时器时间
 * @param[in] reschedule 定时器的循环触发时间
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_timer_start(yx_timer_ref_t timer_ref, INT32U init,
    INT32U reschedule);

/**
 * @brief RTOS定时器停止
 * @ingroup ia_sys
 * @param[in] timer_ref 定时器指针
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_timer_stop(yx_timer_ref_t timer_ref);

/**
 * @brief RTOS互斥锁创建
 * @ingroup ia_sys
 * @param[out] mutex_ref_ptr 返回互斥锁指针
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_mutex_create(yx_mutex_ref_t *mutex_ref_ptr);

/**
 * @brief RTOS互斥锁删除
 * @ingroup ia_sys
 * @param[in] mutex_ref 互斥锁指针
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_mutex_delete(yx_mutex_ref_t mutex_ref);

/**
 * @brief RTOS互斥锁上锁
 * @ingroup ia_sys
 * @param[in] mutex_ref 互斥锁指针
 * @param[in] timeout 超时时间
 *                  - YA_IA_SYS_NO_BLOCK 为非阻塞，
 *                  - YA_IA_SYS_BLOCK 为阻塞
 *                  - 1 ~ 4,294,967,293为系统节拍
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_mutex_lock(yx_mutex_ref_t mutex_ref, INT32U timeout);

/**
 * @brief RTOS互斥锁解锁
 * @ingroup ia_sys
 * @param[in] mutex_ref 互斥锁指针
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_mutex_unlock(yx_mutex_ref_t mutex_ref);

/**
 * @brief RTOS信号量创建
 * @ingroup ia_sys
 * @param[out] sem_ref_ptr 返回互斥锁指针
 * @param[in] init 信号量初始值
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_sem_create(yx_sem_ref_t *sem_ref_ptr, INT32U init);

/**
 * @brief RTOS信号量创建
 * @ingroup ia_sys
 * @param[out] sem_ref_ptr 返回互斥锁指针
 * @param[in] init 信号量初始值
 * @param[in] max_cnt 信号量最大计数
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_sem_create_ex(yx_sem_ref_t *sem_ref_ptr, INT32U init, INT32U max_cnt);

/**
 * @brief RTOS信号量移除
 * @ingroup ia_sys
 * @param[in] sem_ref 信号量指针
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_sem_delete(yx_sem_ref_t sem_ref);

/**
 * @brief RTOS信号量获取
 * @ingroup ia_sys
 * @param[in] sem_ref 信号量指针
 * @param[in] timeout 超时时间
 *                  - YA_IA_SYS_NO_BLOCK 为非阻塞，
 *                  - YA_IA_SYS_BLOCK 为阻塞
 *                  - 1 ~ 4,294,967,293为系统节拍
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_sem_acquire(yx_sem_ref_t sem_ref, INT32U timeout);

/**
 * @brief RTOS信号量释放
 * @ingroup ia_sys
 * @param[in] sem_ref 信号量指针
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_sem_release(yx_sem_ref_t sem_ref);

/**
 * @brief 消息队列创建
 * @ingroup ia_sys
 * @param[out] mq_ref_ptr 返回消息队列指针
 * @param[in] name 消息队列名称
 * @param[in] msg_size 单个消息的大小
 * @param[in] q_size 消息队列消息数量
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 * @note simcom的消息队列，在消息创建时即申请固定内存大小，在发送和接收时，无法校验
 *       所传指针参数的合法性，需开发者自己把握合法性
 */
INT32S yx_ia_sys_mq_create(yx_mq_ref_t *mq_ref_ptr, CHAR *name, INT32U msg_size,
    INT32U q_size);

/**
 * @brief 消息队列删除
 * @ingroup ia_sys
 * @param[in] mq_ref 消息队列指针
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_mq_delete(yx_mq_ref_t mq_ref);

/**
 * @brief 消息队列接收消息
 * @ingroup ia_sys
 * @param[in] mq_ref 消息队列指针
 * @param[in] msg_buf 消息缓冲区
 * @param[in] size 消息缓冲区大小
 * @param[in] timeout 超时时间
 *                  - YA_IA_SYS_NO_BLOCK 为非阻塞，
 *                  - YA_IA_SYS_BLOCK 为阻塞
 *                  - 1 ~ 4,294,967,293为系统节拍
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 * @note simcom的消息队列，在消息创建时即申请固定内存大小，在发送和接收时，无法校验
 *       所传指针参数的合法性，需开发者自己把握合法性
 */
INT32S yx_ia_sys_mq_recv(yx_mq_ref_t mq_ref, void *msg_buf, INT32U size,
    INT32U timeout);

/**
 * @brief 消息队列发送消息
 * @ingroup ia_sys
 * @param[in] mq_ref 消息队列指针
 * @param[in] msg_buf 消息缓冲区
 * @param[in] size 消息缓冲区大小(此参数暂时无用)
 * @param[in] timeout 超时时间
 *                  - YA_IA_SYS_NO_BLOCK 为非阻塞，
 *                  - YA_IA_SYS_BLOCK 为阻塞
 *                  - 1 ~ 4,294,967,293为系统节拍
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 * @note simcom的消息队列，在消息创建时即申请固定内存大小，在发送和接收时，无法校验
 *       所传指针参数的合法性，需开发者自己把握合法性
 */
INT32S yx_ia_sys_mq_send(yx_mq_ref_t mq_ref, void *msg_buf, INT32U size,
    INT32U timeout);

/**
 * @brief 消息队列获取消息数量
 * @ingroup ia_sys
 * @param[in] mq_ref 消息队列指针
 * @return INT32S
 * @retval >= 0 返回消息数量
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_mq_poll(yx_mq_ref_t mq_ref);

/**
 * @brief 获取ntp时间
 * @ingroup ia_sys
 * @param[out] time 返回雅迅日期时间数据结构体
 * @return INT32S
 * @retval >= 0 返回消息数量
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_ntptime(yx_ia_sys_time_t *time);

/**
 * @brief 获取rtc时间
 * @ingroup ia_sys
 * @param[out] time 返回雅迅日期时间数据结构体
 * @return INT32S
 * @retval >= 0 返回消息数量
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_rtctime(yx_ia_sys_time_t *time);

/**
 * @brief 设置rtc时间
 * @ingroup ia_sys
 * @param[in] time 雅迅日期时间数据结构体
 * @return INT32S
 * @retval >= 0 返回消息数量
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_sys_set_rtctime(yx_ia_sys_time_t *time);

/**
 * @brief 获取系统运行时间
 * @ingroup ia_sys
 * @return INT64U 系统运行时间，单位us
 */
INT64U yx_ia_sys_runtime(void);

/**
 * @brief 启用dump功能
 * @ingroup ia_sys
 * @return VOID
 */
VOID yx_ia_sys_enable_dump(VOID);

/**
 * @brief 调试接口
 * @ingroup ia_sys
 * @param[in] format 日志输出格式
 * @param[in] 参数列表
 * @note 函数原型应该为
 *      INT32S yx_ia_sys_debug(const char *format, ...);
 *      单数变长度参数的封装比较麻烦，故采用宏定义，为了不暴露reach的头文件，故额外
 *      声明ql_prf
 */
#define yx_ia_sys_debug(fmt, ...) ql_prf(fmt, ##__VA_ARGS__)
extern void ql_prf(char *format, ...);

/**
 * @brief 动态分配指定大小的内存
 *
 * 使用malloc函数动态分配指定大小的内存，并返回指针。
 * @ingroup ia_sys
 * @param size 需要分配的内存大小（以字节为单位）
 * @return 成功时返回指向分配内存的指针，失败时返回NULL
 * @note 若size为0，则直接返回NULL，避免传入0大小的内存请求
 */
void* yx_ia_sys_malloc(INT32U size);

/**
 * @brief 释放动态分配的内存
 *
 * 释放由 malloc、calloc 或 realloc 函数分配的内存。
 * @ingroup ia_sys
 * @param ptr 需要释放的内存指针
 */
void yx_ia_sys_free(void* ptr);
#endif /* YX_IA_SYSTEM_H */