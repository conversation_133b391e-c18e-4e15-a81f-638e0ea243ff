#include "cmsis_os2.h"
#include "xy_system.h"
#include "audio_play.h"

#if (MP3_DECODER_TYPE == 1)

#include "minimp3_ex.h"

#define MP3_MAX_SAMPLES_PER_FRAME (1152 * 2)

static audio_sink_handle_t * sink_handle = NULL;

typedef struct
{
    mp3dec_t *mp3d;
    play_state_get_t state_get;
    audio_sink_t *sink;
    mp3d_sample_t * pcm;
    osSemaphoreId_t *sem;
} frames_iterate_data_t;

static int frames_iterate_cb(void *user_data, const uint8_t *frame, int frame_size, int free_format_bytes, size_t buf_size, uint64_t offset, mp3dec_frame_info_t *info)
{
    (void)buf_size;
    (void)offset;
    (void)free_format_bytes;

    audio_play_state_t state;
    frames_iterate_data_t *d = user_data;

    int samples = mp3dec_decode_frame(d->mp3d, frame, frame_size, d->pcm, info);

    state = d->state_get();
    if (state == AUDIO_PLAY_STATE_STOP)
    {    
        return AUDIO_ERR_STOP;
    }
    else if (state == AUDIO_PLAY_STATE_PAUSE)
    {
        osSemaphoreAcquire(*(d->sem), osWaitForever);
    }

    if (samples)
    {
        if (sink_handle == NULL)
        {
            sink_handle = d->sink->open(SINK_MODE_OUT, info->hz, info->channels, 16);
            if (sink_handle == NULL)
            {
                return AUDIO_ERR_SINK_OPEN;
            }
            d->sink->start(sink_handle);
        }

        d->sink->write(sink_handle, (uint8_t *)(d->pcm), samples*sizeof(mp3d_sample_t)*info->channels);
    }

    return 0;
}

int audio_decode_mp3_process(audio_play_t *audio_play)
{
    audio_error_t ret = AUDIO_ERR_OK;
    frames_iterate_data_t user_data;
    mp3dec_io_t io;
    uint8_t * io_buf = NULL;

    io.read = (MP3D_READ_CB)(audio_play->source->read);
    io.seek = (MP3D_SEEK_CB)(audio_play->source->seek);
    io.read_data = io.seek_data = audio_play->source->source_arg;

    user_data.mp3d = (mp3dec_t *)xy_malloc(sizeof(mp3dec_t));
    if(user_data.mp3d == NULL)
    {
        ret = AUDIO_ERR_MEMORY;
        goto exit;
    }
    mp3dec_init(user_data.mp3d);

    user_data.pcm = (mp3d_sample_t *)xy_malloc(MP3_MAX_SAMPLES_PER_FRAME * sizeof(mp3d_sample_t));
    if(user_data.pcm == NULL)
    {
        ret = AUDIO_ERR_MEMORY;
        goto exit;
    }

    user_data.state_get = audio_play->state_get;
    user_data.sink = audio_play->sink;
    user_data.sem = audio_play->sem;

    io_buf = xy_malloc(MINIMP3_BUF_SIZE);
    if(io_buf == NULL)
    {
        ret = AUDIO_ERR_MEMORY;
        goto exit;
    }

    ret = mp3dec_detect_cb(&io, io_buf, MINIMP3_BUF_SIZE);
    io.seek(0, io.seek_data);
    if(ret != 0)
    {
        ret = AUDIO_ERR_FORMAT;
        goto exit;
    }

    ret = mp3dec_iterate_cb(&io, io_buf, MINIMP3_BUF_SIZE, frames_iterate_cb, &user_data);

exit:
    if(user_data.mp3d)
    {
        xy_free(user_data.mp3d);
    }

    if(user_data.pcm)
    {
        xy_free(user_data.pcm);
    }

    if(io_buf)
    {
        xy_free(io_buf);
    }

    if(sink_handle != NULL)
    {
        audio_play->sink->stop(sink_handle);
        audio_play->sink->close(sink_handle);
        sink_handle = NULL;
    }

    return ret;
}

#elif (MP3_DECODER_TYPE == 2)
#include "mad.h"

#define MP3_INPUT_BUFFER_SIZE (1024)
#define MP3_MAX_SAMPLES_PER_FRAME (1152 * 2)

static audio_sink_handle_t * sink_handle = NULL;

typedef struct
{
    audio_play_t * audio_play;
    uint8_t * input_buf;
    uint16_t * pcm;
    enum mad_error error;
} frames_iterate_data_t;


/*
 * This is the input callback. The purpose of this callback is to (re)fill
 * the stream buffer which is to be decoded. In this example, an entire file
 * has been mapped into memory, so we just call mad_stream_buffer() with the
 * address and length of the mapping. When this callback is called a second
 * time, we are finished decoding.
 */

static
enum mad_flow input(void *data,
		    struct mad_stream *stream)
{
    frames_iterate_data_t * d = (frames_iterate_data_t *)data;
    int ret;
    int rest_len;   // unprocessed data's size
    int read_len;
    int readn;

    rest_len = stream->bufend - stream->next_frame;
    read_len = MP3_INPUT_BUFFER_SIZE - rest_len;
    if(rest_len > 0)
    {
        memcpy(d->input_buf, d->input_buf + read_len, rest_len);
    }

    readn = d->audio_play->source->read(d->input_buf + rest_len, read_len, d->audio_play->source->source_arg);
    if(readn > 0)
    {
        mad_stream_buffer(stream, d->input_buf, rest_len + readn);
        ret = MAD_FLOW_CONTINUE;
    }
    else 
    {
        ret = MAD_FLOW_STOP;
    }

    return ret;
}

/*
 * The following utility routine performs simple rounding, clipping, and
 * scaling of MAD's high-resolution samples down to 16 bits. It does not
 * perform any dithering or noise shaping, which would be recommended to
 * obtain any exceptional audio quality. It is therefore not recommended to
 * use this routine if high-quality output is desired.
 */

static inline
signed int scale(mad_fixed_t sample)
{
  /* round */
  sample += (1L << (MAD_F_FRACBITS - 16));

  /* clip */
  if (sample >= MAD_F_ONE)
    sample = MAD_F_ONE - 1;
  else if (sample < -MAD_F_ONE)
    sample = -MAD_F_ONE;

  /* quantize */
  return sample >> (MAD_F_FRACBITS + 1 - 16);
}

/*
 * This is the output callback function. It is called after each frame of
 * MPEG audio data has been completely decoded. The purpose of this callback
 * is to output (or play) the decoded PCM audio.
 */

static
enum mad_flow output(void *data,
		     struct mad_header const *header,
		     struct mad_pcm *pcm)
{
    frames_iterate_data_t * d = (frames_iterate_data_t *)data;
    uint16_t nchannels;
    uint16_t nsamples;
    mad_fixed_t const *left_ch, *right_ch;
    uint16_t * sample;
    audio_play_state_t state;

    nchannels = pcm->channels;
    nsamples  = pcm->length;
    left_ch   = pcm->samples[0];
    right_ch  = pcm->samples[1];

    if (sink_handle == NULL)
    {
        sink_handle = d->audio_play->sink->open(SINK_MODE_OUT, pcm->samplerate, (uint8_t)nchannels, 16);
        if (sink_handle == NULL)
        {
            return MAD_FLOW_BREAK;
        }
        d->audio_play->sink->start(sink_handle);
    }

    sample = d->pcm;

    while (nsamples--) {
        /* output sample(s) in 16-bit signed little-endian PCM */
        *sample = (uint16_t)scale(*left_ch++);
        sample++;

        if (nchannels == 2) {
            *sample = (uint16_t)scale(*right_ch++);
            sample++;
        }
    }

    state = d->audio_play->state_get();
    if (state == AUDIO_PLAY_STATE_STOP)
    {    
        return MAD_FLOW_STOP;
    }
    else if (state == AUDIO_PLAY_STATE_PAUSE)
    {
        osSemaphoreAcquire(*(d->audio_play->sem), osWaitForever);
    }

    d->audio_play->sink->write(sink_handle, (uint8_t *)(d->pcm), (pcm->length) * sizeof(uint16_t) * nchannels);

    return MAD_FLOW_CONTINUE;
}

/*
 * This is the error callback function. It is called whenever a decoding
 * error occurs. The error is indicated by stream->error; the list of
 * possible MAD_ERROR_* errors can be found in the mad.h (or mad_stream.h)
 * header file.
 */

static
enum mad_flow error(void *data,
		    struct mad_stream *stream,
		    struct mad_frame *frame)
{
    frames_iterate_data_t * d = (frames_iterate_data_t *)data;

    d->error = stream->error;
    if(stream->error >= MAD_ERROR_BUFPTR)
    {
        xy_printf(0, PLATFORM_AP, WARN_LOG, "mad decoding error 0x%x", stream->error);
        return MAD_FLOW_BREAK;
    }

    /* return MAD_FLOW_BREAK here to stop decoding (and propagate an error) */
    return MAD_FLOW_CONTINUE;
}

int audio_decode_mp3_process(audio_play_t *audio_play)
{
    audio_error_t ret = AUDIO_ERR_OK;
    frames_iterate_data_t user_data;
    struct mad_decoder decoder;

    user_data.error = MAD_ERROR_NONE;
    user_data.audio_play = audio_play;
    user_data.pcm = (uint16_t *)xy_malloc(MP3_MAX_SAMPLES_PER_FRAME * sizeof(uint16_t));
    if(user_data.pcm == NULL)
    {
        ret = AUDIO_ERR_MEMORY;
        goto exit;
    }
    user_data.input_buf = (uint8_t *)xy_malloc(MP3_INPUT_BUFFER_SIZE);
    if(user_data.input_buf == NULL)
    {
        ret = AUDIO_ERR_MEMORY;
        goto exit;
    }

    /* configure input, output, and error functions */
    mad_decoder_init(&decoder, &user_data,
            input, 0 /* header */, 0 /* filter */, output,
            error, 0 /* message */);

    /* start decoding */
    mad_decoder_run(&decoder, MAD_DECODER_MODE_SYNC);

    if(sink_handle == NULL)
    {
        ret = AUDIO_ERR_FORMAT;
        audio_play->source->seek(0, audio_play->source->source_arg);
    }

    /* release the decoder */
    mad_decoder_finish(&decoder);

exit:

    if(user_data.pcm)
    {
        xy_free(user_data.pcm);
    }

    if(user_data.input_buf)
    {
        xy_free(user_data.input_buf);
    }

    if(sink_handle != NULL)
    {
        audio_play->sink->stop(sink_handle);
        audio_play->sink->close(sink_handle);
        sink_handle = NULL;
    }

    return ret;
}

#endif