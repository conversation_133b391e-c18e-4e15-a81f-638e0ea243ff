/*
 * libmad - MPEG audio decoder library
 * Copyright (C) 2000-2004 Underbit Technologies, Inc.
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * $Id: imdct_s.dat,v 1.8 2004/01/23 09:41:32 rob Exp $
 */

  /*  0 */  {  MAD_F(0x09bd7ca0) /*  0.608761429 */,
	      -MAD_F(0x0ec835e8) /* -0.923879533 */,
	      -MAD_F(0x0216a2a2) /* -0.130526192 */,
	       MAD_F(0x0fdcf549) /*  0.991444861 */,
	      -MAD_F(0x061f78aa) /* -0.382683432 */,
	      -MAD_F(0x0cb19346) /* -0.793353340 */ },

  /*  6 */  { -MAD_F(0x0cb19346) /* -0.793353340 */,
	       MAD_F(0x061f78aa) /*  0.382683432 */,
	       MAD_F(0x0fdcf549) /*  0.991444861 */,
	       MAD_F(0x0216a2a2) /*  0.130526192 */,
	      -MAD_F(0x0ec835e8) /* -0.923879533 */,
	      -MAD_F(0x09bd7ca0) /* -0.608761429 */ },

  /*  1 */  {  MAD_F(0x061f78aa) /*  0.382683432 */,
	      -MAD_F(0x0ec835e8) /* -0.923879533 */,
	       MAD_F(0x0ec835e8) /*  0.923879533 */,
	      -MAD_F(0x061f78aa) /* -0.382683432 */,
	      -MAD_F(0x061f78aa) /* -0.382683432 */,
	       MAD_F(0x0ec835e8) /*  0.923879533 */ },

  /*  7 */  { -MAD_F(0x0ec835e8) /* -0.923879533 */,
	      -MAD_F(0x061f78aa) /* -0.382683432 */,
	       MAD_F(0x061f78aa) /*  0.382683432 */,
	       MAD_F(0x0ec835e8) /*  0.923879533 */,
	       MAD_F(0x0ec835e8) /*  0.923879533 */,
	       MAD_F(0x061f78aa) /*  0.382683432 */ },

  /*  2 */  {  MAD_F(0x0216a2a2) /*  0.130526192 */,
	      -MAD_F(0x061f78aa) /* -0.382683432 */,
	       MAD_F(0x09bd7ca0) /*  0.608761429 */,
	      -MAD_F(0x0cb19346) /* -0.793353340 */,
	       MAD_F(0x0ec835e8) /*  0.923879533 */,
	      -MAD_F(0x0fdcf549) /* -0.991444861 */ },

  /*  8 */  { -MAD_F(0x0fdcf549) /* -0.991444861 */,
	      -MAD_F(0x0ec835e8) /* -0.923879533 */,
	      -MAD_F(0x0cb19346) /* -0.793353340 */,
	      -MAD_F(0x09bd7ca0) /* -0.608761429 */,
	      -MAD_F(0x061f78aa) /* -0.382683432 */,
	      -MAD_F(0x0216a2a2) /* -0.130526192 */ }
