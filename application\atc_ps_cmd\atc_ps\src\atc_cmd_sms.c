/*******************************************************************************
@(#)Copyright(C)2016,HuaChang Technology (Dalian) Co,. LTD.
File name   : atc_cmd_sms.c
Description : 
Function List:
History:
1. Dep2_066    2016.12.20  Create
*******************************************************************************/
#include "atc_ps.h"

#ifdef LTE_SMS_FEATURE


unsigned char ATC_CGSMS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Csms = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_SMS_FORMAT, pCommandBuffer, &ptEvent_Csms->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Csms->usEvent = D_ATC_EVENT_CGSMS;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Csms->usEvent = D_ATC_EVENT_CGSMS_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

/*lint -e438*/
/*******************************************************************************
  MODULE    : ATC_CSMS_Command
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  Dep2_066   2016.12.20   create
*******************************************************************************/
unsigned char ATC_CSMS_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Csms = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CSMS_FORMAT, pCommandBuffer, &ptEvent_Csms->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Csms->usEvent = D_ATC_EVENT_CSMS;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Csms->usEvent = D_ATC_EVENT_CSMS_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CMGF_Command
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  Dep2_066   2016.12.20   create
*******************************************************************************/
unsigned char ATC_CMGF_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Cmgf = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CMGF_FORMAT, pCommandBuffer, &ptEvent_Cmgf->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Cmgf->usEvent = D_ATC_EVENT_CMGF;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Cmgf->usEvent = D_ATC_EVENT_CMGF_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CPMS_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CPMS_PARAMETER*  ptEvent_Cpms = (ST_ATC_CPMS_PARAMETER*)pEventBuffer;
    unsigned char*      pucMen1 = NULL;
    unsigned char*      pucMen2 = NULL;
    unsigned char*      pucMen3 = NULL;
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CPMS_FORMAT, pCommandBuffer,
                            NULL, &pucMen1,
                            NULL, &pucMen2,
                            NULL, &pucMen3) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(pucMen1 != NULL)
        {
            ptEvent_Cpms->ucMem1Len = strlen(pucMen1);
            AtcAp_MemCpy(ptEvent_Cpms->aucMem1, pucMen1, ptEvent_Cpms->ucMem1Len);
        }
        if(pucMen2 != NULL)
        {
            ptEvent_Cpms->ucMem2Len = strlen(pucMen2);
            AtcAp_MemCpy(ptEvent_Cpms->aucMem2, pucMen2, ptEvent_Cpms->ucMem2Len);
        }
        if(pucMen3 != NULL)
        {
            ptEvent_Cpms->ucMem3Len = strlen(pucMen3);
            AtcAp_MemCpy(ptEvent_Cpms->aucMem3, pucMen3, ptEvent_Cpms->ucMem3Len);
        }
        ptEvent_Cpms->usEvent = D_ATC_EVENT_CPMS;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Cpms->usEvent = D_ATC_EVENT_CPMS_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CNMA_Command
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  Dep2_066   2016.12.20   create
*******************************************************************************/
unsigned char ATC_CNMA_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_PDU_TPDU_PARAMETER*  ptEvent_Cnma = (ST_ATC_PDU_TPDU_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(g_SmsFormatMode == 0)
        {
            if(AtcAp_CmdParamPrase(D_ATC_CMD_CNMA_FORMAT, pCommandBuffer,
                                &ptEvent_Cnma->ucNum,
                                NULL,&ptEvent_Cnma->ucPduLength) != ATC_AP_TRUE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ptEvent_Cnma->usEvent = D_ATC_EVENT_CNMA;
        }
        else
        {
            ptEvent_Cnma->ucNum = 0;
            ptEvent_Cnma->ucPduLength = 0;
            ptEvent_Cnma->usEvent = D_ATC_EVENT_CNMA;
        }
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Cnma->usEvent = D_ATC_EVENT_CNMA;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CSCA_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CSCA_PARAMETER*  ptEvent_Csca = (ST_ATC_CSCA_PARAMETER*)pEventBuffer;
    unsigned char*      pucSca = NULL;
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CSCA_FORMAT, pCommandBuffer, 
                            &pucSca,
                            &ptEvent_Csca->ucToscaFlag,&ptEvent_Csca->ucTosca) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Csca->ucScaLen = strlen(pucSca);
        AtcAp_MemCpy(ptEvent_Csca->aucScaData, pucSca, ptEvent_Csca->ucScaLen);
        ptEvent_Csca->usEvent = D_ATC_EVENT_CSCA;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Csca->usEvent = D_ATC_EVENT_CSCA_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CSMP_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CSMP_PARAMETER*  ptEvent_Csmp = (ST_ATC_CSMP_PARAMETER*)pEventBuffer;
    unsigned char*      pucVp = NULL;
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CSMP_FORMAT, pCommandBuffer,
                            &ptEvent_Csmp->ucFo,
                            NULL, &pucVp,
                            &ptEvent_Csmp->ucPidFlag,&ptEvent_Csmp->ucPid,
                            &ptEvent_Csmp->ucDcsFlag,&ptEvent_Csmp->ucDcs) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(pucVp != NULL)
        {
            ptEvent_Csmp->ucVpLen = strlen(pucVp);
            AtcAp_MemCpy(ptEvent_Csmp->ucVp, pucVp, ptEvent_Csmp->ucVpLen);
        }
        ptEvent_Csmp->usEvent = D_ATC_EVENT_CSMP;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Csmp->usEvent = D_ATC_EVENT_CSMP_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CNMI_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CNMI_PARAMETER*  ptEvent_Cnmi = (ST_ATC_CNMI_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CNMI_FORMAT, pCommandBuffer, 
                            &ptEvent_Cnmi->ucMode,
                            &ptEvent_Cnmi->ucMtFlag,&ptEvent_Cnmi->ucMt,
                            &ptEvent_Cnmi->ucBmFlag,&ptEvent_Cnmi->ucBm,
                            &ptEvent_Cnmi->ucDsFlag,&ptEvent_Cnmi->ucDs,
                            &ptEvent_Cnmi->ucBfrFlag,&ptEvent_Cnmi->ucBfr) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Cnmi->usEvent = D_ATC_EVENT_CNMI;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Cnmi->usEvent = D_ATC_EVENT_CNMI_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CMGW_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_PDU_TPDU_PARAMETER*  ptEvent_CmgwPdu = NULL;
    ST_ATC_TEXT_PARAMETER*  ptEvent_CmgwText = NULL;
    unsigned char           *pucDaData = NULL,*pucStat = NULL;
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(g_SmsFormatMode == 0)
        {
            ptEvent_CmgwPdu = (ST_ATC_PDU_TPDU_PARAMETER*)pEventBuffer;
            if(AtcAp_CmdParamPrase(D_ATC_CMD_CMGW_PDU_FORMAT, pCommandBuffer, 
                                &ptEvent_CmgwPdu->ucPduLength,
                                &ptEvent_CmgwPdu->ucStatFlag,&ptEvent_CmgwPdu->ucStat) != ATC_AP_TRUE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ptEvent_CmgwPdu->usEvent = D_ATC_EVENT_CMGW;
        }
        else
        {
            ptEvent_CmgwText = (ST_ATC_TEXT_PARAMETER*)pEventBuffer;
            if(AtcAp_CmdParamPrase(D_ATC_CMD_CMGW_TEXT_FORMAT, pCommandBuffer,
                                &pucDaData,
                                &ptEvent_CmgwText->ucToDaFlag,&ptEvent_CmgwText->ucToda,
                                NULL, &pucStat) != ATC_AP_TRUE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ptEvent_CmgwText->ucDaLen = strlen(pucDaData);
            AtcAp_MemCpy(ptEvent_CmgwText->aucDaData, pucDaData, ptEvent_CmgwText->ucDaLen);
            if(pucStat != NULL)
            {
                ptEvent_CmgwText->ucStatLen = strlen(pucStat);
                AtcAp_MemCpy(ptEvent_CmgwText->aucStat, pucStat, ptEvent_CmgwText->ucStatLen);
            }
            ptEvent_CmgwText->usEvent = D_ATC_EVENT_CMGW;
        }

    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CMGR_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMGR_PARAMETER*  ptEvent_Cmgr = (ST_ATC_CMGR_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CMGR_FORMAT, pCommandBuffer, &ptEvent_Cmgr->ucIndex) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Cmgr->usEvent = D_ATC_EVENT_CMGR;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CMGD_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMGD_PARAMETER*  ptEvent_Cmgd = (ST_ATC_CMGD_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CMGD_FORMAT, pCommandBuffer,
                                &ptEvent_Cmgd->ucIndex,
                                NULL,&ptEvent_Cmgd->ucDelFlag) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Cmgd->usEvent = D_ATC_EVENT_CMGD;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CMGL_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMGL_PARAMETER*  ptEvent_Cmgl = (ST_ATC_CMGL_PARAMETER*)pEventBuffer;
    unsigned char*      pucStat = NULL;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(g_SmsFormatMode == 0)
        {
#if VER_CM
            if((AtcAp_CmdParamPrase(D_ATC_CMD_CMGL_FORMAT, pCommandBuffer, &ptEvent_Cmgl->ucStat) != ATC_AP_TRUE)
                || ( ptEvent_Cmgl->ucStat > 4 && ptEvent_Cmgl->ucStat < 8))
#else
            if(AtcAp_CmdParamPrase(D_ATC_CMD_CMGL_FORMAT, pCommandBuffer, &ptEvent_Cmgl->ucStat) != ATC_AP_TRUE)
#endif
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        else
        {
            if((AtcAp_CmdParamPrase(D_ATC_CMD_CMGL_STATE_FORMAT, pCommandBuffer, NULL,&pucStat) != ATC_AP_TRUE)
                || (pucStat == NULL))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ptEvent_Cmgl->ucStatLen = strlen(pucStat);
            AtcAp_MemCpy(ptEvent_Cmgl->aucStat, pucStat, ptEvent_Cmgl->ucStatLen);
        }
        ptEvent_Cmgl->usEvent = D_ATC_EVENT_CMGL;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CMSS_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMSS_PARAMETER*  ptEvent_Cmss = (ST_ATC_CMSS_PARAMETER*)pEventBuffer;
    unsigned char*      pucDaData = NULL;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CMSS_FORMAT, pCommandBuffer,
                                &ptEvent_Cmss->ucIndex,
                                NULL, &pucDaData,
                                &ptEvent_Cmss->ucToDaFlag,&ptEvent_Cmss->ucToda) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(pucDaData != NULL)
        {
            ptEvent_Cmss->ucDaLen= strlen(pucDaData);
            AtcAp_MemCpy(ptEvent_Cmss->aucDaData, pucDaData, ptEvent_Cmss->ucDaLen);
        }
        ptEvent_Cmss->usEvent = D_ATC_EVENT_CMSS;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}


/*******************************************************************************
  MODULE    : ATC_CMGS_Command
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  Dep2_066   2016.12.20   create
*******************************************************************************/
unsigned char ATC_CMGS_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_PDU_TPDU_PARAMETER*  ptEvent_CmgsPdu = NULL;
    ST_ATC_TEXT_PARAMETER*  ptEvent_CmgsText = NULL;
    unsigned char*          pucDaData = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(g_SmsFormatMode == 0)
        {
            ptEvent_CmgsPdu = (ST_ATC_PDU_TPDU_PARAMETER*)pEventBuffer;
            if(AtcAp_CmdParamPrase(D_ATC_CMD_CMGS_PDU_FORMAT, pCommandBuffer,
                                &ptEvent_CmgsPdu->ucPduLength) != ATC_AP_TRUE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ptEvent_CmgsPdu->usEvent = D_ATC_EVENT_CMGS;
        }
        else
        {
            ptEvent_CmgsText = (ST_ATC_TEXT_PARAMETER*)pEventBuffer;
            if((AtcAp_CmdParamPrase(D_ATC_CMD_CMGS_TEXT_FORMAT, pCommandBuffer, 
                                NULL, &pucDaData,
                                &ptEvent_CmgsText->ucToDaFlag,&ptEvent_CmgsText->ucToda) != ATC_AP_TRUE)
                || (pucDaData == NULL))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ptEvent_CmgsText->ucDaLen = strlen(pucDaData);
            AtcAp_MemCpy(ptEvent_CmgsText->aucDaData, pucDaData, ptEvent_CmgsText->ucDaLen);
            ptEvent_CmgsText->usEvent = D_ATC_EVENT_CMGS;
        }
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CMGC_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_PDU_TPDU_PARAMETER*  ptEvent_CmgcPdu = NULL;
    ST_ATC_TEXT_PARAMETER*  ptEvent_CmgcText = NULL;
    unsigned char*          pucDaData = NULL;
    unsigned char*          pucStat = NULL;
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(g_SmsFormatMode == 0)
        {
            ptEvent_CmgcPdu = (ST_ATC_PDU_TPDU_PARAMETER*)pEventBuffer;
            if(AtcAp_CmdParamPrase(D_ATC_CMD_CMGC_PDU_FORMAT, pCommandBuffer, 
                                &ptEvent_CmgcPdu->ucPduLength) != ATC_AP_TRUE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ptEvent_CmgcPdu->usEvent = D_ATC_EVENT_CMGC;
        }
        else
        {
            ptEvent_CmgcText = (ST_ATC_TEXT_PARAMETER*)pEventBuffer;
            ptEvent_CmgcText->ucPid = 0;
            if(AtcAp_CmdParamPrase(D_ATC_CMD_CMGC_TEXT_FORMAT, pCommandBuffer, 
                                &ptEvent_CmgcText->ucFo,
                                &ptEvent_CmgcText->ucCt,
                                NULL,&ptEvent_CmgcText->ucPid,
                                &ptEvent_CmgcText->ucMnFlag,&ptEvent_CmgcText->ucMn,
                                NULL,&pucDaData,
                                &ptEvent_CmgcText->ucToDaFlag,&ptEvent_CmgcText->ucToda) != ATC_AP_TRUE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            if(pucDaData != NULL)
            {
                ptEvent_CmgcText->ucDaLen = strlen(pucDaData);
                AtcAp_MemCpy(ptEvent_CmgcText->aucDaData, pucDaData, ptEvent_CmgcText->ucDaLen);
            }
            ptEvent_CmgcText->usEvent = D_ATC_EVENT_CMGC;
        }

    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CMMS_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMMS_PARAMETER*  ptEvent_Cmms = (ST_ATC_CMMS_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CMMS_FORMAT, pCommandBuffer, 
                                &ptEvent_Cmms->ucVal) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Cmms->usEvent = D_ATC_EVENT_CMMS;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Cmms->usEvent = D_ATC_EVENT_CMMS_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}


unsigned char ATC_CSCB_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CSCB_PARAMETER*  ptEvent_Cscb = (ST_ATC_CSCB_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CSCB_FORMAT, pCommandBuffer, 
                                &ptEvent_Cscb->ucMode) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Cscb->usEvent = D_ATC_EVENT_CSCB;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Cscb->usEvent = D_ATC_EVENT_CSCB_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CSDH_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Csdh = (ST_ATC_CMD_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CSDH_FORMAT, pCommandBuffer, 
                                &ptEvent_Csdh->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Csdh->usEvent = D_ATC_EVENT_CSDH;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Csdh->usEvent = D_ATC_EVENT_CSDH_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QCMGS_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_QCMGS_PARAMETER*  ptEvent_Qcmgs = (ST_ATC_QCMGS_PARAMETER*)pEventBuffer;
    unsigned char*      pucDaData = NULL;
    unsigned char       ucUidFlg = 0,ucMsgSegFlg = 0,ucMsgTotalFlg = 0;
    unsigned char       ucAllFlgValue = 0;
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if((AtcAp_CmdParamPrase(D_ATC_CMD_QCMGS_FORMAT, pCommandBuffer,
                                NULL, &pucDaData,
                                &ptEvent_Qcmgs->ucToDaFlag,&ptEvent_Qcmgs->ucToda,
                                &ucUidFlg,&ptEvent_Qcmgs->ucUid,
                                &ucMsgSegFlg,&ptEvent_Qcmgs->ucMsgSeg,
                                &ucMsgTotalFlg,&ptEvent_Qcmgs->ucMsgTotal) != ATC_AP_TRUE)
            || (pucDaData == NULL))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ucAllFlgValue = ucUidFlg + ucMsgSegFlg + ucMsgTotalFlg;
        if(ucAllFlgValue != 3 && ucAllFlgValue != 0)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Qcmgs->ucExtParamFlg = ATC_AP_TRUE;
        ptEvent_Qcmgs->ucDaLen= strlen(pucDaData);
        AtcAp_MemCpy(ptEvent_Qcmgs->aucDa, pucDaData, ptEvent_Qcmgs->ucDaLen);
        ptEvent_Qcmgs->usEvent = D_ATC_EVENT_QCMGS;
        g_AtcApInfo.usCurrEvent = D_ATC_EVENT_QCMGS;
        AtcAp_SmsQcmgsText_Analysis(NULL,ptEvent_Qcmgs);
        return D_ATC_COMMAND_END;
    }
    return D_ATC_COMMAND_SYNTAX_ERROR;
}

unsigned char ATC_QCMGR_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_QCMGR_PARAMETER*  ptEvent_Qcmgr = (ST_ATC_QCMGR_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QCMGR_FORMAT, pCommandBuffer, 
                                &ptEvent_Qcmgr->ucIndex) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Qcmgr->usEvent = D_ATC_EVENT_QCMGR;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

#ifdef LCS_MOLR_ENABLE
unsigned char ATC_CMOLR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMOLR_PARAMETER*  ptEvent_Cmolr = (ST_ATC_CMOLR_PARAMETER*)pEventBuffer;
    unsigned char           pucNmeaReqData = NULL, pucTPAddrData = NULL;
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CMOLR_FORMAT, pCommandBuffer,
                                &ptEvent_Cmolr->ucEnableReportPos,
                                &ptEvent_Cmolr->ucMethodFlg,&ptEvent_Cmolr->ucMethod,
                                NULL,&ptEvent_Cmolr->ucHorAccSet,
                                &ptEvent_Cmolr->ucHorAccFlg,&ptEvent_Cmolr->ucHorAcc,
                                NULL,&ptEvent_Cmolr->ucVelReq,
                                NULL,&ptEvent_Cmolr->ucVerAccSet,
                                &ptEvent_Cmolr->ucVerAccFlg,&ptEvent_Cmolr->ucVerAcc,
                                NULL,&ptEvent_Cmolr->ucVelReq,
                                NULL,&ptEvent_Cmolr->ucRepMode,
                                NULL,&ptEvent_Cmolr->usTimeOut,
                                NULL,&ptEvent_Cmolr->usInterval,
                                NULL,&ptEvent_Cmolr->ucShapeRep,
                                NULL,&ptEvent_Cmolr->ucPlane,
                                NULL,&pucNmeaReqData,
                                NULL,&pucTPAddrData) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        //if(pucNmeaReqData != NULL)
        //{
        //    ptEvent_Cmolr->ucNmeaRepLen = strlen(pucNmeaReqData);
        //    AtcAp_MemCpy(ptEvent_Cmolr->uc, pucNmeaReqData, ptEvent_Cmolr->ucNmeaRepLen)
        //}
        //if(pucTPAddrData != NULL)
        //{
        //    ptEvent_Cmolr->ucStatLen = strlen(pucTPAddrData);
        //    AtcAp_MemCpy(ptEvent_Cmolr->aucStat, pucTPAddrData, ptEvent_Cmolr->ucStatLen)
        //}
        ptEvent_Cmolr->usEvent = D_ATC_EVENT_CMOLR;
    }
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        ptEvent_Cmolr->usEvent = D_ATC_EVENT_CMOLR_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}
#endif
#endif
