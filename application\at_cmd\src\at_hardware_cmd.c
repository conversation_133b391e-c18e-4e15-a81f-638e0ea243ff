/*******************************************************************************
 *硬件外设相关AT命令*
 ******************************************************************************/
#include "posix_io.h"
#include "at_hardware_api.h"
#include "xy4100_ll_gpio.h"
#include "hal_adc.h"
#include "softap_nv.h"
#include "xy_at_api.h"

#if (LPUART_AT || UART_AT)
/**
 * @brief  AT串口配置(波特率)
 * @arg 请求类AT命令：AT+IPR=<baud_rate>，可配置波特率：4800,9600,14400, 19200,28800,33600,38400,57600,115200,230400,460800,921600,1000000
 * @warning LIGHTSLEEP睡眠唤醒，由于耗时需要0.7ms左右，进而波特率至多只能支持230400；超过230400波特率时，需要人为关闭LIGHTSLEEP，建议通过锁来关闭。
 */
int at_IPR_req(char *at_buf, char **prsp_cmd)
{
	/*返回0表示波特率自适应*/
	if (g_req_type == AT_CMD_QUERY)
	{
		*prsp_cmd = xy_malloc(40);
		sprintf(*prsp_cmd, "%d", (g_softap_var_nv->at_ipr & 0x8000) ? 0 : atUartBaudRateGet());
	}

#if (!AT_TEST_OFF)
	else if (g_req_type == AT_CMD_TEST)
	{
		*prsp_cmd = xy_malloc(200);
		sprintf(*prsp_cmd, "(4800, 9600, 14400, 19200, 28800, 33600, 38400, 57600, 115200, 230400, 460800, 921600),(0,4800,9600,19200,28800,33600,38400,57600,115200,230400,460800,921600,1000000)");
	}
#endif
	
	else if (g_req_type == AT_CMD_REQ) //设置类
	{ 
        int ret = XY_OK;
        uint32_t baud_rate = 115200;
		
#if UART_AT
		if (at_parse_param("%d[0|4800|9600|14400|19200|28800|33600|38400|57600|115200|230400|460800|921600|1000000|2000000]", at_buf, &baud_rate) != XY_OK)
#else
        if (at_parse_param("%d[0|4800|9600|14400|19200|28800|33600|38400|57600|115200|230400|460800|921600|1000000]", at_buf, &baud_rate) != XY_OK)
#endif
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }
#if VER_CM
        ret = set_at_uart_ipr(get_current_ttyFd(), baud_rate, true);
#else /* VER_CM */
        ret = set_at_uart_ipr(get_current_ttyFd(), baud_rate, false);
#endif /* VER_CM */
        if (ret == XY_OK)
            return AT_ASYN;
        else if (ret == XY_ERR)
            return AT_END;
        else
            *prsp_cmd = AT_PLAT_CME_ERR(ret); 
	}
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

	return AT_END;
}


// AT+IFC=<dce_by_dte>,<dte_by_dce> 设置串口通信的流控方式
int at_IFC_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_QUERY)
	{
		*prsp_cmd = xy_malloc(40);
		if (at_uart_flowctl_IsEnable())
			sprintf(*prsp_cmd, "2,2");
		else
			sprintf(*prsp_cmd, "0,0");
	}
	else if (g_req_type == AT_CMD_REQ)
	{       
        int ret = XY_OK;
		// <dce_by_dte>和<dte_by_dce>的值必须相同
        int dce_by_dte = 0, dte_by_dce = 0;

        if (at_parse_param("%d[0|2],%d[0|2]", at_buf, &dce_by_dte, &dte_by_dce) != XY_OK)
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }
		else if (dce_by_dte == dte_by_dce)
		{
            ret = set_at_uart_ifc(get_current_ttyFd(), dte_by_dce);
            if (ret == XY_OK)
                return AT_ASYN;
            else
                *prsp_cmd = AT_PLAT_CME_ERR(ret);
		}
		else
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
#if (!AT_TEST_OFF)
	else if (g_req_type == AT_CMD_TEST)
	{
		*prsp_cmd = xy_malloc(200);
		sprintf(*prsp_cmd, "(0,2),(0,2)");
	}
#endif	
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

    return AT_END;
}


// AT&C[<value>] 设置DCD信号模式，与检测远端的线路信号有关
int at_ANDC_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_ACTIVE)
    {
		int value = 1;

        if (at_parse_param("%d[0-1]", at_buf, &value) == XY_OK)
        {
			set_atc_mode(value);
        }
		else
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
	}
	else
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);

    return AT_END;
}
 
// AT&D[<value>] 设置在端口处于数据模式时，DTR信号从低电平转变至高电平后UE对应的状态
int at_ANDD_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_ACTIVE)
    {
		int value = 2;

        if (at_parse_param("%d[0-2]", at_buf, &value) == XY_OK)
            *prsp_cmd = AT_PLAT_CME_ERR(set_at_uart_dtr(get_current_ttyFd(), value));
		else
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
	}
	else
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);

    return AT_END;
}
#endif


/**************************IO连通性测试******************************/
#define XYCNNT_IO_NUM (5 * 2)
static TEST_GPIO_PinTypeDef g_gpio_num[XYCNNT_IO_NUM] = {
    WKP1, WKP3,
    GPIO2, GPIO3,
    GPIO15, GPIO16,
    GPIO25, GPIO26,
    GPIO28, GPIO29
};

static int g_XYCNNT_bitmap = 0x7FFFFFFF; //1个bit表示一个g_gpio_num的元素，如0x1表示g_gpio_num[0]

/**
  * @brief  连通性测试
  * @param  test_cmd 0：查询类命令，1：测试类命令
  */
static void do_XYCNNT_process(char **prsp_cmd, char test_cmd)
{
    //查询、测试命令上报
    *prsp_cmd = xy_malloc(100 + XYCNNT_IO_NUM * 4);
    if(test_cmd == 0)
        snprintf(*prsp_cmd, 40, "\r\n+XYCNNT:bitmap=0x%x\r\n", g_XYCNNT_bitmap);
    else
		snprintf(*prsp_cmd, 40, "\r\n+XYCNNT:bitmap=(0-0x%x)\r\n", g_XYCNNT_bitmap);

    //判断g_XYCNNT_bitmap是否有效
    if(g_XYCNNT_bitmap == 0)
    {
        snprintf(*prsp_cmd + strlen(*prsp_cmd), 40, "\r\n+XYCNNT:FAIL");
        return;
    }

    //查询、测试命令都要做连通性测试
    ErrorStatus result = XY_SUCCESS;
    snprintf(*prsp_cmd + strlen(*prsp_cmd), 20, "\r\n+XYCNNT:");
    for (volatile uint32_t i = 0; i < XYCNNT_IO_NUM; i++)
    {
        if (g_XYCNNT_bitmap & (1UL << i))
        {
			//偶数：低号引脚输出，高号引脚输入
            if (i % 2 == 0)
			{
                //输出高
				if (Check_Pin_Connectivity(g_gpio_num[i + 1], g_gpio_num[i], 1))
					snprintf(*prsp_cmd + strlen(*prsp_cmd), 10, "%xY", i);
				else{
                    snprintf(*prsp_cmd + strlen(*prsp_cmd), 10, "%xN", i);
                    result = XY_ERROR;
                }
					
                //输出低
				if (Check_Pin_Connectivity(g_gpio_num[i + 1], g_gpio_num[i], 0))
					snprintf(*prsp_cmd + strlen(*prsp_cmd), 5, "Y,");
				else{
					snprintf(*prsp_cmd + strlen(*prsp_cmd), 5, "N,");
                    result = XY_ERROR;
                }

			}
            //奇数：高号引脚输出，低号引脚输入
			else
			{
                //输出高
				if (Check_Pin_Connectivity(g_gpio_num[i - 1], g_gpio_num[i], 1))
					snprintf(*prsp_cmd + strlen(*prsp_cmd), 10, "%xY", i);
				else{
					snprintf(*prsp_cmd + strlen(*prsp_cmd), 10, "%xN", i);
                    result = XY_ERROR;
                }

                //输出低
				if (Check_Pin_Connectivity(g_gpio_num[i - 1], g_gpio_num[i], 0))
					snprintf(*prsp_cmd + strlen(*prsp_cmd), 5, "Y,");
				else{
					snprintf(*prsp_cmd + strlen(*prsp_cmd), 5, "N,");
                    result = XY_ERROR;
                }
			}
        }
    }

    //打印测试结果
    if(result == XY_SUCCESS)
    {
        snprintf(*prsp_cmd + strlen(*prsp_cmd), 20, "SUCCESS");
    }
    else
    {
        snprintf(*prsp_cmd + strlen(*prsp_cmd), 20, "FAIL");
    }
}

/**
  * @brief  引脚连通性测试AT命令
  * @param  at_buf:
  * @param  prsp_cmd:
  * @retval AT_END
  * @arg 请求类AT命令：AT+XYCNNT=<bit map>
  * @arg 查询类AT命令：AT+XYCNNT?
  * @arg 测试类AT命令：AT+XYCNNT=?
  * @attention 此指令仅限于示例用，用户根据具体使用的GPIO自行调整维护。
  */
int at_XYCNNT_req(char *at_buf, char **prsp_cmd)
{
	int temp_bitmap = 0;
	if(g_req_type == AT_CMD_REQ) //设置参数
	{
        if(at_parse_param("%d[0-2147483647]", at_buf, &temp_bitmap) != XY_OK)
        {
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

		if((temp_bitmap > g_XYCNNT_bitmap) || (temp_bitmap == 0))
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

        g_XYCNNT_bitmap = temp_bitmap;
	}
	else if(g_req_type == AT_CMD_QUERY) //查询
	{
        do_XYCNNT_process(prsp_cmd, 0);
	}
#if (!AT_TEST_OFF)
	else if(g_req_type == AT_CMD_TEST) //测试
	{
		do_XYCNNT_process(prsp_cmd, 1);
	}
#endif
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

	return AT_END;
}
//0表示UART下载；1表示USB下载
//UART下载时，需要把logview的bootloader.ini中[XY4100L]的TimeOut改成5000
int at_FORCEDL_req(char *at_buf, char **prsp_cmd)
{
    int32_t mode = 1;
    if (at_parse_param("%d", at_buf, &mode) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return AT_END;
    }
    else
    {
        force_download_and_reset((uint8_t)mode);
    }

    return AT_END;
}
