#include "at_hardware_api.h"
#include "xy_at_api.h"
#include "xy_system.h"
#include "xy_atc_interface.h"
#include "xy_ps_api.h"
#include "atc_ps.h"

#define  ATERR_CMCC_INVALID  8001   /*对应中移的ATERR_PARAM_INVALID*/

int at_MUECONFIG_req(char *at_buf, char **prsp_cmd){				//add by lyh
        char       cmd_str[32] = {0};
        int32_t     param_1 = -1;
        int32_t     param_2 = -1;
        int32_t     param_cnt_flag = -1;        //用于参数个数限制

    switch(g_req_type){
        case AT_CMD_REQ:
        {
            if(at_parse_param("%31s,",at_buf,cmd_str) != XY_OK)
            {
                *prsp_cmd = AT_PLAT_CME_ERR(ATERR_CMCC_INVALID);
                 break;
            }
            xy_printf(0,ATC_AP_T,INFO_LOG,"cmd=%s ",cmd_str);
            if(!strcmp(cmd_str,"simenable"))
            {
                *prsp_cmd = AT_PLAT_CME_ERR(ATERR_CMCC_INVALID);
                break;                                          //暂未实现
            }

            if(!strcmp(cmd_str,"simswap"))
            {
                if(at_parse_param(",%d[0-1],%d[0-1],%d",at_buf,&param_1,&param_2,&param_cnt_flag) != XY_OK || param_cnt_flag != -1)
                {
                   *prsp_cmd = AT_PLAT_CME_ERR(ATERR_CMCC_INVALID);
                    break;
                }
                xy_printf(0,ATC_AP_T,INFO_LOG,"query:param1=%d param2=%d",param_1,param_2);
                if(param_1 == -1 && param_2 ==-1)           //查询
                {
                    int len = 0;
                    /*查询sim卡位*/
                    ATC_MSG_SIMSWICH_R_CNF_STRU * pSIMSwitch_R = (ATC_MSG_SIMSWICH_R_CNF_STRU *)AtcAp_Malloc(sizeof(ATC_MSG_SIMSWICH_R_CNF_STRU)); 

                    if(XY_OK != xy_atc_interface_call("AT+QSIMSWITCH?\r\n",NULL,(void *)pSIMSwitch_R))
                    {
                        AtcAp_Free(pSIMSwitch_R);
                        *prsp_cmd = AT_PLAT_CME_ERR(ATERR_CMCC_INVALID);
                        break;
                    }

                    /*查询是否支持热插拔AT+QSIMDET?*/
                    ATC_MSG_QSIMDET_R_CNF_STRU * pQSIMDET_R = (ATC_MSG_QSIMDET_R_CNF_STRU *)AtcAp_Malloc(sizeof(ATC_MSG_QSIMDET_R_CNF_STRU));
                    if(XY_OK != xy_atc_interface_call("AT+QSIMDET?\r\n",NULL,(void *)pQSIMDET_R))
                    {
                        AtcAp_Free(pQSIMDET_R);
                        *prsp_cmd = AT_PLAT_CME_ERR(ATERR_CMCC_INVALID);
                        break;
                    }
                    xy_printf(0,ATC_AP_T,INFO_LOG,"query:DET=%d Switch=%d",pQSIMDET_R->ucEnable,pSIMSwitch_R->ucValue);

					*prsp_cmd = AtcAp_Malloc(48);

                    if(pSIMSwitch_R->ucValue == 1)              //307X卡1不支持热插拔
                    {
                        sprintf(*prsp_cmd,"+MUECONFIG: \"simswap\",0,%d",pSIMSwitch_R->ucValue);
                    }
                    else
                        sprintf(*prsp_cmd,"+MUECONFIG: \"simswap\",%d,%d",pQSIMDET_R->ucEnable,pSIMSwitch_R->ucValue);
                    AtcAp_Free(pSIMSwitch_R);
                    AtcAp_Free(pQSIMDET_R);
                    break;
                }
                if(param_1 != -1 && param_2 == -1)         //只设置simswap参数
                {
                    xy_printf(0,ATC_AP_T,INFO_LOG,"set only simswap:param1=%d param2=%d",param_1,param_2);
                    if(param_1 == 0)
                    {
                        if(XY_OK != xy_atc_interface_call("AT+QSIMDET=0,1\r\n",NULL,(void*)NULL))
                        {
                            *prsp_cmd = AT_PLAT_CME_ERR(ATERR_CMCC_INVALID);
                            break;
                        }
                    }
                    if(param_1 == 1)
                    {
                        if(XY_OK != xy_atc_interface_call("AT+QSIMDET=1,1\r\n",NULL,(void*)NULL))
                        {
                            *prsp_cmd = AT_PLAT_CME_ERR(ATERR_CMCC_INVALID);
                            break;
                        }
                    }
                }
                else                                       //设置simswap和simslot参数
                {
                    if(param_2 == 1 && param_1 ==1)         //sim卡1不支持热插拔
                    {
                        *prsp_cmd = AT_PLAT_CME_ERR(ATERR_CMCC_INVALID);
                        break;
                    }
                    else
                    {
                        char *QSIMDET_str = AtcAp_Malloc(30);
                        char *QSIMSWITCH_str = AtcAp_Malloc(30);
                        sprintf(QSIMSWITCH_str,"AT+QSIMSWITCH=%d\r\n",param_2);
                        sprintf(QSIMDET_str,"AT+QSIMDET=%d,1\r\n",param_1);
                        xy_printf(0,ATC_AP_T,INFO_LOG,"%s %s",QSIMSWITCH_str,QSIMDET_str);
                        if(XY_OK != xy_atc_interface_call(QSIMSWITCH_str,NULL,(void*)NULL))
                        {
                            *prsp_cmd = AT_PLAT_CME_ERR(ATERR_CMCC_INVALID);
                            break;
                        }
                        if(XY_OK != xy_atc_interface_call(QSIMDET_str,NULL,(void*)NULL))
                        {
                            *prsp_cmd = AT_PLAT_CME_ERR(ATERR_CMCC_INVALID);
                            break;
                        }
                        AtcAp_Free(QSIMDET_str);
                        AtcAp_Free(QSIMSWITCH_str);
                    }
                }
            }
            else
                return AT_FORWARD;
            break;
        }

        default:
            return AT_FORWARD;
    }
    return AT_END;

}


