
#include "audio_record.h"
#include "alsa_capture_config.h"


int32_t audio_encode_pcm_process(uint8_t * data, uint32_t len, audio_rec_t *audio_rec)
{
    int32_t ret = REC_ERR_OK;
    int32_t write_len;

    if(audio_rec->state_get() == REC_PAUSE)
    {
        return REC_ERR_OK;
    }

    if(audio_rec->state_get() == REC_STOP)
    {
        return REC_ERR_STOP;
    }

    write_len = audio_rec->dest->write(data, len, audio_rec->dest->dest_arg);

    if(write_len < (int32_t)len)
    {
        ret = REC_ERR_DEST_FULL;
    }
    audio_rec->dest->dest_arg->real_duration_ms += ALSA_CAPTURE_DMA_SLOT_MS;
    if(audio_rec->dest->dest_arg->real_duration_ms >= audio_rec->dest->dest_arg->duration_ms)
    {
        ret = REC_ERR_DEST_FULL;
    }

    return ret;
}
