/**
 * @file sms_passthrough.c
 * @brief 
 * @version 1.0
 * @date 2021-04-29
 * @copyright Copyright (c) 2021  芯翼信息科技有限公司
 * 
 */

#include "sms_passthrough.h"
#include "at_passthrough.h"
#include "xy_at_api.h"
#include "xy_utils.h"
#include "xy_system.h"

#define SMS_BLOCK_LEN           512

char *sms_rcv_buff = NULL;
uint32_t sms_rcved_len = 0;
uint32_t sms_buff_len = 0;
static int sms_ttyFd = AT_FD_INVAILD;
static int g_sms_passth_id = -1;
extern void xy_atc_data_req(unsigned short usDataLen, unsigned char*pucData, int ittyFd);
extern int enterPassthroughMode(int ttyFd, int events, passthEventCb func);


static uint8_t sendFlag = 0;

/*tty收到的短信透传数据的处理，需要识别结束标识，如"ctrl+z"或ECS等*/
int at_sms_passthr_proc(int atHandle, uint8_t channCurState, int eventId, void *arg)
{
    if (eventId != PASSTH_EVENT_DATA_INPUT)
    {
        return XY_ERR;
    }

    osMutexAcquire(g_sms_passth_mutex, osWaitForever);

    xy_assert(channCurState == DEF_SMS_PASSTH_STATE);
    atPassthDataArg_t *dataArg = (atPassthDataArg_t *)arg;
	char *buf = (char *)dataArg->pData;
    uint32_t data_len = dataArg->dataSize;

    if (sms_rcv_buff == NULL)
	{
		sms_rcv_buff = xy_malloc(SMS_BLOCK_LEN);
        memset(sms_rcv_buff, 0x0, SMS_BLOCK_LEN);
        sms_buff_len = SMS_BLOCK_LEN;
	}
	
CONTINUE:

    if (sms_buff_len < sms_rcved_len + data_len)
    {
        char *new_mem = xy_malloc(sms_buff_len + SMS_BLOCK_LEN);
        memset(new_mem, 0x0, sms_buff_len + SMS_BLOCK_LEN);
        if (sms_rcved_len != 0)
            memcpy(new_mem, sms_rcv_buff, sms_rcved_len);
        xy_free(sms_rcv_buff);
        sms_rcv_buff = new_mem;
        sms_buff_len += SMS_BLOCK_LEN;
		goto CONTINUE;
    }

    memcpy(sms_rcv_buff + sms_rcved_len, buf, data_len);
    sms_rcved_len += data_len;

    if (strchr(sms_rcv_buff, PASSTH_CTRLZ) != NULL || strchr(sms_rcv_buff, PASSTH_ESC) != NULL)
    {
        /* 检测到数据中包含1A或者1B,即投递给PS */
        if (!sendFlag)
        {
            xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_sms_passthr_proc]recv 1A or 1B, send to ps directly");
            void *sms_data = NULL;
            sms_data = (void *)xy_malloc(sms_rcved_len);
            memset(sms_data, 0x0, sms_rcved_len);
            memcpy((char *)sms_data, sms_rcv_buff, sms_rcved_len);
            sendFlag = 1;
            xy_atc_data_req(sms_rcved_len, sms_data, sms_ttyFd);
            xy_free(sms_data);
        }
        else
        {
            /* 避免1A1B同时存在且被lpuart分别投递造次多次调用xy_atc_data_req接口 */
            xy_printf(0, PLATFORM_AP, WARN_LOG, "[at_sms_passthr_proc]may recv more 1A or 1B, do nothing");
        }
    }

    // 在AT框架中退出短信的透传，由命令结果码返回触发，此处不需调用atEnterCommandState
    osMutexRelease(g_sms_passth_mutex);
    return XY_OK;
}

int at_sms_passthr_exit(void)
{
    osMutexAcquire(g_sms_passth_mutex, osWaitForever);
    if (sms_rcv_buff != NULL)
    {
        xy_free(sms_rcv_buff);
        sms_rcv_buff = NULL;
    }
    sms_rcved_len = 0;
    sms_buff_len = 0;
    sms_ttyFd = AT_FD_INVAILD;
    sendFlag = 0;
    osMutexRelease(g_sms_passth_mutex);
    return XY_OK;
}

int sms_enterPassthroughMode(int fd)
{
    int ret = atEnterPassthroughState(fd, DEF_SMS_PASSTH_STATE, PASSTH_EVENT_DATA_INPUT, at_sms_passthr_proc);
    
    if (ret != -1)
    {
        sendFlag = 0;
        sms_ttyFd = fd;
        g_sms_passth_id = ret;

        return XY_OK;
    }

    return XY_ERR;
}

int sms_exitPassthroughMode()
{
    if (sms_ttyFd != AT_FD_INVAILD)
    {
        atEnterCommandState(sms_ttyFd, DEF_SMS_PASSTH_STATE);
        at_sms_passthr_exit();
    }
    return XY_OK;
}