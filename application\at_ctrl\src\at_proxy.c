/**
 * @file at_proxy.c
 * @brief at代理线程，用于处理平台拓展命令
 * @version 1.0
 * @date 2022-07-07
 * @copyright Copyright (c) 2022  芯翼信息科技有限公司
 * 
 */

#include "at_context.h"
#include "at_ctl.h"
#include "at_utils.h"
#include "xy_system.h"
#include "factory_nv.h"

/*******************************************************************************
 *                             Type definitions                                *
 ******************************************************************************/
typedef struct at_proxy_msg
{
    int size;       //消息数据长度
    char data[0];   //消息数据
} at_proxy_msg_t;

/*******************************************************************************
 *						   Local variable definitions				           *
 ******************************************************************************/
osMessageQueueId_t at_proxy_msg_q = NULL;
int g_cur_ttyFd = AT_FD_INVAILD; /* 代理线程正在处理的AT命令FD */

/*******************************************************************************
 *						Local function implementations						   *
 ******************************************************************************/
bool proc_at_proxy_req(at_proxy_msg_t *msg)
{
    /* Step1: 参数检测 */
	xy_assert(msg != NULL && msg->data != NULL);
	at_msg_t *at_msg = (at_msg_t *)msg->data;
    at_context_t *proxy_ctx = &g_at_tty_ctx[at_msg->srcFd];
    xy_assert(proxy_ctx != NULL && proxy_ctx->at_proc != NULL);
	g_req_type = proxy_ctx->at_type; /* 记录当前处理的AT命令类型 */
	char *rsp_cmd = NULL;

    /* Step2: 记录当前at命令是从哪个外设发来的，主要用于透传模块 */
    g_cur_ttyFd = at_msg->srcFd;
    
    /* Step3: 处理at命令 */
    int ret = proxy_ctx->at_proc((at_msg->data + at_msg->offset), &rsp_cmd);
    xy_printf(0, PLATFORM_AP, INFO_LOG, "[proc_at_proxy_req]deal with %s from tty:%d done, ret:%d", proxy_ctx->at_cmd_prefix, g_cur_ttyFd, ret);
	debug_log_print("[proc_at_proxy_req]deal with %s from tty:%d done, ret:%d\n",proxy_ctx->at_cmd_prefix, g_cur_ttyFd, ret); 

    /* Step4: at命令路由，转发给PS协议栈处理或者发送给各业务模块处理，最终由业务模块产生结果码后发送给at_ctl主框架处理 */
    if (AT_FORWARD == ret)
	{
		at_req_forward(at_msg);
		goto END_PROC;
	}
	else if (AT_ASYN == ret)
	{
        goto END_PROC;
	}

	/* Step5: 若at命令内部未构建任何结果码，由at_proxy线程构建OK结果码发送出去*/
	if (rsp_cmd == NULL)
	{
        rsp_cmd = at_ok_build();
    }

	/*具体AT应答字符串尾部不加\r\n时，由框架自动添加前缀和OK结果码*/
	else if(strstr(rsp_cmd+strlen(rsp_cmd)-2, "\r\n") == NULL)
	{
	
		char *temp = xy_malloc(strlen(rsp_cmd)+15+strlen(proxy_ctx->at_cmd_prefix));

		/*有前缀，仅需添加尾部OK。通常用于前缀不规则的URC上报，如REBOOTING上报*/
		if(*rsp_cmd == '\r')
		{
			sprintf(temp,"%s\r\n\r\nOK\r\n",rsp_cmd);
		}
		/*添加头部\r\n和尾部OK，通常用于URC冒号后面不带空格的特殊URC*/
		else if(*rsp_cmd == '+')
		{
			sprintf(temp,"\r\n%s\r\n\r\nOK\r\n",rsp_cmd);
		}
		/*添加前缀和尾部OK，用于标准的URC上报*/
		else 
		{
			sprintf(temp,"\r\n+%s: %s\r\n\r\nOK\r\n",proxy_ctx->at_cmd_prefix,rsp_cmd);
		}
		
		xy_free(rsp_cmd);
		rsp_cmd = temp;
	}

	/*未携带OK，框架尾部自动添加*/
	else if(Is_Result_AT_str(rsp_cmd) == 0)
	{
		char *temp = xy_malloc(strlen(rsp_cmd)+8+strlen(proxy_ctx->at_cmd_prefix));

		sprintf(temp,"%s\r\nOK\r\n",rsp_cmd);	
		xy_free(rsp_cmd);
		rsp_cmd = temp;
	}


    /* Step6: 根据srcFd将at命令结果发送对应的外设 */
	at_send_to_farps(rsp_cmd, strlen(rsp_cmd), at_msg->srcFd);

END_PROC:
	if (rsp_cmd != NULL)
		xy_free(rsp_cmd);

	return 1;
}

void at_proxy_proc(void)
{
    at_proxy_msg_t *msg = NULL;
    while (1)
    {
        osMessageQueueGet(at_proxy_msg_q, (void *)(&msg), NULL, osWaitForever);
        proc_at_proxy_req((at_proxy_msg_t *)msg);
        xy_free(msg);
    }
}

int send_msg_2_at_proxy(void *buff, int len)
{
    xy_assert(at_proxy_msg_q != NULL);

    at_proxy_msg_t *msg = NULL;
    msg = xy_malloc2(sizeof(at_proxy_msg_t) + len);

	debug_assert(len<10000);
	
    if (msg == NULL)
        return XY_Err_NoMemory;
    msg->size = len;
    if (buff != NULL)
        memcpy(msg->data, buff, len);
    osMessageQueuePut(at_proxy_msg_q, &msg, 0, osWaitForever);
    return XY_OK;
}

/**
  * @brief 获取AT主控正在处理的AT命令接收的通道句柄，仅供at_basic_req注册的解析函数调用。普通业务线程禁止调用该接口，否则会断言！
  * @return  句柄ID，@ref @see AT_TTY_FD
  * @warning 只能用于在at_proxy主控线程处理的AT命令中使用，否则会断言！！
  */
/*在at_proxy线程中获取当前正在处理的AT请求对应的tty串口句柄*/
int get_current_ttyFd()
{
    if (strstr(osThreadGetName(osThreadGetId()), AT_PROXY_THREAD_NAME) != NULL)
    {
        return g_cur_ttyFd;
    }
    else
    {
        /* 非at_proxy线程调用断言 */
        xy_assert(0);
		return g_cur_ttyFd;
    }
}

void at_proxy_init(void)
{
	at_proxy_msg_q = osMessageQueueNew(AT_PROXY_QUEUE_SIZE, sizeof(void *), NULL);
	
	osThreadAttr_t thread_attr = {0};
	thread_attr.name        = AT_PROXY_THREAD_NAME;
	thread_attr.priority    = AT_PROXY_THREAD_PRIO;
	thread_attr.stack_size  = AT_PROXY_THREAD_STACKSIZE;
	osThreadNew((osThreadFunc_t)(at_proxy_proc), NULL, &thread_attr);
}
