#if PS_TEST_MODE
#ifndef _PSTEST_H_
#define _PSTEST_H_

#include "xy_atc_interface.h"
#include "atc_ps_def.h"

#include "xy_lpm.h"
#include "oss_nv.h"
#include "at_ps_proxy.h"
#include "xy_walltime.h"

#define Test_True     1
#define Test_False    0

#define D_THEARD_TEST_CMD_PRIO            D_THEARD_ATC_AP_PRIO + 1
#define D_THEARD_TEST_RECEIVE_PRIO        D_THEARD_ATC_AP_PRIO - 1

#define D_THEARD_TEST_CMD_STACK         0x1000
#define D_THEARD_TEST_RECEIVE_STACK     0x1000

#define START_PS_TEST_FLG               0
//#define RESTART_PS_TEST_FLG             1
//#define END_PS_TEST_FLG                 2
#define PS_TEST_EVENT_MAX               1


#define PS_TEST_AT_MSG                  0
#define PS_TEST_AT_TIME_OUT             1
#define PS_TEST_TIME_OUT                2
#define PS_TEST_MSG_MAX                 3

#define PS_TEST_WAITURC_NOT_CKECK_OK    0
#define PS_TEST_WAITURC_NEED_CKECK_OK   1

typedef struct 
{
    unsigned char   ucEventId;
    void            (*MsgProc)(unsigned char *);
} ST_PS_TEST_MSG_RCV_PROC_TABLE;

typedef struct st_test_ap_msg_node
{
    struct st_test_ap_msg_node*     next;
    unsigned long           ulMsg;
} ST_TEST_AP_MSG_NODE;

typedef struct
{
    osMutexId_t             msgMutex;
    osSemaphoreId_t         msgSemaPhore;
    unsigned char           ucCnt;
    ST_TEST_AP_MSG_NODE*     head;
    ST_TEST_AP_MSG_NODE*     last;
} ST_TEST_AP_MSG_INFO;

typedef struct
{
    unsigned char                ucMsgName;
    unsigned short                usDataLen;
    unsigned char*              ucData;
} ST_RECV_DATA_STRU;

typedef struct
{
    osMutexId_t             mutex;
#define D_TEST_RSP_MAX_BUF_SIZE          100
    unsigned short          usRspLen;
    unsigned char*          aucAtRspBuf;
} ST_TEST_AT_RSP_INFO;

typedef struct
{
    unsigned char          ucEventID;
} ST_TEST_EVENT_HEAD;

typedef struct
{
    unsigned char           ucEventID;
    unsigned short          usRspLen;
    unsigned char*          aucAtRspBuf;
} ST_TEST_AT_URC_INFO;

typedef struct
{
    osMutexId_t                 mutex;
    unsigned char*              pRspBuf;
} ST_TEST_MSG_RCV_INFO;

typedef struct
{
    struct ST_TEST_KEY_INFO*     next;
    unsigned int*           puiMsg;  
} ST_TEST_KEY_INFO;

typedef struct
{
    osMutexId_t                     Mutex;
    #define PS_TEST_START_VALUE        1
    #define PS_TEST_GET_RIGHT_RCV      2
    #define PS_TEST_NEED_WAIT_OK       4
    #define PS_TEST_NEED_WAIT_AT       8
    unsigned char                   ucShareInfo;  //bit0-4   1:ucPSTestStartFlg 2:ucGetATRcvFlg 3:ucNeedWaitOKFlg 4:ucUrcMonitorFlg
} ST_TEST_SHARE_INFO;

typedef struct
{
    osMutexId_t                     Mutex;
    unsigned char                   rsrp;
    unsigned char                   rssi;
} ST_TEST_CP_STATE;

typedef struct
{
    osTimerId_t                     tTestCaseTime;
    osTimerId_t                     tTestAtTime;
    osSemaphoreId_t                 ATSemaPhore;
    unsigned char                   ucPSTestNum;
    unsigned char                   ucPSTestFailTimers;
    unsigned char                   ucATNoAnswtime;
    ST_TEST_SHARE_INFO              stTestShareInfo;
    ST_TEST_AP_MSG_INFO             stTestSendMsgInfo;
    ST_TEST_AP_MSG_INFO             stTestReceiveMsgInfo;
    ST_TEST_AT_RSP_INFO             stTestWaitAtKey;
    ST_TEST_MSG_RCV_INFO            stTestMSGRcv;
    ST_TEST_CP_STATE                stTestCpInfo;
} ST_PS_TEST_DEMO_INFO;

typedef struct
{
    osMutexId_t             msgMutex;
    unsigned short           usCnt;
    ST_TEST_AP_MSG_NODE*     head;
    ST_TEST_AP_MSG_NODE*     last;
} ST_TEST_AP_QUEST_INFO;

#define  PS_TEST_LOG_FILE       "x:\\sd\\ps_test_log"
typedef struct
{

    unsigned char                   file_num;
    ST_TEST_AP_QUEST_INFO           stTestQuestInfo;
} ST_PS_TEST_SAVE_SD_INFO;

extern ST_PS_TEST_SAVE_SD_INFO g_PSTestSDInfo;
extern ST_PS_TEST_DEMO_INFO g_PSTestDemoInfo;

extern void PS_TEST_TASK_INIT();
#endif
#endif
