/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "xy_at_api.h"
#include "xy_system.h"
#include "xy_ps_api.h"
#include "oss_nv.h"

//射频驱动用
int at_SRRC_req(char *at_buf, char **prsp_cmd)
{
	uint8_t val;
	/* AT+SRRC=xxx */
	if (g_req_type == AT_CMD_REQ)
	{
		if (at_parse_param("%d(0-255)", at_buf, &val) != XY_OK)
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			return AT_END;
		}
        g_softap_fac_nv->srrc = val;
        SAVE_FAC_PARAM(srrc);
	}
	/* AT+SRRC?  */
	else if (g_req_type == AT_CMD_QUERY)
	{
		*prsp_cmd = xy_malloc(32);
		snprintf(*prsp_cmd, 32, "%d", g_softap_fac_nv->srrc);
	}
	else
	{
		*prsp_cmd = AT_PLAT_ERR(XY_Err_Parameter);
	}

    return AT_END;
}

#if VER_QUEC
int at_QRFTESTMODE_req(char *at_buf, char **prsp_cmd)
{
	int modeVal = 0;
    uint8_t cfunVal = 0;
	if (g_req_type == AT_CMD_REQ)
	{
		if (at_parse_param("%d[0-255]", at_buf, &modeVal) != XY_OK)
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			return AT_END;
		}
        if(modeVal == RF_FTM)
        {
            cfunVal = 5;
        }
        else
        {
            cfunVal = 1;
        }
        xy_set_rfmode(modeVal);
        xy_cfun_excute(cfunVal);
	}
	else if (g_req_type == AT_CMD_QUERY)
	{
	    xy_get_rfmode(&modeVal);
		*prsp_cmd = xy_malloc(50);
		snprintf(*prsp_cmd, 50, "\r\n+QRFTESTMODE:%d\r\n\r\nOK\r\n",modeVal);
	}
#if (AT_TEST_OFF!=1)
    else if (g_req_type == AT_CMD_TEST)
	{
		*prsp_cmd = xy_malloc(50);
		snprintf(*prsp_cmd, 50, "\r\n+QRFTESTMODE:(0-1)\r\n\r\nOK\r\n");
	}
#endif
	else
	{
		*prsp_cmd = AT_PLAT_ERR(XY_Err_Parameter);
	}
    return AT_END;
}
#endif