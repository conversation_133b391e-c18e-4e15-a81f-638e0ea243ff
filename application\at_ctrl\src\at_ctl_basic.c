#include "at_ps_api.h"
#include "at_ps_proxy.h"
#include "icc_msg.h"
#include "xy_system.h"
#include "xy_at_api.h"
#include "oss_nv.h"
#if ATCTL_EN
#include "at_ctl.h"
#include "xy4100_ll_uart.h"
#endif /* ATCTL_EN */

/**
 * @brief 仅限芯翼内部使用！进行BSP层面调试，直接往外设写调试类URC，可能乱码或丢字符
 * @param data [IN] 调试的URC信息，例如 "\r\n+DBGINFO:NV ERROR\r\n"
 * @warning  执行AT+NV=SET,CLOSEDEBUG,0后才会输出，该接口仅用于发送调试信息，不得发送正常功能性AT命令.
 * @attention 业务模块禁止使用该接口，而应使用xy_printf打印接口。
 */
void send_debug_str_to_ext(char *buf)
{
#if ATCTL_EN
	if (g_softap_fac_nv->off_debug == 1 || osKernelGetState() == osKernelInactive)
		return;

	/*一旦进IDLE，USB端口不可用，此时再调用可能因为互斥量等断言死机*/
	if (osKernelIsRunningIdle() != osOK)
	{
		if (g_softap_fac_nv->urc_cfg & (1 << AT_LPUART_FD))
		{
			at_posix_write(AT_LPUART_FD, (char *)buf, strlen(buf));
			at_posix_write(AT_CMUX1_FD, (char *)buf, strlen(buf));
			at_posix_write(AT_CMUX2_FD, (char *)buf, strlen(buf));
		}
#if UART_AT
		if (g_softap_fac_nv->urc_cfg & (1 << AT_UART_FD))
		{
			at_posix_write(AT_UART_FD, (char *)buf, strlen(buf));
			at_posix_write(AT_CMUX1_FD, (char *)buf, strlen(buf));
			at_posix_write(AT_CMUX2_FD, (char *)buf, strlen(buf));
		}
#endif /* UART_AT */
		if (g_softap_fac_nv->urc_cfg & (1 << AT_USB_FD))
		{
			at_posix_write(AT_USB_FD, (char *)buf, strlen(buf));
		}

		if (g_softap_fac_nv->urc_cfg & (1 << AT_USB_MODEM_FD))
		{
			at_posix_write(AT_USB_MODEM_FD, (char *)buf, strlen(buf));
		}
	}
	/*idle线程不能调用posix接口，因为有互斥量申请，会断言*/
	else
	{
		if (g_softap_fac_nv->urc_cfg & (1 << AT_LPUART_FD))
		{
			LL_UART_StringPut(LPUART,(int8_t *)buf);
			LL_UART_WaitTxDone(LPUART);
		}
	}
#else
    UNUSED_ARG(buf);
#endif /* ATCTL_EN */
}

/* CP通知AP释放AP申请的内存 */
void icc_at_free(void* data, size_t len)
{
    xy_assert(len == sizeof(uint32_t));
    char *addr = (char *)(size_t)(*(uint32_t *)data);
    xy_free(addr);
}

void at_init(void)
{
    /* 注册PS AT核间通信处理回调 */
    icc_at_channel_register(ICC_AT_FREE, (icc_cb_t)icc_at_free);
    icc_at_channel_register(ICC_AT_PSMSG, (icc_cb_t)SendShmMsg2AtcAp);

    /* ps回调注册，该接口必须在内核主线程中执行 */
	ps_urc_register_callback_init();

#if ATCTL_EN
    at_ctl_init(); /* at框架初始化 */
#endif /* ATCTL_EN */
}

application_init(at_init, APP_STARTUP_PRIORITY_HIGH);