/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#if VER_QUEC
#include "app_basic_config.h"
#include "xy_at_api.h"
#include "oss_nv.h"
#include "xy_flash.h"
#include "xy_lpm.h"
#include "xy_ps_api.h"
#include "pdp_dial.h"
#include "xy_sleep_lock.h"
#include "xy_system.h"
#include "xy_tcpip_api.h"
#include "softap_nv.h"
#include "hal_adc.h"
#include "at_fota.h"
#include "pin_ctl.h"


//AT+QURCCFG="urcport"[,<urc_port_value>]
int at_QURCCFG_req(char *at_buf, char **prsp_cmd)
{
	if(g_req_type == AT_CMD_REQ)
	{
		char urcport[10] = {0};
		char urc_port_value[20] = {0};
		
		if(at_parse_param("%10s,%20s", at_buf, urcport, urc_port_value) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }
		
		if(at_strcasecmp(urcport,"urcport") != 1)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

		// 查询参数配置	
		if (strlen(urc_port_value) == 0)
		{
			if (g_softap_fac_nv->urc_cfg & (1 << AT_LPUART_FD) || 
				g_softap_fac_nv->urc_cfg & (1 << AT_UART_FD))
				strcpy(urc_port_value, "uart1");
			else if (g_softap_fac_nv->urc_cfg & (1 << AT_USB_FD))
				strcpy(urc_port_value, "usbat");
			else if (g_softap_fac_nv->urc_cfg & (1 << AT_USB_MODEM_FD))
				strcpy(urc_port_value, "usbmodem");
			else
				strcpy(urc_port_value, "error");

			*prsp_cmd = xy_malloc(50);
			snprintf(*prsp_cmd, 50, "\"urcport\",\"%s\"",urc_port_value);
		}
		// 设置URC输出端口
		else if (at_strcasecmp(urc_port_value, "usbat") == 1)
		{
#if !USR_CUSTOM4			
			g_softap_fac_nv->urc_cfg = (1 << AT_USB_FD);
			SAVE_FAC_PARAM(urc_cfg);
#endif			
		}
		else if (at_strcasecmp(urc_port_value, "usbmodem") == 1)
		{
			g_softap_fac_nv->urc_cfg = (1 << AT_USB_MODEM_FD);
			SAVE_FAC_PARAM(urc_cfg);
		}
		else if (at_strcasecmp(urc_port_value, "uart1") == 1)
		{
			g_softap_fac_nv->urc_cfg = (1 << AT_LPUART_FD);
			SAVE_FAC_PARAM(urc_cfg);
		}
		else
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        }
	}
#if (!AT_TEST_OFF)
	else if (g_req_type == AT_CMD_TEST)
	{
		*prsp_cmd = xy_malloc(70);
		snprintf(*prsp_cmd, 70, "\"urcport\",(\"usbat\",\"usbmodem\",\"uart1\")");
	}
#endif
	else
	{
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

	return AT_END;
}
extern int set_nat_work_mode(int mode);
int at_QCFG_req(char *at_buf, char **prsp_cmd)
{
#define QCFG_URC_HEAD "\r\n+QCFG: "
    if (g_req_type == AT_CMD_REQ)
    {
		char param1[20] = {0};
		int val = -1;

		if (at_parse_param("%20s,", at_buf, param1) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }
		// AT+QCFG="usbnet"[,<net>] 配置网卡接口
		if (at_strcasecmp(param1, "usbnet"))
		{
			if (at_parse_param(",%d[1|3|31]", at_buf, &val) != XY_OK)
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }
			if (val == -1)
			{
				*prsp_cmd = xy_malloc(50);
				if ((g_softap_fac_nv->usb_mode&0x3F) == 0x1E)
				{
				  sprintf(*prsp_cmd, "\"usbnet\",%d", 1);
				}else if ((g_softap_fac_nv->usb_mode&0x3F) == 0x1D)
				{
				  sprintf(*prsp_cmd, "\"usbnet\",%d", 3);
				}else if ((g_softap_fac_nv->usb_mode&0x3F) == 0x1F)
				{
				  sprintf(*prsp_cmd, "\"usbnet\",%d", 31);
				}
			}	
			else
			{
				if (val == 1)//ecm+三串口
				{
				  g_softap_fac_nv->usb_mode = 0x1E;
				  SAVE_FAC_PARAM(usb_mode);

				}else if (val == 3)//rndis+三串口
				{
				  g_softap_fac_nv->usb_mode = 0x1D;
				  SAVE_FAC_PARAM(usb_mode);
				}else if (val == 31)//自适应
				{
				  g_softap_fac_nv->usb_mode = 0x1F;
				  SAVE_FAC_PARAM(usb_mode);
				}
			}
		}
		//"fast/poweroff"[,<pin>,<enable>]   [BG95]DTR引脚的配置，但仅用于睡眠控制，不参与唤醒
		else if (at_strcasecmp(param1, "fast/poweroff"))
		{
			uint8_t pin = -1;
			uint8_t enable = 0;
			
			if (at_parse_param(",%d[],%d[0|1]", at_buf,&pin,&enable) != XY_OK)
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }
			/*查询+QCFG: "fast/poweroff",<pin>,<enable>*/
			if (pin == -1)
			{
				*prsp_cmd = xy_malloc(128);
        
		        sprintf(*prsp_cmd, "\r\n+QCFG: \"fast/poweroff\",%d,%d",(g_softap_fac_nv->dtrpin_ctl&0x0F),(g_softap_fac_nv->dtrpin_ctl==0 ? 0:1));
			}	
			else
			{
				if(enable == 1)
				{
					g_softap_fac_nv->dtrpin_ctl = 0X50 + pin; 
				}
				else
				{
					g_softap_fac_nv->dtrpin_ctl = 0;
				}
			}
		}
		// AT+QCFG="ntp"[,<cnt>,<interval>] 设置NTP最大重传次数和间隔
		else if (at_strcasecmp(param1, "ntp"))
		{
			int ntp_cnt = -1;
			int ntp_interval = -1;

			if (at_parse_param(",%d[1-10],%d[5-60]", at_buf, &ntp_cnt, &ntp_interval) != XY_OK)
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }

			if (ntp_cnt == -1 && ntp_interval == -1)
			{
				*prsp_cmd = xy_malloc(60);
				sprintf(*prsp_cmd, "\"ntp\",%d,%d",g_app_basic_cfg.ntp_cnt,g_app_basic_cfg.ntp_interval);
			}
			else if(ntp_cnt == -1 || ntp_interval == -1)
			{
				*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			}
			else
			{
				g_app_basic_cfg.ntp_cnt = (uint8_t)ntp_cnt;
				SAVE_APP_BASIC_CFG_PARAM(ntp_cnt);

				g_app_basic_cfg.ntp_interval = (uint8_t)ntp_interval;
				SAVE_APP_BASIC_CFG_PARAM(ntp_interval);
			}
		}

		// AT+QCFG="urc/ri/other"[,<typeRI>[,<pulse_duration>[,<pulse_count>]]] 设置其他URC上报时的RI行为
		else if (at_strcasecmp(param1, "urc/ri/other"))
		{
			char typeRI[6] = {0};
			uint32_t ri_enable = 0;
			uint32_t pulse_duration = 0;
			uint32_t pulse_count = 0;

			if (at_parse_param(",%6s,%d[5-2000],%d[1-5]", at_buf, typeRI, &pulse_duration, &pulse_count) != XY_OK)
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }
			
			if (strlen(typeRI) == 0)
			{
				*prsp_cmd = xy_malloc(60);
				Get_RI_Pulse(&ri_enable, &pulse_duration, NULL, &pulse_count);
				if(ri_enable)
					sprintf(*prsp_cmd, "\"urc/ri/other\",\"pulse\",%d,%d", pulse_duration, pulse_count);
				else
					sprintf(*prsp_cmd, "\"urc/ri/other\",\"off\"" );
			}					
			else if (at_strcasecmp(typeRI, "pulse"))
			{
				Set_RI_Pulse(1, pulse_duration, (pulse_duration * 2), pulse_count);
			}
			else if (at_strcasecmp(typeRI, "off"))
			{
				Set_RI_Pulse(0,0,0,0);
			}
			else 
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            }		
		}
		// AT+QCFG="urc/ri/smsincoming"[,<typeRI>[,<pulse_duration>[,<pulse_count>]]] 设置短信URC上报时的RI行为
		else if (at_strcasecmp(param1, "urc/ri/smsincoming"))
		{
			char typeRI[6] = {0};
			uint32_t ri_enable = 0;
			uint32_t pulse_duration_sms = 0;
			uint32_t pulse_count_sms = 0;

			if (at_parse_param(",%6s,%d[5-2000],%d[1-5]", at_buf, typeRI, &pulse_duration_sms, &pulse_count_sms) != XY_OK)
			{
				*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
				return AT_END;
			}

			if (strlen(typeRI) == 0)
			{
				*prsp_cmd = xy_malloc(60);
				Get_RI_Pulse(&ri_enable, &pulse_duration_sms, NULL, &pulse_count_sms);
				if(ri_enable)
					sprintf(*prsp_cmd, "\"urc/ri/smsincoming\",\"pulse\",%d,%d", pulse_duration_sms, pulse_count_sms);
				else
					sprintf(*prsp_cmd, "\"urc/ri/smsincoming\",\"off\"");
			}
			else if (at_strcasecmp(typeRI, "pulse"))
			{
				Set_RI_Pulse(1,pulse_duration_sms,(pulse_duration_sms * 2),pulse_count_sms);
			}
			else if (at_strcasecmp(typeRI, "off"))
			{
				Set_RI_Pulse(0,0,0,0);
			}
			else
			{
				*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			}
		}
		// AT+QCFG="urc/ri/ring"[,<typeRI>[,<pulse_duration>[,<pulse_count>]]] 设置来电URC上报时的RI（振铃指示器）行为
		else if (at_strcasecmp(param1, "urc/ri/ring"))
		{
			char typeRI[6] = {0};
			uint32_t ri_enable = 0;
			uint32_t pulse_duration = 0;
			uint32_t pulse_count = 0;

			if (at_parse_param(",%6s,%d[5-2000],%d[1-5]", at_buf, typeRI, &pulse_duration, &pulse_count) != XY_OK)
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }
			
			if (strlen(typeRI) == 0)
			{
				*prsp_cmd = xy_malloc(60);
				Get_RI_Pulse(&ri_enable, &pulse_duration, NULL, &pulse_count);
				if(ri_enable)
					sprintf(*prsp_cmd, "\"urc/ri/ring\",\"pulse\",%d,%d", pulse_duration, pulse_count);
				else
					sprintf(*prsp_cmd, "\"urc/ri/ring\",\"off\"");
			}				
			else if (at_strcasecmp(typeRI, "pulse"))
			{
				Set_RI_Pulse(1,pulse_duration , (pulse_duration * 2), pulse_count);
			}
			else if (at_strcasecmp(typeRI, "off"))
			{
				Set_RI_Pulse(0,0,0,0);
			}
			else 
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            }
		}
		// AT+QCFG="risignaltype",[<RI_signal_type>] 指定RI（振铃指示器）信号输出载体
		else if (at_strcasecmp(param1,"risignaltype"))
		{
			char RI_signal_type[12] = {0};
			if(at_parse_param(",%12s", at_buf, RI_signal_type) != XY_OK)
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }
			if (strlen(RI_signal_type) == 0)
			{
				*prsp_cmd = xy_malloc(50);
				if(g_ri_Carrier == 0)
					sprintf(*prsp_cmd, "\"risignaltype\",\"respective\"", "");
				else
					sprintf(*prsp_cmd, "\"risignaltype\",\"physical\"");
			}
			else if (at_strcasecmp(RI_signal_type, "respective")) 
			{
				g_ri_Carrier = 0;
			}
			else if (at_strcasecmp(RI_signal_type, "physical"))
			{
				g_ri_Carrier = 1;
			}
			else
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            }		
		}
		// AT+QCFG="urcdelay"[,<time>] 设置自RI脉冲开始时起的URC延迟时间
		else if (at_strcasecmp(param1, "urcdelay"))
		{
			if(at_parse_param(",%d[0-120]", at_buf, &val) != XY_OK)
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }
			if (val == -1)
			{
				*prsp_cmd = xy_malloc(50);
				sprintf(*prsp_cmd, "\"urcdelay\",%d", 0);
			}
			else
			{
				// 设置自RI脉冲开始时起的URC延迟时间
			}			
		}
		// AT+QCFG="urc/cache"[,<enable>] 打开或关闭URC缓存功能
		else if (at_strcasecmp(param1, "urc/cache"))
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			return AT_END;
		}
		// AT+QCFG="ppp/termframe"[,<flag>] 启用或禁用模块自行挂断PPP时的PPP TERM帧发送功能
#if XY_PPP
		else if (at_strcasecmp(param1, "ppp/termframe"))
		{
			if (at_parse_param(",%d[0-1]", at_buf, &val) != XY_OK)
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }
			if (val == -1)
			{
				*prsp_cmd = xy_malloc(50);
				sprintf(*prsp_cmd, "\"ppp/termframe\",%d", g_app_basic_cfg.ppp_term_send);
			}	
			else
			{
				// 启用或禁用模块自行挂断PPP时的PPP TERM帧发送功能
                g_app_basic_cfg.ppp_term_send = (uint8_t)val;
                SAVE_APP_BASIC_CFG_PARAM(ppp_term_send);
			}
		}
#endif /* XY_PPP */
		// AT+QCFG="nat"[,<nat>] 配置网卡工作模式
		else if (at_strcasecmp(param1, "nat"))
		{
			if (at_parse_param(",%d[0-1]", at_buf, &val) != XY_OK)
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }
			if (val == -1)
			{
				*prsp_cmd = xy_malloc(50);
				sprintf(*prsp_cmd, "\"nat\",%d", (g_softap_fac_nv->ue_ip4_type) ? 1 : 0);
			}	
			else
				set_nat_work_mode(((val) ? 2 : 0));
		}
		// AT+QCFG="uart2ipr"[,<ipr>] 配置UART2的波特率
		else if (at_strcasecmp(param1, "uart2ipr"))
		{
#if UART_AT
			if (at_parse_param(",%d[4800|9600|19200|38400|57600|115200|230400|460800|921600|1000000|2000000]", at_buf, &val) != XY_OK)
#else
			if (at_parse_param(",%d[4800|9600|19200|38400|57600|115200|230400|460800|921600|1000000]", at_buf, &val) != XY_OK)
#endif
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }
			if (val == -1)
			{
				*prsp_cmd = xy_malloc(50);
				sprintf(*prsp_cmd, "\"uart2ipr\",%d", 115200);
			}	
			else
			{
				// 设置波特率
			}
		}
		else if (at_strcasecmp(param1, "fota/cid"))
		{
			if (at_parse_param(",%d[]", at_buf, &val) != XY_OK)
			{
				*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
				return AT_END;
			}

			Qfota_context_t *QfotaContext = Qfota_get_context();
			if (QfotaContext == NULL)
			{
				*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
				return AT_END;
			}

			if(val == -1)//AT+QCFG="fota/cid"
			{
				*prsp_cmd = xy_malloc(50);
				sprintf(*prsp_cmd, "\"fota/cid\",%d", QfotaContext->cid);
			}
			else if (is_Ps_Cid_Vaild(val))
			{			
				QfotaContext->cid = val;
			}
			else
			{
				*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            	return AT_END;
			}
		}
		else if (at_strcasecmp(param1, "fota/times"))
		{

		}
		else if (at_strcasecmp(param1, "fota/path"))
		{

		}
		// AT+QCFG="airplanecontrol"[,<enable>]
		else if (at_strcasecmp(param1, "airplanecontrol"))
		{
			static uint32_t s_mode = 0;
			if (at_parse_param(",%d[0|1]", at_buf, &val) != XY_OK)
			{
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }
			if (val == -1)
			{
				*prsp_cmd = xy_malloc(50);
				sprintf(*prsp_cmd, "\"airplanecontrol\",%d", s_mode);
			}	
			else
			{
				s_mode = val;
			}
		}
	
#if CDP_LWM
		//AT+QCFG="LWM2M/lifetime",900
		else if (at_strcasecmp(param1, "LWM2M/Lifetime"))
		{
			if (at_parse_param(",%d[0-2592000]", at_buf, &val) != XY_OK)
			{
                *prsp_cmd = AT_PLAT_CME_ERR(ATERR_PARAM_INVALID);
                return AT_END;
            }
			cdp_module_init();
			if (val == -1)
			{
				*prsp_cmd = xy_malloc(48);
				snprintf(*prsp_cmd, 48, "\"LWM2M/Lifetime\",%ld",  g_cdp_config_data->cdp_lifetime);
			}	
			else
			{
				if(is_cdp_running() || CDP_NEED_RECOVERY(g_cdp_session_info->cdp_lwm2m_event_status))    
				{
					*prsp_cmd = AT_PLAT_CME_ERR(ATERR_NOT_ALLOWED);
					return AT_END;
				}
			    g_cdp_config_data->cdp_lifetime = (val > 0 && val <= 900)?900:val;
			    app_write_fs(CDP_CONFIG_NVM_FILE_NAME,(void*)g_cdp_config_data,sizeof(cdp_config_nvm_t));
			}
				
		}
		//AT+QCFG="LWM2M/EndpointName",123456789
		else if (at_strcasecmp(param1, "LWM2M/EndpointName"))
		{
			uint8_t *sub_param = NULL;
			if (at_parse_param(",%p", at_buf, &sub_param) != XY_OK)
			{
                *prsp_cmd = AT_PLAT_CME_ERR(ATERR_PARAM_INVALID);
                return AT_END;
            }

			cdp_module_init();

			if(sub_param == NULL)
			{
				*prsp_cmd = xy_malloc(50 + strlen(g_cdp_session_info->endpointname));
				snprintf(*prsp_cmd, 50 + strlen(g_cdp_session_info->endpointname), "\"LWM2M/EndpointName\",\"%s\"",  g_cdp_session_info->endpointname);
			}
			else if(strlen(sub_param) > 252) //endpoint 长度限制在252个字节，SDK内部组包会添加"ep="导致溢出
			{
				*prsp_cmd = AT_PLAT_CME_ERR(ATERR_NOT_ALLOWED);
				return AT_END;
			}
			else
			{
			
				if(cdp_set_endpoint_name(sub_param))
				{
					*prsp_cmd = AT_PLAT_CME_ERR(ATERR_PARAM_INVALID);
					return AT_END;
				}
			}
		}
		//AT+QCFG="LWM2M/BindingMode",1
		else if (at_strcasecmp(param1, "LWM2M/BindingMode"))
		{
			if (at_parse_param(",%d[1-2]", at_buf, &val) != XY_OK)
			{
                *prsp_cmd = AT_PLAT_CME_ERR(ATERR_PARAM_INVALID);
                return AT_END;
            }

			cdp_module_init();

			if (val == -1)
			{
				*prsp_cmd = xy_malloc(48);
				snprintf(*prsp_cmd, 48, "\"LWM2M/BindingMode\",%1d", (g_cdp_config_data->binding_mode == 0)?1:(g_cdp_config_data->binding_mode));
			}	
			else
			{
				//移远版本随时可设置
				g_cdp_config_data->binding_mode = val;
				app_write_fs(CDP_CONFIG_NVM_FILE_NAME,(void*)g_cdp_config_data,sizeof(cdp_config_nvm_t));
			}
				
		}
#endif
        else
        {
            return AT_FORWARD;
        }
    }
#if (!AT_TEST_OFF)
	else if (g_req_type == AT_CMD_TEST)
	{
        int n;
        *prsp_cmd = xy_malloc2(640);
        if (*prsp_cmd == NULL)
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_NoMemory);
            return AT_END;
        }

        n = sprintf(*prsp_cmd, "%s\"gprsattach\",(0,1),(0,1)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"nwscanmode\",(0-3)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"nwscanseq\",(0-12)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"roamservice\",(1,2),(0,1)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"servicedomain\",(0-2),(0,1)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"band\",(0-200),(0-7FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"urc/ri/other\",(\"off\",\"pulse\"),(5-2000),(1-5)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"urcdelay\",(0-120)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"urc/cache\",(0,1)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"usbnet\",(1,3)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"ppp/termframe\",(0,1)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"airplanecontrol\",(0,1),(0,1)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"urc/ri/ring\",(\"off\",\"pulse\"),(5-2000),(1-5)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"risignaltype\",(\"respective\",\"physical\")", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "%s\"uart2ipr\",(4800,9600,19200,38400,57600,115200,230400,460800,921600,1000000)", QCFG_URC_HEAD);
        n += sprintf(*prsp_cmd + n, "\r\n+QCFG: \"nat\",(0,1)");
    }
#endif /* AT_TEST_OFF */
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

	return AT_END;	
}


// AT+ECADC=<option>  option取值：temp,vbat,all
int at_ECADC_req(char *at_buf, char **prsp_cmd)
{
    if(g_req_type == AT_CMD_REQ)
    {
        //参数检查
        uint32_t channel;
        char cmd[5] = {0};
        if (at_parse_param("%5s()", at_buf, &cmd) != XY_OK)
        {
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }
        if(at_strcasecmp(cmd, "temp"))
        {
            *prsp_cmd = xy_malloc(64);
            snprintf(*prsp_cmd, 64, "TEMP,%d", HAL_ADC_Single_GetValue(HAL_ADC_TEMP));
        }
        else if(at_strcasecmp(cmd, "vbat"))
        {
            *prsp_cmd = xy_malloc(64);
            snprintf(*prsp_cmd, 64, "VBAT,%d", HAL_ADC_Single_GetValue(HAL_ADC_VBAT));
        }
        else if(at_strcasecmp(cmd, "all"))
        {
            *prsp_cmd = xy_malloc(64);
            snprintf(*prsp_cmd, 64, "TEMP,%d,VBAT,%d", HAL_ADC_Single_GetValue(HAL_ADC_TEMP), HAL_ADC_Single_GetValue(HAL_ADC_VBAT));
        }
        else
        {
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        }
    }
#if (!AT_TEST_OFF)
    else if(g_req_type == AT_CMD_TEST)
    {
        *prsp_cmd = xy_malloc(64);
        sprintf(*prsp_cmd, "temp,vbat,all");
    }
#endif
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

    return AT_END;
}


#define QADC0_PAD	(g_softap_fac_nv->pad_adc0) //ADC0对应的实际ADC通道号
#define QADC1_PAD	(g_softap_fac_nv->pad_adc1) //ADC1对应的实际ADC通道号
// AT+QADC=<port>  port取值0-1，依次表示为通道ADC1-1
int at_QADC_req(char *at_buf, char **prsp_cmd)
{
    if(g_req_type == AT_CMD_REQ)
    {
        //参数检查
        uint32_t channel;
        if (at_parse_param("%d(0-1)", at_buf, &channel) != XY_OK)
        {
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

		//获取实际ADC通道号
		HAL_ADC_ChannelTypeDef channelID = (channel) ? QADC1_PAD : QADC0_PAD;

		//判断实际通道号是否无效，若无效则返回错误提示，有效则采集ADC值
		if(channelID == 255)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			return AT_END;
        }

		//采集ADC值
        int16_t adcValue = HAL_ADC_Single_GetValue(channelID);
        if(adcValue != -32768)
        {
            *prsp_cmd = xy_malloc(64);
            sprintf(*prsp_cmd, "%u,%d", channel, adcValue);
        }
        else
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        }
    }
	/*AT+QADC*/
	else if(g_req_type == AT_CMD_ACTIVE)
    {
        *prsp_cmd = xy_malloc(64);
        sprintf(*prsp_cmd, "%d,%d,%d",HAL_ADC_Single_GetValue(HAL_ADC_VBAT),HAL_ADC_Single_GetValue(QADC0_PAD),HAL_ADC_Single_GetValue(QADC1_PAD));
    }
#if (!AT_TEST_OFF)
    else if(g_req_type == AT_CMD_TEST)
    {
        *prsp_cmd = xy_malloc(64);
        sprintf(*prsp_cmd, "(0,1)");
    }
#endif
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

    return AT_END;
}


/*AT+QPOWD[=<n>[,<cfun val>]]  关闭模块。n取值：0/1：CFUN0或5后进入PD模式，RTC清空链表，只有外部PWRKEY方能唤醒SoC；2: 释放系统锁，CFUN0或5后进睡眠。AT命令、RTC、PWRKEY、WAKUP-PIN等可唤醒；3：立即强行进入PD模式，RTC清空链表，只有外部PWRKEY方能唤醒SoC*/
int at_QPOWD_req(char *at_buf, char **prsp_cmd)
{
	static int val = 0;
	int val1 = 0;
	
	if (g_req_type == AT_CMD_ACTIVE || g_req_type == AT_CMD_REQ)
	{
		if (at_parse_param("%d[0-5],%d[0|5]", at_buf, &val,&val1) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }
		/*CFUN0或5后进入PD模式，关RTC，只有外部PWRKEY方能唤醒SoC*/
		if(val == 0 || val == 1)
		{
			xy_cfun_excute(val1);
			appAtWriteImmediately(get_current_ttyFd(), AT_RSP_OK, strlen(AT_RSP_OK));
			set_sleep_mode(get_current_ttyFd(), FAST_OFF);
			return AT_ASYN;
		}
		/*释放系统锁，CFUN0或5后进睡眠。AT命令、RTC、PWRKEY、WAKUP-PIN等可唤醒*/
		else if(val == 2)
		{
			set_sleep_mode(get_current_ttyFd(), NORMAL_SLEEP);
			xy_cfun_excute(val1);
			
			*prsp_cmd = xy_malloc(64);
			snprintf(*prsp_cmd, 64, "\r\nOK\r\n\r\nPOWERED DOWN\r\n");
		}
		/*立即强行进入PD模式，关RTC，只有外部PWRKEY方能唤醒SoC*/
		else if(val == 3)  
		{
			appAtWriteImmediately(get_current_ttyFd(), AT_RSP_OK, strlen(AT_RSP_OK));
			set_sleep_mode(get_current_ttyFd(), FAST_OFF);
			return AT_ASYN;
		}
		/* AT+QPOWD=4 释放所有锁，包括DTR*/
		else if(val == 4)  
		{
			set_sleep_mode(get_current_ttyFd(), UNLOCK_ALL);
		}
		/*AT+QPOWD=5,<enable dtr interupt>  动态开关DTR中断能力*/
		else if(val == 5) 
		{
			extern void Enable_DTRPIN_Interrupt(uint32_t enable);
			Enable_DTRPIN_Interrupt(val1);	
		}
		else
		{
			xy_assert(0);
		}
	}
#if (!AT_TEST_OFF)
	else if(g_req_type == AT_CMD_TEST)
	{
	    *prsp_cmd = xy_malloc(40);
		snprintf(*prsp_cmd, 40, "(0,1,2,3)");
	}
#endif
	else if (g_req_type == AT_CMD_QUERY)
	{
		*prsp_cmd = xy_malloc(48);
		sprintf(*prsp_cmd, "%d",val);
	}
	else
	{
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

	return AT_END;
}


// AT+QSCLK=<n>[,<delay>[,<save>]]   睡眠锁的设置
int at_QSCLK_req(char *at_buf, char **prsp_cmd)
{
	int val = 0;
	int save=0,delay=0;  

	if (g_req_type == AT_CMD_REQ)
	{
		if (at_parse_param("%d(0-2),%d[1-255],%d[0-1]", at_buf, &val,&delay,&save) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

        if(val == 0)
		{
			sys_lock(WL_USR_SLEEP);
		}
		
		else if(val == 1)
		{
			sys_unlock(WL_USR_SLEEP);
		}
		
		else /*AT+QSCLK=2  关闭DTR能力，释放锁，可选设置AT延迟锁*/
		{
			if(delay != 0)
				set_AT_delaylock_time(delay);
			else
				set_AT_delaylock_time(5);

			extern void dtr_unlock(void);
			dtr_unlock();  /*DTR临时无效，强行释放DTR锁*/
			sys_unlock(WL_USR_SLEEP);
			at_delaylock_act();
			
			if(delay != 0)
			{
				g_softap_fac_nv->sleep_delay = delay;
				if(save == 1)
					SAVE_FAC_PARAM(sleep_delay);
			}
		}
		g_softap_var_nv->Qsclk_val = val;

		/*DTR不使用时，该命令退化为仅支持AT唤醒模式*/
		extern bool DtrIsUnitedATcmd();
		if(DtrIsUnitedATcmd() == true)
		{
			extern void Enable_DTRPIN_Interrupt(uint32_t enable);
			/*QSCLK=0时，当前持有系统锁，不支持DTR和AT唤醒*/
			if(g_softap_var_nv->Qsclk_val == 0)
			{
				Enable_DTRPIN_Interrupt(0);			
				Enable_AT_Wakup(0);
			}
			/*QSCLK=1时，当前释放系统锁，支持DTR唤醒，不支持AT唤醒*/
			else if(g_softap_var_nv->Qsclk_val == 1)
			{
				Enable_DTRPIN_Interrupt(1);
				Enable_AT_Wakup(0);
			}
			/*QSCLK=2时，当前释放系统锁，支持AT唤醒，不支持DTR唤醒*/
			else
			{			
				Enable_DTRPIN_Interrupt(0);			
				Enable_AT_Wakup(1);
			}
		}
	}
#if (!AT_TEST_OFF)
	else if (g_req_type == AT_CMD_TEST)
	{
		*prsp_cmd = xy_malloc(30);
		snprintf(*prsp_cmd, 30, "(0,1,2)");
	}
#endif
	else if (g_req_type == AT_CMD_QUERY)
	{
		uint8_t mode = 1;
		*prsp_cmd = xy_malloc(48);

		if(g_softap_var_nv->Qsclk_val != 0XFF)
			mode = g_softap_var_nv->Qsclk_val;
		else if(g_softap_fac_nv->sleep_mode == 0)
			mode = 0;
		else
			mode = 1;

		if(mode != 2)
			sprintf(*prsp_cmd, "%d", mode);
		else
		{
			if(g_softap_var_nv->delay_sec != 0)
				sprintf(*prsp_cmd, "%d,%d,0", mode,g_softap_var_nv->delay_sec);
			else
				sprintf(*prsp_cmd, "%d,%d,0", mode,g_softap_fac_nv->sleep_delay);
		}
	}
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

    return AT_END;
}

int at_CIPGSMLOC_req(char *at_buf, char **prsp_cmd)
{
	int type = 0, cid = 0;

	if (g_req_type == AT_CMD_REQ)
	{
		if (at_parse_param("%d(1-2),%d(1-3)", at_buf, &type, &cid) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

		*prsp_cmd = xy_malloc(100);
		if(type == 1) //返回查看精度、维度和时间
		{
			sprintf(*prsp_cmd, "\r\n+CIPGSMLOC: 0,000.0000000,000.0000000,0000/00/00,00:00:00");
		}
		else if(type == 2) //只查看时间
		{
			sprintf(*prsp_cmd, "\r\n+CIPGSMLOC: 0,0000/00/00,00:00:00");
		}
	}
	else if (g_req_type == AT_CMD_TEST)
	{
		*prsp_cmd = xy_malloc(50);
		sprintf(*prsp_cmd, "\r\n+CIPGSMLOC:(1-2),(1-3)");
	}	
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

    return AT_END;
}
#endif
