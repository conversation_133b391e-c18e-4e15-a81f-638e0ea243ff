#include "alsa_os.h"
#include "hal_trace.h"

alsa_os_thread_t alsa_os_thread_create(
    alsa_os_thread_func_t func,
    const char *name,
    uint32_t stack_size,
    alsa_os_priority_t priority,
    void *arg)
{
    osThreadAttr_t thread_attr;
    thread_attr.name = name;
    thread_attr.attr_bits = osThreadDetached;
    thread_attr.cb_mem = NULL;
    thread_attr.cb_size = 0U;
    thread_attr.stack_mem = NULL;
    thread_attr.stack_size = 8 * ((stack_size + 7) / 8);
    thread_attr.priority = priority;
    thread_attr.reserved = 0U;
    thread_attr.tz_module = 0U;
    return osThreadNew(func, arg, &thread_attr);
}

alsa_os_status_t alsa_os_thread_delete(alsa_os_thread_t thread_id)
{
    if (osThreadGetState(thread_id) == osThreadTerminated)
    {
        TRACE(0, "%s thread state terminated.", __func__);
        return 0;
    }
    if (osThreadGetState(thread_id) == osThreadInactive)
    {
        TRACE(0, "%s thread state inactive.", __func__);
        return 0;
    }
    return osThreadTerminate(thread_id);
}

alsa_os_mutex_t alsa_os_mutex_create(void)
{
    osMutexAttr_t mutex_attr;
    mutex_attr.name = NULL;
    mutex_attr.attr_bits = osMutexRecursive | osMutexPrioInherit;
    mutex_attr.cb_mem = NULL;
    mutex_attr.cb_size = 0U;
    return osMutexNew(&mutex_attr);
}

alsa_os_status_t alsa_os_mutex_wait(alsa_os_mutex_t mutex_id, uint32_t millisec)
{
    return osMutexAcquire(mutex_id, millisec);
}

alsa_os_status_t alsa_os_mutex_release(alsa_os_mutex_t mutex_id)
{
    return osMutexRelease(mutex_id);
}

alsa_os_status_t alsa_os_mutex_delete(alsa_os_mutex_t mutex_id)
{
    return osMutexDelete(mutex_id);
}

alsa_os_mq_t alsa_os_mq_create(uint32_t msg_count, uint32_t msg_size)
{
    osMessageQueueAttr_t mq_attr;
    mq_attr.name = NULL;
    mq_attr.attr_bits = 0U;
    mq_attr.cb_mem = NULL;
    mq_attr.cb_size = 0U;
    mq_attr.mq_mem = NULL;
    mq_attr.mq_size = 0U;
    return osMessageQueueNew(msg_count, msg_size, &mq_attr);
}

alsa_os_status_t alsa_os_mq_get(alsa_os_mq_t mq_id, void *msg_ptr, uint32_t millisec)
{
    return osMessageQueueGet(mq_id, msg_ptr, NULL, millisec);
}

alsa_os_status_t alsa_os_mq_put(alsa_os_mq_t mq_id, void *msg)
{
    return osMessageQueuePut(mq_id, msg, 0, 0);
}

alsa_os_status_t alsa_os_mq_delete(alsa_os_mq_t mq_id)
{
    return osMessageQueueDelete(mq_id);
}

alsa_os_semaphore_t alsa_os_semaphore_create(uint32_t max_count, uint32_t initial_count)
{
    osSemaphoreAttr_t sem_attr;
    sem_attr.name = NULL;
    sem_attr.attr_bits = 0U;
    sem_attr.cb_mem = NULL;
    sem_attr.cb_size = 0U;
    return osSemaphoreNew(max_count, initial_count, &sem_attr);
}

alsa_os_status_t alsa_os_semaphore_acquire(alsa_os_semaphore_t semaphore_id, uint32_t timeout)
{
    return osSemaphoreAcquire(semaphore_id, timeout);
}

alsa_os_status_t alsa_os_semaphore_release(alsa_os_semaphore_t semaphore_id)
{
    return osSemaphoreRelease(semaphore_id);
}

uint32_t alsa_os_semaphore_get_count(alsa_os_semaphore_t semaphore_id)
{
    return osSemaphoreGetCount(semaphore_id);
}

alsa_os_status_t alsa_os_semaphore_delete(alsa_os_semaphore_t semaphore_id)
{
    return osSemaphoreDelete(semaphore_id);
}
