#pragma once

 /**
 * @file at_tcpip_api.h
 * @brief tcpip at命令相关api接口，供客户开发相关at命令使用
 * @note 客户应尽可能使用该头文件提供的开发接口，避免直接使用lwip内部接口
 * @version 1.0
 * @date 2022-06-14
 * @copyright Copyright (c) 2022  芯翼信息科技有限公司
 */

#include <stdint.h>
#include "lwip/ip_addr.h"
#include "xy_tcpip_api.h"

/*******************************************************************************
 *                             Type definitions                                *
 ******************************************************************************/
typedef struct dns_cfg_param
{
	uint8_t contextID;		    // PDP场景ID
	uint8_t reserved[3];	
	char* pridns;				// 主dns
	char* secdns;				// 辅dns
} dns_cfg_param_t;

typedef enum dns_query_type
{
	QDNS = 0,
	CMDNS
} dns_query_type_t;

typedef struct dns_query_param
{
	char    *host;
	uint8_t  contextID;         // PDP场景ID
	uint8_t  answer_num;        // dns查询结果，<host>对应的IP地址个数
	uint8_t  query_type;		// dns查询AT命令类型，@ref dns_query_type_t
	uint16_t timeout;           // 重置dns超时时间	
	uint32_t ttl;               // dns查询结果，<host>对应的最后一个IP地址的生存时间	
	int 	 atHandle;
    ip_addr_t *ipaddr_list;     // 获取的ip地址链表，字符串形式
} dns_query_param_t;

typedef struct
{
	uint8_t  contextID;         // 关联的CID
	uint8_t  type;	            // 0：断开；1：连接
} usb_netdev_ctl_msg_t;

/*******************************************************************************
 *                       Global function declarations                          *
 ******************************************************************************/
int set_ipv4_dns_server(uint8_t cid, char *pre_dns, char *sec_dns);

int set_ipv6_dns_server(uint8_t cid, char *pre_dns, char *sec_dns);

/**
 * @brief 用于at命令内dns服务器地址配置
 * @param arg [IN] dns配置入参 @see @ref dns_cfg_param_t。客户可自行设定主辅dns，自行决定是否保存相关配置到文件系统中
 * @return 结果码参考 @see @ref xy_ret_Status_t 
 * @warning 此接口可直接更改lwip的原有的DNS配置，客户使用时，请务必要确认所设置DNS地址可靠
 */
int at_dns_config(dns_cfg_param_t *arg);

/**
 * @brief 用于at命令内异步方式查询ntp服务器网络时间
 * @param arg [IN] ntp解析请求入参 @see @ref ntp_query_param_t。客户可自行定制一些特性，如指定协议簇/重置ntp tmr超时时间等
 * @return 结果码参考 @see @ref xy_ret_Status_t
 * @note  使用该接口，默认使用芯翼的query_ntp_task异步线程进行ntp获取网络时间，客户需修改query_ntp_task中的at urc上报
 * 		  若客户有自己的异步处理线程，可以将该接口内部的query_ntp_task替换为自己的异步处理线程
 */
int at_query_ntp_async(ntp_query_param_t *arg);

/**
 * @brief 供相关AT命令异步方式触发USB网卡拨号，成功与否需要用户主动注册查询。特别注意的是，该API不负责PDP的激活去活操作，需要用户提前通过"AT+CIDACT"等命令进行PDP激活相关操作。
 * @param cid  [IN] PDP的上下文。范围：1~15。该参数指定一个目标PDP上下文，用于其他和PDP上下文相关的命令中
 * @param type [IN] USB网卡拨号连接与断开。0：断开USB网卡连接；1：连接USB网卡；
 * @return 结果码参考 @see @ref xy_ret_Status_t
 * @note  该接口为异步接口，使用后可调用gw_eth_is_ok()获取USB网卡拨号连接状态
 */
int at_usb_netdev_ctl_async(usb_netdev_ctl_msg_t *msg);

