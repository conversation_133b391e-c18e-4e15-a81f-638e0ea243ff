/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "at_com.h"
#include "at_context.h"
#include "at_ctl.h"
#include "at_error.h"
#include "at_utils.h"
#include "oss_nv.h"
#include "xy_system.h"

/*******************************************************************************
 *						   Global variable definitions						   *
 ******************************************************************************/
char g_req_type = AT_CMD_REQ;

/*******************************************************************************
 *						Local function implementations						   *
 ******************************************************************************/
static void at_send_wait_rsp_timeout(osTimerId_t timerId)
{
    xy_assert(timerId != NULL);
    ATCTRL_NET_LOG("timer:%p timeout", timerId);      
    xy_printf(0, PLATFORM_AP, WARN_LOG, "[at_send_wait_rsp_timeout]timer:%p timeout!!!",  timerId);
	debug_log_print("[at_send_wait_rsp_timeout]timer:%p timeout!!!\n", timerId);

    osMessageQueueId_t queue_Id = get_dync_queue_by_timer(timerId);

    char *data = AT_PLAT_CME_ERR(XY_Err_Timeout);
    uint32_t size = strlen(data);
    struct at_fifo_msg *msg = xy_malloc(sizeof(struct at_fifo_msg) + size + 1);
    memcpy(msg->data, data, size);
    *(msg->data + size) = '\0';
    osMessageQueuePut(queue_Id, &msg, 0, osWaitForever);
    xy_free(data);
    return;
}

static char *find_first_print_char(char *at_str, int len)
{
	char *str = at_str;
	while ((*str < '!' || *str > '}') && str < at_str + len)
	{
		str++;
	}
	return (str < at_str + len ? str : NULL);
}

/*仅供内部调试使用*/
static int get_handle_rst_vp(osMessageQueueId_t rcv_fifo, char *prefix, char *info_fmt, va_list vp)
{
	int at_err = 0;
    int ret = -1;
    char *end_tail;
    char *str_param;
    char *maohao = NULL;
    int rf_at = 0;
    char *outPutBuffer = NULL;
    bool isAllResultOutput = false;
    va_list oringianl_vp = vp;

    if (prefix != NULL && at_strcasecmp(prefix, "RF"))
        rf_at = 1;

    if( (strcmp(info_fmt, "%a") == 0) || (strcmp(info_fmt, "%A") == 0) )
    {
        outPutBuffer = (char *)va_arg(vp, char *);
        xy_assert(outPutBuffer != NULL);
        isAllResultOutput = true;
    }

    do
    {
        struct at_fifo_msg *rcv_msg = NULL;
        osMessageQueueGet(rcv_fifo, &rcv_msg, NULL, osWaitForever);
        char *rsp_at = rcv_msg->data;

        xy_printf(0, PLATFORM_AP, INFO_LOG, "[get_handle_rst_vp]user at recv rsp:%s", rsp_at);
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[get_handle_rst_vp]current vp:0x%x", vp);
        if (rsp_at == NULL)
            xy_assert(0);

        if (isAllResultOutput)
        {
            /* 用户申请的buffer长度不要超过1024字节 */
            strncat(outPutBuffer, rsp_at, USR_AT_RECV_BUFFER_SIZE - strlen(outPutBuffer) - 1);
            if (Is_Result_AT_str(rsp_at))
            {
                ret = XY_OK;
                break;
            }
            else
            {
            	continue;
            }
        }
        
		if (info_fmt != NULL && prefix != NULL)
		{
			//标准URC，前缀匹配成功
	        if (((str_param = at_prefix_strstr(rsp_at, prefix)) != NULL))
	        {
	            xy_assert(*str_param == ':' || *str_param == '=' || *str_param == '^');
	            str_param++;
	            if ((end_tail = strchr(str_param, '\r')) != NULL)
	                *end_tail = '\0';
	            else
	                xy_assert(0);
                ret = parse_param(info_fmt, str_param,NULL,NULL, AT_PARAM_PARSE_DEFAULT, &vp);
                *end_tail = '\r';
	        }
            else if (rf_at)
            {
				char *colon = NULL;
                /* 适配AT+RF命令结果不携带+RF前缀场景 */
                str_param = rsp_at;
                while (*str_param == '\r' || *str_param == '\n')
                {
                    str_param++;
                }
				if((colon = strchr(str_param, ':')) != NULL)
					str_param = colon + 1;
 	            if ((end_tail = strchr(str_param, '\r')) != NULL)
	                *end_tail = '\0';
	            else
	                xy_assert(0);
                ret = parse_param(info_fmt, str_param,NULL,NULL, AT_PARAM_PARSE_DEFAULT, &vp);
                *end_tail = '\r';
            }
			//不标准的URC，即中间结果没有前缀，检查到没冒号或逗号前没冒号，可以直接解析参数,如3,"do:it"
            else if ((str_param = find_first_print_char(rsp_at, strlen(rsp_at))) != NULL  && 
            ((maohao = strchr(str_param, ':')) == NULL || (maohao > strchr(str_param, ','))))
            {
            	char *ok_head = strstr(str_param, "OK\r");
                char *err_tag = strstr(str_param, "ERROR");
                end_tail = strchr(str_param, '\r');
	            if (ok_head != str_param && end_tail != NULL && err_tag == NULL)
	            {
                    /* +CME ERROR:xx 不需要被parse_param处理 */
	                *end_tail = 0;
					ret = parse_param(info_fmt, str_param,NULL,NULL, AT_PARAM_PARSE_DEFAULT, &vp);
	            	*end_tail = '\r';
	            }
	            else if (end_tail == NULL)
	                xy_assert(0);
	        }
		}
		//没有中间结果
        if (ret == -1)
        {
            if (Is_AT_Rsp_OK(rsp_at))
                ret = XY_OK;
            else if ((at_err = Get_AT_errno(rsp_at)) != XY_OK)
                ret = at_err;
        }
        //中间结果解析正确，但可能还未收到最终结果码，需要继续接收
        else if (ret == XY_OK && !Is_Result_AT_str(rsp_at))
        {
            ret = -1;
        }
   
        xy_free(rcv_msg);
        //如果某条AT命令有多条前缀相同的中间结果先后上报，解析完上一条后把可变参数的指针移到初始位置，否则发生内存被踩导致死机
        vp = oringianl_vp;
    } while (ret == -1);

    return ret;
}

/**
 * @brief  供业务应用发送AT命令并阻塞等待应答结果。仅能处理射频AT命令，3GPP命令请使用xy_atc_interface_call
 * @param  req_at   [IN] 完整的AT命令字符串，末尾需要包含\r
 * @param  info_fmt [IN] 中间上报结果解析使用的格式化字符串，支持%s %d %u,不支持其他格式化类型
 * @param  timeout  [IN] 命令执行最大超时时间，单位秒
 * @param  ... 可变参数,需要解析获取的参数
 * @return XY_OK：成功，错误返回错误码 @see @ref XY_PLAT_ERR_E
 * @warning 发送AT命令后该接口处于阻塞状态，不可在软定时器和RTC的回调中使用该接口!!!!!!!!!!!!
 * @example  char subcmd[10] = {0};
             int val1, val2;
             at_send_wait_rsp("AT+RF=MIPIREAD\r\n", "%s,%d,%d", 10, subcmd, &val1, &val2);
             解析出来的参数-->subcmd:MIPIREAD,val1:0,val2:255
             uint16_t vbat = 0;
             at_send_wait_rsp("AT+RF=GVBAT\r\n", ",%2d", 10, &vbat);
             解析出来的参数-->vbat:3743
 */
int at_send_wait_rsp(char *req_at, char *info_fmt, int timeout, ...)
{
    int at_errno = -1;
    char prefix[AT_CMD_PREFIX] = {0};
    at_context_t *ctx = NULL;
	osTimerAttr_t timer_attr = {0};
    timer_attr.name = "UserAt";
    va_list vp;
    va_start(vp, timeout);
    char *curTaskName = (char *)(osThreadGetName(osThreadGetId()));

    // xy_assert(timeout != 0);
    xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_send_wait_rsp]user at req:%s in task:%s", req_at, curTaskName);
    at_get_prefix_and_param(req_at, prefix, NULL);

    if (strcmp(prefix, "") == 0)
    {
        return XY_Err_Prefix;
    }

    /* 创建用户、虚拟AT上下文、超时定时器和消息队列 */
    ctx = get_dync_context();
    xy_assert(ctx != NULL);
    ctx->user_queue_Id = osMessageQueueNew(10, sizeof(void *), NULL);
    xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_send_wait_rsp]create ttyFd:%d, timer:%p, queue:%p for user:%s", ctx->fd, ctx->ctx_tmr, ctx->user_queue_Id, curTaskName);
    
    if (timeout != 0)
    {
        ctx->ctx_tmr = osTimerNew((osTimerFunc_t)(at_send_wait_rsp_timeout), osTimerOnce, NULL, &timer_attr);
        osTimerStart(ctx->ctx_tmr, timeout * 1000);
    }
    
    send_msg_2_atctl(AT_MSG_RCV_STR_FROM_FARPS, req_at, strlen(req_at), ctx->fd);
    xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_send_wait_rsp]vp oringin:0x%x", vp);
    at_errno = get_handle_rst_vp(ctx->user_queue_Id, prefix, info_fmt, vp);
    xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_send_wait_rsp]user at result:%d", at_errno);
    xy_assert(at_errno != -1);

    //Step1: delete the related queue
    if (ctx->user_queue_Id != NULL)
    {
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_send_wait_rsp]delete queue:%p for user:%s", ctx->user_queue_Id, curTaskName);
        void *elem = NULL;
        while (osMessageQueueGet(ctx->user_queue_Id, &elem, NULL, 0) == osOK)
        {
        	xy_free(elem);
        }
    	osMessageQueueDelete(ctx->user_queue_Id);
        ctx->user_queue_Id = NULL;
    }
    //Step2: delete the related timer
    if (ctx->ctx_tmr != NULL)
    {
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_send_wait_rsp]delete timer:%p for user:%s", ctx->ctx_tmr, curTaskName);
        osTimerDelete(ctx->ctx_tmr);
        ctx->ctx_tmr = NULL;
    }
    //Step3: delete the user context which register in the global context dictionary
    if (ctx != NULL)
    {
        dereg_at_dync_ctx(ctx->fd);
        xy_free(ctx);
    }
    
    va_end(vp);
    return at_errno;
}

/**
 * @brief  按照fmt格式解析每个参数值，类似scanf格式。其中参数解析返回值变量入参，必须是地址入参
 * @param fmt  [IN] AT命令参数对应的格式化字符串,其中()表示必选参数，[]表示可选参数。具体参数类型有：
 *
 *       %d,%1d,%2d分别对应int，char，short整形参数,支持16进制和10进制两类数值;后面还可以添加()表示必选参数，[]表示可选参数；括号内部可以使用-来指定参数值的上下限；
 *       如%d(0-100)表示可选4字节整形参数，取值范围为0到100，若解析该参数时发现不在此范围，会报错。括号内可以使用|来指定离散取值，
 *       如%d(0|0x20|0x100)表示可选4字节整形参数，取值为0/0x20/0x100三个值中的一个，若解析该参数时发现不是这三个值中的一个，会报参数错。
 *  
 *       %c，等效于%1d，解析uint8_t的字符型变量
 *
 *       %m，对应uint64_t整形参数
 * 
 *       %f，对应double浮点类型参数
 *
 *       %l，用法同%d，指示%p或%h对应字符串的传输长度，通常用于长度合法性检查；最多只能有一个%l，优先配合%h使用，若无%h，则配合%p使用，
 * 
 *       %s，对应字符串类型的参数；中间可以携带数字指示字符串缓存的内存大小，如%10s，若真实字符串长度大于9，则会报参数错误；
 *
 *       %p，对应长字符串类型的参数，将对应长字符串首地址赋值给对应的参数，以供解析方直接使用；可单独使用，也可搭配%l使用
 *
 *       %h，对应16进制字符串类型的参数，接口内部会将16进制码流字符串转换成码流；一般搭配%l使用
 *
 * @param buf  [IN] AT字符串参数首地址，如"2,5,cmnet"
 * @param va_args ... [IN/OUT] 每个待解析参数赋值的地址空间，参数个数与fmt中的个数保持一致
 * @note 
 * @warning   该接口类似scanf形式，通过灵活的fmt格式化，来达到参数检错和解析一步到位，简化具体AT命令的开发。
 */
int at_parse_param(char *fmt, char *buf, ...)
{
	int ret = XY_OK;
	va_list vl;
	va_start(vl,buf);

	while (*buf == ' ')
		buf++;

	ret = parse_param(fmt, buf,NULL,NULL, AT_PARAM_PARSE_DEFAULT, &vl);

	va_end(vl);
	return ret;
}

/**
 * @brief  仅用于含转义字符串参数的解析，通常用于http等特殊命令中,由于支持字符串中转义字符解析，输入的字符串必须用""包含!!!
 * @param fmt  [IN] 同at_parse_param函数的使用
 * @param buf  [IN] 待解析的字符串头地址,例如 "2,5,cmnet"
 * @param parse_num  [OUT] 实际解析的参数个数，例如AT+HTTPHEADER=1,"" parse_num=2，而AT+HTTPHEADER=1  parse_num=1
 * @param va_args ... [IN/OUT] 每个待解析参数赋值的地址空间，参数个数与fmt中的个数保持一致
 * @return 解析结果 参考@AT_ERRNO_E
 * @note 字符串转义字符解析示例: "\101xinyin\x42" => "AxinyiB"
 * @note 字符串转义字符解析示例: "\r\nxinyi\?" = "'\r''\n'xinyi?" 原先字符串中\r占用两个字节，解析成功后转为'\r' ACSII字符占用一个字节
 */
int at_parse_param_escape(char *fmt, char *buf, int* parse_num, ...)
{
	int ret = XY_OK;
	va_list vl;
	va_start(vl, parse_num);
	*parse_num = 0;
	ret = parse_param(fmt, buf,NULL,parse_num, AT_PARAM_PARSE_ESC, &vl);
	va_end(vl);
	return ret;
}

/**
 * @brief 入参与at_parse_param用法一致。新增两个功能，一是严格参数个数检查；二是针对最后一个参数是字符串型变量，容许内容包括,""\r等特征字符。
 * @note  at_parse_param()不进行参数个数的严格检查，通常用于部分指定参数解析；而at_parse_param_all()用于包括最后一个参数的解析，防止AT命令携带过多非法参数。
 * 例如："AT+CFUN=0,7\r\n"这条命令多携带了参数'7'，用at_parse_param_all()进行解析时就会报XY_Err_Parameter错误码。
 *
 * @note  at_parse_param_all()可用于最后一个参数为不规则data的字符串型参数解析，如字符串内容中包括,""\r等特征字符，一律当成有效数据内容处理。
 * 例如："AT+MIPSEND=1,10,"TEST,\rTE"T"\r\n"用该函数解析后，最后一个字符串参数解析结果为：TEST,\rTE"T
 */
int at_parse_param_all(char *fmt, char *buf, ...)
{
	int ret = XY_OK;
	va_list vl;
	va_start(vl,buf);

	while (*buf == ' ')
		buf++;

	ret = parse_param(fmt, buf,NULL,NULL, AT_PARAM_PARSE_ALL, &vl);

	va_end(vl);
	return ret;
}
