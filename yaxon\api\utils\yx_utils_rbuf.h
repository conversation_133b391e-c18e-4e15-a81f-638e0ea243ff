/**********************************************************************/
/**
 * @file yx_utils_rbuf.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-11
 * @version V1.0
 * @brief 环形buf工具封装接口
 **********************************************************************/
#ifndef YX_UTILS_RBUF_H
#define YX_UTILS_RBUF_H

#include "yx_type.h"

/**
 * @brief 环形buf句柄结构定义
 * @ingroup yx_utils
 */
typedef struct
{
    INT32U bufsize; /**< 循环缓冲区的大小（字节数） */
    INT32U used;    /**< 循环缓冲区中已使用的字节数 */
    INT8U* bptr;    /**< 循环缓冲区所管理内存的起始指针 */
    INT8U* eptr;    /**< 循环缓冲区所管理内存的结束指针 */
    INT8U* wptr;    /**< 循环缓冲区的写入位置指针 */
    INT8U* rptr;    /**< 循环缓冲区的读取位置指针 */
} rbuf_t;

/**
 * @brief 初始化循环缓冲区
 * @ingroup yx_utils
 * @param round 循环缓冲区结构体指针
 * @param mem 循环缓冲区所管理的内存地址
 * @param memsize 循环缓冲区所管理的内存字节数
 * @return 无
 */
VOID yx_utils_rbuf_init(rbuf_t* round, INT8U* mem, INT32U memsize);

/**
 * @brief 复位循环缓冲区，将已使用字节数清0
 * @ingroup yx_utils
 * @param round 循环缓冲区结构体指针
 * @return 无
 */
VOID yx_utils_rbuf_reset(rbuf_t* round);

/**
 * @brief 获取循环缓冲区所管理内存的起始指针
 * @ingroup yx_utils
 * @param round 循环缓冲区结构体指针
 * @return 所管理内存的起始指针
 */
INT8U* yx_utils_rbuf_start_pos(rbuf_t* round);

/**
 * @brief 往循环缓冲区中写入一个字节的数据
 * @ingroup yx_utils
 * @param round 循环缓冲区结构体指针
 * @param data 待写入的数据
 * @return 成功返回RTN_OK，失败返回RTN_ERR
 */
INT32S yx_utils_rbuf_write(rbuf_t* round, INT8U data);

/**
 * @brief 从循环缓冲区中读取一个字节的数据
 * @ingroup yx_utils
 * @param round 循环缓冲区结构体指针
 * @return 成功返回读取到的字节，失败返回RTN_ERR
 */
INT32S yx_utils_rbuf_read(rbuf_t* round);

/**
 * @brief 在不移动缓冲区指针的情况下，从循环缓冲区中读取一个字节的数据
 * @ingroup yx_utils
 * @param round 循环缓冲区结构体指针
 * @return 成功返回读取到的字节，失败返回RTN_ERR
 */
INT8U yx_utils_rbuf_read_no_mv_ptr(rbuf_t* round);

/**
 * @brief 获取循环缓冲区中剩余的可用字节数
 * @ingroup yx_utils
 * @param round 循环缓冲区结构体指针
 * @return 剩余的可用字节数
 */
INT32U yx_utils_rbuf_left(rbuf_t* round);

/**
 * @brief 获取循环缓冲区中已使用字节数
 * @ingroup yx_utils
 * @param round 循环缓冲区结构体指针
 * @return 已使用的字节数
 */
INT32U yx_utils_rbuf_used(rbuf_t* round);

/**
 * @brief 往循环缓冲区中写入一块数据单元
 * @ingroup yx_utils
 * @param round 循环缓冲区结构体指针
 * @param bptr 待写入数据块的指针
 * @param blksize 待写入数据块的字节数
 * @return 成功返回RTN_OK，失败返回RTN_ERR
 */
INT32S yx_utils_rbuf_write_block(rbuf_t* round, INT8U* bptr, INT32U blksize);

/**
 * @brief 从循环缓冲区中读取一块数据单元
 * @ingroup yx_utils
 * @param round 循环缓冲区结构体指针
 * @param bptr 用于存储读取数据的缓冲区指针
 * @param blksize 要读取的数据块字节数
 * @return 成功返回实际读取的字节数，失败返回RTN_ERR
 */
INT32S yx_utils_rbuf_read_block(rbuf_t* round, INT8U* bptr, INT32U blksize);

#endif
