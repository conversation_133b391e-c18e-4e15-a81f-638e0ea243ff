/**********************************************************************/
/**
 * @file yx_ia_can.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-06-17
 * @version V1.0
 * @brief can模块接口适配
 **********************************************************************/
#ifndef YX_IA_CAN_H
#define YX_IA_CAN_H


#include "yx_type.h"


/**
 * @brief CAN通信通道枚举
 * @ingroup ia_can
 */
typedef enum {
    IA_CAN_COM_0 = 0,
    IA_CAN_COM_1,
    IA_CAN_COM_MAX
} yx_ia_can_com_e;

/**
 * @brief CAN工作模式枚举
 * @ingroup ia_can
 */
typedef enum {
    IA_CAN_WORK_MODE_NORMAL = 0,                    /* normal mode */
    IA_CAN_WORK_MODE_LOOPBACK,                      /* loopback mode */
    IA_CAN_WORK_MODE_SILENT,                        /* silent mode */
    IA_CAN_WORK_MODE_SILENT_LOOPBACK,               /* loopback combined with silent mode */
    IA_CAN_WORK_MODE_MAX
} yx_ia_can_work_mode_e;

/**
 * @brief CAN帧ID类型枚举
 * @ingroup ia_can
 */
typedef enum {
    IA_CAN_ID_TYPE_STD = 0,                         /* 标准帧 */
    IA_CAN_ID_TYPE_EXT,                             /* 扩展帧 */
    IA_CAN_ID_TYPE_MAX
} yx_ia_can_id_type_e;

/**
 * @brief CAN帧数据类型枚举
 * @ingroup ia_can
 */
typedef enum {
    IA_CAN_RTR_TYPE_DATA = 0,                       /* 数据帧 */
    IA_CAN_RTR_TYPE_REMOTE,                         /* 远程帧 */
    IA_CAN_RTR_TYPE_MAX
} yx_ia_can_rtr_type_e;

/**
 * @brief CAN软件滤波模式
 * @ingroup ia_can
 */
typedef enum {
    IA_CAN_SOFT_FILTER_MODE_CLOSE = 0,              /* 关闭所有软件滤波 */
    IA_CAN_SOFT_FILTER_MODE_ONLY_RECEIVE_FILTER,    /* 只接收滤波ID里定义的数据 */
    IA_CAN_SOFT_FILTER_MODE_ONLY_FILTE_FILTER,      /* 只过滤滤波ID里的数据,其他数据正常接收 */
    IA_CAN_SOFT_FILTER_MODE_MAX
} yx_ia_can_soft_filter_mode_e;

/**
 * @brief CAN配置结构体定义
 * @ingroup ia_can
 */
typedef struct {
    INT8U  com;              /* 串口通道编号,见 yx_ia_can_com_e */
    INT8U  mode;             /* 工作模式,见 yx_ia_can_work_mode_e */
    INT8U  idtype;           /* 帧ID类型,见 yx_ia_can_id_type_e */
    INT32U baud;             /* 波特率 */
} yx_ia_can_config_t;

/**
 * @brief CAN数据发送结构体定义
 * @ingroup ia_can
 */
typedef struct {
    INT32U id;               /* 帧ID,标准帧则取值0~0x7FF,扩展帧则取值0~0x1FFFFFFF. */
    INT8U  idtype;           /* 帧ID类型,见 yx_ia_can_id_type_e */
    INT8U  datatype;         /* 帧数据类型,见 yx_ia_can_rtr_type_e */
    INT8U  dlc;              /* 数据长度,取值0~8 */
    INT8U  data[8];          /* 帧数据 */
} yx_ia_can_send_data_t;

/**
 * @brief CAN数据接收结构体定义
 * @ingroup ia_can
 */
typedef struct {
    INT8U  com;              /* 串口通道编号,见 yx_ia_can_com_e */
    INT32U id;               /* 帧ID,标准帧则取值0~0x7FF,扩展帧则取值0~0x1FFFFFFF. */
    INT8U  idtype;           /* 帧ID类型,见 yx_ia_can_id_type_e */
    INT8U  datatype;         /* 帧数据类型,见 yx_ia_can_rtr_type_e */
    INT8U  dlc;              /* 数据长度,取值0~8 */
    INT8U  data[8];          /* 帧数据 */
} yx_ia_can_recv_data_t;

/**
 * @brief CAN软件滤波结构体定义
 * @ingroup ia_can
 */
typedef struct {
    INT16U index;            /* 索引ID,如果最大软件滤波数为100,则索引范围为0~30 */
    INT16U cache;            /* 缓存条数 , 如缓存条数为10，则收到10条才转发 */
    INT32U id;               /* 帧ID,标准帧则取值0~0x7FF,扩展帧则取值0~0x1FFFFFFF. */
} yx_ia_can_soft_filter_t;

/**
 * @brief 初始化CAN功能
 * @ingroup ia_can
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_can_init(VOID);

/**
 * @brief 反初始化CAN功能
 * @ingroup ia_can
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_can_deinit(VOID);

/**
 * @brief 打开CAN通信总线
 * @ingroup ia_can
 * @param[in] cfg CAN配置结构体指针
 * @param[in] fp 回调函数指针
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_can_open(yx_ia_can_config_t *cfg, void (*fp)(INT8U com, INT8U result));

/**
 * @brief 关闭CAN通信总线
 * @ingroup ia_can
 * @param[in] com CAN通道编号
 * @param[in] fp 回调函数指针
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_can_close(INT8U com, void (*fp)(INT8U com, INT8U result));

/**
 * @brief 设置CAN滤波参数，滤波ID掩码过滤方式
 * @ingroup ia_can
 * @param[in] com CAN通道编号
 * @param[in] idtype 帧ID类型
 * @param[in] idnum 滤波ID个数
 * @param[in] filterid 滤波ID指针
 * @param[in] maskid 掩码ID指针
 * @param[in] fp 回调函数指针
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_can_set_filter_para(INT8U com, INT8U idtype, INT8U idnum, const INT32U *filterid, const INT32U *maskid, void (*fp)(INT8U com, INT8U result));

/**
 * @brief 设置CAN软件滤波模式
 * @ingroup ia_can
 * @param[in] com CAN通道编号
 * @param[in] mode 软件滤波模式
 * @param[in] fp 回调函数指针
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_can_set_soft_filter_mode(INT8U com, INT8U mode, void (*fp)(INT8U com, INT16U index, INT8U result));

/**
 * @brief 设置CAN滤波参数，滤波ID掩码过滤方式
 * @ingroup ia_can
 * @param[in] com CAN通道编号
 * @param[in] idnum 滤波ID个数
 * @param[in] filterid 滤波ID指针
 * @param[in] fp 回调函数指针
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_can_set_soft_filter_para(INT8U com, INT8U idnum, const yx_ia_can_soft_filter_t *filterid, void (*fp)(INT8U com, INT16U index, INT8U result));

/**
 * @brief CAN数据发送，该函数为阻塞函数
 * @ingroup ia_can
 * @param[in] com CAN通道编号
 * @param[in] data CAN数据发送结构体指针
 * @param[in] fp 回调函数指针
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_can_send_data(INT8U com, const yx_ia_can_send_data_t *data, void (*fp)(INT8U com, INT8U result));

/**
 * @brief CAN数据接收，该函数为阻塞函数
 * @ingroup ia_can
 * @param[in] com CAN通道编号
 * @param[in] data CAN数据接收结构体指针
 * @param[in] fp 回调函数指针
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_can_recv_data(INT8U com, yx_ia_can_recv_data_t *data, void (*fp)(INT8U com, INT8U result));


#endif /* YX_IA_CAN_H */
