#pragma once

/*******************************************************************************
 *                           Include header files                              *
 ******************************************************************************/
#include "at_com.h"
#include "at_passthrough.h"

/*******************************************************************************
 *                             Type definitions                                *
 ******************************************************************************/
/**
* @brief at io操作函数声明
* @param data[IN] at数据
* @param size[IN] at数据长度
*/
typedef bool (*at_io_op)(void *data, size_t size);

enum at_proc_state
{
	DEACTIVE_AT = -1,           //指示当前AT通道处于关闭状态，如USB、log_UART等被拔出场景
	REQ_IDLE = 0x0,
	SEND_REQ_NOW,               //标明当前at上下文对应的at命令已经投递给PS，尚未处理完成，需等待PS返回。
	RCV_REQ_NOW                //标明当前at上下文对应的at命令已经接收，尚未处理完成。  
};

typedef struct at_urc_cache
{
	char* urc;
    uint32_t urc_size;
    struct at_urc_cache* next;
} at_urc_cache_t;

typedef struct at_cmd_cache
{
	char* data;
	uint32_t len;
	uint32_t fd;
	struct at_cmd_cache* next;
} at_cmd_cache_t;

typedef struct atRxCmdList
{
	at_cmd_cache_t* head;
	at_cmd_cache_t* tail;
} atRxCmdList_t;

typedef struct at_context
{
    char *g_farps_rcv_mem;				//已接收的at数据起始地址
	uint32_t g_have_rcved_len;			//已接收的at数据总长度
	uint32_t g_rcv_buf_len;				//分配用于接收at数据的buffer大小,非已接收的数据实际长度
	at_cmd_proc_fn at_proc;             //代理线程处理的命令的对应函数
	at_io_op output;      	            //at io输出接口
	at_io_op input;      	            //at io输入接口，在at_read_tty_task线程中调用
	osMessageQueueId_t user_queue_Id;	//用户线程创建的队列id,用于at_send_wait_rsp接口
	osTimerId_t ctx_tmr;			    //软定时器ID,用于at_send_wait_rsp接口
    at_urc_cache_t *urcCache_data;      //终端内部上报的urc缓存数据
    osMutexId_t urcCache_mux;           //终端内部上报urc缓存数据操作互斥锁
    osMutexId_t rxCmd_mux;              //接收命令、级联命令缓存和缓存读取、重置上下文操作互斥锁
	atRxCmdList_t rxCombCmdList;        //AT级联命令链表
	atRxCmdList_t rxCacheCmdList;       //AT缓存命令链表,上限受AT_RECV_CMD_CACHE_NUM宏控制
    char *at_param;						//at参数指针
	int error_no;						//at命令错误编号
	uint32_t urcCache_size;				//终端内部上报的urc缓存条目
	int fd;								//AT框架自定义的上下文句柄,与dev_fd紧耦, @see @ref AT_TTY_FD
	int devFd;							//外设句柄，-1表示设备未打开
    char *devPath;                      //at外设路径
    uint8_t at_type;					//at命令类型 @see @ref AT_REQ_TYPE
	int8_t state;					    //at命令状态 @see @ref at_proc_state
    char at_cmd_prefix[AT_CMD_PREFIX];	//at命令前缀,例如"WORKLOCK"/"AT"/"ATI"....

	atPassthInfo_t passthInfo;			//记录AT通道的透传信息
} at_context_t;

typedef struct at_context_dync
{
	struct at_context_dync  *next;
	at_context_t *node;
} at_context_dync_t;

/*******************************************************************************
 *                       Global variable declarations                          *
 ******************************************************************************/
extern at_context_dync_t *g_at_ctx_dync_head;
extern osMutexId_t g_at_dync_ctx_mux;
extern at_context_t g_at_tty_ctx[AT_FD_MAX];
extern int g_cur_ttyFd;

/*******************************************************************************
 *                       Global function declarations                          *
 ******************************************************************************/
/**
 * @brief 重置at上下文,如at命令状态，at命令类型等信息
 * @param ctx [IN] 指定的at上下文，如uart_ctx/ap_ctx 
 * @note at参数指针指向的内存必须在此释放
 */
void reset_ctx(at_context_t *ctx);

/**
 * @brief 只有应用线程使用at_send_wait_rsp时，才准动态注册；真实的物理通道皆静态注册，包括USB通道
 * @warning	相同fd的上下文同一时间只能注册一个，再次尝试注册会返回XY_ERR
 */
bool regist_at_dync_ctx(at_context_t *ctx);

/**
 * @brief 只有动态上下文才行销毁，例如at_send_wait_rsp应用线程使用的
 * @warning 凡是非应用线程动态创建的上下文，一律严禁销毁，包括USB虚拟的
 */
bool dereg_at_dync_ctx(int fd);

/**
 * @brief 根据给定fd在全局链表g_at_context_dict中查找对应的at上下文
 * @param fd [IN] at上下文fd @see @ref AT_TTY_FD
 * @return 找到后返回对应at上下文地址，否则返回NULL
 */
at_context_t *search_dync_ctx(int fd);

/**
 * @brief   获取可用的at上下文,主要用于at_send_wait_rsp接口
 * @param is_from_atproxy [IN]  1:调用者是xy_proxy线程, 0: 其他线程
 * @return  返回NULL表示无可用at_context，否则会malloc一个at上下文并返回其地址
 * @note   可用的user at上下文数受FARPS_USER_MIN和FARPS_USER_MAX宏控制
 * 如果当前并发执行的user at上下文数>=FARPS_USER_MAX，再次尝试调用接口会返回NULL
 * @warning 获取at上下文成功时接口内部会malloc at_context,需在外部调用deregister_at_context接口释放
 */
at_context_t* get_dync_context();

/**
 * @brief  通过软定时器id获取对应的队列ID
 * @param timerId  [IN]软定时器id
 * @return  成功返回对应的队列ID,失败返回NULL
 * @note 仅用于at_send_wait_rsp接口
 */
osMessageQueueId_t get_dync_queue_by_timer(osTimerId_t timerId);

/**
 * @brief 判断是否为有效的动态at上下文FD
 * @param srcFd [IN] at上下文FD
 * @return true:合法的动态at上下文FD,false:不合法的动态at上下文FD
 */
bool is_dync_ctx_fd(int srcFd);
