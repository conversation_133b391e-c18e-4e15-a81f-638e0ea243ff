/*
 ** Copyright 2003-2010, VisualOn, Inc.
 **
 ** Licensed under the Apache License, Version 2.0 (the "License");
 ** you may not use this file except in compliance with the License.
 ** You may obtain a copy of the License at
 **
 **     http://www.apache.org/licenses/LICENSE-2.0
 **
 ** Unless required by applicable law or agreed to in writing, software
 ** distributed under the License is distributed on an "AS IS" BASIS,
 ** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 ** See the License for the specific language governing permissions and
 ** limitations under the License.
 */



/*-------------------------------------------------------------*
 *  Table for az_isp()                                         *
 *                                                             *
 * Vector grid[] is in Q15                                     *
 *                                                             *
 * grid[0] = 1.0;                                              *
 * grid[grid_points+1] = -1.0;                                 *
 * for (i = 1; i < grid_points; i++)                           *
 *   grid[i] = cos((6.283185307*i)/(2.0*grid_points));         *
 *                                                             *
 *-------------------------------------------------------------*/

/* Version 101 points */

#define   GRID_POINTS     100

const Word16 vogrid[GRID_POINTS+1] ={
    32767,     32751,     32703,     32622,     32509,     32364,
    32187,     31978,     31738,     31466,     31164,     30830,
    30466,     30072,     29649,     29196,     28714,     28204,
    27666,     27101,     26509,     25891,     25248,     24579,
    23886,     23170,     22431,     21669,     20887,     20083,
    19260,     18418,     17557,     16680,     15786,     14876,
    13951,     13013,     12062,     11099,     10125,      9141,
     8149,      7148,      6140,      5126,      4106,      3083,
     2057,      1029,         0,     -1029,     -2057,     -3083,
    -4106,     -5126,     -6140,     -7148,     -8149,     -9141,
   -10125,    -11099,    -12062,    -13013,    -13951,    -14876,
   -15786,    -16680,    -17557,    -18418,    -19260,    -20083,
   -20887,    -21669,    -22431,    -23170,    -23886,    -24579,
   -25248,    -25891,    -26509,    -27101,    -27666,    -28204,
   -28714,    -29196,    -29649,    -30072,    -30466,    -30830,
   -31164,    -31466,    -31738,    -31978,    -32187,    -32364,
   -32509,    -32622,    -32703,    -32751,    -32760};

