/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "app_basic_config.h"
#include "xy_at_api.h"
#include "oss_nv.h"
#include "xy_sleep_lock.h"
#include "xy_system.h"
#include "xy_tcpip_api.h"
#include "xy_utils.h"
#include "at_hardware_api.h"
#include "xy_flash.h"
#include "usb_api.h"
#include "pin_ctl.h"
#include "hal_adc.h"
#include "softap_nv.h"

#if USR_CUSTOM8 || USR_CUSTOM14

/* ATS24=<delay sec>,<save> */
int at_ATS24_req(char *at_buf, char **prsp_cmd)
{
	int save=0,delay=0;  

	if (g_req_type == AT_CMD_REQ)
	{
		if (at_parse_param("%d[1-255],%d[0-1]", at_buf,&delay,&save) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }
		
		set_AT_delaylock_time(delay);
		at_delaylock_act();
		
		if(save == 1)
		{
			g_softap_fac_nv->sleep_delay = delay;
			SAVE_FAC_PARAM(sleep_delay);
		}
	}
	else if (g_req_type == AT_CMD_QUERY)
	{
		*prsp_cmd = xy_malloc(48);
		
		if(g_softap_var_nv->delay_sec != 0)
			sprintf(*prsp_cmd, "\r\n%d",g_softap_var_nv->delay_sec);
		else
			sprintf(*prsp_cmd, "\r\n%d",g_softap_fac_nv->sleep_delay);
	}	
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

    return AT_END;
}


/*AT+CBAUD=<baud_rate>，可配置波特率：4800,9600,14400, 19200,28800,33600,38400,57600,115200,230400,460800,921600,1000000*/
int at_CBAUD_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_QUERY)
	{
		*prsp_cmd = xy_malloc(40);
		sprintf(*prsp_cmd, "%d", g_softap_var_nv->at_ipr & 0x8000 ? 0 : atUartBaudRateGet());
	}
	else if (g_req_type == AT_CMD_REQ) //设置类
	{ 
        int ret = XY_OK;
        uint32_t baud_rate = 115200;
#if UART_AT
		if (at_parse_param("%d[0|4800|9600|14400|19200|28800|33600|38400|57600|115200|230400|460800|921600|1000000|2000000]", at_buf, &baud_rate) != XY_OK)
#else
        if (at_parse_param("%d[0|4800|9600|14400|19200|28800|33600|38400|57600|115200|230400|460800|921600|1000000]", at_buf, &baud_rate) != XY_OK)
#endif
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

#if VER_CM
        ret = set_at_uart_ipr(get_current_ttyFd(), baud_rate, true);
#else /* VER_CM */
        ret = set_at_uart_ipr(get_current_ttyFd(), baud_rate, false);
#endif /* VER_CM */
        if (ret == XY_OK)
            return AT_ASYN;
        else if (ret == XY_ERR)
            return AT_END;
        else
            *prsp_cmd = AT_PLAT_CME_ERR(ret); 
	}
	
#if (!AT_TEST_OFF)
		else if (g_req_type == AT_CMD_TEST)
		{
			*prsp_cmd = xy_malloc(200);
			sprintf(*prsp_cmd, "(0),(4800,9600,19200,28800,33600,38400,57600,115200,230400,460800,921600,1000000)");
		}
#endif
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

	return AT_END;
}
#endif


#if USR_CUSTOM7
int at_CFGRI_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_REQ)
    {
		int param0 = -1, param1 = -1, param2 = -1, param3 = -1;

		if (at_parse_param("%d(0|1),%d,%d,%d", at_buf, &param0, &param1, &param2, &param3) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			return AT_END;
        }
		if (param0)
		{
			if(param3 != -1)
			{
				//AT+CFGRI=1,200,150,3
				Set_RI_Pulse(1,param1,param1+param2,param3);
			}
			else
			{
				//AT+CFGRI=1
				Set_RI_Pulse(1,120,240,1);
			}
		}
		else
		{
			//关闭RI
			Set_RI_Pulse(0,120,240,1);
		}
	}

	return AT_END;
}


static uint32_t g_ri_trigger_len = 5;

/**
 * @brief 设置触发长度，数据业务超过该长度触发RI
 */
void Set_RI_Triggerlen(uint32_t len)
{
    g_ri_trigger_len = len;
}

/**
 * @brief 获取触发长度
 */
uint32_t Get_RI_Triggerlen()
{
    return g_ri_trigger_len;
}


int at_WAKEUPLEN_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_REQ)
    {
		int param0 = -1;

		if (at_parse_param("%d", at_buf, &param0) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			return AT_END;
        }
		Set_RI_Triggerlen(param0);
	}

	return AT_END;
}

//AT+WAKEUPINTERVAL=interval
int at_WAKEUPINTERVAL_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_REQ)
    {
		uint32_t interval = 0;

		if (at_parse_param("%d", at_buf, &interval) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			return AT_END;
        }
		Set_RI_Period_Sec(interval);
	}
	else if(g_req_type == AT_CMD_QUERY)
	{
		*prsp_cmd = xy_malloc(32);
		sprintf(*prsp_cmd, "%d", Get_RI_Period_Sec());
	}

	return AT_END;
}
#endif

