#pragma once

/*******************************************************************************
 *                           Include header files                              *
 ******************************************************************************/
 #include <stdarg.h>
 #include <stdint.h>
 #include <stdbool.h>
 
/*******************************************************************************
 *                             Macro definitions                               *
 ******************************************************************************/
//指示AT后面的标识符，通常为+
#define IS_HEAD_TAG(a)      (a == '+' || a == '^' || a == '&' || a == '#' || a == '*')
#define AT_RSP_OK           "\r\nOK\r\n"
#define AT_RSP_ERR          "\r\nERROR\r\n"
#define AT_CMD_PREFIX       30
#define ATS_ID_MAX          12
#define AT_CR_LF            "\r\n"

/*******************************************************************************
 *                             Type definitions                                *
 ******************************************************************************/
/**
* @brief 支持的AT物理通道，在init_at_resource中进行物理通道注册,与编译宏紧藕。
* @note  出厂NV参数urc_cfg通过位图标识，决定URC广播的物理通道，需要关注编译宏开关造成的枚举值差异。
*/
typedef enum
 {
	AT_FD_INVAILD = -1,

	AT_LPUART_FD = 0,  /*lpuart物理通道*/	
	AT_USB_FD,         /*usb枚举的物理通道，用于AT命令收发*/
	AT_CMUX1_FD,       /*lpuart虚拟的CMUX串口通道1，XY_CMUX开启方有效*/
	AT_CMUX2_FD,       /*lpuart虚拟的CMUX串口通道2，XY_CMUX开启方有效*/
	AT_LOG_FD,         /*log物理通道进行AT命令收发*/
	AT_USB_MODEM_FD,   /*usb枚举的物理通道,用于PPP拨号*/
	
#if PS_TEST_MODE
	AT_PS_TEST_FD,     /*PS内部测试专用通道*/
#endif

	AT_UART_FD,        /*uart0物理通道*/


	AT_FD_MAX,
 } AT_TTY_FD;

 /**
* @brief at_basic_req中注册的AT请求处理函数的返回值，用户必须填对返回值，否则AT框架会处理错误
*/
enum at_cmd_route
{
	AT_END = 0,      /*在AT框架中直接同步处理完毕时，返回该值，由框架组装"\r\nOK\r\n"发送给外部MCU*/
	AT_FORWARD,      /*用户禁用！通常用于3GPP相关AT命令的拦截处理，处理完毕后还要继续转发给3GPP处理*/
	AT_ASYN,         /*通过消息发送给具体业务线程处理，最终由业务线程产生结果码发送出去, 或者命令内部处理完不需要输出结果码的场景，如波特率切换 */
	AT_ROUTE_MAX,
};

enum rf_test_mode
{
    RF_NORMAL = 0,      /*用于退出非信令模式*/
    RF_FTM,            /*非信令模式用于测试强发强收*/
};

/**
* @brief AT动作的种类，用全局g_req_type来记录
*/
typedef enum AT_REQ_TYPE
{
	AT_CMD_INVALID = 0,
	AT_CMD_REQ, 	//AT+xxx=param
	AT_CMD_ACTIVE,	//AT+xxx,not include param
	AT_CMD_QUERY,	//AT+XXX?
	AT_CMD_TEST,	//AT+XXX=?  工程测试命令。FLASH受限时，通过#if (AT_TEST_OFF!=1)关闭该部分代码
} AT_REQ_TYPE_E;


#define  IS_AT_TTY_PHY(fd)  (fd == AT_LPUART_FD || fd == AT_UART_FD || fd == AT_CMUX1_FD || fd == AT_CMUX2_FD)


/**
* @brief  请求类AT命令的注册回调函数声明,内部进行AT命令解析同步处理，最终通过rsp_cmd作为应答字符串
* @param  at_params [IN] AT请求的参数头指针，例如 "1,5,CMNET"
* @param  rsp_cmd   [OUT] 内部申请内存，并组装应答字符串，由平台发送。"\r\nOK\r\n"可由后台自动填充。
* @return at路由类型,同步返回结果使用AT_END, 异步返回结果使用AT_ASYN；具体参考 @see @ref at_cmd_route
* @warning  在函数实现内部，可以通过appAtWriteImmediately发送中间结果，但不可发送OK结果码，而应该由AT框架后台添加OK结果码。
*/
typedef int (*at_cmd_proc_fn)(char *at_params, char **rsp_cmd);

typedef struct at_serv_proc_e
{
	char *at_prefix;
	at_cmd_proc_fn proc;
} at_cmd_t;

struct at_fifo_msg
{
	char data[0];
};

typedef enum AT_PARAM_PARSE_FLAG
{
    AT_PARAM_PARSE_DEFAULT = 0, /*常规字符*/
    AT_PARAM_PARSE_ESC,         /*转义字符*/
    AT_PARAM_PARSE_ALL,         /*一次性解析完所有的参数，如果输入参数的个数超过格式字符串中的参数个数，则报错*/
} AT_PARAM_PARSE_FLAG_E;

/**
 * @brief AT配置信息结构体
 * @note AT&W 保存到NVM； ATZ 恢复为NVM值； AT&F 重置为出厂设置；AT&V 显示当前配置
 */
typedef struct
{
    uint8_t ate;    /* ATE:     AT&W ATZ AT&F AT&V */
    uint8_t atq;    /* ATQ:     AT&W ATZ AT&F AT&V */
    uint8_t atv;    /* ATV:     AT&W ATZ AT&F AT&V */
    uint8_t atx;    /* ATX:     AT&W ATZ AT&F AT&V */

    uint8_t atz;    /* ATZ:     AT&V */
    uint8_t at_c;   /* AT&C:    AT&W ATZ AT&F AT&V */
    uint8_t at_d;   /* AT&D:    AT&W ATZ AT&F AT&V */
    uint8_t at_f;   /* AT&F:    AT&V */

    uint8_t at_w;   /* AT&W:    AT&V */
    uint8_t dcebydte;  /* AT+IFC:  AT&W ATZ AT&F */
    uint8_t dtebydce;  /* AT+IFC:  AT&W ATZ AT&F */
    uint8_t padding;

/** 
 *  ATS0:    AT&W ATZ AT&F AT&V
 *  ATS3:    AT&F AT&V
 *  ATS4:    AT&F AT&V
 *  ATS5:    AT&F AT&V
 *  ATS6:    AT&V
 *  ATS7:    AT&V
 *  ATS8:    AT&V
 *  ATS10:   AT&V 
*/
    uint8_t ats[ATS_ID_MAX];
} at_config_t;

/*******************************************************************************
 *                       Global variable declarations                          *
 ******************************************************************************/
extern at_config_t g_at_config;
/*当前AT框架正在处理的AT请求来源tty通道*/
extern int g_cur_ttyFd;

/*******************************************************************************
 *                       Global function declarations                          *
 ******************************************************************************/

/**
 * @brief 获取AT请求命令的前缀和参数首地址，仅内部使用！
 * @param at_cmd [IN] at cmd data
 * @param at_prefix [OUT] 仅返回AT命令前缀中有效字符串，不携带头尾标识，如“NRB” “ATI” “AT” "WORKLOCK"等
 * @param type [OUT] 返回AT请求命令的类型，@see @ref AT_REQ_TYPE
 */
char *at_get_prefix_and_param(char *at_cmd, char *at_prefix, uint8_t *type);

/**
 * @brief  该接口仅内部函数调用，参数解析以可变入参方式提供，类似scanf
 */
int parse_param(char *fmt_param, char *buf,char **next_param,int *arg, int flag, va_list *ap);

/**
 * @brief  设置回显模式，上电初始化和收到ATE命令时设置
 */
int set_ate_mode(uint8_t mode);

/**
 * @brief  获取当前回显模式
 */
uint8_t get_ate_mode();

/**
 * @brief  设置结果码回显模式，该命令用于控制是否将结果码发送到 TE，作为响应发送的其他信息不受影响。
 * @param mode 0 发送结果码 1 结果码被禁止而不发送。
 */
int set_atq_mode(uint8_t mode);

/**
 * @brief  获取当前结果码回显模式
 */
uint8_t get_atq_mode();

/**
 * @brief  设置TA响应格式，该命令用于确定与 AT 命令结果码和信息响应一起发送的首尾的内容
 * @param mode  0 信息返回：<text><CR><LF> 短结果码格式：<numeric code><CR>
                1 信息返回：<CR><LF><text><CR><LF> 长结果码格式：<CR><LF><verbose code><CR><LF>
 */
int set_atv_mode(uint8_t mode);

/**
 * @brief  判断当前TA响应格式
 */
uint8_t get_atv_mode();

/**
 * @brief  AT&W,保存当前配置到NVM
 */
int do_at_and_w(void);

/**
 * @brief  AT&F,恢复部分配置为出厂配置
 */
int do_at_and_f(void);

/**
 * @brief  AT&V, 显示当前部分AT配置
 */
int do_at_and_v(char *prsp);

/**
 * @brief  ATZ, 从用户定义配置文件还原所有AT命令设置
 */
int do_at_z(void);

/**
 * @brief  ATS, 根据regId 设置对应值
 */
int set_ats_val(uint8_t regId, uint8_t val);

/**
 * @brief  ATS, 根据regId 获取对应值
 */
uint8_t get_ats_val(uint8_t regId);

/*供业务应用发送AT命令并阻塞等待应答结果。仅能处理平台扩展AT命令，3GPP命令请使用xy_atc_interface_call*/
int at_send_wait_rsp(char *req_at, char *info_fmt, int timeout, ...);

bool is_usb_at_support();

bool is_usb_modem_support();


/**
 * @brief 使能AT的SLEEP的睡眠唤醒能力，通常与DTR能力捆绑使用。
 * @note  目前仅AT+QSCLK=1时，会关闭LPUART的AT命令唤醒睡眠能力。
 */
void Enable_AT_Wakup(uint8_t enable);

