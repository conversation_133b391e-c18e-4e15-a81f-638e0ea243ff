/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "at_ctl.h"
#include "app_init.h"
#include "at_com.h"
#include "at_context.h"
#include "at_error.h"
#include "at_utils.h"
#include "at_hardware_api.h"
#include "at_ps_api.h"
#include "at_ps_proxy.h"
#include "factory_nv.h"
#include "icc_msg.h"
#include "posix_device.h"
#include "posix_io.h"
#include "sms_passthrough.h"
#include "xy_lpm.h"
#include "xy_system.h"
#include <errno.h>

/*******************************************************************************
 *						  Local variable definitions						   *
 ******************************************************************************/
osMessageQueueId_t at_msg_q = NULL;
osMutexId_t gAtLpuartLock_Mutex = NULL;
/*AT串口接收期间，不能进睡眠。且释放该锁的同时，需要保证立即执行at_delaylock_act()*/
int8_t g_at_lpuart_lock = -1; 

/*******************************************************************************
 *						Local function implementations						   *
 ******************************************************************************/
/**
 * @brief 判断是否是射频或者平台CP命令，非3GPP命令
 */
int is_softap_DSP_cmd(char *buf)
{
	int i = 0;
	char *dsp_at_list[] =
	{
		"AT+RF",
		"AT+RDNV",
		"AT+RFNV",
		"AT+NST",
		"AT+PHY",
		"AT+NV",
		"AT+ASSERTCP",
        "AT+TEST",
        "AT+DEBUG",
        "AT+QRFTEST",
        "AT+QRXFTM",
        "AT*CalInfo",
        "AT*MRD_IMEI"
	};

    for (i = 0; i < (int)(sizeof(dsp_at_list) / sizeof(dsp_at_list[0])); i++)
    {
        if (at_strncasecmp(buf, dsp_at_list[i]))
        {
            return 1;
        }
    }
    return 0;
}

/**
 * @brief 判断是否是短信at命令, 如CMGS/CMGC/CNMA
 */
bool is_SMS_cmd(char *at_prefix)
{
	char *sms_cmd_list[] = {
        "QCMGS",
		"CMGS",
		"CMGC",
		"CMGW",
		"CNMA",
	};

    if (at_prefix == NULL)
        return false;

    uint32_t i = 0;
	for (i = 0; i < sizeof(sms_cmd_list) / sizeof(sms_cmd_list[0]); i++)
	{
		if (at_strcasecmp(at_prefix, sms_cmd_list[i]))
			return true;
	}
	return false;
}

/*AT通道上下文初始化，一旦注册就生效，但通过dev_fd支持底层热插拔*/
void at_tty_ctx_init(int ttyFd, char* devPath, at_io_op input, at_io_op output)
{
    xy_assert(ttyFd > AT_FD_INVAILD && ttyFd < AT_FD_MAX);
    g_at_tty_ctx[ttyFd].fd = ttyFd;
    g_at_tty_ctx[ttyFd].devFd = -1;
    g_at_tty_ctx[ttyFd].devPath = devPath;
    g_at_tty_ctx[ttyFd].rxCmd_mux = osMutexNew(NULL);
    g_at_tty_ctx[ttyFd].input = input;
    g_at_tty_ctx[ttyFd].output = output;
    g_at_tty_ctx[ttyFd].urcCache_size = 0;
    g_at_tty_ctx[ttyFd].rxCombCmdList.head = NULL;
    g_at_tty_ctx[ttyFd].rxCombCmdList.tail = NULL;
    g_at_tty_ctx[ttyFd].rxCacheCmdList.head = NULL;
    g_at_tty_ctx[ttyFd].rxCacheCmdList.tail = NULL;

    if (ttyFd == AT_LOG_FD) //log at通道不考虑at缓存
        g_at_tty_ctx[ttyFd].urcCache_mux = NULL;
    else
        g_at_tty_ctx[ttyFd].urcCache_mux = osMutexNew(NULL);

}

/* PS或者AT异步线程发送来的AT命令 */
static void proc_from_nearps(at_msg_t *msg)
{
	xy_assert(msg->data != NULL);
    at_context_t *ctx = NULL;
	bool is_rlt_str = Is_Result_AT_str(msg->data);

    if (is_dync_ctx_fd(msg->srcFd))
    {
        /* 虚拟AT通道超时已销毁，PS后续上报给虚拟通道的消息需丢弃 */
        if ((ctx = search_dync_ctx(msg->srcFd)) == NULL)
        {
            ATCTRL_NET_LOG("drop at for user tty(%d)", msg->srcFd);
            xy_printf(0, PLATFORM_AP, WARN_LOG, "[proc_from_nearps]drop at for user tty(%d)",  msg->srcFd);
			debug_log_print("[proc_from_nearps]drop at for user tty(%d)\n", msg->srcFd);
            return;
        }
    }
    else
    {
        ctx = &g_at_tty_ctx[msg->srcFd];
    }

    xy_assert(ctx != NULL);

	//处理短信
	if (is_SMS_cmd(ctx->at_cmd_prefix))
	{
		//非透传模式下的数据正常输出
		at_send_to_farps(msg->data, msg->size, msg->srcFd);

		if (strstr(msg->data, "> "))
		{
            sms_enterPassthroughMode(msg->srcFd);
		}
		else if (is_rlt_str)
		{
			sms_exitPassthroughMode();
		}
        return;
    }

    xy_printf(0, PLATFORM_AP, INFO_LOG, "[proc_from_nearps]response info for sended cmd from ttyFd:%d", msg->srcFd);
	debug_log_print("[proc_from_nearps]response info for sended cmd from ttyFd:%d\n", msg->srcFd);
    at_send_to_farps(msg->data, msg->size, msg->srcFd);
}

static void proc_from_farps(at_msg_t *msg)
{
	xy_assert(msg->msg_id == AT_MSG_RCV_STR_FROM_FARPS);
    at_context_t *ctx = NULL;
    ctx = is_dync_ctx_fd(msg->srcFd) ? search_dync_ctx(msg->srcFd) : &g_at_tty_ctx[msg->srcFd];
    xy_assert(ctx != NULL);

    //Step1: 获取at命令的前缀,参数和命令类型
    char *at_param = at_get_prefix_and_param(msg->data, ctx->at_cmd_prefix, &ctx->at_type);

    //Step2: 判断解析的at前缀是否合法
	char *at_prefix = ctx->at_cmd_prefix;
	if (at_prefix == NULL || !strlen(at_prefix))
	{
        if (msg->size <= AT_DEBUG_LOG_MAX_LEN)
        {
            ATCTRL_NET_LOG("at:%s prefix invalid", msg->data);
            xy_printf(0, PLATFORM_AP, WARN_LOG, "[proc_from_farps]at(%s) prefix invalid", msg->data);
			debug_log_print("[proc_from_farps]at(%s) prefix invalid\n", msg->data);
        }
        else
        {
            ATCTRL_NET_LOG("[proc_from_farps]long at prefix invalid");
            xy_printf(0, PLATFORM_AP, WARN_LOG, "[proc_from_farps]long at prefix invalid");
			debug_log_print("[proc_from_farps]long at prefix invalid\n");
        }

        /* 前缀错误仅显示ERROR */
        char *at_errno = AT_PLAT_ERR(XY_Err_Prefix);
        at_send_to_farps(at_errno, strlen(at_errno), msg->srcFd);
        xy_free(at_errno);
		return;
	}

	//Step3: 从at proxy线程发送到at_ctl的命令必须是3gpp命令，如果再次发送给at proxy线程会造成死锁
	if (msg->srcFd == FARPS_XY_PROXY)
	{
		xy_printf(0,PLATFORM_AP, INFO_LOG, "[proc_from_farps]forward at directly from tty(%d)", msg->srcFd);
		debug_log_print("[proc_from_farps]forward at directly from tty(%d)\n", msg->srcFd);
		goto FWD_PROC;
	}

	//Step4:芯翼平台基础命令由at proxy线程处理
	int i = 0;
	at_cmd_t *at_basic;

	/*debug模式下检查是否同一个AT命令有多个解析处理函数*/
	if(IS_DEBUG_MODE())
	{
		int num = 0;
		for (i = 0; g_at_basic_req != NULL && (g_at_basic_req + i)->at_prefix != 0; i++)
		{
			at_basic = g_at_basic_req + i;
			if (at_strcasecmp(at_prefix, at_basic->at_prefix))
			{
				num++;
			}
		}
		xy_assert(num < 2);
	}
		
	for (i = 0; g_at_basic_req != NULL && (g_at_basic_req + i)->at_prefix != 0; i++)
	{
		at_basic = g_at_basic_req + i;
		if (at_strcasecmp(at_prefix, at_basic->at_prefix))
		{
			xy_assert(at_basic->proc != NULL);

			/* 输入的前缀为小写时，应答的前缀仍然需要大写，进而此处统一整改为大写。TODO：此处未检查长度，当AT命令前缀长度大于at_prefix所占内存大小时，可能发生越界访问 */
			strcpy(at_prefix,at_basic->at_prefix);
			
			ctx->at_proc = at_basic->proc;
			/* 只传递参数offset，减少拷贝及malloc次数 */
			if (at_param != NULL)
			{
				msg->offset = at_param - msg->data;
            }
			else
			{
				ctx->at_param = NULL;
				msg->offset = 0;
			}
			//将当前的at_msg拷贝发送到at proxy线程中
            int ret = send_msg_2_at_proxy(msg, sizeof(at_msg_t) + msg->size + 1);
            if (ret != XY_OK)
            {
                ATCTRL_NET_LOG("mem not enough, stop send %s to at proxy from tty(%d)", at_prefix, msg->srcFd); 
                xy_printf(0, PLATFORM_AP, WARN_LOG, "[proc_from_farps]mem not enough, stop send %s to at proxy from tty(%d)", at_prefix, msg->srcFd);
				debug_log_print("[proc_from_farps]mem not enough, stop send %s to at proxy from tty(%d)\n", at_prefix, msg->srcFd);
                char *at_errno = AT_PLAT_CME_ERR(ret);
                at_send_to_farps(at_errno, strlen(at_errno), msg->srcFd);
                xy_free(at_errno);
            }
            else
            {
                xy_printf(0, PLATFORM_AP, INFO_LOG, "[proc_from_farps]send basic cmd:%s to at proxy", at_prefix);
				debug_log_print("[proc_from_farps]send basic cmd:%s to at proxy\n", at_prefix);
            }
			return;
		}
	}

FWD_PROC:
	//Step5: 转发at命令给PS
	at_req_forward(msg);
}

static void init_at_resource(void)
{
	at_msg_q = osMessageQueueNew(AT_CTRL_QUEUE_SIZE, sizeof(void *), NULL);
	osMutexAttr_t mutex_attr = {0};
	mutex_attr.attr_bits = osMutexRecursive;
	g_at_dync_ctx_mux = osMutexNew(&mutex_attr);
    g_at_lpuart_lock = create_sleep_lock("ATlpuart");
    gAtLpuartLock_Mutex = osMutexNew(NULL);

#if LPUART_AT
    /* AT_TTY_FD中定义的AT上下文都必须初始化！ */
	at_tty_ctx_init(AT_LPUART_FD, DEV_LPUART_AT, (at_io_op)at_recv_from_lpuart, (at_io_op)at_send_to_lpuart);
#else
	g_softap_fac_nv->urc_cfg = g_softap_fac_nv->urc_cfg & (~((1 << AT_LPUART_FD)+(1 << AT_CMUX1_FD)+(1 << AT_CMUX2_FD)));
	at_tty_ctx_init(AT_LPUART_FD, NULL, NULL, NULL);
#endif
    
    if (is_usb_modem_support())
        at_tty_ctx_init(AT_USB_MODEM_FD, DEV_USB_MODEM, (at_io_op)at_recv_from_usb_modem, (at_io_op)at_send_to_usb_modem);
    else
        at_tty_ctx_init(AT_USB_MODEM_FD, NULL, NULL, NULL);

    if (is_usb_at_support())
        at_tty_ctx_init(AT_USB_FD, DEV_USB_AT, (at_io_op)at_recv_from_usb, (at_io_op)at_send_to_usb);
    else
        at_tty_ctx_init(AT_USB_FD, NULL, NULL, NULL);

    at_tty_ctx_init(AT_CMUX1_FD,  DEV_CMUX1,  (at_io_op)at_recv_from_cmux1, (at_io_op)at_send_to_cmux1);
    at_tty_ctx_init(AT_CMUX2_FD,  DEV_CMUX2,  (at_io_op)at_recv_from_cmux2, (at_io_op)at_send_to_cmux2);

#if LOG_AT
    at_tty_ctx_init(AT_LOG_FD,    DEV_LOG_AT, (at_io_op)at_recv_from_log,   (at_io_op)at_send_to_log);
#else
    at_tty_ctx_init(AT_LOG_FD,    NULL, NULL, NULL);
#endif /* LOG_AT */

#if PS_TEST_MODE
    at_tty_ctx_init(AT_PS_TEST_FD, NULL, (at_io_op)at_recv_from_ps_test, (at_io_op)at_send_to_ps_test);
#endif /* PS_TEST_MODE */

#if UART_AT
    at_tty_ctx_init(AT_UART_FD, DEV_UART_AT, (at_io_op)at_recv_from_uart, (at_io_op)at_send_to_uart);
#else
    at_tty_ctx_init(AT_UART_FD, NULL, NULL, NULL);
#endif /* UART_AT */
}

/*******************************************************************************
 *						Global function implementations 					   *
 ******************************************************************************/
void at_ctl(void)
{
	void *rcv_msg = NULL;
	at_msg_t *msg = NULL;

    Sys_Up_URC();
 
    while (1)
	{
		osMessageQueueGet(at_msg_q, &rcv_msg, NULL, osWaitForever);

		xy_assert(rcv_msg != NULL);
		msg = ((at_msg_t *)rcv_msg);
        
		switch (msg->msg_id)
		{
		case AT_MSG_RCV_STR_FROM_FARPS:
			proc_from_farps(msg);
			break;
		case AT_MSG_RCV_STR_FROM_NEARPS:
			proc_from_nearps(msg);
			break;
#if (XY_GNSS1 || XY_GNSS2)
		case AT_MSG_RCV_FROM_GNSS:
		{
            extern void AtCtlProcFromGnssDie(at_msg_t *msg);
            AtCtlProcFromGnssDie(msg);
			break;
		}
#endif
		default:
			break;
		}
		xy_free(msg);
	}
}

void at_ctl_init(void)
{
	/* 初始化at框架使用的全局上下文、信号量和队列等资源 */
	init_at_resource();

    at_config_init();

    /* 注册RF/PHY/PLATFORM 跨核AT通信处理回调 */
    icc_at_channel_register(ICC_AT_CMD, (icc_cb_t)at_recv_from_CP);

	/* 创建at_ctl线程 */
	osThreadAttr_t thread_attr 	= {0};
	thread_attr.name 			= AT_CTRL_THREAD_NAME;
	thread_attr.priority 		= AT_CTRL_THREAD_PRIO;
	thread_attr.stack_size 		= AT_CTRL_THREAD_STACKSIZE;
	osThreadNew((osThreadFunc_t)(at_ctl), NULL, &thread_attr);

	at_proxy_init();

	at_recv_task_init();
	
#if (LPUART_AT || UART_AT)
    // 需在AT&W影响到的参数恢复以及LPUART初始化后执行
    at_config_hardware_init();
#endif /* (LPUART_AT || UART_AT) */
}

int send_msg_2_atctl(int msg_id, void *buf, int size, int ttyFd)
{
    int ret = XY_OK;

    xy_printf(0, PLATFORM_AP, INFO_LOG, "[send_msg_2_atctl]msg_id:%d, tty:%d", msg_id, ttyFd);
	debug_log_print("[send_msg_2_atctl]msg_id:%d, tty:%d\n", msg_id, ttyFd);

	at_msg_t *msg = xy_malloc(sizeof(at_msg_t) + size + 1);

	msg->msg_id = msg_id;
    msg->srcFd = ttyFd;
    msg->size = size;
	msg->offset = 0;
	if (buf != NULL)
	{
		memcpy(msg->data, buf, size);
		*(msg->data + size) = '\0';
	}
	osMessageQueuePut(at_msg_q, &msg, 0, osWaitForever);

    return ret;
}

#if (XY_GNSS1 || XY_GNSS2)
#include "gnss_log_print.h"
#include "gnss_api.h"
extern int gGnssAtRecvedFd;
extern int IS_GNSS_AT_Req_Cmd(char *buf);
#endif

/*非AP核平台业务AT命令，则识别是否为CP核平台扩展AT命令，若不是，则统一发给PS处理*/
void at_req_forward(at_msg_t *msg)
{
    xy_assert(msg->size != 0);
    at_context_t* ctx = is_dync_ctx_fd(msg->srcFd) ? search_dync_ctx(msg->srcFd) : &g_at_tty_ctx[msg->srcFd];

    if (is_softap_DSP_cmd(msg->data))
    {
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_req_forward]forward %s to cp from tty:%d", ctx->at_cmd_prefix, msg->srcFd);
		debug_log_print("[at_req_forward]forward %s to cp from tty:%d\n", ctx->at_cmd_prefix, msg->srcFd);
        at_send_to_CP(msg->data, msg->size, msg->srcFd);
    }
#if (XY_GNSS1 || XY_GNSS2)
    else if (IS_GNSS_AT_Req_Cmd(msg->data))
    {
		gGnssAtRecvedFd = msg->srcFd; //更新Fd

        //复制消息内容、长度
        int data_len = msg->size; 
        char *pdata = xy_zalloc(msg->size + 2); // +2 for '\n' '\0'
        memcpy(pdata, msg->data, msg->size);

        //原数据最后一个是'\r'，则加上'\n'，长度+1
        if(pdata[msg->size - 1] == '\r')
        {
            pdata[msg->size] = '\n';
            data_len++;
        }
        
        //转发消息给GNSS
        SendDataToGnssDie(pdata, data_len);
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[GNSS] at_req_forward data[%s] size[%d]", pdata, data_len);
        gnss_debug_print("at_req_forward data[%s] size[%d]\r\n", pdata, data_len);
        xy_free(pdata);
    }
#endif 
    else
    {
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_req_forward]forward %s to atcap from tty:%d", ctx->at_cmd_prefix, msg->srcFd);
		debug_log_print("[at_req_forward]forward %s to atcap from tty:%d\n", ctx->at_cmd_prefix, msg->srcFd);
        SendAt2AtcAp((char *)msg->data, msg->size, msg->srcFd);
    }

    return;
}

bool at_send_to_farps(void *data, size_t data_len, int ttyFd)
{
    xy_assert(data_len != 0);
    bool ret = false;
    if (is_dync_ctx_fd(ttyFd))
    {
        at_context_t *ctx = search_dync_ctx(ttyFd);
        if (ctx != NULL)
        {
            xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_send_to_farps]recv user at:%d rsp and notify queue: %p", ttyFd, ctx->user_queue_Id);
			debug_log_print("[at_send_to_farps]recv user at:%d rsp and notify queue: %p\n", ttyFd, ctx->user_queue_Id);
            struct at_fifo_msg *msg = xy_malloc(strlen(data) + sizeof(struct at_fifo_msg) + 1);
            strcpy(msg->data, data);
            osMessageQueuePut(ctx->user_queue_Id, &msg, 0, osWaitForever);
            return true;
        }
        else
        {
            ATCTRL_NET_LOG("recv user at:%d rsp but timeout,drop it!", ttyFd);             
            /* 协议栈以队列形式支持多通道处理，虚拟AT通道的命令处理时可能受其他AT通道影响而出现超时，此时ctx为空，不能断言 */
            xy_printf(0, PLATFORM_AP, WARN_LOG, "[at_send_to_farps]recv user at:%d rsp but timeout,drop it!", ttyFd);
			debug_log_print("[at_send_to_farps]recv user at:%d rsp but timeout,drop it!\n", ttyFd);
            return false;
        }
    }
    else
    {
        if (g_at_tty_ctx[ttyFd].output != NULL)
            ret = g_at_tty_ctx[ttyFd].output((char *)data, data_len);
    }

    return ret;
}

int at_posix_write(int tty, const void *data, size_t size)
{
    xy_assert(tty > AT_FD_INVAILD && tty < AT_FD_MAX);
    int ret = 0;

    if (g_at_tty_ctx[tty].devFd == -1)
    {                
        return XY_Err_NotAllowed;
    }

    if (tty == AT_USB_MODEM_FD)
    {
        /* 受限于usb modem输出长度限制，输出超过限制长度立刻丢弃 */
        if (size >= USB_MODEM_AT_OUTPUT_LIMIT)
        {
            xy_printf(0,PLATFORM_AP, WARN_LOG, "usb modem at output size too long:%d, drop", size);
            return XY_Err_NotAllowed;
        }
        /* usb modem开启零拷贝期间，不允许URC/debug信息等非必要数据上报 */
        if (at_posix_ioctl(tty, USB_MODEM_IOC_GET_ZEROCOPY_FLAG, NULL) == 1)
        {
            return XY_Err_InProgress;
        }
    }

    if ((ret = posix_write(g_at_tty_ctx[tty].devFd, data, size)) <= 0)
    {
        if (tty == AT_LPUART_FD || tty == AT_UART_FD)
        {
            xy_printf(0,PLATFORM_AP,WARN_LOG,"at posix write fail ret:%d, errno:%d", ret, errno);
        }
        return XY_ERR;
    }
        
    return XY_OK;
}

int at_posix_write_zerocopy(int tty, const void *data, size_t size)
{
    xy_assert(tty == AT_USB_MODEM_FD);
    int ret = 0;

    if (g_at_tty_ctx[tty].devFd == -1)
    {                
        return XY_Err_NotAllowed;
    }

    /* 受限于usb modem输出长度限制，输出超过限制长度立刻丢弃 */
    if (size >= USB_MODEM_AT_OUTPUT_LIMIT)
    {
        xy_printf(0,PLATFORM_AP, WARN_LOG, "[at_posix_write_zerocopy]usb modem at output size too long:%d, drop", size);
        return XY_Err_NotAllowed;
    }
        
    if ((ret = posix_write(g_at_tty_ctx[tty].devFd, data, size)) <= 0)
    {
        xy_printf(0,PLATFORM_AP,WARN_LOG,"[at_posix_write_zerocopy]modem write fail ret:%d, errno:%d", ret, errno);
        return XY_ERR;
    }
        
    return XY_OK;
}

int at_posix_ioctl(int tty, unsigned long cmd, void *args)
{
    xy_assert(tty > AT_FD_INVAILD && tty < AT_FD_MAX);

    if (g_softap_fac_nv->off_debug == 0)
    {
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_posix_ioctl]tty:%d,cmd:%d", tty, cmd);
    }

    if (g_at_tty_ctx[tty].devFd == -1)
    {         
        return -1;
    }

    int ret = posix_ioctl(g_at_tty_ctx[tty].devFd, cmd, args);
    if (ret == -1)
    {             
        xy_printf(0, PLATFORM_AP, WARN_LOG, "at_posix_ioctl fail! ttyFd:%d,cmd:%d", tty, cmd);   
    }
        
    return ret;
}


