#include "xy_at_api.h"
#include "hal_adc.h"
#include "at_error.h"
#include "xy_ps_api.h"
#include "hal_agpio.h"
#include "hal_gpio.h"
#include "xy_utils.h"
#include "cm_version.h"
#include "xy4100_ll_gpio.h"
#include "cm_version.h"
#include "xy_basic_api.h"

//AT+MADC[=<chanel>]  
#define ADC0_PAD	(g_softap_fac_nv->pad_adc0) //ADC0对应的实际ADC通道号
#define ADC1_PAD	(g_softap_fac_nv->pad_adc1) //ADC1对应的实际ADC通道号




int at_MADC_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_REQ || g_req_type == AT_CMD_ACTIVE)
    {
		//参数检查
		int chanel = 0;
        if (at_parse_param("%d[0-1]", at_buf, &chanel) != XY_OK)
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

		//获取实际ADC通道号
		HAL_ADC_ChannelTypeDef channelID = (chanel) ? ADC1_PAD : ADC0_PAD;

		//判断实际通道号是否无效，若无效则返回错误提示，有效则采集ADC值
		if(channelID == 255)
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			return AT_END;
		}

		//采集ADC值
		int16_t adcvalue = HAL_ADC_Single_GetValue(channelID);
		if(adcvalue > 1400)
		{
			adcvalue = 1400;
		}
		if(adcvalue < 0)
		{
			adcvalue = 0;
		}
		*prsp_cmd = xy_malloc(60);
		sprintf(*prsp_cmd, "+MADC: %d", adcvalue);
	}
	/*AT+MADC*/
	else if(g_req_type == AT_CMD_ACTIVE)
    {
        *prsp_cmd = xy_malloc(64);
        sprintf(*prsp_cmd, "+MADC: %d,%d,%d",HAL_ADC_Single_GetValue(HAL_ADC_VBAT),HAL_ADC_Single_GetValue(ADC0_PAD),HAL_ADC_Single_GetValue(ADC1_PAD));
    }
#if (AT_TEST_OFF!=1)
	else if (g_req_type == AT_CMD_TEST)
	{
		*prsp_cmd = xy_malloc(30);
		sprintf(*prsp_cmd, "+MADC: (0-1)");
	}
#endif
	else
	{
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

	return AT_END;
}

int at_CMVERSION_req(char *at_buf, char **prsp_cmd)
{
	if(g_req_type == AT_CMD_ACTIVE)
	{
		*prsp_cmd = xy_malloc(256);
		char internal_version[26] = {0};
		cm_get_interver(internal_version);

		char swver_version[26] = {0};
		cm_get_swver(swver_version);

		int len = 0;
		len = snprintf(*prsp_cmd, 256,"\r\nCMIOT_%s\r\n",swver_version);
#if AT_FS_CMCC
		len += snprintf(*prsp_cmd + len, 256 - len,"FS_SUPPORT\r\n");
#endif
#if AT_SSL_CMCC
		len += snprintf(*prsp_cmd + len, 256 - len,"SSL_SUPPORT\r\n");
#endif
#if CM_COT_ENABLE
		len += snprintf(*prsp_cmd + len, 256 - len,"LWM2M_SUPPORT\r\n");
#endif
#if AT_FTP_CMCC
		len += snprintf(*prsp_cmd + len, 256 - len,"FTP_SUPPORT\r\n");
#endif
#if (LBS_ONEOS || LBS_AMAP)
		len += snprintf(*prsp_cmd + len, 256 - len,"LBS_SUPPORT\r\n");
#endif
		len += snprintf(*prsp_cmd + len, 256 - len,"RELEASE_VERSION\r\n%s\r\n\r\nOK\r\n",internal_version);
	}
	else
		*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
	return AT_END;	
}


// AT+MREBOOT[=<mode>] 该命令用于设备软重启。默认保存配置NV和协议栈的非易变NV，擦除其他所有工作态NV。0：软重启，1：硬重启，外设接口将掉电重启
int at_MREBOOT_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_ACTIVE || g_req_type == AT_CMD_REQ)
	{
		int mode = 0;
		if (at_parse_param("%d[0-0]", at_buf, &mode) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

		xy_cfun_excute(NET_CFUN5);
		appAtWriteImmediately(get_current_ttyFd(), "\r\nOK\r\n\r\nREBOOTING\r\n", strlen("\r\nOK\r\n\r\nREBOOTING\r\n"));     

		/*直接复位，可能造成低概率NV尚未保存到FLASH中就复位了*/
		if(mode == 0)
		{
			xy_Soft_Reset(SOFT_RB_BY_NRB);
		}
		else /*在IDLE线程入口处执行复位，需要确保NV保存不在IDLE线程中执行，否则会造成NV保存未执行*/
		{
			xy_Soft_Reset_safe(0);
			return AT_ASYN;
		}
	}
#if (!AT_TEST_OFF)
    else if (g_req_type == AT_CMD_TEST)
    {
	    *prsp_cmd = xy_malloc(40);
		snprintf(*prsp_cmd, 40, "+MREBOOT: (0)");		//add by cmiot
	}
#endif

	else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

	return AT_END;
}


/**
 * \brief AT+MTSETID命令: 写入SN/IMEI
 *
 * \param [in] at_buf at口输入字串
 * \param [in] prsp_cmd at口输出响应字串
 *
 * \details AT+MTSETID=<snt>,<value>
 */
int at_MTSETID_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_REQ)
    {
        int32_t snt = -1;
        char str[32] = {0};
		
		if (at_parse_param("%d(0-1),%32s", at_buf, &snt, str) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

        if (strlen(str) < 1)
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

        if (snt == 0)
        {
            if(xy_dev_set_sn(str) != XY_OK)
            {
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }
        }
        else if (snt == 1)
        {
            if(xy_set_IMEI(str) != XY_OK)
            {
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }
        }
		else
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
		}

    }
#if (AT_TEST_OFF!=1)

    else if (g_req_type == AT_CMD_TEST)
    {
        *prsp_cmd = xy_malloc(50);
        snprintf(*prsp_cmd, 50, "\r\n+MTSETID: (0-1,16),\r\n\r\nOK\r\n");
    }
#endif
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

    return AT_END;

}



#if ((XY4100LD==0) && (XY4100LDFE==0))

#if	(RF_BOARD_EU||RF_BOARD_LA||RF_BOARD_GLOBALS)
static int __get_xy_pinnum(int cm_pin_num)
{
    switch(cm_pin_num)
    {
        case 1:
            return AGPIO_PIN0;//WKP1

        case 4:
            return GPIO_PIN23;

        case 5:
            return GPIO_PIN22;

        case 6:
            return GPIO_PIN21;

        case 7:
            return GPIO_PIN20;

        case 18:
            return GPIO_PIN_A;

        case 19:
            return GPIO_PIN2;

        case 20:
            return GPIO_PIN_C;

        case 21:
            return GPIO_PIN_D;

        case 22:
            return GPIO_PIN15;

        case 23:
            return GPIO_PIN16;

        case 25:
            return GPIO_PIN24;

        case 26:
            return GPIO_PIN27;

        case 27:
            return GPIO_PIN26;

        case 28:
            return GPIO_PIN25;

        case 30:
            return GPIO_PIN3;

        case 34:
            return GPIO_PIN9;

        case 35:
            return GPIO_PIN8;

        case 36:
            return GPIO_PIN4;

        case 37:
            return GPIO_PIN5;

        case 38:
            return GPIO_PIN6;

        case 39:
            return GPIO_PIN_B;

        case 40:
            return GPIO_PIN17;

        case 41:
            return GPIO_PIN18;

        case 42:
            return GPIO_PIN7;

        case 64:
            return GPIO_PIN19;

        case 65:
            return GPIO_PIN28;

        case 66:
            return GPIO_PIN29;

        case 83:
            return GPIO_PIN13;

        case 84:
            return GPIO_PIN14;

        case 85:
            return GPIO_PIN32;

        case 86:
            return GPIO_PIN33;

        case 87:
            return GPIO_PIN35;

        case 88:
            return GPIO_PIN36;

        default:
            return -1;
    }
}
int at_MPRODUCTMODE_req(char *at_buf, char **prsp_cmd)  //add by cmiot
{
	HAL_AGPIO_InitTypeDef agpio_init = {0};
    uint64_t result_low = 0;

    //U306
    int GRFC2_pin = 84;
    int BG95_GPIO7_pin = 86;
    int BG95_GPIO9_pin = 88;
    int PSM_IND_pin = 1;

    int GRFC1_pin = 83;
    int BG95_GPIO6_pin = 85;
    int BG95_GPIO8_pin = 87;
    int NET_STATUS_pin = 21;

    //U302
    int MAIN_DTR_pin = 30;
    int I2C_SCL_pin = 40;
    int W_DISABLE_pin = 18;
    int BG95_GPIO4_pin = 65;

    int MAIN_DCD_pin = 38;
    int I2C_SDA_pin = 41;
    int BG95_GPIO3_pin = 64;
    int BG95_GPIO5_pin = 66;

    //U303
    int BG95_GPIO1_pin = 25;
    int PCM_DIN_pin = 6;
    int PCM_DOUT_pin = 7;
    int MAIN_RI_pin = 39;

    int BG95_GPIO2_pin = 26;
    int PCM_SYNC_pin = 5;
    int AP_READY_pin = 19;
    int PCM_CLK_pin = 4;

    //U300
    int MAIN_CTS_pin = 36;
    int MAIN_TXD_pin = 35;
    int DBG_TX_pin = 23;
    int GNSS_RXD_pin = 28;

    int MAIN_RTS_pin = 37;
    int MAIN_RXD_pin = 34;
    int DBG_RX_pin = 22;
    int GNSS_TXD_pin = 27;

    //U301
    int STATUS_pin = 20;
    int USIM_DET_pin = 42;

    //================level==================

    //U306
    uint64_t GRFC2_level = 0;
    uint64_t BG95_GPIO7_level = 0;
    uint64_t BG95_GPIO9_level = 0;
    uint64_t PSM_IND_level = 0;

    uint64_t GRFC1_level = 0;
    uint64_t BG95_GPIO6_level = 0;
    uint64_t BG95_GPIO8_level = 0;
    uint64_t NET_STATUS_level = 0;

    //U302
    uint64_t MAIN_DTR_level = 0;
    uint64_t I2C_SCL_level = 0;
    uint64_t W_DISABLE_level = 0;
    uint64_t BG95_GPIO4_level = 0;

    uint64_t MAIN_DCD_level = 0;
    uint64_t I2C_SDA_level = 0;
    uint64_t BG95_GPIO3_level = 0;
    uint64_t BG95_GPIO5_level = 0;

    //U303
    uint64_t BG95_GPIO1_level = 0;
    uint64_t PCM_DIN_level = 0;
    uint64_t PCM_DOUT_level = 0;
    uint64_t MAIN_RI_level = 0;

    uint64_t BG95_GPIO2_level = 0;
    uint64_t PCM_SYNC_level = 0;
    uint64_t AP_READY_level = 0;
    uint64_t PCM_CLK_level = 0;

    //U300
    uint64_t MAIN_CTS_level = 0;
    uint64_t MAIN_TXD_level = 0;
    uint64_t DBG_TX_level = 0;
    uint64_t GNSS_RXD_level = 0;

    uint64_t MAIN_RTS_level = 0;
    uint64_t MAIN_RXD_level = 0;
    uint64_t DBG_RX_level = 0;
    uint64_t GNSS_TXD_level = 0;

    //U301
    uint64_t STATUS_level = 0;
    uint64_t USIM_DET_level = 0;

    
    if(g_req_type == AT_CMD_REQ)
    {
        /* 引脚初始化设置 */
        //U306
        int GRFC2_pin_xy = __get_xy_pinnum(GRFC2_pin);
        int BG95_GPIO7_pin_xy = __get_xy_pinnum(BG95_GPIO7_pin);
        int BG95_GPIO9_pin_xy = __get_xy_pinnum(BG95_GPIO9_pin);
        int PSM_IND_pin_xy = __get_xy_pinnum(PSM_IND_pin);  

        int GRFC1_pin_xy = __get_xy_pinnum(GRFC1_pin);
        int BG95_GPIO6_pin_xy = __get_xy_pinnum(BG95_GPIO6_pin);
        int BG95_GPIO8_pin_xy = __get_xy_pinnum(BG95_GPIO8_pin);
        int NET_STATUS_pin_xy = __get_xy_pinnum(NET_STATUS_pin);

        //U302
        int MAIN_DTR_pin_xy = __get_xy_pinnum(MAIN_DTR_pin);
        int I2C_SCL_pin_xy = __get_xy_pinnum(I2C_SCL_pin);
        int W_DISABLE_pin_xy = __get_xy_pinnum(W_DISABLE_pin);
        int BG95_GPIO4_pin_xy = __get_xy_pinnum(BG95_GPIO4_pin);

        int MAIN_DCD_pin_xy = __get_xy_pinnum(MAIN_DCD_pin);
        int I2C_SDA_pin_xy = __get_xy_pinnum(I2C_SDA_pin);
        int BG95_GPIO3_pin_xy = __get_xy_pinnum(BG95_GPIO3_pin);
        int BG95_GPIO5_pin_xy = __get_xy_pinnum(BG95_GPIO5_pin);

        //U303
        int BG95_GPIO1_pin_xy = __get_xy_pinnum(BG95_GPIO1_pin);
        int PCM_DIN_pin_xy = __get_xy_pinnum(PCM_DIN_pin);
        int PCM_DOUT_pin_xy = __get_xy_pinnum(PCM_DOUT_pin);
        int MAIN_RI_pin_xy = __get_xy_pinnum(MAIN_RI_pin);

        int BG95_GPIO2_pin_xy = __get_xy_pinnum(BG95_GPIO2_pin);
        int PCM_SYNC_pin_xy = __get_xy_pinnum(PCM_SYNC_pin);
        int AP_READY_pin_xy = __get_xy_pinnum(AP_READY_pin);
        int PCM_CLK_pin_xy = __get_xy_pinnum(PCM_CLK_pin);

        //U300
        int MAIN_CTS_pin_xy = __get_xy_pinnum(MAIN_CTS_pin);
        int MAIN_TXD_pin_xy = __get_xy_pinnum(MAIN_TXD_pin);
        int DBG_TX_pin_xy = __get_xy_pinnum(DBG_TX_pin);
        int GNSS_RXD_pin_xy = __get_xy_pinnum(GNSS_RXD_pin);

        int MAIN_RTS_pin_xy = __get_xy_pinnum(MAIN_RTS_pin);
        int MAIN_RXD_pin_xy = __get_xy_pinnum(MAIN_RXD_pin);
        int DBG_RX_pin_xy = __get_xy_pinnum(DBG_RX_pin);
        int GNSS_TXD_pin_xy = __get_xy_pinnum(GNSS_TXD_pin);

        //U301
        int STATUS_pin_xy = __get_xy_pinnum(STATUS_pin);
        int USIM_DET_pin_xy = __get_xy_pinnum(USIM_DET_pin);

        //U306
        /*配置为输出模式，高电平*/
        gpio_init_cfg(GRFC2_pin_xy, 1, 0, 1);
        gpio_init_cfg(BG95_GPIO7_pin_xy, 1, 0, 1);
        gpio_init_cfg(BG95_GPIO9_pin_xy, 1, 0, 1);
        memset(&agpio_init, 0, sizeof(HAL_AGPIO_InitTypeDef));
        agpio_init.Pin = PSM_IND_pin_xy;
        agpio_init.Mode = AGPIO_MODE_OUTPUT_PP;
        HAL_AGPIO_Init(&agpio_init);
        HAL_AGPIO_Write_Pin(PSM_IND_pin_xy, SET);

        /*配置为输入模式，浮空*/
        gpio_init_cfg(GRFC1_pin_xy, 0, 0, 0);
		GRFC1_level = HAL_GPIO_Read_Pin(GRFC1_pin_xy);
        gpio_init_cfg(BG95_GPIO6_pin_xy, 0, 0, 0);
		BG95_GPIO6_level = HAL_GPIO_Read_Pin(BG95_GPIO6_pin_xy);
        gpio_init_cfg(BG95_GPIO8_pin_xy, 0, 0, 0);
		BG95_GPIO8_level = HAL_GPIO_Read_Pin(BG95_GPIO8_pin_xy);
        gpio_init_cfg(NET_STATUS_pin_xy, 0, 0, 0);
		NET_STATUS_level = HAL_GPIO_Read_Pin(NET_STATUS_pin_xy);

        /*配置为输出模式，高电平*/
        gpio_init_cfg(GRFC1_pin_xy, 1, 0, 1);
        gpio_init_cfg(BG95_GPIO6_pin_xy, 1, 0, 1);
        gpio_init_cfg(BG95_GPIO8_pin_xy, 1, 0, 1);
        gpio_init_cfg(NET_STATUS_pin_xy, 1, 0, 1);

        /*配置为输入模式，浮空*/
        gpio_init_cfg(GRFC2_pin_xy, 0, 0, 0);
		GRFC2_level = HAL_GPIO_Read_Pin(GRFC2_pin_xy);
        gpio_init_cfg(BG95_GPIO7_pin_xy, 0, 0, 0);
		BG95_GPIO7_level = HAL_GPIO_Read_Pin(BG95_GPIO7_pin_xy);
        gpio_init_cfg(BG95_GPIO9_pin_xy, 0, 0, 0);
		BG95_GPIO9_level = HAL_GPIO_Read_Pin(BG95_GPIO9_pin_xy);
        memset(&agpio_init, 0, sizeof(HAL_AGPIO_InitTypeDef));
        agpio_init.Pin = PSM_IND_pin_xy;
        agpio_init.Mode = AGPIO_MODE_INPUT;
        agpio_init.Pull = AGPIO_FLOAT;
        HAL_AGPIO_Init(&agpio_init);
        PSM_IND_level = HAL_AGPIO_Read_Pin(PSM_IND_pin_xy);


        //U302
         /*配置为输出模式，高电平*/
        gpio_init_cfg(MAIN_DTR_pin_xy, 1, 0, 1);
        gpio_init_cfg(I2C_SCL_pin_xy, 1, 0, 1);
        gpio_init_cfg(W_DISABLE_pin_xy, 1, 0, 1);
        gpio_init_cfg(BG95_GPIO4_pin_xy, 1, 0, 1);

        /*配置为输入模式，浮空*/
        gpio_init_cfg(MAIN_DCD_pin_xy, 0, 0, 0);
		MAIN_DCD_level = HAL_GPIO_Read_Pin(MAIN_DCD_pin_xy);
        gpio_init_cfg(I2C_SDA_pin_xy, 0, 0, 0);
		I2C_SDA_level= HAL_GPIO_Read_Pin(I2C_SDA_pin_xy);
        gpio_init_cfg(BG95_GPIO3_pin_xy, 0, 0, 0);
		BG95_GPIO3_level = HAL_GPIO_Read_Pin(BG95_GPIO3_pin_xy);
        gpio_init_cfg(BG95_GPIO5_pin_xy, 0, 0, 0);
		BG95_GPIO5_level = HAL_GPIO_Read_Pin(BG95_GPIO5_pin_xy);

        /*配置为输出模式，高电平*/
        gpio_init_cfg(MAIN_DCD_pin_xy, 1, 0, 1);
        gpio_init_cfg(I2C_SDA_pin_xy, 1, 0, 1);
        gpio_init_cfg(BG95_GPIO3_pin_xy, 1, 0, 1);
        gpio_init_cfg(BG95_GPIO5_pin_xy, 1, 0, 1);

        /*配置为输入模式，浮空*/
        gpio_init_cfg(MAIN_DTR_pin_xy, 0, 0, 0);
		MAIN_DTR_level = HAL_GPIO_Read_Pin(MAIN_DTR_pin_xy);
        gpio_init_cfg(I2C_SCL_pin_xy, 0, 0, 0);
		I2C_SCL_level = HAL_GPIO_Read_Pin(I2C_SCL_pin_xy);
        gpio_init_cfg(W_DISABLE_pin_xy, 0, 0, 0);
		W_DISABLE_level = HAL_GPIO_Read_Pin(W_DISABLE_pin_xy);
        gpio_init_cfg(BG95_GPIO4_pin_xy, 0, 0, 0);
		BG95_GPIO4_level = HAL_GPIO_Read_Pin(BG95_GPIO4_pin_xy);

        //U303
        /*配置为输出模式，高电平*/
        gpio_init_cfg(BG95_GPIO1_pin_xy, 1, 0, 1);
        gpio_init_cfg(PCM_DIN_pin_xy, 1, 0, 1);
        gpio_init_cfg(PCM_DOUT_pin_xy, 1, 0, 1);
        gpio_init_cfg(MAIN_RI_pin_xy, 1, 0, 1);

        /*配置为输入模式，浮空*/
        gpio_init_cfg(BG95_GPIO2_pin_xy, 0, 0, 0);
		BG95_GPIO2_level = HAL_GPIO_Read_Pin(BG95_GPIO2_pin_xy);
        gpio_init_cfg(PCM_SYNC_pin_xy, 0, 0, 0);
		PCM_SYNC_level= HAL_GPIO_Read_Pin(PCM_SYNC_pin_xy);
        gpio_init_cfg(AP_READY_pin_xy, 0, 0, 0);
		AP_READY_level = HAL_GPIO_Read_Pin(AP_READY_pin_xy);
        gpio_init_cfg(PCM_CLK_pin_xy, 0, 0, 0);
		PCM_CLK_level = HAL_GPIO_Read_Pin(PCM_CLK_pin_xy);

        /*配置为输出模式，高电平*/
        gpio_init_cfg(BG95_GPIO2_pin_xy, 1, 0, 1);
        gpio_init_cfg(PCM_SYNC_pin_xy, 1, 0, 1);
        gpio_init_cfg(AP_READY_pin_xy, 1, 0, 1);
        gpio_init_cfg(PCM_CLK_pin_xy, 1, 0, 1);

        /*配置为输入模式，浮空*/
        gpio_init_cfg(BG95_GPIO1_pin_xy, 0, 0, 0);
		BG95_GPIO1_level = HAL_GPIO_Read_Pin(BG95_GPIO1_pin_xy);
        gpio_init_cfg(PCM_DIN_pin_xy, 0, 0, 0);
		PCM_DIN_level = HAL_GPIO_Read_Pin(PCM_DIN_pin_xy);
        gpio_init_cfg(PCM_DOUT_pin_xy, 0, 0, 0);
		PCM_DOUT_level = HAL_GPIO_Read_Pin(PCM_DOUT_pin_xy);
        gpio_init_cfg(MAIN_RI_pin_xy, 0, 0, 0);
		MAIN_RI_level = HAL_GPIO_Read_Pin(MAIN_RI_pin_xy);

        //U300
         /*配置为输出模式，高电平*/
        gpio_init_cfg(MAIN_CTS_pin_xy, 1, 0, 1);
        gpio_init_cfg(MAIN_TXD_pin_xy, 1, 0, 1);
        gpio_init_cfg(DBG_TX_pin_xy, 1, 0, 1);
        gpio_init_cfg(GNSS_RXD_pin_xy, 1, 0, 1);

        /*配置为输入模式，浮空*/
        gpio_init_cfg(MAIN_RTS_pin_xy, 0, 0, 0);
		MAIN_RTS_level = HAL_GPIO_Read_Pin(MAIN_RTS_pin_xy);
        gpio_init_cfg(MAIN_RXD_pin_xy, 0, 0, 0);
		MAIN_RXD_level= HAL_GPIO_Read_Pin(MAIN_RXD_pin_xy);
        gpio_init_cfg(DBG_RX_pin_xy, 0, 0, 0);
		DBG_RX_level = HAL_GPIO_Read_Pin(DBG_RX_pin_xy);
        gpio_init_cfg(GNSS_TXD_pin_xy, 0, 0, 0);
		GNSS_TXD_level = HAL_GPIO_Read_Pin(GNSS_TXD_pin_xy);

        /*配置为输出模式，高电平*/
        gpio_init_cfg(MAIN_RTS_pin_xy, 1, 0, 1);
        gpio_init_cfg(MAIN_RXD_pin_xy, 1, 0, 1);
        gpio_init_cfg(DBG_RX_pin_xy, 1, 0, 1);
        gpio_init_cfg(GNSS_TXD_pin_xy, 1, 0, 1);

        /*配置为输入模式，浮空*/
        gpio_init_cfg(MAIN_CTS_pin_xy, 0, 0, 0);
		MAIN_CTS_level = HAL_GPIO_Read_Pin(MAIN_CTS_pin_xy);
        gpio_init_cfg(MAIN_TXD_pin_xy, 0, 0, 0);
		MAIN_TXD_level = HAL_GPIO_Read_Pin(MAIN_TXD_pin_xy);
        gpio_init_cfg(DBG_TX_pin_xy, 0, 0, 0);
		DBG_TX_level = HAL_GPIO_Read_Pin(DBG_TX_pin_xy);
        gpio_init_cfg(GNSS_RXD_pin_xy, 0, 0, 0);
		GNSS_RXD_level = HAL_GPIO_Read_Pin(GNSS_RXD_pin_xy);

        //U301
        /*配置为输出模式，高电平*/
        gpio_init_cfg(STATUS_pin_xy, 1, 0, 1);

        /*配置为输入模式，浮空*/
        gpio_init_cfg(USIM_DET_pin_xy, 0, 0, 0);
		USIM_DET_level = HAL_GPIO_Read_Pin(USIM_DET_pin_xy);

        /*配置为输出模式，高电平*/
        gpio_init_cfg(USIM_DET_pin_xy, 1, 0, 1);

        /*配置为输入模式，浮空*/
        gpio_init_cfg(STATUS_pin_xy, 0, 0, 0);
		STATUS_level = HAL_GPIO_Read_Pin(STATUS_pin_xy);

        result_low |= GRFC2_level;
        result_low|= BG95_GPIO7_level << 1;       
        result_low|= BG95_GPIO9_level << 2;
        result_low |= PSM_IND_level << 3;

        result_low |= GRFC1_level << 4;
        result_low |= BG95_GPIO6_level << 5;
        result_low |= BG95_GPIO8_level << 6;
        result_low |= NET_STATUS_level << 7;


        result_low |= MAIN_DTR_level << 8;
        result_low |= I2C_SCL_level << 9;
        result_low |= W_DISABLE_level << 10;
        result_low |= BG95_GPIO4_level << 11;

        result_low |= MAIN_DCD_level << 12;
        result_low |= I2C_SDA_level << 13;
        result_low |= BG95_GPIO3_level << 14;
        result_low |= BG95_GPIO5_level << 15;

        result_low |= BG95_GPIO1_level << 16;
        result_low |= PCM_DIN_level << 17;
        result_low |= PCM_DOUT_level << 18;
        result_low |= MAIN_RI_level << 19;

        result_low |= BG95_GPIO2_level << 20;
        result_low |= PCM_SYNC_level << 21;
        result_low |= AP_READY_level << 22;
        result_low |= PCM_CLK_level << 23;

        result_low |= MAIN_CTS_level << 24;
        result_low |= MAIN_TXD_level << 25;
        result_low |= DBG_TX_level << 26;
        result_low |= GNSS_RXD_level << 27;

        result_low |= MAIN_RTS_level << 28;
        result_low |= MAIN_RXD_level << 29;
        result_low |= DBG_RX_level << 30;
        result_low |= GNSS_TXD_level << 31;

        result_low |= STATUS_level << 32;
        result_low |= USIM_DET_level << 33;
        
        *prsp_cmd = xy_malloc(100);
        // snprintf(*prsp_cmd, 100, "+MPRODUCTMODE: %" PRIX64,result_low);
        snprintf(*prsp_cmd, 100, "+MPRODUCTMODE: %X%X",result_low>>32, result_low);
    }
    else 
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);

    return  AT_END;
}
#else
static int __get_xy_pinnum(int cm_pin_num)
{
    switch(cm_pin_num)
    {
        case 16:
            return LL_GPIO_PAD_NUM_6;

        case 19:
            return AGPIO_PIN1;

        case 20:
            return LL_GPIO_PAD_NUM_39;

        case 21:
            return LL_GPIO_PAD_NUM_23;

        case 22:
            return LL_GPIO_PAD_NUM_2;

        case 23:
            return LL_GPIO_PAD_NUM_3;

        case 25:
            return LL_GPIO_PAD_NUM_4;

        case 28:
            return LL_GPIO_PAD_NUM_25;

        case 29:
            return LL_GPIO_PAD_NUM_26;

        case 30:
            return LL_GPIO_PAD_NUM_0;

        case 31:
            return LL_GPIO_PAD_NUM_22;

        case 32:
            return LL_GPIO_PAD_NUM_1;

        case 33:
            return LL_GPIO_PAD_NUM_20;
		
        case 66:
            return LL_GPIO_PAD_NUM_29;

        case 67:
            return LL_GPIO_PAD_NUM_28;

        case 68:
            return LL_GPIO_PAD_NUM_17;

        case 69:
            return LL_GPIO_PAD_NUM_18;

        case 74:
            return LL_GPIO_PAD_NUM_24;

        case 75:
            return LL_GPIO_PAD_NUM_36;

        case 76:
            return LL_GPIO_PAD_NUM_21;

        case 77:
            return LL_GPIO_PAD_NUM_19;

        case 79:
            return LL_GPIO_PAD_NUM_7;

        case 83:
            return LL_GPIO_PAD_NUM_33;

        case 84:
            return LL_GPIO_PAD_NUM_14;

        case 85:
            return LL_GPIO_PAD_NUM_13;

        case 86:
            return LL_GPIO_PAD_NUM_35;

        case 87:
            return LL_GPIO_PAD_NUM_27;

        default:
            return -1;
    }
}

int at_MPRODUCTMODE_req(char *at_buf, char **prsp_cmd)  //add by cmiot
{
	HAL_AGPIO_InitTypeDef agpio_init = {0};
    uint32_t result_low = 0;
    
    int NETLIGHT_pin = 16;
    int UART0_DTR_pin = 19;         //WKP2引脚单独处理
    int UART0_RI_pin = 20;
    int UART0_DCD_pin = 21;
    int UART0_CTS_pin = 22;
    int UART0_RTS_pin = 23;
    int STATE_pin = 25;
    int UART1_RXD_pin = 28;
    int UART1_TXD_pin = 29;
    int RSV1_pin = 30;
    int RSV2_pin = 31;
    int RSV3_pin = 32;
    int RSV4_pin = 33;
    int RSV5_pin = 66;
    int RSV6_pin = 67;
    int RSV7_pin = 68;
    int RSV8_pin = 69;
    int PWM0_pin = 74;
    int PWM1_pin = 75;
    int GPIO0_pin = 76;
    int GPIO1_pin = 77;
    int SIM_pin = 79;
    int RSV9_pin = 83;
    int RSV10_pin = 84;
    int RSV11_pin = 85;
    int GPIO2_pin = 86;
    int GPIO3_pin = 87;

    int NETLIGHT_level = 0;
    int UART0_DTR_level = 0;       
    int UART0_RI_level = 0;
    int UART0_DCD_level = 0;
    int UART0_CTS_level = 0;
    int UART0_RTS_level = 0;
    int STATE_level = 0;
    int UART1_RXD_level = 0;
    int UART1_TXD_level = 0;
    int RSV1_level = 0;
    int RSV2_level = 0;
    int RSV3_level = 0;
    int RSV4_level = 0;
    int RSV5_level = 0;
    int RSV6_level = 0;
    int RSV7_level = 0;
    int RSV8_level = 0;
    int PWM0_level = 0;
    int PWM1_level = 0;
    int GPIO0_level = 0;
    int GPIO1_level = 0;
    int SIM_level = 0;
    int RSV9_level = 0;
    int RSV10_level = 0;
    int RSV11_level = 0;
    int GPIO2_level = 0;
    int GPIO3_level = 0;
    
    if(g_req_type == AT_CMD_REQ)
    {
        /* 引脚初始化设置 */
        int NETLIGHT_pin_xy = __get_xy_pinnum(NETLIGHT_pin);
        int UART0_DTR_pin_xy = AGPIO_PIN1;
        int UART0_RI_pin_xy = __get_xy_pinnum(UART0_RI_pin);
        int UART0_DCD_pin_xy = __get_xy_pinnum(UART0_DCD_pin);
        int UART0_CTS_pin_xy = __get_xy_pinnum(UART0_CTS_pin);
        int UART0_RTS_pin_xy = __get_xy_pinnum(UART0_RTS_pin);
        int STATE_pin_xy = __get_xy_pinnum(STATE_pin);
        int UART1_RXD_pin_xy = __get_xy_pinnum(UART1_RXD_pin);
        int UART1_TXD_pin_xy = __get_xy_pinnum(UART1_TXD_pin);
        int RSV1_pin_xy = __get_xy_pinnum(RSV1_pin);
        int RSV2_pin_xy = __get_xy_pinnum(RSV2_pin);
        int RSV3_pin_xy = __get_xy_pinnum(RSV3_pin);
        int RSV4_pin_xy = __get_xy_pinnum(RSV4_pin);
        int RSV5_pin_xy = __get_xy_pinnum(RSV5_pin);
        int RSV6_pin_xy = __get_xy_pinnum(RSV6_pin);
        int RSV7_pin_xy = __get_xy_pinnum(RSV7_pin);
        int RSV8_pin_xy = __get_xy_pinnum(RSV8_pin);
        int RSV9_pin_xy = __get_xy_pinnum(RSV9_pin);
        int RSV10_pin_xy = __get_xy_pinnum(RSV10_pin);
        int RSV11_pin_xy = __get_xy_pinnum(RSV11_pin);
        int PWM0_pin_xy = __get_xy_pinnum(PWM0_pin);
        int PWM1_pin_xy = __get_xy_pinnum(PWM1_pin);
        int GPIO0_pin_xy = __get_xy_pinnum(GPIO0_pin);
        int GPIO1_pin_xy = __get_xy_pinnum(GPIO1_pin);
        int SIM_pin_xy = __get_xy_pinnum(SIM_pin);
        int GPIO2_pin_xy = __get_xy_pinnum(GPIO2_pin);
        int GPIO3_pin_xy = __get_xy_pinnum(GPIO3_pin);

        /*配置为输入模式，浮空*/
        gpio_init_cfg(NETLIGHT_pin_xy, 0, 0, 0);
		NETLIGHT_level = LL_GPIO_Read_InputPin(NETLIGHT_pin_xy);
		
        gpio_init_cfg(UART0_RI_pin_xy, 0, 0, 0);
        UART0_RI_level = LL_GPIO_Read_InputPin(UART0_RI_pin_xy);
		
        gpio_init_cfg(UART0_DCD_pin_xy, 0, 0, 0);
        UART0_DCD_level = LL_GPIO_Read_InputPin(UART0_DCD_pin_xy);

        gpio_init_cfg(UART0_CTS_pin_xy, 0, 0, 0);
        UART0_CTS_level = LL_GPIO_Read_InputPin(UART0_CTS_pin_xy);

        gpio_init_cfg(UART0_RTS_pin_xy, 0, 0, 0);
        UART0_RTS_level = LL_GPIO_Read_InputPin(UART0_RTS_pin_xy);
		
        gpio_init_cfg(STATE_pin_xy, 0, 0, 0);
        STATE_level = LL_GPIO_Read_InputPin(STATE_pin_xy);

		gpio_init_cfg(UART1_RXD_pin_xy, 0, 0, 0);
        UART1_RXD_level = LL_GPIO_Read_InputPin(UART1_RXD_pin_xy);

        gpio_init_cfg(UART1_TXD_pin_xy, 0, 0, 0);
        UART1_TXD_level = LL_GPIO_Read_InputPin(UART1_TXD_pin_xy);

        gpio_init_cfg(RSV1_pin_xy, 0, 0, 0);
        RSV1_level = LL_GPIO_Read_InputPin(RSV1_pin_xy);

        gpio_init_cfg(RSV2_pin_xy, 0, 0, 0);
        RSV2_level = LL_GPIO_Read_InputPin(RSV2_pin_xy);

        gpio_init_cfg(RSV3_pin_xy, 0, 0, 0);
        RSV3_level = LL_GPIO_Read_InputPin(RSV3_pin_xy);

        gpio_init_cfg(RSV4_pin_xy, 0, 0, 0);
        RSV4_level = LL_GPIO_Read_InputPin(RSV4_pin_xy);

        gpio_init_cfg(RSV5_pin_xy, 0, 0, 0);
        RSV5_level = LL_GPIO_Read_InputPin(RSV5_pin_xy);

        gpio_init_cfg(RSV6_pin_xy, 0, 0, 0);
        RSV6_level = LL_GPIO_Read_InputPin(RSV6_pin_xy);
		
        gpio_init_cfg(RSV7_pin_xy, 0, 0, 0);
        RSV7_level = LL_GPIO_Read_InputPin(RSV7_pin_xy);
		
        gpio_init_cfg(RSV8_pin_xy, 0, 0, 0);
        RSV8_level = LL_GPIO_Read_InputPin(RSV8_pin_xy);

        gpio_init_cfg(RSV9_pin_xy, 0, 0, 0);
        RSV9_level = LL_GPIO_Read_InputPin(RSV9_pin_xy);

        gpio_init_cfg(RSV10_pin_xy, 0, 0, 0);
        RSV10_level = LL_GPIO_Read_InputPin(RSV10_pin_xy);

        gpio_init_cfg(RSV11_pin_xy, 0, 0, 0);
        RSV11_level = LL_GPIO_Read_InputPin(RSV11_pin_xy);
		
        gpio_init_cfg(PWM0_pin_xy, 0, 0, 0);
        PWM0_level = LL_GPIO_Read_InputPin(PWM0_pin_xy);
		
        gpio_init_cfg(PWM1_pin_xy, 0, 0, 0);
        PWM1_level = LL_GPIO_Read_InputPin(PWM1_pin_xy);
		
        gpio_init_cfg(GPIO0_pin_xy, 0, 0, 0);
        GPIO0_level = LL_GPIO_Read_InputPin(GPIO0_pin_xy);

        gpio_init_cfg(GPIO1_pin_xy, 0, 0, 0);
        GPIO1_level = LL_GPIO_Read_InputPin(GPIO1_pin_xy);

        gpio_init_cfg(SIM_pin_xy, 0, 0, 0);
        SIM_level = LL_GPIO_Read_InputPin(SIM_pin_xy);

		gpio_init_cfg(GPIO2_pin_xy, 0, 0, 0);
        GPIO2_level = LL_GPIO_Read_InputPin(GPIO2_pin_xy);

        gpio_init_cfg(GPIO3_pin_xy, 0, 0, 0);
        GPIO3_level = LL_GPIO_Read_InputPin(GPIO3_pin_xy);

		HAL_Delay_US(20*1000);

        /*读取电平状态*/
//        UART0_DTR_level = HAL_AGPIO_Read_Pin(UART0_DTR_pin_xy);
        UART0_DTR_level = 1;                        //DTR不参与产测，置1
        result_low |= NETLIGHT_level;
        result_low|= UART0_DTR_level << 1;       
        result_low|= UART0_RI_level << 2;
        result_low |= UART0_DCD_level << 3;
        result_low |= UART0_CTS_level << 4;
        result_low |= UART0_RTS_level << 5;
        result_low |= STATE_level << 6;
        result_low |= UART1_RXD_level << 7;
        result_low |= UART1_TXD_level << 8;
        result_low |= RSV1_level << 9;
        result_low |= RSV2_level << 10;
        result_low |= RSV3_level << 11;
        result_low |= RSV4_level << 12;
        result_low |= RSV5_level << 13;
        result_low |= RSV6_level << 14;
        result_low |= RSV7_level << 15;
        result_low |= RSV8_level << 16;
        result_low |= PWM0_level << 17;
        result_low |= PWM1_level << 18;
        result_low |= GPIO0_level << 19;
        result_low |= GPIO1_level << 20;
        result_low |= SIM_level << 21;
        result_low |= RSV9_level << 22;
        result_low |= RSV10_level << 23;
        result_low |= RSV11_level << 24;
        result_low |= GPIO2_level << 25;
        result_low |= GPIO3_level << 26;
        
        *prsp_cmd = xy_malloc(100);
        snprintf(*prsp_cmd, 100, "+MPRODUCTMODE: %X",result_low);
    }
    else 
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);

    return  AT_END;
}
#endif
#else
static int __get_xy_pinnum(int cm_pin_num)
{
    switch(cm_pin_num)
    {
        case 16:
            return LL_GPIO_PAD_NUM_6;

        case 20:
            return LL_GPIO_PAD_NUM_17;
        case 21:
            return LL_GPIO_PAD_NUM_18;
        case 22:
            return LL_GPIO_PAD_NUM_2;
        case 23:
            return LL_GPIO_PAD_NUM_3;
        case 25:
            return LL_GPIO_PAD_NUM_5;
        case 28:
            return LL_GPIO_PAD_NUM_25;
        case 29:
            return LL_GPIO_PAD_NUM_26;

        case 49:
            return LL_GPIO_PAD_NUM_32;

        case 74:
            return LL_GPIO_PAD_NUM_4;
        case 75:
            return LL_GPIO_PAD_NUM_14;
        case 76:
            return LL_GPIO_PAD_NUM_13;
        case 77:
            return LL_GPIO_PAD_NUM_1;
        case 79:
            return LL_GPIO_PAD_NUM_7;

        case 86:
            return LL_GPIO_PAD_NUM_20;
        case 87:
            return LL_GPIO_PAD_NUM_19;

        default: return -1;
    }
}

int at_MPRODUCTMODE_req(char *at_buf, char **prsp_cmd)  //add by cmiot
{
    uint32_t result_low = 0;
    
    int NETLIGHT_pin  = 16;
    int UART0_RI_pin  = 20;
    int UART0_DCD_pin = 21;
    int UART0_CTS_pin = 22;
    int UART0_RTS_pin = 23;
    int STATE_pin     = 25;
    int UART1_RXD_pin = 28;
    int UART1_TXD_pin = 29;
    int WAKEUPOUT_Pin = 49;
    int PWM0_pin      = 74;
    int PWM1_pin      = 75;
    int GPIO0_pin     = 76;
    int GPIO1_pin     = 77;
    int SIM_pin       = 79;
    int GPIO2_pin     = 86;
    int GPIO3_pin     = 87;

    int NETLIGHT_level  = 0;
    int UART0_RI_level  = 0;
    int UART0_DCD_level = 0;
    int UART0_CTS_level = 0;
    int UART0_RTS_level = 0;
    int STATE_level     = 0;
    int UART1_RXD_level = 0;
    int UART1_TXD_level = 0;
    int WAKEUPOUT_level = 0;
    int PWM0_level      = 0;
    int PWM1_level      = 0;
    int GPIO0_level     = 0;
    int GPIO1_level     = 0;
    int SIM_level       = 0;
    int GPIO2_level     = 0;
    int GPIO3_level     = 0;
    
    if(g_req_type == AT_CMD_REQ)
    {
        /* 引脚初始化设置 */
        int NETLIGHT_pin_xy  = __get_xy_pinnum(NETLIGHT_pin);
        int UART0_RI_pin_xy  = __get_xy_pinnum(UART0_RI_pin);
        int UART0_DCD_pin_xy = __get_xy_pinnum(UART0_DCD_pin);
        int UART0_CTS_pin_xy = __get_xy_pinnum(UART0_CTS_pin);
        int UART0_RTS_pin_xy = __get_xy_pinnum(UART0_RTS_pin);
        int STATE_pin_xy     = __get_xy_pinnum(STATE_pin);
        int UART1_RXD_pin_xy = __get_xy_pinnum(UART1_RXD_pin);
        int UART1_TXD_pin_xy = __get_xy_pinnum(UART1_TXD_pin);
        int WAKEUPOUT_Pin_xy = __get_xy_pinnum(WAKEUPOUT_Pin);
        int PWM0_pin_xy      = __get_xy_pinnum(PWM0_pin);
        int PWM1_pin_xy      = __get_xy_pinnum(PWM1_pin);
        int GPIO0_pin_xy     = __get_xy_pinnum(GPIO0_pin);
        int GPIO1_pin_xy     = __get_xy_pinnum(GPIO1_pin);
        int SIM_pin_xy       = __get_xy_pinnum(SIM_pin);
        int GPIO2_pin_xy     = __get_xy_pinnum(GPIO2_pin);
        int GPIO3_pin_xy     = __get_xy_pinnum(GPIO3_pin);

        /*配置为输入模式，浮空*/
        gpio_init_cfg(NETLIGHT_pin_xy, 0, 0, 0);
		NETLIGHT_level = LL_GPIO_Read_InputPin(NETLIGHT_pin_xy);
		
        gpio_init_cfg(UART0_RI_pin_xy, 0, 0, 0);
        UART0_RI_level = LL_GPIO_Read_InputPin(UART0_RI_pin_xy);
		
        gpio_init_cfg(UART0_DCD_pin_xy, 0, 0, 0);
        UART0_DCD_level = LL_GPIO_Read_InputPin(UART0_DCD_pin_xy);

        gpio_init_cfg(UART0_CTS_pin_xy, 0, 0, 0);
        UART0_CTS_level = LL_GPIO_Read_InputPin(UART0_CTS_pin_xy);

        gpio_init_cfg(UART0_RTS_pin_xy, 0, 0, 0);
        UART0_RTS_level = LL_GPIO_Read_InputPin(UART0_RTS_pin_xy);
		
        gpio_init_cfg(STATE_pin_xy, 0, 0, 0);
        STATE_level = LL_GPIO_Read_InputPin(STATE_pin_xy);

		gpio_init_cfg(UART1_RXD_pin_xy, 0, 0, 0);
        UART1_RXD_level = LL_GPIO_Read_InputPin(UART1_RXD_pin_xy);

        gpio_init_cfg(UART1_TXD_pin_xy, 0, 0, 0);
        UART1_TXD_level = LL_GPIO_Read_InputPin(UART1_TXD_pin_xy);

        gpio_init_cfg(WAKEUPOUT_Pin_xy, 0, 0, 0);
        WAKEUPOUT_level = LL_GPIO_Read_InputPin(WAKEUPOUT_Pin_xy);
		
        gpio_init_cfg(PWM0_pin_xy, 0, 0, 0);
        PWM0_level = LL_GPIO_Read_InputPin(PWM0_pin_xy);
		
        gpio_init_cfg(PWM1_pin_xy, 0, 0, 0);
        PWM1_level = LL_GPIO_Read_InputPin(PWM1_pin_xy);
		
        gpio_init_cfg(GPIO0_pin_xy, 0, 0, 0);
        GPIO0_level = LL_GPIO_Read_InputPin(GPIO0_pin_xy);

        gpio_init_cfg(GPIO1_pin_xy, 0, 0, 0);
        GPIO1_level = LL_GPIO_Read_InputPin(GPIO1_pin_xy);

        gpio_init_cfg(SIM_pin_xy, 0, 0, 0);
        SIM_level = LL_GPIO_Read_InputPin(SIM_pin_xy);

		gpio_init_cfg(GPIO2_pin_xy, 0, 0, 0);
        GPIO2_level = LL_GPIO_Read_InputPin(GPIO2_pin_xy);

        gpio_init_cfg(GPIO3_pin_xy, 0, 0, 0);
        GPIO3_level = LL_GPIO_Read_InputPin(GPIO3_pin_xy);

        HAL_Delay_US(20 * 1000);

        /*读取电平状态*/
        result_low |= NETLIGHT_level;
        result_low |= UART0_RI_level  << 1;
        result_low |= UART0_DCD_level << 2;
        result_low |= UART0_CTS_level << 3;
        result_low |= UART0_RTS_level << 4;
        result_low |= STATE_level     << 5;
        result_low |= UART1_RXD_level << 6;
        result_low |= UART1_TXD_level << 7;
        result_low |= WAKEUPOUT_level << 8;
        result_low |= PWM0_level      << 9;
        result_low |= PWM1_level      << 10;
        result_low |= GPIO0_level     << 11;
        result_low |= GPIO1_level     << 12;
        result_low |= SIM_level       << 13;
        result_low |= GPIO2_level     << 14;
        result_low |= GPIO3_level     << 15;
        
        *prsp_cmd = xy_malloc(100);
        snprintf(*prsp_cmd, 100, "+MPRODUCTMODE: %X",result_low);
    }
    else 
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

    return  AT_END;
}

#endif

