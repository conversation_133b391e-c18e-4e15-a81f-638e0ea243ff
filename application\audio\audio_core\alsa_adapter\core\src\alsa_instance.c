#include "alsa_config.h"
#include "alsa_instance.h"
#include "alsa.h"
#include "alsa_os.h"
#include "cqueue.h"
#include <string.h>
#include "hal_trace.h"
#include "alsa_extra.h"
#include "alsa_deamon.h"

// #define ALSA_INSTANCE_DEBUG


#ifdef ALSA_SUPPORT_CAPTURE
static uint8_t alsa_capture_instance_buffer[ALSA_CAPTURE_QUEUE_SIZE * ALSA_CAPTURE_INSTANCE_COUNT] NO_INIT;
#endif

static alsa_os_mutex_t alsa_instance_mutex = NULL;

static struct alsa_instance alsa_instance_array[ALSA_INSTANCE_COUNT] = {0};
#ifdef ALSA_MIXER_PRIORITY_EN
static uint8_t alsa_mixer_priority_high = 0;
#endif

static struct alsa_instance *acquire_useable_alsa_instance(alsa_instance_type_t type)
{
    int idle_alsa_instance_id = -1;
    alsa_os_mutex_wait(alsa_instance_mutex, osWaitForever);
    for (int i = 0; i < ALSA_INSTANCE_COUNT; i++)
    {
        if (alsa_instance_array[i].used == 0 &&
            alsa_instance_array[i].type == type)
        {
            idle_alsa_instance_id = i;
            alsa_instance_array[i].used = 1;
            break;
        }
    }
    alsa_os_mutex_release(alsa_instance_mutex);

    if (idle_alsa_instance_id == -1)
    {
        return NULL;
    }
    else
    {
        if (idle_alsa_instance_id < 0 || idle_alsa_instance_id >= ALSA_INSTANCE_COUNT)
        {
            ASSERT(0, "%s %d idle_alsa_instance_id = %d error", __func__, __LINE__, idle_alsa_instance_id);
        }
        return &alsa_instance_array[idle_alsa_instance_id];
    }
}

static int release_used_alsa_instance(struct alsa_instance *alsa_instance)
{
    if (!alsa_instance)
    {
        TRACE(0, "%s alsa_instance NULL error", __func__);
        return -1;
    }
    alsa_os_mutex_wait(alsa_instance_mutex, osWaitForever);
    alsa_instance->used = 0;
    if(alsa_instance->buffer)
    {
        xy_free(alsa_instance->buffer);
        alsa_instance->buffer = NULL;
        alsa_instance->buffer_size = 0;
    }
    alsa_os_mutex_release(alsa_instance_mutex);
    return 0;
}

alsa_instance_t *alsa_get_instance_by_id(alsa_instance_id_t id)
{
    if (!alsa_instance_id_check_avail(id))
        return NULL;

    alsa_instance_t *alsa_instance = NULL;
    alsa_os_mutex_wait(alsa_instance_mutex, osWaitForever);
    alsa_instance = &alsa_instance_array[id];
    if (alsa_instance->used == 0)
    {
        alsa_instance = NULL;
    }
    alsa_os_mutex_release(alsa_instance_mutex);
    return alsa_instance;
}

int alsa_instance_id_bind_user_arg(alsa_instance_id_t id, void *user_arg)
{
    if (!alsa_instance_id_check_avail(id))
        return -1;

    alsa_instance_t *alsa_instance = NULL;
    alsa_os_mutex_wait(alsa_instance_mutex, osWaitForever);
    alsa_instance = &alsa_instance_array[id];
    alsa_instance->user_arg = user_arg;
    alsa_os_mutex_release(alsa_instance_mutex);
    return 0;
}

void *alsa_instance_get_user_arg_by_id(alsa_instance_id_t id)
{
    if (!alsa_instance_id_check_avail(id))
        return NULL;

    void *user_arg = NULL;
    alsa_instance_t *alsa_instance = NULL;
    alsa_os_mutex_wait(alsa_instance_mutex, osWaitForever);
    alsa_instance = &alsa_instance_array[id];
    user_arg = alsa_instance->user_arg;
    alsa_os_mutex_release(alsa_instance_mutex);
    return user_arg;
}

void alsa_instance_init(void)
{
    alsa_instance_mutex = alsa_os_mutex_create();
    if (!alsa_instance_mutex)
    {
        ASSERT(0, "%s alsa_instance_mutex create error", __func__);
    }

    int index = 0;
    for (; index < ALSA_PLAYBACK_INSTANCE_COUNT; index++)
    {
        alsa_instance_array[index].instance_id = index;
        alsa_instance_array[index].buffer = NULL;
        alsa_instance_array[index].buffer_size = 0;
        alsa_instance_array[index].lock = alsa_os_mutex_create();
        if (!alsa_instance_array[index].lock)
        {
            ASSERT(0, "%s alsa_instance_array[%d].lock create error", __func__, index);
        }

        alsa_instance_array[index].used = 0;
        alsa_instance_array[index].type = ALSA_INS_PLAYBACK;
    }
#ifdef ALSA_SUPPORT_CAPTURE
    for (; index < ALSA_CAPTURE_INSTANCE_COUNT + ALSA_PLAYBACK_INSTANCE_COUNT; index++)
    {
        alsa_instance_array[index].instance_id = index;
        alsa_instance_array[index].buffer = &alsa_capture_instance_buffer[(index - ALSA_PLAYBACK_INSTANCE_COUNT) * ALSA_CAPTURE_QUEUE_SIZE];
        alsa_instance_array[index].buffer_size = ALSA_CAPTURE_QUEUE_SIZE;
        alsa_instance_array[index].lock = alsa_os_mutex_create();
        if (!alsa_instance_array[index].lock)
        {
            ASSERT(0, "%s alsa_instance_array[%d].lock create error", __func__, index);
        }
        if (0 != InitCQueue(&alsa_instance_array[index].cqueue, alsa_instance_array[index].buffer_size, alsa_instance_array[index].buffer))
        {
            ASSERT(0, "%s alsa_instance_array[%d].cqueue create error", __func__, index);
        }
        alsa_instance_array[index].used = 0;
        alsa_instance_array[index].type = ALSA_INS_CAPTURE;
    }
#endif

}

alsa_instance_t *alsa_instance_open(alsa_instance_type_t type, uint32_t instance_buffer_size)
{
    alsa_instance_t *alsa_instance = acquire_useable_alsa_instance(type);
    if(instance_buffer_size != 0)
    {
        alsa_instance->buffer = xy_malloc(instance_buffer_size);
        alsa_instance->buffer_size = instance_buffer_size;
        if (0 != InitCQueue(&(alsa_instance->cqueue), alsa_instance->buffer_size, alsa_instance->buffer))
        {
            ASSERT(0, "%s alsa_instance_array[%d].cqueue create error", __func__, index);
        }
    }
    else
    {
        alsa_instance->buffer = NULL;
        alsa_instance->buffer_size = 0;
    }

    return alsa_instance;
}

int alsa_instance_close(alsa_instance_t *alsa_instance)
{
    if (!alsa_instance)
    {
        TRACE(0, "%s alsa_instance NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(alsa_instance->lock, osWaitForever);
    release_used_alsa_instance(alsa_instance);
    alsa_os_mutex_release(alsa_instance->lock);
    return 0;
}

int alsa_instance_write(alsa_instance_t *alsa_instance, uint8_t *buf, uint32_t len)
{
    if (!alsa_instance)
    {
        TRACE(0, "%s alsa_instance NULL error", __func__);
        return -1;
    }

    uint32_t write_length = 0;

    alsa_os_mutex_wait(alsa_instance->lock, osWaitForever);
    write_length = alsa_instance_get_writeable_length(alsa_instance);
    write_length = len > write_length ? write_length : len;
    EnCQueue(&alsa_instance->cqueue, (CQItemType *)buf, write_length);
    alsa_os_mutex_release(alsa_instance->lock);

    return write_length;
}

int alsa_instance_read(alsa_instance_t *alsa_instance, uint8_t *buf, uint32_t len)
{
    if (!alsa_instance)
    {
        TRACE(0, "%s alsa_instance NULL error", __func__);
        return -1;
    }

    uint32_t read_length = 0;
    uint32_t readable_length = 0;

    alsa_os_mutex_wait(alsa_instance->lock, osWaitForever);

    readable_length = LengthOfCQueue(&alsa_instance->cqueue);
    if (readable_length == 0)
    {
        alsa_os_mutex_release(alsa_instance->lock);
        return 0;
    }

    read_length = len > readable_length ? readable_length : len;

    if (alsa_instance->state != ALSA_STATE_STOP)
    {
        if (read_length && read_length != len && alsa_instance->type == ALSA_INS_PLAYBACK)
        {
            TRACE(0, "%s want %d real %d", __func__, len, read_length);
        }
    }

    if (read_length)
    {
        DeCQueue(&alsa_instance->cqueue, (CQItemType *)buf, read_length);
    }

    alsa_os_mutex_release(alsa_instance->lock);
    return read_length;
}

int alsa_instance_flush(alsa_instance_t *alsa_instance)
{
    if (!alsa_instance)
    {
        TRACE(0, "%s alsa_instance NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(alsa_instance->lock, osWaitForever);
    ResetCQueue(&alsa_instance->cqueue);
    memset(alsa_instance->buffer, 0, alsa_instance->buffer_size);
    alsa_os_mutex_release(alsa_instance->lock);
    return 0;
}

int alsa_instance_get_readable_length(alsa_instance_t *alsa_instance)
{
    if (!alsa_instance)
    {
        TRACE(0, "%s alsa_instance NULL error", __func__);
        return -1;
    }

    int length = 0;
    alsa_os_mutex_wait(alsa_instance->lock, osWaitForever);
    length = LengthOfCQueue(&alsa_instance->cqueue);
    alsa_os_mutex_release(alsa_instance->lock);
    return length;
}

int alsa_instance_get_writeable_length(alsa_instance_t *alsa_instance)
{
    if (!alsa_instance)
    {
        TRACE(0, "%s alsa_instance NULL error", __func__);
        return -1;
    }

    int length = 0;
    alsa_os_mutex_wait(alsa_instance->lock, osWaitForever);
    length = (int)AvailableOfCQueue(&alsa_instance->cqueue);
    alsa_os_mutex_release(alsa_instance->lock);
    return length;
}

int alsa_instance_id_check_avail(alsa_instance_id_t id)
{
    if (id >= 0 && id < ALSA_INSTANCE_COUNT)
    {
        return 1;
    }
    else
    {
        TRACE(0, "%s id = %d error", __func__, id);
        return 0;
    }
}

alsa_instance_id_t alsa_instance_get_id(alsa_instance_t *alsa_instance)
{
    if (!alsa_instance)
    {
        TRACE(0, "%s alsa_instance NULL error", __func__);
        return -1;
    }

    alsa_instance_id_t id = -1;

    alsa_os_mutex_wait(alsa_instance->lock, osWaitForever);
    id = alsa_instance->instance_id;
    alsa_os_mutex_release(alsa_instance->lock);

    return id;
}

alsa_state_t alsa_instance_get_state(alsa_instance_t *alsa_instance)
{
    if (!alsa_instance)
    {
        TRACE(0, "%s alsa_instance NULL error", __func__);
        return -1;
    }

    alsa_state_t state;

    alsa_os_mutex_wait(alsa_instance->lock, osWaitForever);
    state = alsa_instance->state;
    alsa_os_mutex_release(alsa_instance->lock);

    return state;
}

int alsa_instance_set_state(alsa_instance_t *alsa_instance, alsa_state_t state)
{
    if (!alsa_instance)
    {
        TRACE(0, "%s alsa_instance NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(alsa_instance->lock, osWaitForever);
    alsa_instance->state = state;
    alsa_os_mutex_release(alsa_instance->lock);

    return 0;
}

void alsa_all_instance_info_printf(const alsa_instance_info_t *info)
{
#if (ALSA_INSTANCE_COUNT == 3)
    TRACE(0, "[%.5d|%.5d|%.5d]", info->target_size_array[0], info->target_size_array[1], info->target_size_array[2]);
#endif
#if (ALSA_INSTANCE_COUNT == 6)
    TRACE(0, "playback: [%.5d|%.5d|%.5d|%.5d|%.5d|%.5d]",
          info->target_size_array[0], info->target_size_array[1], info->target_size_array[2],
          info->target_size_array[3], info->target_size_array[4], info->target_size_array[5]);
#endif
}

int alsa_get_all_instance_info(alsa_instance_info_t *info, alsa_instance_type_t target_ins_type)
{
    if (!info)
    {
        TRACE(0, "%s info NULL error", __func__);
        return -1;
    }

    if (!(target_ins_type == ALSA_INS_PLAYBACK || target_ins_type == ALSA_INS_CAPTURE))
    {
        TRACE(0, "%s target_ins_type %d error", __func__, target_ins_type);
        return -1;
    }

    alsa_instance_t *alsa_instance = NULL;
    uint32_t readable_size;
    uint32_t writeable_size;
    uint32_t target_size;
#ifdef ALSA_MIXER_PRIORITY_EN
    uint8_t mixer_priority = 0;
#endif
    memset(&info->target_size_array, 0, sizeof(info->target_size_array));
    alsa_os_mutex_wait(alsa_instance_mutex, osWaitForever);
    for (int i = 0; i < ALSA_INSTANCE_COUNT; i++)
    {
        alsa_instance = &alsa_instance_array[i];
        if (alsa_instance->used && alsa_instance->type == target_ins_type)
        {
            readable_size = alsa_instance_get_readable_length(alsa_instance);
            writeable_size = alsa_instance_get_writeable_length(alsa_instance);
            target_size = (target_ins_type == ALSA_INS_PLAYBACK) ? readable_size : writeable_size;
            if (target_size > 0)
            {
                if (alsa_instance->state == ALSA_STATE_START)
                {
                    info->target_instance_count++;
                    info->target_instance_bit_map |= (0x01 << i);
                    info->target_size_array[i] = target_size;
                }
                if (target_ins_type == ALSA_INS_PLAYBACK)
                {
                    if (alsa_instance->pcm_state == ALSA_PCM_STATE_UNDERRUN)
                    {
                        alsa_instance->pcm_state = ALSA_PCM_STATE_UNDERRUN_EXIT;
                        ALSA_THIRDPARTY_TRACE(0, "alsa_instance id = %d, UNDERRUN EXIT", i);
                        alsa_deamon_pcm_state_notify(ALSA_STREAM_PLAYBACK, alsa_instance->pcm_state, alsa_instance->instance_id);
                    }
                }
            }
            else if (target_size == 0)
            {
                if (alsa_instance->state == ALSA_STATE_START)
                {
                    if (target_ins_type == ALSA_INS_PLAYBACK)
                    {
                        if (alsa_instance->pcm_state != ALSA_PCM_STATE_UNDERRUN)
                        {
                            alsa_instance->pcm_state = ALSA_PCM_STATE_UNDERRUN;
                            ALSA_THIRDPARTY_TRACE(0, "alsa_instance id = %d, UNDERRUN", i);
                            alsa_deamon_pcm_state_notify(ALSA_STREAM_PLAYBACK, alsa_instance->pcm_state, alsa_instance->instance_id);
                        }
                    }
                }
            }
        }
    }
    alsa_os_mutex_release(alsa_instance_mutex);
    return 0;
}

#ifdef ALSA_SW_GAIN_PROCESS_EN
int alsa_all_instance_sw_gain_process(alsa_instance_info_t *info, uint32_t len)
{
    alsa_instance_t *alsa_instance = NULL;
    alsa_instance_id_t id = -1;
    uint8_t *instance_peek_ptr_1 = NULL;
    uint8_t *instance_peek_ptr_2 = NULL;
    uint32_t instance_peek_len_1 = 0;
    uint32_t instance_peek_len_2 = 0;
    alsa_sw_gain_t *sw_gain = NULL;
    alsa_instance_bit_map_t readable_instance_bit_map = 0;
    alsa_instance_count_t readable_instance_count = 0;
    uint32_t instance_read_len = {0};

    if (!info)
    {
        TRACE(0, "%s info NULL error", __func__);
        return -1;
    }

    if (info->target_instance_count == 0)
    {
        TRACE(0, "%s target_instance_count == %d error", __func__, info->target_instance_count);
        return 0;
    }

#ifndef ALSA_MIXER_LIMITER_EN
    if (info->target_instance_count == 1)
    {
        TRACE(0, "%s target_instance_count == 1", __func__);
    }
#endif

    readable_instance_bit_map = info->target_instance_bit_map;
    readable_instance_count = info->target_instance_count;
    while (readable_instance_count)
    {
        id = get_lsb_pos(readable_instance_bit_map);
        alsa_instance = alsa_get_instance_by_id(id);
        instance_read_len = info->target_size_array[id] > len ? len : info->target_size_array[id];
        sw_gain = alsa_get_sw_gain_by_id(id);
#ifdef ALSA_MIXER_PRIORITY_EN
        if (alsa_instance->mixer_priority < alsa_mixer_priority_high)
        {
            if (!alsa_instance->mixer_fadeout)
            {
                TRACE(0, "mixer_priority %d, high %d, fadeout", alsa_instance->mixer_priority, alsa_mixer_priority_high);
                alsa_instance->mixer_fadeout = true;
            }
            alsa_sw_gain_fadeinout_finish_notify_register(sw_gain,
                                                          NULL,
                                                          NULL);
            alsa_sw_gain_fade_out(sw_gain, 0);
        }
#endif
        PeekCQueue(&alsa_instance->cqueue, instance_read_len, (CQItemType **)&instance_peek_ptr_1, &instance_peek_len_1, (CQItemType **)&instance_peek_ptr_2, &instance_peek_len_2);
        if (instance_peek_len_1)
        {
            alsa_sw_gain_pre_compensation_process(sw_gain, instance_peek_ptr_1, instance_peek_len_1);
            alsa_sw_gain_process(sw_gain, instance_peek_ptr_1, &instance_peek_len_1);
            instance_peek_ptr_1 = NULL;
            instance_peek_len_1 = 0;
        }
        if (instance_peek_len_2)
        {
            alsa_sw_gain_pre_compensation_process(sw_gain, instance_peek_ptr_2, instance_peek_len_2);
            alsa_sw_gain_process(sw_gain, instance_peek_ptr_2, &instance_peek_len_2);
            instance_peek_ptr_2 = NULL;
            instance_peek_len_2 = 0;
        }
        readable_instance_bit_map &= ~(0x01 << id);
        readable_instance_count--;
    }
    id = -1;

    return len;
}
#endif

#ifdef ALSA_SUPPORT_THRESHOLD
int alsa_instance_set_threshold(alsa_instance_t *alsa_instance, uint32_t threshold)
{
    if (!alsa_instance)
    {
        TRACE(0, "%s alsa_instance NULL error", __func__);
        return -1;
    }

    if (threshold > ALSA_PLAYBACK_QUEUE_SIZE)
    {
        TRACE(0, "%s threshold %d is too large error", __func__, threshold);
        return -1;
    }

    alsa_os_mutex_wait(alsa_instance->lock, osWaitForever);
    alsa_instance->threshold = threshold;
    alsa_os_mutex_release(alsa_instance->lock);

    TRACE(0, "%s to %d", __func__, threshold);

    return 0;
}

int alsa_instance_threshold_enable(alsa_instance_t *alsa_instance, uint8_t enable)
{
    alsa_os_mutex_wait(alsa_instance->lock, osWaitForever);
    if (enable)
    {
        alsa_instance->threshold_enable = 1;
    }
    else
    {
        alsa_instance->threshold_enable = 0;
    }
    alsa_os_mutex_release(alsa_instance->lock);
    TRACE(0, "%s %d", __func__, enable);
    return 0;
}
#endif

int alsa_instance_lock(alsa_instance_t *alsa_instance, uint8_t lock)
{
    if (!alsa_instance)
    {
        TRACE(0, "%s alsa_instance NULL error", __func__);
        return -1;
    }

    if (lock)
    {
        alsa_os_mutex_wait(alsa_instance->lock, osWaitForever);
    }
    else
    {
        alsa_os_mutex_release(alsa_instance->lock);
    }

    return 0;
}

int alsa_instance_set_mixer_priority(alsa_instance_id_t instance_id, uint8_t priority)
{
#ifdef ALSA_MIXER_PRIORITY_EN
    if (priority > ALSA_PLAYBACK_INSTANCE_COUNT)
    {
        ASSERT(0, "%s id %d, priority %d error", __func__, instance_id, priority);
    }
    alsa_os_mutex_wait(alsa_instance_mutex, osWaitForever);
    alsa_instance_t *alsa_instance = &alsa_instance_array[instance_id];
    alsa_instance->mixer_priority = priority;
    if (priority > alsa_mixer_priority_high)
    {
        alsa_mixer_priority_high = priority;
    }
    alsa_os_mutex_release(alsa_instance_mutex);
    return 0;
#else
    TRACE(0, "%s current not support", __func__);
    return 0;
#endif
}

bool alsa_instance_get_mixer_fadeout_state(alsa_instance_id_t instance_id)
{
#ifdef ALSA_MIXER_PRIORITY_EN
    bool fadeout;
    alsa_os_mutex_wait(alsa_instance_mutex, osWaitForever);
    alsa_instance_t *alsa_instance = &alsa_instance_array[instance_id];
    fadeout = alsa_instance->mixer_fadeout;
    alsa_os_mutex_release(alsa_instance_mutex);
    return fadeout;
#else
    TRACE(0, "%s current not support", __func__);
    return 0;
#endif
}

int alsa_instance_mixer_fadeout_state_reset(alsa_instance_id_t instance_id)
{
#ifdef ALSA_MIXER_PRIORITY_EN
    alsa_os_mutex_wait(alsa_instance_mutex, osWaitForever);
    alsa_instance_t *alsa_instance = &alsa_instance_array[instance_id];
    alsa_instance->mixer_fadeout = false;
    alsa_os_mutex_release(alsa_instance_mutex);
    return 0;
#else
    TRACE(0, "%s current not support", __func__);
    return 0;
#endif
}
