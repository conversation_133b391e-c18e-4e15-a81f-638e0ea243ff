#include "alsa_config.h"
#include "alsa_playback.h"
#include <stdlib.h>
#include <string.h>
#include "audioflinger.h"
#include "alsa.h"
#include "alsa_extra.h"
#include "alsa_os.h"
#include "alsa_deamon.h"
#include "alsa_instance.h"
#include "hal_trace.h"
#include "cmsis_os2.h"
#include "heap_api.h"

struct alsa_playback
{
    alsa_instance_t *instance;
    alsa_instance_id_t id;
    uint32_t sample_rate;
    uint8_t channels;
    uint8_t bits;
    alsa_state_t state;
    alsa_os_mutex_t lock;
    alsa_pcm_state_callback_t cb;
    void *cb_arg;
    alsa_user_type_t user_type;
    alsa_trigger_level_t trigger_level;
    alsa_data_pull_push_cb_t data_push_cb;
#ifdef ALSA_RESAMPLE_PROCESS_EN
    alsa_resample_t *resample;
#endif
#ifdef ALSA_SW_GAIN_PROCESS_EN
    alsa_sw_gain_t *sw_gain;
#endif
#ifdef ALSA_SW_SINGLE_TONE_DEBUG_EN
    void *sw_single_tone_debug;
#endif
#ifdef ALSA_PLAYBACK_START_DELAY_EN
    int maxdelaytime;
#endif
};

static uint32_t get_trigger_size(alsa_playback_t *playback);
static int alsa_write_internal(alsa_playback_t *playback, uint8_t *buf, uint32_t len);

#ifdef ALSA_SW_GAIN_PROCESS_EN
static int sw_gain_fade_in_finish_notify(uint8_t sw_gain_id);
static int sw_gain_fade_out_finish_notify(uint8_t sw_gain_id);
#endif

#ifdef ALSA_PLAYBACK_START_DELAY_EN
extern void alsa_deamon_set_playback_user_type(alsa_user_type_t type);
extern void alsa_deamon_set_playback_maxdelaytime(int delay_ms);
#endif

#ifdef ALSA_WRITE_CONVERT_MONO_TO_STEREO
extern int alsa_mono_to_stereo_16bit(uint8_t id, uint8_t **buf, uint32_t *len);
#endif

static uint8_t alsa_volume = 15;

// Interface

void alsa_playback_set_volume(uint8_t volume)
{
    if(volume > 15)
    {
        volume = 15;
    }

    alsa_volume = volume;
}

void alsa_playback_get_volume(uint8_t *volume)
{
    *volume = alsa_volume;
}

alsa_playback_t *alsa_playback_open(uint32_t sample_rate,
                                    uint8_t channels,
                                    uint8_t bits)
{
    enum AUD_CHANNEL_NUM_T real_ch_num;
    alsa_playback_t *playback = NULL;
    alsa_instance_t *instance = NULL;
    alsa_instance_id_t id = -1;
    alsa_os_mutex_t lock = NULL;
    uint32_t playback_instance_buffer_size;

    xy_assert(bits == 16);

    playback = (alsa_playback_t *)audio_calloc(1, sizeof(alsa_playback_t));
    if (!playback)
    {
        TRACE(0, "%s playback alloc error", __func__);
        goto error_exit;
    }

    playback->sample_rate = sample_rate;
    playback->bits = bits;
    playback->channels = channels;

    real_ch_num = ALSA_PLAYBACK_CHANNEL_NUM;

    // 采样点字节数*每ms采样点数*通道数*ms数*2*2 (alsa_playback_dma_buffer_size * 2)
    playback_instance_buffer_size = 2 * (sample_rate / 1000) * ALSA_PLAYBACK_CHANNEL_NUM * ALSA_PLAYBACK_DMA_SLOT_MS * 2 * 2;
    instance = alsa_instance_open(ALSA_INS_PLAYBACK, playback_instance_buffer_size);
    if (!instance)
    {
        TRACE(0, "%s instance open error", __func__);
        goto error_exit;
    }
    playback->instance = instance;
    id = alsa_instance_get_id(instance);
    playback->id = id;
    alsa_instance_set_state(instance, ALSA_STATE_OPEN);

    lock = alsa_os_mutex_create();
    if (!lock)
    {
        TRACE(0, "%s lock create error", __func__);
        goto error_exit;
    }
    playback->lock = lock;

    alsa_os_mutex_wait(playback->lock, osWaitForever);

    if (0 != alsa_deamon_playback_open(playback->id, (enum AUD_SAMPRATE_T)sample_rate, (enum AUD_BITS_T)bits, (enum AUD_CHANNEL_NUM_T)real_ch_num))
    {
        TRACE(0, "%s alsa_deamon_playback_open error", __func__);
        goto error_exit;
    }

    playback->state = ALSA_STATE_OPEN;

    alsa_os_mutex_release(playback->lock);

    alsa_instance_id_bind_user_arg(id, playback);

    return playback;

error_exit:

    if (playback)
    {
        audio_free(playback);
    }
    if (instance)
    {
        alsa_instance_close(instance);
    }
    if (lock)
    {
        alsa_os_mutex_delete(lock);
    }

    return NULL;
}

int alsa_playbakc_set_data_push_callback(alsa_playback_t *playback, alsa_data_pull_push_cb_t cb)
{
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(playback->lock, osWaitForever);
    playback->data_push_cb = cb;
    alsa_os_mutex_release(playback->lock);
    return 0;
}

int alsa_playback_start(alsa_playback_t *playback)
{
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(playback->lock, osWaitForever);

    if (playback->state != ALSA_STATE_OPEN && playback->state != ALSA_STATE_STOP)
    {
        TRACE(0, "%s state = %d error", __func__, playback->state);
        alsa_os_mutex_release(playback->lock);
        return -1;
    }

    playback->state = ALSA_STATE_START;

#ifdef ALSA_SUPPORT_THRESHOLD
    alsa_instance_threshold_enable(playback->instance, 1);
#endif

#ifdef ALSA_SW_GAIN_PROCESS_EN
    alsa_sw_gain_fade_in(playback->sw_gain, 0);
#endif

    alsa_os_mutex_release(playback->lock);
    return 0;
}

int alsa_playback_pause(alsa_playback_t *playback)
{
    alsa_os_mutex_wait(playback->lock, osWaitForever);
    alsa_instance_flush(playback->instance);
    alsa_os_mutex_release(playback->lock);
    return 0;
}

int alsa_playback_stop(alsa_playback_t *playback, alsa_playback_stop_type_t stop_type)
{
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(playback->lock, osWaitForever);

    if (playback->state != ALSA_STATE_START)
    {
        TRACE(0, "%s state = %d not need stop", __func__, playback->state);
        alsa_os_mutex_release(playback->lock);
        return -1;
    }

    alsa_state_t deamon_state = alsa_deamon_get_playback_state();
    alsa_state_t instance_state = alsa_instance_get_state(playback->instance);
    uint32_t readable_length = alsa_instance_get_readable_length(playback->instance);

    TRACE(0, "%s deamon_state = %d, instance_state = %d, readable_length = %d", __func__, deamon_state, instance_state, readable_length);

#ifdef ALSA_SUPPORT_THRESHOLD
    alsa_instance_threshold_enable(playback->instance, 0);
#endif

#ifdef ALSA_SW_GAIN_PROCESS_EN
    if (stop_type == ALSA_PLAYBACK_STOP_NORMAL)
    {
        alsa_sw_gain_fadeinout_finish_notify_register(playback->sw_gain,
                                                      sw_gain_fade_in_finish_notify,
                                                      NULL);
    }
#endif

    if (stop_type == ALSA_PLAYBACK_STOP_IMMEDIATE)
    {
        alsa_instance_flush(playback->instance);
    }
    else
    {
        if (readable_length != 0 && instance_state == ALSA_STATE_OPEN)
        {
            alsa_instance_set_state(playback->instance, ALSA_STATE_START);
        }

        if (readable_length != 0 && deamon_state != ALSA_STATE_START)
        {
            if (deamon_state == ALSA_STATE_OPEN || deamon_state == ALSA_STATE_STOP)
            {
#ifdef ALSA_SW_GAIN_PROCESS_EN
                alsa_sw_gain_fade_in(playback->sw_gain, 0);
#endif
                if (0 != alsa_deamon_playback_start(playback->id))
                {
                    TRACE(0, "%s FALTA error. alsa_deamon_playback_start error", __func__);
                    alsa_instance_set_state(playback->instance, ALSA_STATE_STOP);
                    alsa_os_mutex_release(playback->lock);
                    return 0;
                }
            }
            else
            {
                ASSERT(0, "%s readable_length = %d deamon_state %d error", __func__, readable_length, deamon_state);
            }
        }

        while (readable_length)
        {
#ifdef ALSA_SW_GAIN_PROCESS_EN
            alsa_sw_gain_fade_out(playback->sw_gain, readable_length / (ALSA_PLAYBACK_QUEUE_BITS == 16 ? 2 : 4));
#endif
            osDelay(10);
            readable_length = alsa_instance_get_readable_length(playback->instance);
        }
        readable_length = alsa_instance_get_readable_length(playback->instance);
        if (readable_length)
        {
            TRACE(0, "%s %d readable_length = %d error", __func__, __LINE__, readable_length);
        }
    }

    alsa_instance_set_state(playback->instance, ALSA_STATE_STOP);
    if (stop_type == ALSA_PLAYBACK_STOP_IMMEDIATE)
    {
        alsa_deamon_playback_stop(playback->id, 1);
    }
    else
    {
        alsa_deamon_playback_stop(playback->id, 0);
    }
    playback->state = ALSA_STATE_STOP;

#ifdef ALSA_SW_GAIN_PROCESS_EN
    if (stop_type == ALSA_PLAYBACK_STOP_NORMAL)
    {
        alsa_sw_gain_fadeinout_finish_notify_register(playback->sw_gain,
                                                      sw_gain_fade_in_finish_notify,
                                                      sw_gain_fade_out_finish_notify);
    }
#endif

    alsa_os_mutex_release(playback->lock);
    return 0;
}

int alsa_playback_flush(alsa_playback_t *playback)
{
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(playback->lock, osWaitForever);
    alsa_instance_flush(playback->instance);
    alsa_os_mutex_release(playback->lock);
    return 0;
}

int alsa_playback_close(alsa_playback_t *playback)
{
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(playback->lock, osWaitForever);

    if (playback->state != ALSA_STATE_STOP && playback->state != ALSA_STATE_OPEN)
    {
        TRACE(0, "%s state = %d error", __func__, playback->state);
        alsa_os_mutex_release(playback->lock);
        return -1;
    }

    alsa_deamon_playback_close(playback->id);

#ifdef ALSA_RESAMPLE_PROCESS_EN
    if (playback->resample)
    {
        alsa_resample_process_close(playback->resample);
    }
#endif

#ifdef ALSA_SW_GAIN_PROCESS_EN
    alsa_sw_gain_process_close(playback->sw_gain);
#endif

#ifdef ALSA_SW_SINGLE_TONE_DEBUG_EN
    alsa_sw_single_tone_debug_close(playback->sw_single_tone_debug);
#endif

    alsa_instance_close(playback->instance);

    alsa_os_mutex_release(playback->lock);
    if (0 != alsa_os_mutex_delete(playback->lock))
    {
        TRACE(0, "%s lock delete error", __func__);
    }

    audio_free(playback);
    return 0;
}

int alsa_playback_write(alsa_playback_t *playback, uint8_t *buf, uint32_t length)
{
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    if (!buf)
    {
        TRACE(0, "%s buf NULL error", __func__);
        return -1;
    }

    if (!length)
    {
        TRACE(0, "%s length = 0 error", __func__);
        return -1;
    }

    alsa_os_mutex_wait(playback->lock, osWaitForever);

    if (playback->state != ALSA_STATE_START)
    {
        TRACE(0, "%s state = %d error", __func__, playback->state);
        alsa_os_mutex_release(playback->lock);
        return -1;
    }

    if (playback->data_push_cb)
    {
        TRACE(0, "%s current in data push mode, not support write, need use data push callback", __func__);
        alsa_os_mutex_release(playback->lock);
        return -1;
    }

    uint32_t remain_len = length;
    uint8_t *write_ptr = buf;
    uint32_t write_len = 0;

    while (remain_len)
    {
        write_len = remain_len > ALSA_WRITE_DATA_MAX_SIZE ? ALSA_WRITE_DATA_MAX_SIZE : remain_len;
        alsa_write_internal(playback, write_ptr, write_len);
        write_ptr += write_len;
        remain_len -= write_len;
    }

    alsa_os_mutex_release(playback->lock);
    return length;
}

uint32_t alsa_playback_data_from_user(uint8_t *data, uint32_t length)
{
    uint32_t read_length;
    alsa_instance_t *alsa_instance = NULL;
    POSSIBLY_UNUSED alsa_playback_t *playback = NULL;

    memset(data, 0, length);

    playback = alsa_instance_get_user_arg_by_id(0);
    xy_assert(playback != NULL);
    if (playback->data_push_cb)
    {
        read_length = playback->data_push_cb(data, length);
        return read_length;
    }

    alsa_instance = alsa_get_instance_by_id(0);
    xy_assert(alsa_instance != NULL);
    read_length = alsa_instance_read(alsa_instance, data, length);

    return read_length;
}

int alsa_playback_register_pcm_state_callback(alsa_playback_t *playback,
                                              alsa_pcm_state_callback_t cb,
                                              void *arg)
{
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }
    alsa_os_mutex_wait(playback->lock, osWaitForever);
    playback->cb = cb;
    playback->cb_arg = arg;
    alsa_os_mutex_release(playback->lock);
    return 0;
}

int alsa_playback_set_user_type(alsa_playback_t *playback, alsa_user_type_t type)
{
    if (playback)
    {
        playback->user_type = type;
#ifdef ALSA_PLAYBACK_START_DELAY_EN
        alsa_deamon_set_playback_user_type(playback->user_type);
#endif
    }
    return 0;
}

int alsa_playback_get_user_type(alsa_playback_t *playback, alsa_user_type_t *type)
{
    if (playback)
    {
        *type = playback->user_type;
        return 0;
    }

    *type = 0;
    return -1;
}

int alsa_playback_set_maxdelaytime(alsa_playback_t *playback, int delay_ms)
{
#ifdef ALSA_PLAYBACK_START_DELAY_EN
    if (playback)
    {
        playback->maxdelaytime = delay_ms;
        alsa_deamon_set_playback_maxdelaytime(playback->maxdelaytime);
    }
#endif
    return 0;
}

int alsa_playback_sw_gain_bypass(alsa_playback_t *playback, uint8_t bypass)
{
#ifdef ALSA_SW_GAIN_PROCESS_EN
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    if (bypass != 0)
    {
        bypass = 1;
    }

    return alsa_sw_gain_process_bypass(playback->sw_gain, bypass);
#else
    TRACE(0, "%s need define ALSA_SW_GAIN_PROCESS_EN, current not support", __func__);
    return -1;
#endif
}

int alsa_playback_sw_gain_fade_inout_bypass(alsa_playback_t *playback, uint8_t bypass)
{
#ifdef ALSA_SW_GAIN_PROCESS_EN
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    if (bypass != 0)
    {
        bypass = 1;
    }

    return alsa_sw_gain_process_fade_inout_bypass(playback->sw_gain, bypass);
#else
    TRACE(0, "%s need define ALSA_SW_GAIN_PROCESS_EN, current not support", __func__);
    return -1;
#endif
}

int alsa_playback_sw_gain_fade_inout_coef(alsa_playback_t *playback, float coef)
{
#ifdef ALSA_SW_GAIN_PROCESS_EN
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    return alsa_sw_gain_process_adjust_fade_inout_coef(playback->sw_gain, coef);
#else
    TRACE(0, "%s need define ALSA_SW_GAIN_PROCESS_EN, current not support", __func__);
    return -1;
#endif
}

int alsa_playback_sw_gain_pre_compensation_enable(alsa_playback_t *playback, uint8_t enable, float compensation_db)
{
#ifdef ALSA_SW_GAIN_PROCESS_EN
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    if (enable)
        return alsa_sw_gain_pre_compensation_open(playback->sw_gain, compensation_db);
    else
        return alsa_sw_gain_pre_compensation_close(playback->sw_gain);
#else
    TRACE(0, "%s need define ALSA_SW_GAIN_PROCESS_EN, current not support", __func__);
    return -1;
#endif
}

int alsa_playback_set_trigger_level(alsa_playback_t *playback, alsa_trigger_level_t level)
{
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    if (!(level >= ALSA_TRIGGER_LEVEL_LOW && ALSA_TRIGGER_LEVEL_LOW <= ALSA_TRIGGER_LEVEL_HIGH))
    {
        TRACE(0, "tr_lvl %d not support", level);
        return -1;
    }

    TRACE(0, "tr_lvl %d", level);

    playback->trigger_level = level;

    return 0;
}

int alsa_playback_set_threshold(alsa_playback_t *playback, uint32_t threshold)
{
#ifdef ALSA_SUPPORT_THRESHOLD
    return alsa_instance_set_threshold(playback->instance, threshold);
#else
    TRACE(0, "%s need define ALSA_SUPPORT_THRESHOLD, current not support", __func__);
    return -1;
#endif
}

int alsa_playback_internal_buffer_get_total_length(alsa_playback_t *playback, uint32_t *length)
{
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    if (!length)
    {
        TRACE(0, "%s length NULL error", __func__);
        return -1;
    }

    *length = ALSA_PLAYBACK_QUEUE_SIZE;
    return 0;
}

int alsa_playback_internal_buffer_get_readable_length(alsa_playback_t *playback, uint32_t *length)
{
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    if (!length)
    {
        TRACE(0, "%s length NULL error", __func__);
        return -1;
    }

    int readable_len = alsa_instance_get_readable_length(playback->instance);
    if (readable_len < 0)
    {
        TRACE(0, "%s alsa_instance_get_readable_length error", __func__);
        return -1;
    }

    *length = (uint32_t)readable_len;
    return 0;
}

int alsa_playback_internal_buffer_get_writeable_length(alsa_playback_t *playback, uint32_t *length)
{
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    if (!length)
    {
        TRACE(0, "%s length NULL error", __func__);
        return -1;
    }

    int writeable_len = alsa_instance_get_writeable_length(playback->instance);
    if (writeable_len < 0)
    {
        TRACE(0, "%s alsa_instance_get_writeable_length error", __func__);
        return -1;
    }

    *length = (uint32_t)writeable_len;
    return 0;
}

// internal functions

static uint32_t get_trigger_size(alsa_playback_t *playback)
{
    uint32_t dma_pcm_buffer_size = alsa_deamon_get_playback_dma_buffer_size();
    uint32_t temp_trigger_size = dma_pcm_buffer_size;
    uint32_t queue_size_size = playback->instance->buffer_size;

    alsa_trigger_level_t temp_trigger_level = playback->trigger_level;

    switch (temp_trigger_level)
    {
    case ALSA_TRIGGER_LEVEL_LOW:
        temp_trigger_size = queue_size_size * 1 / 4;
        break;
    case ALSA_TRIGGER_LEVEL_NORMAL:
        temp_trigger_size = queue_size_size * 2 / 4;
        break;
    case ALSA_TRIGGER_LEVEL_MEDIUM:
        temp_trigger_size = queue_size_size * 3 / 4;
        break;
    case ALSA_TRIGGER_LEVEL_HIGH:
        temp_trigger_size = queue_size_size;
        break;
    default:
        break;
    }

    if (temp_trigger_size > queue_size_size)
    {
        TRACE(0, "%s level %d size %d error", __func__, temp_trigger_level, temp_trigger_size);
        temp_trigger_size = dma_pcm_buffer_size;
    }

    return temp_trigger_size;
}

static int alsa_write_internal(alsa_playback_t *playback, uint8_t *buf, uint32_t len)
{
    uint32_t remain_len = len;
    uint8_t *ptr = buf;
    int trigger_size = (int)get_trigger_size(playback);
#ifdef ALSA_WRITE_CONVERT_16BIT_TO_24BIT
    uint8_t *new_buf = NULL;
#endif

#ifdef ALSA_WRITE_CONVERT_STEREO_TO_MONO
    if (playback->channels == AUD_CHANNEL_NUM_2)
    {
        alsa_stereo_to_mono_16bit(ptr, &remain_len);
    }
#endif

#if ((AUDIO_CODEC_TYPE == 0) || (AUDIO_CODEC_TYPE == 1) || (AUDIO_CODEC_TYPE == 3))
    alsa_16bit_volume_ctrl((int16_t *)ptr, (int16_t *)ptr, &remain_len, alsa_volume);
#endif

#if (AUDIO_CODEC_TYPE == 0)
    // for pwm audio
    alsa_convert_16bit_to_10bit((int16_t *)ptr, (int16_t *)ptr, &remain_len);
#else
#ifdef ALSA_WRITE_CONVERT_MONO_TO_STEREO
    if (playback->channels == AUD_CHANNEL_NUM_1)
    {
        alsa_mono_to_stereo_16bit(playback->id, &ptr, &remain_len);
    }
#endif
#endif

#ifdef ALSA_WRITE_CONVERT_16BIT_TO_24BIT
///
    uint32_t convert_16to24_buf_size = remain_len * 3 / 2;

    new_buf = (uint8_t *)audio_malloc(convert_16to24_buf_size);
    if (!new_buf)
    {
        TRACE(0, "%s alloc 24bit convert alloc error, need %d", __func__, convert_16to24_buf_size);
        goto exit;
    }


    alsa_convert_16bit_to_24bit(new_buf, (int16_t *)ptr, &remain_len);
    if (!(remain_len <= convert_16to24_buf_size))
    {
        TRACE(0, "%d, new_buf too small for 24bit convert", remain_len);
        goto exit;
    }
    ptr = new_buf;
#endif

    int write_len, readable_length;
    alsa_state_t deamon_state, instance_state, user_state;
    while (remain_len)
    {
        write_len = alsa_instance_write(playback->instance, ptr, remain_len);
        if (write_len == -1)
        {
            TRACE(0, "instance wirte error");
            goto exit;
        }
        deamon_state = alsa_deamon_get_playback_state();
        instance_state = alsa_instance_get_state(playback->instance);
        user_state = alsa_deamon_get_playback_user_state(playback->id);
        readable_length = alsa_instance_get_readable_length(playback->instance);
        // TRACE(0, "write_len %d, deamon_state = %d, instance_state = %d, readable_length = %d, tr = %d", write_len, deamon_state, instance_state, readable_length, trigger_size);
        if (readable_length >= trigger_size && instance_state != ALSA_STATE_START)
        {
            if (write_len == 0)
            {
                TRACE(0, "%s WARNING: instance wirte_len == 0 readable_length %d, instance_state = %d", __func__, readable_length, instance_state);
            }
            if (instance_state == ALSA_STATE_OPEN || instance_state == ALSA_STATE_STOP)
            {
                alsa_instance_set_state(playback->instance, ALSA_STATE_START);
            }
            else
            {
                TRACE(0, "%s instance_state %d error", __func__, instance_state);
            }
        }
        if ((write_len == 0 || readable_length >= trigger_size) && user_state != ALSA_STATE_START)
        {
            if (write_len == 0)
            {
                TRACE(0, "%s WARNING: instance wirte_len == 0 readable_length %d deamon_state = %d", __func__, readable_length, deamon_state);
            }
            if (0 != alsa_deamon_playback_start(playback->id))
            {
                TRACE(0, "%s FATAL error . alsa_deamon_playback_start error", __func__);
                goto exit;
            }
        }
        if (write_len == 0)
        {
            osDelay(10);
            deamon_state = alsa_deamon_get_playback_state();
            if (deamon_state != ALSA_STATE_START)
            {
                TRACE(0, "%s WARNING: deamon_state %d discard data.", __func__, deamon_state);
                goto exit;
            }
        }
        remain_len -= write_len;
        ptr += write_len;
    }

exit:

#ifdef ALSA_WRITE_CONVERT_16BIT_TO_24BIT
    if (new_buf)
    {
        audio_free(new_buf);
        new_buf = NULL;
    }
#endif

    return len;
}

int alsa_playback_pmc_state_changed(alsa_playback_t *playback, alsa_pcm_state_t pcm_state)
{
    if (playback)
    {
        if (playback->state == ALSA_STATE_START)
        {
            if (playback->cb)
            {
                TRACE(0, "pcm_state %d, cb@%p", pcm_state, playback->cb);
                playback->cb(pcm_state, playback->cb_arg, NULL);
            }
        }
        else
        {
            TRACE(0, "%s state = %d, pcm_state = %d", __func__, playback->state, pcm_state);
        }
    }
    else
    {
        TRACE(0, "%s playback NULL, pcm_state = %d", __func__, pcm_state);
    }
    return 0;
}