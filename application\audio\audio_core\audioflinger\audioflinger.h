/***************************************************************************
 *
 * Copyright 2015-2019 XY.
 * All rights reserved. All unpublished rights reserved.
 *
 * No part of this work may be used or reproduced in any form or by any
 * means, or stored in a database or retrieval system, without prior written
 * permission of XY.
 *
 * Use of this work is governed by a license granted by XY.
 * This work contains confidential and proprietary information of
 * XY. which is protected by copyright, trade secret,
 * trademark and other intellectual property rights.
 *
 ****************************************************************************/
#ifndef AUDIO_FLINGER_H
#define AUDIO_FLINGER_H

#include "plat_types.h"
#include "hal_aud.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef uint32_t (*AF_STREAM_HANDLER_T)(uint8_t *buf, uint32_t len);

//pingpong machine
enum AF_PP_T{
    PP_PING = 0,
    PP_PANG = 1
};

struct AF_STREAM_CONFIG_T {
    enum AUD_SAMPRATE_T sample_rate;
    enum AUD_CHANNEL_NUM_T channel_num;
    enum AUD_BITS_T bits;
    uint8_t vol;

    AF_STREAM_HANDLER_T handler;

    uint8_t *data_ptr;
    uint32_t data_size;
};

//Should define return status
uint32_t af_open(void);
uint32_t af_stream_open(enum AUD_STREAM_T stream, const struct AF_STREAM_CONFIG_T *cfg);
uint32_t af_stream_mute(enum AUD_STREAM_T stream, bool mute);
uint32_t af_stream_set_chan_vol(enum AUD_STREAM_T stream, enum AUD_CHANNEL_MAP_T ch_map, uint8_t vol);
uint32_t af_stream_restore_chan_vol(enum AUD_STREAM_T stream);
uint32_t af_stream_get_cfg(enum AUD_STREAM_T stream, struct AF_STREAM_CONFIG_T **cfg);
uint32_t af_stream_start(enum AUD_STREAM_T stream);
uint32_t af_stream_stop(enum AUD_STREAM_T stream);
uint32_t af_stream_close(enum AUD_STREAM_T stream);
uint32_t af_close(void);

#ifdef __cplusplus
}
#endif

#endif /* AUDIO_FLINGER_H */
