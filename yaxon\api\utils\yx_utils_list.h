/**********************************************************************/
/**
 * @file yx_utils_list.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-11
 * @version V1.0
 * @brief 链表工具封装接口
 **********************************************************************/
#ifndef YX_UTILS_LIST_H
#define YX_UTILS_LIST_H

#include "yx_type.h"

#define LISTMEM INT8U
#define LISTNODE struct node

/**
 * @brief 链表节点结构定义
 * @ingroup yx_utils
 */
typedef struct node
{
    LISTNODE* prv;  /**< 上一个节点指针 */
    LISTNODE* next; /**< 下一个节点指针 */
} node_t;

/**
 * @brief 链表句柄结构定义
 * @ingroup yx_utils
 */
typedef struct
{
    LISTNODE* phead; /**< 链表头指针 */
    LISTNODE* ptail; /**< 链表尾指针 */
    INT32U item;     /**< 链表节点个数 */
} list_t;

/**
 * @brief 检测链表的有效性
 * @ingroup yx_utils
 * @param plist 链表指针
 * @param bptr 内存起始地址
 * @param eptr 内存结束地址
 * @param needchecklp 是否需要检查链表指针的有效性
 * @return TRUE: 链表有效; FALSE: 链表无效
 */
BOOLEAN yx_utils_list_check(list_t* plist, VOID* bptr, VOID* eptr, BOOLEAN needchecklp);

/**
 * @brief 初始化链表
 * @ingroup yx_utils
 * @param plist 链表指针
 * @return TRUE: 成功; FALSE: 失败
 */
BOOLEAN yx_utils_list_init(list_t* plist);

/**
 * @brief 获取链表节点个数
 * @ingroup yx_utils
 * @param plist 链表指针
 * @return 链表节点个数
 */
INT32U yx_utils_list_count(list_t* plist);

/**
 * @brief 获取链表头节点
 * @ingroup yx_utils
 * @param plist 链表指针
 * @return 链表头节点; 如链表无节点, 则返回0
 */
LISTMEM* yx_utils_list_get_head(list_t* plist);

/**
 * @brief 获取链表尾节点
 * @ingroup yx_utils
 * @param plist 链表指针
 * @return 链表尾节点; 如链表无节点, 则返回0
 */
LISTMEM* yx_utils_list_get_tail(list_t* plist);

/**
 * @brief 获取指定节点的后一节点
 * @ingroup yx_utils
 * @param bp 指定节点指针
 * @return 指定节点的后一节点; 如返回0, 则表示节点不存在
 */
LISTMEM* yx_utils_list_next(LISTMEM* bp);

/**
 * @brief 获取指定节点的前一节点
 * @ingroup yx_utils
 * @param bp 指定节点指针
 * @return 指定节点的前一节点; 如返回0, 则表示不存在前一节点
 */
LISTMEM* yx_utils_list_prev(LISTMEM* bp);

/**
 * @brief 删除指定节点
 * @ingroup yx_utils
 * @param plist 链表指针
 * @param bp 指定节点指针
 * @return 指定节点的下一节点; 如返回0, 则表示Bp不存在下一节点
 */
LISTMEM* yx_utils_list_delete_element(list_t* plist, LISTMEM* bp);

/**
 * @brief 删除链表头节点
 * @ingroup yx_utils
 * @param plist 链表指针
 * @return 链表头节点; 如返回0, 则表示不存在链表头节点
 */
LISTMEM* yx_utils_list_delete_head(list_t* plist);

/**
 * @brief 删除链表尾节点
 * @ingroup yx_utils
 * @param plist 链表指针
 * @return 链表尾节点; 如返回0, 则表示不存在链表尾节点
 */
LISTMEM* yx_utils_list_delete_tail(list_t* plist);

/**
 * @brief 在链表尾上追加一个节点
 * @ingroup yx_utils
 * @param plist 链表指针
 * @param bp 待追加节点指针
 * @return TRUE: 成功; FALSE: 失败
 */
BOOLEAN yx_utils_list_append(list_t* plist, LISTMEM* bp);

/**
 * @brief 在链表头插入一个节点
 * @ingroup yx_utils
 * @param plist 链表指针
 * @param bp 待插入节点指针
 * @return TRUE: 成功; FALSE: 失败
 */
BOOLEAN yx_utils_list_prepend(list_t* plist, LISTMEM* bp);

/**
 * @brief 将链表首尾相连,形成环形
 * @ingroup yx_utils
 * @param plist 链表指针
 * @return TRUE: 成功; FALSE: 失败
 */
BOOLEAN yx_utils_list_make_circular(list_t* plist);

/**
 * @brief 在指定节点前插入一个新节点
 * @ingroup yx_utils
 * @param plist 链表指针
 * @param curbp 指定节点指针
 * @param insbp 待插入节点指针
 * @return TRUE: 成功; FALSE: 失败
 */
BOOLEAN yx_utils_list_insert_before(list_t* plist, LISTMEM* curbp, LISTMEM* insbp);

/**
 * @brief 在指定节点后插入一个新节点
 * @ingroup yx_utils
 * @param plist 链表指针
 * @param curbp 指定节点指针
 * @param insbp 待插入节点指针
 * @return TRUE: 成功; FALSE: 失败
 */
BOOLEAN yx_utils_list_insert_after(list_t* plist, LISTMEM* curbp, LISTMEM* insbp);

/**
 * @brief 将一块内存初始化成链表缓冲区
 * @ingroup yx_utils
 * @param memLp 链表指针
 * @param addr 内存起始地址
 * @param nblks 内存块个数
 * @param blksize 内存块大小
 * @return TRUE: 成功; FALSE: 失败
 */
BOOLEAN yx_utils_list_init_mem_pool(list_t* memLp, LISTMEM* addr, INT32U nblks, INT32U blksize);

#endif
