/**
* @file        at_led_api.c
* @ingroup     peripheral
* @brief       LED指示灯，指示模组当前网络状态以及运行状态
* @attention   1，由于硬件上有些限制，如果要使用，请咨询我们的硬件FAE;
*              2，目前默认未使用该源文件;
* @par	网络状态指示灯闪烁规则：1、未注网时：灯熄灭；
                              2、注网中：灯快闪，高电平50ms，低电平1000ms；
                              3、注网成功：灯慢闪，高电平50ms，低电平2000ms。
* @par	运行状态指示灯闪烁规则：1、正常运行时：常亮；
                              2、休眠时：熄灭。
***********************************************************************************/
#include "xy4100_ll_adc.h"
#include "hal_gpio.h" 
#include "hal_agpio.h" 
#include "xy4100_ll_gpio.h"
#include "xy4100_ll_uart.h"
#include "xy_atc_interface.h"
#include "xy_system.h"
#include "xy_ps_api.h"
#include "posix_io.h"
#include "posix_device.h"
#include "xy_at_api.h"
#include "hal_def.h"
#include "oss_nv.h"
#include "factory_nv.h"
#include "xy4100_ll_adc.h"
#include "at_hardware_api.h"
#include "hal_adc.h"
#include "at_passthrough.h"
#include "xy_prcm.h"
#include "softap_nv.h"
#include "app_utils.h"
#include "xy_lpm.h"
#include "csi_device.h"
#include "usb_reg.h"
#include "at_ctl.h"
#include "xy4100_ll_clock_driver.h"
#include "xy_fs.h"

osThreadId_t g_set_ifc_thd = NULL;
osThreadId_t g_set_ipr_thd = NULL;

int set_atc_mode(uint8_t mode)
{
    g_at_config.at_c = mode;
    return XY_OK;    
}

/*返回值若为0，通常为未使用LPUART作为AT通道，或者当前正在执行波特率自适应。*/
static int uart_baudrate[] = {4800,9600,14400,19200,28800,33600,38400,57600,115200,230400,460800,921600,1000000,2000000};
uint32_t atUartBaudRateGet(void)
{
    uint8_t i;
    uint8_t j = 0;
    int delta_baudrate_tmp = 0;
    int delta_baudrate = 1000000;

#if (!LPUART_AT && !UART_AT)
	return 0;
#endif
    
	/*波特率自适应*/
	if(g_softap_fac_nv->at_uart_rate == 0)
	{
		return (g_softap_var_nv->at_ipr & 0x7FFF) * 2400;
	}
	else
	{
#if UART_AT
        int baudrate = at_posix_ioctl(AT_UART_FD, UART_AT_IOC_GET_BAUDRATE, NULL);
#else /* UART_AT */
	    int baudrate = at_posix_ioctl(AT_LPUART_FD, LPUART_AT_IOC_GET_BAUDRATE, NULL);
#endif /* UART_AT */
		/*未使用LPUART作为AT通道*/
	    if (baudrate == -1)
			return 0;

	    for(i = 0; i < sizeof(uart_baudrate) / sizeof(uart_baudrate[0]); i++)
	    {
	        if (baudrate >= uart_baudrate[i])
	        {
	            delta_baudrate_tmp = baudrate - uart_baudrate[i];
	        }
	        else
	        {
	            delta_baudrate_tmp = uart_baudrate[i] - baudrate;
	        }

	        if(delta_baudrate_tmp < delta_baudrate)
	        {
	            delta_baudrate = delta_baudrate_tmp;
	            j = i;
	        }
	    }
	    // 波特率偏差大于10%断言失败
	    xy_assert((delta_baudrate * 10) < uart_baudrate[j]);
		xy_assert(baudrate > 4000);

	    return (uint32_t)uart_baudrate[j];
	}
}

uint32_t at_uart_baudrate_init(void)
{
	uint32_t bautrate_times;
    uint32_t bautrate;

	if (Get_Boot_Reason() == SOFT_RESET && Get_Boot_Sub_Reason() == SOFT_RB_BY_FOTA)
	{
		extern uint32_t OTA_get_at_uart_baudrate();
		return OTA_get_at_uart_baudrate();
	}

	if(g_softap_var_nv->at_ipr != 0)
		bautrate_times = (uint32_t)(g_softap_var_nv->at_ipr & 0x7FFF);
	else
    {
        bautrate_times = (uint32_t)g_softap_fac_nv->at_uart_rate;
        if((bautrate_times == 0) && (Get_Boot_Reason() != POWER_ON))
        {
            uint16_t temp_at_ipr;
            if(app_read_fs(BSP_VAR_NV_FS,OFFSET_VARNV_PARAM(at_ipr),(void*)&temp_at_ipr,VARNV_PARAM_LEN(at_ipr))== XY_OK)
            {
                bautrate_times = (uint32_t)(temp_at_ipr & 0x7FFF);
                g_softap_var_nv->at_ipr = temp_at_ipr;
            }
        }
    }

	if (bautrate_times != 0)
	{
        if(g_softap_var_nv->at_ipr == 0)
            g_softap_var_nv->at_ipr = bautrate_times;
		bautrate = bautrate_times * 2400; //波特率为1000000时，恢复出来的值为998400，因为1000000/2400=416.67
		if (bautrate > 921600)
		{
			bautrate = ((bautrate + 5000U) / 10000U) * 10000U;
		}

		return bautrate;
	}
	/*开启波特率自适应*/
	else
	{
        extern void StartAutoBaudAdapt(void);
		StartAutoBaudAdapt();
		return 0;
	}
}


bool at_uart_flowctl_IsEnable(void)
{
	return ((g_at_config.dcebydte && g_at_config.dtebydce) ? 1 : 0);
}

int set_at_uart_ipr(int ttyFd, uint32_t baud_rate, bool isSave)
{
    if (!IS_AT_TTY_PHY(ttyFd))
    {
		xy_printf(0, PLATFORM_AP, INFO_LOG, "set_at_uart_ipr: don't allow other tty:%d!\n", ttyFd);
        return XY_Err_NotAllowed;
    }

    if (isSave)
    {
    	/*中移立即保存出厂NV，无需等AT&W输入*/
        g_softap_fac_nv->at_uart_rate = baud_rate / 2400;
        SAVE_FAC_PARAM(at_uart_rate);
    	xy_fremove(BSP_VAR_NV_FS);
        if (baud_rate == 0)
        {
            g_softap_var_nv->at_ipr = 0x8000;
            return XY_ERR;
        }
    }

    appAtResp(ttyFd, AT_RSP_OK);
#if UART_AT
    at_posix_ioctl(ttyFd, UART_AT_IOC_SET_BAUDRATE, (void *)baud_rate);
#else /* UART_AT */
    at_posix_ioctl(ttyFd, LPUART_AT_IOC_SET_BAUDRATE, (void *)baud_rate);
#endif /* UART_AT */

    if (baud_rate)
        g_softap_var_nv->at_ipr = baud_rate / 2400;
    else
        g_softap_var_nv->at_ipr = 0x8000;

    return XY_OK;
}

int at_uart_ifc_init()
{
    int enable = at_uart_flowctl_IsEnable();

#if UART_AT
    at_posix_ioctl(AT_UART_FD, UART_AT_IOC_SET_FLOWCTL, (void *)enable);
#else /* UART_AT */
	at_posix_ioctl(AT_LPUART_FD, LPUART_AT_IOC_SET_FLOWCTL, (void *)enable);
#endif /* UART_AT */

	return XY_OK;
}

int set_at_uart_ifc(int ttyFd, bool enable)
{
    if (!IS_AT_TTY_PHY(ttyFd))
    {
		xy_printf(0, PLATFORM_AP, INFO_LOG, "set_at_uart_ifc:don't allow other tty:%d!\n", ttyFd);
        return XY_Err_NotAllowed;
    }  

	uint8_t lpuart_rts_pin = (uint8_t)(g_softap_fac_nv->lpuart_rts_pin & 0x7F);
	uint8_t lpuart_cts_pin = (uint8_t)(g_softap_fac_nv->lpuart_cts_pin & 0x7F);
	if (lpuart_rts_pin > GPIO_NUM_MAX || lpuart_cts_pin > GPIO_NUM_MAX)
	{
		xy_printf(0, PLATFORM_AP, WARN_LOG, "set_at_uart_ifc:flow ctl pin not configured:%d %d!\n", lpuart_rts_pin, lpuart_cts_pin);
		return XY_Err_NotAllowed;
	}

    appAtResp(ttyFd, AT_RSP_OK);
#if UART_AT
    at_posix_ioctl(ttyFd, UART_AT_IOC_SET_FLOWCTL, (void *)enable);
#else /* UART_AT */
	at_posix_ioctl(ttyFd, LPUART_AT_IOC_SET_FLOWCTL, (void *)enable);
#endif /* UART_AT */
    g_at_config.dcebydte = enable;
    g_at_config.dtebydce = enable;
    xy_printf(0, PLATFORM_AP, INFO_LOG, "set_at_uart_ifc succ:%d,%d!\n", enable, ttyFd);

	return XY_OK;
}

/**
 * @brief DTR PIN对应GPIO中断服务函数
 */
extern void atPassthDTRIrqHandler(void);
static void DTR_IrqHandler(void)
{
	atPassthDTRIrqHandler();
}

/*需要与at_uart_dtr_pin_recover接口的功能保持一致*/
int at_uart_dtr_pin_init(uint8_t mode)
{
	uint8_t pin = g_softap_fac_nv->lpuart_dtr_pin & 0x3F;
	if (pin > GPIO_NUM_MAX)
	{
		xy_printf(0, PLATFORM_AP, WARN_LOG, "at_uart_dtr_pin_init:pin not configured:%d!\n", pin);
		return XY_Err_NotAllowed;
	}

	HAL_GPIO_InitTypeDef gpio_init = {0};
	gpio_init.Pin = pin;
	gpio_init.Mode = GPIO_MODE_INPUT;
	if (mode)
	{
		gpio_init.Pull = GPIO_PULL_UP;
		gpio_init.Int.Mode = GPIO_INT_RISE_EDGE; // 上升沿中断
		gpio_init.Int.Irq = DTR_IrqHandler;      // 注册中断服务函数
	}
	else
		gpio_init.Pull = GPIO_FLOAT;
	HAL_GPIO_Init(&gpio_init);

	return XY_OK;
}

int set_at_uart_dtr(int ttyFd, uint8_t mode)
{
    if (!IS_AT_TTY_PHY(ttyFd))
    {
		xy_printf(0, PLATFORM_AP, INFO_LOG, "set_at_uart_dtr:don't allow other tty:%d!\n", ttyFd);
        return XY_Err_NotAllowed;
    }  

	if (mode == g_at_config.at_d)
	{
		xy_printf(0, PLATFORM_AP, INFO_LOG, "set_at_uart_dtr:dtr already set:%d!\n", mode);
		return XY_OK;
	}

	if (at_uart_dtr_pin_init(mode) != XY_OK)
		return XY_Err_NotAllowed;

	g_at_config.at_d = mode;

	return XY_OK;
}


void at_config_hardware_init(void)
{
	at_uart_ifc_init();
	at_uart_dtr_pin_init(g_at_config.at_d);
}

/************************************************************************************
 * @brief 引脚连通性测试
 * <AUTHOR>
 * @date 2023-09-26
*************************************************************************************/
/**
 * @brief 使用HAL接口配置引脚的输入输出模式
 * @param num：引脚号，详情参考 @ref TEST_GPIO_PinTypeDef
 * @param dir：0：输入，1：输出
 * @return 0：成功，-1：非法参数
 */
static char Config_Pin(char num, uint8_t dir)
{
    if( num < WKP3 || num > GPIO63 )
    {
        return -1; //入参非法
    }

    //GPIO引脚
    if(num >= GPIO0)
    {
        HAL_GPIO_InitTypeDef gpio_init = {0};
        gpio_init.Pin = (HAL_GPIO_PinTypeDef)num;
        switch (dir)
        {
            //浮空输入
            case 0: {
                gpio_init.Mode = GPIO_MODE_INPUT;
                gpio_init.Pull = GPIO_FLOAT;
                break;
            }
            //推挽输出
            case 1: {
                gpio_init.Mode = GPIO_MODE_OUTPUT_PP;
                break;
            }
            default: return -1; //入参非法
        }
        HAL_GPIO_Init(&gpio_init);
    }
    //AGPIO引脚
    else
    {
        HAL_AGPIO_InitTypeDef agpio_init = {0};
        switch(num)
        {
            case WKP1: { agpio_init.Pin = AGPIO_PIN0; break; }
            case WKP2: { agpio_init.Pin = AGPIO_PIN1; break; }
            case WKP3: { agpio_init.Pin = AGPIO_PIN2; break; }
            default: return -1; //入参非法
        }
        switch (dir)
        {
            //浮空输入
            case 0: {
                agpio_init.Mode = AGPIO_MODE_INPUT;
                agpio_init.Pull = AGPIO_FLOAT;
                break;
            }
            //推挽输出
            case 1: {
                agpio_init.Mode = AGPIO_MODE_OUTPUT_PP;
                break;
            }
            default: return -1; //入参非法
        }
        HAL_AGPIO_Init(&agpio_init);
    }

    return 0;
}

/**仅限连通性测试内部使用，其他模块严禁调用*/
static char Write_Pin(char num, uint8_t level)
{
    if( num < WKP3 || num > GPIO63 )
    {
        return -1; //入参非法
    }

    if(level != 0 && level != 1)
    {
        return -1; //入参非法
    }

    //GPIO引脚
    if(num >= GPIO0)
    {
        HAL_GPIO_Write_Pin((HAL_GPIO_PinTypeDef)num, (FlagStatus)level);
    }
    //AGPIO引脚
    else
    {
        HAL_AGPIO_PinTypeDef pin_num = {0};
        switch(num)
        {
            case WKP1: { pin_num = AGPIO_PIN0; break; }
            case WKP2: { pin_num = AGPIO_PIN1; break; }
            case WKP3: { pin_num = AGPIO_PIN2; break; }
            default: return -1; //入参非法
        }
        HAL_AGPIO_Write_Pin(pin_num, (FlagStatus)level);
    }

    return 0;
}

/**仅限连通性测试内部使用，其他模块严禁调用*/
static char Read_Pin(char num)
{
    if( num < WKP3 || num > GPIO63 )
    {
        return -1; //入参非法
    }

    //GPIO引脚
    if(num >= GPIO0)
    {
        return HAL_GPIO_Read_Pin((HAL_GPIO_PinTypeDef)num);
    }
    //AGPIO引脚
    else
    {
        HAL_AGPIO_PinTypeDef pin_num = {0};
        switch(num)
        {
            case WKP1: { pin_num = AGPIO_PIN0; break; }
            case WKP2: { pin_num = AGPIO_PIN1; break; }
            case WKP3: { pin_num = AGPIO_PIN2; break; }
            default: return -1; //入参非法
        }
        return HAL_AGPIO_Read_Pin(pin_num);
    }

    return -1;
}

/**
  * @brief  一个引脚输出电平，另一个引脚读取电平，若读取电平值符合预期则返回正确。
  * @param  gpio_in ：输入引脚号，详情参考 @ref TEST_GPIO_PinTypeDef
  * @param  gpio_out：输出引脚号，详情参考 @ref TEST_GPIO_PinTypeDef
  * @param  is_High： 输出引脚的输出电平，0：低电平，1：高电平
  * @return 0：读取电平值不符合预期，1：读取电平值符合预期
  */
uint8_t Check_Pin_Connectivity(char gpio_in, char gpio_out, char is_High)
{
    char ret0 = 0, ret1 = 0;

    //配置输入、输出引脚
    ret0 = Config_Pin(gpio_in, 0);
    ret1 = Config_Pin(gpio_out, 1);
    if(ret0 == -1 || ret1 == -1)
    {
        while(1);
    }

    //输出指定电平
    ret0 = Write_Pin(gpio_out, is_High);
    if(ret0 == -1)
    {
        while(1);
    }

    //延迟1ms，待电平稳定后读取电平
    HAL_Delay_US(1000);
    ret1 = Read_Pin(gpio_in);
    if(ret1 == -1)
    {
        while(1);
    }

    //符合预期返回1
    if(is_High == ret1)
    {
        return 1;
    }

    return 0;
}
