/**********************************************************************/
/**
 * @file yx_ia_power.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-02-25
 * @version V1.0
 * @brief 电源控制接口适配
 **********************************************************************/
#ifndef YX_IA_POWER_H
#define YX_IA_POWER_H

#include "yx_type.h"
#define IA_POWER_ERRNO            ((INT32U)(0x8000 + 0x00) << 16)

/**
 * @brief 错误码枚举
 * @ingroup ia_power
 */
typedef enum {
    IA_POWER_SUUCESS = RTN_OK,                               /**< 成功 */

    IA_POWER_NOT_SUPPORT         = IA_POWER_ERRNO | 0x80,    /**< 功能不支持 */
    IA_POWER_WAKELOCK_FAILED     = IA_POWER_ERRNO | 0x81,    /**< 唤醒锁创建失败 */
    IA_POWER_WAKELOCK_LOCK_ERR   = IA_POWER_ERRNO | 0x82,    /**< 唤醒锁锁定失败 */
    IA_POWER_WAKELOCK_UNLOCK_ERR = IA_POWER_ERRNO | 0x83,    /**< 唤醒锁解锁失败 */
} ia_power_errno_e;

/**
 * @brief 上电类型枚举定义
 * @ingroup ia_power
 */
typedef enum {
    POWER_UP_REASON_RESET_KEY = 1,                             /**< 通过复位按键上电（硬复位）*/
    POWER_UP_REASON_POWER_KEY,                                 /**< 通过电源按键上电（正常启动）*/
    POWER_UP_REASON_SOFTWARE_RESET,                            /**< 通过软件触发复位（系统调用重启,任何调用接口重启的都是软件复位,不区分应用主动复位和kernel复位）*/
    POWER_UP_REASON_RTC_ALARM,                                 /**< 由RTC闹钟触发上电（定时唤醒）*/
    POWER_UP_REASON_ERR,                                       /**< 获取上电原因失败或未知错误 */
} yx_ia_power_up_reason_e;

/**
 * @brief 下电类型枚举定义
 * @ingroup ia_power
 */
typedef enum {
    POWER_DOWN_REASON_LONGPRESS_POWER_KEY = 1,                 /**< 长按电源键导致软件关机 */
    POWER_DOWN_REASON_VRTC_MIN_TH,                             /**< VRTC电压过低（低于最小阈值）*/
    POWER_DOWN_REASON_OVER_TEMPERATURE,                        /**< 过温保护触发下电 */
    POWER_DOWN_REASON_VIN_LDO_DOWN,                            /**< VIN或LDO电压掉电（低压检测）*/
    POWER_DOWN_REASON_PMIC_WATCH_DOG_EXPIRY_EVENT,             /**< PMIC看门狗超时导致复位 */
    POWER_DOWN_REASON_LONGPRESS_RESET_KEY,                     /**< 长按复位按键导致关机或复位 */
    POWER_DOWN_REASON_VIN_LDO_UP,                              /**< VIN或LDO电压过高（高压检测）*/
    POWER_DOWN_REASON_ERR,                                     /**< 获取下电原因失败或未知错误 */
} yx_ia_power_down_reason_e;

/**
 * @brief 电源状态结构体定义
 * @ingroup ia_power
 */
typedef struct {
    yx_ia_power_up_reason_e up;
    yx_ia_power_down_reason_e down;
} yx_ia_power_reason_t;

/**
 * @brief 关机
 *
 * @retval VOID
 * @ingroup ia_power
 */
VOID yx_ia_power_off(VOID);

/**
 * @brief 重置
 *
 * @retval VOID
 * @ingroup ia_power
 */
VOID yx_ia_power_reset(VOID);

/**
 * @brief 系统休眠
 * @ingroup ia_power
 * @retval IA_POWER_SUUCESS(0) 成功 其它 @see ia_power_errno_e
 */
INT32S yx_ia_power_sleep(VOID);

/**
 * @brief 系统唤醒
 * @ingroup ia_power
 * @retval IA_POWER_SUUCESS(0) 成功 其它 @see ia_power_errno_e
 */
INT32S yx_ia_power_wakeup(VOID);

/**
 * @brief 初始化唤醒锁 创建唤醒锁
 * @ingroup ia_power
 * @retval IA_POWER_SUUCESS(0) 成功 其它 @see ia_power_errno_e
 */
INT32S yx_ia_power_wakelock_init(VOID);

/**
 * @brief 获取当前系统休眠状态
 * @ingroup ia_power
 * @retval TRUE  系统处于休眠状态
 * @retval FALSE 系统处于工作状态
 */
BOOLEAN yx_ia_power_get_sleep_state(VOID);

/**
 * @brief 获取系统上电/掉电原因
 * @ingroup ia_power
 * @param[out] reason 上下电原因结构体指针
 * @retval IA_POWER_SUUCESS(0) 成功 其它 @see ia_power_errno_e
 * @note 获取这一次上电原因和上一次掉电原因
 */
INT32S yx_ia_power_get_on_off_reason(yx_ia_power_reason_t *reason);

/**
 * @brief 看门狗喂狗初始化
 * @ingroup ia_power
 * @retval IA_POWER_SUUCESS(0) 成功 其它 @see ia_power_errno_e
 * @note 该函数主要负责看门狗喂狗引脚初始化等功能,只需在喂狗前调用一次
 */
INT32S yx_ia_power_init_feed_watchdog(VOID);

/**
 * @brief 看门狗喂狗
 * @ingroup ia_power
 * @retval VOID
 * @note 该函数通过翻转IO电平实现喂狗，需要多次调用以控制喂狗周期
 */
VOID yx_ia_power_feed_watchdog(VOID);

/**
 * @brief 设置Bootloader喂狗的周期
 * @ingroup ia_power
 * @param[in] period 喂狗周期(ms), 设为0表示关闭Bootloader喂狗, 最大值为22000
 * @retval IA_POWER_SUUCESS(0) 成功 其它 @see ia_power_errno_e
 * @note 该函数设置Bootloader阶段喂狗周期,只需在fota升级等操作前调用一次
 */
INT32S yx_ia_power_set_bld_feed_watchdog(INT32U period);
#endif