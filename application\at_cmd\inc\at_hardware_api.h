#pragma once


#include <stdio.h>
#include <stdbool.h>
#include "at_net_led.h"
#include "xy_utils.h"
void at_config_hardware_init();


/**
 * @brief 从LPUART寄存器获取当前实时波特率
 */
uint32_t atUartBaudRateGet(void);

/**
 * @brief 打开/关闭UART口硬件流控 
 * @param ttyFd  [IN] AT通道句柄，目前仅支持AT_LPUART_FD/AT_CMUX1_FD/AT_CMUX2_FD/AT_UART_FD
 * @param enable [IN] 0：关闭, 1：打开
 * @note  系统会在LPUART总线上的数据传输完成后再执行流控状态切换
 */
int set_at_uart_ifc(int ttyFd, bool enable);

/**
 * @brief 更改LPUART口波特率
 * @param ttyFd     [IN] AT通道句柄，目前仅支持AT_LPUART_FD/AT_CMUX1_FD/AT_CMUX2_FD/AT_UART_FD
 * @param baud_rate [IN] 待切换的波特率
 * @param isSave    [IN] 是否保存，保存后重启仍然生效
 * @note  系统会在LPUART总线上的数据传输完成后再执行波特率的更改
 * @warning LIGHTSLEEP睡眠唤醒，由于耗时需要0.7ms左右，进而波特率至多只能支持230400；超过230400波特率时，需要人为关闭LIGHTSLEEP，建议通过锁来关闭。
 */
int set_at_uart_ipr(int ttyFd, uint32_t baud_rate, bool isSave);

/**
 * @brief 设置主串口的DTR信号模式
 * @param ttyFd [IN] AT通道句柄，目前仅支持AT_LPUART_FD/AT_CMUX1_FD/AT_CMUX2_FD/AT_UART_FD
 * @param mode  [IN] DTR信号模式
 */
int set_at_uart_dtr(int ttyFd, uint8_t mode);

// 获取主串口的硬件流控当前是否打开
bool at_uart_flowctl_IsEnable(void);

// 设置DCD信号模式
int set_atc_mode(uint8_t mode);



/************************************************************************************
 * @brief 引脚连通性测试
 * <AUTHOR>
 * @date 2023-09-26
*************************************************************************************/
typedef enum{
    WKP3 = -3,  //AGPI2
    WKP2 = -2,  //AGPI1
    WKP1 = -1,  //AGPI0
    GPIO0 = 0,
	GPIO1,  GPIO2,  GPIO3,  GPIO4,  GPIO5,  GPIO6,  GPIO7,  GPIO8,  GPIO9,  GPIO10,
	GPIO11, GPIO12, GPIO13, GPIO14, GPIO15, GPIO16, GPIO17, GPIO18, GPIO19, GPIO20,
	GPIO21, GPIO22, GPIO23, GPIO24, GPIO25, GPIO26, GPIO27, GPIO28, GPIO29, GPIO30,
	GPIO31, GPIO32, GPIO33, GPIO34, GPIO35, GPIO36, GPIO37, GPIO38, GPIO39, GPIO40,
	GPIO41, GPIO42, GPIO43, GPIO44, GPIO45, GPIO46, GPIO47, GPIO48, GPIO49, GPIO50,
	GPIO51, GPIO52, GPIO53, GPIO54, GPIO55, GPIO56, GPIO57, GPIO58, GPIO59, GPIO60,
	GPIO61, GPIO62, GPIO63
} TEST_GPIO_PinTypeDef;


/**
  * @brief  一个引脚输出电平，另一个引脚读取电平，若读取电平值符合预期则返回正确。
  * @param  gpio_in ：输入引脚号，详情参考 @ref TEST_GPIO_PinTypeDef
  * @param  gpio_out：输出引脚号，详情参考 @ref TEST_GPIO_PinTypeDef
  * @param  is_High： 输出引脚的输出电平，0：低电平，1：高电平
  * @return 0：读取电平值不符合预期，1：读取电平值符合预期
  */
uint8_t Check_Pin_Connectivity(char gpio_in, char gpio_out, char is_High);
