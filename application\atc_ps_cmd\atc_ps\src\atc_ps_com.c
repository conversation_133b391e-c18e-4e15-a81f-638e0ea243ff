#include "atc_ps.h"
#include <stdarg.h>
#include "xy_wan_api.h"

/* add for TimeZone Conversion */
const unsigned char ATC_MonthDayTbl[2][12] = 
{
    {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31},                                   /* common year */
    {31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31}                                    /* leap year */
};

const ST_ATC_GLOBLE_ERROR_CAUSE_TABLE PCmeErrorTextTbl[] = 
{
    /* usErrCode  */       /* pCmeErrorText  */
    // {   1,                (unsigned char *)"no connection to phone"      },
    // {   2,                (unsigned char *)"phone-adaptor link reserved" },
    {   3,                (unsigned char *)"operation not allowed"       },
    {   4,                (unsigned char *)"operation not supported"     },
    // {   5,                (unsigned char *)"PH-SIM PIN required"         },
    // {   6,                (unsigned char *)"PH-FSIM PIN required"        },
    // {   7,                (unsigned char *)"PH-FSIM PUK required"        },
    {   10,               (unsigned char *)"SIM not inserted"            },
    {   11,               (unsigned char *)"SIM PIN required"            },
    {   12,               (unsigned char *)"SIM PUK required"            },
    {   13,               (unsigned char *)"SIM failure"                 },
    {   14,               (unsigned char *)"SIM busy"                    },
    {   15,               (unsigned char *)"SIM wrong"                   },
    {   16,               (unsigned char *)"incorrect password"          },
    // {   17,               (unsigned char *)"SIM PIN2 required"           },
    // {   18,               (unsigned char *)"SIM PUK2 required"           },
    {   20,               (unsigned char *)"memory full"                 },
    // {   21,               (unsigned char *)"invalid index"               },
    {   22,               (unsigned char *)"not found"                   },
    // {   23,               (unsigned char *)"memory failure"              },
    {   24,               (unsigned char *)"text string too long"        },
    // {   25,               (unsigned char *)"invalid characters in text string"                 },
    // {   26,               (unsigned char *)"dial string too long"                              },
    // {   27,               (unsigned char *)"invalid characters in dial string"                 },
    {   30,               (unsigned char *)"no network service"                                },
    // {   31,               (unsigned char *)"network timeout"                                   },
    // {   32,               (unsigned char *)"network not allowed - emergency calls only"        },
    // {   40,               (unsigned char *)"network personalisation PIN required"              },
    // {   41,               (unsigned char *)"network personalisation PUK required"              },
    // {   42,               (unsigned char *)"network subset personalisation PIN required"       },
    // {   43,               (unsigned char *)"network subset personalisation PUK required"       },
    // {   44,               (unsigned char *)"service provider personalisation PIN required"     },
    // {   45,               (unsigned char *)"service provider personalisation PUK required"     },
    // {   46,               (unsigned char *)"corporate personalisation PIN required"            },
    // {   47,               (unsigned char *)"corporate personalisation PUK required"            },
    // {   48,               (unsigned char *)"hidden key required"                               },
    // {   49,               (unsigned char *)"EAP method not supported"                          },
    {   50,               (unsigned char *)"Incorrect parameters"                              },
    // {   51,               (unsigned char *)"command implemented but currently disabled"        },
    // {   52,               (unsigned char *)"command aborted by user"                           },
    // {   53,               (unsigned char *)"not attached to network due to MT functionality restrictions"             },
    // {   54,               (unsigned char *)"modem not allowed - MT restricted to emergency calls only"                },
    // {   55,               (unsigned char *)"operation not allowed because of MT functionality restrictions"           },
    // {   56,               (unsigned char *)"fixed dial number only allowed - called number is not a fixed dial number"},
    // {   57,               (unsigned char *)"temporarily out of service due to other MT usage"                         },
    // {   58,               (unsigned char *)"language/alphabet not supported"                   },
    // {   59,               (unsigned char *)"unexpected data value"                             },
    // {   60,               (unsigned char *)"system failure"                                    },
    // {   61,               (unsigned char *)"data missing"                                      },
    // {   62,               (unsigned char *)"call barred"                                       },
    // {   63,               (unsigned char *)"message waiting indication subscription failure"   },
    {   100,              (unsigned char *)"unknown"                                           },
    // /* GPRS and EPS-related errors start */
    // {   103,              (unsigned char *)"Illegal MS (#3)"                                      },
    // {   106,              (unsigned char *)"Illegal ME (#6)"                                      },
    // {   107,              (unsigned char *)"GPRS services not allowed (#7)"                       },
    // {   108,              (unsigned char *)"GPRS services and non-GPRS services not allowed (#8)" },
    // {   111,              (unsigned char *)"PLMN not allowed (#11)"                               },
    // {   112,              (unsigned char *)"Location area not allowed (#12)"                      },
    // {   113,              (unsigned char *)"Roaming not allowed in this location area (#13)"      },
    // {   114,              (unsigned char *)"GPRS services not allowed in this PLMN (#14)"         },
    // {   115,              (unsigned char *)"No Suitable Cells In Location Area (#15)"             },
    // {   122,              (unsigned char *)"Congestion (#22))"                                    },
    // {   125,              (unsigned char *)"Not authorized for this CSG (#25)",                   },
    {   171,              (unsigned char *)"Last PDN disconnection not allowed (#49) "            },
    // {   172,              (unsigned char *)"Semantically incorrect message (#95) "                },
    // {   173,              (unsigned char *)"Mandatory information element error (#96)"            },
    // {   174,              (unsigned char *)"Information element non-existent or not implemented (#97)" },
    // {   175,              (unsigned char *)"Conditional IE error (#99)"                           },
    // {   176,              (unsigned char *)"Protocol error, unspecified (#111)"                   },
    // /* GPRS and EPS-related errors end */
    // /* Errors related to a failure to activate a context start */
    {   177,              (unsigned char *)"Operator Determined Barring (#8)"                        },
    {   126,              (unsigned char *)"insufficient resources (#26)"                            },
    // {   127,              (unsigned char *)"missing or unknown APN (#27)"                            },
    // {   128,              (unsigned char *)"unknown PDP address or PDP type (#28)"                   },
    // {   129,              (unsigned char *)"user authentication failed (#29)"                        },
    // {   130,              (unsigned char *)"activation rejected by GGSN, Serving GW or PDN GW (#30)" },
    // {   131,              (unsigned char *)"activation rejected, unspecified (#31)"                  },
    {   132,              (unsigned char *)"service option not supported (#32)"                      },
    {   133,              (unsigned char *)"requested service option not subscribed (#33)"           },
    {   134,              (unsigned char *)"service option temporarily out of order (#34)"           },
    // {   140,              (unsigned char *)"feature not supported (#40)"                             },
    // {   141,              (unsigned char *)"semantic error in the TFT operation (#41)"               },
    // {   142,              (unsigned char *)"syntactical error in the TFT operation (#42)"            },
    {   143,              (unsigned char *)"unknown PDP context (#43)"                               },
    {   144,              (unsigned char *)"semantic errors in packet filter(s) (#44)"               },
    // {   145,              (unsigned char *)"syntactical errors in packet filter(s) (#45)"            },
    // {   146,              (unsigned char *)"PDP context without TFT already activated (#46)"         },
    // {   149,              (unsigned char *)"PDP authentication failure"                              },
    {   178,              (unsigned char *)"maximum number of PDP contexts reached (#65)"            },
    // {   179,              (unsigned char *)"requested APN not supported in current RAT and PLMN combination (#66)" },
    // {   180,              (unsigned char *)"request rejected, Bearer Control Mode violation (#48)"   },
    {   181,              (unsigned char *)"unsupported QCI value (#83)"                             },
    // /* Errors related to a failure to activate a context end */
};

const ST_ATC_GLOBLE_ERROR_CAUSE_TABLE PEmmCauseTextTbl[D_ATC_EMM_CAUSE_TBL_SIZE] = 
{
    {   2,                (unsigned char *)"IMSI unknown in HSS"                                     },
    {   3,                (unsigned char *)"Illegal UE"                                              },
    {   5,                (unsigned char *)"IMEI not accepted"                                       },
    {   6,                (unsigned char *)"Illegal ME"                                              },
    {   7,                (unsigned char *)"EPS services not allowed"                                },
    {   8,                (unsigned char *)"EPS services and non,EPS services not allowed"           },
    {   9,                (unsigned char *)"UE identity cannot be derived by the network"            },
    {   10,               (unsigned char *)"Implicitly detached"                                     },
    {   11,               (unsigned char *)"PLMN not allowed"                                        },
    {   12,               (unsigned char *)"Tracking area not allowed"                               },
    {   13,               (unsigned char *)"Roaming not allowed in this tracking area"               },
    {   14,               (unsigned char *)"EPS services not allowed in this PLMN"                   },
    {   15,               (unsigned char *)"No suitable cells in tracking area"                      },
    {   16,               (unsigned char *)"MSC temporarily not reachable"                           },
    {   17,               (unsigned char *)"Network failure"                                         },
    {   18,               (unsigned char *)"CS domain not available"                                 },
    {   19,               (unsigned char *)"ESM failure"                                             },
    {   20,               (unsigned char *)"MAC failure"                                             },
    {   21,               (unsigned char *)"Synch failure"                                           },
    {   22,               (unsigned char *)"Congestion"                                              },
    {   23,               (unsigned char *)"UE security capabilities mismatch"                       },
    {   24,               (unsigned char *)"Security mode rejected, unspecified"                     },
    {   25,               (unsigned char *)"Not authorized for this CSG"                             },
    {   26,               (unsigned char *)"Non,EPS authentication unacceptable"                     },
    {   35,               (unsigned char *)"Requested service option not authorized in this PLMN"    },
    {   39,               (unsigned char *)"CS service temporarily not available"                    },
    {   40,               (unsigned char *)"No EPS bearer context activated"                         },
    {   42,               (unsigned char *)"Severe network failure"                                  },
};

const ST_ATC_GLOBLE_ERROR_CAUSE_TABLE PEsmCauseTextTbl[D_ATC_ESM_CAUSE_TBL_SIZE] = 
{
    {   8,                 (unsigned char *)"Operator Determined Barring"                            },
    {   26,                (unsigned char *)"Insufficient resources"                                 },
    {   27,                (unsigned char *)"Missing or unknown APN"                                 },
    {   28,                (unsigned char *)"Unknown PDN type"                                       },
    {   29,                (unsigned char *)"User authentication failed"                             },
    {   30,                (unsigned char *)"Request rejected by Serving GW or PDN GW"               },
    {   31,                (unsigned char *)"Request rejected, unspecified"                          },
    {   32,                (unsigned char *)"Service option not supported"                           },
    {   33,                (unsigned char *)"Requested service option not subscribed"                },
    {   34,                (unsigned char *)"Service option temporarily out of order"                },
    {   35,                (unsigned char *)"PTI already in use"                                     },
    {   36,                (unsigned char *)"Regular deactivation"                                   },
    {   37,                (unsigned char *)"EPS QoS not acceptedETSI"                               },
    {   38,                (unsigned char *)"Network failure"                                        },
    {   39,                (unsigned char *)"Reactivation requested"                                 },
    {   41,                (unsigned char *)"Semantic error in the TFT operation."                   },
    {   42,                (unsigned char *)"Syntactical error in the TFT operation."                },
    {   43,                (unsigned char *)"Invalid EPS bearer identity"                            },
    {   44,                (unsigned char *)"Semantic errors in packet filter(s)"                    },
    {   45,                (unsigned char *)"Syntactical error in packet filter(s)"                  },
    {   47,                (unsigned char *)"PTI mismatch"                                           },
    {   49,                (unsigned char *)"Last PDN disconnection not allowed"                     },
    {   50,                (unsigned char *)"PDN type IPv4 only allowed"                             },
    {   51,                (unsigned char *)"PDN type IPv6 only allowed"                             },
    {   52,                (unsigned char *)"single address bearers only allowed"                    },
    {   53,                (unsigned char *)"ESM information not receivedETSI"                       },
    {   54,                (unsigned char *)"PDN connection does not exist"                          },
    {   55,                (unsigned char *)"Multiple PDN connections for a given APN not allowed"   },
    {   56,                (unsigned char *)"Collision with network initiated request"               },
    {   57,                (unsigned char *)"PDN type IPv4v6 only allowed"                           },
    {   58,                (unsigned char *)"PDN type non IP only allowed"                           },
    {   59,                (unsigned char *)"Unsupported QCI value"                                  },
    {   60,                (unsigned char *)"Bearer handling not supported"                          },
    {   65,                (unsigned char *)"Maximum number of EPS bearers reached"                  },
    {   66,                (unsigned char *)"Requested APN not supported in current RAT and PLMN combination"},
    {   81,                (unsigned char *)"Invalid PTI value"                                      },
    {   112,               (unsigned char *)"APN restriction value incompatible with active EPS bearer context."},
    {   113,               (unsigned char *)"Multiple accesses to a PDN connection not allowed"      }, 
    {   200,               (unsigned char *)"User detached by command +COPS=2"                       }, //D_ATC_USER_DETACH_BY_CMD_COPS
    {   201,               (unsigned char *)"User detached by command +CGATT=0"                      }, //D_ATC_USER_DETACH_BY_CMD_CGATT
};

const unsigned char ATC_PinCodeTbl[3][8] =
{
    "READY",                                                                            /* 0 */
    "SIM PIN",                                                                          /* 1 */
    "SIM PUK",                                                                          /* 2 */
    // "PH-SIM PIN",                                                                       /* 3 */
    // "PH-FSIM PIN",                                                                      /* 4 */
    // "PH-FSIM PUK",                                                                      /* 5 */
    // "SIM PIN2",                                                                         /* 6 */
    // "SIM PUK2",                                                                         /* 7 */
    // "PH-NET PIN",                                                                       /* 8 */
    // "PH-NET PUK",                                                                       /* 9 */
    // "PH-NETSUB PIN",                                                                    /* 10 */
    // "PH-NETSUB PUK",                                                                    /* 11 */
    // "PH-SP PIN",                                                                        /* 12 */
    // "PH-SP PUK",                                                                        /* 13 */
    // "PH-CORP PIN",                                                                      /* 14 */
    // "PH-CORP PUK"                                                                       /* 15 */
};

static void ATC_DelCascadeInfo()
{
    if(NULL != g_AtcApInfo.atCascateInfo.pCasaadeAtBuff)
    {
        AtcAp_Free(g_AtcApInfo.atCascateInfo.pCasaadeAtBuff);
    }

    AtcAp_MemSet(&g_AtcApInfo.atCascateInfo, 0, sizeof(ST_ATC_CASCADE_AT_INFO));
}

void ATC_SendApDataReq(unsigned char ucReqType, unsigned char ucExternalFlg, unsigned long ulAppSemaId, unsigned short usDataLen, unsigned char* pucData, unsigned char ucAtChannelId)
{
    ATC_AP_MSG_DATA_REQ_STRU   *pAtcApDataReq = NULL;
    unsigned long              ulLength;

    if (usDataLen < 4)
    {
        ulLength = sizeof(ATC_AP_MSG_DATA_REQ_STRU);
    }
    else
    {
        ulLength = sizeof(ATC_AP_MSG_DATA_REQ_STRU) + usDataLen - 4 + 1;
    }
    
    pAtcApDataReq = (ATC_AP_MSG_DATA_REQ_STRU *)AtcAp_Malloc(ulLength);

    pAtcApDataReq->ucReqType = ucReqType;
    pAtcApDataReq->ucExternalFlg = ucExternalFlg;
    pAtcApDataReq->ulSemaId = ulAppSemaId;
    pAtcApDataReq->ucAtChannelId = ucAtChannelId;
    pAtcApDataReq->usMsgLen = usDataLen;
    AtcAp_MemCpy(pAtcApDataReq->aucMsgData, pucData, usDataLen);
    pAtcApDataReq->ucAplNum = 0;

    pAtcApDataReq->MsgHead.ulMsgName = D_ATC_AP_DATA_REQ;
    AtcAp_SendMsg2AtcAp((void*)pAtcApDataReq, &g_AtcApInfo.msgInfo);
}

void AtcAp_SendOkRsp()
{
    ST_ATC_CMD_COM_EVENT tCmdComEvent = { D_ATC_AP_AT_CMD_RST };
    
    if(NULL != g_AtcApInfo.pCurrEvent)
    {
        AtcAp_FreeEventBuffer((unsigned char*)g_AtcApInfo.pCurrEvent);
        g_AtcApInfo.pCurrEvent = NULL;
    }
    g_AtcApInfo.usCurrEvent = 0xFFFF;

    if(g_AtcApInfo.atCascateInfo.ucCascadeAtCnt > 1)
    {
        AtcAp_PrintLog(0, NAS_THREAD_ID, DEBUG_LOG, "[ATC_SendOkRsp]CascadeAtCnt = %d",g_AtcApInfo.atCascateInfo.ucCascadeAtCnt);
        g_AtcApInfo.atCascateInfo.ucCascadeAtCnt--;
        AtcAp_CascadeAtProc_NextAt();
        return;
    }
    else if( g_AtcApInfo.atCascateInfo.ucCascadeAtCnt == 0)
    {
        AtcAp_PrintLog(0, NAS_THREAD_ID, DEBUG_LOG, "[ATC_SendOkRsp]RedundancyRpt OK");
        return;
    }

    if(g_AtcApInfo.ucTempSeqNum != 0)
    {
        AtcAp_AppInterfaceInfo_CmdRstProc(g_AtcApInfo.ucTempSeqNum, D_APP_INTERFACE_RESULT_SUCC);
    }
    g_AtcApInfo.ucTempSeqNum = 0;

    ATC_DelCascadeInfo();
    if(g_AtcApInfo.ucUserAtFlg == ATC_AP_FALSE)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\nOK\r\n");
        AtcAp_SendDataInd((unsigned char*)&tCmdComEvent);
    }

    g_AtcApInfo.ucWaitOKOrErrorFlg = ATC_AP_FALSE;
    AtcAp_AtcDataReqListProc();
    return;
}

unsigned char api_GetCmeeValue()
{
#if VER_CM
    return g_AtcApInfo.ucCmeeValue;
#else
    return g_softap_fac_nv->cmee_mode;
#endif

}

void AtcAp_SendCmeeErr(unsigned short usErrCode)
{
    unsigned short   i;
    ST_ATC_CMD_COM_EVENT tCmdComEvent = { D_ATC_AP_AT_CMD_RST };
    
    if(NULL != g_AtcApInfo.pCurrEvent)
    {
        AtcAp_FreeEventBuffer((unsigned char*)g_AtcApInfo.pCurrEvent);
        g_AtcApInfo.pCurrEvent = NULL;
    }
    g_AtcApInfo.usCurrEvent = 0xFFFF;
    
    if( g_AtcApInfo.atCascateInfo.ucCascadeAtCnt > 1)
    {
        AtcAp_PrintLog(0, NAS_THREAD_ID, DEBUG_LOG, "[ATC_SendCmeeErr]CascadeAtCnt = %d",g_AtcApInfo.atCascateInfo.ucCascadeAtCnt);
    }
    else if( g_AtcApInfo.atCascateInfo.ucCascadeAtCnt == 0)
    {
        AtcAp_PrintLog(0, NAS_THREAD_ID, DEBUG_LOG, "[ATC_SendCmeeErr]RedundancyRpt CME ERROR");
        return;
    }

    if(g_AtcApInfo.ucTempSeqNum != 0)
    {
        AtcAp_AppInterfaceInfo_CmdRstProc(g_AtcApInfo.ucTempSeqNum, D_APP_INTERFACE_RESULT_FAIL);
    }
    g_AtcApInfo.ucTempSeqNum = 0;

    ATC_DelCascadeInfo();
    if(g_AtcApInfo.ucUserAtFlg == ATC_AP_FALSE)
    {
        if (D_ATC_ERRMOD_DIG_CMEE == api_GetCmeeValue())
        {
            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                (const unsigned char *)"\r\n+CME ERROR:%d\r\n", usErrCode);
        }
        else if (D_ATC_ERRMOD_STR_CMEE == api_GetCmeeValue())
        {
            for (i = 0; i < D_ATC_CME_ERROR_TBL_SIZE; i++)
            {
                if (usErrCode == PCmeErrorTextTbl[i].usErrCode)
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                        (const unsigned char *)"\r\n+CME ERROR:%s\r\n", PCmeErrorTextTbl[i].pCmeErrorText);
                    break;
                }
            }
            
            if (i ==  D_ATC_CME_ERROR_TBL_SIZE)
            {
                 g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                    (const unsigned char *)"\r\n+CME ERROR:%s\r\n", "unknown");
            }
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                    (const unsigned char *)"\r\nERROR\r\n");
        }
        AtcAp_SendDataInd((unsigned char*)&tCmdComEvent);
    }

    g_AtcApInfo.ucWaitOKOrErrorFlg = ATC_AP_FALSE;
    AtcAp_AtcDataReqListProc();
}

void AtcAp_SendErrorRsp()
{
    AtcAp_SendCmeeErr(D_ATC_AP_CME_UNKNOWN);
}

/*******************************************************************************
  MODULE    : ATC_SendCmsErr
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  Dep2_066   2016.12.20   create
*******************************************************************************/
void AtcAp_SendCmsErr(unsigned short usErrCode)
{
    ST_ATC_CMD_COM_EVENT tCmdComEvent = { D_ATC_AP_AT_CMD_RST };
    
    if(NULL != g_AtcApInfo.pCurrEvent)
    {
        AtcAp_FreeEventBuffer((unsigned char*)g_AtcApInfo.pCurrEvent);
        g_AtcApInfo.pCurrEvent = NULL;
    }
    g_AtcApInfo.usCurrEvent = 0xFFFF;

    if( g_AtcApInfo.atCascateInfo.ucCascadeAtCnt > 1)
    {
        //AtcAp_PrintLog(0, NAS_THREAD_ID, DEBUG_LOG, "[ATC_SendCmsErr]CascadeAtCnt = %d",g_AtcApInfo.atCascateInfo.ucCascadeAtCnt);
    }
    else if( g_AtcApInfo.atCascateInfo.ucCascadeAtCnt == 0)
    {
        //AtcAp_PrintLog(0, NAS_THREAD_ID, DEBUG_LOG, "[ATC_SendCmsErr]RedundancyRpt ERROR");
        return;
    }

    if(g_AtcApInfo.ucTempSeqNum != 0)
    {
        AtcAp_AppInterfaceInfo_CmdRstProc(g_AtcApInfo.ucTempSeqNum, D_APP_INTERFACE_RESULT_FAIL);
    }
    g_AtcApInfo.ucTempSeqNum = 0;

    ATC_DelCascadeInfo();
    if(g_AtcApInfo.ucUserAtFlg == ATC_AP_FALSE)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
            (const unsigned char *)"\r\n+CMS ERROR:%d\r\n", usErrCode);
        AtcAp_SendDataInd((unsigned char*)&tCmdComEvent);
    }

    g_AtcApInfo.ucWaitOKOrErrorFlg = ATC_AP_FALSE;
    AtcAp_AtcDataReqListProc();
    return;
}

extern void  SendAtInd2User (char *pAt, unsigned int ulAtLen, int srcFd);
extern int at_write_to_uart(char* buf, int size);

void AtcAp_AtCacheLogHandle(unsigned char* pbuffer ,unsigned short usAtLogLen, unsigned short usEvent)
{
    ST_ATCNF_INFO*  pTemBuffer;
    unsigned char   i = 0;
    
    xy_printf(0,ATC_AP_T,INFO_LOG, "[AtcAp_AtCacheLogHandle] %d   %s",g_AtcApInfo.stAtLogCacheList.ucAtLogCount, (const char *)pbuffer);

    if(g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList == NULL)
    {
        g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList = (ST_ATCNF_INFO*)AtcAp_Malloc(sizeof(ST_ATCNF_INFO) * AT_LOG_CACHE_MAX_NUM);
        g_AtcApInfo.stAtLogCacheList.ucBufferNum = AT_LOG_CACHE_MAX_NUM;
    }

    if(g_AtcApInfo.stAtLogCacheList.ucAtLogCount >= g_AtcApInfo.stAtLogCacheList.ucBufferNum)
    {
        g_AtcApInfo.stAtLogCacheList.ucBufferNum += AT_LOG_CACHE_MAX_NUM;
        pTemBuffer = (ST_ATCNF_INFO*)AtcAp_Malloc(sizeof(ST_ATCNF_INFO) * g_AtcApInfo.stAtLogCacheList.ucBufferNum);
        AtcAp_MemCpy(pTemBuffer, g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList ,(sizeof(ST_ATCNF_INFO) * g_AtcApInfo.stAtLogCacheList.ucAtLogCount));
        AtcAp_Free(g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList);
        g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList = pTemBuffer;
    }
    
    if(usEvent != D_ATC_AP_AT_CMD_RST)
    {

        if(usEvent <= D_ATC_AP_AT_CMD_RST || D_ATC_AP_SMS_PDU_IND == usEvent)
        {
            g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList[g_AtcApInfo.stAtLogCacheList.ucAtLogCount].cttyFd = g_AtcApInfo.ucAtChannelId;
        }
        else
        {
            g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList[g_AtcApInfo.stAtLogCacheList.ucAtLogCount].cttyFd = AT_FD_INVAILD;
        }
        g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList[g_AtcApInfo.stAtLogCacheList.ucAtLogCount].usCnfLen = usAtLogLen;
        g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList[g_AtcApInfo.stAtLogCacheList.ucAtLogCount].pAtCnfBuf = AtcAp_Malloc(usAtLogLen + 1);
        AtcAp_MemCpy(g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList[g_AtcApInfo.stAtLogCacheList.ucAtLogCount].pAtCnfBuf, pbuffer, usAtLogLen);
        g_AtcApInfo.stAtLogCacheList.ucAtLogCount++;
        return;
    }
    g_AtcApInfo.usAtRspOKFlg = ATC_AP_FALSE;
    SendAtInd2User( pbuffer, usAtLogLen, g_AtcApInfo.ucAtChannelId);
    
    for(i = 0; i < g_AtcApInfo.stAtLogCacheList.ucAtLogCount; i++)
    {
        SendAtInd2User( g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList[i].pAtCnfBuf, g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList[i].usCnfLen, g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList[i].cttyFd);
        AtcAp_Free(g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList[i].pAtCnfBuf);
    }
    AtcAp_Free(g_AtcApInfo.stAtLogCacheList.pucAtLogCacheList);
    AtcAp_MemSet(&g_AtcApInfo.stAtLogCacheList, 0, sizeof(ST_ATCNF_CACHE_INFO));

}

unsigned char ATC_SMS_DATA_URC_CHECK(unsigned short usEvent)
{
    switch(usEvent)
    {
        case D_ATC_EVENT_QCMGR:
        case D_ATC_EVENT_CMGL:
        case D_ATC_EVENT_CMGR:
        case D_ATC_AP_CDS_IND:
        case D_ATC_AP_CMT_IND:
        case D_ATC_EVENT_MWIFISCANQUERY:
            return ATC_AP_TRUE;
        default:
            return ATC_AP_FALSE;
    }
}
void AtcAp_SendDataInd(unsigned char *pEventMsg)
{
    ST_ATC_CMD_COM_EVENT *pCmdComEvent = (ST_ATC_CMD_COM_EVENT*)pEventMsg;
    
    if(g_softap_fac_nv->ucAtHeaderSpaceFlg == 1)
    {
        if(ATC_SMS_DATA_URC_CHECK(pCmdComEvent->usEvent) == ATC_AP_TRUE)
        {
            ATC_CmdHeaderWithSpaceProc((char**)&g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, &g_AtcApInfo.stAtRspInfo.usRspLen, D_ATC_RSP_MAX_BUF_SIZE - 1, ATC_AP_TRUE);
        }
        else
        {
            ATC_CmdHeaderWithSpaceProc((char**)&g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, &g_AtcApInfo.stAtRspInfo.usRspLen, D_ATC_RSP_MAX_BUF_SIZE - 1, ATC_AP_FALSE);
        }
    }

    if(g_AtcApInfo.stAtRspInfo.usRspLen != 0)
    {
        if(g_AtcApInfo.usAtRspOKFlg == ATC_AP_TRUE)
        {
            AtcAp_AtCacheLogHandle(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, g_AtcApInfo.stAtRspInfo.usRspLen, pCmdComEvent->usEvent);

            g_AtcApInfo.stAtRspInfo.usRspLen = 0;
            AtcAp_MemSet(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, 0 ,D_ATC_RSP_MAX_BUF_SIZE);
            return;
        }
#if OLD_ATC
        AT_RCV_FROM_PS(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,g_AtcApInfo.stAtRspInfo.usRspLen);
#else
        if(pCmdComEvent->usEvent <= D_ATC_AP_AT_CMD_RST || D_ATC_AP_SMS_PDU_IND == pCmdComEvent->usEvent)
        {
            SendAtInd2User(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,g_AtcApInfo.stAtRspInfo.usRspLen, g_AtcApInfo.ucAtChannelId);
        }
        else
        {
            SendAtInd2User(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,g_AtcApInfo.stAtRspInfo.usRspLen, AT_FD_INVAILD);
        }
#endif
    }
    g_AtcApInfo.stAtRspInfo.usRspLen = 0;
    AtcAp_MemSet(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, 0 ,D_ATC_RSP_MAX_BUF_SIZE);
    return;
}

/*******************************************************************************
  MODULE    : ATC_SendLongDataInd
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  GCM   2018.11.02   create
*******************************************************************************/
void AtcAp_SendLongDataInd(unsigned char *pEventMsg, unsigned char **pBuffer, unsigned short usMaxLen)
{
    ST_ATC_CMD_COM_EVENT *pCmdComEvent = (ST_ATC_CMD_COM_EVENT*)pEventMsg;
    
    if(g_softap_fac_nv->ucAtHeaderSpaceFlg == 1)
    {
        if(ATC_SMS_DATA_URC_CHECK(pCmdComEvent->usEvent) == ATC_AP_TRUE)
        {
            ATC_CmdHeaderWithSpaceProc((char**)pBuffer, &g_AtcApInfo.stAtRspInfo.usRspLen, usMaxLen, ATC_AP_TRUE);
        }
        else
        {
            ATC_CmdHeaderWithSpaceProc((char**)pBuffer, &g_AtcApInfo.stAtRspInfo.usRspLen, usMaxLen, ATC_AP_FALSE);
        }
    }

    if(g_AtcApInfo.stAtRspInfo.usRspLen != 0)
    {
        if(g_AtcApInfo.usAtRspOKFlg == ATC_AP_TRUE)
        {
            AtcAp_AtCacheLogHandle(*pBuffer, g_AtcApInfo.stAtRspInfo.usRspLen, pCmdComEvent->usEvent);

            g_AtcApInfo.stAtRspInfo.usRspLen = 0;
            return;
        }
#if OLD_ATC
        AT_RCV_FROM_PS(*pBuffer, g_AtcApInfo.stAtRspInfo.usRspLen);
#else
        if(pCmdComEvent->usEvent <= D_ATC_AP_AT_CMD_RST)
        {
            SendAtInd2User(*pBuffer,g_AtcApInfo.stAtRspInfo.usRspLen, g_AtcApInfo.ucAtChannelId);
        }
        else
        {
            SendAtInd2User(*pBuffer,g_AtcApInfo.stAtRspInfo.usRspLen, AT_FD_INVAILD);
        }
#endif
        g_AtcApInfo.stAtRspInfo.usRspLen = 0;
    }
    return;
}

/*******************************************************************************
  MODULE    : ATC_FreeEventBuffer
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  GCM   2018.10.15   create
*******************************************************************************/
void AtcAp_FreeEventBuffer(unsigned char* pCmdEvent)
{
    UN_ATC_CMD_EVENT* pCmdComEvent;

    if(NULL == pCmdEvent)
    {
        return;
    }
    
    pCmdComEvent = (UN_ATC_CMD_EVENT*)pCmdEvent;
    switch(pCmdComEvent->stCgdcontParam.usEvent)
    {
        case D_ATC_EVENT_CGDCONT:
            if(NULL != pCmdComEvent->stCgdcontParam.pucApnValue)
            {
                AtcAp_Free(pCmdComEvent->stCgdcontParam.pucApnValue);
            }
            break;
        case D_ATC_EVENT_CSODCP:
            if(NULL != pCmdComEvent->stCsodcpParam.pucCpdata)
            {
                AtcAp_Free(pCmdComEvent->stCsodcpParam.pucCpdata);
            }
            break;
        case D_ATC_EVENT_CSIM:
            if(NULL != pCmdComEvent->stCsimParam.pucCommand)
            {
                AtcAp_Free(pCmdComEvent->stCsimParam.pucCommand);
            }
            break;
        case D_ATC_EVENT_CGLA:
            if(NULL != pCmdComEvent->stCglaParam.pucCommand)
            {
                AtcAp_Free(pCmdComEvent->stCglaParam.pucCommand);
            }
            break;
        case D_ATC_EVENT_CRSM:
            if(NULL != pCmdComEvent->stCrsmParam.pucData)
            {
                AtcAp_Free(pCmdComEvent->stCrsmParam.pucData);
            }
            break;
        case D_ATC_EVENT_NSNPD:
            if(NULL != pCmdComEvent->stNsnpdParam.pucNonIpData)
            {
                AtcAp_Free(pCmdComEvent->stNsnpdParam.pucNonIpData);
            }
            break;
        case D_ATC_EVENT_PSTEST:
            if(NULL != pCmdComEvent->stPstestParam.pucData)
            {
                AtcAp_Free(pCmdComEvent->stPstestParam.pucData);
            }
            break;
        default:
            break;
    }

    AtcAp_Free(pCmdComEvent);
}

#if ATC_DSP
extern void Ps_SendMsg(ULONG ulSrcTskId, ULONG ulMsgName, void* pMsg, ULONG ulMemSize);
static void AtcAp_SendAtcDateReqToNas(unsigned char *pReqMsg, unsigned short usMsgLen)
{
    Ps_SendMsg(ATC_THREAD_ID, D_ATC_DATA_REQ, pReqMsg, usMsgLen);
}

int AtcAp_SndDataReqToPs(unsigned char ucAtCmdFlg, unsigned char *pCodeStream, unsigned short usCodeStreamLen)
{
    ST_ATC_DATA_REQ*        pAtcDataReq;
    unsigned short          usLen;
    UN_ATC_CMD_EVENT*       pCmdEvent;

#if RF_MT_MODE == 1
    if (g_rf_mt.rf_mt_mode == 1)
    {
        return ATC_AP_FALSE;
    }
#endif

    if(usCodeStreamLen <= 4)
    {
        usLen = sizeof(ST_ATC_DATA_REQ);
    }
    else
    {
        usLen = sizeof(ST_ATC_DATA_REQ) + usCodeStreamLen - 4;
    }
    
    pAtcDataReq = (ST_ATC_DATA_REQ *)AtcAp_Malloc(usLen + 1);
    pAtcDataReq->ucAtCmdFlg = ucAtCmdFlg;
    if(g_AtcApInfo.ucUserAtFlg == ATC_AP_TRUE)
    {
        pAtcDataReq->ucSeqNum = g_AtcApInfo.stAppInterfaceInfo.ucSeqNum;
    }
    pAtcDataReq->usMsgLen = usCodeStreamLen;
    AtcAp_MemCpy(pAtcDataReq->aucMsgData, pCodeStream, usCodeStreamLen);

    pCmdEvent  = (UN_ATC_CMD_EVENT*)pCodeStream; 

    xy_printf(0, ATC_AP_T, DEBUG_LOG, "[AtcAp_SndDataReqToPs] ucUserAtFlg=%d, ucSeqNum=%d, event=%d", g_AtcApInfo.ucUserAtFlg, pAtcDataReq->ucSeqNum, pCmdEvent->stCgdcontParam.usEvent);
    AtcAp_SendAtcDateReqToNas((unsigned char*)pAtcDataReq, usLen);

    return ATC_AP_TRUE;
}
#else //M3
extern int send_ps_shm_msg(void *msg, int msg_len);
int AtcAp_SndDataReqToShm(unsigned char ucAtCmdFlg, unsigned char *pCodeStream, unsigned short usCodeStreamLen)
{
    unsigned short          usLen;
    UN_ATC_CMD_EVENT*       pCmdEvent;
    int                     ret;
    
    ATC_INTER_CORE_MSG_M3ToDSP_STRU* pInterCoreMsg;
    
    if(usCodeStreamLen <= 4)
    {
        usLen = sizeof(ATC_INTER_CORE_MSG_M3ToDSP_STRU);
    }
    else
    {
        usLen = sizeof(ATC_INTER_CORE_MSG_M3ToDSP_STRU) + usCodeStreamLen - 4;
    }

    pInterCoreMsg = (ATC_INTER_CORE_MSG_M3ToDSP_STRU *)AtcAp_Malloc(usLen);
    pInterCoreMsg->ucAtCmdFlg = ucAtCmdFlg;
    if(g_AtcApInfo.ucUserAtFlg == ATC_AP_TRUE)
    {
        pInterCoreMsg->ucSeqNum = g_AtcApInfo.stAppInterfaceInfo.ucSeqNum;
    }
    else
    {
        pInterCoreMsg->ucSeqNum = 0;
    }
    pInterCoreMsg->usMsgLen = usCodeStreamLen;
    if(0 != usCodeStreamLen)
    {
        AtcAp_MemCpy(pInterCoreMsg->aucMsgData, pCodeStream, usCodeStreamLen);
    }
    pCmdEvent  = (UN_ATC_CMD_EVENT*)pCodeStream; 
    xy_printf(0, ATC_AP_T, DEBUG_LOG, "[AtcAp_SndDataReqToShm] ucUserAtFlg=%d, ucSeqNum=%d, event=%d", g_AtcApInfo.ucUserAtFlg, pInterCoreMsg->ucSeqNum, pCmdEvent->stCgdcontParam.usEvent);
    
    ret = !send_ps_shm_msg(pInterCoreMsg, usLen);
    AtcAp_Free(pInterCoreMsg);

    return ret;
}
#endif

unsigned short AtcAp_StrPrintf_AtcRspBuf(const char* FormatBuffer,...)
{
    unsigned short          usLen;
    va_list                 ap;
    
    va_start(ap, FormatBuffer);
    usLen = vsnprintf((char*)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                        D_ATC_RSP_MAX_BUF_SIZE - g_AtcApInfo.stAtRspInfo.usRspLen - 1, FormatBuffer, ap);
    g_AtcApInfo.stAtRspInfo.usRspLen += usLen;
    va_end(ap);

    return usLen;
}

static unsigned short AtcAp_GetCmdEventBuffSize(UN_ATC_CMD_EVENT *pCmdEvent)
{
    switch(pCmdEvent->stCgdcontParam.usEvent)
    {
        case D_ATC_EVENT_CGSN:
            return sizeof(ST_ATC_CGSN_PARAMETER);
        case D_ATC_EVENT_CEREG:
        case D_ATC_EVENT_CGREG:
            return sizeof(ST_ATC_CEREG_PARAMETER);
        case D_ATC_EVENT_CGDCONT:
            return offsetof(ST_ATC_CGDCONT_PARAMETER, aucApnValue) + pCmdEvent->stCgdcontParam.ucApnLen;
        case D_ATC_EVENT_CFUN:
            return sizeof(ST_ATC_CFUN_PARAMETER);
        case D_ATC_EVENT_CESQ:
            return sizeof(ST_ATC_CESQ_PARAMETER);
        case D_ATC_EVENT_CGPADDR:
            return sizeof(ST_ATC_CGPADDR_PARAMETER);
        case D_ATC_EVENT_CGACT:
            return sizeof(ST_ATC_CGACT_PARAMETER);
        case D_ATC_EVENT_CSODCP:
            return offsetof(ST_ATC_CSODCP_PARAMETER, pucCpdata) + pCmdEvent->stCsodcpParam.usCpdataLength;
        case D_ATC_EVENT_CRTDCP:
            return sizeof(ST_ATC_CRTDCP_PARAMETER);
        case D_ATC_EVENT_CEDRXS:
            return sizeof(ST_ATC_CEDRXS_PARAMETER);
        case D_ATC_EVENT_CPSMS:
            return sizeof(ST_ATC_CPSMS_PARAMETER);
        case D_ATC_EVENT_CGAPNRC:
            return sizeof(ST_ATC_CGAPNRC_PARAMETER);
#ifdef ESM_DEDICATED_EPS_BEARER
        case D_ATC_EVENT_CGDSCONT:
            return sizeof(ST_ATC_CGDSCONT_PARAMETER);
        case D_ATC_EVENT_CGSCONTRDP:
            return sizeof(ST_ATC_CGSCONTRDP_PARAMETER);
        case D_ATC_EVENT_CGTFT:
            return sizeof(ST_ATC_CGTFT_PARAMETER);
        case D_ATC_EVENT_CGTFTRDP:
            return sizeof(ST_ATC_CGTFTRDP_PARAMETER);
        case D_ATC_EVENT_CGEQOS:
            return sizeof(ST_ATC_CGEQOS_PARAMETER);
        case D_ATC_EVENT_CGCMOD:
            return sizeof(ST_ATC_CGCMOD_PARAMETER);
#endif
#ifdef LTE_SMS_FEATURE
        case D_ATC_EVENT_CPMS:
            return sizeof(ST_ATC_CPMS_PARAMETER);
        case D_ATC_EVENT_CSCA:
            return sizeof(ST_ATC_CSCA_PARAMETER);
        case D_ATC_EVENT_CSMP:
            return sizeof(ST_ATC_CSMP_PARAMETER);
        case D_ATC_EVENT_CNMI:
            return sizeof(ST_ATC_CNMI_PARAMETER);
        case D_ATC_EVENT_CMGW:
        case D_ATC_EVENT_CMGS:
        case D_ATC_EVENT_CMGC:
        case D_ATC_EVENT_CNMA:
            if(g_SmsFormatMode == 0)
            {
                return offsetof(ST_ATC_PDU_TPDU_PARAMETER, aucPduData);
            }
            else
            {
                return offsetof(ST_ATC_TEXT_PARAMETER, pucTextData);
            }
        case D_ATC_EVENT_CMGR:
        case D_ATC_EVENT_QCMGR:
            return sizeof(ST_ATC_CMGR_PARAMETER);
        case D_ATC_EVENT_CMGD:
            return sizeof(ST_ATC_CMGD_PARAMETER);
        case D_ATC_EVENT_CMGL:
            return sizeof(ST_ATC_CMGL_PARAMETER);
        case D_ATC_EVENT_CMSS:
            return sizeof(ST_ATC_CMSS_PARAMETER);
        case D_ATC_EVENT_CMMS:
            return sizeof(ST_ATC_CMMS_PARAMETER);
        case D_ATC_AP_SMS_PDU_REQ:
            return offsetof(ST_ATC_AP_SMS_PDU_PARAMETER, pucPduData) + pCmdEvent->stAtcApPduParam.usPduLength;
        case D_ATC_AP_SMS_TEXT_REQ:
            return offsetof(ST_ATC_AP_SMS_TEXT_PARAMETER, pucTextData) + pCmdEvent->stAtcApTextParam.usTextLength;
        case D_ATC_EVENT_CSCB:
            return sizeof(ST_ATC_CSCB_PARAMETER);
        case D_ATC_EVENT_QCMGS:
            return offsetof(ST_ATC_QCMGS_PARAMETER, pucTextData) + pCmdEvent->stQcmgsParam.usTextLength + 1;
#endif
        case D_ATC_EVENT_COPS:
            return sizeof(ST_ATC_COPS_PARAMETER);
        case D_ATC_EVENT_CSIM:
            return offsetof(ST_ATC_CSIM_PARAMETER, aucCommand) + pCmdEvent->stCsimParam.usLength;
        case D_ATC_EVENT_CCHC:
            return sizeof(ST_ATC_CCHC_PARAMETER);
        case D_ATC_EVENT_CCHO:
            return sizeof(ST_ATC_CCHO_PARAMETER);
        case D_ATC_EVENT_CGLA:
            return offsetof(ST_ATC_CGLA_PARAMETER, aucCommand) + pCmdEvent->stCglaParam.usLength;
        case D_ATC_EVENT_CRSM:
            return offsetof(ST_ATC_CRSM_PARAMETER, pucData) + pCmdEvent->stCrsmParam.ucDataLen;
        case D_ATC_EVENT_CGEREP:
            return sizeof(ST_ATC_CGEREP_PARAMETER);
        case D_ATC_EVENT_CCIOTOPT:
            return sizeof(ST_ATC_CCIOTOPT_PARAMETER);
        case D_ATC_EVENT_CGEQOSRDP:
            return sizeof(ST_ATC_CGEQOSRDP_PARAMETER);
        case D_ATC_EVENT_CTZR:
            return sizeof(ST_ATC_CTZR_PARAMETER);
        case D_ATC_EVENT_CGCONTRDP:
            return sizeof(ST_ATC_CGCONTRDP_PARAMETER);
        case D_ATC_EVENT_CPIN:
            return sizeof(ST_ATC_CPIN_PARAMETER);
        case D_ATC_EVENT_CLCK:
            return sizeof(ST_ATC_CLCK_PARAMETER);
        case D_ATC_EVENT_CPWD:
            return sizeof(ST_ATC_CPWD_PARAMETER);
#if USR_CUSTOM2
        case D_ATC_EVENT_NUESTATS:
#endif
        case D_ATC_EVENT_MUESTATS:
            return sizeof(ST_ATC_NUESTATS_PARAMETER);
        case D_ATC_EVENT_NEARFCN:
            return sizeof(ST_ATC_NEARFCN_PARAMETER);
        case D_ATC_EVENT_NBAND:
            return sizeof(ST_ATC_NBAND_PARAMETER);
        case D_ATC_EVENT_NCONFIG:
            return sizeof(ST_ATC_NCONFIG_PARAMETER);
        case D_ATC_EVENT_NL2THP:
            return sizeof(ST_ATC_NL2THP_PARAMETER);
        case D_ATC_EVENT_NSET:
        case D_ATC_EVENT_NSET_R:
            return sizeof(ST_ATC_NSET_PARAMETER);
#ifdef LCS_MOLR_ENABLE
        case D_ATC_EVENT_CMOLR:
            return sizeof(ST_ATC_CMOLR_PARAMETER); 
#endif
        case D_ATC_EVENT_CIPCA:
            return sizeof(ST_ATC_CIPCA_PARAMETER);
        case D_ATC_EVENT_CGAUTH:
            return sizeof(ST_ATC_CGAUTH_PARAMETER);
        case D_ATC_EVENT_CPINR:
            return sizeof(ST_ATC_CPINR_PARAMETER);
        case D_ATC_EVENT_NPOWERCLASS:
            return sizeof(ST_ATC_NPOWERCLASS_PARAMETER);
        case D_ATC_EVENT_NPTWEDRXS:
            return sizeof(ST_ATC_CEDRXS_PARAMETER);
        // case D_ATC_EVENT_NPIN:
        //     return sizeof(ST_ATC_NPIN_PARAMETER);
        case D_ATC_EVENT_NTSETID:
            return sizeof(ST_ATC_NTSETID_PARAMETER);
#if !defined(_FLASH_OPTIMIZE_) || (USR_CUSTOM2)
        case D_ATC_EVENT_NCIDSTATUS:
            return sizeof(ST_ATC_NCIDSTATUS_PARAMETER);
#endif
        case D_ATC_EVENT_NGACTR:
            return sizeof(ST_ATC_NGACTR_PARAMETER);
        case D_ATC_EVENT_NPOPB:
            return sizeof(ST_ATC_NPOPB_PARAMETER);
        // case D_ATC_EVENT_NIPINFO:
        //     return sizeof(ST_ATC_NIPINFO_PARAMETER);
        case D_ATC_EVENT_NQPODCP:
            return sizeof(ST_ATC_NQPODCP_PARAMETER);
        case D_ATC_EVENT_NSNPD:
            return offsetof(ST_ATC_NSNPD_PARAMETER, pucNonIpData) + pCmdEvent->stNsnpdParam.usNonIpDataLen;
        case D_ATC_EVENT_NQPNPD:
            return sizeof(ST_ATC_NQPNPD_PARAMETER);
        case D_ATC_EVENT_NRNPDM:
            return sizeof(ST_ATC_NRNPDM_PARAMETER);
        case D_ATC_EVENT_NCPCDPR:
            return sizeof(ST_ATC_NCPCDPR_PARAMETER);
        case D_ATC_EVENT_CGPIAF:
            return sizeof(ST_ATC_CGPIAF_PARAMETER);
        case D_ATC_EVENT_NLOCKF:
            return sizeof(ST_ATC_NLOCKF_PARAMETER);
#ifdef CSG_FEATURE
        case D_ATC_EVENT_NCSG:
            return sizeof(ST_ATC_NCSG_PARAMETER);
#endif
        case D_ATC_EVENT_CACDC:
            return sizeof(ST_ATC_CACDC_PARAMETER);
        case D_ATC_EVENT_PINGXY:
            return sizeof(ST_ATC_PINGXY_PARAMETER);
        case D_ATC_EVENT_PSTEST:
            return offsetof(ST_ATC_PSTEST_PARAMETER, pucData) + pCmdEvent->stPstestParam.usDataLen;
        case D_ATC_EVENT_QICSGP:
        case D_ATC_EVENT_QICSGP_R:
            return sizeof(ST_ATC_QICSGP_PARAMETER);
        case D_ATC_EVENT_QIACT:
        case D_ATC_EVENT_QIDEACT:
            return sizeof(ST_ATC_QIACT_PARAMETER);
        case D_ATC_EVENT_QENG:
            return sizeof(ST_ATC_QENG_PARAMETER);
        case D_ATC_EVENT_GSN:
            return sizeof(ST_ATC_GSN_PARAMETER);
        case D_ATC_EVENT_QSIMDET:
            return sizeof(ST_ATC_QSIMDET_PARAMETER);
        case D_ATC_EVENT_CPOL:
            return sizeof(ST_ATC_CPOL_PARAMETER);
        case D_ATC_EVENT_CSCS:
            return sizeof(ST_ATC_CSCS_PARAMETER);
        case D_ATC_EVENT_QPINC:
            return sizeof(ST_ATC_QPINC_PARAMETER);
        case D_ATC_EVENT_NITZ:
        case D_ATC_EVENT_CTZU:
            return sizeof(ST_ATC_NITZ_PARAMETER);
        case D_ATC_EVENT_MLOCKFREQ:
            return sizeof(ST_ATC_MLOCKFREQ_PARAMETER);
        case D_ATC_EVENT_MUECONFIG:
        case D_ATC_EVENT_MUECONFIG_R:
            return sizeof(ST_ATC_MUECONFIG_PARAMETER);
        case D_ATC_EVENT_MWIFISCANCFG:
        case D_ATC_EVENT_MWIFISCANCFG_R:
            return sizeof(ST_ATC_MWIFISCANCFG_PARAMETER);
        case D_ATC_EVENT_MWIFISCANSTART:
            return sizeof(ST_ATC_MWIFISCANSTART_PARAMETER);
        case D_ATC_EVENT_QBLACKCELL:
            return sizeof(ST_ATC_QBLACKCELL_PARAMETER);
        case D_ATC_EVENT_PCTTESTINFO:
            return sizeof(ST_ATC_AP_APP_INTERFACE_INFO);
        case D_ATC_EVENT_NPREEARFCN:
        case D_ATC_EVENT_NPREEARFCN_R:
            return sizeof(ST_ATC_NPREEARFCN_PARAMETER);
        case D_ATC_EVENT_QSCLKEX:
            return sizeof(ST_ATC_QSCLKEX_PARAMETER);
        case D_ATC_EVENT_SIMUUICC:
        case D_ATC_EVENT_SIMUUICC_R:
            return sizeof(ST_ATC_SIMUUICC_PARAMETER);
        case D_ATC_EVENT_QWIFISCAN:
            return sizeof(ST_ATC_QWIFISCAN_PARAMETER);
        case D_ATC_EVENT_CCED:
            return sizeof(ST_ATC_CCED_PARAMETER);
        case D_ATC_EVENT_QIACTEX:
            return sizeof(ST_ATC_QIACTEX_PARAMETER);
        case D_ATC_EVENT_QIDEACTEX:
            return sizeof(ST_ATC_QIDEACTEX_PARAMETER);
        case D_ATC_EVENT_QINDCFG:
            return sizeof(ST_ATC_QINDCFG_PARAMETER);
        case D_ATC_EVENT_CIDACT:
            return sizeof(ST_ATC_CIDACT_PARAMETER);
        case D_ATC_EVENT_QCFG:
        case D_ATC_EVENT_QCFG_R:
            return sizeof(ST_ATC_QCFG_PARAMETER);
        case D_ATC_EVENT_QEHPLMN:
            return sizeof(ST_ATC_QEHPLMN_PARAMETER);
        default:
            return sizeof(ST_ATC_CMD_PARAMETER);
    }
}

static void  AtcAp_Encode_ArrayOrPointer(unsigned char* pbDesc, unsigned short usDateLen, unsigned char ucSrc1Size, unsigned char* pSrc1, unsigned char* pSrc2)
{
    if(0 == usDateLen)
    {
        return;
    }

    if(usDateLen <= ucSrc1Size)
    {
        AtcAp_MemCpy(pbDesc, pSrc1, usDateLen);
    }
    else
    {
        AtcAp_MemCpy(pbDesc, pSrc2, usDateLen);
    }
}

void AtcAp_Encode_UN_ATC_CMD_EVENT(UN_ATC_CMD_EVENT *pCmdEvent, unsigned char** ppCodeStream, unsigned short* pusLen)
{
    unsigned short         usLen        = 0;
    unsigned char         *pCodeStream;
    unsigned short         usOffset;

    usLen       = AtcAp_GetCmdEventBuffSize(pCmdEvent);
    
    pCodeStream = (unsigned char *)AtcAp_Malloc(usLen);
    switch(pCmdEvent->stCgdcontParam.usEvent)
    {
        case D_ATC_EVENT_CGDCONT:
            usOffset = offsetof(ST_ATC_CGDCONT_PARAMETER, aucApnValue);
            AtcAp_MemCpy(pCodeStream, pCmdEvent, usOffset);

            AtcAp_Encode_ArrayOrPointer(pCodeStream + usOffset, 
                                        pCmdEvent->stCgdcontParam.ucApnLen,
                                        sizeof(pCmdEvent->stCgdcontParam.aucApnValue),
                                        pCmdEvent->stCgdcontParam.aucApnValue,
                                        pCmdEvent->stCgdcontParam.pucApnValue);
            break;
#ifndef _FLASH_OPTIMIZE_
        case D_ATC_EVENT_CSODCP:
            usOffset = offsetof(ST_ATC_CSODCP_PARAMETER, usCpdataLength) + sizeof(unsigned short);
            AtcAp_MemCpy(pCodeStream, pCmdEvent, usOffset);
            if(0 != pCmdEvent->stCsodcpParam.usCpdataLength)
            {
                AtcAp_MemCpy(pCodeStream + usOffset, pCmdEvent->stCsodcpParam.pucCpdata, pCmdEvent->stCsodcpParam.usCpdataLength);
            }
            break;
#endif
        case D_ATC_EVENT_CSIM:
            usOffset = offsetof(ST_ATC_CSIM_PARAMETER, aucCommand);
            AtcAp_MemCpy(pCodeStream, pCmdEvent, usOffset);

            AtcAp_Encode_ArrayOrPointer(pCodeStream + usOffset, 
                                        pCmdEvent->stCsimParam.usLength,
                                        sizeof(pCmdEvent->stCsimParam.aucCommand),
                                        pCmdEvent->stCsimParam.aucCommand,
                                        pCmdEvent->stCsimParam.pucCommand);
            break;
        case D_ATC_EVENT_CGLA:
            usOffset = offsetof(ST_ATC_CGLA_PARAMETER, aucCommand);
            AtcAp_MemCpy(pCodeStream, pCmdEvent, usOffset);

            AtcAp_Encode_ArrayOrPointer(pCodeStream + usOffset, 
                                        pCmdEvent->stCglaParam.usLength,
                                        sizeof(pCmdEvent->stCglaParam.aucCommand),
                                        pCmdEvent->stCglaParam.aucCommand,
                                        pCmdEvent->stCglaParam.pucCommand);
            break;
        case D_ATC_EVENT_CRSM:
            usOffset = offsetof(ST_ATC_CRSM_PARAMETER, ucDataLen) + sizeof(unsigned char);
            AtcAp_MemCpy(pCodeStream, pCmdEvent, usOffset);
            if(0 != pCmdEvent->stCrsmParam.ucDataLen)
            {
                AtcAp_MemCpy(pCodeStream + usOffset, pCmdEvent->stCrsmParam.pucData, pCmdEvent->stCrsmParam.ucDataLen);
            }
            break;
        case D_ATC_EVENT_NSNPD:
            usOffset = offsetof(ST_ATC_NSNPD_PARAMETER, pucNonIpData);
            AtcAp_MemCpy(pCodeStream, pCmdEvent, usOffset);
            if(0 != pCmdEvent->stNsnpdParam.usNonIpDataLen)
            {
                AtcAp_MemCpy(pCodeStream + usOffset, pCmdEvent->stNsnpdParam.pucNonIpData, pCmdEvent->stNsnpdParam.usNonIpDataLen);
            }
            break;
#ifdef LTE_SMS_FEATURE
        case D_ATC_AP_SMS_PDU_REQ:
            usOffset = offsetof(ST_ATC_AP_SMS_PDU_PARAMETER, pucPduData);
            AtcAp_MemCpy(pCodeStream, pCmdEvent, usOffset);
            if(0 != pCmdEvent->stAtcApPduParam.pucPduData)
            {
                AtcAp_MemCpy(pCodeStream + usOffset, pCmdEvent->stAtcApPduParam.pucPduData, pCmdEvent->stAtcApPduParam.usPduLength);
            }
            break;
        case D_ATC_AP_SMS_TEXT_REQ:
            usOffset = offsetof(ST_ATC_AP_SMS_TEXT_PARAMETER, pucTextData);
            AtcAp_MemCpy(pCodeStream, pCmdEvent, usOffset);
            if(0 != pCmdEvent->stAtcApTextParam.pucTextData)
            {
                AtcAp_MemCpy(pCodeStream + usOffset, pCmdEvent->stAtcApTextParam.pucTextData, pCmdEvent->stAtcApTextParam.usTextLength);
            }
            break;
        case D_ATC_EVENT_CMGW:
        case D_ATC_EVENT_CMGS:
        case D_ATC_EVENT_CMGC:
        case D_ATC_EVENT_CNMA:
            if(g_SmsFormatMode == 0)
            {
                usOffset = offsetof(ST_ATC_PDU_TPDU_PARAMETER, aucPduData);
            }
            else
            {
                usOffset = offsetof(ST_ATC_TEXT_PARAMETER, pucTextData);
            }
            AtcAp_MemCpy(pCodeStream, pCmdEvent, usOffset);
            break;
        case D_ATC_EVENT_QCMGS:
            usOffset = offsetof(ST_ATC_QCMGS_PARAMETER, pucTextData);
            AtcAp_MemCpy(pCodeStream, pCmdEvent, usOffset);
            if(0 != pCmdEvent->stQcmgsParam.pucTextData)
            {
                AtcAp_MemCpy(pCodeStream + usOffset, (unsigned char*)pCmdEvent->stQcmgsParam.pucTextData, pCmdEvent->stQcmgsParam.usTextLength);
            }
            break;
#endif
        case D_ATC_EVENT_PSTEST:
            usOffset = offsetof(ST_ATC_PSTEST_PARAMETER, pucData);
            AtcAp_MemCpy(pCodeStream, pCmdEvent, usOffset);
            if(0 != pCmdEvent->stPstestParam.usDataLen)
            {
                AtcAp_MemCpy(pCodeStream + usOffset, pCmdEvent->stPstestParam.pucData, pCmdEvent->stPstestParam.usDataLen);
            }
            break;
        default:
            AtcAp_MemCpy(pCodeStream, pCmdEvent, usLen);
            break;
    }

    *pusLen = usLen;
    *ppCodeStream = pCodeStream;
}

/*******************************************************************************
  MODULE    : AtcAp_Strncmp
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  Dep2_066   2016.12.20   create
*******************************************************************************/
int AtcAp_Strncmp(unsigned char *pStr1, unsigned char *pStr2)
{
    return strcmp((char*)pStr1, (char*)pStr2);
}

/*******************************************************************************
  MODULE    : ATC_DecConv
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  Dep2_066   2016.12.20   create
*******************************************************************************/
static void  AtcAp_DecConv(ST_ATC_AP_FORMAT *FCTable, unsigned int IntData)
{
    unsigned char  i;
    unsigned char  DispData[20] = { 0 };
    unsigned char  Length = 0;

    if (0 == IntData)
    {
        DispData[Length++] = 0x30;
    }
    else
    {
        while(IntData)
        {
            DispData[Length++] = (unsigned char)(IntData % 10 + 0x30);
            IntData = IntData / 10;
        };
    }

    for(i=0;i!=Length;i++){
        *FCTable->EditBufRef++ = DispData[(Length-i)-1];
        FCTable->EditCount++;
    }

    *FCTable->EditBufRef = 0;
}

/*******************************************************************************
  MODULE    : ATC_HexConv
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  Dep2_066   2016.12.20   create
*******************************************************************************/
static void  AtcAp_HexConv(ST_ATC_AP_FORMAT *FCTable, unsigned int HexData)
{
    unsigned char  DispData[20];
    unsigned short  Length = 0;
    unsigned short  i;
    const unsigned char LargeTable[16] = {'0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'};

    for (;;)
    {
        DispData[Length++] = LargeTable[HexData & 0x0F];
        HexData = HexData >> 4;
        if (0 == HexData)
        {
            break;
        }
 
    }
    if (1 == (Length % 2))
    {
        DispData[Length++] = 0x30;
    }

    for(i = 0;i != Length;i++)
    {
        *FCTable->EditBufRef++ = DispData[(Length - i) - 1];
        FCTable->EditCount++;
    }
}

/*******************************************************************************
  MODULE    : ATC_FixedLenHexConv
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  GCM   2018.12.12   create
*******************************************************************************/
static void  AtcAp_FixedLenHexConv(ST_ATC_AP_FORMAT *FCTable, unsigned int HexData, unsigned char ucHexStrLen)
{
    unsigned char  DispData[8];
    unsigned short  Length = 0;
    unsigned short  i;
    const unsigned char LargeTable[16] = {'0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'};
    if(ucHexStrLen == 0)
    {
        if(HexData != 0)
        {
            i = 28;
            ucHexStrLen = 8;
            while(i != 0 && HexData>>i == 0)
            {
                i-=4;
                ucHexStrLen--;
            }            
        }
        else
        {
            ucHexStrLen = 1;
        }
    }

    for(Length = 0; Length < ucHexStrLen; Length++)
    {
        DispData[Length] = LargeTable[HexData & 0x0F];
        HexData = HexData >> 4;
    }

    for(i = 0;i != Length;i++)
    {
        *FCTable->EditBufRef++ = DispData[(Length - i) - 1];
        FCTable->EditCount++;
    }    
}

unsigned short AtcAp_StrPrintf(unsigned char *EditBuffer, const unsigned char *FormatBuffer,...)
{
    va_list         ap;
    unsigned char           *StringDataPoint;
     signed int             IntData;
    unsigned int            uIntData;
    unsigned char i;
    ST_ATC_AP_FORMAT      FCTable;

    va_start(ap,FormatBuffer);          
    FCTable.FormatRef = (unsigned char *)FormatBuffer;
    FCTable.EditBufRef = (unsigned char *)EditBuffer;
    FCTable.EditCount = 0;
    *FCTable.EditBufRef = 0;

    while(*FCTable.FormatRef){
        if (*FCTable.FormatRef == '%')
        {
           FCTable.FormatRef++;
            switch(*FCTable.FormatRef++)
            {
                case 'c':               
                    *FCTable.EditBufRef++ = (unsigned char)va_arg(ap,  signed int);      
                    *FCTable.EditBufRef = 0;
                    FCTable.EditCount++;
                    break;
                case 's':               
                    StringDataPoint = (unsigned char *)va_arg(ap, unsigned char *);    
                    for (i = 0; ;i++)
                    {
                        if (0x00 == *StringDataPoint)
                        {
                            break;
                        }
                        else
                        {
                            *FCTable.EditBufRef++ = *StringDataPoint++;
                            FCTable.EditCount++;
                        }
                    }
                    *FCTable.EditBufRef = 0;
                    break;
                case 'd':               
                    IntData = va_arg(ap,  signed int);
                    AtcAp_DecConv(&FCTable, (unsigned int)IntData);
                    break;
                case 'x':               
                    StringDataPoint = (unsigned char *)va_arg(ap,unsigned char *);
                    for (i = 0; i < g_AtcApInfo.stAtRspInfo.ucHexStrLen; i++)
                    {
                        AtcAp_HexConv(&FCTable, *StringDataPoint);

                        StringDataPoint++;
                    }
                    *FCTable.EditBufRef = 0;
                    break;
                case 'h':               
                    uIntData = va_arg(ap,  signed int);
                    AtcAp_FixedLenHexConv(&FCTable, uIntData, g_AtcApInfo.stAtRspInfo.ucHexStrLen);
                    g_AtcApInfo.stAtRspInfo.ucHexStrLen = 0;
                    *FCTable.EditBufRef = 0;
                    break;
                default:
                    break;
            }
        }
        else{
            *FCTable.EditBufRef++ = *FCTable.FormatRef++;
            *FCTable.EditBufRef = 0;
            FCTable.EditCount++;
        }
    }
    va_end(ap);

    return(FCTable.EditCount);
}


void AtcAp_WriteHexPara_M(unsigned int uiFlg, unsigned int uiPara, unsigned char* pRespBuff, unsigned char ucHexLen)
{
    g_AtcApInfo.stAtRspInfo.ucHexStrLen = ucHexLen;
    if (uiFlg)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pRespBuff + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)",%h",
            uiPara);
    }
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pRespBuff + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)",");
    }
    g_AtcApInfo.stAtRspInfo.ucHexStrLen = 0;
    return;
}

/*******************************************************************************
  MODULE    : AtcAp_WriteStrPara_M
  FUNCTION  :
  NOTE      :
  HISTORY   :
      1.   JiangNa   2018.07.17   create
*******************************************************************************/
void AtcAp_WriteStrPara_M(unsigned int uiFlg, unsigned char *pucPara )
{
    if (NULL == pucPara)
    {
        return;
    }
    if (0 != uiFlg)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)",\"%s\"",pucPara);
    }
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)",");
    }
    return;
}

/*******************************************************************************
  MODULE    : AtcAp_WriteIntPara_M
  FUNCTION  :
  NOTE      :
  HISTORY   :
      1.   JiangNa   2018.07.17   create
*******************************************************************************/
void AtcAp_WriteIntPara_M(unsigned int uiFlg, unsigned int uiPara, unsigned char *pucAtcRspBuf)
{
    if (uiFlg)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)",%d",
            uiPara);
    }
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)",");
    }
    return;
}

/*******************************************************************************
  MODULE    : AtcAp_WriteIntPara
  FUNCTION  :
  NOTE      :
  HISTORY   :
      1.   JiangNa   2018.07.17   create
*******************************************************************************/
void AtcAp_WriteIntPara(unsigned int uiFlg, unsigned int uiPara )
{
    if (uiFlg)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)",%d",
            uiPara);
    }
    return;
}

/*******************************************************************************
  MODULE    : ATC_Write4BitData
  FUNCTION  :
  NOTE      :
  HISTORY   :
      1.   tianchengbin   2018.11.26   create
*******************************************************************************/
void AtcAp_Write4BitData(unsigned char ucData)
{
    unsigned char i;
    unsigned char aucStr[5] = {0};
    for (i = 0; i < 4; i++)
    {
        aucStr[i] = ((ucData >> (3-i))&0x01) + 0x30;
    }
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
        (const unsigned char *)",\"%s\"", aucStr);
}


/*******************************************************************************
  MODULE    : AtcAp_OutputAddr
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  JiangNa    2018/12/18   create
*******************************************************************************/
void AtcAp_OutputAddr(unsigned char ucDataLen, unsigned char *pData, unsigned char *pucAtcRspBuf)
{   
    if(4 == ucDataLen)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)",\"%d.%d.%d.%d\"",
            pData[0], pData[1], 
            pData[2], pData[3]);
    }
    else if (8 == ucDataLen)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)",\"%d.%d.%d.%d.%d.%d.%d.%d\"",
            pData[0], pData[1], 
            pData[2], pData[3], pData[4],
            pData[5], pData[6], pData[7]);
    }
    else if(16 == ucDataLen && g_softap_fac_nv->ucIpv6AddressFormat == 0)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)",\"%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d\"",
            pData[0], pData[1], pData[2], pData[3], pData[4], pData[5], pData[6], pData[7], 
            pData[8], pData[9], pData[10], pData[11], pData[12], pData[13], pData[14], pData[15]);
    }
    else if (32 == ucDataLen && g_softap_fac_nv->ucIpv6AddressFormat == 0)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)",\"%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d\"",
            pData[0], pData[1], pData[2], pData[3], pData[4], pData[5], pData[6], pData[7], 
            pData[8], pData[9], pData[10], pData[11], pData[12], pData[13], pData[14], pData[15], pData[16], 
            pData[17], pData[18], pData[19], pData[20], pData[21], pData[22], pData[23], pData[24], pData[25],
            pData[26], pData[27], pData[28], pData[29], pData[30], pData[31]);
    }
    else if(ucDataLen >=16 && g_softap_fac_nv->ucIpv6AddressFormat == 1)
    {
        AtcAp_OutputAddr_IPv6(ucDataLen, pData, pucAtcRspBuf);
    }
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)",\"\"");
    }
    return;
}

void AtcAp_IPV6_ZeroLead(unsigned short parameter, unsigned char *pucAtcRspBuf)
{
    if(g_softap_fac_nv->ucIpv6LeadingZeros == 1)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += sprintf((char *)pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const char *)"%04X", parameter);
    }
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += sprintf((char *)pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const char *)"%X", parameter);
    }
    return;
}

void AtcAp_IPV6_ZeroCompress(unsigned short *pusIpv6Addr, unsigned char *pucAtcRspBuf)
{
    int     i = 0;
    unsigned char ucZeroFlag = 0;
    unsigned char ucPoint = 0;
    unsigned char ucCount = 0;

    if(g_softap_fac_nv->ucIpv6CompressZeros == 1)
    {
        for(i = 0; i < 8; i++)
        {
            if(pusIpv6Addr[i] == 0 && ucCount != 1)
            {
                if((pusIpv6Addr[i + 1] == 0 || (i+1) == 8) )
                {
                    ucZeroFlag++;
                }
                
                if(ucZeroFlag != 0 && ucCount != 1)
                {
                    if((ucZeroFlag - ucPoint) != 0)
                    {
                        ucPoint = ucZeroFlag;
                        if(i == 0 || i == 7)
                        {
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                                (const unsigned char *)":");
                        }
                        continue;
                    }
                    else
                    {
                        ucCount = 1;
                    }
                }
            }
            else
            {
                AtcAp_IPV6_ZeroLead(pusIpv6Addr[i], pucAtcRspBuf);
            }
            
            if(i == 7)
            {
                return;
            }
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                (const unsigned char *)":");
        }
    }
    else
    {
        for(i = 0; i < 8; i++)
        {
            AtcAp_IPV6_ZeroLead(pusIpv6Addr[i], pucAtcRspBuf);
            if(i == 7)
            {
                return;
            }
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                (const unsigned char *)":");
        }
    }
}

void AtcAp_IPV6_SubnetNotation(unsigned short *pusIpv6AddrMask, unsigned char *pucAtcRspBuf)
{
    unsigned short*  pusIpv6Mask = pusIpv6AddrMask;
    unsigned char MaskCidr = 0;
    unsigned char i = 0,j = 0;

    if(g_softap_fac_nv->ucIpv6SubnetNotation == 1)
    {
        for(i = 0; i < 8; i++)
        {
            for(j = 0; j < 16; j++)
            {
                if(pusIpv6Mask[i] & (0x8000 >>j))
                {
                    MaskCidr++;
                }
                else
                {
                    break;
                }
            }
        }
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
                (const unsigned char *)"/%d",MaskCidr);
    }
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
                (const unsigned char *)" ");
        AtcAp_IPV6_ZeroCompress(pusIpv6Mask, pucAtcRspBuf);
    }
}

void AtcAp_OutputAddr_IPv6(unsigned char ucDataLen, unsigned char *pData, unsigned char *pucAtcRspBuf)
{
    int     i = 0,j = 0;
    unsigned short    pusIpv6Addr[16] = {0};
    
    for(i = 0; i < (ucDataLen / 2); i++)
    {
        pusIpv6Addr[j++] = pData[2*i] << 8 | pData[2*i+1];
    }


    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
            (const unsigned char *)",\"");
  
    AtcAp_IPV6_ZeroCompress(pusIpv6Addr, pucAtcRspBuf);
    if(ucDataLen == 32 )
    {
        AtcAp_IPV6_SubnetNotation(pusIpv6Addr + 8, pucAtcRspBuf);
    }
       
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)"\"");

    return;
}

void AtcAp_OutputAddr_IPv6ColonFormat(unsigned char *pData, unsigned char *pucAtcRspBuf)
{

    int     i = 0,j = 0;
    unsigned short    pusIpv6Addr[8] = {0};
    
    for(i = 0; i < 8; i++)
    {
        pusIpv6Addr[j++] = pData[2*i] << 8 | pData[2*i+1];
    }

    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
            (const unsigned char *)",\"");
    
    AtcAp_IPV6_ZeroCompress(pusIpv6Addr, pucAtcRspBuf);
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
            (const unsigned char *)"\"");
}

/*******************************************************************************
  MODULE    : ATC_IntegerToPlmn
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  JiangNa   2018.09.28   create
*******************************************************************************/
void AtcAp_IntegerToPlmn(unsigned long ulInputData, unsigned char *pOutputData)
{
    if(ulInputData == 0xFFFFFFFF)
    {
        return;
    }
    
    if (NULL != pOutputData)
    {
        pOutputData[0] = (unsigned char)(((ulInputData) >> 16) & 0x0F) + 0x30;
        pOutputData[1] = (unsigned char)(((ulInputData) >> 20) & 0x0F) + 0x30;
        pOutputData[2] = (unsigned char)(((ulInputData) >> 8) & 0x0F) + 0x30;
        pOutputData[3] = (unsigned char)((ulInputData) & 0x0F) + 0x30;
        pOutputData[4] = (unsigned char)(((ulInputData) >> 4) & 0x0F) + 0x30;
        if (0x0F != (((ulInputData) >> 12) & 0x0F))
        {
            pOutputData[5] = (((ulInputData) >> 12) & 0x0F) + 0x30;
        }
    }

    return;
}

void AtcAp_OutputPortRange(unsigned char ucDataLen, unsigned short *pData, unsigned char* pRespBuff)
{
    if (0 != ucDataLen)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)",\"%d.%d\"", pData[0], pData[1]);
    }
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)",\"\"");
    }
}

static void AtcAp_GetIpv4SubnetMask(unsigned char ucFirstAddr, unsigned char* pbSubnetMask)
{
    AtcAp_MemSet(pbSubnetMask, 0, 4);
    //Class A
    if(1 <= ucFirstAddr && ucFirstAddr <= 126)
    {
        pbSubnetMask[0] = 255;
    }
    //Class B
    else if(128 <= ucFirstAddr && ucFirstAddr <= 191)
    {
        pbSubnetMask[0] = 255;
        pbSubnetMask[1] = 255; 
    }
    //Class C
    else if(192 <= ucFirstAddr && ucFirstAddr <= 223)
    {
        pbSubnetMask[0] = 255;
        pbSubnetMask[1] = 255;
        pbSubnetMask[2] = 255;
    }
    else
    {
        pbSubnetMask[0] = 255;
        pbSubnetMask[1] = 255;
        pbSubnetMask[2] = 255;
        pbSubnetMask[3] = 255;
    }   
}

static void AtcAp_OutputIpAddrAndSubMask(unsigned char ucCid,unsigned char ucPdpType, unsigned char* pucIpAddr, unsigned char *pucAtcRspBuf)
{
    unsigned char ucIpAddrAndSubMaskLen  = 0;
    unsigned char ucIpAddrAndSubMask[32] = { 0 };
    unsigned char ucIpv6addr_all[16];

    if(ucPdpType == D_PDP_TYPE_IPV4)
    {
        AtcAp_MemCpy(ucIpAddrAndSubMask, pucIpAddr, 4);
#if (VER_QUEC|VER_CM)
        ucIpAddrAndSubMaskLen = 4;
#else
        AtcAp_GetIpv4SubnetMask(ucIpAddrAndSubMask[0], ucIpAddrAndSubMask + 4);
        ucIpAddrAndSubMaskLen = 8;
#endif
    }
    else //IPv6
    {
#if !(VER_CM)
        if(ATC_GET_IPV6ADDR_ALL(ucCid,ucIpv6addr_all) == ATC_AP_TRUE)
        {
            AtcAp_MemCpy(ucIpAddrAndSubMask, ucIpv6addr_all, 16);
        }
        else
#endif
        {
            ucIpAddrAndSubMask[0] = 0xFE;
            ucIpAddrAndSubMask[1] = 0x80;
            AtcAp_MemCpy(ucIpAddrAndSubMask + 8, pucIpAddr, 8);
        }
#if (VER_CM)
        ucIpAddrAndSubMaskLen = 16;
#else
        ucIpAddrAndSubMaskLen = 32;
#endif
    }

    AtcAp_OutputAddr(ucIpAddrAndSubMaskLen, ucIpAddrAndSubMask, pucAtcRspBuf);

}

void AtcAp_CGCONTRDP_Print(EPS_CGCONTRDP_DYNAMIC_INFO *ptPdpDynamicInfo, unsigned char bPdpType, unsigned char *pucAtcRspBuf)
{
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
        (const unsigned char *)"\r\n+CGCONTRDP:%d,%d", ptPdpDynamicInfo->ucCid, ptPdpDynamicInfo->ucBearerId);

    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
        (const unsigned char *)",\"%s\"", ptPdpDynamicInfo->aucApn);

    //IpAddrAndSubMask
    if(D_PDP_TYPE_IPV4 == bPdpType)
    {
        AtcAp_OutputIpAddrAndSubMask(ptPdpDynamicInfo->ucCid, bPdpType, ptPdpDynamicInfo->aucPdpAddrValue + 8, pucAtcRspBuf);
    }
    else if(D_PDP_TYPE_IPV6 == bPdpType)
    {
        AtcAp_OutputIpAddrAndSubMask(ptPdpDynamicInfo->ucCid, bPdpType, ptPdpDynamicInfo->aucPdpAddrValue, pucAtcRspBuf);
    }
    else
    {
        AtcAp_OutputAddr(0, NULL, pucAtcRspBuf);
    }

    AtcAp_OutputAddr(0, NULL, pucAtcRspBuf); //GwAddr

    if(D_PDP_TYPE_IPV4 == bPdpType)
    {
        if(ptPdpDynamicInfo->stDnsAddr.ucPriDnsAddr_IPv4Flg)
        {
            AtcAp_OutputAddr(4, ptPdpDynamicInfo->stDnsAddr.ucPriDnsAddr_IPv4, pucAtcRspBuf);
        }
        else
        {
            AtcAp_OutputAddr(0, NULL, pucAtcRspBuf);
        }

        if(ptPdpDynamicInfo->stDnsAddr.ucSecDnsAddr_IPv4Flg)
        {
            AtcAp_OutputAddr(4, ptPdpDynamicInfo->stDnsAddr.ucSecDnsAddr_IPv4, pucAtcRspBuf);
        }
        else
        {
            AtcAp_OutputAddr(0, NULL, pucAtcRspBuf);
        }
    }
    else if(D_PDP_TYPE_IPV6 == bPdpType)
    {
        if(ptPdpDynamicInfo->stDnsAddr.ucPriDnsAddr_IPv6Flg)
        {
            AtcAp_OutputAddr(16, ptPdpDynamicInfo->stDnsAddr.ucPriDnsAddr_IPv6, pucAtcRspBuf);
        }
        else
        {
            AtcAp_OutputAddr(0, NULL, pucAtcRspBuf);
        }

        if(ptPdpDynamicInfo->stDnsAddr.ucSecDnsAddr_IPv6Flg)
        {
            AtcAp_OutputAddr(16, ptPdpDynamicInfo->stDnsAddr.ucSecDnsAddr_IPv6, pucAtcRspBuf);
        }
        else
        {
            AtcAp_OutputAddr(0, NULL, pucAtcRspBuf);
        }
    }
    else //Non-IP
    {
        AtcAp_OutputAddr(0, NULL, pucAtcRspBuf); //PriDnsAddr
        AtcAp_OutputAddr(0, NULL, pucAtcRspBuf); //SecDnsAddr
    }

    AtcAp_OutputAddr(0, NULL, pucAtcRspBuf); //PCSCFPrimAddr
    AtcAp_OutputAddr(0, NULL, pucAtcRspBuf); //PCSCFSecAddr
    AtcAp_WriteIntPara_M(ATC_AP_TRUE, 0, pucAtcRspBuf); //IMCNSignalling
    AtcAp_WriteIntPara_M(ATC_AP_TRUE, 0, pucAtcRspBuf); //LIPAInd

#if (VER_QUEC|VER_CM)
    return;
#endif
    if(D_PDP_TYPE_IPV4 == bPdpType)
    {
        AtcAp_WriteIntPara_M(ptPdpDynamicInfo->ucIPv4MTUFlag, ptPdpDynamicInfo->usIPv4MTU, pucAtcRspBuf);
    }
    else
    {
        AtcAp_WriteIntPara_M(ATC_AP_FALSE, 0, pucAtcRspBuf);
    }  
    
    AtcAp_WriteIntPara_M(ATC_AP_FALSE, 0, pucAtcRspBuf); //WLANOffload
    AtcAp_WriteIntPara_M(ATC_AP_FALSE, 0, pucAtcRspBuf); //LocalAddrInd

    if(D_PDP_TYPE_NonIP == bPdpType)
    {
        AtcAp_WriteIntPara_M(ptPdpDynamicInfo->ucNonIPMTUFlag, ptPdpDynamicInfo->usNonIPMTU, pucAtcRspBuf);
    }
    else
    {
        AtcAp_WriteIntPara_M(ATC_AP_FALSE, 0, pucAtcRspBuf);
    }

    AtcAp_WriteIntPara_M(ptPdpDynamicInfo->ucServingPLMNRateCtrValueFlag, ptPdpDynamicInfo->usServingPLMNRateCtrValue, pucAtcRspBuf);
}

void AtcAp_CSCA_ConvertScaByte2Str(unsigned char* pucScaData, unsigned char ucScaLen, unsigned char* pScaStr)
{
    unsigned char  i = 0;
    unsigned char  aucScaData[D_ATC_P_CSCA_IND_SCA_SIZE_MAX + 2] = { 0 };

    for (i = 0; i < ucScaLen; i++)
    {
        aucScaData[2 * i]     = (unsigned char)(pucScaData[i] & 0x0F);
        aucScaData[2 * i + 1] = (unsigned char)((pucScaData[i] & 0xF0) >> 4);
    }

    for(i = 0; i < ucScaLen * 2; i++)
    {
        if (10 == aucScaData[i])
        {
            pScaStr[i] = '*';
        }
        else if (11 == aucScaData[i])
        {
            pScaStr[i] = '#';
        }
        else if (aucScaData[i] >= 12 && aucScaData[i] <= 14)
        {
            pScaStr[i] = (unsigned char)(aucScaData[i] + 'a' - 12);
        }
        else if(aucScaData[i] <= 9)
        {
            pScaStr[i] = (unsigned char)(aucScaData[i] + '0');
        }
        else //'F'
        {
            break;
        }
    }
}

/*******************************************************************************
  MODULE    : ATC_HexToAsc
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  JiangNa   2018.08.28   create
*******************************************************************************/
void AtcAp_HexToAsc(unsigned     short usLength, unsigned char *pOutData, unsigned char *pInputData)
{
    unsigned     short i = 0;
    unsigned     short j = 0;
    for (i = 0;i < usLength;i++)
    {
        if (((pInputData[i] >> 4) >= 0x0a) && ((pInputData[i] >> 4) <= 0x0f))
        {
            pOutData[j++] = (pInputData[i] >> 4) - 0x0a + 'A';
        }
        else if((pInputData[i] >> 4) <= 0x09)
        {
            pOutData[j++] = (pInputData[i] >> 4) + 0x30;
        }
        else
        {

        }
        if (((pInputData[i] & 0x0F) >= 0x0a) && ((pInputData[i] & 0x0F) <= 0x0f))
        {
            pOutData[j++] = (pInputData[i] & 0x0F) - 0x0a + 'A';
        }
        else if(((pInputData[i] & 0x0F) >= 0x00) && ((pInputData[i] & 0x0F) <= 0x09))
        {
            pOutData[j++] = (pInputData[i] & 0x0F) + 0x30;
        }
        else
        {

        }
    }
        return;
}

void AtcAp_ConvertByte2BitStr(unsigned char ucVal, unsigned char len, unsigned char* pBitStr)
{
    unsigned char  i;
    unsigned char  ucTemp  = 0;

    for (i  = 0; i < len; i++)
    {
        ucTemp = (0x08 >> i) & ucVal;
        pBitStr[i] = (ucTemp >> (3-i)) + 0x30;
    }
}

/*******************************************************************************
  MODULE    : AtcAp_WriteStrPara
  FUNCTION  :
  NOTE      :
  HISTORY   :
      1.   JiangNa   2018.07.17   create
*******************************************************************************/
void AtcAp_WriteStrPara(unsigned          int uiFlg, unsigned char *pucPara )
{
    if (NULL == pucPara)
    {
        return;
    }
    if (uiFlg)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)",\"%s\"",pucPara);
    }
    return;
}

/*******************************************************************************
  MODULE    : AtcAp_RevertHexToDecimal
  FUNCTION  : convert reverted Hex to Decimal number
  NOTE      :
  HISTORY   :
      1.  WangCong    2018/12/11   create
*******************************************************************************/
unsigned char AtcAp_RevertHexToDecimal(unsigned char ucHex)
{
    unsigned char ucDecimal = 0;

    ucDecimal = (ucHex & 0x0F) * 10;
    ucDecimal += (ucHex & 0xF0) >> 4;

    return ucDecimal;
}

/*******************************************************************************
  MODULE    : AtcAp_ConvertTimeZone
  FUNCTION  : Convert GMT to local time
  NOTE      : temporarily save the resulting time in the input parameter
  HISTORY   :
      1.  WangCong    2018/12/11   create
*******************************************************************************/
void AtcAp_ConvertTimeZone(unsigned char *pTimeZoneTime,unsigned char ucDayLightTime)
{
    unsigned char i                 = 0;
    unsigned char ucYear            = 0;
    unsigned char ucMonth           = 0;
    unsigned char ucHour            = 0;
    unsigned char ucMinute          = 0;
    unsigned char ucSecond          = 0;
    unsigned char ucTimeZoneOffset  = 0;                                                        /* MSB0: positive    MGB1: negative */
    unsigned char ucLeapYear        = ATC_FALSE;                                                /* 0common year    1: leap year */
    unsigned short usDay            = 0;
    unsigned long ulTimeInMinute    = 0;                                                        /* save current time in MINUTE */
    unsigned long ulTimeInMinuteTmp = 0;

    /* retrieve GMT time */
    ucYear = AtcAp_RevertHexToDecimal(pTimeZoneTime[0]);
    ucMonth = AtcAp_RevertHexToDecimal(pTimeZoneTime[1]);
    usDay = (unsigned short)AtcAp_RevertHexToDecimal(pTimeZoneTime[2]);
    ucHour = AtcAp_RevertHexToDecimal(pTimeZoneTime[3]);
    ucMinute = AtcAp_RevertHexToDecimal(pTimeZoneTime[4]);
    ucSecond = AtcAp_RevertHexToDecimal(pTimeZoneTime[5]);
    ucTimeZoneOffset = (pTimeZoneTime[6] & 0x07) * 10;
    ucTimeZoneOffset += (pTimeZoneTime[6] & 0xF0) >> 4;
    ucTimeZoneOffset += ucDayLightTime * 4;

    ulTimeInMinute += (unsigned long)((ucYear * 365 + ucYear / 4) * 24 * 60);                   /* consider AD 0001.01.01 as 365 days passed */
    if(0 == ucYear % 4)
    {
        ucLeapYear = 1;
    }
    else
    {
        ucLeapYear = 0;
    }

    for(i = 0; i < (ucMonth - 1) && i < 11; i++)
    {
        ulTimeInMinute += (unsigned long)(ATC_MonthDayTbl[ucLeapYear][i] * 24 * 60);
    }

    ulTimeInMinute += (unsigned long)((usDay - 1) * 60 * 24);
    ulTimeInMinute += (unsigned long)(ucHour * 60);
    ulTimeInMinute += (unsigned long)ucMinute;

    if(0 == (pTimeZoneTime[6] & 0x08))
    {
        ulTimeInMinute += (unsigned long)(ucTimeZoneOffset * 15);
        if(ulTimeInMinute > (unsigned long)(((ucYear + 1) * 365 + ucYear / 4) * 24 * 60))
        {
            ucYear += 1;
            if(0 == ucYear % 4)
            {
                ucLeapYear = ATC_TRUE;
                ulTimeInMinute += 60 * 24;                                              /* an extra Feb.29 will be calculated afterwards */
            }
            else
            {
                if(ATC_TRUE == ucLeapYear)
                {
                    ulTimeInMinute -= 60 * 24;                                          /* an extra Feb.29 will be calculated afterwards */
                    ucLeapYear = ATC_FALSE;
                }
            }
        }
    }
    else
    {
        ulTimeInMinute -= (unsigned long)(ucTimeZoneOffset * 15);
        if(ulTimeInMinute < (unsigned long)((ucYear * 365 + ucYear / 4) * 24 * 60))
        {
            ucYear -= 1;
            if(0 == ucYear % 4)
            {
                ucLeapYear = ATC_TRUE;
                ulTimeInMinute += 60 * 24;                                              /* an extra Feb.29 will be calculated afterwards */
            }
            else
            {
                if(ATC_TRUE == ucLeapYear)
                {
                    ulTimeInMinute -= 60 * 24;                                          /* an extra Feb.29 will be calculated afterwards */
                    ucLeapYear = ATC_FALSE;
                }
            }
        }
    }

    ulTimeInMinute -= (unsigned long)((ucYear * 365 + ucYear / 4) * 24 * 60);                   /* exclude YEAR */
    usDay = (unsigned short)(ulTimeInMinute / (60 * 24) + 1);                                   /* calculate which day is it of the year */
    ulTimeInMinuteTmp = (unsigned long)((usDay - 1) * 60 * 24);
    for(i = 0; i < 12; i++)
    {
        if(usDay > ATC_MonthDayTbl[ucLeapYear][i])
        {
            usDay -= (unsigned short)ATC_MonthDayTbl[ucLeapYear][i];
        }
        else
        {
            break;
        }
    }
    ucMonth = i + 1;

    ulTimeInMinute -= ulTimeInMinuteTmp;                                                /* exclude DAY */
    ucHour = (unsigned char)(ulTimeInMinute / 60);
    ucMinute = (unsigned char)(ulTimeInMinute % 60);

    pTimeZoneTime[0] = ucYear;
    pTimeZoneTime[1] = ucMonth;
    pTimeZoneTime[2] = (unsigned char)usDay;
    pTimeZoneTime[3] = ucHour;
    pTimeZoneTime[4] = ucMinute;
    pTimeZoneTime[5] = ucSecond;
    pTimeZoneTime[6] = 0x00;

    return;
}


/*******************************************************************************
  MODULE    : ATC_OutputLocalTime
  FUNCTION  : 
  NOTE      :
  HISTORY   :
*******************************************************************************/
void AtcAp_OutputLocalTime(LTE_NAS_LOCAL_TIME_STRU* pLocalTime)
{
    unsigned char aucUtAndLtz[D_ATC_UTANDLTZ_LEN] = {0};
    unsigned char ucDayLightTime = 0;
    if (D_ATC_FLAG_TRUE == pLocalTime->ucUtAndLtzFlg)
    {
        AtcAp_MemCpy(aucUtAndLtz, pLocalTime->aucUtAndLtz, D_ATC_UTANDLTZ_LEN);

        if (D_ATC_FLAG_TRUE == pLocalTime->ucNwDayltSavTimFlg)
        {
            ucDayLightTime = pLocalTime->ucNwDayltSavTim;
        }

        AtcAp_ConvertTimeZone(aucUtAndLtz, ucDayLightTime);
#if USR_CUSTOM2
        AtcAp_StrPrintf_AtcRspBuf((const char *)"20%d%d/%d%d/%d%d,%d%d:%d%d:%d%d",
            aucUtAndLtz[0] / 10,aucUtAndLtz[0] % 10,
            aucUtAndLtz[1] / 10,aucUtAndLtz[1] % 10,
            aucUtAndLtz[2] / 10,aucUtAndLtz[2] % 10,
            aucUtAndLtz[3] / 10,aucUtAndLtz[3] % 10,
            aucUtAndLtz[4] / 10,aucUtAndLtz[4] % 10,
            aucUtAndLtz[5] / 10,aucUtAndLtz[5] % 10
            );
#else
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\"20%d%d/%d%d/%d%d,%d%d:%d%d:%d%d\"",
            aucUtAndLtz[0] / 10,aucUtAndLtz[0] % 10,
            aucUtAndLtz[1] / 10,aucUtAndLtz[1] % 10,
            aucUtAndLtz[2] / 10,aucUtAndLtz[2] % 10,
            aucUtAndLtz[3] / 10,aucUtAndLtz[3] % 10,
            aucUtAndLtz[4] / 10,aucUtAndLtz[4] % 10,
            aucUtAndLtz[5] / 10,aucUtAndLtz[5] % 10
            );
#endif
    }
}
/*******************************************************************************
  MODULE    : AtcAp_OutputUniversalTime
  FUNCTION  : 
  NOTE      :
  HISTORY   :
*******************************************************************************/
void AtcAp_OutputUniversalTime(LTE_NAS_LOCAL_TIME_STRU* pLocalTime)
{
    if (D_ATC_FLAG_TRUE == pLocalTime->ucUtAndLtzFlg)
    {
#if USR_CUSTOM2
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf 
            + g_AtcApInfo.stAtRspInfo.usRspLen),(const unsigned char *)"20%d%d/%d%d/%d%d,%d%d:%d%d:%d%d",
                pLocalTime->aucUtAndLtz[0] & 0x0F,
                (pLocalTime->aucUtAndLtz[0] & 0xF0) >> 4,
                pLocalTime->aucUtAndLtz[1] & 0x0F,
                (pLocalTime->aucUtAndLtz[1] & 0xF0) >> 4,
                pLocalTime->aucUtAndLtz[2] & 0x0F,
                (pLocalTime->aucUtAndLtz[2] & 0xF0) >> 4,
                pLocalTime->aucUtAndLtz[3] & 0x0F,
                (pLocalTime->aucUtAndLtz[3] & 0xF0) >> 4,
                pLocalTime->aucUtAndLtz[4] & 0x0F,
                (pLocalTime->aucUtAndLtz[4] & 0xF0) >> 4,
                pLocalTime->aucUtAndLtz[5] & 0x0F,
                (pLocalTime->aucUtAndLtz[5] & 0xF0) >> 4
            );
#else
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf 
            + g_AtcApInfo.stAtRspInfo.usRspLen),(const unsigned char *)"\"20%d%d/%d%d/%d%d,%d%d:%d%d:%d%d\"",
                pLocalTime->aucUtAndLtz[0] & 0x0F,
                (pLocalTime->aucUtAndLtz[0] & 0xF0) >> 4,
                pLocalTime->aucUtAndLtz[1] & 0x0F,
                (pLocalTime->aucUtAndLtz[1] & 0xF0) >> 4,
                pLocalTime->aucUtAndLtz[2] & 0x0F,
                (pLocalTime->aucUtAndLtz[2] & 0xF0) >> 4,
                pLocalTime->aucUtAndLtz[3] & 0x0F,
                (pLocalTime->aucUtAndLtz[3] & 0xF0) >> 4,
                pLocalTime->aucUtAndLtz[4] & 0x0F,
                (pLocalTime->aucUtAndLtz[4] & 0xF0) >> 4,
                pLocalTime->aucUtAndLtz[5] & 0x0F,
                (pLocalTime->aucUtAndLtz[5] & 0xF0) >> 4
            );
#endif
    }
}
/*******************************************************************************
  MODULE    : ATC_OutputTimeZone
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  JiangNa    2018/12/18   create
*******************************************************************************/
static void AtcAp_OutputTimeZone_Sub(unsigned char* pucData, unsigned char ucCtzrReport, LTE_NAS_LOCAL_TIME_STRU* pLocalTime)
{
    unsigned char aucDataTmp[] = {"+XYCTZEU"};
    unsigned char ucLocalTimeZone = 0;
    unsigned char ucDayLightTime = 0;

    ucLocalTimeZone = (pLocalTime->ucLocalTimeZone & 0x07) * 10 + ((pLocalTime->ucLocalTimeZone & 0xf0) >> 4);
    if (D_ATC_FLAG_TRUE == pLocalTime->ucNwDayltSavTimFlg)
    {
        ucDayLightTime = (pLocalTime->ucNwDayltSavTim & 3) * 4;
    }
#if USR_CUSTOM2
    if (0 == (pLocalTime->ucLocalTimeZone & 0x08))
    {/* bit8: 0-positive 1-negative */
        //ucLocalTimeZone += ucDayLightTime;
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, (const unsigned char *)"\r\n%s:+%d%d", pucData,
            (ucLocalTimeZone / 10), (ucLocalTimeZone % 10));
    }
    else
    {
        //ucLocalTimeZone -= ucDayLightTime;
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, (const unsigned char *)"\r\n%s:-%d%d", pucData,
            (ucLocalTimeZone / 10), (ucLocalTimeZone % 10));
    }
#else
    if (0 == (pLocalTime->ucLocalTimeZone & 0x08))
    {/* bit8: 0-positive 1-negative */
        //ucLocalTimeZone += ucDayLightTime;
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, (const unsigned char *)"\r\n%s:\"+%d%d\"", pucData,
            (ucLocalTimeZone / 10), (ucLocalTimeZone % 10));
    }
    else
    {
        //ucLocalTimeZone -= ucDayLightTime;
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, (const unsigned char *)"\r\n%s:\"-%d%d\"", pucData,
            (ucLocalTimeZone / 10), (ucLocalTimeZone % 10));
    }
#endif
    if ((2 == ucCtzrReport) || (3 == ucCtzrReport)
        || 0 == AtcAp_Strncmp(pucData, aucDataTmp))
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
            (const unsigned char *)",%d,", ucDayLightTime / 4);
    }

    return;
}

void AtcAp_OutputTimeZone(unsigned char ucCtzrReport, LTE_NAS_LOCAL_TIME_STRU* pLocalTime)
{
    unsigned char aucData[][7] = {"","+CTZV","+CTZE","+CTZEU"};
    AtcAp_OutputTimeZone_Sub(aucData[ucCtzrReport], ucCtzrReport, pLocalTime);
    return;
}

void AtcAp_OutputTimeZone_XY(unsigned char ucCtzrReport, LTE_NAS_LOCAL_TIME_STRU* pLocalTime)
{
    unsigned char aucData[] = {"+XYCTZEU"};
    AtcAp_OutputTimeZone_Sub(aucData, ucCtzrReport, pLocalTime);
    return;
}

void AtcAp_ConvertInDotFormat(char* pStrBuff, unsigned char* pAddr, unsigned char ucLen)
{
    unsigned char index;
    unsigned char ucBuffIdx = 0;

    if(NULL == pAddr)
    {
        return;
    }

    if(0 != strlen(pStrBuff))
    {
        pStrBuff[ucBuffIdx++] = '.';
    }

    for(index = 0; index < ucLen; index++)
    {
        ucBuffIdx += sprintf(pStrBuff + ucBuffIdx, "%d", pAddr[index]);
        if(index != ucLen - 1)
        {
            pStrBuff[ucBuffIdx++] = '.';
        }
    }
}

void AtcAp_OutputAddr_IpDns(unsigned char ucV4V6Fg, unsigned char* pAddr_v4, unsigned char* pAddr_v6)
{
    char   aucAddr_v4_str[20] = { 0 };
    char   aucAddr_v6_str[64] = { 0 };
    
    AtcAp_ConvertInDotFormat(aucAddr_v4_str, pAddr_v4, 4);
    AtcAp_ConvertInDotFormat(aucAddr_v6_str, pAddr_v6, 16);
    
    if(ATC_AP_TRUE == ucV4V6Fg)
    {
        if(0 != strlen(aucAddr_v4_str) || 0 != strlen(aucAddr_v6_str))
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)",\"%s.%s\"", aucAddr_v4_str, aucAddr_v6_str);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)",\"\"");
        }
    }
    else
    {
        if(NULL != pAddr_v4)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)",\"%s\"", aucAddr_v4_str);
        }
        else if(NULL != pAddr_v6)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)",\"%s\"", aucAddr_v6_str);
        }
        else
        {
             AtcAp_StrPrintf_AtcRspBuf((const char *)",\"\"");
        }
    }
}

#ifdef LCS_MOLR_ENABLE
static void Lcs_OutputXML_ElementOlny_Start(unsigned char* pXmlData, char* pElementName)
{
    char  dataStr[100]         = { 0 };
    char  spaceFormat[10]      = { 0 };

    g_AtcApInfo.ucXmlElementLevel++;
    if(0 != g_AtcApInfo.ucXmlElementLevel)
    {
        sprintf(spaceFormat, "%%%ds", g_AtcApInfo.ucXmlElementLevel * 2);
        sprintf(dataStr, spaceFormat, " ");
    }   
    sprintf(dataStr + strlen(dataStr), "<%s>\r\n", pElementName);

    strcat((char*)pXmlData, dataStr);
}

static void Lcs_OutputXML_ElementWithValue_Str(unsigned char* pXmlData, char* pElementName, char* pValue)
{
    char  dataStr[100]         = { 0 };
    char  spaceFormat[10]      = { 0 };

    sprintf(spaceFormat, "%%%ds", (g_AtcApInfo.ucXmlElementLevel + 1) * 2);
    sprintf(dataStr, spaceFormat, " ");   
    sprintf(dataStr + strlen(dataStr), "<%s>%s</%s>\r\n", pElementName, pValue, pElementName);
    
    strcat((char*)pXmlData, dataStr);
}

static void Lcs_OutputXML_ElementWithValue_Float(unsigned char* pXmlData, char* pElementName, float Val)
{
    char  dataStr[100]         = { 0 };
    char  spaceFormat[10]      = { 0 };

    sprintf(spaceFormat, "%%%ds", (g_AtcApInfo.ucXmlElementLevel + 1) * 2);
    sprintf(dataStr, spaceFormat, " ");   
    sprintf(dataStr + strlen(dataStr), "<%s>%f</%s>\r\n", pElementName, Val, pElementName);
    
    strcat((char*)pXmlData, dataStr);
}


static void Lcs_OutputXML_ElementWithValue_Interger(unsigned char* pXmlData, char* pElementName,  signed int Val)
{
    char  dataStr[100]         = { 0 };
    char  spaceFormat[10]      = { 0 };
    
    sprintf(spaceFormat, "%%%ds", (g_AtcApInfo.ucXmlElementLevel + 1) * 2);
    sprintf(dataStr, spaceFormat, " ");   
    sprintf(dataStr + strlen(dataStr), "<%s>%d</%s>\r\n", pElementName, Val, pElementName);
    
    strcat((char*)pXmlData, dataStr);
}

static void Lcs_OutputXML_ElementOlny_End(unsigned char* pXmlData, char* pElementName)
{
    char  dataStr[100]         = { 0 };
    char  spaceFormat[10]      = { 0 };    

    if(0 != g_AtcApInfo.ucXmlElementLevel)
    {
        sprintf(spaceFormat, "%%%ds", g_AtcApInfo.ucXmlElementLevel * 2);
        sprintf(dataStr, spaceFormat, " ");
    }   
    sprintf(dataStr + strlen(dataStr), "</%s>\r\n", pElementName);
    g_AtcApInfo.ucXmlElementLevel--;    

    strcat((char*)pXmlData, dataStr);
}

static void Lcs_OutputXML_CoordinateInfo(unsigned char* pXmlData, LCS_COOR_STRU* pCoordinate)
{
    Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_COORDINATE);

    //<latitude>
    Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_LATITUDE);
    Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_NORTH, pCoordinate->tLatitude.ucLatitudeSign);
    Lcs_OutputXML_ElementWithValue_Float(pXmlData, LCS_XML_ELEMEN_DEGRESS, pCoordinate->tLatitude.uiDegreesLatitude * 90.0 / (1 << 23));
    Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_LATITUDE);
    //<longitude>
    Lcs_OutputXML_ElementWithValue_Float(pXmlData, LCS_XML_ELEMEN_LONGITUDE, pCoordinate->iDegreesLongitude * 360.0 / (1 << 24));
     
    Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_COORDINATE);
}

static void Lcs_OutputXML_UncertInfo(unsigned char* pXmlData, unsigned char* pElementName, unsigned char ucUncertn)
{
    float   fUncert; 
    char    OutputStr[20] = { 0 };

    fUncert = 10* (pow(1 + 0.1, ucUncertn) - 1);
    sprintf(OutputStr, "%0.1f", fUncert);

    Lcs_OutputXML_ElementWithValue_Str(pXmlData, pElementName, OutputStr);
}

static void Lcs_OutputXML_Confidence(unsigned char* pXmlData, unsigned char ucConfidence)
{
    if(0 == ucConfidence || ucConfidence > 100)
    {
        return;
    }

    Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_CONFIDENCE, ucConfidence);
}


static void Lcs_OutputXML_UncertEcliipseInfo(unsigned char* pXmlData, LCS_UNCERTN_ELLIP_STRU* pUncertEllipse, unsigned char ucConfidence)
{
    Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_UNCERT_ELLIP);

    Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_UNCERT_SEMI_MAJOR, pUncertEllipse->ucUncertnSemiMajor);
    Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_UNCERT_SEMI_MIJOR, pUncertEllipse->ucUncertnSemiMin);
    Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_ORIENT_MIJOR, pUncertEllipse->ucOrientMajorAxis);
    Lcs_OutputXML_Confidence(pXmlData, ucConfidence);

    Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_UNCERT_ELLIP);
}

static void Lcs_OutputXML_Altitude(unsigned char* pXmlData, LCS_ALTI_STRU* pAltitude)
{
    Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_ALT);

    Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_HEIGHT_ABOVE_SURFACE, pAltitude->ucHeightAboveSurface);
    Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_HEIGHT, pAltitude->usAlti);

    Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_ALT);
}

static void Lcs_OutputXML_UncertAlti(unsigned char* pXmlData, unsigned char ucUncertnAlti)
{
    float   val; 
    char    OutputStr[20] = { 0 };

    val = 45 * (pow(1 + 0.025, ucUncertnAlti) - 1);
    sprintf(OutputStr, "%0.2f", val);

    Lcs_OutputXML_ElementWithValue_Str(pXmlData, LCS_XML_ELEMEN_UNCERT_ALT, OutputStr);
}

static void Lcs_OutputXml_ShapeDataInfo(unsigned char* pXmlData, LCS_SHAPE_DATA_STRU* pShapeData)
{
    unsigned char    i;
    if(NULL == pShapeData)
    {
        return;
    }

    Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_SHAPE_DATA);   
    switch(pShapeData->ucShapeType)
    {
        case LCS_SHAPE_ELLIP_POINT:
            Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_ELLIP_POINT);
            Lcs_OutputXML_CoordinateInfo(pXmlData, (LCS_COOR_STRU*)&pShapeData->u.tEllipPoint);
            Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_ELLIP_POINT);
            break;
        case LCS_SHAPE_ELLIP_POINT_UNCERT_CIRCLE:
            Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_ELLIP_UNCERT_CIRCLE);
            Lcs_OutputXML_CoordinateInfo(pXmlData, &pShapeData->u.tEllipPointUncertCircle.tCoordinate);
            Lcs_OutputXML_UncertInfo(pXmlData, LCS_XML_ELEMEN_UNCERT_CIRCLE, pShapeData->u.tEllipPointUncertCircle.ucUncertn);
            Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_ELLIP_UNCERT_CIRCLE);
            break;
        case LCS_SHAPE_ELLIP_POINT_UNCERT_ELLIP:
            Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_ELLIP_UNCERT_ELLIP);
            Lcs_OutputXML_CoordinateInfo(pXmlData, &pShapeData->u.tEllipPointUncertEllip.tCoordinate);
            Lcs_OutputXML_UncertEcliipseInfo(pXmlData, &pShapeData->u.tEllipPointUncertEllip.tUncertEllipse, pShapeData->u.tEllipPointUncertEllip.ucConfidence);
            Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_ELLIP_UNCERT_ELLIP);
            break;
        case LCS_SHAPE_POLYGON:
            Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_POLYGON);
            for(i = 0; i < pShapeData->u.tPolygon.ucCnt && i < 8; i++) //i < 8: prevent exceeding the size of pXmlData by 4096 byte
            {
                Lcs_OutputXML_CoordinateInfo(pXmlData, &pShapeData->u.tPolygon.atCoordinate[i]);
            }
            Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_POLYGON);
            break;
        case LCS_SHAPE_ELLIP_POINT_ALT:
            Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_ELLIP_POINT_ALT);
            Lcs_OutputXML_CoordinateInfo(pXmlData, &pShapeData->u.tEllipPointWithAlti.tCoordinate);
            Lcs_OutputXML_Altitude(pXmlData, &pShapeData->u.tEllipPointWithAlti.tAltitude);
            Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_ELLIP_POINT_ALT);
            break;
        case LCS_SHAPE_ELLIP_POINT_ALT_UNCERT:
            Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_ELLIP_POINT_ALT_UNCET);
            Lcs_OutputXML_CoordinateInfo(pXmlData, &pShapeData->u.tEllipPointWithAltiUncert.tCoordinate);
            Lcs_OutputXML_Altitude(pXmlData, &pShapeData->u.tEllipPointWithAltiUncert.tAltitude);
            Lcs_OutputXML_UncertEcliipseInfo(pXmlData, &pShapeData->u.tEllipPointWithAltiUncert.tUncertEllipse, pShapeData->u.tEllipPointWithAltiUncert.ucConfidence);
            Lcs_OutputXML_UncertAlti(pXmlData, pShapeData->u.tEllipPointWithAltiUncert.ucUncertnAlti);
            Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_ELLIP_POINT_ALT_UNCET);
            break;
        case LCS_SHAPE_ELLIP_ARC:
            Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_ELLIP_POINT_ARC);
            Lcs_OutputXML_CoordinateInfo(pXmlData, &pShapeData->u.tEllipArc.tCoordinate);
            Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_INNER_RAD, 5 * pShapeData->u.tEllipArc.usInnerRadius);
            Lcs_OutputXML_UncertInfo(pXmlData, LCS_XML_ELEMEN_UNCERT_RAD, pShapeData->u.tEllipPointUncertCircle.ucUncertn);
            Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_OFFSET_ANGLE, 2 * pShapeData->u.tEllipArc.ucOffsetAngle);
            Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_INC_ANGLE, 2 * (pShapeData->u.tEllipArc.ucIncludedAngle + 1));
            Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_ELLIP_POINT_ARC);
            break;
        default:
            break;
    }
    
    Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_SHAPE_DATA);
}

static void Lcs_OutputXml_VelocityInfo(unsigned char* pXmlData, LCS_VELOCITY_DATA_STRU* pVelData)
{
    if(NULL == pVelData)
    {
        return;
    }

    Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_VEL_DATA);
    
    Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_HOR_VEL, pVelData->tHorizSpeed.usSpeed);
    if(pVelData->OP_VertSpeed)
    {
        Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_VERT_VEL, pVelData->tVertSpeed.ucSpeed);
        Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_VERT_VEL_DIRECT, pVelData->tVertSpeed.ucDirect);
    }
    
    if(pVelData->OP_HorUncert)
    {
        Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_HOR_UNCERT, pVelData->ucHorSpeedUncert);
    }

    if(pVelData->OP_VertUncert)
    {
        Lcs_OutputXML_ElementWithValue_Interger(pXmlData, LCS_XML_ELEMEN_VERT_UNCERT, pVelData->ucVertSpeedUncert);
    }

    Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_VEL_DATA);
}


unsigned short Lcs_MolrResult_OutputXML(unsigned char* pXmlData, unsigned short* pSize, LCS_SHAPE_DATA_STRU* pShapeData, LCS_VELOCITY_DATA_STRU* pVelData)
{
    g_AtcApInfo.ucXmlElementLevel = 255;
    
    sprintf((char*)pXmlData, "%s\r\n", LCS_XML_ELEMENT_HEAD);
    Lcs_OutputXML_ElementOlny_Start(pXmlData, LCS_XML_ELEMEN_LCATION_PARAM);
    
    Lcs_OutputXml_ShapeDataInfo(pXmlData, pShapeData);
    Lcs_OutputXml_VelocityInfo(pXmlData, pVelData);

    Lcs_OutputXML_ElementOlny_End(pXmlData, LCS_XML_ELEMEN_LCATION_PARAM);

    return strlen((char*)pXmlData);
}
#endif

void AtcAp_Build_NCell(API_CELL_LIST_STRU* pCellList)
{
    unsigned char  ucIdx = 0;
    unsigned short u16Rsrp;
    unsigned short u16Rsrq;
    unsigned short u16Rssi;
    unsigned short u16Snr;

    for (ucIdx = 1; ucIdx < pCellList->ucCellNum; ucIdx++)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, 
            (const unsigned char *)"%s%d%c%d%s", "+NUESTATS:CELL,", pCellList->aNCell[ucIdx].ulDlEarfcn, ',', pCellList->aNCell[ucIdx].usPhyCellId, ",0,");

        if(0 == pCellList->aNCell[ucIdx].sRsrp)
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), (const unsigned char *)"%s", "0,");
        }
        else
        {
            u16Rsrp = (~(pCellList->aNCell[ucIdx].sRsrp - 1)) & 0x7FFF;
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), (const unsigned char *)"%c%d%c", '-', u16Rsrp, ',');
        }

        if(pCellList->aNCell[ucIdx].sRsrq < 0)
        {
            u16Rsrq = (~(pCellList->aNCell[ucIdx].sRsrq - 1)) & 0x7FFF;
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
                (const unsigned char *)"%c%d%c", '-', u16Rsrq, ',');
        }
        else
        {
            u16Rsrq = pCellList->aNCell[ucIdx].sRsrq;
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
                (const unsigned char *)"%d%c", u16Rsrq, ',');
        }

        if(pCellList->aNCell[ucIdx].sRssi < 0)
        {
            u16Rssi = (~(pCellList->aNCell[ucIdx].sRssi - 1)) & 0x7FFF;
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
                (const unsigned char *)"%c%d%c", '-', u16Rssi, ',');
        }
        else
        {
            u16Rssi = pCellList->aNCell[ucIdx].sRssi;
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
                (const unsigned char *)"%d%c", u16Rssi, ',');
        }

        if(pCellList->aNCell[ucIdx].sSinr < 0)
        {
            u16Snr = (~(pCellList->aNCell[ucIdx].sSinr - 1)) & 0x7FFF;
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
                (const unsigned char *)"%c%d\r\n", '-', u16Snr);
        }
        else
        {
            u16Snr = pCellList->aNCell[ucIdx].sSinr;
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
                (const unsigned char *)"%d\r\n", u16Snr);
        }
    }
}

unsigned char AtcAp_CurrEventChk_IsWaitSmsPdu()
{
    switch(g_AtcApInfo.usCurrEvent)
    {
        case D_ATC_EVENT_CMGW:
        case D_ATC_EVENT_CMGS:
        case D_ATC_EVENT_CMGC:
        case D_ATC_EVENT_CNMA:
        case D_ATC_EVENT_QCMGS:
            return ATC_AP_TRUE;
        default:
            return ATC_AP_FALSE;
    }
}

/*******************************************************************************
  MODULE    : ATC_Strncpy
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  Dep2_066   2016.12.20   create
*******************************************************************************/
void ATC_Strncpy(unsigned char *pStr1, unsigned char *pStr2, unsigned short usCount)
{
    unsigned short i;

    for (i = 0; i < usCount; i++)
    {
        *(pStr1 + i) = *(pStr2 + i);
    }
    return ; 
}


/*******************************************************************************
  MODULE    : ATC_ShortToBinary
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  Dep2_066   2016.12.20   create
*******************************************************************************/
unsigned char ATC_ShortToBinary(unsigned char *pInputCharStr, unsigned char ucLength, unsigned short *pBinaryData)
{
    unsigned short usNum;                                                                       /* binary data                          */
    unsigned char ucSymbolFlg;                                                                  /* 0:Positive  1:Negative               */
    unsigned char ucCount;

    ucSymbolFlg = 0;                                                                    /* Initial period                       */

    if ((*pInputCharStr) == '-')
    {
        pInputCharStr = pInputCharStr + 1;
        ucSymbolFlg = 1;                                                                /* negative number set                  */
        if (ucLength < 2)
        {
            return  ATC_NG;
        }
        else
        {
            ucLength--;
        }
    }

    usNum = 0;
    for (ucCount = 0; ucCount < ucLength; ucCount++)
    {   
        if ((*(pInputCharStr + ucCount) >= '0') && (*(pInputCharStr + ucCount) <= '9'))
        {
            usNum = (usNum * 10);
            usNum += (*(pInputCharStr + ucCount) - '0');
        }
        else
        {
            return ATC_NG;
        }
    }
    if (1 == ucSymbolFlg)
    {
        usNum = usNum * (-1);                                                           /*lint !e732*/
    }

    *pBinaryData = usNum;                                                               /* binary data set                          */
    return ATC_OK;
}

//shao add for USAT
/*******************************************************************************
  MODULE    : ATC_HexToBinary
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  Dep2_066   2016.12.20   create
*******************************************************************************/
unsigned char ATC_HexToBinary(unsigned char *pInputCharStr, unsigned char ucLength, unsigned int *pBinaryData)
{
    unsigned int usNum = 0;                                                                       /* binary data                          */
    unsigned char  ucCount;

    usNum = 0;
    for (ucCount = 0; ucCount < ucLength; ucCount++)
    {   
        if ((*(pInputCharStr + ucCount) >= '0') && (*(pInputCharStr + ucCount) <= '9'))
        {
            usNum = (unsigned int)(usNum * 16);
            usNum = (unsigned int)(usNum + (*(pInputCharStr + ucCount) - '0'));
        }
        else if((*(pInputCharStr + ucCount) >= 'a') && (*(pInputCharStr + ucCount) <= 'f'))
        {
            usNum = (unsigned int)(usNum * 16);
            usNum = (unsigned int)(usNum + (*(pInputCharStr + ucCount) - 'a'+10));
        }
        else if((*(pInputCharStr + ucCount) >= 'A') && (*(pInputCharStr + ucCount) <= 'F'))
        {
            usNum = (unsigned int)(usNum * 16);
            usNum = (unsigned int)(usNum + (*(pInputCharStr + ucCount) - 'A'+10));
        }
        else
        {
            return ATC_NG;
        }
    }

    *pBinaryData = usNum;                                                               /* binary data set                      */
    return ATC_OK;
}

/*******************************************************************************
  MODULE    : ATC_CharToBinary
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  Dep2_066   2016.12.20   create
*******************************************************************************/
unsigned char ATC_CharToBinary(unsigned char *pInputCharStr ,unsigned char ucLength ,unsigned char *pBinaryData, unsigned char ucSignedFlg)
{
    unsigned char ucNum;                                                                        /* binary data                          */
    unsigned char ucSymbolFlg;                                                                  /* 0:Positive  1:Negative               */
    unsigned char ucCount;
    unsigned long ulTemp = 0;

    ucSymbolFlg = 0;                                                                    /* Initial period                       */

    if ((*pInputCharStr) == '-')
    {
        pInputCharStr = pInputCharStr + 1;
        ucSymbolFlg = 1;                                                                /* negative number set                  */
        if (ucLength < 2)
        {
            return  ATC_NG;
        }
        else
        {
            ucLength = (unsigned char)(ucLength - 1);
        }
    }
    else if((*pInputCharStr) == '+')
    {
        pInputCharStr = pInputCharStr + 1;
        if (ucLength < 2)
        {
            return  ATC_NG;
        }
        else
        {
            ucLength = (unsigned char)(ucLength - 1);
        }
    }

    ucNum = 0;
    for (ucCount = 0; ucCount < ucLength; ucCount++)
    {   
        if ((*(pInputCharStr + ucCount) >= '0') && (*(pInputCharStr + ucCount) <= '9'))
        {
            if(0 == ulTemp && 0 != ucCount)
            {
                return ATC_NG;
            }
            ulTemp = ulTemp * 10;
            ulTemp = (ulTemp + *(pInputCharStr + ucCount) - '0');
        }
        else
        {
            return ATC_NG;
        }
    }

    if(0 == ucSignedFlg)
    {
        if(1 == ucSymbolFlg || ulTemp > 255)
        {
            return ATC_NG;
        }
    }
    else // -128 ~ 127
    {
        if((1 == ucSymbolFlg && ulTemp > 128) || (0 == ucSymbolFlg && ulTemp > 127))
        {
            return ATC_NG;
        }
    }
    
    ucNum = (unsigned char)ulTemp;
    if (1 == ucSymbolFlg)
    {
        ucNum = (unsigned char)(ucNum * (-1));
    }

    *pBinaryData = ucNum;                                                               /* binary data set                      */
    return ATC_OK;
}

/*******************************************************************************
  MODULE    : ATC_UshortToBinary
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  Dep2_066   2016.12.20   create
*******************************************************************************/
unsigned char ATC_UshortToBinary(unsigned char *pInputCharStr, unsigned char ucLength, unsigned short *pBinaryData)
{
    unsigned short usNum;                                                                       /* binary data                          */
    unsigned char  ucSymbolFlg;                                                                 /* 0:Positive  1:Negative               */
    unsigned char  ucCount;

    ucSymbolFlg = 0;                                                                    /* Initial period                       */

    if ((*pInputCharStr) == '-')
    {
        pInputCharStr = pInputCharStr + 1;
        ucSymbolFlg = 1;                                                                /* negative number set                  */
        if (ucLength < 2)
        {
            return  ATC_NG;
        }
        else
        {
            ucLength = (unsigned char)(ucLength - 1);
        }
    }

    usNum = 0;
    for (ucCount = 0; ucCount < ucLength; ucCount++)
    {   
        if ((*(pInputCharStr + ucCount) >= '0') && (*(pInputCharStr + ucCount) <= '9'))
        {
            usNum = (unsigned short)(usNum * 10);
            usNum = (unsigned short)(usNum + (*(pInputCharStr + ucCount) - '0'));
        }
        else
        {
            return ATC_NG;
        }
    }
    if (1 == ucSymbolFlg)
    {
        usNum = (unsigned short)(usNum * (-1));
    }

    *pBinaryData = usNum;                                                               /* binary data set                      */
    return ATC_OK;
}

/*******************************************************************************
  MODULE    : ATC_PassWordStrCheck
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  tianchengbin    2018/12/18   create
*******************************************************************************/
unsigned char ATC_PassWordStrCheck(unsigned char *pInputStr, unsigned char *pEventBuffer)
{
    unsigned short ucCount;
    unsigned char  ucResult = ATC_OK;

    for (ucCount = 0; *(pInputStr + ucCount) != '\0'; ucCount++)
    {
        switch(*(pInputStr + ucCount))
        {
        case '0' :
        case '1' :
        case '2' :
        case '3' :
        case '4' :
        case '5' :
        case '6' :
        case '7' :
        case '8' :
        case '9' :
            continue;
        default:
            ucResult = ATC_NG;
            break;
        }
        break;
    }

    if (ATC_OK == ucResult)
    {
        ATC_Strncpy(pEventBuffer, pInputStr, ucCount);
    }

    return ucResult;
}

const char* ATC_ConvertErrCode2Str(const ST_ATC_GLOBLE_ERROR_CAUSE_TABLE* pErrTab, unsigned char size, unsigned char ucErrCode)
{ 
    unsigned char          index;
    const char*    unknown = "unknown";

    for(index = 0; index < size; index++)
    {
        if(ucErrCode == pErrTab[index].usErrCode)
        {
            return (const char*)pErrTab[index].pCmeErrorText;
        }
    }
    return unknown;
}

unsigned short ATC_GetExtendSpaceNum(char* pBuff, unsigned char ucOnceFlg)
{
    char* pSubStr;
    char* at_line_header = "\r\n";
    unsigned short usConut = 0;

    //pSubStr = strstr(pBuff, at_line_header);
    pSubStr = pBuff;
    while(NULL != pSubStr)
    {
        pSubStr = strstr(pSubStr, ":");   
        if(pSubStr == NULL)
        {
            break;
        }

        pSubStr++;
        if(pSubStr[0] != ' ')
        {
            usConut++;
        }
        if(ucOnceFlg == ATC_AP_TRUE)
        {
            break;
        }
        pSubStr = strstr(pSubStr, at_line_header);
    }

    return usConut;
}

void ATC_CmdHeaderWithSpaceProc(char** pBuff, unsigned short* pusLen, unsigned short usMaxLen, unsigned char ucSmsDataFlg)
{
    char* pInStr;
    char* pSubStr;
    char* key_str = "\r\n";
    unsigned short usSpaceCnt;
    char* pTempBuff;
    
    if(NULL != strstr(*pBuff, "+PSINFO:") || NULL != strstr(*pBuff, "+DBGINFO:")|| NULL != strstr(*pBuff, "^SIMST:") )
    {
        return;
    }

    usSpaceCnt = ATC_GetExtendSpaceNum(*pBuff, ucSmsDataFlg);
    if(usMaxLen < *pusLen + usSpaceCnt)
    {
        pTempBuff = (char*)AtcAp_Malloc(*pusLen + usSpaceCnt + 1);
        strncpy(pTempBuff, *pBuff, *pusLen);
        AtcAp_Free(*pBuff);
        *pBuff = pTempBuff;
  
        usMaxLen = *pusLen + usSpaceCnt;
    }

    pInStr = (char*)*pBuff;
    //pSubStr = strstr(pInStr, key_str);
    pSubStr = pInStr;
    while(NULL != pSubStr)
    {
        pInStr = pSubStr + strlen(key_str);
        pSubStr = strstr(pInStr, ":");
        if(pSubStr == NULL)
        {
            break;
        }
        pInStr = (pSubStr + 1);

        if(pInStr[0] != ' ')
        {
            if(strlen((const char *)(*pBuff)) + 1 > usMaxLen)
            {
                xy_assert(0);
            }

            if(strlen(pInStr) != 0)
            {
                memmove(pInStr + 1, pInStr, strlen(pInStr) + 1);
            }
            pInStr[0] = ' ';
        }
        pInStr++;

        pSubStr = strstr(pInStr, key_str);
        if(ucSmsDataFlg == ATC_AP_TRUE)
        {
            break;
        }
    }

    *pusLen += usSpaceCnt;
}

unsigned char ATC_NCONFIG_SET_IsStrChk(unsigned char ucType)
{
    switch(ucType)
    {
        case D_ATC_NCONFIG_AUTOCONNECT:
        case D_ATC_NCONFIG_CELL_RESELECTION:
        case D_ATC_NCONFIG_ENABLE_BIP:
        case D_ATC_NCONFIG_PCO_IE_TYPE:
        case D_ATC_NCONFIG_NON_IP_NO_SMS_ENABLE:
        case D_ATC_NCONFIG_T3324_T3412_EXT_CHANGE_REPORT:
            return ATC_AP_TRUE;
        default:
            return ATC_AP_FALSE;
    }
}

unsigned char ATC_ConvertDotSeptNumParams2Short(unsigned char *pSrc, unsigned char ucLen, unsigned char ucParamNum, unsigned short ausDesc[], unsigned char* pucDescLen)
{
    unsigned char i, j = 0;
    unsigned char char_num = 0;
    unsigned long ulValue = 0;

    *pucDescLen = 0;
    if(0 == ucLen)
    {
        return ATC_OK;
    }
    
    for(i = 0; i < ucLen; i++)
    {
        if('.' == pSrc[i])
        {
            if(0 == char_num || j > ucParamNum - 1)
            {
                return ATC_NG;
            }

            ausDesc[j++] = (unsigned short)ulValue;
            ulValue = 0;
            char_num = 0;
            continue;
        }
        char_num++;

        if(pSrc[i] < '0' || pSrc[i] > '9')
        {
            return ATC_NG;
        }
        
        ulValue = ulValue * 10 + (pSrc[i] - '0');
        if(ulValue > 65535)
        {
            return ATC_NG;
        }
        
    }
    
    if(0 == char_num)
    {
        return ATC_NG;
    }
    ausDesc[j++] = (unsigned short)ulValue;
    *pucDescLen = j;

    return ATC_OK;
}

unsigned char ATC_ConvertDotSeptNumParams2Char(unsigned char *pSrc, unsigned char ucLen, unsigned char ucParamNum, unsigned char aucDesc[], unsigned char* pucDescLen)
{
    unsigned char i, j = 0;
    unsigned char char_num = 0;
    unsigned short usValue = 0;

    *pucDescLen = 0;
    if(0 == ucLen)
    {
        return ATC_OK;
    }
    
    for(i = 0; i < ucLen; i++)
    {
        if('.' == pSrc[i])
        {
            if(0 == char_num || j > ucParamNum - 1)
            {
                return ATC_NG;
            }

            aucDesc[j++] = (unsigned char)usValue;
            usValue = 0;
            char_num = 0;
            continue;
        }
        
        if(++char_num > 3)
        {
            return ATC_NG;
        }

        if(pSrc[i] < '0' || pSrc[i] > '9')
        {
            return ATC_NG;
        }
        
        usValue = usValue * 10 + (pSrc[i] - '0');
        if(usValue > 255)
        {
            return ATC_NG;
        }
        
    }
    
    if(0 == char_num)
    {
        return ATC_NG;
    }
    aucDesc[j++] = (unsigned char)usValue;
    *pucDescLen = j;

    return ATC_OK;
}

unsigned char ATC_ConvertIpAddrAndSubnetMask(unsigned char *pSrc, unsigned char ucLen, unsigned char aucDesc[], unsigned char* pucDescLen)
{
    if(ATC_OK != ATC_ConvertDotSeptNumParams2Char(pSrc, ucLen, 32 , aucDesc, pucDescLen))
    {
        return ATC_NG;
    }

    if(0 != *pucDescLen && 8 != *pucDescLen && 32 != *pucDescLen)
    {
        return ATC_NG;
    }

    return ATC_OK;
}


unsigned char ATC_ConvertPortRangeValue(unsigned char *pSrc, unsigned char ucLen, unsigned short ausDesc[], unsigned char* pucDescLen)
{
    if(ATC_OK != ATC_ConvertDotSeptNumParams2Short(pSrc, ucLen,2 , ausDesc, pucDescLen))
    {
        return ATC_NG;
    }

    if((0 != *pucDescLen && 2 != *pucDescLen) || (ausDesc[0] > ausDesc[1]))
    {
        return ATC_NG;
    }

    return ATC_OK;
}

void AtcAp_TpduToScts(char* pTpdu, char* pScts)
{
    pScts[0] = ((*pTpdu) & 0x0F)+'0';
    pScts[1] = (((*pTpdu) & 0xF0)>>4)+'0';
    pTpdu++;

    pScts[2] = '/';
    pScts[3] = ((*pTpdu) & 0x0F)+'0';
    pScts[4] = (((*pTpdu) & 0xF0)>>4)+'0';
    pTpdu++;

    pScts[5] = '/';
    pScts[6] = ((*pTpdu) & 0x0F)+'0';
    pScts[7] = (((*pTpdu) & 0xF0)>>4)+'0';
    pTpdu++;

    pScts[8] = ',';
    pScts[9] = ((*pTpdu) & 0x0F)+'0';
    pScts[10] = (((*pTpdu) & 0xF0)>>4)+'0';
    pTpdu++;

    pScts[11] = ':';
    pScts[12] = ((*pTpdu) & 0x0F)+'0';
    pScts[13] = ((*pTpdu & 0xF0)>>4)+'0';
    pTpdu++;

    pScts[14] = ':';
    pScts[15] = ((*pTpdu) & 0x0F)+'0';
    pScts[16] = (((*pTpdu) & 0xF0)>>4)+'0';
    pTpdu++;

    if(((*pTpdu) & 0x08) == 0)
    {
        pScts[17] = '+';
    }
    else
    {
        pScts[17] = '-';
    }
    
    pScts[18] = ((*pTpdu) & 0x07)+'0';
    pScts[19] = (((*pTpdu) & 0xF0)>>4)+'0';
}

unsigned char ATC_GET_IPV6ADDR_ALL(unsigned char ucCid, unsigned char* pucIpv6Addr)
{
    unsigned int  uiIpv6Addr[4] = { 0 };
    int i = 0;
    
    if(xy_get_Wan_Ipv6Addr2(ucCid,uiIpv6Addr) != ATC_AP_TRUE)
    {
        return ATC_AP_FALSE;
    }

    for(i = 0; i < 4; i++)
    {
        *(pucIpv6Addr+(4*i+3)) = (unsigned char)((uiIpv6Addr[i] & 0xFF000000) >> 24);
        *(pucIpv6Addr+(4*i+2)) = (unsigned char)((uiIpv6Addr[i] & 0xFF0000) >> 16);
        *(pucIpv6Addr+(4*i+1)) = (unsigned char)((uiIpv6Addr[i] & 0xFF00) >> 8);
        *(pucIpv6Addr+(4*i+0)) = (unsigned char)(uiIpv6Addr[i] & 0xFF);
    }

    return ATC_AP_TRUE;
}

void AtcAp_ConvertPlmn2NameStr(unsigned long ulPlmn, unsigned char ucFormat, unsigned char *pucPlmnName)
{
    unsigned char            i;
    unsigned char            aucPlmnNum[8] = { 0 };
    
    AtcAp_IntegerToPlmn(ulPlmn, aucPlmnNum);
    if(0 == strlen(aucPlmnNum))
    {
        return;
    }

    if(ucFormat == PLMN_FORMAT_NUMERIC)
    {
        strcpy((char*)pucPlmnName, (char*)aucPlmnNum);
        return;
    }

    for(i = 0; i < sizeof(ATC_PlmnName_Table)/sizeof(ST_ATC_AP_PLMN_NAME_TABLE); i++)
    {
        if(0 == strcmp(aucPlmnNum, ATC_PlmnName_Table[i].pucOper))
        {
            if(ucFormat == PLMN_FORMAT_LONG_ALPHA)
            {
                strcpy((char*)pucPlmnName, (char*)ATC_PlmnName_Table[i].pucLongName);
            }
            else
            {
                strcpy((char*)pucPlmnName, (char*)ATC_PlmnName_Table[i].pucShortName);
            }
            return;
        }
    }
    
    strncpy((char*)pucPlmnName, aucPlmnNum, 3);
    pucPlmnName[3] = ' ';
    strncpy((char*)pucPlmnName + 4, aucPlmnNum + 3, 3);
    pucPlmnName[7] = '\0';
}

char* AtcAp_GetPlmnStrByName(unsigned char ucOperFormat, unsigned char *pucPlmnName)
{
    unsigned char            i;

    for(i = 0; i < sizeof(ATC_PlmnName_Table)/sizeof(ST_ATC_AP_PLMN_NAME_TABLE); i++)
    {
        if(PLMN_FORMAT_LONG_ALPHA == ucOperFormat)
        {
            if(0 == strcmp(pucPlmnName, ATC_PlmnName_Table[i].pucLongName))
            {
                return (char*)ATC_PlmnName_Table[i].pucOper;
            }
        }
        else if(PLMN_FORMAT_SHORT_ALPHA == ucOperFormat)
        {
            if(0 == strcmp(pucPlmnName, ATC_PlmnName_Table[i].pucShortName))
            {
                return (char*)ATC_PlmnName_Table[i].pucOper;
            }
        }
    }

    return (char*)NULL;
}

unsigned char AtcAp_ConvertOperStr2Hex(unsigned char *pucOperStr, unsigned char* pucOper, unsigned char ucSize)
{
    unsigned char aucOperData[6] = { 0 };
    unsigned char i;
    unsigned char ucOperStrLen;
    
    for (i = 0; i < 3; i++)
    {
        pucOper[i] = 0;
    }

    ucOperStrLen = strlen((char*)pucOperStr);
    if((ucOperStrLen != 5 && ucOperStrLen != 6) || ucSize < 3)
    {
        return ATC_AP_FALSE;
    }
    
    for (i = 0; i < ucOperStrLen; i++)
    {
        if ((pucOperStr[i] >= '0') && (pucOperStr[i] <= '9'))
        {
            aucOperData[i] = (unsigned char)(pucOperStr[i] - '0');
        }
        else if ((pucOperStr[i] >= 'A') && (pucOperStr[i] <= 'F'))
        {
            aucOperData[i] = (unsigned char)((pucOperStr[i] - 'A') + 10);
        }
        else if ((pucOperStr[i] >= 'a') && (pucOperStr[i] <= 'f'))
        {
            aucOperData[i] = (unsigned char)((pucOperStr[i] - 'a') + 10);
        }
        else
        {
            return ATC_AP_FALSE;
        }

    }
    pucOper[0] |= (aucOperData[1] & 0x0F) << 4;
    pucOper[0] |= (aucOperData[0] & 0x0F);
    if (5 == ucOperStrLen)
    {
        pucOper[1] |= 0xF0;
    }
    else
    {
        pucOper[1] |= (aucOperData[5] & 0x0F) << 4;
    }
    pucOper[1] |= (aucOperData[2] & 0x0F);
    pucOper[2] |= (aucOperData[4] & 0x0F) << 4;
    pucOper[2] |= (aucOperData[3] & 0x0F);

    return ATC_AP_TRUE;
}

unsigned short api_GetWriteSDVolt()
{
    if(g_WriteSDVolt > 5000)
    {
        return 0x7FFF;
    }
    else
    {
        return g_WriteSDVolt;
    }
}

ST_ATC_ASCII_ESCAPE ATC_PARAM_ASCII_ESCAPE_TABLE[] =
{
    {'a', '\a'}, 
    {'b', '\b'},
    {'f', '\f'},
    {'n', '\n'},
    {'r', '\r'},
    {'t', '\t'},
    {'v', '\v'},
    {'?', '\?'},
    {'\\', '\\'},
    {'\'', '\''},
    {'\"', '\"'},
};

/* "AB235E"---->AB235E(3 BYTES) */
int Atc_HexStrToHexDigit(char* src, int src_len, uint32_t* dst)
{
    int i;

    if (src ==  NULL || dst == NULL || src_len < 0) 
    {
        xy_assert(0);
    }

    for (i = 0; i < src_len; i ++) 
    {
        if(*src >= 'a' && *src <= 'f')
        {
            *dst |= ((*src - 'a') + 10);
        }
        else if (*src >= '0' && *src <= '9') 
        {
            *dst |= (*src - '0');
        } 
        else if (*src >= 'A' && *src <= 'F') 
        {
            *dst |= ((*src - 'A') + 10);
        } 
        else 
        {
            return ATC_AP_FALSE;
        }
        src++;
        
        if(i+1 < src_len)
        {
            *dst <<=4;
        }
        
    }

    return ATC_AP_TRUE;
}

/* "0102030405060708090a0b0c0d0e0f"---->0102030405060708090a0b0c0d0e0f */
unsigned char Atc_HexStrToHexDigit_LongStr(unsigned char* src, int src_len, unsigned char* dst)
{
    int i = 0;
    unsigned int  uiHexValue = 0;
    for(i = 0;i < (src_len / 2); i++)
    {
        if(Atc_HexStrToHexDigit(src,2,&uiHexValue) == ATC_AP_FALSE)
        {
            return ATC_AP_FALSE;
        }
        dst[i] = uiHexValue;
        uiHexValue = 0;
        src = src + 2;
    }
    return ATC_AP_TRUE;
}


/* 获取特殊字符一共有多少个 */
static uint8_t Atc_GetChrNumInStr(char *str, char chr)
{
    uint8_t chr_num = 0;
    char *chr_addr = NULL;
    while(*str != '\0')
    {
        if((chr_addr = strchr(str, chr)) != NULL)
        {
            chr_num++;
            str = chr_addr + 1;
        }
        else
            break;
    }
    return chr_num;
}

/* 获取%d后面() []的int型参数范围 */
static void Atc_GetParamValidRange(char *fmt, char *required_flag, char *type, int *size, ST_ATC_CMD_PARAM_RANG *range)
{
    char len_str[6] = {0};
    char *left_bracket = NULL;
    char *right_bracket = NULL;
    char *left_square_bracket = NULL;
    char *right_square_bracket = NULL;
    char *mid_line = NULL;
    uint8_t break_num = 0;        //括号内参数合法值分隔符'|'的个数

    if(strlen(fmt) > 2)
    {
        left_bracket = strchr(fmt, '(');
        right_bracket = strchr(fmt, ')');
        left_square_bracket = strchr(fmt, '[');
        right_square_bracket = strchr(fmt, ']');
        if((left_bracket == NULL && right_bracket == NULL) && (left_square_bracket == NULL && right_square_bracket== NULL))
        {
            *type = *(fmt + strlen(fmt) - 1);
            strncpy(len_str, fmt + 1, strlen(fmt) - 2);
            *size = (int)strtol(len_str,NULL,10);
        }
        else if(((left_bracket != NULL && right_bracket != NULL) && (left_square_bracket == NULL && right_square_bracket== NULL)) || \
            ((left_bracket == NULL && right_bracket == NULL) && (left_square_bracket != NULL && right_square_bracket != NULL)))
        {
            if(left_bracket !=NULL)
            {
                *required_flag = 1;
            }
            else
            {
                left_bracket = left_square_bracket;
                right_bracket = right_square_bracket;
            }
            *left_bracket = '\0';
            *type = *(fmt + strlen(fmt) - 1);
            mid_line = strchr(left_bracket + 1, '-');
            break_num = Atc_GetChrNumInStr(left_bracket + 1, '|');

            xy_assert(left_bracket < right_bracket);
            if(*type != 'd' && *type != 'D' && *type != 'l' && *type != 'L' && *type != 'u' && *type != 'U'&& *type != 'h' && *type != 'H')    //对于%s、%h、%p、%h类型的参数只能输()或[]表示必选和可选，括号中间不能有其他字符
                xy_assert((left_bracket + 1) == right_bracket);    

            if(mid_line != NULL && break_num == 0)            //定义了上下限,例如(0-100),[2-10]
            {
                range->range_type = 1;
                xy_assert(mid_line < right_bracket);
                *right_bracket = '\0';
                if(mid_line - left_bracket > 1)
                    range->min_limit = (int)strtol(left_bracket + 1,NULL,0);        //min_limit至少为0
                if(right_bracket - mid_line > 1)
                    range->max_limit = (int)strtol(mid_line + 1,NULL,0);        //不输上限时，max_limit为0x0fffffff,-)间必须输有数字，否则max_limit解析为0

                if(range->min_limit > range->max_limit)
                {
                    xy_assert(0);
                }
            }
            else if(mid_line == NULL && break_num != 0)        //定义了合法值枚举，例如（0|1|2|4）,[3|5|7]
            {
                range->range_type = 2;
                range->valid_value_start = left_bracket + 1;
            }
            else
            {
                if((left_bracket + 1) == right_bracket)
                    range->range_type = 1;            //%d()表示必选参数没有范围限制，括号之间没有任何字符，表示为无符号类型的最大范围。示例：%1d() 表示 0-255
            }

            if(strlen(fmt) > 2)
            {
                strncpy(len_str, fmt + 1, strlen(fmt) - 2);
                *size = (int)strtol(len_str,NULL,10);
            }
        }
        else
        {
            xy_assert(0);
        }
    }
    else
    {
        *type = *(fmt + strlen(fmt) - 1);
    }
}

static uint8_t Atc_DigitStrCheck(char *str)
{
    unsigned digit_len = 0;

    if(*str == '-'){
        str++;
    }
    while(D_ATC_PARAM_DIGIT_CHECK((int)(*str))){
        str++, digit_len++;
    }
    while(*str == ' '){
        str++;
    }
    return *str == '\0' && digit_len > 0;
}

static uint8_t Atc_HexStrCheck(char *str, uint8_t check_head)
{
    uint16_t digit_len = 0;

    if(check_head)
    {
        if(strncmp(str,"0x",2) == 0 || strncmp(str,"0X",2) == 0)
            str += 2;
        else
            return ATC_FALSE;
    }

    while(D_ATC_PARAM_HEX_CHECK((int)(*str)))
    {
        str++;
        digit_len++;
    }

    while(*str == ' ')
    {
        str++;
    }

    return (*str == '\0' && digit_len > 0);    
}

static uint8_t Atc_CheckParamInValidRang(uint32_t  val_trans, ST_ATC_CMD_PARAM_RANG *param_range)
{
    uint8_t ret = ATC_TRUE;

    if(param_range->range_type == 1 && val_trans == param_range->max_limit)
    {
        return ATC_TRUE; /* for value == (max = 0xFFFFFFFF) */
    }
    else if((param_range->range_type == 1 && (int)val_trans < 0) || val_trans < param_range->min_limit || val_trans > param_range->max_limit)
        ret = ATC_FALSE;
    else if(param_range->range_type ==2) 
    {
        uint32_t valid_value = 0;
        char *value_head = param_range->valid_value_start;
        char *value_end = strchr(value_head, '|');
        while(1)
        {
            valid_value = (int)strtol(value_head,NULL,0);
            if(val_trans == valid_value)
                break;
            else if(value_end != NULL)
            {
                value_head = value_end + 1; 
                value_end = strchr(value_head, '|');
            }
            else
            {
                ret = ATC_FALSE;
                break;
            }
        }
    }
    return ret;
}

static uint8_t Atc_HexCharCheck(char ch)
{
    if ((ch >= '0' && ch <= '9') || (ch >= 'a' && ch <= 'f') || (ch >= 'A' && ch <= 'F'))
        return ATC_TRUE;
    else
        return ATC_FALSE;
}

static char covertHextoNum(char ch) //  otc
{
    char temp=0;
    if (D_ATC_PARAM_DIGIT_CHECK(ch))
        temp = (ch - '0');

    if (D_ATC_PARAM_ALPHA_CHECK(ch))
        temp = (D_ATC_PARAM_UPPER_CHECK(ch) ? ch - 55 : ch - 87);
    return (char)temp;
}

static uint8_t isOctChar(char ch)
{
    if ((ch >= '0' && ch <= '7'))
        return ATC_TRUE;
    else 
        return ATC_FALSE;
}

static int covertEscToAscII(char* p)
{
    int len = -1;
    int index_val = *(p + 1);
    int i = 0;
    for (; i < (int)(sizeof(ATC_PARAM_ASCII_ESCAPE_TABLE) / sizeof(ST_ATC_ASCII_ESCAPE)); i++)
    {
        if (index_val == ATC_PARAM_ASCII_ESCAPE_TABLE[i].ch)
        {
            *p = ATC_PARAM_ASCII_ESCAPE_TABLE[i].esc_ch;
            xy_printf(0,ATC_AP_T, DEBUG_LOG, "match esc table and changed:%d", *p);
            //printf_xy("match esc table and changed:%d", *p);
            return 1;
        }
    }

    //Hex
    if(index_val == 'x')
    {
        if(Atc_HexCharCheck(*(p + 2)) && Atc_HexCharCheck(*(p + 3)))
        {
            *p = (covertHextoNum(*(p + 2)) << 4) + covertHextoNum(*(p + 3));
            xy_printf(0,ATC_AP_T, DEBUG_LOG, "hex changed:%d", *p);
            //printf_xy("hex changed:%d", *p);
            return 3;
        }
        else if(Atc_HexCharCheck(*(p + 2)))
        {
            *p = covertHextoNum(*(p + 2));
            xy_printf(0,ATC_AP_T, DEBUG_LOG, "hex changed:%d", *p);
            //printf_xy("hex changed:%d", *p);
            return 2;            
        }
    }

    //Octal eg:/101
    if (isOctChar(*(p + 1)) && isOctChar(*(p + 2)) && isOctChar(*(p + 3)))
    {
        *p = (char)(((*(p + 1) - '0') << 6) + ((*(p + 2) - '0') << 3) + (*(p + 3) - '0'));
        xy_printf(0,ATC_AP_T, DEBUG_LOG, "oct changed:%d", *p);
        //printf_xy("oct changed:%d", *p);
        return 3;
    }
    else if (isOctChar(*(p + 1)) && isOctChar(*(p + 2)))
    {
        *p = (char)(((*(p + 1) - '0') << 3) + (*(p + 2) - '0'));
        xy_printf(0,ATC_AP_T, DEBUG_LOG, "oct changed:%d", *p);
        //printf_xy("oct changed:%d", *p);
        return 2;
    }
    else if (isOctChar(*(p + 1)))
    {
        *p = (char)(*(p + 1) - '0');
        xy_printf(0,ATC_AP_T, DEBUG_LOG, "oct changed:%d", *p);
        //printf_xy("oct changed:%d", *p);
        return 1;
    }

    return len;
}

static void Atc_format_escape_char(char *input)
{    
    char *p = input;
    int offset = 0;
    unsigned int i = 0;

    xy_assert(input != NULL);

    while ((p = strchr(p, '\\')) != NULL && *(p + 1) != '\0')
    {
        if ((offset = covertEscToAscII(p)) != -1)
        {
            if(strlen(p + offset + 1) + 1 < (unsigned int)(offset)) //eg: \101a --> Aa,  原先1a位置必须置0
            {
                memset(p + 1, '\0', (unsigned int)(offset));
            }    
            for (i = 0; i <= strlen(p + offset + 1); i++)
            {
                *(p + 1 + i) = *(p + offset + 1 + i);
            }
        }
        p = p + 1;
    };
}

/* 根据形参解析具体参数值    */
static int Atc_ParamParseInType(char *str, char *fmt, ST_ATC_CMD_DATA_PARSE *at_data, int* arg, int flag, unsigned char lastflg, va_list *ap)
{
    int ret = ATC_AP_TRUE;
    int size = 0;
    char type = 0;
    char required_flag = 0;
    ST_ATC_CMD_PARAM_RANG param_range = {0};
    uint32_t val_trans = 0;
    uint8_t * pu8FlagTmp = NULL;
    param_range.max_limit = 0xffffffff;
    while(*fmt == ' ')
        fmt++;

    //no valid param value
    if (strlen(fmt) == 0)
    {
        if(strlen(str) != 0)
        {
            ret = ATC_AP_FALSE;
        }
        return ret;
    }
    else
    {
        Atc_GetParamValidRange(fmt, &required_flag, &type, &size, &param_range);
        if(ATC_AP_TRUE == lastflg  && 1 == required_flag)
        {
            ret = ATC_AP_FALSE;
        }
        while (*str == ' ' && type != 's' && type != 'S')
            str++;
        // xy_printf(0,ATC_AP_T,INFO_LOG,"Atc_ParamParseInType: type=%d, str=%s", type, str);
        if(*(str-1) == '"' && ATC_AP_FALSE == lastflg && (type == 'd' || type == 'D'))
        {
            return ATC_AP_FALSE;
        }
        if (strlen(str) == 0)
        {      
            /* "" */
            if(*(str-1) == '"' && (type == 'c' || type == 'C'))/* "" */
            {
                if(required_flag == 0)
                {
                    pu8FlagTmp = (uint8_t *)va_arg(*ap, uintptr_t);
                    if(pu8FlagTmp!=NULL)
                    {
                        *pu8FlagTmp = 1;
                    }                
                }
                at_data->point_flag = 1;
                *((uintptr_t *)(va_arg(*ap, uintptr_t))) = (uintptr_t)str;
            }
            else/* NULL */
            {
                if(required_flag == 0)/*opt： set optflag*/
                {
                    pu8FlagTmp = (uint8_t *)va_arg(*ap, uintptr_t);
                    if(pu8FlagTmp!=NULL)
                    {
                        *pu8FlagTmp = 0;
                    }  
                    va_arg(*ap, uintptr_t);
                }
                else /* mamdantory： Null report error */
                {
                    ret = ATC_AP_FALSE;
                    
                }
             }
            return ret;
        }
        else/* not NULL */
        {
            if(required_flag == 0)
            {
                pu8FlagTmp = (uint8_t *)va_arg(*ap, uintptr_t);
                if(pu8FlagTmp!=NULL)
                {
                    *pu8FlagTmp = 1;
                }                
            }            
        }
    }

    at_data->point_flag = 0;

    if(type == 'd' || type == 'D' || type == 'l' || type == 'L' || type == 'u' || type == 'U')
    {
        if (!Atc_DigitStrCheck(str) && !Atc_HexStrCheck(str, 1))
        {
            ret = ATC_AP_FALSE;
        }
        else if (size == 0 || size == 4)
        {
            *((uint32_t *)(va_arg(*ap, uintptr_t))) = (uint32_t)strtoul(str, NULL, 0);
            val_trans = (uint32_t)strtoul(str, NULL, 0);
            if (flag == D_ATC_PARAM_PARSE_ESC && arg != NULL)
                *arg += 1;
        }
        else if (size == 1)
        {
            
            *((uint8_t *)(va_arg(*ap, uintptr_t))) = (uint8_t)strtoul(str, NULL, 0);
            val_trans = (uint32_t)strtoul(str,NULL,0);
            if (flag == D_ATC_PARAM_PARSE_ESC && arg != NULL)
                *arg += 1;

            if(param_range.range_type == 1 && param_range.max_limit > (unsigned int)0xff)
            {
                param_range.max_limit = 0xff;
            }
        }
        else if (size == 2)
        {
            *((uint16_t *)(va_arg(*ap, uintptr_t))) = (uint16_t)strtoul(str, NULL, 0);
            val_trans = (uint32_t)strtoul(str, NULL, 0);
            if (flag == D_ATC_PARAM_PARSE_ESC && arg != NULL)
                *arg += 1;

            if(param_range.range_type == 1 && param_range.max_limit> (unsigned int)0xffff)
            {
                param_range.max_limit = 0xffff;
            }
        }
        else
        {
            xy_assert(0);
        }

        if (type == 'l' || type == 'L')
        {
            at_data->input_len = val_trans;
        }
        if (Atc_CheckParamInValidRang(val_trans, &param_range) == ATC_FALSE)
            ret = ATC_AP_FALSE;
    }
    else if (type == 's' || type == 'S')
    {
        if (size == 0)
        {
            if (flag == D_ATC_PARAM_PARSE_ESC)
            {
                Atc_format_escape_char(str);
                if (arg != NULL)
                    *arg += 1;
            }
            strcpy((char *)(va_arg(*ap, uintptr_t)), str);
        }
        else
        {
            if (size <= (int)(strlen(str)))
            {
                ret = ATC_AP_FALSE;
            }
            else
            {
                int cpy_len;
                char *var_dst;
                if (flag == D_ATC_PARAM_PARSE_ESC)
                {
                    Atc_format_escape_char(str);
                    if (arg != NULL)
                        *arg += 1;
                }
                cpy_len = (int)(strlen(str));
                var_dst = (char *)(va_arg(*ap, uintptr_t));
                strncpy(var_dst, str, cpy_len);
                *(char *)(var_dst + cpy_len) = '\0';
            }
        }
    }
    else if ((type == 'p' || type == 'P'))
    {
        at_data->point_flag = 1;
        if(at_data->data_type == D_ATC_PARAM_FMT_POINT)
        {
            at_data->data_len = strlen(str);
        }

        if(size != 0 && size <= (int)strlen(str))
        {
            ret = ATC_AP_FALSE;
            return ret;
        }
        if(strlen(str) == 0)
        {
            va_arg(*ap, uintptr_t);
        }
        else
        {
            *((uintptr_t *)(va_arg(*ap, uintptr_t))) = (uintptr_t)str;
        }
    }
    else if ((type == 'c' || type == 'C'))
    {
        at_data->point_flag = 1;
        if(at_data->data_type == D_ATC_PARAM_FMT_POINT)
        {
            at_data->data_len = strlen(str);
        }

        if(size != 0 && size <= (int)strlen(str))
        {
            ret = ATC_AP_FALSE;
            return ret;
        }

        *((uintptr_t *)(va_arg(*ap, uintptr_t))) = (uintptr_t)str;
    }
    else if(type == 'h' || type == 'H')
    {
        xy_assert(size <= 8);//unit_32
        at_data->data_len = strlen(str);
        if(at_data->data_len > 2 && *str == '0' && *(str+1) == 'x')
        {
            at_data->data_len -= 2;
            str+=2;
        }
        if(at_data->data_len > size)
        {
            ret = ATC_AP_FALSE;
            return ret;
        }

        //if(at_data->data_type != D_ATC_PARAM_FMT_HEX)    //没有%l时，必须为类似%32h带长度限制的格式，不带长度则断言，此时入参长度必须为固定值32，否则报错
        //{
        //    xy_assert(size != 0);
        //    if(at_data->data_len != size)
        //    {
        //        ret = ATC_AP_FALSE;
        //        return ret;
        //    }
        //}
        {
            uint32_t valTmp = 0;
            if(Atc_HexStrToHexDigit(str, at_data->data_len, &valTmp) == ATC_AP_FALSE)
            {
                ret = ATC_AP_FALSE;
            }
            else if (Atc_CheckParamInValidRang(valTmp, &param_range) == ATC_FALSE)
            {
                ret = ATC_AP_FALSE;
            }
            else
            {
                if(size >4)//u32
                {
                    *((uint32_t *)(va_arg(*ap, uintptr_t))) = valTmp;
                }
                else if(size >2)//u16
                {
                    *((uint16_t *)(va_arg(*ap, uintptr_t))) = (uint16_t)valTmp;
                }
                else//u8
                {
                    *((uint8_t *)(va_arg(*ap, uintptr_t))) = (uint8_t)valTmp;
                }
            }
            
        }      
        //if(Atc_HexStrToHexDigit(str, at_data->data_len, (char *)(va_arg(*ap, uintptr_t)), at_data->data_len/2) == -1)
        //    ret = ATC_AP_FALSE;
    }
    else if(type == 'f' || type == 'F')
    {
        *((double *)(va_arg(*ap, uintptr_t))) = strtod(str, NULL);
    }
    else
    {
        xy_assert(0);
        return ret;
    }

    return ret;
}

static uint8_t Atc_GetParamFmtType(char *fmt_param)
{
    uint8_t lenfmt_num = Atc_GetChrNumInStr(fmt_param, 'l') + Atc_GetChrNumInStr(fmt_param, 'L');
    uint8_t hexfmt_num = Atc_GetChrNumInStr(fmt_param, 'h') + Atc_GetChrNumInStr(fmt_param, 'H');
    uint8_t pointfmt_num = Atc_GetChrNumInStr(fmt_param, 'p') + Atc_GetChrNumInStr(fmt_param, 'P');

    if(lenfmt_num == 0)
    {
        return D_ATC_PARAM_FMT_NORMAL;
    }
    else if(lenfmt_num == 1)
    {
        if(hexfmt_num == 1)
        {
            return D_ATC_PARAM_FMT_HEX;
        }
        else if(hexfmt_num == 0 && pointfmt_num == 1)
        {
            return D_ATC_PARAM_FMT_POINT;
        }
    }

    return D_ATC_PARAM_FMT_INVALID;
}

static char* Atc_FindNextDoubleQuato(char* data)
{
    char *tmp = NULL;

    xy_assert(data != NULL);

    while(*data != '\0')
    {
        if(*data == '"' && *(data - 1) != '\\')
        {
            tmp = data;
            break;
        }
        data++;
    }

    return tmp;
}

//参数被“”包围的时候，从'“'的下一个字符开始，找与之匹配的'”'
static char *Atc_GetQuatoEnd(char *quote_next)
{
    uint8_t quote_num = 0;
    char *comma = NULL;
    char *buf = quote_next;

    while(*buf != '\0')
    {
        if((comma = strchr(buf, ',')) != NULL)            //参数内部或末尾有','
        {
            *comma = '\0';
            quote_num = Atc_GetChrNumInStr(buf, '"');
            *comma = ',';
            if(quote_num % 2 == 0)    //','前面的引号都是成对出现的，说明没有找到与参数起始上引号匹配的下引号
            {
                buf = comma + 1;
                continue;
            }
            else if(*(comma - 1) == '"')
                return (comma - 1);
            else
                return NULL;
        }
        else
        {
            quote_num = Atc_GetChrNumInStr(buf, '"');
            if(quote_num % 2 != 0 && *(buf + strlen(buf) - 1) == '"')
                return (buf + strlen(buf) - 1);
            else
                return NULL;
        }
    }
    return NULL;
}

static unsigned char Atc_ParamParse(char *fmt_parm, char *buf, int is_strict, int* arg, int flag, va_list *ap)
{
    unsigned char ret = ATC_AP_TRUE;
    unsigned char lastflg = ATC_AP_FALSE;
    char *param_comma = NULL;
    char *param_quotes_end = NULL;
    char *fmt_comma = NULL;
    char *param_end = NULL;
    int fmt_len = strlen(fmt_parm);
    char *fmt_original = AtcAp_Malloc(fmt_len + 1);
    char *fmt = fmt_original;
    ST_ATC_CMD_DATA_PARSE at_data = {0};

    xy_assert(fmt_parm != NULL && buf != NULL);
    *(fmt_original + fmt_len) = '\0';
    strncpy(fmt, fmt_parm, fmt_len);
    at_data.data_type = Atc_GetParamFmtType(fmt);

    if(at_data.data_type == D_ATC_PARAM_FMT_INVALID)
    {
        ret = ATC_AP_FALSE;
        goto END;
    }

    while (*buf == ' ')
        buf++;

    param_end = strchr(buf, '\r');
    if (param_end != NULL)
        *param_end = '\0';

    while (*buf != '\0' || *fmt != '\0')
    {
        // xy_printf(0,ATC_AP_T,INFO_LOG,"Atc_ParamParse: fmt_comma=%s, param_comma=%s", fmt, buf);
        param_comma = strchr(buf, ',');
        fmt_comma = strchr(fmt, ',');
        param_quotes_end = NULL;

        //如果参数带引号，则去掉引号
        if (*buf == '"')
        {
            buf++;
            if (flag == D_ATC_PARAM_PARSE_ESC)
                param_quotes_end = Atc_FindNextDoubleQuato(buf);
            else
                param_quotes_end = Atc_GetQuatoEnd(buf);
            //只有左引号，没有右引号
            if (param_quotes_end == NULL)
            {
                ret = ATC_AP_FALSE;
                break;
            }

            param_comma = strchr(param_quotes_end + 1, ',');
            *param_quotes_end = '\0';
        }
        else if(*buf == ' ')
        {
            ret = ATC_AP_FALSE;
            break;
        }
        /* AT命令参数字符串和fmt格式字符串后续都有逗号，则强行将逗号改为'\0'，待处理完当前参数后再恢复 */
        if (param_comma && fmt_comma)
        {
            *param_comma = '\0';
            *fmt_comma = '\0';

            if ((ret = Atc_ParamParseInType(buf, fmt, &at_data, arg, flag, lastflg, ap)) != ATC_AP_TRUE)
                break;
            *fmt_comma = ',';
            /* 若为%p，则外部用户会直接访问对应字符串，进而不能把尾部的'\0'恢复正常；该行为强行修改了入参AT字符串，考虑到该字符串仅在对应的AT命令解析函数中有生命，问题不大 */
            if(at_data.point_flag == 0)
            {
                *param_comma = ',';
                if (param_quotes_end)
                    *param_quotes_end = '"';
            }
            buf = param_comma + 1;
            fmt = fmt_comma + 1;
        }
        /* AT命令参数字符串后续有逗号，而fmt格式字符串已是最后参数时 */
        else if (param_comma)
        {    
            *param_comma = '\0';
            ret = Atc_ParamParseInType(buf, fmt, &at_data, arg, flag, lastflg, ap);

            //too many param
            if (ret == ATC_AP_TRUE && is_strict)
                ret = ATC_AP_PARAM_OVER;
            break;
        }
        /* fmt格式字符串后续有逗号，而AT命令参数字符串已是最后参数时 */
        else if (fmt_comma)
        {
            *fmt_comma = '\0';
            ret = Atc_ParamParseInType(buf, fmt, &at_data, arg, flag, lastflg, ap);
            if(ret == ATC_AP_TRUE)
            {
                buf = buf + strlen(buf);
                if(param_quotes_end)
                {
                    buf++;
                    if(at_data.point_flag == 0)
                        *param_quotes_end = '"';
                }
                *fmt_comma = ',';
                fmt = fmt_comma + 1;
                lastflg = ATC_AP_TRUE;
            }
            else
                break;
        }
        /* AT命令参数字符串和fmt格式字符串皆已解析到最后一个参数时 */
        else
        {
            ret = Atc_ParamParseInType(buf, fmt, &at_data, arg, flag, lastflg, ap);
            break;
        }
    }
    //AT字符串携带的长度值与对应的字符串实际传输长度不一致，报错；该类型错误往往是测试人员故意制造的错误AT命令
    if((at_data.data_type == D_ATC_PARAM_FMT_POINT && at_data.input_len != at_data.data_len && at_data.input_len * 2 != at_data.data_len) || (at_data.data_type == D_ATC_PARAM_FMT_HEX && at_data.input_len * 2 != at_data.data_len))
    {
        ret = ATC_AP_FALSE;
    }

    if(ret != ATC_AP_TRUE)
    {
        xy_printf(0,ATC_AP_T,INFO_LOG,"Atc_ParamParse: fmt_comma:%s, param_comma:%s", fmt, buf);
    }
    
END:
    if(at_data.point_flag == 0)
    {
        if (param_end)
            *param_end = '\r';
        if (param_comma)
            *param_comma = ',';
        if (param_quotes_end)
            *param_quotes_end = '"';
    }
    AtcAp_Free(fmt_original);

    return ret;
}

//对于整形参数，%d后面加()表示参数必选；[]表示参数可选；且参数值一定为正整形，括号内可选通过-来设置上下限值，上限值可缺省
//vl参数列表的第一个参数为bitmap指示对应位置上是否有设置值,注：部分命令与结构体的bitmap顺序有绑定关系，需要注意
unsigned char AtcAp_CmdParamPrase(char *fmt, unsigned char *buf, ...)
{
    unsigned char ret = ATC_AP_TRUE;
    va_list vl;

    if(NULL == fmt || NULL == buf)
    {
        return ATC_AP_FALSE;
    }
    
    va_start(vl,buf);

    while (*buf == ' ')
        buf++;

    ret = Atc_ParamParse(fmt, (unsigned char*)buf, 1, NULL, D_ATC_PARAM_PARSE_DEFAULT, &vl);

    va_end(vl);
    return ret;
}

//不区分大小写的子父字符串比较，其中n表示仅匹配父字符串前n字节
uint8_t AtcAp_StrCaseCmpWithNum(const char *s1, const char *s2, int n)
{
    int ch1 = 0;
    int ch2 = 0;

    if(NULL == s1 || NULL == s2 || n<1)
    {
        return 0;
    }

    do
    {
        if((ch1 = *(unsigned char *)s1++) >= 'a' && (ch1 <= 'z'))
        {
            ch1 -= D_ATC_DIFF_VALUE;
        }
        if((ch2 = *(unsigned char *)s2++) >= 'a' && (ch2 <= 'z'))
        {
            ch2 -= D_ATC_DIFF_VALUE;
        }
    }while(--n && ch1 && (ch1 == ch2));

    if(ch1 == ch2)
        return 1;
    else
        return 0;
}

//make sure get correct prefix,for example :AT+CGSN=1;+CIMI;+CGSN?, prefix should be AT+CGSN
static char* Atc_CmdGetPrefix4comb(char* start, char* end, uint8_t* type)
{
    char *tmp = start;
    xy_assert(start != NULL && end != NULL && start < end);
    while(tmp < end)
    {
        if (*tmp == '=')
        {
            if (*(tmp + 1) == '?')
            {
                *type = D_ATC_CMD_TEST;
            }
            else
            {
                *type = D_ATC_CMD_REQ;
            }
            return tmp;
        }
        else if (*tmp == '?')
        {
            *type = D_ATC_CMD_REQ;
            return tmp;
        }
        tmp++;
    };

    if (*tmp == ';')
    {
        //example: AT+CIMI;+CGEQOS=0,9;+CGSN=1;+CGACT?
        *type = D_ATC_CMD_ACTIVE;
        return tmp;
    }
    
    return NULL;
}

//获取的前缀at_prefix里不包含头尾标识，即'+''*''=''?';最终结果为“NRB”"WORKLOCK""ATI""AT"等
char *AtcAp_CmdGetPrefixAndType(char *at_cmd, char *at_prefix, uint8_t *type)
{
    char *head = NULL;
    char *end;
    char *param = NULL;
    uint8_t atcmd_type = D_ATC_CMD_INVALID;
    int at_prefix_len = 0; 

    while (*at_cmd == '\r' || *at_cmd == '\n')
        at_cmd++;
    
    //include param
    if (AtcAp_StrCaseCmpWithNum(at_cmd, "AT", 2))
    {
        char *temp = at_cmd + 2;
        if('+' == (*temp))
        {
            g_AtcApInfo.ucATSymbolType = D_ATC_CMD_PLUS;
            head = temp + 1;
            if ((end = strchr(head, '=')) != NULL && *(end+1) == '?' && *(end+2) == '\r')
            {
                atcmd_type = D_ATC_CMD_TEST;
                param = end + 1;
            }
            else if((end = strchr(head, '?')) != NULL && *(end+1) == '\r')
            {
                atcmd_type = D_ATC_CMD_QUERY;
                param = end + 1;
            }
            else if((end = strchr(head, '=')) != NULL)
            {
                atcmd_type = D_ATC_CMD_REQ;
                param = end + 1;
            }
            else if ((end = strchr(head, '\r')) != NULL)
            {
                atcmd_type = D_ATC_CMD_ACTIVE;
                param = end;
            }
            else
                xy_assert(0);
                
            at_prefix_len = end - head;
        }
        else if('*' == (*temp))
        {
            g_AtcApInfo.ucATSymbolType = D_ATC_CMD_STAR;
            head = temp + 1;
            if ((end = strchr(head, '=')) != NULL && *(end+1) == '?' && *(end+2) == '\r')
            {
                atcmd_type = D_ATC_CMD_TEST;
                param = end + 1;
            }
            else if((end = strchr(head, '?')) != NULL && *(end+1) == '\r')
            {
                atcmd_type = D_ATC_CMD_QUERY;
                param = end + 1;
            }
            else if((end = strchr(head, '=')) != NULL)
            {
                atcmd_type = D_ATC_CMD_REQ;
                param = end + 1;
            }
            else if ((end = strchr(head, '\r')) != NULL)
            {
                atcmd_type = D_ATC_CMD_ACTIVE;
                param = end;
            }
            else
                xy_assert(0);
                
            at_prefix_len = end - head;
        }
        else if ('&' == (*temp))
        {
            g_AtcApInfo.ucATSymbolType = D_ATC_CMD_AND;
            /* eg: AT&W[0] */
            head = temp + 1;
            if((end = strchr(head, '\r')) != NULL)
            {
                if ((end - head) > 1)
                {
                    param = head + 1;
                }
                else
                {
                    param = end;
                }
            }
            else
            {
                xy_assert(0);
            }
            atcmd_type = D_ATC_CMD_SYMBOL_REQ;
            at_prefix_len = end - head;
        }
        else if (D_ATC_PARAM_ALPHA_CHECK((int)(*temp))) 
        {
            g_AtcApInfo.ucATSymbolType = D_ATC_CMD_NULL;
            /* eg: ATZ */
            head = temp;
            if((end = strchr(head, '\r')) != NULL)
            {
                if ((end - head) > 1)
                {
                    param = head;
                }
                else
                {
                    param = end;
                }
            }
            else
            {
                xy_assert(0);
            }
                atcmd_type = D_ATC_CMD_SINGLE_REQ;
                at_prefix_len = end - head;
        }
        else if('\r' == *temp)   //eg: AT
        {
            g_AtcApInfo.ucATSymbolType = D_ATC_CMD_NULL;
            atcmd_type = D_ATC_CMD_AT_REQ;
            at_prefix_len = 0;
        }
        else
            param = NULL;

        if(at_prefix_len > 0 && head != NULL && at_prefix_len < D_ATC_CMD_PREFIX_MAX_LEN)
        {
            strncpy(at_prefix, head, at_prefix_len);
        }
        else
        {
            param = NULL;
        }
    }
    else
    {
        param = NULL;
    }

    if (type != NULL)
        *type = atcmd_type;
    return param;
}

//不区分大小写的字符串比较，1表示一样，例如“IPV6” “ipv6”
uint8_t AtcAp_StrCaseCmp(const char *s1, const char *s2)
{
    int ch1 = 0;
    int ch2 = 0;

    if(NULL == s1 || NULL == s2)
    {
        return 0;
    }

    do
    {
        if((ch1 = *(unsigned char *)s1++) >= 'a' && (ch1 <= 'z'))
        {
            ch1 -= D_ATC_DIFF_VALUE;
        }
        if((ch2 = *(unsigned char *)s2++) >= 'a' && (ch2 <= 'z'))
        {
            ch2 -= D_ATC_DIFF_VALUE;
        }
    }while(ch1 && (ch1 == ch2));

    if(ch1 == ch2)
        return 1;
    else
        return 0;
}

/* 仅用于参数解析时的第几个关键字符匹配，如逗号、双引号等，需要关注n取值正确 */
char * Atc_StrnChr(char *s, int c, int n)
{
    char *match = NULL;
    int i = 0;

    do {
        if (*s == (char)c) 
        {
            i++;
            if (i == n) 
            {
                match = (char *)s;
                break;
            }
        }
    } while (*s++);

    return match;
}

unsigned char AtcAp_FindNextComma(unsigned char* pStrCmd)
{
    unsigned char  i = 0;
    for(i = 0;i < strlen(pStrCmd);i++)
    {
        if(*(pStrCmd + i) == D_ATC_N_COMMA)
        {
            return i + 1;
        }
    }
    return ATC_FALSE;
}

unsigned char AtcAp_ParseQEHPLMNData(unsigned char* pucData, ST_ATC_QEHPLMN_PARAMETER* pParam)
{
    char* pucPlmnStr;
    unsigned char i, ucPlmnStrLen;
    unsigned long ulPlmn;
    char*         pucDesData;
    
    if(NULL == pucData || pucData[0] == ',' || NULL != strstr((char*)pucData, ",,"))
    {
        return ATC_FALSE;
    }

    pucPlmnStr = strrchr((char*)pucData, ',');
    if(pucPlmnStr != NULL && pucPlmnStr[1] == 0)
    {
        return ATC_FALSE;
    }

    pucDesData = (char*)AtcAp_Malloc(strlen((char*)pucData) + 1);
    strcpy(pucDesData, (char*)pucData);

    pucPlmnStr = strtok(pucDesData, ",");
    while(pucPlmnStr != NULL)
    {
        if(pParam->ucNum >= sizeof(pParam->aulPlmn)/sizeof(pParam->aulPlmn[0]))
        {
            AtcAp_Free(pucDesData);
            return ATC_FALSE;
        }
        
        ucPlmnStrLen = strlen(pucPlmnStr);
        if(5 != ucPlmnStrLen && 6 != ucPlmnStrLen)
        {
            AtcAp_Free(pucDesData);
            return ATC_FALSE;
        }

        for(i = 0; i < ucPlmnStrLen; i++)
        {
            if(pucPlmnStr[i] < '0' || pucPlmnStr[i] > '9')
            {
                AtcAp_Free(pucDesData);
                return ATC_FALSE;
            }

            pucPlmnStr[i] = pucPlmnStr[i] - '0';
        }

        ulPlmn = (unsigned long)(pucPlmnStr[0] << 16)
               + (unsigned long)(pucPlmnStr[1] << 20)
               + (unsigned long)(pucPlmnStr[2] << 8)
               + (unsigned long)pucPlmnStr[3]
               + (unsigned long)(pucPlmnStr[4] << 4);

        if(ulPlmn == 0)
        {
            AtcAp_Free(pucDesData);
            return ATC_FALSE;
        }

        if(ucPlmnStrLen == 5)
        {
            ulPlmn += 0x00F000;
        }
        else
        {
            ulPlmn += (((unsigned long)pucPlmnStr[5]) << 12);
        }
        pParam->aulPlmn[pParam->ucNum++] = ulPlmn;
                       
        pucPlmnStr = strtok((char*)NULL, ",");
    }

    AtcAp_Free(pucDesData);
    return ATC_TRUE;
}
