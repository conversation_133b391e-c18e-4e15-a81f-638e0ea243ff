/**********************************************************************/
/**
 * @file module_id_def.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-02-25
 * @version V1.0
 * @brief 模块ID的定义文件
 **********************************************************************/
#ifndef MODULE_ID_H
#define MODULE_ID_H

#define YX_FUNC_MODULE_ID_INVALID 0x00           /**< 非法模块ID */
#define YX_FUNC_MODULE_ID_CORE 0x02              /**< 核心消息通信模块 */
#define YX_FUNC_MODULE_ID_STORAGE 0x03           /**< 数据存储模块 */
#define YX_FUNC_MODULE_ID_YX_TIMER 0x04          /**< 通用定时器模块 */
#define YX_FUNC_MODULE_ID_YX_DEBUG 0x05          /**< 通用调试打印模块 */
#define YX_FUNC_MODULE_ID_YX_SYS 0x06            /**< 系统通用功能 */
#define YX_FUNC_MODULE_ID_YX_GSM 0x07            /**< gsm网络管理模块 */
#define YX_FUNC_MODULE_ID_COMM_UART 0x08         /**< 串口通信模块 */
#define YX_FUNC_MODULE_ID_COMM_SIF 0x09          /**< 一线通通信模块 */
#define YX_FUNC_MODULE_ID_COMM_RS485 0x0A        /**< RS485通信模块 */
#define YX_FUNC_MODULE_ID_GNSS 0x0B              /**< 定位模块 */
#define YX_FUNC_MODULE_ID_YX_NET 0x0C            /**< net网络管理模块 */
#define YX_FUNC_MODULE_ID_YX_WIFI 0x0D           /**< WIFI扫描模块 */
#define YX_FUNC_MODULE_ID_YX_SENSOR_HIT 0x0E     /**< 传感器-加速度模块 */
#define YX_FUNC_MODULE_ID_YX_FILESYS 0x0F        /**< 应用文件管理模块 */
#define YX_FUNC_MODULE_ID_YX_PE_IO 0x10          /**< PE IO模块 */
#define YX_FUNC_MODULE_ID_DATA_SHARE_CENTER 0x11 /**< 数据共享中心模块 */
#define YX_FUNC_MODULE_ID_YX_POWER 0x12          /**< 电源省电管理 */
#define YX_FUNC_MODULE_ID_ODOMETER 0x13          /**< 里程统计模块 */
#define YX_FUNC_MODULE_ID_M2M 0x14               /**< 核间通讯模块 */
#define YX_FUNC_MODULE_ID_FOTA 0x15              /**< OTA升级模块 */
#define YX_FUNC_MODULE_ID_HTTP 0x16              /**< HTTP客户端模块 */
#define YX_FUNC_MODULE_ID_LBS 0x17               /**< LBS定位模块 */
#define YX_FUNC_MODULE_ID_MQTT 0x18              /**< MQTT客户端模块 */
#define YX_FUNC_MODULE_ID_CAN 0x19               /**< CAN通信模块 */
#define YX_FUNC_MODULE_ID_PARSER 0x1A            /**< 协议解析器模块 */
#define YX_FUNC_MODULE_ID_ALTEST 0x60            /**< AL层测试模块 */


#define YX_APP_MODULE_ID_BASE 0x80 /**< 业务层模块ID从0x80往上加 */

#endif