#include "atc_ps.h"

ST_ATC_AP_INFO                g_AtcApInfo;
unsigned char                 g_SmsFormatMode;
unsigned short                g_WriteSDVolt = 0;

const ST_ATC_AP_DATA_IND_PROC_TABLE AtcAp_DataIndProcTable[] = 
{
    //OK/Err
    { D_ATC_AP_AT_CMD_RST,                      AtcAp_MsgProc_AT_CMD_RST        },

    //Ind
    { D_ATC_AP_SIMST_IND,                       AtcAp_MsgProc_SIMST_Ind         },
    { D_ATC_AP_XYIPDNS_IND,                     AtcAp_MsgProc_XYIPDNS_Ind       },
    { D_ATC_AP_CGAPNRC_IND,                     AtcAp_MsgProc_CGAPNRC_Ind       },
    { D_ATC_AP_CGEV_IND,                        AtcAp_MsgProc_CGEV_Ind          },
    { D_ATC_AP_CREG_IND,                        AtcAp_MsgProc_CREG_Ind          }, 
    { D_ATC_AP_CGREG_IND,                       AtcAp_MsgProc_CGREG_Ind         },
    { D_ATC_AP_CEREG_IND,                       AtcAp_MsgProc_CEREG_Ind         },
    { D_ATC_AP_CSCON_IND,                       AtcAp_MsgProc_CSCON_Ind         },
    { D_ATC_AP_L2THP_IND,                       AtcAp_MsgProc_L2_THP_Ind        },
    { D_ATC_AP_MALLOCADDR_IND,                  AtcAp_MsgProc_MALLOC_ADDR_Ind   },
    { D_ATC_AP_IPSN_IND,                        AtcAp_MsgProc_IPSN_Ind          },
    { D_ATC_AP_LOCALTIMEINFO_IND,               AtcAp_MsgProc_LOCALTIMEINFO_Ind },
    { D_ATC_AP_IPADDR_IND,                      AtcAp_MsgProc_PDNIPADDR_Ind     },
#ifdef LTE_SMS_FEATURE
    { D_ATC_AP_CMTI_IND,                        AtcAp_MsgProc_CMTI_Ind          },
    { D_ATC_AP_CMT_IND,                         AtcAp_MsgProc_CMT_Ind           },
    { D_ATC_AP_CDS_IND,                         AtcAp_MsgProc_CDS_Ind           },
    { D_ATC_AP_CDSI_IND,                        AtcAp_MsgProc_CDSI_Ind           },
    { D_ATC_AP_SMS_PDU_IND,                     AtcAp_MsgProc_SMS_PDU_Ind       },
#endif
    { D_ATC_AP_PINSTATUS_IND,                   AtcAp_MsgProc_PINSTATUS_Ind     },
    { D_ATC_NO_CARRIER_IND,                     AtcAp_MsgProc_NoCarrier_Ind     },
    { D_ATC_AP_PSINFO_IND,                      AtcAp_MsgProc_PSINFO_Ind        },
    // { D_ATC_AP_NIPINFO_IND,                     AtcAp_MsgProc_NIPINFO_Ind       },
    { D_ATC_AP_CNEC_IND,                        AtcAp_MsgProc_CNEC_Ind          },
    { D_ATC_AP_MNBIOTEVENT_IND,                 AtcAp_MsgProc_MNBIOTEVENT_Ind   },
    { D_ATC_AP_CELL_SRCH_IND,                   AtcAp_MsgProc_CELLSRCH_Ind      },
    { D_ATC_AP_QSIMSTAT_IND,                    AtcAp_MsgProc_QSIMSTAT_Ind      },
    { D_ATC_AP_BACKOFFTIMER_IND,                AtcAp_MsgProc_MEMMTIMER_Ind     },
    { D_ATC_AP_QIURC_PDPDEACT_IND,              AtcAp_MsgProc_QIURC_PdpDeact_Ind },
    { D_ATC_AP_TEST_INFO_IND,                   AtcAp_MsgProc_PSTEST_Info_Ind   },
    { D_ATC_AP_CIEV_IND,                        AtcAp_MsgProc_CIEV_Ind          },
    { D_ATC_AP_CEDRXP_IND,                      AtcAp_MsgProc_CEDRXP_Ind        },
    { D_ATC_AP_QWIFISCAN_IND,                   AtcAp_MsgProc_QWIFISCAN_IND     },
#ifndef _FLASH_OPTIMIZE_
    { D_ATC_AP_CRTDCP_IND,                      AtcAp_MsgProc_CRTDCP_Ind        },
    { D_ATC_AP_CSODCPR_IND,                     AtcAp_MsgProc_CSODCPR_Ind       },
    { D_ATC_AP_NPTWEDRXP_IND,                   AtcAp_MsgProc_NPTWEDRXP_Ind     },
    { D_ATC_AP_CCIOTOPTI_IND,                   AtcAp_MsgProc_CCIOTOPTI_Ind     },
    { D_ATC_AP_NSNPDR_IND,                      AtcAp_MsgProc_NSNPDR_Ind        },
#endif
    { D_ATC_AP_NGACTR_IND,                      AtcAp_MsgProc_NGACTR_Ind        },
    { D_ATC_AP_MIPCALL_IND,                     AtcAp_MsgProc_MIPCALL_Ind       },
#ifdef SIB16_FEATURE
    { D_ATC_AP_QUTCTIME_IND,                    AtcAp_MsgProc_QUTCTIME_Ind      },
#endif
    //AT CMD result
    { D_ATC_EVENT_CGSN,                         AtcAp_MsgProc_CGSN_Cnf          },
    { D_ATC_EVENT_CREG_R,                       AtcAp_MsgProc_CREG_R_Cnf        },
    { D_ATC_EVENT_CEREG_R,                      AtcAp_MsgProc_CEREG_R_Cnf       },
    { D_ATC_EVENT_CGATT_R,                      AtcAp_MsgProc_CGATT_R_Cnf       },
    { D_ATC_EVENT_CIMI,                         AtcAp_MsgProc_CIMI_Cnf          },
    { D_ATC_EVENT_CGDCONT_R,                    AtcAp_MsgProc_CGDCONT_R_Cnf     },
    { D_ATC_EVENT_CFUN_R,                       AtcAp_MsgProc_CFUN_R_Cnf        },
    { D_ATC_EVENT_CESQ,                         AtcAp_MsgProc_CESQ_Cnf          },
    { D_ATC_EVENT_CSQ,                          AtcAp_MsgProc_CSQ_Cnf           },
    { D_ATC_EVENT_CGPADDR,                      AtcAp_MsgProc_CGPADDR_Cnf       },
    { D_ATC_EVENT_CGPADDR_T,                    AtcAp_MsgProc_CGPADDR_T_Cnf     },
    { D_ATC_EVENT_CGACT_R,                      AtcAp_MsgProc_CGACT_R_Cnf       },
    { D_ATC_EVENT_SIMST_R,                      AtcAp_MsgProc_SIMST_R_Cnf       },
    { D_ATC_EVENT_CPSMS_R,                      AtcAp_MsgProc_CPSMS_R_Cnf       },
    { D_ATC_EVENT_CGAPNRC,                      AtcAp_MsgProc_CGAPNRC_Cnf       },
    { D_ATC_EVENT_CGAPNRC_T,                    AtcAp_MsgProc_CGAPNRC_T_Cnf     },
    { D_ATC_EVENT_CSCON_R,                      AtcAp_MsgProc_CSCON_R_Cnf       },
    { D_ATC_EVENT_NL2THP_R,                     AtcAp_MsgProc_NL2THP_R_Cnf      },
#if USR_CUSTOM2
    { D_ATC_EVENT_NUESTATS,                     AtcAp_MsgProc_NUESTATS_Cnf      },
#endif
    { D_ATC_EVENT_NEARFCN_R,                    AtcAp_MsgProc_NEARFCN_R_Cnf     },
    { D_ATC_EVENT_NBAND_R,                      AtcAp_MsgProc_NBAND_R_Cnf       },
    { D_ATC_EVENT_NBAND_T,                      AtcAp_MsgProc_NBAND_T_Cnf       },
    { D_ATC_EVENT_NSET_R,                       AtcAp_MsgProc_NSET_R_Cnf        },
#ifdef ESM_DEDICATED_EPS_BEARER
    { D_ATC_EVENT_CGDSCONT_R,                   AtcAp_MsgProc_CGDSCONT_R_Cnf    },
    { D_ATC_EVENT_CGDSCONT_T,                   AtcAp_MsgProc_CGDSCONT_T_Cnf    },
    { D_ATC_EVENT_CGSCONTRDP,                   AtcAp_MsgProc_CGSCONTRDP_Cnf     },
    { D_ATC_EVENT_CGSCONTRDP_T,                 AtcAp_MsgProc_CGSCONTRDP_T_Cnf   },
    { D_ATC_EVENT_CGTFT_R,                      AtcAp_MsgProc_CGTFT_R_Cnf       },
    { D_ATC_EVENT_CGTFTRDP,                     AtcAp_MsgProc_CGTFTRDP_Cnf     },
    { D_ATC_EVENT_CGTFTRDP_T,                   AtcAp_MsgProc_CGTFTRDP_T_Cnf   },
    { D_ATC_EVENT_CGEQOS_R,                     AtcAp_MsgProc_CGEQOS_R_Cnf      },
    { D_ATC_EVENT_CGCMOD_T,                     AtcAp_MsgProc_CGCMOD_T_Cnf      },
    { D_ATC_EVENT_CGEQOSRDP,                    AtcAp_MsgProc_CGEQOSRDP_Cnf     },
    { D_ATC_EVENT_CGEQOSRDP_T,                  AtcAp_MsgProc_CGEQOSRDP_T_Cnf   },
#endif
    { D_ATC_EVENT_COPS_R,                       AtcAp_MsgProc_COPS_R_Cnf        },
    { D_ATC_EVENT_COPS_T,                       AtcAp_MsgProc_COPS_T_Cnf        },
    { D_ATC_EVENT_CGEREP_R,                     AtcAp_MsgProc_CGEREP_R_Cnf      },
    { D_ATC_EVENT_CTZR_R,                       AtcAp_MsgProc_CTZR_R_Cnf        },
    { D_ATC_EVENT_CGCONTRDP,                    AtcAp_MsgProc_CGCONTRDP_Cnf     },
    { D_ATC_EVENT_CGCONTRDP_T,                  AtcAp_MsgProc_CGCONTRDP_T_Cnf   },
    { D_ATC_EVENT_CEER,                         AtcAp_MsgProc_CEER_Cnf          },
    { D_ATC_EVENT_CIPCA_R,                      AtcAp_MsgProc_CIPCA_R_Cnf       },
    { D_ATC_EVENT_CGAUTH_R,                     AtcAp_MsgProc_CGAUTH_R_Cnf      },
    { D_ATC_EVENT_NPOWERCLASS_R,                AtcAp_MsgProc_NPOWERCLASS_R_Cnf },
    { D_ATC_EVENT_NPOWERCLASS_T,                AtcAp_MsgProc_NPOWERCLASS_T_Cnf },
    // { D_ATC_EVENT_NIPINFO_R,                    AtcAp_MsgProc_NIPINFO_R_Cnf     },
    { D_ATC_EVENT_CNEC_R,                       AtcAp_MsgProc_CNEC_R_Cnf        },
    { D_ATC_EVENT_CEID,                         AtcAp_MsgProc_CEID_Cnf          },
#ifdef LTE_SMS_FEATURE
    { D_ATC_EVENT_CSMS,                         AtcAp_MsgProc_CSMS_Cnf          },
    { D_ATC_EVENT_CSMS_R,                       AtcAp_MsgProc_CSMS_R_Cnf        },
    { D_ATC_EVENT_CMGC,                         AtcAp_MsgProc_CMGC_Cnf          },
    { D_ATC_EVENT_CMSS,                         AtcAp_MsgProc_CMSS_Cnf          },
    { D_ATC_EVENT_CMGS,                         AtcAp_MsgProc_CMGS_Cnf          },
    { D_ATC_EVENT_CMGF,                         AtcAp_MsgProc_CMGF_Cnf        },
    { D_ATC_EVENT_CMGF_R,                       AtcAp_MsgProc_CMGF_R_Cnf        },
    { D_ATC_EVENT_CPMS,                         AtcAp_MsgProc_CPMS_Cnf        },
    { D_ATC_EVENT_CPMS_R,                       AtcAp_MsgProc_CPMS_R_Cnf        },
    { D_ATC_EVENT_CPMS_T,                       AtcAp_MsgProc_CPMS_T_Cnf        },
    { D_ATC_EVENT_CSCA_R,                       AtcAp_MsgProc_CSCA_R_Cnf        },
    { D_ATC_EVENT_CSMP_R,                       AtcAp_MsgProc_CSMP_R_Cnf        },
    { D_ATC_EVENT_CNMI_R,                       AtcAp_MsgProc_CNMI_R_Cnf        },
    { D_ATC_EVENT_CMMS_R,                       AtcAp_MsgProc_CMMS_R_Cnf        },
    { D_ATC_EVENT_CSCB_R,                       AtcAp_MsgProc_CSCB_R_Cnf        },
    { D_ATC_EVENT_CSDH_R,                       AtcAp_MsgProc_CSDH_R_Cnf        },
    { D_ATC_EVENT_CMGR,                         AtcAp_MsgProc_CMGR_Cnf          },
    { D_ATC_EVENT_CMGW,                         AtcAp_MsgProc_CMGW_Cnf          },
    { D_ATC_EVENT_CMGL,                         AtcAp_MsgProc_CMGL_Cnf          },
    { D_ATC_EVENT_QCMGS,                        AtcAp_MsgProc_QCMGS_Cnf         },
    { D_ATC_EVENT_QCMGR,                        AtcAp_MsgProc_QCMGR_Cnf         },
#endif
    // { D_ATC_EVENT_NPIN,                         AtcAp_MsgProc_NPIN_Cnf          },
    { D_ATC_EVENT_CPIN_R,                       AtcAp_MsgProc_CPIN_R_Cnf        },
    { D_ATC_EVENT_CLCK,                         AtcAp_MsgProc_CLCK_Cnf          },
    { D_ATC_EVENT_CPINR,                        AtcAp_MsgProc_CPINR_Cnf         },
    { D_ATC_EVENT_NCCID,                        AtcAp_MsgProc_NCCID_Cnf         },
    { D_ATC_EVENT_CCHO,                         AtcAp_MsgProc_CCHO_Cnf          },
    { D_ATC_EVENT_CCHC,                         AtcAp_MsgProc_CCHC_Cnf          },
    { D_ATC_EVENT_CRSM,                         AtcAp_MsgProc_CRSM_Cnf          },
    { D_ATC_EVENT_CGLA,                         AtcAp_MsgProc_CGLA_Cnf          },
    { D_ATC_EVENT_CSIM,                         AtcAp_MsgProc_CSIM_Cnf          },
#ifdef LCS_MOLR_ENABLE
    { D_ATC_EVENT_CMOLR_R,                      AtcAp_MsgProc_CMOLR_R_Cnf       },
    { D_ATC_AP_CMOLRE_IND,                      AtcAp_MsgProc_CMOLRE_Ind        },
    { D_ATC_AP_CMOLRG_IND,                      AtcAp_MsgProc_CMOLRG_Ind        },
#endif
    { D_ATC_EVENT_NLOCKF_R,                     AtcAp_MsgProc_NLOCKF_R_Cnf      },
#ifdef CSG_FEATURE
    { D_ATC_EVENT_NCSG_R,                       AtcAp_MsgProc_NCSG_R_Cnf        },
    { D_ATC_EVENT_NCSG_T,                       AtcAp_MsgProc_NCSG_T_Cnf        },
#endif
    { D_ATC_EVENT_QICSGP_R,                     AtcAp_MsgProc_QICSGP_R_Cnf      },
    { D_ATC_EVENT_QIACT_R,                      AtcAp_MsgProc_QIACT_R_Cnf       },
    { D_ATC_EVENT_QGSN,                         AtcAp_MsgProc_QGSN_Cnf          },
    { D_ATC_EVENT_GSN,                          AtcAp_MsgProc_GSN_Cnf           },
    { D_ATC_EVENT_QCCID,                        AtcAp_MsgProc_QCCID_Cnf         },
    { D_ATC_EVENT_CGREG_R,                      AtcAp_MsgProc_CGREG_R_Cnf       },
    { D_ATC_EVENT_QSPN,                         AtcAp_MsgProc_QSPN_Cnf          },
    { D_ATC_EVENT_QENG,                         AtcAp_MsgProc_QENG_Cnf          },
    { D_ATC_EVENT_QSIMDET_R,                    AtcAp_MsgProc_QSIMDET_R_Cnf     },
    { D_ATC_EVENT_QSIMSTAT_R,                   AtcAp_MsgProc_QSIMSTAT_R_Cnf    },
    { D_ATC_EVENT_QNWINFO,                      AtcAp_MsgProc_QNWINFO_Cnf       },
    { D_ATC_EVENT_QCSQ_R,                       AtcAp_MsgProc_QCSQ_R_Cnf        },
    { D_ATC_EVENT_QCSQ_E,                       AtcAp_MsgProc_QCSQ_E_Cnf        },
    { D_ATC_EVENT_CSCS_R,                       AtcAp_MsgProc_CSCS_R_Cnf        },
    { D_ATC_EVENT_CGSMS_R,                      AtcAp_MsgProc_CGSMS_R_Cnf       },
    { D_ATC_EVENT_QGDCNT_R,                     AtcAp_MsgProc_QGDCNT_R_Cnf      },
    { D_ATC_EVENT_QAUGDCNT_R,                   AtcAp_MsgProc_QAUGDCNT_R_Cnf    },
    { D_ATC_EVENT_CPOL_R,                       AtcAp_MsgProc_CPOL_R_Cnf        },
    { D_ATC_EVENT_CPOL_T,                       AtcAp_MsgProc_CPOL_T_Cnf        },
    { D_ATC_EVENT_QINISTAT,                     AtcAp_MsgProc_QINISTAT_Cnf      },
    { D_ATC_EVENT_QCELL_R,                      AtcAp_MsgProc_QCELL_R_Cnf       },
    { D_ATC_EVENT_QCELLEX,                      AtcAp_MsgProc_QCELL_R_Cnf       },
    { D_ATC_EVENT_QPINC,                        AtcAp_MsgProc_QPINC_Cnf         },
    { D_ATC_EVENT_MLOCKFREQ_R,                  AtcAp_MsgProc_MLOCKFREQ_R_Cnf    },
    { D_ATC_EVENT_MLOCKFREQ_T,                  AtcAp_MsgProc_MLOCKFREQ_T_Cnf    },
    { D_ATC_EVENT_MUESTATS,                     AtcAp_MsgProc_MUESTATS_Cnf       },
    { D_ATC_EVENT_MEMMTIMER_R,                  AtcAp_MsgProc_MEMMTIMER_R_Cnf    },
    { D_ATC_EVENT_MUECONFIG_R,                  AtcAp_MsgProc_MUECONFIG_R_Cnf    },
    { D_ATC_EVENT_CNUM,                         AtcAp_MsgProc_CNUM_Cnf           },
    { D_ATC_EVENT_QBLACKCELL_R,                 AtcAp_MsgProc_QBLACKCELL_R_Cnf   },
    { D_ATC_EVENT_QBLACKCELLCFG_R,              AtcAp_MsgProc_QBLACKCELLCFG_R_Cnf },
    { D_ATC_EVENT_SIMSWITCH_R,                  AtcAp_MsgProc_QSIMSWITCH_R_Cnf    },
    { D_ATC_EVENT_PCTTESTINFO_R,                AtcAp_MsgProc_PCTTESTINFO_R_Cnf  },
    { D_ATC_EVENT_NPREEARFCN_R,                 AtcAp_MsgProc_NPREEARFCN_R_Cnf   },
    { D_ATC_EVENT_QSCLKEX_R,                    AtcAp_MsgProc_QSCLKEX_R_Cnf      },
    { D_ATC_EVENT_NWDRX,                        AtcAp_MsgProc_NWDRX_Cnf          },
#ifdef SIMULATOR_UICC
    { D_ATC_EVENT_SIMUUICC_R,                   AtcAp_MsgProc_SIMUUICC_R_Cnf     },
#endif
    { D_ATC_EVENT_CEDRXS_R,                     AtcAp_MsgProc_CEDRXS_R_Cnf      },
    { D_ATC_EVENT_NCONFIG_R,                    AtcAp_MsgProc_NCONFIG_R_Cnf     },
    { D_ATC_EVENT_NCONFIG_T,                    AtcAp_MsgProc_NCONFIG_T_Cnf     },
    { D_ATC_EVENT_MWIFISCANCFG_R,               AtcAp_MsgProc_MWIFISCANCFG_R_Cnf },
    { D_ATC_EVENT_QWIFISCAN_R,                  AtcAp_MsgProc_QWIFISCAN_R_Cnf   },
    { D_ATC_EVENT_MWIFISCANQUERY,               AtcAp_MsgProc_MWIFISCANQUERY_Cnf},
    { D_ATC_EVENT_CEDRXRDP,                     AtcAp_MsgProc_CEDRXRDP_Cnf      },
#ifndef _FLASH_OPTIMIZE_
    { D_ATC_EVENT_CRTDCP_R,                     AtcAp_MsgProc_CRTDCP_R_Cnf      },
    { D_ATC_EVENT_CCIOTOPT_R,                   AtcAp_MsgProc_CCIOTOPT_R_Cnf    },
    { D_ATC_EVENT_NPTWEDRXS_R,                  AtcAp_MsgProc_NPTWEDRXS_R_Cnf   },
    { D_ATC_EVENT_NPOPB_R,                      AtcAp_MsgProc_NPOPB_R_Cnf       },
    { D_ATC_EVENT_NQPODCP,                      AtcAp_MsgProc_NQPODCP_Cnf       },
    { D_ATC_EVENT_NQPNPD,                       AtcAp_MsgProc_NQPNPD_Cnf        },
    { D_ATC_EVENT_NRNPDM_R,                     AtcAp_MsgProc_NRNPDM_R_Cnf      },
    { D_ATC_EVENT_CACDC_T,                      AtcAp_MsgProc_CACDC_T_Cnf       },
#endif
#if !defined(_FLASH_OPTIMIZE_) || (USR_CUSTOM2)
    { D_ATC_EVENT_NCIDSTATUS,                   AtcAp_MsgProc_NCIDSTATUS_Cnf    },
    { D_ATC_EVENT_NCIDSTATUS_R,                 AtcAp_MsgProc_NCIDSTATUS_R_Cnf  },
#endif
    { D_ATC_EVENT_NCPCDPR_R,                    AtcAp_MsgProc_NCPCDPR_R_Cnf     },
    { D_ATC_EVENT_NGACTR_R,                     AtcAp_MsgProc_NGACTR_R_Cnf      },
    { D_ATC_AP_SIM_DATA_DOWNLOAD_IND,           AtcAp_MsgProc_SimDataDownload_Ind },
    { D_ATC_EVENT_CCED,                         AtcAp_MsgProc_CCED_Cnf          },
    { D_ATC_EVENT_CEMODE_R,                     AtcAp_MsgProc_CEMODE_Cnf        },
    { D_ATC_EVENT_QIACTEX_R,                    AtcAp_MsgProc_QIACTEX_R_Cnf     },
    { D_ATC_AP_QIACTEX_IND,                     AtcAp_MsgProc_QIACTEX_Ind       },
    { D_ATC_AP_QIDEACTEX_IND,                   AtcAp_MsgProc_QIDEACTEX_Ind     },
    { D_ATC_EVENT_ICCID_R,                      AtcAp_MsgProc_ICCID_R_Cnf       },
    { D_ATC_EVENT_BAND_R,                       AtcAp_MsgProc_BAND_R_Cnf        },
    { D_ATC_EVENT_BANDIND_R,                    AtcAp_MsgProc_BANDIND_R_Cnf     },
    { D_ATC_EVENT_QCELLINFO_R,                  AtcAp_MsgProc_QCELLINFO_R_Cnf   },
    { D_ATC_EVENT_QINDCFG_R,                    AtcAp_MsgProc_QINDCFG_R_Cnf     },
    { D_ATC_EVENT_QDSIM_R,                      AtcAp_MsgProc_QDSIM_R_Cnf       },
    { D_ATC_EVENT_QCFG_R,                       AtcAp_MsgProc_QCFG_R_Cnf        },
    { D_ATC_EVENT_CUSD_R,                       AtcAp_MsgProc_CUSD_R_Cnf        },
    { D_ATC_EVENT_QEHPLMN_R,                    AtcAp_MsgProc_QEHPLMN_R_Cnf     },
#if USR_CUSTOM12
    { D_ATC_EVENT_CCID,                         AtcAp_MsgProc_CCID_Cnf          },
#endif
    { D_ATC_EVENT_ECSIMCFG_R,                   AtcAp_MsgProc_ECSIMCFG_R_Cnf    },
    { D_ATC_EVENT_NULL,                         NULL                            },
};

const ST_ATC_COMMAND_ANAL_TABLE ATC_Plus_CommandTable[] =
{
//basic AT in atc_cmd_basic.c
    {  ATC_CFUN_COMMAND_PREFIX               ,    ATC_CFUN_LTE_Command           },
    {  ATC_CGATT_COMMAND_PREFIX              ,    ATC_CGATT_LTE_Command          },
    {  ATC_CGACT_COMMAND_PREFIX              ,    ATC_CGACT_LTE_Command          },
    {  ATC_CGDCONT_COMMAND_PREFIX            ,    ATC_CGDCONT_LTE_Command        },
    {  ATC_CGCONTRDP_COMMAND_PREFIX          ,    ATC_CGCONTRDP_LTE_Command      },
    {  ATC_CREG_COMMAND_PREFIX               ,    ATC_CREG_LTE_Command           },
    {  ATC_CGREG_COMMAND_PREFIX              ,    ATC_CGREG_LTE_Command          },
    {  ATC_CEREG_COMMAND_PREFIX              ,    ATC_CEREG_LTE_Command          },
    {  ATC_CSCON_COMMAND_PREFIX              ,    ATC_CSCON_LTE_Command          },
    {  ATC_CMEE_COMMAND_PREFIX               ,    ATC_CMEE_LTE_Command           },
    {  ATC_CGSN_COMMAND_PREFIX               ,    ATC_CGSN_LTE_Command           },
    {  ATC_CESQ_COMMAND_PREFIX               ,    ATC_CESQ_LTE_Command           },
    {  ATC_CSQ_COMMAND_PREFIX                ,    ATC_CSQ_LTE_Command            },
    {  ATC_CGPADDR_COMMAND_PREFIX            ,    ATC_CGPADDR_LTE_Command        }, 
    {  ATC_CPSMS_COMMAND_PREFIX              ,    ATC_CPSMS_LTE_Command          },
    {  ATC_CNEC_COMMAND_PREFIX               ,    ATC_CNEC_LTE_Command           },
    {  ATC_CLAC_COMMAND_PREFIX               ,    ATC_CLAC_LTE_Command           },
    {  ATC_CGEREP_COMMAND_PREFIX             ,    ATC_CGEREP_Command             },
    {  ATC_COPS_COMMAND_PREFIX               ,    ATC_COPS_LTE_Command           },
    {  ATC_CTZR_COMMAND_PREFIX               ,    ATC_CTZR_LTE_Command           },
    {  ATC_CTZU_COMMAND_PREFIX               ,    ATC_CTZU_LTE_Command           },
    {  ATC_CGPIAF_COMMAND_PREFIX             ,    ATC_CGPIAF_LTE_Command         },
    {  ATC_CPOL_COMMAND_PREFIX               ,    ATC_CPOL_LTE_Command           },
    {  ATC_COPN_COMMAND_PREFIX               ,    ATC_COPN_LTE_Command           },
    {  ATC_CSCS_COMMAND_PREFIX               ,    ATC_CSCS_LTE_Command           },
    {  ATC_CGMI_COMMAND_PREFIX               ,    ATC_CGMI_LTE_Command           },
    {  ATC_CGMM_COMMAND_PREFIX               ,    ATC_CGMM_LTE_Command           },
    {  ATC_CGMR_COMMAND_PREFIX               ,    ATC_CGMR_LTE_Command           },
#ifdef ESM_DEDICATED_EPS_BEARER
    {  ATC_CGDSCONT_COMMAND_PREFIX           ,    ATC_CGDSCONT_LTE_Command       },
    {  ATC_CGSCONTRDP_COMMAND_PREFIX         ,    ATC_CGSCONTRDP_LTE_Command     },
    {  ATC_CGEQOS_COMMAND_PREFIX             ,    ATC_CGEQOS_LTE_Command         },
    {  ATC_CGEQOSRDP_COMMAND_PREFIX          ,    ATC_CGEQOSRDP_LTE_Command      },
    {  ATC_CGTFT_COMMAND_PREFIX              ,    ATC_CGTFT_LTE_Command          },
    {  ATC_CGTFTRDP_COMMAND_PREFIX           ,    ATC_CGTFTRDP_LTE_Command       },
    {  ATC_CGCMOD_COMMAND_PREFIX             ,    ATC_CGCMOD_LTE_Command         },
#endif
    {  ATC_CIPCA_COMMAND_PREFIX              ,    ATC_CIPCA_LTE_Command          },
    {  ATC_CGAUTH_COMMAND_PREFIX             ,    ATC_CGAUTH_LTE_Command         },
    {  ATC_CEER_COMMAND_PREFIX               ,    ATC_CEER_LTE_Command           },
    {  ATC_CNUM_COMMAND_PREFIX               ,    ATC_CNUM_LTE_Command           },
    {  ATC_CEDRXS_COMMAND_PREFIX             ,    ATC_CEDRXS_LTE_Command         },
    {  ATC_CEDRXRDP_COMMAND_PREFIX           ,    ATC_CEDRXRDP_LTE_Command       },
#ifndef _FLASH_OPTIMIZE_
    {  ATC_CSODCP_COMMAND_PREFIX             ,    ATC_CSODCP_LTE_Command         },
    {  ATC_CRTDCP_COMMAND_PREFIX             ,    ATC_CRTDCP_LTE_Command         },
    {  ATC_CCIOTOPT_COMMAND_PREFIX           ,    ATC_CCIOTOPT_LTE_Command       },
    {  ATC_CNMPSD_COMMAND_PREFIX             ,    ATC_CNMPSD_LTE_Command         },
    {  ATC_CACDC_COMMAND_PREFIX              ,    ATC_CACDC_LTE_Command          },
#endif
    {  ATC_CGAPNRC_COMMAND_PREFIX            ,    ATC_CGAPNRC_LTE_Command        },
//sim
    {  ATC_CIMI_COMMAND_PREFIX               ,    ATC_CIMI_LTE_Command           },
    {  ATC_CSIM_COMMAND_PREFIX               ,    ATC_CSIM_LTE_Command           },
    {  ATC_CCHO_COMMAND_PREFIX               ,    ATC_CCHO_LTE_Command           },
    {  ATC_CCHC_COMMAND_PREFIX               ,    ATC_CCHC_LTE_Command           },
    {  ATC_CGLA_COMMAND_PREFIX               ,    ATC_CGLA_LTE_Command           },
    {  ATC_CRSM_COMMAND_PREFIX               ,    ATC_CRSM_LTE_Command           },
    {  ATC_CPWD_COMMAND_PREFIX               ,    ATC_CPWD_LTE_Command           },
    {  ATC_CPIN_COMMAND_PREFIX               ,    ATC_CPIN_LTE_Command           },
    {  ATC_CLCK_COMMAND_PREFIX               ,    ATC_CLCK_LTE_Command           },
    {  ATC_CPINR_COMMAND_PREFIX              ,    ATC_CPINR_LTE_Command          },
//sms
#ifdef LTE_SMS_FEATURE
     {  ATC_CSMS_COMMAND_PREFIX              ,    ATC_CSMS_Command               },
     {  ATC_CMGF_COMMAND_PREFIX              ,    ATC_CMGF_Command               },
     {  ATC_CPMS_COMMAND_PREFIX              ,    ATC_CPMS_Command               },
     {  ATC_CSCA_COMMAND_PREFIX              ,    ATC_CSCA_Command               },
     {  ATC_CSMP_COMMAND_PREFIX              ,    ATC_CSMP_Command               },
     {  ATC_CNMI_COMMAND_PREFIX              ,    ATC_CNMI_Command               },
     {  ATC_CMGW_COMMAND_PREFIX              ,    ATC_CMGW_Command               },
     {  ATC_CMGR_COMMAND_PREFIX              ,    ATC_CMGR_Command               },
     {  ATC_CMGD_COMMAND_PREFIX              ,    ATC_CMGD_Command               },
     {  ATC_CMGL_COMMAND_PREFIX              ,    ATC_CMGL_Command               },
     {  ATC_CMSS_COMMAND_PREFIX              ,    ATC_CMSS_Command               },
     {  ATC_CMGS_COMMAND_PREFIX              ,    ATC_CMGS_Command               },
     {  ATC_CNMA_COMMAND_PREFIX              ,    ATC_CNMA_Command               },
     {  ATC_CMGC_COMMAND_PREFIX              ,    ATC_CMGC_Command               },
     {  ATC_CMMS_COMMAND_PREFIX              ,    ATC_CMMS_Command               },
     {  ATC_CSCB_COMMAND_PREFIX              ,    ATC_CSCB_Command               },
     {  ATC_CSDH_COMMAND_PREFIX              ,    ATC_CSDH_Command               },
     {  ATC_QCMGS_COMMAND_PREFIX             ,    ATC_QCMGS_Command              },
     {  ATC_QCMGR_COMMAND_PREFIX             ,    ATC_QCMGR_Command              },
     {  ATC_CGSMS_COMMAND_PREFIX             ,    ATC_CGSMS_LTE_Command          },
#endif
#ifdef LCS_MOLR_ENABLE
    //{  ATC_CMOLR_COMMAND_PREFIX             ,      ATC_CMOLR_LTE_Command          },
#endif
//other
    {  ATC_GMI_COMMAND_PREFIX                ,    ATC_GMI_LTE_Command            },
    {  ATC_GMM_COMMAND_PREFIX                ,    ATC_GMM_LTE_Command            },
    {  ATC_GMR_COMMAND_PREFIX                ,    ATC_GMR_LTE_Command            },
    {  ATC_GSN_COMMAND_PREFIX                ,    ATC_GSN_LTE_Command            },
    {  ATC_RAI_COMMAND_PREFIX                ,    ATC_RAI_LTE_Command            },
#ifdef CSG_FEATURE
    {  ATC_QCSG_COMMAND_PREFIX               ,    ATC_NCSG_LTE_Command           },
#endif
    {  ATC_QICSGP_COMMAND_PREFIX             ,    ATC_QICSGP_LTE_Command         },
    {  ATC_QIACT_COMMAND_PREFIX              ,    ATC_QIACT_LTE_Command          },
    {  ATC_QIACTEX_COMMAND_PREFIX            ,    ATC_QIACTEX_LTE_Command        },
    {  ATC_QIDEACT_COMMAND_PREFIX            ,    ATC_QIDEACT_LTE_Command        },
    {  ATC_QIDEACTEX_COMMAND_PREFIX          ,    ATC_QIDEACTEX_LTE_Command      },
    {  ATC_QGSN_COMMAND_PREFIX               ,    ATC_QGSN_LTE_Command           },
    {  ATC_QCCID_COMMAND_PREFIX              ,    ATC_QCCID_LTE_Command          },
    {  ATC_QSPN_COMMAND_PREFIX               ,    ATC_QSPN_LTE_Command           },
    {  ATC_QENG_COMMAND_PREFIX               ,    ATC_QENG_LTE_Command           },
    {  ATC_QINISTAT_COMMAND_PREFIX           ,    ATC_QINISTAT_LTE_Command       },
    {  ATC_QSIMDET_COMMAND_PREFIX            ,    ATC_QSIMDET_LTE_Command        },
    {  ATC_QSIMSTAT_COMMAND_PREFIX           ,    ATC_QSIMSTAT_LTE_Command       },
    {  ATC_QNWINFO_COMMAND_PREFIX            ,    ATC_QNWINFO_LTE_Command        },
    {  ATC_QCSQ_COMMAND_PREFIX               ,    ATC_QCSQ_LTE_Command           },
    {  ATC_QGDCNT_COMMAND_PREFIX             ,    ATC_QGDCNT_LTE_Command         },
    {  ATC_QAUGDCNT_COMMAND_PREFIX           ,    ATC_QAUGDCNT_LTE_Command       },
    {  ATC_QCELL_COMMAND_PREFIX              ,    ATC_QCELL_LTE_Command          },
    {  ATC_QCELLEX_COMMAND_PREFIX            ,    ATC_QCELLEX_Command            },
    {  ATC_QPINC_COMMAND_PREFIX              ,    ATC_QPINC_LTE_Command          },   
    {  ATC_QBLACKCELL_COMMAND_PREFIX         ,    ATC_QBLACKCELL_LTE_Command     },
    {  ATC_QBLACKCELLCFG_COMMAND_PREFIX      ,    ATC_QBLACKCELLCFG_LTE_Command  },
    {  ATC_QSIMSWITCH_COMMAND_PREFIX         ,    ATC_QSIMSWITCH_LTE_Command     },
    {  ATC_QCFG_COMMAND_PREFIX               ,    ATC_QCFG_LTE_Command           },
    {  ATC_QSCLKEX_COMMAND_PREFIX            ,    ATC_QSCLKEX_LTE_Command        },
#if USR_CUSTOM2
    {  ATC_NUESTATS_COMMAND_PREFIX           ,    ATC_NUESTATS_LTE_Command       },
#endif
    {  ATC_NEARFCN_COMMAND_PREFIX            ,    ATC_NEARFCN_LTE_Command        },
    {  ATC_NCONFIG_COMMAND_PREFIX            ,    ATC_NCONFIG_LTE_Command        },
#ifndef _FLASH_OPTIMIZE_
    {  ATC_MPTWEDRXS_COMMAND_PREFIX          ,    ATC_NPTWEDRXS_LTE_Command      },
    {  ATC_NPOPB_COMMAND_PREFIX              ,    ATC_NPOPB_LTE_Command          },
    {  ATC_NQPODCP_COMMAND_PREFIX            ,    ATC_NQPODCP_LTE_Command        },
    {  ATC_NSNPD_COMMAND_PREFIX              ,    ATC_NSNPD_LTE_Command          },
    {  ATC_NQPNPD_COMMAND_PREFIX             ,    ATC_NQPNPD_LTE_Command         },
    {  ATC_NRNPDM_COMMAND_PREFIX             ,    ATC_NRNPDM_LTE_Command         },
#endif
#if !defined(_FLASH_OPTIMIZE_) || (USR_CUSTOM2)
    {  ATC_NCIDSTATUS_COMMAND_PREFIX         ,    ATC_NCIDSTATUS_LTE_Command     },
#endif
    {  ATC_NCPCDPR_COMMAND_PREFIX            ,    ATC_NCPCDPR_LTE_Command        },
    {  ATC_NGACTR_COMMAND_PREFIX             ,    ATC_NGACTR_LTE_Command         },
    {  ATC_NCSEARFCN_COMMAND_PREFIX          ,    ATC_NCSEARFCN_LTE_Command      },
    {  ATC_NFPLMN_COMMAND_PREFIX             ,    ATC_NFPLMN_LTE_Command         },
    {  ATC_NL2THP_COMMAND_PREFIX             ,    ATC_NL2THP_LTE_Command         },
    {  ATC_NSET_COMMAND_PREFIX               ,    ATC_NSET_LTE_Command           },
    {  ATC_NPOWERCLASS_COMMAND_PREFIX        ,    ATC_NPOWERCLASS_LTE_Command    },
    //  ATC_NPIN_COMMAND_PREFIX               ,    ATC_NPIN_LTE_Command           },
    {  ATC_NTSETID_COMMAND_PREFIX            ,    ATC_NTSETID_LTE_Command        },
    //  ATC_NIPINFO_COMMAND_PREFIX            ,    ATC_NIPINFO_LTE_Command        },
    {  ATC_NLOCKF_COMMAND_PREFIX             ,    ATC_NLOCKF_LTE_Command         },
    {  ATC_NITZ_COMMAND_PREFIX               ,    ATC_NITZ_LTE_Command           }, 
    {  ATC_NPREEARFCN_COMMAND_PREFIX         ,    ATC_NPREEARFCN_LTE_Command     },

    {  ATC_MBAND_COMMAND_PREFIX              ,    ATC_NBAND_LTE_Command          },
    {  ATC_MCCID_COMMAND_PREFIX              ,    ATC_NCCID_LTE_Command          },
    {  ATC_MEID_COMMAND_PREFIX               ,    ATC_CEID_LTE_Command           },
    {  ATC_MNBIOTEVENT_COMMAND_PREFIX        ,    ATC_MNBIOTEVENT_LTE_Command    },
    {  ATC_MLOCKFREQ_COMMAND_PREFIX          ,    ATC_MLOCKFREQ_LTE_Command      },
    {  ATC_MCSEARFCN_COMMAND_PREFIX          ,    ATC_MCSEARFCN_LTE_Command      },
    {  ATC_MUESTATS_COMMAND_PREFIX           ,    ATC_MUESTATS_LTE_Command       },
    {  ATC_MPSRAT_COMMAND_PREFIX             ,    ATC_MPSRAT_LTE_Command         },
    {  ATC_MEMMTIMER_COMMAND_PREFIX          ,    ATC_MEMMTIMER_LTE_Command      },
    {  ATC_MUECONFIG_COMMAND_PREFIX          ,    ATC_MUECONFIG_LTE_Command      },
    {  ATC_MWIFISCANCFG_COMMAND_PREFIX       ,    ATC_MWIFISCANCFG_LTE_Command   },
    {  ATC_MWIFISCANSTART_COMMAND_PREFIX     ,    ATC_MWIFISCANSTART_LTE_Command },
    {  ATC_MWIFISCANSTOP_COMMAND_PREFIX      ,    ATC_MWIFISCANSTOP_LTE_Command  },
    {  ATC_MWIFISCANQUERY_COMMAND_PREFIX     ,    ATC_MWIFISCANQUERY_LTE_Command },
    {  ATC_QWIFISCAN_COMMAND_PREFIX          ,    ATC_QWIFISCAN_LTE_Command      },
//Test
    {  ATC_PSTEST_COMMAND_PREFIX             ,    ATC_PSTEST_LTE_Command         },
#if PS_TEST_MODE
    {  ATC_PSTESTMODE_COMMAND_PREFIX         ,    ATC_PSTESTMODE_LTE_Command     },
    {  ATC_NWSDVOLT_COMMAND_PREFIX           ,    ATC_NWSDVOLT_LTE_Command       },
#endif
    {  ATC_QTCNI_COMMAND_PREFIX              ,    ATC_PCTTESTINFO_LTE_Command    },
    {  ATC_NWDRX_COMMAND_PREFIX              ,    ATC_NWDRX_LTE_Command          },
#ifdef SIMULATOR_UICC
    {  ATC_SIMUUICC_COMMAND_PREFIX           ,    ATC_SIMUUICC_LTE_Command       },
#endif
    {  ATC_SIMST_COMMAND_PREFIX              ,    ATC_SIMST_LTE_Command          },
    {  ATC_CCED_COMMAND_PREFIX               ,    ATC_CCED_LTE_Command           },
    {  ATC_CEMODE_COMMAND_PREFIX             ,    ATC_CEMODE_LTE_Command         },
    {  ATC_QCELLINFO_COMMAND_PREFIX          ,    ATC_QCELLINFO_LTE_Command      },
    {  ATC_QINDCFG_COMMAND_PREFIX            ,    ATC_QINDCFG_LTE_Command        },
    {  ATC_CIDACT_COMMAND_PREFIX             ,    ATC_CIDACT_LTE_Command         },
    {  ATC_QDSIM_COMMAND_PREFIX              ,    ATC_QDSIM_LTE_Command          },
    {  ATC_CUSD_COMMAND_PREFIX               ,    ATC_CUSD_LTE_Command           },
    {  ATC_QEHPLMN_COMMAND_PREFIX            ,    ATC_QEHPLMN_LTE_Command        },
#if USR_CUSTOM12
    {  ATC_CCID_COMMAND_PREFIX               ,    ATC_CCID_LTE_Command           },
    {  ATC_QMUXCFG_COMMAND_PREFIX            ,    ATC_QMUXCFG_LTE_Command        },
    {  ATC_QIFGCNT_COMMAND_PREFIX            ,    ATC_QIFGCNT_LTE_Command        },
    {  ATC_QPDPTIMER_COMMAND_PREFIX          ,    ATC_QPDPTIMER_LTE_Command      },
    {  ATC_QPPPTIMER_COMMAND_PREFIX          ,    ATC_QPPPTIMER_LTE_Command      },
    {  ATC_CGATTEX_COMMAND_PREFIX            ,    ATC_CGATTEX_LTE_Command        },
    {  ATC_QIREGAPP_COMMAND_PREFIX           ,    ATC_QIREGAPP_LTE_Command       },
#endif
    {  ATC_ECSIMCFG_COMMAND_PREFIX           ,    ATC_ECSIMCFG_LTE_Command       },
#ifdef SIB16_FEATURE
    {  ATC_QUTCTIME_COMMAND_PREFIX           ,    ATC_QUTCTIME_LTE_Command       },
#endif
    {  (unsigned char *)""                   ,(unsigned char (*)(unsigned char *,unsigned char *))NULL   }
};

const ST_ATC_COMMAND_ANAL_TABLE ATC_Star_CommandTable[] =
{
    {  ATC_ICCID_COMMAND_PREFIX              ,    ATC_ICCID_LTE_Command          },
    {  ATC_BAND_COMMAND_PREFIX               ,    ATC_BAND_LTE_Command           },
    {  ATC_BANDIND_COMMAND_PREFIX            ,    ATC_BANDIND_LTE_Command        },
    {  (unsigned char *)""                   ,(unsigned char (*)(unsigned char *,unsigned char *))NULL   }
};


const ST_ATC_AP_STR_EVENT_TABLE ATC_CommandTestToPS_EventTable[] =
{
    {   D_ATC_EVENT_CGCONTRDP_T,        ATC_CGCONTRDP_COMMAND_PREFIX     },
    {   D_ATC_EVENT_CGPADDR_T,          ATC_CGPADDR_COMMAND_PREFIX       },
    {   D_ATC_EVENT_COPS_T,             ATC_COPS_COMMAND_PREFIX          },
    {   D_ATC_EVENT_CPOL_T,             ATC_CPOL_COMMAND_PREFIX          },
#ifndef _FLASH_OPTIMIZE_
    {   D_ATC_EVENT_CACDC_T,            ATC_CACDC_COMMAND_PREFIX         },
#endif
    {   D_ATC_EVENT_NCONFIG_T,          ATC_NCONFIG_COMMAND_PREFIX       },
    {   D_ATC_EVENT_CGAPNRC_T,          ATC_CGAPNRC_COMMAND_PREFIX       },
#ifdef ESM_DEDICATED_EPS_BEARER
    {   D_ATC_EVENT_CGDSCONT_T,         ATC_CGDSCONT_COMMAND_PREFIX      },
    {   D_ATC_EVENT_CGSCONTRDP_T,       ATC_CGSCONTRDP_COMMAND_PREFIX    },
    {   D_ATC_EVENT_CGEQOSRDP_T,        ATC_CGEQOSRDP_COMMAND_PREFIX     },
    {   D_ATC_EVENT_CGTFTRDP_T,         ATC_CGTFTRDP_COMMAND_PREFIX      },
    {   D_ATC_EVENT_CGCMOD_T,           ATC_CGCMOD_COMMAND_PREFIX        },
#endif
#ifdef LTE_SMS_FEATURE
    {   D_ATC_EVENT_CPMS_T,             ATC_CPMS_COMMAND_PREFIX          },
#endif
    {   D_ATC_EVENT_NBAND_T,            ATC_MBAND_COMMAND_PREFIX         },
    {   D_ATC_EVENT_NPOWERCLASS_T,      ATC_NPOWERCLASS_COMMAND_PREFIX   },
#ifdef CSG_FEATURE
    {   D_ATC_EVENT_NCSG_T,             ATC_QCSG_COMMAND_PREFIX          },
#endif
    {   D_ATC_EVENT_MLOCKFREQ_T,        ATC_MLOCKFREQ_COMMAND_PREFIX     },
    {   D_ATC_EVENT_NULL,               NULL                             },
};
#if (!AT_TEST_OFF)
const ST_ATC_COMMAND_TEST_RSP_TABLE ATC_CommandTestRspTable[] =
{
//basic AT in atc_cmd_basic.c
    {  ATC_CFUN_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CFUN_RSP             },
    {  ATC_CGATT_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_CGATT_RSP            },
    {  ATC_CGACT_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_CGACT_RSP            },
    {  ATC_CGDCONT_COMMAND_PREFIX            ,      D_ATC_TEST_CMD_CGDCONT_RSP          },
    {  ATC_CREG_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CREG_RSP             },
    {  ATC_CGREG_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_CGREG_RSP            },
    {  ATC_CEREG_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_CEREG_RSP            },
    {  ATC_CSCON_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_CSCON_RSP            },
    {  ATC_CMEE_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CMEE_RSP             },
    {  ATC_CGSN_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CGSN_RSP             },
    {  ATC_CESQ_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CESQ_RSP             },
    {  ATC_CSQ_COMMAND_PREFIX                ,      D_ATC_TEST_CMD_CSQ_RSP              },
    {  ATC_CPSMS_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_CPSMS_RSP            },
    {  ATC_CNEC_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CNEC_RSP             },
    {  ATC_CLAC_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CGEREP_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_CGEREP_RSP           },
    {  ATC_CTZR_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CTZR_RSP             },
    {  ATC_CTZU_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CTZU_RSP             },
    {  ATC_CGPIAF_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_CGPIAF_RSP           },
    {  ATC_COPN_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CSCS_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CSCS_RSP             },
    {  ATC_CGMI_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CGMM_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CGMR_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
#ifdef ESM_DEDICATED_EPS_BEARER
    {  ATC_CGEQOS_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_CGEQOS_RSP           },
    {  ATC_CGTFT_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_CGTFT_RSP            },
#endif
    {  ATC_CIPCA_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_CIPCA_RSP            },
    {  ATC_CGAUTH_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_CGAUTH_RSP           },
    {  ATC_CEER_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CNUM_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CEDRXS_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_CEDRXS_RSP           },
    {  ATC_CEDRXRDP_COMMAND_PREFIX           ,      D_ATC_OK_RSP                        },
#ifndef _FLASH_OPTIMIZE_
    {  ATC_CSODCP_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_CSODCP_RSP           },
    {  ATC_CRTDCP_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_CRTDCP_RSP           },
    {  ATC_CCIOTOPT_COMMAND_PREFIX           ,      D_ATC_TEST_CMD_CCIOTOPT_RSP         },
    {  ATC_CNMPSD_COMMAND_PREFIX             ,      D_ATC_OK_RSP                        },
#endif
    {  ATC_CIMI_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CSIM_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CCHC_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CGLA_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CPWD_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CPWD_RSP             },
#if USR_CUSTOM2
    {  ATC_CPIN_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
#else
    {  ATC_CPIN_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CPIN_RSP             },
#endif
    {  ATC_CLCK_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CLCK_RSP             },
    {  ATC_CPINR_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_CPINR_RSP            },
#ifdef LCS_MOLR_ENABLE
    {  ATC_CMOLR_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_CMOLR_RSP            },
#endif
    {  ATC_GMI_COMMAND_PREFIX                ,      D_ATC_OK_RSP                        },
    {  ATC_GMM_COMMAND_PREFIX                ,      D_ATC_OK_RSP                        },
    {  ATC_GMR_COMMAND_PREFIX                ,      D_ATC_OK_RSP                        },
#ifdef LTE_SMS_FEATURE
    {  ATC_CSMS_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CSMS_RSP             },
    {  ATC_CMGF_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CMGF_RSP             },
    {  ATC_CSCA_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CSMP_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CNMI_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CNMI_RSP             },
    {  ATC_CMGW_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CMGR_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CMGD_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CMGD_RSP             },
    {  ATC_CMGL_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CMGL_RSP             },
    {  ATC_CMSS_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CMGS_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CNMA_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CNMA_RSP             },
    {  ATC_CMGC_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_CMMS_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CMMS_RSP             },
    {  ATC_CSCB_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CSCB_RSP             },
    {  ATC_CSDH_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CSDH_RSP             },
    {  ATC_QCMGS_COMMAND_PREFIX              ,      D_ATC_OK_RSP                        },
    {  ATC_QCMGR_COMMAND_PREFIX              ,      D_ATC_OK_RSP                        },
    {  ATC_CGSMS_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_CGSMS_RSP            },
#endif
#if USR_CUSTOM2
    {  ATC_NUESTATS_COMMAND_PREFIX           ,      D_ATC_TEST_CMD_NUESTATS_RSP         },
#endif
    {  ATC_NEARFCN_COMMAND_PREFIX            ,      D_ATC_OK_RSP                        },
    {  ATC_MCCID_COMMAND_PREFIX              ,      D_ATC_OK_RSP                        },
    {  ATC_NCSEARFCN_COMMAND_PREFIX          ,      D_ATC_OK_RSP                        },
    {  ATC_NL2THP_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_NL2THP_RSP           },
#ifndef _FLASH_OPTIMIZE_
    {  ATC_MPTWEDRXS_COMMAND_PREFIX          ,      D_ATC_TEST_CMD_MPTWEDRXS_RSP        },
    {  ATC_NQPODCP_COMMAND_PREFIX            ,      D_ATC_TEST_CMD_NQPODCP_RSP          },
    {  ATC_NSNPD_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_NSNPD_RSP            },
    {  ATC_NQPNPD_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_NQPNPD_RSP           },
    {  ATC_NRNPDM_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_NRNPDM_RSP           },
#endif

#if !defined(_FLASH_OPTIMIZE_) || (USR_CUSTOM2)
    {  ATC_NCIDSTATUS_COMMAND_PREFIX         ,      D_ATC_TEST_CMD_NCIDSTATUS_RSP       },
#endif
    {  ATC_NCPCDPR_COMMAND_PREFIX            ,      D_ATC_TEST_CMD_NCPCDPR_RSP          },
    {  ATC_NGACTR_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_NGACTR_RSP           },
    // { ATC_NPIN_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },   
    {  ATC_NTSETID_COMMAND_PREFIX            ,      D_ATC_OK_RSP                        },  
    // { ATC_NIPINFO_COMMAND_PREFIX            ,      D_ATC_TEST_CMD_NIPINFO_RSP          },
    {  ATC_MEID_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_MNBIOTEVENT_COMMAND_PREFIX        ,      D_ATC_OK_RSP                        },
    {  ATC_NLOCKF_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_NLOCKF_RSP           },
    {  ATC_QICSGP_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_QICSGP_RSP           },
    {  ATC_QIACT_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_QIACT_RSP            },
    {  ATC_QIACTEX_COMMAND_PREFIX            ,      D_ATC_TEST_CMD_QIACTEX_RSP          },
    {  ATC_QIDEACT_COMMAND_PREFIX            ,      D_ATC_TEST_CMD_QIDEACT_RSP          },
    {  ATC_QIDEACTEX_COMMAND_PREFIX          ,      D_ATC_TEST_CMD_QIDEACTEX_RSP        },
    {  ATC_QGSN_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_QCCID_COMMAND_PREFIX              ,      D_ATC_OK_RSP                        },
    {  ATC_QSPN_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
    {  ATC_QENG_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_QENG_RSP             },
#if USR_CUSTOM9
    {  ATC_GSN_COMMAND_PREFIX                ,      D_ATC_OK_RSP                        },
#else
    {  ATC_GSN_COMMAND_PREFIX                ,      D_ATC_TEST_CMD_GSN_RSP              },
#endif
    {  ATC_QINISTAT_COMMAND_PREFIX           ,      D_ATC_TEST_CMD_QINISTAT_RSP         },
    {  ATC_QSIMDET_COMMAND_PREFIX            ,      D_ATC_TEST_CMD_QSIMDET_RSP          },
    {  ATC_QSIMSTAT_COMMAND_PREFIX           ,      D_ATC_TEST_CMD_QSIMSTAT_RSP         },
    {  ATC_QNWINFO_COMMAND_PREFIX            ,      D_ATC_OK_RSP                        },
    {  ATC_QCSQ_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_QCSQ_RSP             },
    {  ATC_QGDCNT_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_QGDCNT_RSP           },
    {  ATC_QAUGDCNT_COMMAND_PREFIX           ,      D_ATC_TEST_CMD_QAUGDCNT_RSP         },
    {  ATC_QCELL_COMMAND_PREFIX              ,      D_ATC_OK_RSP                        },
    {  ATC_QCELLEX_COMMAND_PREFIX            ,      D_ATC_TEST_CMD_QCELLEX_RSP          },
    {  ATC_QPINC_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_QPINC_RSP            },    
    {  ATC_MCSEARFCN_COMMAND_PREFIX          ,      D_ATC_TEST_CMD_MCSEARFCN_RSP        },
    {  ATC_MUESTATS_COMMAND_PREFIX           ,      D_ATC_TEST_CMD_MUESTATS_RSP         },
    {  ATC_MPSRAT_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_MPSRAT_RSP           },
    {  ATC_MEMMTIMER_COMMAND_PREFIX          ,      D_ATC_TEST_CMD_MEMMTIMER_RSP        },
    {  ATC_MWIFISCANCFG_COMMAND_PREFIX       ,      D_ATC_TEST_CMD_MWIFISCANCFG_RSP     },
    {  ATC_QWIFISCAN_COMMAND_PREFIX          ,      D_ATC_TEST_CMD_QWIFISCAN_RSP        },
    {  ATC_QBLACKCELLCFG_COMMAND_PREFIX      ,      D_ATC_TEST_CMD_QBLACKCELLCFG_RSP    },
    {  ATC_QSIMSWITCH_COMMAND_PREFIX         ,      D_ATC_TEST_CMD_QSIMSWICH_RSP        },
    {  ATC_QSCLKEX_COMMAND_PREFIX            ,      D_ATC_TEST_CMD_QSCLKEX_RSP          },
#if VER_QUEC && (!USR_CUSTOM2)
    {  ATC_CCHO_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CCHO_RSP             },
#else
    {  ATC_CCHO_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
#endif
#if VER_QUEC
    {  ATC_CRSM_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CRSM_RSP             },
#else
    {  ATC_CRSM_COMMAND_PREFIX               ,      D_ATC_OK_RSP                        },
#endif
    {  ATC_NWSDVOLT_COMMAND_PREFIX           ,      D_ATC_TEST_CMD_NWSDVOLT_RSP         },
    {  ATC_MUECONFIG_COMMAND_PREFIX          ,      D_ATC_TEST_CMD_MUECONFIG_RSP        },
    {  ATC_CCED_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CCED_RSP             },
    {  ATC_CEMODE_COMMAND_PREFIX             ,      D_ATC_TEST_CMD_CEMODE_RSP           },
    {  ATC_QINDCFG_COMMAND_PREFIX            ,      D_ATC_TEST_CMD_QINDCFG_RSP          },
    {  ATC_QCELLINFO_COMMAND_PREFIX          ,      D_ATC_OK_RSP                        },
    {  ATC_QDSIM_COMMAND_PREFIX              ,      D_ATC_TEST_CMD_QDSIM_RSP            },
    {  ATC_CUSD_COMMAND_PREFIX               ,      D_ATC_TEST_CMD_CUSD_RSP             },
    {  ATC_QEHPLMN_COMMAND_PREFIX            ,      D_ATC_OK_RSP                        },
    {  ATC_ECSIMCFG_COMMAND_PREFIX           ,      D_ATC_TEST_CMD_ECSIMCFG_RSP         },
    {  (unsigned char *) ""                  ,      NULL                                }
};
#endif
const ST_ATC_COMMAND_ANAL_TABLE ATC_Symbol_CommandTable[] =
{
    {  (unsigned char *)"F"          ,     ATC_F_LTE_Command              },
    {  (unsigned char *)"F0"         ,     ATC_F_LTE_Command              },
    {  (unsigned char *)"W"          ,     ATC_W_LTE_Command              },
    {  (unsigned char *)"W0"         ,     ATC_W_LTE_Command              },
    {  (unsigned char *)""           ,(unsigned char (*)(unsigned char *,unsigned char *))NULL   }
};

const ST_ATC_COMMAND_ANAL_TABLE ATC_Single_CommandTable[] =
{
    {  (unsigned char *)"Z"          ,     ATC_Z_LTE_Command              },
    {  (unsigned char *)"Z0"         ,     ATC_Z_LTE_Command              },
    {  (unsigned char *)"I"          ,     ATC_ATI_LTE_Command            },
#if USR_CUSTOM12
    {  (unsigned char *)"A"          ,     ATC_A_LTE_Command              },
#endif
    {  (unsigned char *)""           ,(unsigned char (*)(unsigned char *,unsigned char *))NULL   }
};

/* FAC */
const ST_ATC_FAC_TABLE ATC_Fac_Table[D_ATC_FAC_NUM] =
{
    /* Facility lock +CLCK  27.007 7.4 */
    //{   D_ATC_PARAM_FAC_CS,              "CS"                  },
    //{   D_ATC_PARAM_FAC_PS,              "PS"                  },
    //{   D_ATC_PARAM_FAC_PF,              "PF"                  },
      {   D_ATC_PARAM_FAC_SC,              "SC"                  },
    //{   D_ATC_PARAM_FAC_AO,              "AO"                  },
    //{   D_ATC_PARAM_FAC_OI,              "OI"                  },
    //{   D_ATC_PARAM_FAC_OX,              "OX"                  },
    //{   D_ATC_PARAM_FAC_AI,              "AI"                  },
    //{   D_ATC_PARAM_FAC_IR,              "IR"                  },
    //{   D_ATC_PARAM_FAC_NT,              "NT"                  },
    //{   D_ATC_PARAM_FAC_NM,              "NM"                  },
    //{   D_ATC_PARAM_FAC_NS,              "NS"                  },
    //{   D_ATC_PARAM_FAC_NA,              "NA"                  },
    //{   D_ATC_PARAM_FAC_AB,              "AB"                  },
    //{   D_ATC_PARAM_FAC_AG,              "AG"                  },
    //{   D_ATC_PARAM_FAC_AC,              "AC"                  },
    //{   D_ATC_PARAM_FAC_FD,              "FD"                  },
    //{   D_ATC_PARAM_FAC_PN,              "PN"                  },
    //{   D_ATC_PARAM_FAC_PU,              "PU"                  },
    //{   D_ATC_PARAM_FAC_PP,              "PP"                  },
    //{   D_ATC_PARAM_FAC_PC,              "PC"                  },
    /* Change password +CPWD  27.007 7.5 */
    //{   D_ATC_PARAM_FAC_P2,              "P2"                  }
};
#if USR_CUSTOM2
const ST_ATC_STR_TABLE ATC_NUESTATS_Table[ATC_NUESTATS_MAX] = 
{
    {   ATC_NUESTATS_TYPE_RADIO,          "RADIO"                },
    {   ATC_NUESTATS_TYPE_CELL,           "CELL"                 },
    {   ATC_NUESTATS_TYPE_BLER,           "BLER"                 },
    {   ATC_NUESTATS_TYPE_THP,            "THP"                  },
    {   ATC_NUESTATS_TYPE_APPSMEM,        "APPSMEM"              },
    {   ATC_NUESTATS_SBAND,               "SBAND"                },
    {   ATC_NUESTATS_TYPE_ALL,            "ALL"                  }
};
#endif

const ST_ATC_STR_TABLE ATC_MUESTATS_Table[6] = 
{
    {   ATC_NUESTATS_TYPE_RADIO,          "radio"                },
    {   ATC_NUESTATS_TYPE_CELL,           "cell"                 },
    {   ATC_NUESTATS_TYPE_BLER,           "bler"                 },
    {   ATC_NUESTATS_TYPE_THP,            "thp"                  },
    {   ATC_NUESTATS_SBAND,               "sband"                },
    {   ATC_NUESTATS_TYPE_ALL,            "all"                  }
};

const ST_ATC_STR_TABLE ATC_PdpType_Table[9] = 
{
    {   D_PDP_TYPE_IPV4,          "IP"             },
    {   D_PDP_TYPE_IPV4,          "ip"             },
    {   D_PDP_TYPE_IPV6,          "IPV6"           },
    {   D_PDP_TYPE_IPV6,          "ipv6"           },
    {   D_PDP_TYPE_IPV4V6,        "IPV4V6"         },
    {   D_PDP_TYPE_IPV4V6,        "ipv4v6"         },
    {   D_PDP_TYPE_NonIP,         "Non-IP"         },
    {   D_PDP_TYPE_NonIP,         "non-ip"         },
    {   D_PDP_TYPE_NonIP,         "NONIP"          }
};

const ST_ATC_STR_TABLE ATC_CharSet_Table[4] =
{
    { D_CHAR_SET_SELETE_GSM,      "GSM"            },
    { D_CHAR_SET_SELETE_IRA,      "IRA"            },
    { D_CHAR_SET_SELETE_UCS2,     "UCS2"           },
    { D_CHAR_SET_SELETE_HEX,      "HEX"            },
};

const unsigned int ATC_AP_PsRegEventIdMapTable[D_ATC_USER_REG_EVENT_TBL_SIZE][2] =
{
    { D_XY_PS_REG_EVENT_SIMST,         D_ATC_AP_SIMST_IND         },
    { D_XY_PS_REG_EVENT_XYIPDNS,       D_ATC_AP_XYIPDNS_IND       },
    { D_XY_PS_REG_EVENT_CRTDCP,        D_ATC_AP_CRTDCP_IND        },
    { D_XY_PS_REG_EVENT_CGAPNRC,       D_ATC_AP_CGAPNRC_IND       },
    { D_XY_PS_REG_EVENT_CGEV,          D_ATC_AP_CGEV_IND          },
    { D_XY_PS_REG_EVENT_CEREG,         D_ATC_AP_CEREG_IND         },
    { D_XY_PS_REG_EVENT_CSCON,         D_ATC_AP_CSCON_IND         },
    { D_XY_PS_REG_EVENT_NPTWEDRXP,     D_ATC_AP_NPTWEDRXP_IND     },
    
    { D_XY_PS_REG_EVENT_CEDRXP,        D_ATC_AP_CEDRXP_IND        },
    { D_XY_PS_REG_EVENT_CCIOTOPTI,     D_ATC_AP_CCIOTOPTI_IND     },
    { D_XY_PS_REG_EVENT_PINSTATUS,     D_ATC_AP_PINSTATUS_IND     },
    { D_XY_PS_REG_EVENT_IPSN,          D_ATC_AP_IPSN_IND          },
    { D_XY_PS_REG_EVENT_LOCALTIMEINFO, D_ATC_AP_LOCALTIMEINFO_IND },
    { D_XY_PS_REG_EVENT_PDNIPADDR,     D_ATC_AP_IPADDR_IND        },
    { D_XY_PS_REG_EVENT_NGACTR,        D_ATC_AP_NGACTR_IND        },
    { D_XY_PS_REG_EVENT_CMT,           D_ATC_AP_CMT_IND           },
    { D_XY_PS_REG_EVENT_CELLSRCH,      D_ATC_AP_CELL_SRCH_IND     },
    
    { D_XY_PS_REG_EVENT_CMOLRE,        D_ATC_AP_CMOLRE_IND        },
    { D_XY_PS_REG_EVENT_CMOLRG,        D_ATC_AP_CMOLRG_IND        },
    { D_XY_PS_REG_EVENT_L2_THP,        D_ATC_AP_L2THP_IND         },
    { D_XY_PS_REG_EVENT_MALLOC_ADDR,   D_ATC_AP_MALLOCADDR_IND    },
    { D_XY_PS_REG_EVENT_NoCarrier,     D_ATC_NO_CARRIER_IND       },
    { D_XY_PS_REG_EVENT_CESQ_Ind ,     D_ATC_AP_EVENT_CESQ_IND    },
    { D_XY_PS_REG_EVENT_PSINFO ,       D_ATC_AP_PSINFO_IND        },
    { D_XY_PS_REG_EVENT_NRNPDM,        D_ATC_AP_NRNPDM_IND        },
    { D_XY_PS_REG_EVENT_HW_LM_INTERFACE, D_HW_LM_INTERFACE_IND    },
    { D_XY_PS_REG_EVENT_CMTI,          D_ATC_AP_CMTI_IND          },
    { D_XY_PS_REG_EVENT_CDS,           D_ATC_AP_CDS_IND           },
    { D_XY_PS_REG_EVENT_CDSI,          D_ATC_AP_CDSI_IND          },
    { D_XY_PS_REG_EVENT_WIFISCAN,      D_ATC_AP_QWIFISCAN_IND     },
    //{ D_XY_PS_REG_EVENT_QIACTEX,       D_ATC_AP_QIACTEX_IND       },
    //{ D_XY_PS_REG_EVENT_QIDEACTEX,     D_ATC_AP_QIDEACTEX_IND     },
};


const ST_ATC_AP_PLMN_NAME_TABLE ATC_PlmnName_Table[13] = 
{
    { "46000"  , "CHINA MOBILE",   "CMCC"    },
    { "46001"  , "CHN-UNICOM",     "UNICOM"  },
    { "46002"  , "CHINA MOBILE",   "CMCC"    },
    { "46003"  , "CHN-CT",         "CT"      },
    { "46004"  , "CHINA MOBILE",   "CMCC"    },
    { "46005"  , "CHN-CT",         "CT"      },
    { "46006"  , "CHN-UNICOM",     "UNICOM"  },
    { "46007"  , "CHINA MOBILE",   "CMCC"    },
    { "46008"  , "CHINA MOBILE",   "CMCC"    },
    { "46009"  , "CHN-UNICOM",     "UNICOM"  },
    { "46010"  , "CHN-UNICOM",     "UNICOM"  },
    { "46011"  , "CHN-CT",         "CT"      },
    { "46015"  , "China Broadnet", "CBN"     },
};

#if VER_CM
const ST_ATC_AP_PLMN_NAME_TABLE ATC_OperatonName_Table_ZY[12] = 
{
    { "46000"  , "CHINA MOBILE",   "CMCC"    },
    { "46001"  , "CHN-UNICOM",     "UNICOM"  },
    { "46002"  , "CHINA MOBILE",   "CMCC"    },
    { "46003"  , "CHN-CT",         "CT"      },
    { "46004"  , "CHINA MOBILE",   "CMCC"    },
    { "46005"  , "CHINA TELECOM",  "CTCC"    },
    { "46006"  , "CHN-UNICOM",     "UNICOM"  },
    { "46007"  , "CHINA MOBILE",   "CMCC"    },
    { "46008"  , "CHINA MOBILE",   "CMCC"    },
    { "46009"  , "CHN-UNICOM",     "UNICOM"  },
    { "46011"  , "CHN-CT",         "CT"      },
    { "46015"  , "CHINA BROADNET", "CBN"     },
};
#else
const ST_ATC_AP_OPERTION_NAME_TABLE ATC_OperatonName_Table[10] = 
{
    { "46000"  , "CMCC"        },
    { "46001"  , "UNICOM"      },
    { "46002"  , "CMCC"        },
    { "46003"  , "CTCC"        },
    { "46005"  , "CTCC"        },
    { "46006"  , "UNICOM"      },
    { "46007"  , "CMCC"        },
    { "46008"  , "CMCC"        },
    { "46009"  , "UNICOM"      },
    { "46011"  , "CTCC"        },
};
#endif

