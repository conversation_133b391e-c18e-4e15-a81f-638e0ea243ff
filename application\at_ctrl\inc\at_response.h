#pragma once

#include <stdint.h>

#define APP_AT_RESP_HANDLE_STACKSIZE 0x800
#define APP_AT_RESP_HANDLE_PRIO osPriorityNormal3
#define APP_AT_RESP_HANDLE_NAME "appResp"
#define APP_AT_RESP_MSG_QLEN 48

/**
 * @brief app注册的Resp回调函数类型声明
 */
typedef void (*appAtRespHandle)(uint8_t atHandle, uint8_t respId, uint16_t argLen, void *arg);

/**
 * @brief app类型,用于标识Resp来自哪个app
 */
typedef enum
{
    SOURCE_SOCKET = 0,
    SOURCE_SOCKET_SSL,
    SOURCE_DNS,
    SOURCE_MQTT,
    SOURCE_CM_SOCKET,
    SOURCE_DEF_FTP,
    SOURCE_CM_FTP,
    SOURCE_GNSS,
    SOURCE_HTTP,
    SOURCE_CTWING_MQTT,
    SOURCE_CM_SSL_SOCKET,
    SOURCE_FOTA,
    SOURCE_MN316_SOCKET,
    SOURCR_MAX,
} appSource;

/**
 * @brief app Response ID
 */
typedef enum
{
    /* default socket */
    DEF_SOCK_RESP_ERROR = 0,
    DEF_SOCK_RESP_SEND,
    DEF_SOCK_RESP_SEND_PSTHDATA,
    DEF_SOCK_RESP_TCP_SEND_ACKED,
    DEF_SOCK_RESP_ENTER_SEND_STATE,
    DEF_SOCK_RESP_READ,
    DEF_SOCK_RESP_CREATE,
    DEF_SOCK_RESP_CLOSE,
    DEF_SOCK_RESP_QUREY_STATE,
    DEF_SOCK_RESP_SWITCHMODE,
    DEF_SOCK_RESP_QUERY_SENDLEN,
    DEF_SOCK_RESP_SVR_ACCEPT_CLIENT,
    DEF_SOCK_RESP_SVR_ACCEPT_CLIENT_ERROR,
    DEF_SOCK_RESP_DL_DATA,
    DEF_SOCK_RESP_DATA_CONFIG,
    DEF_SOCK_RESP_WAKEUP,
    DEF_SOCK_RESP_HEART_INFO,

    /* ssl socket */
    DEF_SSL_SOCK_RESP_CREATE,
    DEF_SSL_SOCK_RESP_QUERY_STATE,
    DEF_SSL_SOCK_RESP_CLOSE,
    DEF_SSL_SOCK_RESP_DL_DATA,
    DEF_SSL_SOCK_RESP_READ,

    /* mqtt */
    DEF_MQTT_RESP_OPEN,
    DEF_MQTT_RESP_CLOSE,
    DEF_MQTT_RESP_CONN,
    DEF_MQTT_RESP_DISC,
    DEF_MQTT_RESP_SUB,
    DEF_MQTT_RESP_UNSUB,
    DEF_MQTT_RESP_PUB,
    DEF_MQTT_RESP_PUBRETRY,
    DEF_MQTT_RESP_PUBERR,
    DEF_MQTT_RESP_PUBREC,
    DEF_MQTT_RESP_PUBREL,
    DEF_MQTT_RESP_PUBCOMP,
    DEF_MQTT_RESP_PUBLISH,
    DEF_MQTT_RESP_STAT,
    DEF_MQTT_RESP_RECV,
    DEF_MQTT_RESP_PING,

    /* app */
    APP_RESP_ASYNC_DNS_FOUND,

    /* cm socket */
    CM_SOCK_RESP_ERROR,
    CM_SOCK_RESP_SEND_DIRECTLY,
    CM_SOCK_RESP_SEND,
    CM_SOCK_RESP_SEND_PSTHDATA,
    CM_SOCK_RESP_TCP_SEND_ACKED,
    CM_SOCK_RESP_ENTER_SEND_STATE,
    CM_SOCK_RESP_READ,
    CM_SOCK_RESP_CREATE,
    CM_SOCK_RESP_CLOSE,
    CM_SOCK_RESP_DISC,
    CM_SOCK_RESP_QUREY_STATE,
    CM_SOCK_RESP_QUREY_SACK,
    CM_SOCK_RESP_SWITCHMODE,
    CM_SOCK_RESP_DL_DATA,
    CM_SOCK_RESP_CONFIG,

    /* default ftp */
    DEF_FTP_RESP_COMM,
    DEF_FTP_RESP_OPEN,
    DEF_FTP_RESP_CWD,
    DEF_FTP_RESP_PWD,
    DEF_FTP_RESP_PUT,
    DEF_FTP_RESP_GET,
    DEF_FTP_RESP_DEL,
    DEF_FTP_RESP_SIZE,
    DEF_FTP_RESP_MDTM,
    DEF_FTP_RESP_MKDIR,
    DEF_FTP_RESP_RMDIR,
    DEF_FTP_RESP_RENAME,
    DEF_FTP_RESP_LIST,
    DEF_FTP_RESP_NLST,
    DEF_FTP_RESP_MLSD,
    DEF_FTP_RESP_LEN,
    DEF_FTP_RESP_STATE,
    DEF_FTP_RESP_CLOSE,
    DEF_FTP_RESP_DL_DATA,
    DEF_FTP_RESP_CFG_ACCOUNT,
    DEF_FTP_RESP_PASSTH_STATE_SWITCH,

    /* GNSS 外发数据专用 */
    DEF_GNSS_STREAM,	 /*gnss芯片发送来的码流数据，包括URC/AT应答包/特殊码流等*/

    /* http */
    DEF_HTTP_RESP_URC_IND,
    DEF_HTTP_RESP_READ_IND,

    /* fota */
    DEF_FOTA_RESP_DOWNLOAD_IND,

    /* mn316 socket */
    MN316_SOCK_RESP_ERROR,
    MN316_SOCK_RESP_SEND,
    MN316_SOCK_RESP_TCP_SEND_ACKED,
    MN316_SOCK_RESP_READ,
    MN316_SOCK_RESP_CREATE,
    MN316_SOCK_RESP_CONNECT,
    MN316_SOCK_RESP_CLOSE,
    MN316_SOCK_RESP_DISC,
    MN316_SOCK_RESP_DL_DATA,
} appAtResponseId;

/**
 * @brief app发送消息给response后台代理线程,以统一由该代理线程内部根据source识别出appAtRegRespHandle对应的注册函数，组网出AT命令后发送给指定的atHandle。
 * @param atHandle [IN] at通道标识 @see @ref AT_TTY_FD
 * @param source [IN] app类型 @see @ref appSource
 * @param respId [IN] response消息ID @see @ref appAtResponseId
 * @param argLen [IN] 携带的参数长度
 * @param arg [IN] 参数，通常为结构体，不同ResponseId有不同的结构体
 * @return XY_OK:Resp上报成功, XY_ERR:Resp上报失败,接收队列已满
 * @note  之所以通过response后台代理线程来组装AT命令并写AT通道，是为了解决在APP应用模块直接组装AT命令并写AT通道带来的长时间线程阻塞问题。
 */
int appAtRespReport(uint8_t atHandle, uint8_t source, uint8_t respId, uint16_t argLen, void *arg);

/**
 * @brief app模块注册给response后台代理线程的消息处理函数，该注册函数用于组装对应的AT命令，并发送给相应的AT通道。
 * @param source [IN] app类型 @see @ref appSource
 * @param callback [IN] 回调处理函数, @see @ref appAtRespHandle
 * @return XY_OK:注册成功, XY_ERR:注册失败
 * @note  该接口通常在模块初始化时调用注册，与appAtRespReport()接口关联使用，以解决在APP应用模块直接组装AT命令并写AT通道带来的长时间线程阻塞问题。
 */
int appAtRegRespHandle(uint32_t source, appAtRespHandle callback);
