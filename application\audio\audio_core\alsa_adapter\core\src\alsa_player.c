#include "alsa.h"
#include "string.h"
#include <stdlib.h>
#include "hal_trace.h"
#include "xy_fs.h"
#include "heap_api.h"

typedef struct wave_header
{
    uint8_t riff[4];     // 4
    uint32_t size;       // 4
    uint8_t waveFlag[4]; // 4
    uint8_t fmt[4];      // 4
    uint32_t fmtLen;     // 4
    uint16_t tag;        // 2
    uint16_t channels;   // 2
    uint32_t sampFreq;   // 4
    uint32_t byteRate;   // 4
    uint16_t blockAlign; // 2
    uint16_t bitSamp;    // 2
    uint8_t dataFlag[4]; // 4
    uint32_t length;     // 4
} wave_header_t;

#define WAVE_FILE_HEADER_SIZE 0x2CU

static int analy_wav_header(uint8_t **in_out_ptr, wave_header_t *out_wav_header_info)
{
    uint8_t *in_ptr = *in_out_ptr;
    TRACE(0, "%c%c%c%c", in_ptr[0], in_ptr[1], in_ptr[2], in_ptr[3]);
    if (memcmp(in_ptr, "RIFF", 4))
    {
        TRACE(0, "file not WAV");
        return -1;
    }
    wave_header_t *waveh = (wave_header_t *)in_ptr;
    if (out_wav_header_info)
    {
        memcpy(out_wav_header_info, waveh, sizeof(wave_header_t));
    }
    // TODO
    in_ptr += WAVE_FILE_HEADER_SIZE;
    *in_out_ptr = in_ptr;
    return 0;
}

int alsa_wav_play_file(const char *path, uint8_t ultra_sound_flag)
{
    XY_FILE* fd = NULL;

    fd = xy_fopen(path, "r");
    if (fd == NULL)
    {
        TRACE(0, "alsa_wav_play_file error: failed to open %s", path);
        return -1;
    }
    TRACE(0, "open [%s] fd = %d", path, fd);

    wave_header_t wave_header;
    uint8_t file_header = 0;
    uint32_t read_len = 1024;
    uint8_t *alsa_wav_player_mem_buf = (uint8_t *)audio_calloc(1024, sizeof(uint8_t));
    if (!alsa_wav_player_mem_buf)
        return -1;
    uint8_t *ptr = alsa_wav_player_mem_buf;
    uint32_t header_offset = 0;
    alsa_handle_t *h = NULL;
    int ret = 0;

    while (1)
    {
        read_len = xy_fread(ptr, 1024, 1, fd);
        if (read_len > 0)
        {
            if (file_header == 0)
            {
                if (0 != analy_wav_header(&ptr, &wave_header))
                {
                    ret = -2;
                    break;
                }

                file_header = 1;
                header_offset = ptr - alsa_wav_player_mem_buf;
                TRACE(0, "wav file [%s]: sample rate %d, channel %d, header_offset = %d", path, wave_header.sampFreq, wave_header.channels, header_offset);
                if (ultra_sound_flag)
                {
#ifdef ALSA_SUPPORT_ULTRASOUND
                    h = alsa_open(ALSA_MODE_OUT | ALSA_MODE_ULTRASONIC, wave_header.sampFreq, wave_header.channels, 16);
#else
                    h = NULL;
                    TRACE(0, "[%s] not support ULTRASONIC", __func__);
#endif
                }
                else
                {
                    h = alsa_open(ALSA_MODE_OUT, wave_header.sampFreq, wave_header.channels, 16);
                }

                if (!h)
                {
                    ret = -3;
                    break;
                }

                read_len -= header_offset;
            }

            alsa_write(h, ptr, read_len);
            ptr = alsa_wav_player_mem_buf;
        }
        else
        {
            TRACE(0, "play file [%s] finish", path);
            if (file_header)
            {
                ret = -4;
                file_header = 0;
            }
            break;
        }
    }

    if (h)
    {
        alsa_stop(h);
        alsa_close(h);
    }
    audio_free(alsa_wav_player_mem_buf);
    xy_fclose(fd);
    return ret;
}

int alsa_wav_play_mem(uint8_t *data, uint32_t len)
{
    wave_header_t wave_header;
    uint8_t *ptr = data;
    uint32_t remain_len = len;
    uint32_t write_len = 0;

    if (0 != analy_wav_header(&ptr, &wave_header))
    {
        return -1;
    }

    alsa_handle_t *h = alsa_open(ALSA_MODE_OUT, wave_header.sampFreq, wave_header.channels, 16);
    if (!h)
    {
        return -2;
    }

    while (remain_len)
    {
        write_len = remain_len > 1024 ? 1024 : remain_len;
        alsa_write(h, ptr, write_len);
        ptr += write_len;
        remain_len -= write_len;
    }

    alsa_stop(h);
    alsa_close(h);
    return 0;
}

// Function to add WAV header to PCM data
void add_wav_header(uint8_t* pcmData, uint32_t sampleRate, uint16_t numChannels, uint16_t bitsPerSample, uint32_t dataSize) 
{
    // Calculate the total file size
    uint32_t fileSize = dataSize + 44;

    // Write the WAV header
    pcmData[0] = 'R';  // ChunkID: "RIFF"
    pcmData[1] = 'I';
    pcmData[2] = 'F';
    pcmData[3] = 'F';
    pcmData[4] = (fileSize >> 0) & 0xFF;  // ChunkSize
    pcmData[5] = (fileSize >> 8) & 0xFF;
    pcmData[6] = (fileSize >> 16) & 0xFF;
    pcmData[7] = (fileSize >> 24) & 0xFF;
    pcmData[8] = 'W';  // Format: "WAVE"
    pcmData[9] = 'A';
    pcmData[10] = 'V';
    pcmData[11] = 'E';
    pcmData[12] = 'f';  // Subchunk1ID: "fmt "
    pcmData[13] = 'm';
    pcmData[14] = 't';
    pcmData[15] = ' ';
    pcmData[16] = 16;  // Subchunk1Size: 16 for PCM
    pcmData[17] = 0;
    pcmData[18] = 0;
    pcmData[19] = 0;
    pcmData[20] = 1;  // AudioFormat: 1 for PCM
    pcmData[21] = 0;
    pcmData[22] = numChannels;  // NumChannels
    pcmData[23] = 0;
    pcmData[24] = (sampleRate >> 0) & 0xFF;  // SampleRate
    pcmData[25] = (sampleRate >> 8) & 0xFF;
    pcmData[26] = (sampleRate >> 16) & 0xFF;
    pcmData[27] = (sampleRate >> 24) & 0xFF;
    pcmData[28] = (sampleRate * numChannels * bitsPerSample / 8) & 0xFF;  // ByteRate
    pcmData[29] = ((sampleRate * numChannels * bitsPerSample / 8) >> 8) & 0xFF;
    pcmData[30] = ((sampleRate * numChannels * bitsPerSample / 8) >> 16) & 0xFF;
    pcmData[31] = ((sampleRate * numChannels * bitsPerSample / 8) >> 24) & 0xFF;
    pcmData[32] = (numChannels * bitsPerSample / 8) & 0xFF;  // BlockAlign
    pcmData[33] = ((numChannels * bitsPerSample / 8) >> 8) & 0xFF;
    pcmData[34] = bitsPerSample;  // BitsPerSample
    pcmData[35] = 0;
    pcmData[36] = 'd';  // Subchunk2ID: "data"
    pcmData[37] = 'a';
    pcmData[38] = 't';
    pcmData[39] = 'a';
    pcmData[40] = (dataSize >> 0) & 0xFF;  // Subchunk2Size
    pcmData[41] = (dataSize >> 8) & 0xFF;
    pcmData[42] = (dataSize >> 16) & 0xFF;
    pcmData[43] = (dataSize >> 24) & 0xFF;
}
