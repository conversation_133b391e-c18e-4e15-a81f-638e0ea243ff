#if PS_TEST_MODE
#ifndef _PSTEST_API_H_
#define _PSTEST_API_H_

#include "ux_port.h"

extern bool xy_get_RTC_time(xy_walltime_t *walltime);
extern void ps_test_get_net_time();
extern void ps_test_case_timer_callback(void);
extern void ps_test_at_timer_callback(void);
extern void ps_test_sim_state_callback(void);
extern UCHAR ps_test_wait_time(USHORT timeout);
extern void ps_test_save_wait_atkey(UCHAR* pWaitForATkey, USHORT ulAtkeyLen);
extern void ps_test_clean_wait_atkey();
extern UCHAR ps_test_send_at_wait_urc(UCHAR* pATdata, UCHAR* pKeyStr, UCHAR* pWaitForInfo, USHORT timeout);
extern UCHAR ps_test_wait_urc(UCHAR* pWaitForATkey,  USHORT timeout);
extern void ps_test_recv_at(UCHAR* pURC_data, USHORT ulDataLen);
extern void Ps_Test_Msg_Check(UCHAR* pMsg);
extern void Ps_Test_AT_Time_Out(UCHAR* pMsg);
extern void Ps_Test_CASE_Time_Out(UCHAR* pMsg);
extern void Ps_Test_Start();
extern UCHAR ps_test_AT_init();
extern UCHAR Ps_test_get_share_info();
extern void Ps_test_set_share_info(unsigned char ucBitValue );
extern UCHAR ps_test_cops_plmn_search();
extern UCHAR ps_test_freq_scan();
extern UCHAR ps_test_idel_cfun01();
extern UCHAR ps_test_up_perf();
extern UCHAR ps_test_down_perf();
extern UCHAR ps_test_paging();
extern UCHAR ps_test_mutil_default_cid();
extern UCHAR ps_test_secondary_cid();
extern UCHAR ps_test_security_algorithm();
extern UCHAR ps_test_idle_ping_200();
extern UCHAR ps_test_connect_ping_200();
extern UCHAR ps_test_idle_keep();
extern void ps_test_cfun0_api();
extern void Ps_test_at_task_rls();
extern void Ps_Send_to_Test_Msg(void* pMsg, ST_TEST_AP_MSG_INFO* pMsgInfo);
extern void urc_cesq_Callback(unsigned long eventId, void *param, int paramLen);
#endif
#endif
