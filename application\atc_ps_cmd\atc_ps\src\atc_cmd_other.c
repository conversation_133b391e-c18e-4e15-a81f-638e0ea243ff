/*******************************************************************************
@(#)Copyright(C)2016,HuaChang Technology (Dalian) Co,. LTD.
File name   : atc_cmd_basic.c
Description : 
Function List:
History:
1. Dep2_066    2016.12.20  Create
*******************************************************************************/
#include "atc_ps.h"

/*******************************************************************************
  MODULE    : ATC_GSN_LTE_Command
  FORMAT    : AT+GSN[=<snt>]
  FUNCTION  : <snt>: integer type;
    0   returns <sn>
    1   returns the IMEI (International Mobile station Equipment Identity)
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_GSN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_GSN_PARAMETER*  ptEvent_Param = (ST_ATC_GSN_PARAMETER*)pEventBuffer;
#if USR_CUSTOM9 || USR_CUSTOM10
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->ucSntFlg = D_ATC_FLAG_TRUE;
        ptEvent_Param->ucSnt = 1;
        ptEvent_Param->usEvent = D_ATC_EVENT_GSN;
    }
#else
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_GSN_FORMAT, pCommandBuffer, &ptEvent_Param->ucSnt) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_GSN;
        ptEvent_Param->ucSntFlg = D_ATC_FLAG_TRUE;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_GSN;
#if USR_CUSTOM8 || USR_CUSTOM14
        ptEvent_Param->ucSntFlg = D_ATC_FLAG_TRUE;
        ptEvent_Param->ucSnt = 1;
#else
        ptEvent_Param->ucSntFlg = D_ATC_FLAG_FALSE;
#endif
    }
#endif
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
MODULE    : ATC_CGMI_LTE_Command
FORMAT    : AT+CGMI
FUNCTION  : 
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_GMI_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT* ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;
    
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_GMI;
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s\r\n", g_softap_fac_nv->manufacturer);
        AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        AtcAp_SendOkRsp();
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;
}

/*******************************************************************************
MODULE    : ATC_CGMM_LTE_Command
FORMAT    : AT+CGMM
FUNCTION  : 
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_GMM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT* ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;
    
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_GMM;
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s\r\n", g_softap_fac_nv->modul_ver);
        AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        AtcAp_SendOkRsp();
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;
}

/*******************************************************************************
MODULE    : ATC_CGMR_LTE_Command
FORMAT    : AT+CGMR
FUNCTION  : 
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_GMR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT* ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;
    
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_GMR;
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s\r\n", g_softap_fac_nv->versionExt);
        AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        AtcAp_SendOkRsp();
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;
}


/*******************************************************************************
  MODULE    : ATC_RAI_LTE_Command
  FUNCTION  : 
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_RAI_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER* ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;
    
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_RAI_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_RAI;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_NCSG_LTE_Command
  FUNCTION  : AT+QCSG
  NOTE      :
  HISTORY   :
*******************************************************************************/
#ifdef CSG_FEATURE
unsigned char ATC_NCSG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NCSG_PARAMETER*  ptEvent_Param = (ST_ATC_NCSG_PARAMETER*)pEventBuffer;
    unsigned char *pucPlmnStr = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NCSG_FORMAT, pCommandBuffer, 
                            &ptEvent_Param->ucMode,
                            &ptEvent_Param->ucCsgIdFlg,&ptEvent_Param->ulCsgId,
                            &ptEvent_Param->ucPerFlag,&pucPlmnStr) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        
        if(pucPlmnStr != NULL)
        {
            if(ATC_AP_FALSE == AtcAp_ConvertOperStr2Hex(pucPlmnStr, ptEvent_Param->aucPer, sizeof(pucPlmnStr)))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NCSG;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NCSG_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}
#endif
/*******************************************************************************
  MODULE    : ATC_NCSG_LTE_Command
  FUNCTION  : AT+QICSGP=<cid>[,<pdp_type>[,<apn>][,<username>[,<password>][,<authport>]]]
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_QICSGP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char ucPDPtypeFlg = 0;
    unsigned char ucAuthportFlg = 0;
    unsigned char* pApnValue = NULL;
    unsigned char* pUsername = NULL;
    unsigned char* pPassword = NULL;
    ST_ATC_QICSGP_PARAMETER* ptEvent_Param = (ST_ATC_QICSGP_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QICSGP_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucCid,
                            &ucPDPtypeFlg,
                            &ptEvent_Param->ucPdpType,
                            NULL,
                            &pApnValue,
                            NULL,
                            &pUsername,
                            NULL,
                            &pPassword,
                            ucAuthportFlg,
                            &ptEvent_Param->ucAuthProt) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(ucPDPtypeFlg == D_ATC_FLAG_FALSE
            && pApnValue == NULL
            && pUsername == NULL
            && pPassword == NULL
            && ucAuthportFlg == 0)
        {
            ptEvent_Param->usEvent = D_ATC_EVENT_QICSGP_R;
            return D_ATC_COMMAND_OK;
        }
        if(pApnValue != NULL)
        {
            ptEvent_Param->ucApnLen = strlen(pApnValue);
            AtcAp_MemCpy(&ptEvent_Param->aucApnValue, pApnValue,ptEvent_Param->ucApnLen);
        }
        if(pUsername != NULL)
        {
            ptEvent_Param->ucUsernameLen = strlen(pUsername);
            AtcAp_MemCpy(&ptEvent_Param->aucUsername, pUsername,ptEvent_Param->ucUsernameLen);
        }
        else if(pPassword != NULL)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(pPassword != NULL)
        {
            ptEvent_Param->ucPasswordLen = strlen(pPassword);
            AtcAp_MemCpy(&ptEvent_Param->aucPassword, pPassword,ptEvent_Param->ucPasswordLen);
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QICSGP;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QIACT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_QIACT_PARAMETER*  ptEvent_Param = (ST_ATC_QIACT_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer, &ptEvent_Param->ucCid) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QIACT;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QIACT_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QIACTEX_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_QIACTEX_PARAMETER*  ptEvent_Param = (ST_ATC_QIACTEX_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        ptEvent_Param->ucMode = 0xFF;
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QIACTEX_FORMAT, pCommandBuffer, &ptEvent_Param->ucCid, NULL, &ptEvent_Param->ucMode) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QIACTEX;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QIACTEX_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QIDEACT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_QIDEACT_PARAMETER*  ptEvent_Param = (ST_ATC_QIDEACT_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer, &ptEvent_Param->ucCid) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QIDEACT;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QIDEACT_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QIDEACTEX_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_QIDEACTEX_PARAMETER*  ptEvent_Param = (ST_ATC_QIDEACTEX_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer, &ptEvent_Param->ucCid) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QIDEACTEX;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QGSN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QGSN;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QCCID_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QCCID;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QSPN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QSPN;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_QENG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char*         pucCellType = NULL;
    ST_ATC_QENG_PARAMETER*  ptEvent_Param =  (ST_ATC_QENG_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QENG_FORMAT, pCommandBuffer, &pucCellType) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }

        if(0 == strcmp(pucCellType, "servingcell"))
        {
            ptEvent_Param->ucType = D_ATC_QENG_TYPE_SERV_CELL;
        }
        else if(0 == strcmp(pucCellType, "neighbourcell"))
        {
            ptEvent_Param->ucType = D_ATC_QENG_TYPE_NEIGHB_CELL;
        }
        else
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QENG;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QINISTAT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QINISTAT;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QSIMDET_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_QSIMDET_PARAMETER*  ptEvent_Param = (ST_ATC_QSIMDET_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QSIMDET_FORMAT, pCommandBuffer, &ptEvent_Param->ucEnable, &ptEvent_Param->ucInsertLevl) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QSIMDET;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QSIMDET_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QSIMSTAT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QSIMSTAT_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QSIMSTAT;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QSIMSTAT_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QNWINFO_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QNWINFO;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QCSQ_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QCSQ_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QCSQ;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QCSQ_R;
    }
    else
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QCSQ_E;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QGDCNT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QGDCNT_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QGDCNT;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QGDCNT_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QAUGDCNT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_QAUGDCNT_PARAMETER*  ptEvent_Param = (ST_ATC_QAUGDCNT_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QAUGDCNT_FORMAT, pCommandBuffer, &ptEvent_Param->usValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(0 < ptEvent_Param->usValue && ptEvent_Param->usValue< 30)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QAUGDCNT;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QAUGDCNT_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QCELL_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QCELL_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QCELLEX_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QCELLEX_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QCELLEX;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QPINC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char*         pucFacility = NULL;
    ST_ATC_QPINC_PARAMETER*  ptEvent_Param = (ST_ATC_QPINC_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QPINC_FORMAT, pCommandBuffer, &pucFacility) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }

        if(0 == strcmp(pucFacility, "SC"))
        {
            ptEvent_Param->ucFacility = 1;
        }
        else if(0 == strcmp(pucFacility, "P2"))
        {
            ptEvent_Param->ucFacility = 2;
        }
        else
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QPINC;
    }
    else
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QPINC_R;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QBLACKCELL_ParaseCellId(unsigned char ucDataLen, unsigned char *pData, ST_ATC_QBLACKCELL_PARAMETER* pParam)
{
    unsigned char offset = 0;
    char         *pTemp;
    char         aucEarfcn[8];
    char         aucCellId[5];
    unsigned long ulEarfcn;
    unsigned short usPci;
    
    //string:1300:123&1301:124&..., max num 10
    if(0 == ucDataLen)
    {
        return ATC_FALSE;
    }

    pTemp = strtok((char*)pData, "&");
    while(pTemp != NULL && pParam->ucNum < 10)
    {
        AtcAp_MemSet(aucEarfcn, 0, sizeof(aucEarfcn));
        AtcAp_MemSet(aucCellId, 0, sizeof(aucCellId));
        if(0 == sscanf(pTemp, "%[^:]:%4s", aucEarfcn, aucCellId) || strlen(aucEarfcn) > 6 || strlen(aucCellId) > 4)
        {
            return ATC_FALSE;
        }

        if(0 == strlen(aucEarfcn) || 0 == strlen(aucCellId) || NULL != strstr(aucCellId, ":"))
        {
            return ATC_FALSE;
        }

        ulEarfcn = atoi(aucEarfcn);
        usPci = atoi(aucCellId);
        if(ulEarfcn > 65535 || usPci > 503)
        {
            return ATC_FALSE;
        }

        pParam->atEarfcnInfo[pParam->ucNum].ulEarfcn = ulEarfcn;
        pParam->atEarfcnInfo[pParam->ucNum].ucPciFlg = 1;
        pParam->atEarfcnInfo[pParam->ucNum].usPci = usPci;
        pParam->ucNum++;

        pTemp = strtok(NULL, "&");
        if(pParam->ucNum >= 10 && pTemp != NULL)
        {
            return ATC_FALSE;
        }
    }

    if(0 == pParam->ucNum)
    {
        return ATC_FALSE;
    }
    
    return ATC_TRUE;
}
unsigned char ATC_QBLACKCELL_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char*         pData = NULL;
    unsigned char          ucAct = 0;
    ST_ATC_QBLACKCELL_PARAMETER*  ptEvent_Param = (ST_ATC_QBLACKCELL_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QBLACKCELL_FORMAT, pCommandBuffer, &ptEvent_Param->ucOpType, &ucAct, &pData) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(ATC_FALSE == ATC_QBLACKCELL_ParaseCellId(strlen(pData), pData, ptEvent_Param))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QBLACKCELL;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QBLACKCELL_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QBLACKCELLCFG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QBLACKCELLCFG_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QBLACKCELLCFG;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QBLACKCELLCFG_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QSIMSWITCH_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QSIMSWITCH_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_SIMSWITCH;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_SIMSWITCH_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_HexToBandList(unsigned char *pValue, unsigned char ucLen, unsigned char *ucNum, unsigned char *pBandList)
{
    unsigned char          i = 0,j = 0;
    
    for(i = 0; i < ucLen; i++)
    {
        if(*(pValue + i) == 0)
        {
            continue;
        }
        for(j = 0; j < 8; j++)
        {
            if(*(pValue + i) & (0x01 << j))
            {
                pBandList[*ucNum] = (ucLen - i - 1) * 4 + j + 1;
                *ucNum = *ucNum + 1;
                if(*ucNum > D_ATC_NBAND_MAX_BNAD_NUM)
                {
                    return ATC_AP_FALSE;
                }
            }
        }
    }
    if(*ucNum == 0 && ucLen != 0)
    {
        *ucNum = 1;
        pBandList[0] = 0;
    }
    return ATC_AP_TRUE;
}

unsigned char Atc_HexStrToDigit(unsigned char* src, unsigned char src_len, unsigned char* dst)
{
    int i;

    if (src ==  NULL || dst == NULL) 
    {
        xy_assert(0);
    }

    for (i = 0; i < src_len; i ++) 
    {
        if(*src >= 'a' && *src <= 'f')
        {
            *dst = ((*src - 'a') + 10);
        }
        else if (*src >= '0' && *src <= '9') 
        {
            *dst = (*src - '0');
        } 
        else if (*src >= 'A' && *src <= 'F') 
        {
            *dst = ((*src - 'A') + 10);
        } 
        else 
        {
            return ATC_AP_FALSE;
        }
        src++;
        dst++;
    }

    return ATC_AP_TRUE;
}
#if USR_CUSTOM12
unsigned char ATC_QCFG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char*          pFunc = NULL;
    unsigned char           ucValueflag = 0,ucValue1flag = 0,ucValue2flag = 0,ucValue3flag = 0;
    unsigned char*          pucValue = NULL;
    unsigned char*          pucValue1 = NULL;
    unsigned char*          pucValue2 = NULL;
    unsigned char*          pucValue3 = NULL;
    unsigned char*          pucBandValue = NULL;
    unsigned char          i = 0;
    ST_ATC_QCFG_PARAMETER*  ptEvent_Param = (ST_ATC_QCFG_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QCFG_FORMAT, pCommandBuffer, &pFunc,
                                &ucValueflag,&pucValue,&ucValue1flag,&pucValue1,
                                &ucValue2flag,&pucValue2,&ucValue3flag,&pucValue3) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        for(i = 0; i < D_ATC_QCFG_MAX; i++)
        {
            if(1 == AtcAp_StrCaseCmpWithNum((char*)pFunc, (char*)ATC_Qcfg_Table[i].pucStr,strlen(ATC_Qcfg_Table[i].pucStr)))
            {
                ptEvent_Param->ucFunc = ATC_Qcfg_Table[i].ucStrVal;
                break;
            }
        }
        if(i == D_ATC_QCFG_MAX)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(D_ATC_FLAG_FALSE == ucValueflag
            && D_ATC_FLAG_FALSE == ucValue1flag
            && D_ATC_FLAG_FALSE == ucValue2flag
            && D_ATC_FLAG_FALSE == ucValue3flag)
        {
            ptEvent_Param->usEvent = D_ATC_EVENT_QCFG_R;
            return D_ATC_COMMAND_OK;
        }
        if(D_ATC_QCFG_CREG_EMERGENCY == ptEvent_Param->ucFunc
            || D_ATC_QCFG_NWSCANMODE == ptEvent_Param->ucFunc
            || D_ATC_QCFG_NW_QPTMZ == ptEvent_Param->ucFunc
            || D_ATC_QCFG_PDP_RETRYTIMES == ptEvent_Param->ucFunc)
        {
            AtcAp_SendOkRsp();
            return D_ATC_COMMAND_END;
        }
        if(D_ATC_QCFG_BAND == ptEvent_Param->ucFunc)
        {
            if(D_ATC_FLAG_TRUE == ucValueflag)
            {
                if(strlen(pucValue) > D_ATC_QCFG_MAX_GSMBAND_NUM)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                ptEvent_Param->stBandInfo.ucGsmBandFlg = D_ATC_FLAG_TRUE;
                AtcAp_MemCpy(ptEvent_Param->stBandInfo.aucGsmBand, pucValue, strlen(pucValue));
            }
            if(D_ATC_FLAG_TRUE == ucValue1flag)
            {
                if(AtcAp_StrCaseCmpWithNum(pucValue1, "0x", 2) == 1)
                {
                    pucValue1 = pucValue1 + 2;
                }
                pucBandValue = AtcAp_Malloc(strlen(pucValue1) + 1);
                if(Atc_HexStrToDigit(pucValue1, strlen(pucValue1), pucBandValue) == ATC_AP_FALSE)
                {
                    AtcAp_Free(pucBandValue);
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                if(ATC_HexToBandList(pucBandValue, strlen(pucValue1), &ptEvent_Param->stBandInfo.mu8Num, ptEvent_Param->stBandInfo.mau8Band) == ATC_AP_FALSE)
                {
                    AtcAp_Free(pucBandValue);
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                AtcAp_Free(pucBandValue);
                ptEvent_Param->stBandInfo.ucLteBandFlg = D_ATC_FLAG_TRUE;
            }
            if(D_ATC_FLAG_TRUE == ucValue2flag)
            {
                if(strlen(pucValue2) > D_ATC_QCFG_MAX_NBIOTBAND_NUM)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                ptEvent_Param->stBandInfo.ucNbiotBandFlg = D_ATC_FLAG_TRUE;
                AtcAp_MemCpy(ptEvent_Param->stBandInfo.aucNbiotBand, pucValue2, strlen(pucValue2));
            }
            if(D_ATC_FLAG_TRUE == ucValue3flag
                && (D_ATC_FLAG_TRUE == ucValueflag || D_ATC_FLAG_TRUE == ucValue1flag || D_ATC_FLAG_TRUE == ucValue2flag))
            {
                ptEvent_Param->stBandInfo.ucEffFlg = D_ATC_FLAG_TRUE;
                if(ATC_CharToBinary(pucValue3, strlen(pucValue3), &ptEvent_Param->stBandInfo.ucEffect,0) == ATC_NG)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
            else if(D_ATC_FLAG_TRUE == ucValue3flag)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        else
        {
            if(ucValueflag == D_ATC_FLAG_TRUE)
            {
                if(ATC_CharToBinary(pucValue, strlen(pucValue), &ptEvent_Param->stFuncInfo.ucValue,0) == ATC_NG)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
            else
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            if(ucValue1flag == D_ATC_FLAG_TRUE)
            {
                ptEvent_Param->stFuncInfo.ucEffFlg = D_ATC_FLAG_TRUE;
                if(ATC_CharToBinary(pucValue1, strlen(pucValue1), &ptEvent_Param->stFuncInfo.ucEffect,0) == ATC_NG)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QCFG;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}
#else
unsigned char ATC_QCFG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char*          pFunc = NULL;
    unsigned char           ucValueflag = 0,ucValue1flag = 0,ucValue2flag = 0,ucValue3flag = 0;
    unsigned char*          pucValue = NULL;
    unsigned char*          pucValue1 = NULL;
    unsigned char*          pucValue2 = NULL;
    unsigned char*          pucValue3 = NULL;
    unsigned char*          pucBandValue = NULL;
    unsigned char          i = 0;
    ST_ATC_QCFG_PARAMETER*  ptEvent_Param = (ST_ATC_QCFG_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QCFG_FORMAT, pCommandBuffer, &pFunc,
                                &ucValueflag,&pucValue,&ucValue1flag,&pucValue1,
                                &ucValue2flag,&pucValue2,&ucValue3flag,&pucValue3) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        for(i = 0; i < D_ATC_QCFG_MAX; i++)
        {
            if(0 == strcmp((char*)pFunc, (char*)ATC_Qcfg_Table[i].pucStr))
            {
                ptEvent_Param->ucFunc = ATC_Qcfg_Table[i].ucStrVal;
                break;
            }
        }
        if(i == D_ATC_QCFG_MAX)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }

        if(D_ATC_FLAG_FALSE == ucValueflag
            && D_ATC_FLAG_FALSE == ucValue1flag
            && D_ATC_FLAG_FALSE == ucValue2flag
            && D_ATC_FLAG_FALSE == ucValue3flag)
        {
            ptEvent_Param->usEvent = D_ATC_EVENT_QCFG_R;
            return D_ATC_COMMAND_OK;
        }
        if(D_ATC_QCFG_BAND == ptEvent_Param->ucFunc)
        {
            if(D_ATC_FLAG_TRUE == ucValueflag)
            {
                if(strlen(pucValue) > D_ATC_QCFG_MAX_GSMBAND_NUM)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                ptEvent_Param->stBandInfo.ucGsmBandFlg = D_ATC_FLAG_TRUE;
                AtcAp_MemCpy(ptEvent_Param->stBandInfo.aucGsmBand, pucValue, strlen(pucValue));
            }
            if(D_ATC_FLAG_TRUE == ucValue1flag)
            {
                if(AtcAp_StrCaseCmpWithNum(pucValue1, "0x", 2) == 1)
                {
                    pucValue1 = pucValue1 + 2;
                }
                pucBandValue = AtcAp_Malloc(strlen(pucValue1) + 1);
                if(Atc_HexStrToDigit(pucValue1, strlen(pucValue1), pucBandValue) == ATC_AP_FALSE)
                {
                    AtcAp_Free(pucBandValue);
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                if(ATC_HexToBandList(pucBandValue, strlen(pucValue1), &ptEvent_Param->stBandInfo.mu8Num, ptEvent_Param->stBandInfo.mau8Band) == ATC_AP_FALSE)
                {
                    AtcAp_Free(pucBandValue);
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                AtcAp_Free(pucBandValue);
                ptEvent_Param->stBandInfo.ucLteBandFlg = D_ATC_FLAG_TRUE;
            }
            if(D_ATC_FLAG_TRUE == ucValue2flag)
            {
                if(strlen(pucValue2) > D_ATC_QCFG_MAX_NBIOTBAND_NUM)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                ptEvent_Param->stBandInfo.ucNbiotBandFlg = D_ATC_FLAG_TRUE;
                AtcAp_MemCpy(ptEvent_Param->stBandInfo.aucNbiotBand, pucValue2, strlen(pucValue2));
            }
            if(D_ATC_FLAG_TRUE == ucValue3flag
                && (D_ATC_FLAG_TRUE == ucValueflag || D_ATC_FLAG_TRUE == ucValue1flag || D_ATC_FLAG_TRUE == ucValue2flag))
            {
                ptEvent_Param->stBandInfo.ucEffFlg = D_ATC_FLAG_TRUE;
                if(ATC_CharToBinary(pucValue3, strlen(pucValue3), &ptEvent_Param->stBandInfo.ucEffect,0) == ATC_NG)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
            else if(D_ATC_FLAG_TRUE == ucValue3flag)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        else
        {
            if(ucValueflag == D_ATC_FLAG_TRUE)
            {
                if(ATC_CharToBinary(pucValue, strlen(pucValue), &ptEvent_Param->stFuncInfo.ucValue,0) == ATC_NG)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
            else
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            if(ucValue1flag == D_ATC_FLAG_TRUE)
            {
                ptEvent_Param->stFuncInfo.ucEffFlg = D_ATC_FLAG_TRUE;
                if(ATC_CharToBinary(pucValue1, strlen(pucValue1), &ptEvent_Param->stFuncInfo.ucEffect,0) == ATC_NG)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QCFG;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}
#endif
unsigned char ATC_QSCLKEX_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char*         pData = NULL;
    unsigned char          ucAct = 0;
    ST_ATC_QSCLKEX_PARAMETER*  ptEvent_Param = (ST_ATC_QSCLKEX_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        ptEvent_Param->ucIdle_time = 0xFF;
        ptEvent_Param->usRetry_time = 0xFFFF;
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QSCLKEX_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucMode, 
                            NULL,
                            &ptEvent_Param->ucIdle_time,
                            NULL,
                            &ptEvent_Param->usRetry_time) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QSCLKEX;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QSCLKEX_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

#if USR_CUSTOM2
unsigned char ATC_NUESTATS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char               i = 0;
    unsigned char               *pTypeValue = NULL;
    ST_ATC_NUESTATS_PARAMETER*  ptEvent_Param = (ST_ATC_NUESTATS_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NUESTATS_FORMAT, pCommandBuffer, NULL, &pTypeValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(pTypeValue == NULL)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        for (i = 0; i < ATC_NUESTATS_MAX; i++)
        {
            if (0 == AtcAp_Strncmp(pTypeValue, (unsigned char*)ATC_NUESTATS_Table[i].pucStr))
            {
                ptEvent_Param->mu8Type = ATC_NUESTATS_Table[i].ucStrVal;
                break;
            }
        }
        if(ATC_NUESTATS_MAX == i)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NUESTATS;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NUESTATS;
        ptEvent_Param->mu8Type = ATC_NUESTATS_TYPE_NOPARAMETER;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
    
}
#endif

unsigned char ATC_NEARFCN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NEARFCN_PARAMETER*  ptEvent_Param = (ST_ATC_NEARFCN_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        ptEvent_Param->mu16Pci = 0xFFFF;
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NEARFCN_FORMAT, pCommandBuffer, 
                            &ptEvent_Param->mu8Mode, 
                            &ptEvent_Param->mu32Earfcn,
                            NULL,
                            &ptEvent_Param->mu16Pci) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NEARFCN;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NEARFCN_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_NCSEARFCN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NCSEARFCN;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_NFPLMN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NFPLMN;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_NCONFIG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char               *pFunc = NULL;
    unsigned char               *pValue = NULL;
    unsigned char               index = 0, i = 0;
    ST_ATC_NCONFIG_PARAMETER*  ptEvent_Param = (ST_ATC_NCONFIG_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NCONFIG_FORMAT, pCommandBuffer, &pFunc, &pValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
// function handle
        for(index = 0; index < D_ATC_NCONFIG_MAX; index++)
        {
            if(0 == strcmp((char*)pFunc, (char*)ATC_NConfig_Table[index].pucStr))
            {
                ptEvent_Param->mu8Func = ATC_NConfig_Table[index].ucStrVal;
                break;
            }
        }
        if(D_ATC_NCONFIG_MAX == index)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
//value handle -1:str -2:Num
#if !USR_CUSTOM2
        if(ATC_AP_TRUE == ATC_NCONFIG_SET_IsStrChk(ATC_NConfig_Table[index].ucStrVal))
        {
            for(i = 0; i < strlen(pValue); i++)
            {
                pValue[i] = toupper(pValue[i]);
            }

            if(ATC_NConfig_Table[index].ucStrVal == D_ATC_NCONFIG_PCO_IE_TYPE)
            {
                if(0 == strcmp(pValue, "EPCO"))
                {
                    ptEvent_Param->mu16Val = 1;
                }
                else if(0 == strcmp(pValue, "PCO"))
                {
                    ptEvent_Param->mu16Val = 0;
                }
                 else
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
            else
            {
                if(0 == strcmp(pValue, "TRUE"))
                {
                    ptEvent_Param->mu16Val = 1;
                }
                else if(0 == strcmp(pValue, "FALSE"))
                {
                    ptEvent_Param->mu16Val = 0;
                }
                 else
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
        }
        else
#endif
        {
            if(AtcAp_CmdParamPrase(D_ATC_CMD_NCONFIG_PARAM2_FORMAT, pValue, &ptEvent_Param->mu16Val) != ATC_AP_TRUE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NCONFIG;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NCONFIG_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}
#ifndef _FLASH_OPTIMIZE_
static char ATC_ConvertBinaryStr(unsigned char* pBinaryStr, unsigned char ucStrLen, unsigned char ucDesLen, unsigned char* pbData)
{
    unsigned char i;

    if(0 == ucStrLen)
    {
        return D_ATC_PARAM_ERROR;
    }
    
    if(ucStrLen != ucDesLen)
    {
        return D_ATC_PARAM_ERROR;
    }
    
    for (i = 0; i < ucDesLen; i++)
    {
        if ((pBinaryStr[i] != '0') && (pBinaryStr[i] != '1'))
        {
            return D_ATC_PARAM_ERROR;
        }
        *pbData |= ((pBinaryStr[i] - '0') << (3 - i)); 
    }
    
    return D_ATC_PARAM_OK;
    
}

unsigned char ATC_NPTWEDRXS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char*      pucPtwValue = NULL;
    unsigned char*      pucEDRXValue = NULL;
    ST_ATC_CEDRXS_PARAMETER*  ptEvent_Param = (ST_ATC_CEDRXS_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NPTWEDRXS_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucMode, 
                            &ptEvent_Param->ucActTypeFlg,
                            &ptEvent_Param->ucActType,
                            &ptEvent_Param->ucPtwValueFlag,
                            &pucPtwValue,
                            &ptEvent_Param->ucEDRXValueFlag,
                            &pucEDRXValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(D_ATC_FLAG_TRUE == ptEvent_Param->ucPtwValueFlag)
        {
            if(D_ATC_PARAM_OK != ATC_ConvertBinaryStr(pucPtwValue, strlen(pucPtwValue), 4, &(ptEvent_Param->ucPtwValue)))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        if(D_ATC_FLAG_TRUE == ptEvent_Param->ucEDRXValueFlag)
        {
            if(D_ATC_PARAM_OK != ATC_ConvertBinaryStr(pucEDRXValue, strlen(pucEDRXValue), 4, &(ptEvent_Param->ucEDRXValue)))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NPTWEDRXS;
        ptEvent_Param->ucNptwEDrxsCmdFlg = D_ATC_FLAG_TRUE;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NPTWEDRXS_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}


unsigned char ATC_NPOPB_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NPOPB_PARAMETER*  ptEvent_Param = (ST_ATC_NPOPB_PARAMETER*)pEventBuffer;
    unsigned char*            pucPlmn = NULL;
    unsigned char                    ucParamFlg = D_ATC_FLAG_FALSE;
    unsigned char                    ucParam1Flg = D_ATC_FLAG_FALSE;
    unsigned char                    ucPlmnLen = 0,ucIdx = 0;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NPOPB_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucOperatorIndex,
                            &ptEvent_Param->ucPlmnFlg,&pucPlmn,
                            &ptEvent_Param->ucBandNumFlg,&ptEvent_Param->ucBandNum,
                            &ucParamFlg,&ptEvent_Param->ulStartFreq,
                            &ucParam1Flg,&ptEvent_Param->usOffset) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if (!(((D_ATC_FLAG_TRUE == ptEvent_Param->ucBandNumFlg) && (D_ATC_FLAG_TRUE == ucParamFlg) && (D_ATC_FLAG_TRUE == ucParam1Flg))
            || ((D_ATC_FLAG_FALSE == ptEvent_Param->ucBandNumFlg) && (D_ATC_FLAG_FALSE == ucParamFlg) && (D_ATC_FLAG_FALSE == ucParam1Flg))))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if (pucPlmn != NULL)
        {
            ucPlmnLen = strlen(pucPlmn);
            if(5 != ucPlmnLen && 6 != ucPlmnLen)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            
            for (ucIdx = 0; ucIdx < ucPlmnLen; ucIdx++)
            {
                if ((pucPlmn[ucIdx] >= '0') && (pucPlmn[ucIdx] <= '9'))
                {
                    pucPlmn[ucIdx] = (unsigned char)(pucPlmn[ucIdx] - '0');
                }
                else
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
            ptEvent_Param->ulPlmn = ((pucPlmn[0] << 16) | (pucPlmn[1] << 20) | (pucPlmn[2] << 8) | pucPlmn[3] | (pucPlmn[4] << 4));
            if (5 == ucPlmnLen)
            {
                ptEvent_Param->ulPlmn |= (0x0F << 12);
            }
            else
            {
                ptEvent_Param->ulPlmn |= (pucPlmn[5] << 12);
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NPOPB;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NPOPB_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_NQPODCP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NQPODCP_PARAMETER*  ptEvent_Param = (ST_ATC_NQPODCP_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer,
                            &ptEvent_Param->ucCid) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NQPODCP;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_NSNPD_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NSNPD_PARAMETER* ptEvent_Param = (ST_ATC_NSNPD_PARAMETER*)pEventBuffer;
    unsigned char  *pData = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NSNPD_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucCid,
                            &ptEvent_Param->usNonIpDataLen,
                            NULL,
                            &pData,
                            NULL,
                            &ptEvent_Param->ucRai,
                            NULL,
                            &ptEvent_Param->ucTypeData,
                            NULL,
                            &ptEvent_Param->ucSquence) != ATC_AP_TRUE)

        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(pData == NULL)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        else
        {
            if((strlen(pData)/2) != ptEvent_Param->usNonIpDataLen)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            if(0 == ptEvent_Param->usNonIpDataLen)
            {
                ptEvent_Param->pucNonIpData = NULL;
            }
            else
            {
                ptEvent_Param->pucNonIpData = AtcAp_Malloc(ptEvent_Param->usNonIpDataLen + 1);
                if(Atc_HexStrToHexDigit_LongStr(pData, strlen(pData), ptEvent_Param->pucNonIpData) == ATC_AP_FALSE)
                {
                    AtcAp_Free(ptEvent_Param->pucNonIpData);
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NSNPD;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_NQPNPD_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NQPNPD_PARAMETER*  ptEvent_Param = (ST_ATC_NQPNPD_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer,
                            &ptEvent_Param->ucCid) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NQPNPD;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_NRNPDM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NRNPDM_PARAMETER*  ptEvent_Param = (ST_ATC_NRNPDM_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NRNPDM_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucReporting) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NRNPDM;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NRNPDM_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

#endif

#if !defined(_FLASH_OPTIMIZE_) || (USR_CUSTOM2)
unsigned char ATC_NCIDSTATUS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NCIDSTATUS_PARAMETER*  ptEvent_Param = (ST_ATC_NCIDSTATUS_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer,
                            &ptEvent_Param->ucCid) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NCIDSTATUS;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NCIDSTATUS_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}
#endif

unsigned char ATC_NCPCDPR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NCPCDPR_PARAMETER*  ptEvent_Param = (ST_ATC_NCPCDPR_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NCPCDPR_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucParam,
                            &ptEvent_Param->ucState) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NCPCDPR;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NCPCDPR_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_NGACTR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NGACTR_PARAMETER*  ptEvent_Param = (ST_ATC_NGACTR_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NGACTR_FORMAT, pCommandBuffer,
                            &ptEvent_Param->n) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NGACTR;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NGACTR_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_NL2THP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NL2THP_PARAMETER*  ptEvent_Param = (ST_ATC_NL2THP_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NL2THP_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucValue, 
                            NULL,
                            &ptEvent_Param->ucTimerLen) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NL2THP;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NL2THP_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_NSET_LTE_Command
  FUNCTION  : 
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char AtcAp_NSET_AtHeaderSpace_Process(unsigned long ulParam1)
{
    if(ulParam1 > 1)
    {
        AtcAp_SendCmeeErr(D_ATC_AP_CME_INCORRECT_PARAMETERS);
        return D_ATC_COMMAND_END;
    }

    if(g_softap_fac_nv->ucAtHeaderSpaceFlg != ulParam1)
    {
        g_softap_fac_nv->ucAtHeaderSpaceFlg = (unsigned char)ulParam1;
        SAVE_FAC_PARAM(ucAtHeaderSpaceFlg);
    }
    AtcAp_SendOkRsp();
    return D_ATC_COMMAND_END;
}

unsigned char AtcAp_NSET_SimStateRpt_Process(unsigned long ulParam1)
{
    if(ulParam1 > 1)
    {
        AtcAp_SendCmeeErr(D_ATC_AP_CME_INCORRECT_PARAMETERS);
        return D_ATC_COMMAND_END;
    }

    if(g_softap_fac_nv->usim_state_rpt != ulParam1)
    {
        g_softap_fac_nv->usim_state_rpt = (unsigned char)ulParam1;
        SAVE_FAC_PARAM(usim_state_rpt);
    }
    AtcAp_SendOkRsp();
    return D_ATC_COMMAND_END;
}

unsigned char AtcAp_NSET_R_AtHeaderSpace_Process(ST_ATC_CMD_COM_EVENT* ptEvent)
{
    AtcAp_StrPrintf_AtcRspBuf("\r\n+NSET:\"%s\",%d\r\n", D_ATC_NSET_AT_HEADER_SPACE, g_softap_fac_nv->ucAtHeaderSpaceFlg);
    AtcAp_SendDataInd((unsigned char*)ptEvent);
    AtcAp_SendOkRsp();

    return D_ATC_COMMAND_END;
}

unsigned char AtcAp_NSET_R_SimStateRpt_Process(ST_ATC_CMD_COM_EVENT* ptEvent)
{
    AtcAp_StrPrintf_AtcRspBuf("\r\n+NSET:\"%s\",%d\r\n", D_ATC_NSET_SIM_STATE_RPTFLG, g_softap_fac_nv->usim_state_rpt);
    AtcAp_SendDataInd((unsigned char*)ptEvent);
    AtcAp_SendOkRsp();

    return D_ATC_COMMAND_END;
}

unsigned char ATC_NSET_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char               *pValue = NULL;
    unsigned char               *pValue1 = NULL;
    unsigned char               *pValue2 = NULL;
    unsigned char               index = 0;
    unsigned char               ucValue = 0;
    unsigned char               ucNegativeNumFlg = 0;
    ST_ATC_NSET_PARAMETER*  ptEvent_Param = (ST_ATC_NSET_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if((AtcAp_CmdParamPrase(D_ATC_CMD_NSET_FORMAT, pCommandBuffer, &pValue, &ptEvent_Param->ucParam1Flg, &pValue1, &ptEvent_Param->ucParam2Flg, &pValue2) != ATC_AP_TRUE))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
// function handle
        AtcAp_MemCpy(ptEvent_Param->aucInsValue, pValue, strlen(pValue));
        if(pValue1 == NULL && pValue2 == NULL)
        {
            ptEvent_Param->usEvent = D_ATC_EVENT_NSET_R;
            if (0 == AtcAp_Strncmp(pValue, (unsigned char *)D_ATC_NSET_AT_HEADER_SPACE))
            {
                return AtcAp_NSET_R_AtHeaderSpace_Process((ST_ATC_CMD_COM_EVENT*)ptEvent_Param);
            }
            if (0 == AtcAp_Strncmp(pValue, (unsigned char *)D_ATC_NSET_SIM_STATE_RPTFLG))
            {
                return AtcAp_NSET_R_SimStateRpt_Process((ST_ATC_CMD_COM_EVENT*)ptEvent_Param);
            }
            return D_ATC_COMMAND_OK;
        }
        else if(pValue1 != NULL && pValue2 == NULL
            && 0 == AtcAp_Strncmp(pValue, (unsigned char *)"NV_TEST"))
        {
            ptEvent_Param->usEvent = D_ATC_EVENT_NSET_R;
            if(AtcAp_CmdParamPrase(D_ATC_CMD_NSET_PARAM2_FORMAT, pValue1, &ptEvent_Param->ulParam1) != ATC_AP_TRUE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            return D_ATC_COMMAND_OK;
        }

        if(pValue1 != NULL)
        {
            //value1 handle -1:str -2:Num
            //SETSVN ->str
            if(0 == AtcAp_Strncmp(pValue, (unsigned char *)"SETSVN"))
            {
                ptEvent_Param->ucLen = strlen(pValue1);
                AtcAp_MemCpy(ptEvent_Param->aucData, pValue1, strlen(pValue1));
            }
            //PPM-> [-128,127]
            else if(0 == AtcAp_Strncmp(pValue, (unsigned char *)"PPM"))
            {
                ucNegativeNumFlg = 1;
                if(ATC_CharToBinary(pValue1,strlen(pValue1),&ucValue,ucNegativeNumFlg)== ATC_NG)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                ptEvent_Param->ulParam1 = ucValue;
            }
            //[0-0xFFFFFFFF]
            else
            {
                if(AtcAp_CmdParamPrase(D_ATC_CMD_NSET_PARAM2_FORMAT, pValue1, &ptEvent_Param->ulParam1) != ATC_AP_TRUE)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
            if (0 == AtcAp_Strncmp(pValue, (unsigned char *)D_ATC_NSET_AT_HEADER_SPACE))
            {
                return AtcAp_NSET_AtHeaderSpace_Process(ptEvent_Param->ulParam1);
            }
            if (0 == AtcAp_Strncmp(pValue, (unsigned char *)D_ATC_NSET_SIM_STATE_RPTFLG))
            {
                return AtcAp_NSET_SimStateRpt_Process(ptEvent_Param->ulParam1);
            }
        }
        if(pValue2 != NULL)
        {
            //value2 handle -1:hex -2:Num
            //DIRTY_DATA_CHECK->HEX
            if(0 == AtcAp_Strncmp(pValue, (unsigned char *)"DIRTY_DATA_CHECK"))
            {
                if(((pValue2[0] != '0' || (pValue2[1] != 'x' || pValue2[1] != 'X'))
                    && ATC_HexToBinary(pValue2 + 2, 2,&ptEvent_Param->ulParam2) != ATC_OK)
                    && (ATC_HexToBinary(pValue2, 2,&ptEvent_Param->ulParam2) != ATC_OK))
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
            //->Num
            else
            {
                if(AtcAp_CmdParamPrase(D_ATC_CMD_NSET_PARAM3_FORMAT, pValue2, &ptEvent_Param->ulParam2) != ATC_AP_TRUE)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NSET;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_NPOWERCLASS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NPOWERCLASS_PARAMETER*  ptEvent_Param = (ST_ATC_NPOWERCLASS_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NPOWERCLASS_FORMAT, pCommandBuffer,
                            &ptEvent_Param->usBand, 
                            &ptEvent_Param->ucPowerClass) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NPOWERCLASS;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NPOWERCLASS_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}



unsigned char ATC_NTSETID_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char*      pucValue = NULL;
    ST_ATC_NTSETID_PARAMETER*  ptEvent_Param = (ST_ATC_NTSETID_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NTSETID_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucSnt, 
                            &pucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->ucDataLen = strlen(pucValue);
        AtcAp_MemCpy(ptEvent_Param->aucData, pucValue, ptEvent_Param->ucDataLen);
        ptEvent_Param->usEvent = D_ATC_EVENT_NTSETID;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}







unsigned char ATC_NLOCKF_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NLOCKF_PARAMETER*  ptEvent_Param = (ST_ATC_NLOCKF_PARAMETER*)pEventBuffer;
    unsigned char             aucEarfcnFlg[NVM_MAX_CANDIDATE_FREQ_NUM] = { 0 };
    unsigned char             i = 0;
    unsigned char             ucOffset = 0;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NLOCKF_FORMAT, pCommandBuffer, &ptEvent_Param->ucMode) == ATC_AP_FALSE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        for(i = 0; i <= NVM_MAX_CANDIDATE_FREQ_NUM; i++)
        {
            if(AtcAp_FindNextComma(pCommandBuffer + ucOffset) == ATC_FALSE)
            {
                break;
            }
            else if(NVM_MAX_CANDIDATE_FREQ_NUM == i)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ucOffset += AtcAp_FindNextComma(pCommandBuffer + ucOffset);
            if(AtcAp_CmdParamPrase(D_ATC_CMD_EARFCN_RANGE, pCommandBuffer + ucOffset, &ptEvent_Param->aulEarfcnList[i]) == ATC_AP_FALSE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ptEvent_Param->ucEarfcnNum++;
        }
        if((ptEvent_Param->ucMode == 0 && ptEvent_Param->ucEarfcnNum != 0)
            || (ptEvent_Param->ucMode == 1 && (ptEvent_Param->ucEarfcnNum < 1 || ptEvent_Param->ucEarfcnNum > 2))
            || (ptEvent_Param->ucMode == 2 && ptEvent_Param->ucEarfcnNum == 0))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(ptEvent_Param->ucMode == 1)
        {
            ptEvent_Param->usPci = 0xFFFF;
            if(ptEvent_Param->ucEarfcnNum == 2)
            {
                ptEvent_Param->ucEarfcnNum = 1;
                if(ptEvent_Param->aulEarfcnList[1] > 503)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                ptEvent_Param->usPci = ptEvent_Param->aulEarfcnList[1];
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NLOCKF;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NLOCKF_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_NITZ_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NITZ_PARAMETER*  ptEvent_Param = (ST_ATC_NITZ_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NITZ_FORMAT, pCommandBuffer, 
                            &ptEvent_Param->ucMode,
                            NULL,&ptEvent_Param->ucSaveMode) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NITZ;
        if(1 == ptEvent_Param->ucSaveMode && ptEvent_Param->ucMode != g_app_basic_cfg.nitz)
        {
            g_app_basic_cfg.nitz = ptEvent_Param->ucMode;
            SAVE_APP_BASIC_CFG_PARAM(nitz);
        }
        AtcAp_SendOkRsp();
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NITZ_R;
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+NITZ:%d\r\n", g_app_basic_cfg.nitz);
        AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        AtcAp_SendOkRsp();
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;

}

unsigned char ATC_NPREEARFCN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NPREEARFCN_PARAMETER*  ptEvent_Param = (ST_ATC_NPREEARFCN_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        ptEvent_Param->ulEarfcn = 0xFFFFFFFF;
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NPREEARFCN_FORMAT, pCommandBuffer, 
                            &ptEvent_Param->ucOperatorIndex, 
                            NULL,
                            &ptEvent_Param->ulEarfcn) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(ptEvent_Param->ulEarfcn == 0xFFFFFFFF)
        {
            ptEvent_Param->usEvent = D_ATC_EVENT_NPREEARFCN_R;
            return D_ATC_COMMAND_OK;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NPREEARFCN;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_NBAND_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_NBAND_PARAMETER*  ptEvent_Param = (ST_ATC_NBAND_PARAMETER*)pEventBuffer;
    unsigned char             i = 0;
    unsigned char             ucOffset = 0;
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        for(i = 0; i < D_ATC_NBAND_MAX_BNAD_NUM; i++)
        {
            if(AtcAp_CmdParamPrase(D_ATC_CMD_NBAND_FORMAT, pCommandBuffer + ucOffset, &ptEvent_Param->mau8Band[i]) == ATC_AP_FALSE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ptEvent_Param->mu8Num++;

            if(AtcAp_FindNextComma(pCommandBuffer + ucOffset) == ATC_FALSE)
            {
                break;
            }
            else if(D_ATC_NBAND_MAX_BNAD_NUM - 1 == i)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ucOffset += AtcAp_FindNextComma(pCommandBuffer + ucOffset);
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NBAND;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NBAND_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_NCCID_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE || g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NCCID;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CEID_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CEID;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CEID;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_MNBIOTEVENT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_MNBIOTEVENT_PARAMETER*  ptEvent_Param = (ST_ATC_MNBIOTEVENT_PARAMETER*)pEventBuffer;
    unsigned char                 ucfunc = 0;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_MNBIOTEVENT_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucEnable, 
                            &ucfunc) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_MNBIOTEVENT;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_MNBIOTEVENT_R;
        AtcAp_SendOkRsp();
        return D_ATC_COMMAND_END;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}


unsigned char ATC_MLOCKFREQ_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_MLOCKFREQ_PARAMETER*  ptEvent_Param = (ST_ATC_MLOCKFREQ_PARAMETER*)pEventBuffer;
    unsigned char             aucEarfcnFlg[16] = { 0 };
    unsigned char             i = 0;
    unsigned char             ucOffset = 0;
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_MLOCKFREQ_FORMAT, pCommandBuffer, &ptEvent_Param->LockFreqList.ucMode) == ATC_AP_FALSE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        for(i = 0; i <= 16; i++)
        {
            if(AtcAp_FindNextComma(pCommandBuffer + ucOffset) == ATC_FALSE)
            {
                break;
            }
            else if(i == 16)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ucOffset += AtcAp_FindNextComma(pCommandBuffer + ucOffset);
            if(AtcAp_CmdParamPrase(D_ATC_CMD_EARFCN_RANGE, pCommandBuffer + ucOffset, 
                &ptEvent_Param->LockFreqList.aEarfcnInfo[i].ulEarfcn) == ATC_AP_FALSE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ptEvent_Param->LockFreqList.ucNum++;
            
            if(AtcAp_FindNextComma(pCommandBuffer + ucOffset) == ATC_FALSE)
            {
                break;
            }
            ucOffset += AtcAp_FindNextComma(pCommandBuffer + ucOffset);
            if(AtcAp_CmdParamPrase(D_ATC_CMD_MLOCKFREQ_PARAM1_FORMAT, pCommandBuffer + ucOffset, &ptEvent_Param->LockFreqList.aEarfcnInfo[i].ucPciFlg,
                &ptEvent_Param->LockFreqList.aEarfcnInfo[i].usPci) == ATC_AP_FALSE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }

        if((ptEvent_Param->LockFreqList.ucMode == 0 && ptEvent_Param->LockFreqList.ucNum != 0)
            || ((ptEvent_Param->LockFreqList.ucMode == 1 || ptEvent_Param->LockFreqList.ucMode == 2) && ptEvent_Param->LockFreqList.ucNum == 0))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }

        ptEvent_Param->usEvent = D_ATC_EVENT_MLOCKFREQ;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_MLOCKFREQ_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_MCSEARFCN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;
    unsigned char                 ucValue = 0;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_MCSEARFCN_FORMAT, pCommandBuffer,
                            &ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_MCSEARFCN;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_MCSEARFCN;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_MUESTATS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_MUESTATS_PARAMETER*  ptEvent_Param = (ST_ATC_MUESTATS_PARAMETER*)pEventBuffer;
    unsigned char               *pucValue = NULL;
    unsigned char               i = 0;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_MUESTATS_FORMAT, pCommandBuffer, &pucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        for(i = 0; i < strlen(pucValue); i++)
        {
            pucValue[i] = tolower(pucValue[i]);
        }
        for (i = 0; i < 6; i++)
        {
            if (0 == AtcAp_Strncmp(pucValue, (unsigned char*)ATC_MUESTATS_Table[i].pucStr))
            {
                ptEvent_Param->mu8Type = ATC_MUESTATS_Table[i].ucStrVal;
                break;
            }
        }
        if(i == 6)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_MUESTATS;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_MUESTATS;
        ptEvent_Param->mu8Type = ATC_NUESTATS_TYPE_NOPARAMETER;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_MPSRAT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;
    unsigned char               *pucValue = NULL;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {

        if((AtcAp_CmdParamPrase(D_ATC_CMD_MPSRAT_FORMAT, pCommandBuffer, &pucValue) != ATC_AP_TRUE)
            || 0 != strcmp(pucValue, "LTE"))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_MPSRAT;
        AtcAp_SendOkRsp();
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_MPSRAT_R;
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+MPSRAT:\"LTE\"\r\n");
        AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        AtcAp_SendOkRsp();
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;

}

unsigned char ATC_MEMMTIMER_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_MEMMTIMER_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_MEMMTIMER;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_MEMMTIMER_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_MUECONFIG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char               *pFunc = NULL;
    unsigned char               *pValue = NULL;
    unsigned char               *pValue1 = NULL;
    unsigned char               index = 0, i = 0;
    ST_ATC_MUECONFIG_PARAMETER*  ptEvent_Param = (ST_ATC_MUECONFIG_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_MUECONFIG_FORMAT, pCommandBuffer, &pFunc, NULL, &pValue, NULL, &pValue1) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
// function handle
        for(index = 0; index < D_ATC_UECONFIG_MAX; index++)
        {
            if(0 == strcmp((char*)pFunc, (char*)ATC_UeConfig_Table[index].pucStr))
            {
                ptEvent_Param->ucFunc = ATC_UeConfig_Table[index].ucStrVal;
                break;
            }
        }
        if(D_ATC_UECONFIG_MAX == index)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }

        if(NULL == pValue && NULL == pValue1)
        {
            ptEvent_Param->usEvent = D_ATC_EVENT_MUECONFIG_R;
            return D_ATC_COMMAND_OK;
        }
        else if(NULL == pValue)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(D_ATC_UECONFIG_DEFAPN == ptEvent_Param->ucFunc)
        {
            for (index = 0; index < 9; index++)
            {
                if (0 == AtcAp_Strncmp(pValue, (unsigned char*)ATC_PdpType_Table[index].pucStr))
                {
                    ptEvent_Param->ucValue1 = ATC_PdpType_Table[index].ucStrVal;
                    break;
                }
            }
            
            if(index == 9)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
    
            //Apn
            if(pValue1 != NULL)
            {
                ptEvent_Param->ucValue2Len = strlen(pValue1);
                AtcAp_MemCpy(ptEvent_Param->aucValue2, pValue1, ptEvent_Param->ucValue2Len);
            }
        }
        else
        {
            //param2
            if(ATC_CharToBinary(pValue,strlen(pValue),&ptEvent_Param->ucValue1,0)== ATC_NG
                || pValue1 != NULL)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }

#if VER_CM
        if(ptEvent_Param->ucFunc == D_ATC_UECONFIG_AUTOCONN
            && ptEvent_Param->ucValue1 == 0)
        {
            g_softap_fac_nv->gw_act_mode = 0;
            SAVE_FAC_PARAM(gw_act_mode);
        }
#endif
        ptEvent_Param->usEvent = D_ATC_EVENT_MUECONFIG;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_MWIFISCANCFG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char               *pFunc = NULL;
    unsigned char               ucValueFlg = 0;
    ST_ATC_MWIFISCANCFG_PARAMETER*  ptEvent_Param = (ST_ATC_MWIFISCANCFG_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_MWIFISCANCFG_FORMAT, pCommandBuffer, &pFunc, &ucValueFlg, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
// function handle
        if(0 == strcmp((char*)pFunc, "hidden"))
        {
            ptEvent_Param->ucOpt = D_ATC_MWIFISCANCFG_OPT_HIDDEN;
        }
        else if(0 == strcmp((char*)pFunc, "priority"))
        {
            ptEvent_Param->ucOpt = D_ATC_MWIFISCANCFG_OPT_PRIORITY;
        }
        else if(0 == strcmp((char*)pFunc, "max"))
        {
            ptEvent_Param->ucOpt = D_ATC_MWIFISCANCFG_OPT_MAX;
        }
        else
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }

        if(ucValueFlg == D_ATC_FLAG_FALSE)
        {
            ptEvent_Param->usEvent = D_ATC_EVENT_MWIFISCANCFG_R;
            return D_ATC_COMMAND_OK;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_MWIFISCANCFG;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_MWIFISCANSTART_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char               *pucSsid = NULL;
    ST_ATC_MWIFISCANSTART_PARAMETER*  ptEvent_Param = (ST_ATC_MWIFISCANSTART_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        ptEvent_Param->ucRoundFlg = D_ATC_FLAG_TRUE;
        if(AtcAp_CmdParamPrase(D_ATC_CMD_MWIFISCANSTART_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucRound, 
                            &ptEvent_Param->ucTimeoutFlg,&ptEvent_Param->ucTimeout,
                            NULL, &pucSsid,
                            &ptEvent_Param->ucChannelFlg,&ptEvent_Param->ucChannel) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(pucSsid != NULL)
        {
            ptEvent_Param->ucSsidLen = strlen(pucSsid);
            AtcAp_MemCpy(ptEvent_Param->aucSsid, pucSsid,ptEvent_Param->ucSsidLen);
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_MWIFISCANSTART;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_MWIFISCANSTART;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_MWIFISCANSTOP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_MWIFISCANSTOP;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_MWIFISCANQUERY_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_MWIFISCANQUERY_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }

        ptEvent_Param->usEvent = D_ATC_EVENT_MWIFISCANQUERY;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_MWIFISCANQUERY;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;


}

unsigned char ATC_QWIFISCAN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_QWIFISCAN_PARAMETER*  ptEvent_Param = (ST_ATC_QWIFISCAN_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QWIFISCAN_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucTimeoutFlg, &ptEvent_Param->uiTimeout,
                            &ptEvent_Param->ucRoundFlg,&ptEvent_Param->ucRound,
                            &ptEvent_Param->ucMaxIDNumFlg, &ptEvent_Param->ucMasIDNum,
                            &ptEvent_Param->ucScantimeoutFlg,&ptEvent_Param->ucScantimeout,
                            &ptEvent_Param->ucPriorityFlg,&ptEvent_Param->ucPriority) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QWIFISCAN;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QWIFISCAN_R;
    }
    else
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QWIFISCAN;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_PSTEST_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_PSTEST_PARAMETER* ptEvent_Param = (ST_ATC_PSTEST_PARAMETER*)pEventBuffer;
    unsigned char  *pData = NULL;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_PSTEST_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucType,
                            NULL,
                            &ptEvent_Param->usDataLen,
                            NULL,
                            &pData) != ATC_AP_TRUE)

        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(pData == NULL)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if((strlen(pData)/2) != ptEvent_Param->usDataLen)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->pucData = AtcAp_Malloc(ptEvent_Param->usDataLen + 1);
        if(Atc_HexStrToHexDigit_LongStr(pData, strlen(pData), ptEvent_Param->pucData) == ATC_AP_FALSE)
        {
            AtcAp_Free(ptEvent_Param->pucData);
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_PSTEST;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

#if PS_TEST_MODE
unsigned char ATC_PSTESTMODE_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_PSTESTMODE_PARAMETER* ptEvent_Param = (ST_ATC_PSTESTMODE_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        ptEvent_Param->ucPsTestModeFlg = 0xFF;
        ptEvent_Param->usPSTestSocketPort = 0;
        ptEvent_Param->usPSTestSocketPort = 0xFF;
        if(AtcAp_CmdParamPrase(D_ATC_CMD_PSTESTMODE_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucPsTestModeFlg,
                            NULL,
                            &ptEvent_Param->usPSTestSocketPort,
                            NULL,
                            &ptEvent_Param->ucClosePhyLogFlg) != ATC_AP_TRUE)

        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_PSTESTMODE;
        if((g_softap_fac_nv->ucPSTestDemoFlg != ptEvent_Param->ucPsTestModeFlg) && ( 0xFF != ptEvent_Param->ucPsTestModeFlg))
        {
            g_softap_fac_nv->ucPSTestDemoFlg = ptEvent_Param->ucPsTestModeFlg;
            SAVE_FAC_PARAM(ucPSTestDemoFlg);
        }
        if((g_softap_fac_nv->usPSTestSocketPort != ptEvent_Param->usPSTestSocketPort) && ( 0 != ptEvent_Param->usPSTestSocketPort))
        {
            g_softap_fac_nv->usPSTestSocketPort = ptEvent_Param->usPSTestSocketPort;
            SAVE_FAC_PARAM(usPSTestSocketPort);
        }
        if((g_softap_fac_nv->ucPSTestStartFlg != ptEvent_Param->ucClosePhyLogFlg) && ( 0xFF != ptEvent_Param->ucClosePhyLogFlg))
        {
            g_softap_fac_nv->ucPSTestStartFlg = ptEvent_Param->ucClosePhyLogFlg;
            SAVE_FAC_PARAM(ucPSTestStartFlg);
        }
        AtcAp_SendOkRsp();
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_PSTESTMODE_R;
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+PSTESTMODE:%d,%d,%d\r\n", g_softap_fac_nv->ucPSTestDemoFlg, g_softap_fac_nv->usPSTestSocketPort, g_softap_fac_nv->ucPSTestStartFlg);
        AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        AtcAp_SendOkRsp();
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;

}

unsigned char ATC_NWSDVOLT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned short              usVolt = 0;
    ST_ATC_CMD_SHORT_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_SHORT_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_NWSDVOLT_FORMAT, pCommandBuffer, &ptEvent_Param->usValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_NWSDVOLT;
        g_WriteSDVolt =((ST_ATC_CMD_SHORT_PARAMETER*)pEventBuffer)->usValue;
        AtcAp_SendOkRsp();
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NWSDVOLT_R;
        usVolt = api_GetWriteSDVolt();
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+NWSDVOLT:%d\r\n", usVolt);
        AtcAp_SendDataInd(pEventBuffer);
        AtcAp_SendOkRsp();
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;
}
#endif
unsigned char ATC_PCTTESTINFO_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char               *pValue = NULL;
    ST_ATC_PCTTESTINFO_PARAMETER*  ptEvent_Param = (ST_ATC_PCTTESTINFO_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_PCTTESTINFO_FORMAT, pCommandBuffer, &pValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        AtcAp_MemCpy(ptEvent_Param->aucPCTTestSequence, pValue, strlen(pValue));
        ptEvent_Param->usEvent = D_ATC_EVENT_PCTTESTINFO;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_PCTTESTINFO_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_NWDRX_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_NWDRX;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

#ifdef SIMULATOR_UICC
unsigned char ATC_SIMUUICC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char               *pFunc = NULL;
    unsigned char               *pValue = NULL;
    unsigned char               *pValue1 = NULL;
    unsigned char               *pValue2 = NULL;
    unsigned char               index = 0, i = 0;
    ST_ATC_SIMUUICC_PARAMETER*  ptEvent_Param = (ST_ATC_SIMUUICC_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if((AtcAp_CmdParamPrase(D_ATC_CMD_SIMUUICC_FORMAT, pCommandBuffer, &pFunc, NULL, &pValue, NULL,&pValue1,NULL,&pValue2) != ATC_AP_TRUE)
            || ((0 != AtcAp_Strncmp(pFunc, (unsigned char *)"IMSI") )
            && (0 != AtcAp_Strncmp(pFunc, (unsigned char *)"CARDNUM"))
            && (0 != AtcAp_Strncmp(pFunc, (unsigned char *)"AUTH"))
            && (0 != AtcAp_Strncmp(pFunc, (unsigned char *)"FILECONTENT"))))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
// function handle
        ptEvent_Param->ucInsLen = strlen(pFunc);
        AtcAp_MemCpy(ptEvent_Param->aucInsValue, pFunc, ptEvent_Param->ucInsLen);
        if( pValue == NULL && pValue1 == NULL && pValue2 == NULL)
        {
            ptEvent_Param->usEvent = D_ATC_EVENT_SIMUUICC_R;
            return D_ATC_COMMAND_OK;
        }
        else if(0 == AtcAp_Strncmp(pFunc, (unsigned char *)"IMSI") && pValue != NULL && pValue1 == NULL && strlen(pValue) <= 16)
        {
            ptEvent_Param->u.stImsi.ucImsiLen = strlen(pValue);
            AtcAp_MemCpy(ptEvent_Param->u.stImsi.aucImsi, pValue, ptEvent_Param->u.stImsi.ucImsiLen);
        }
        else if(0 == AtcAp_Strncmp(pFunc, (unsigned char *)"CARDNUM") &&  pValue != NULL && pValue1 != NULL)
        {
            if((ATC_CharToBinary(pValue,strlen(pValue),&ptEvent_Param->u.stCardNum.ucCardNum,0)== ATC_NG)
                || (ATC_CharToBinary(pValue1,strlen(pValue1),&ptEvent_Param->u.stCardNum.ucSubCardNum,0)== ATC_NG))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        else if(0 == AtcAp_Strncmp(pFunc, (unsigned char *)"AUTH") && pValue != NULL && pValue1 != NULL)
        {
            AtcAp_MemCpy(ptEvent_Param->u.stAuth.aucKey, pValue, strlen(pValue));
            AtcAp_MemCpy(ptEvent_Param->u.stAuth.aucOp, pValue1, strlen(pValue1));
        }
        else if(0 == AtcAp_Strncmp(pFunc, (unsigned char *)"FILECONTENT") && pValue != NULL && pValue1 != NULL && pValue2 != NULL)
        {
            if(ATC_CharToBinary(pValue,strlen(pValue),&ptEvent_Param->u.stFileContent.ucAppType,0)== ATC_NG
                || (ptEvent_Param->u.stFileContent.ucAppType > 2))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            AtcAp_MemCpy(ptEvent_Param->u.stFileContent.aucFileId, pValue1, strlen(pValue1));
            AtcAp_MemCpy(ptEvent_Param->u.stFileContent.aucFileContent, pValue2, strlen(pValue2));
        }
        else
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_SIMUUICC;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;


}
#endif

unsigned char ATC_F_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    ptEvent_Param->usEvent = D_ATC_EVENT_F;
    if(ATC_AP_FALSE != g_softap_fac_nv->cmee_mode_bak_Flg)
    {
#if (VER_QUEC | VER_600U)
        if(g_softap_fac_nv->cmee_mode != g_softap_fac_nv->cmee_mode_bak)
        {
            g_softap_fac_nv->cmee_mode = g_softap_fac_nv->cmee_mode_bak;
             SOFTAP_FAC_CACHE_CLEAN(cmee_mode);
            SAVE_FAC_PARAM(cmee_mode);
        }
#endif
    }
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_W_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    ptEvent_Param->usEvent = D_ATC_EVENT_W;
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_Z_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    ptEvent_Param->usEvent = D_ATC_EVENT_Z;
    return D_ATC_COMMAND_OK;

}

unsigned char ATC_ATI_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    ptEvent_Param->usEvent = D_ATC_EVENT_ATI;
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s", g_softap_fac_nv->manufacturer);
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s", g_softap_fac_nv->modul_ver);
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\nRevision:%s\r\n", g_softap_fac_nv->versionExt);
    AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
    AtcAp_SendOkRsp();

    return D_ATC_COMMAND_END;

}

unsigned char ATC_SIMST_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_SIMST_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_CCED_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CCED_PARAMETER*  ptEvent_Param = (ST_ATC_CCED_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CCED_FORMAT, pCommandBuffer, &ptEvent_Param->ucMode, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CCED;
        return D_ATC_COMMAND_OK;
    }
    return D_ATC_COMMAND_SYNTAX_ERROR;
}

unsigned char ATC_ICCID_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_ICCID_R;
        return D_ATC_COMMAND_OK;
    }
    return D_ATC_COMMAND_SYNTAX_ERROR;

}

unsigned char ATC_BAND_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_BAND_R;
        return D_ATC_COMMAND_OK;
    }
    return D_ATC_COMMAND_SYNTAX_ERROR;

}

unsigned char ATC_BANDIND_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_BANDIND_R;
        return D_ATC_COMMAND_OK;
    }
    return D_ATC_COMMAND_SYNTAX_ERROR;

}

unsigned char ATC_QCELLINFO_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;
    unsigned char ucValue = 0;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
#if USR_CUSTOM10
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QCELLINFO_FORMAT, pCommandBuffer, &ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        AtcAp_SendOkRsp();
        return D_ATC_COMMAND_END;
#else
        return D_ATC_COMMAND_PARAMETER_ERROR;
#endif
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QCELLINFO_R;
        return D_ATC_COMMAND_OK;
    }
    return D_ATC_COMMAND_SYNTAX_ERROR;

}

unsigned char ATC_QINDCFG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_QINDCFG_PARAMETER*  ptEvent_Param = (ST_ATC_QINDCFG_PARAMETER*)pEventBuffer;
    unsigned char       *pStr = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QINDCFG_FORMAT, pCommandBuffer, &pStr, 
                &ptEvent_Param->ucValueFlg, &ptEvent_Param->ucValue, 
                &ptEvent_Param->ucSaveFlg, &ptEvent_Param->ucSaveValue) != ATC_AP_TRUE
            || (0 != strcmp("all", pStr)))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(ATC_AP_FALSE == ptEvent_Param->ucSaveFlg
            && ATC_AP_FALSE == ptEvent_Param->ucValueFlg)
        {
            ptEvent_Param->usEvent = D_ATC_EVENT_QINDCFG_R;
            return D_ATC_COMMAND_OK;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QINDCFG;
        return D_ATC_COMMAND_OK;
    }
    return D_ATC_COMMAND_SYNTAX_ERROR;

}

unsigned char ATC_CIDACT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CIDACT_PARAMETER*  ptEvent_Param = (ST_ATC_CIDACT_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CIDACT_FORMAT, pCommandBuffer, &ptEvent_Param->ucCid,&ptEvent_Param->ucMipcallFlg) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CIDACT;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

// for QIAO AN
unsigned char ATC_QDSIM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QDSIM_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_QDSIM;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QDSIM_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

//FOR BG95
unsigned char ATC_CUSD_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;
    unsigned char*  pdata = NULL;
    unsigned char   ucDcs = 0;
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CUSD_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue, NULL, &pdata, NULL, &ucDcs) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CUSD;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CUSD_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QEHPLMN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_QEHPLMN_PARAMETER* pParam   = (ST_ATC_QEHPLMN_PARAMETER *)pEventBuffer;
    unsigned char            *pucValue = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_QEHPLMN_FORMAT, pCommandBuffer, &pParam->ucOption, &pParam->ucIndex, NULL, &pucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        pParam->usEvent = D_ATC_EVENT_QEHPLMN;

        if(D_QEHPLMN_OPTION_DEL == pParam->ucOption)
        {
            if(pucValue != NULL)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        else
        {
            if(pucValue == NULL || 0 == strlen((const char*)pucValue))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }

            if(ATC_FALSE == AtcAp_ParseQEHPLMNData(pucValue, pParam) || (0 != pParam->ucIndex && pParam->ucNum > 10))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        pParam->usEvent = D_ATC_EVENT_QEHPLMN_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }

    return D_ATC_COMMAND_OK;
}

#if USR_CUSTOM12
unsigned char ATC_CCID_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CCID;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

unsigned char ATC_QMUXCFG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    AtcAp_SendOkRsp();

    return D_ATC_COMMAND_END;

}

unsigned char ATC_QIFGCNT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    AtcAp_SendOkRsp();

    return D_ATC_COMMAND_END;


}

unsigned char ATC_QPDPTIMER_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    AtcAp_SendOkRsp();

    return D_ATC_COMMAND_END;

}
unsigned char ATC_QPPPTIMER_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    AtcAp_SendOkRsp();

    return D_ATC_COMMAND_END;

}
unsigned char ATC_CGATTEX_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    AtcAp_SendOkRsp();

    return D_ATC_COMMAND_END;

}
unsigned char ATC_QIREGAPP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    AtcAp_SendOkRsp();

    return D_ATC_COMMAND_END;

}

unsigned char ATC_A_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    AtcAp_SendOkRsp();

    return D_ATC_COMMAND_END;

}

#endif

unsigned char ATC_ECSIMCFG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;
    unsigned char*  pdata = NULL;
    unsigned char   ucValueFlg = 0;
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_ECSIMCFG_FORMAT, pCommandBuffer, &pdata, &ucValueFlg, &ptEvent_Param->ucValue) != ATC_AP_TRUE
            || (0 == AtcAp_StrCaseCmp((char*)pdata, "SimSlot")))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(ucValueFlg == ATC_AP_FALSE)
        {
            ptEvent_Param->usEvent = D_ATC_EVENT_ECSIMCFG_R;
        }
        else
        {
            ptEvent_Param->usEvent = D_ATC_EVENT_ECSIMCFG;
        }
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}
#ifdef SIB16_FEATURE
unsigned char ATC_QUTCTIME_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_QUTCTIME;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}
#endif

