/**
 * @file at_passthrough.c
 * @brief 芯翼透传模块，用于通过AT口进行透传数据传输，目前支持PPP，SSL，socket tcp及短信模式等透传相关业务
 */
#include <limits.h>
#include "posix_io.h"
#include "xy_at_api.h"
#include "xy_system.h"
#include "main_proxy.h"
#include "at_passthrough.h"
#include "sms_passthrough.h"
#include "app_utils.h"

/*******************************************************************************
 *                             Type definitions                                *
 ******************************************************************************/
#define AT_PASSTH_LOCK(timeout)               osMutexAcquire(gPassthMutex, timeout)
#define AT_PASSTH_UNLOCK()                    osMutexRelease(gPassthMutex)


/*******************************************************************************
 *                           Local variable definitions                        *
 ******************************************************************************/
// 透传全局保护
static osMutexId_t gPassthMutex = NULL;
// 短信透传保护，用于退出和执行透传接口的互斥
osMutexId_t g_sms_passth_mutex = NULL;

/*******************************************************************************
 *                        Global function implementations                      *
 ******************************************************************************/
atPassthInfo_t* atPassthInfoGet(int atHandle)
{
    xy_assert(atHandle > AT_FD_INVAILD && atHandle < AT_FD_MAX);
    return &g_at_tty_ctx[atHandle].passthInfo;
}

/**
 * @brief   透传AT通道信息清除
 * @warning 该接口暂不对业务开放使用，由芯翼内部实现
 */
void atPassthInfoClear(atPassthInfo_t *passthInfo)
{
    if (passthInfo->matchPlus != NULL)
    {
#if PASSTH_TIMER_DURATION
        osTimerDelete(passthInfo->matchPlus->timerId);
#endif /* PASSTH_TIMER_DURATION */
        xy_free(passthInfo->matchPlus);
        passthInfo->matchPlus = NULL;
    }

    passthInfo->eventCb    = NULL;
    passthInfo->events     = PASSTH_EVENT_UNKNOWN;
    passthInfo->channState = AT_STATE_COMMAND;
    passthInfo->PrevPassthState = AT_STATE_STATE_MAX;
}

void atPassthMatchPlusCharReset(atPassthMatchChar_t *matchPlus)
{
    xy_assert(matchPlus != NULL);

    matchPlus->matchNum  = 0;
#if PASSTH_TIMER_DURATION
    matchPlus->recvTime  = 0;
    if (osTimerIsRunning(matchPlus->timerId))
    {
        osTimerStop(matchPlus->timerId);
    }
#endif /* PASSTH_TIMER_DURATION */
}

void atPassthDTRIrqHandler(void)
{
    if (g_at_config.at_d > 0)
    {
        send_msg_2_proxy(PROXY_MSG_PASSTH_DTR_IRQ, NULL, 0, osNoWait);
    }
}

void atPassthNotifyDTREvent(void)
{
    uint8_t channState;
    passthEventCb eventCb;
    atPassthInfo_t *passthInfo;

    for (int atHandle = 0; atHandle < AT_FD_MAX; atHandle++)
    {
        AT_PASSTH_LOCK(osNoWait);

        passthInfo = atPassthInfoGet(atHandle);
        xy_assert(passthInfo != NULL);
        eventCb = passthInfo->eventCb;
        channState = passthInfo->channState;

        if (AT_STATE_IS_PASSTHROUGH(passthInfo))
        {
            if ((passthInfo->events & PASSTH_EVENT_DTR1) && (g_at_config.at_d == 1))
            {
                AT_PASSTH_UNLOCK();
                /* 防止应用层在事件处理回调里若尝试进行透传相关操作会造成死锁 */
                eventCb(atHandle, channState, PASSTH_EVENT_DTR1, NULL);
                continue;
            }
            else if ((passthInfo->events & PASSTH_EVENT_DTR2) && (g_at_config.at_d == 2))
            {
                AT_PASSTH_UNLOCK();
                /* 防止应用层在事件处理回调里若尝试进行透传相关操作会造成死锁 */
                eventCb(atHandle, channState, PASSTH_EVENT_DTR2, NULL);
                continue;
            }
        }

        AT_PASSTH_UNLOCK();
    }
}


#if PASSTH_TIMER_DURATION

int atPassthMatchCharIsTimout(uint32_t curTicks, uint32_t addTicks)
{
    uint32_t timeLag;
    
    if (curTicks >= addTicks)
    {
        timeLag = curTicks - addTicks;
    }
    else
    {
        timeLag = (UINT32_MAX + curTicks) - addTicks;
    }

    return ((timeLag < PASSTH_TIMER_DURATION) ? false : true);
}

/* +++匹配成功通知上层，匹配失败将缓存的匹配字符作为普通数据投递给上层 */
void atPassthNotifyMatchPlus(int atHandle)
{
    /* 加互斥锁，确保不会打断透传退出流程 */
    AT_PASSTH_LOCK(osWaitForever);

    atPassthInfo_t *passthInfo = atPassthInfoGet(atHandle);
    xy_assert(passthInfo != NULL);

    if (!AT_STATE_IS_PASSTHROUGH(passthInfo))
    {
        AT_PASSTH_UNLOCK();
        return;
    }

    uint8_t channState = passthInfo->channState;
    passthEventCb eventCb = passthInfo->eventCb;
    atPassthMatchChar_t *matchPlus = passthInfo->matchPlus;

    if (matchPlus->matchNum == strlen(matchPlus->matchChar))
    {
        atPassthMatchPlusCharReset(matchPlus);
    
        AT_PASSTH_UNLOCK();
        /* "+++"匹配成功，通知上层 */
        eventCb(atHandle, channState, PASSTH_EVENT_TRIPLE_PLUS, NULL);
    }
    else
    {
        atPassthDataArg_t plusDataArg = { .pData = matchPlus->matchChar, .dataSize = matchPlus->matchNum };
        matchPlus->matchNum = 0;

        AT_PASSTH_UNLOCK();
        /* "+++"匹配失败，需将缓存的匹配字符当成正常数据投递给上层，防止出现收到部分匹配字符(如："++"、'+')以及携带特殊字符的数据（如："+++++++++123"等）时出错 */
        eventCb(atHandle, channState, PASSTH_EVENT_DATA_INPUT, &plusDataArg);
    }
}

/* +++匹配成功通知上层，匹配失败将缓存的匹配字符作为普通数据投递给上层 */
void atPassthMatchPlusTimeoutCb(osTimerId_t arg)
{
    int atHandle = (int)osTimerGetCallbackArgs(arg);
    send_msg_2_proxy(PROXY_MSG_PASSTH_MATCH_PLUS, (void*)&atHandle, sizeof(int), osNoWait);
}

/* +++匹配规则：
 * 1）+++输入前 1 秒或更长时间内不能输入其它任何数据；
 * 2）必须在 1 秒内输入+++，并且不能输入其它任何数据；
 * 3）+++输入后 1 秒内不能输入其它任何数据；
 */
bool atPassthMatchPlusChar(atPassthMatchChar_t *matchPlus, char *pData, uint32_t dataSize)
{
    xy_assert(matchPlus != NULL);

    uint32_t curTicks = osKernelGetTickCount();

    /* 第一个字符不为+ */
    if (*pData != matchPlus->matchChar[0])
    {
        goto failMatchPlus;
    }

    uint32_t matchSize = strlen(matchPlus->matchChar);
    /* 匹配长度超出指定字符串"+++"长度 */
    if (dataSize + matchPlus->matchNum > matchSize)
    {
        goto failMatchPlus;
    }
    /* 接收数据中含有非'+'的字符 */
    if (memcmp(pData, matchPlus->matchChar, dataSize))
    {
        goto failMatchPlus;
    }
    /* 若输入+++前1秒内有其他任何字符，则表示检测失败 */
    if (matchPlus->matchNum > 0 || atPassthMatchCharIsTimout(curTicks, matchPlus->recvTime))
    {
        matchPlus->matchNum += dataSize;
        matchPlus->recvTime = curTicks;
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[atPassthMatchPlusChar]matchNum=%u", matchPlus->matchNum);
        /* "+++"输入后 1 秒内不能输入其它任何数据；必须在 1 秒内输入+++ */
        if (matchPlus->matchNum == matchSize || !osTimerIsRunning(matchPlus->timerId))
        {
            osTimerStart(matchPlus->timerId, PASSTH_TIMER_DURATION);
        }

        return 1;
    }
    else
    {
        xy_printf(0, PLATFORM_AP, DEBUG_LOG, "[atPassthMatchPlusChar]match fail,dataSize:%u,matchNum:%u,timeLag:%u", dataSize, matchPlus->matchNum, curTicks - matchPlus->recvTime);
    }

failMatchPlus:
    matchPlus->recvTime = curTicks;
    if (osTimerIsRunning(matchPlus->timerId))
    {
        osTimerStop(matchPlus->timerId);
    }

    return 0;
}

#else /* PASSTH_TIMER_DURATION */

// 参照EC做法
bool atPassthMatchPlusChar(atPassthMatchChar_t *matchPlus, char *pData, uint32_t dataSize)
{
    /* 匹配长度超出指定字符串"+++"长度 */
    if (dataSize == strlen(matchPlus->matchChar) && strcmp(pData, matchPlus->matchChar))
    {
        return 1;
    }

    return 0;
}

#endif /* PASSTH_TIMER_DURATION */

void atPassthMatchPlusCharInit(int atHandle, atPassthMatchChar_t **matchPlus)
{
    xy_assert(matchPlus != NULL);

    if (*matchPlus == NULL)
    {
        *matchPlus = xy_malloc(sizeof(atPassthMatchChar_t));
        memset(*matchPlus, 0, sizeof(atPassthMatchChar_t));

        (*matchPlus)->matchChar = PASSTH_QUIT_SYMBOL;
#if PASSTH_TIMER_DURATION
        osTimerAttr_t pAttr = {0};
        pAttr.name = PASSTH_TIMER_NAME;
        (*matchPlus)->timerId = osTimerNew((osTimerFunc_t)atPassthMatchPlusTimeoutCb, osTimerOnce, (void *)atHandle, &pAttr);
#endif /* PASSTH_TIMER_DURATION */
    }
    else
    {
        atPassthMatchPlusCharReset(*matchPlus);
    }
}

/**
 * @brief   处理从串口接收到的透传码流，先识别是否AT通道的透传模式，再识别是否为GNSS特殊透传模式。若都失败，则认为是普通AT字符串请求
 * @param   atHandle   [IN] 进入透传状态的AT通道句柄
 * @param   pData      [IN] 透传码流数据
 * @param   datasize   [IN] 透传码流数据长度
 * @note    由于uart接收线程线程栈较小并且不能被长时间阻塞，收到透传码流数据后需要投递到业务自定义的数据处理线程进行处理
 * @warning 该接口暂不对业务开放使用，由芯翼内部实现
 */
int atPassthStateDataInput(int atHandle, char *pData, uint32_t datasize)
{
    xy_assert(pData != NULL && datasize > 0);

    /* 加互斥锁，确保不会打断透传退出流程 */
    AT_PASSTH_LOCK(osWaitForever);

    atPassthInfo_t *passthInfo = atPassthInfoGet(atHandle);
    xy_assert(passthInfo != NULL);

    if (!AT_STATE_IS_PASSTHROUGH(passthInfo))
    {
        AT_PASSTH_UNLOCK();
        return XY_ERR;
    }

    uint8_t channState = passthInfo->channState;
    passthEventCb eventCb = passthInfo->eventCb;
    atPassthDataArg_t dataArg = { .pData = pData, .dataSize = datasize };
    // xy_printf(0, PLATFORM_AP, DEBUG_LOG, "[atPassthStateDataInput]recv data,channState:%u,atHandle:%d,datasize:%u", channState, atHandle, datasize);

    if (passthInfo->events & PASSTH_EVENT_TRIPLE_PLUS)
    {
        /* 特殊字符匹配，不作为透传数据投放至应用层（例如：+++）*/
        if (atPassthMatchPlusChar(passthInfo->matchPlus, pData, datasize))
        {
            AT_PASSTH_UNLOCK();
#if !PASSTH_TIMER_DURATION
            eventCb(atHandle, channState, PASSTH_EVENT_TRIPLE_PLUS, NULL);
#endif /* PASSTH_TIMER_DURATION */
            return XY_OK;
        }

        atPassthMatchChar_t *matchPlus = passthInfo->matchPlus;
        if ((matchPlus->matchNum > 0) && (passthInfo->events & PASSTH_EVENT_DATA_INPUT))
        {
            atPassthDataArg_t plusDataArg = { .pData = matchPlus->matchChar, .dataSize = matchPlus->matchNum };
            matchPlus->matchNum = 0;

            AT_PASSTH_UNLOCK();
            /* "+++"匹配失败，需将缓存的匹配字符当成正常数据投递给上层，防止出现收到部分匹配字符(如："++"、'+')以及携带特殊字符的数据（如："+++++++++123"等）时出错 */
            eventCb(atHandle, channState, PASSTH_EVENT_DATA_INPUT, &plusDataArg);
            /* eventCb处理数据时释放锁，防止当数据发送过快时，在此处死等，此时应用层在事件处理回调里若尝试进行透传相关操作会造成死锁 */
            eventCb(atHandle, channState, PASSTH_EVENT_DATA_INPUT, &dataArg);
            return XY_OK;
        }
    }
    if (passthInfo->events & PASSTH_EVENT_DATA_INPUT)
    {
        AT_PASSTH_UNLOCK();
        /* eventCb处理数据时释放锁，防止当数据发送过快时，在此处死等，此时应用层在事件处理回调里若尝试进行透传相关操作会造成死锁 */
        eventCb(atHandle, channState, PASSTH_EVENT_DATA_INPUT, &dataArg);
        return XY_OK;
    }

    AT_PASSTH_UNLOCK();

    return XY_OK;
}

/**
 * @brief   ATO恢复该AT通道最近一次记录的透传业务
 * @param   atHandle   [IN] 进入透传状态的AT通道句柄
 * @warning 该接口暂不对业务开放使用，由芯翼内部实现
 */
int atPassthStateResumeByATO(int atHandle)
{
    AT_PASSTH_LOCK(osWaitForever);

    atPassthInfo_t *passthInfo = atPassthInfoGet(atHandle);
    xy_assert(passthInfo != NULL);

    if (!AT_STATE_IS_ONLINE_COMMAND(passthInfo))
    {
        xy_printf(0, PLATFORM_AP, DEBUG_LOG, "[atPassthStateResumeByATO]atHandle:%d,channState:%u", atHandle, passthInfo->channState);
        AT_PASSTH_UNLOCK();
        return XY_ERR;
    }

    passthEventCb eventCb = passthInfo->eventCb;
    AT_PASSTH_UNLOCK();

    return eventCb(atHandle, AT_STATE_ONLINE_COMMAND, PASSTH_EVENT_ATO, NULL);
}

/**
 * @brief  清空透传相关上下文，退出透传模式，切换回AT命令模式，该接口调用点为完成透传数据的传递后，或者接收超时后。
 * @param  atHandle   [IN] 进入透传状态的AT通道句柄
 * @param  passthState[IN] 使用者唯一的透传业务状态标识
 * @return 操作结果码，@see @ref XY_PLAT_ERR_E 
 * @note   用户可自行定义退出的条件，在各自业务流程中匹配成功后调用该接口
 * @note   若同一AT通道做了多个透传相关业务，透传框架仅保存最近一次的透传业务相关信息，ATO/+++等特殊命令只针对最新的透传业务。
 *         其他业务调用此接口会返回XY_Err_InProgress，此时其他业务不应再进行透传恢复/挂起等操作，可再次调用atEnterPassthroughState重新占用AT透传通道。
 */
int atEnterCommandState(int atHandle, atchannState_e passthState)
{
    AT_PASSTH_LOCK(osWaitForever);

    atPassthInfo_t *passthInfo = atPassthInfoGet(atHandle);
    xy_assert(passthInfo != NULL);

    if (AT_STATE_IS_COMMAND(passthInfo))
    {
        xy_printf(0, PLATFORM_AP, WARN_LOG, "[atEnterCommandState]state err,atHandle:%d,channState:%u", atHandle, passthState);
        AT_PASSTH_UNLOCK();
        return XY_Err_NotAllowed;
    }
    if (PASSTH_STATE_IS_CHANGED(passthInfo, passthState))
    {
        xy_printf(0, PLATFORM_AP, WARN_LOG, "[atEnterCommandState]AT chann busy,atHandle:%d,channState:%u,passthState:%u", atHandle, passthInfo->PrevPassthState, passthState);
        AT_PASSTH_UNLOCK();
        return XY_Err_InProgress;
    }

    xy_printf(0, PLATFORM_AP, WARN_LOG, "[atEnterCommandState]AT chann exit passth succ,atHandle:%d,channState:%u", atHandle, passthInfo->channState);
    atPassthInfoClear(passthInfo);

    app_delay_unlock();

    AT_PASSTH_UNLOCK();

    return XY_OK;
}
/**
 * @brief   保存透传相关上下文，从透传模式切换到AT命令模式(对应AT_STATE_ONLINE_COMMAND状态)，该接口调用点为识别到特殊字符后，例如“+++”，可使用"ATO"等命令进行恢复最近一次的透传业务状态
 * @param   atHandle   [IN] 进入透传状态的AT通道句柄
 * @param   passthState[IN] 使用者唯一的透传业务状态标识
 * @param   events     [IN] 用户想要监测的事件类型集合，@see @ref PASSTH_EVENTID_E
 * @param   eventCb    [IN] 用户实现的透传事件处理回调函数，内部进行透传事件的接收处理，eventCb内不可有阻塞操作（如网络数据交互等），否则会影响透传数据的接收处理
 * @return  操作结果码，@see @ref XY_PLAT_ERR_E
 * @note    一般与"ATO"命令捆绑使用!
 * @warning 该函数会新建透传线程处理TTY传输的透传数据，用于透传数据处理存在耗时或者栈调用较深的场景，如socket透传
 */
int atEnterOnlineCommandState(int atHandle, atchannState_e passthState, int events, passthEventCb eventCb)
{
    AT_PASSTH_LOCK(osWaitForever);

    atPassthInfo_t *passthInfo = atPassthInfoGet(atHandle);
    xy_assert(passthInfo != NULL);

    if (!AT_STATE_IS_PASSTHROUGH(passthInfo))
    {
        xy_printf(0, PLATFORM_AP, WARN_LOG, "[atEnterOnlineCommandState]state err,atHandle:%d,channState:%u", atHandle, passthInfo->channState);
        AT_PASSTH_UNLOCK();
        return XY_Err_NotAllowed;
    }
    if (PASSTH_STATE_IS_CHANGED(passthInfo, passthState))
    {
        xy_printf(0, PLATFORM_AP, WARN_LOG, "[atEnterCommandState]AT chann busy,atHandle:%d,channState:%u,passthState:%u", atHandle, passthInfo->PrevPassthState, passthState);
        AT_PASSTH_UNLOCK();
        return XY_Err_InProgress;
    }
    if (passthInfo->matchPlus != NULL)
    {
        atPassthMatchPlusCharReset(passthInfo->matchPlus);
    }
    passthInfo->events     = events;
    passthInfo->eventCb    = eventCb;
    passthInfo->channState = AT_STATE_ONLINE_COMMAND;

    app_delay_unlock();

    AT_PASSTH_UNLOCK();

    return XY_OK;
}

/**
 * @brief 进入透传前等待一会，避免低波特率场景下，用户at命令尾部的\n被当作透传数据处理
 */
void atPassthroughWaitForUart(int atHandle)
{
 	if (IS_AT_TTY_PHY(atHandle))
    {
        uint64_t enterTimeout = __get_MTIME() + 32; //1ms
        uint32_t bandrate = atUartBaudRateGet();
        if (bandrate < 115200 && bandrate != 0)
        {
            if (bandrate == 4800)
            {
                enterTimeout = __get_MTIME() + 160; // 5ms
            }
            else if (bandrate == 9600)
            {
                enterTimeout = __get_MTIME() + 64; // 2ms
            }
            else
            {
                enterTimeout = __get_MTIME() + 32; // 1ms
            }

            xy_printf(0, PLATFORM_AP, WARN_LOG, "[atPassthroughWaitForUart]bandrate:%d wait...", bandrate);
            while (__get_MTIME() < enterTimeout)
            {
                ;
            }
        }
    }   
}

/**
 * @brief   进入透传状态，供特殊业务注册透传事件回调，相关回调函数由芯翼平台后台调用执行。
 * @param   atHandle   [IN] 进入透传状态的AT通道句柄
 * @param   passthState[IN] 使用者唯一的透传业务状态标识
 * @param   events     [IN] 用户想要监测的事件类型集合，@see @ref PASSTH_EVENTID_E
 * @param   eventCb    [IN] 用户实现的透传事件处理回调函数，内部进行透传事件的接收处理，eventCb内不可有阻塞操作（如网络数据交互等），否则会影响透传数据的接收处理
 * @return  操作结果码，@see @ref XY_PLAT_ERR_E
 * @note    支持多AT通道的透传模式并行处理!
 *          多业务进入透传情况下，每个AT通道仅记录最近一次透传业务信息，历史业务不能自动恢复，需重新调用此接口！
 * @warning 该函数会新建透传线程处理TTY传输的透传数据，用于透传数据处理存在耗时或者栈调用较深的场景，如socket透传
 */
int atEnterPassthroughState(int atHandle, atchannState_e passthState, int events, passthEventCb eventCb)
{
    xy_assert(events == PASSTH_EVENT_UNKNOWN || eventCb != NULL);

    atPassthroughWaitForUart(atHandle);

    AT_PASSTH_LOCK(osWaitForever);

    app_delay_lock(30*60*1000);

    atPassthInfo_t *passthInfo = atPassthInfoGet(atHandle);
    xy_assert(passthInfo != NULL);

    if (events & PASSTH_EVENT_TRIPLE_PLUS)
    {
        atPassthMatchPlusCharInit(atHandle, &passthInfo->matchPlus);
    }
    if (AT_STATE_IS_PASSTHROUGH(passthInfo))
    {
        /* 记录之前的透传状态，防止业务层误操作已经被另外的业务占用的透传通道 */
        passthInfo->PrevPassthState = passthInfo->channState;
    }
    passthInfo->events     = events;
    passthInfo->eventCb    = eventCb;
    passthInfo->channState = passthState;
    xy_assert(AT_STATE_IS_PASSTHROUGH(passthInfo));

    xy_printf(0, PLATFORM_AP, WARN_LOG, "[atEnterPassthroughState]atHandle:%d,passthState:%d,events:%d", atHandle, passthState, events);

    AT_PASSTH_UNLOCK();

    return XY_OK;
}
/**
 * @brief   透传资源初始化
 * @warning 该接口暂不对业务开放使用，由芯翼内部实现
 */
void atPassthInit(void)
{
    gPassthMutex = osMutexNew(NULL);
    g_sms_passth_mutex = osMutexNew(NULL);
}
