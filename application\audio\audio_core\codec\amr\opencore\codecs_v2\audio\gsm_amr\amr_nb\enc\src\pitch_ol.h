/* ------------------------------------------------------------------
 * Copyright (C) 1998-2009 PacketVideo
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied.
 * See the License for the specific language governing permissions
 * and limitations under the License.
 * -------------------------------------------------------------------
 */
/****************************************************************************************
Portions of this file are derived from the following 3GPP standard:

    3GPP TS 26.073
    ANSI-C code for the Adaptive Multi-Rate (AMR) speech codec
    Available from http://www.3gpp.org

(C) 2004, 3GPP Organizational Partners (ARIB, ATIS, CCSA, ETSI, TTA, TTC)
Permission to distribute, modify and use this file under the standard license
terms listed above has been obtained from the copyright holder.
****************************************************************************************/
/*
------------------------------------------------------------------------------



 Filename: pitch_ol.h

------------------------------------------------------------------------------
 INCLUDE DESCRIPTION

       File             : pitch_ol.h
       Purpose          : Compute the open loop pitch lag.

------------------------------------------------------------------------------
*/

#ifndef PITCH_OL_H
#define PITCH_OL_H "$Id $"

/*----------------------------------------------------------------------------
; INCLUDES
----------------------------------------------------------------------------*/
#include "typedef.h"
#include "mode.h"
#include "vad.h"

/*--------------------------------------------------------------------------*/
#ifdef __cplusplus
extern "C"
{
#endif

    /*----------------------------------------------------------------------------
    ; MACROS
    ; [Define module specific macros here]
    ----------------------------------------------------------------------------*/

    /*----------------------------------------------------------------------------
    ; DEFINES
    ; [Include all pre-processor statements here.]
    ----------------------------------------------------------------------------*/

    /*----------------------------------------------------------------------------
    ; EXTERNAL VARIABLES REFERENCES
    ; [Declare variables used in this module but defined elsewhere]
    ----------------------------------------------------------------------------*/

    /*----------------------------------------------------------------------------
    ; SIMPLE TYPEDEF'S
    ----------------------------------------------------------------------------*/

    /*----------------------------------------------------------------------------
    ; ENUMERATED TYPEDEF'S
    ----------------------------------------------------------------------------*/

    /*----------------------------------------------------------------------------
    ; STRUCTURES TYPEDEF'S
    ----------------------------------------------------------------------------*/


    /*----------------------------------------------------------------------------
    ; GLOBAL FUNCTION DEFINITIONS
    ; [List function prototypes here]
    ----------------------------------------------------------------------------*/
    Word16 Pitch_ol(       /* o   : open loop pitch lag                         */
        vadState *vadSt,   /* i/o : VAD state struct                            */
        enum Mode mode,    /* i   : coder mode                                  */
        Word16 signal[],   /* i   : signal used to compute the open loop pitch  */
        /*    signal[-pit_max] to signal[-1] should be known */
        Word16 pit_min,    /* i   : minimum pitch lag                           */
        Word16 pit_max,    /* i   : maximum pitch lag                           */
        Word16 L_frame,    /* i   : length of frame to compute pitch            */
        Word16 idx,        /* i   : frame index                                 */
        Flag dtx,          /* i   : dtx flag; use dtx=1, do not use dtx=0       */
        Flag *pOverflow    /* i/o : overflow Flag                               */
    );


#ifdef __cplusplus
}
#endif

#endif  /* PITCH_OL_H_ */


