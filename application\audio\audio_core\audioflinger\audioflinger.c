/***************************************************************************
 *
 * Copyright 2023 XY.
 *
 ****************************************************************************/
#include "audioflinger.h"
#include "cmsis_os2.h"
#include "hal_trace.h"
#include "string.h"
#include "audio_device.h"

static uint8_t * play_buf_for_record = NULL;

#define AF_TRACE_DEBUG()    TRACE(2,"%s:%d\n", __func__, __LINE__)

#define AF_THREAD_NAME        "audio_flinger"
#define AF_THREAD_STACKSIZE   (1024*2)
#define AF_THREAD_PRIO        osPriorityAboveNormal

volatile int play_dma_stop_flag;
volatile int record_dma_stop_flag;
static osSemaphoreId_t audio_colse_sync_sem;
static uint8_t audio_colse_sync_flag = 0;

enum AF_RESULT_T{
    AF_RES_SUCCESS = 0,
    AF_RES_FAILD = 1
};

//status machine
enum AF_STATUS_T{
    AF_STATUS_NULL = 0x00,
    AF_STATUS_OPEN_CLOSE = 0x01,
    AF_STATUS_STREAM_OPEN_CLOSE = 0x02,
    AF_STATUS_STREAM_START_STOP = 0x04,
    AF_STATUS_STREAM_PAUSE_RESTART = 0x08,
    AF_STATUS_MASK = 0x0F,
};

struct af_stream_ctl_t{
    enum AF_PP_T pp_index;      //pingpong operate
    uint8_t pp_cnt;             //use to count the lost signals
    uint8_t status;             //status machine

    bool first_hdlr_proc;
};

struct af_stream_cfg_t {
    //used inside
    struct af_stream_ctl_t ctl;

    //dma buf parameters, RAM can be alloced in different way
    uint8_t *dma_buf_ptr;
    uint32_t dma_buf_size;

    //store stream cfg parameters
    struct AF_STREAM_CONFIG_T cfg;

    //callback function
    AF_STREAM_HANDLER_T handler;
};

static struct af_stream_cfg_t af_stream[AUD_STREAM_NUM];

static osThreadId_t af_thread_tid;

//0为ping 1为pang
volatile uint8_t play_ping_pang_flag = 0;
volatile uint8_t record_ping_pang_flag = 0;

//used by dma irq and af_thread
static inline struct af_stream_cfg_t *af_get_stream_role(enum AUD_STREAM_T stream)
{
    ASSERT(stream < AUD_STREAM_NUM, "[%s] Bad stream=%d", __func__, stream);

    return &af_stream[stream];
}

static void af_set_status(enum AUD_STREAM_T stream, enum AF_STATUS_T status)
{
    struct af_stream_cfg_t *role = NULL;

    role = af_get_stream_role(stream);
    role->ctl.status |= status;
}

static void af_clear_status(enum AUD_STREAM_T stream, enum AF_STATUS_T status)
{
    struct af_stream_cfg_t *role = NULL;

    role = af_get_stream_role(stream);
    role->ctl.status &= ~status;
}

//get current stream config parameters
uint32_t af_stream_get_cfg( enum AUD_STREAM_T stream, struct AF_STREAM_CONFIG_T **cfg)
{
    AF_TRACE_DEBUG();

    struct af_stream_cfg_t *role;
    enum AF_RESULT_T ret;

    role = af_get_stream_role(stream);
    //check stream is open
    if (role->ctl.status & AF_STATUS_STREAM_OPEN_CLOSE) {
        *cfg = &(role->cfg);
        ret = AF_RES_SUCCESS;
    } else {
        ret = AF_RES_FAILD;
    }

    return ret;
}

static inline void af_thread_stream_handler(enum AUD_STREAM_T stream)
{
    uint32_t lock;
    struct af_stream_cfg_t *role=NULL;
    uint8_t *buf=NULL;
    uint32_t len;

    role = af_get_stream_role(stream);

    if (role->handler && (role->ctl.status & AF_STATUS_STREAM_START_STOP)) 
    {
        len = role->dma_buf_size / 2;

        if(play_ping_pang_flag == 1)
        {
            buf = role->dma_buf_ptr;
        }
        else
        {
            buf = role->dma_buf_ptr + len;
        }

        //需64字节对齐
        xy_assert((len & (64-1)) == 0);

        if(stream == AUD_STREAM_PLAYBACK)
        {
            role->handler(buf, len);
            csi_dcache_clean_range(buf, len);
        }
        else
        {
            csi_dcache_invalid_range(buf, len);
            role->handler(buf, len);
        }
    }
}


static void af_thread(void const *argument)
{
    uint32_t flags = 0;
    enum AUD_STREAM_T stream;

    while(1)
    {
        //wait signal
        flags = osThreadFlagsWait (3, osFlagsWaitAny, osWaitForever);

        if((flags & EVENT_FLAGS_INVALID_BITS) == 0)
        {
            for(uint8_t i = 0; i < AUD_STREAM_NUM; i ++)
            {
                if(flags & (1 << i))
                {
                    stream = (enum AUD_STREAM_T)(i & 1);
                    af_thread_stream_handler(stream);
                }
            }
        }
        else
        {
            xy_assert(0);
        }
    }
}
#if AUDIO_CODEC_TYPE
__attribute__((section(".psram.text"))) static void af_play_irq_handler(void)
{
	uint8_t is_tfr_done = 0;
	uint32_t temp_addr = 0;
    uint32_t dma_buf;
    uint32_t dma_buf_size;

    is_tfr_done = audio_device.done(0);

    if((play_dma_stop_flag == 1) && (record_dma_stop_flag == 1))
    {
        osSemaphoreRelease(audio_colse_sync_sem);
        return;
    }

	if(is_tfr_done)
	{
        temp_addr = audio_device.address(0);
        dma_buf = (uint32_t)(af_stream[AUD_STREAM_PLAYBACK].dma_buf_ptr);
        dma_buf_size = af_stream[AUD_STREAM_PLAYBACK].dma_buf_size;

		if(play_ping_pang_flag == 0)
		{
			play_ping_pang_flag = 1;
            xy_assert(temp_addr == (dma_buf + (dma_buf_size / 2)));
		}
		else
		{
			play_ping_pang_flag = 0;
            xy_assert(temp_addr == (dma_buf + dma_buf_size));
            temp_addr = dma_buf;
		}

        audio_device.start((uint32_t)temp_addr, dma_buf_size / 2, 0);
        //录音不需要获取播放数据
        if(af_stream[AUD_STREAM_CAPTURE].dma_buf_ptr == NULL)
        {
            osThreadFlagsSet(af_thread_tid, 1);
        }
	}
    else
    {
        xy_assert(0);
    }

    return;
}
#else

#include "xy4100_ll_timer.h"
uint16_t * play_data;
uint32_t play_data_count;
uint32_t play_data_size1;
uint32_t play_data_size2;
static BaseType_t yield = pdFALSE;;
__attribute__((section(".sysram.text"))) static void af_play_irq_handler(void)
{
    // LL_TIMER_ClearFlag_IntStatus(DATA_TIMER); //清除中断标志，timer0不需要

    LL_TIMER_Set_PwmValue(PWM_TIMER, play_data[play_data_count]);
    play_data_count ++;

    if(play_data_count == play_data_size1)
    {
        (void)xTaskNotifyAndQueryFromISR (af_thread_tid, 1, eSetBits, NULL, NULL);
        play_ping_pang_flag = 1;

        if(play_dma_stop_flag == 1)
        {
            osSemaphoreRelease(audio_colse_sync_sem);
        }
    }
    else if(play_data_count == play_data_size2)
    {
        (void)xTaskNotifyAndQueryFromISR (af_thread_tid, 1, eSetBits, NULL, NULL);
        play_ping_pang_flag = 0;

        if(play_dma_stop_flag == 1)
        {
            osSemaphoreRelease(audio_colse_sync_sem);
        }
        play_data_count = 0;
    }

}
#endif
__attribute__((section(".psram.text"))) static void af_record_irq_handler(void)
{
	uint8_t is_tfr_done = 0;
	uint32_t temp_addr = 0;
    uint32_t dma_buf;
    uint32_t dma_buf_size;

    is_tfr_done = audio_device.done(1);

	if(is_tfr_done)
	{
        temp_addr = audio_device.address(1);
        dma_buf = (uint32_t)(af_stream[1].dma_buf_ptr);
        dma_buf_size = af_stream[1].dma_buf_size;

		if(record_ping_pang_flag == 0)
		{
			record_ping_pang_flag = 1;
            xy_assert(temp_addr == (dma_buf + (dma_buf_size / 2)));
		}
		else
		{
			record_ping_pang_flag = 0;
            xy_assert(temp_addr == (dma_buf + dma_buf_size));
            temp_addr = dma_buf;
		}

        //先关录音，再关播放，播放关闭CLK关闭，最后一笔录音数据发送给上层
        if(play_dma_stop_flag == 1)
        {
            record_dma_stop_flag = 1;
        }
        else
        {
            audio_device.start((uint32_t)temp_addr, dma_buf_size / 2, 1);
        }
        osThreadFlagsSet(af_thread_tid, 2);
	}
    else
    {
        xy_assert(0);
    }

    return;
}

uint32_t af_open(void)
{
    struct af_stream_cfg_t *role = NULL;
	osThreadAttr_t thread_attr = {0};

    for(uint8_t stream=0; stream < AUD_STREAM_NUM; stream++)
    {
        role = af_get_stream_role((enum AUD_STREAM_T)stream);

        if(role->ctl.status == AF_STATUS_NULL)
        {
            role->dma_buf_ptr = NULL;
            role->dma_buf_size = 0;
            role->ctl.status = AF_STATUS_OPEN_CLOSE;
        }
        else
        {
            ASSERT(0, "[%s] ERROR: id = %d, stream = %d", __func__, id, stream);
        }
    }

	thread_attr.name        = AF_THREAD_NAME;
	thread_attr.priority    = AF_THREAD_PRIO;
	thread_attr.stack_size  = AF_THREAD_STACKSIZE;
	af_thread_tid = osThreadNew((osThreadFunc_t)(af_thread), NULL, &thread_attr);
    osThreadFlagsSet(af_thread_tid, 0);

    return AF_RES_SUCCESS;
}

// Support memory<-->peripheral
// Note: Do not support peripheral <--> peripheral
uint32_t af_stream_open(enum AUD_STREAM_T stream, const struct AF_STREAM_CONFIG_T *cfg)
{
    AF_TRACE_DEBUG();
    struct af_stream_cfg_t *role;
    enum AF_RESULT_T ret;
    enum AUD_STREAM_USE_DEVICE_T device;

    role = af_get_stream_role(stream);
    TRACE(3,"[%s] id = %d, stream = %d", __func__, id, stream);

    ASSERT(cfg->data_ptr != NULL, "[%s] ERROR: data_ptr == NULL!!!", __func__);
    ASSERT(((uint32_t)cfg->data_ptr) % 4 == 0, "[%s] ERROR: data_ptr(%p) is not align!!!", __func__, cfg->data_ptr);
    ASSERT(cfg->data_size != 0, "[%s] ERROR: data_size == 0!!!", __func__);

    ret = AF_RES_FAILD;

    //check af is open
    if(role->ctl.status != AF_STATUS_OPEN_CLOSE)
    {
        TRACE(2,"[%s] ERROR: status = %d",__func__, role->ctl.status);
        goto _exit;
    }

    role->cfg = *cfg;
    role->handler = cfg->handler;
    role->dma_buf_ptr = cfg->data_ptr;
    role->dma_buf_size = cfg->data_size;

    if(audio_colse_sync_sem == NULL)
    {
        audio_colse_sync_sem = osSemaphoreNew(1, 0, NULL);
    }

    if (stream == AUD_STREAM_PLAYBACK)
    {
        xy_printf(0,XYAPP, INFO_LOG, "sample_rate: %d, dma_buf_size: %d", cfg->sample_rate, cfg->data_size);

        play_dma_stop_flag = 0;
        record_dma_stop_flag = 1;

        audio_device.poweron((uint8_t)stream);
        audio_device.open(cfg->sample_rate, (uint8_t)stream);
        audio_device.config(af_play_irq_handler, (uint8_t)stream);

    }
#ifdef XY_AUDIO_RECORD
    else
    {
        play_dma_stop_flag = 0;
        record_dma_stop_flag = 0;
        audio_device.poweron((uint8_t)stream);
        audio_device.open(cfg->sample_rate, (uint8_t)stream);
        audio_device.config(af_record_irq_handler, (uint8_t)stream);

        //录音时，需要打开播放DMA传输，否则无CLK
        role = af_get_stream_role(AUD_STREAM_PLAYBACK);
        play_buf_for_record = xy_malloc_align(cfg->data_size);
        memset(play_buf_for_record, 0, cfg->data_size);
        role->dma_buf_ptr = play_buf_for_record;
        csi_dcache_clean_range(play_buf_for_record, cfg->data_size);

        role->dma_buf_size = cfg->data_size;
        audio_device.config(af_play_irq_handler, AUD_STREAM_PLAYBACK);
    }
#endif

    af_set_status(stream, AF_STATUS_STREAM_OPEN_CLOSE);

    AF_TRACE_DEBUG();
    ret = AF_RES_SUCCESS;

_exit:

    return ret;
}

uint32_t af_stream_mute(enum AUD_STREAM_T stream, bool mute)
{
    AF_TRACE_DEBUG();

    struct af_stream_cfg_t *role;
    enum AF_RESULT_T ret;

    ret = AF_RES_FAILD;
    role = af_get_stream_role(stream);

    if ((role->ctl.status & AF_STATUS_STREAM_OPEN_CLOSE) == 0) {
        TRACE(2,"[%s] ERROR: status = %d", __func__, role->ctl.status);
        goto _exit;
    }

#ifdef AF_DEVICE_INT_CODEC
    //codec_int_stream_mute(stream, mute);
    ret = AF_RES_SUCCESS;
#endif

_exit:

    return ret;
}

uint32_t af_stream_set_chan_vol(enum AUD_STREAM_T stream, enum AUD_CHANNEL_MAP_T ch_map, uint8_t vol)
{
    AF_TRACE_DEBUG();

    struct af_stream_cfg_t *role;
    enum AF_RESULT_T ret;

    ret = AF_RES_FAILD;
    role = af_get_stream_role(stream);

    if ((role->ctl.status & AF_STATUS_STREAM_OPEN_CLOSE) == 0) {
        TRACE(2,"[%s] ERROR: status = %d", __func__, role->ctl.status);
        goto _exit;
    }

#ifdef AF_DEVICE_INT_CODEC
    codec_int_stream_set_chan_vol(stream, ch_map, vol);
    ret = AF_RES_SUCCESS;
#endif

_exit:

    return ret;
}

uint32_t af_stream_restore_chan_vol(enum AUD_STREAM_T stream)
{
    AF_TRACE_DEBUG();

    struct af_stream_cfg_t *role;
    enum AF_RESULT_T ret;

    ret = AF_RES_FAILD;
    role = af_get_stream_role(stream);

    if ((role->ctl.status & AF_STATUS_STREAM_OPEN_CLOSE) == 0) {
        TRACE(2,"[%s] ERROR: status = %d", __func__, role->ctl.status);
        goto _exit;
    }

#ifdef AF_DEVICE_INT_CODEC
    codec_int_stream_restore_chan_vol(stream);
    ret = AF_RES_SUCCESS;
#endif

_exit:

    return ret;
}

uint32_t af_stream_start(enum AUD_STREAM_T stream)
{
    struct af_stream_cfg_t *role;
    enum AF_RESULT_T ret;

    role = af_get_stream_role(stream);

    //check stream is open and not start.
    if(role->ctl.status != (AF_STATUS_OPEN_CLOSE | AF_STATUS_STREAM_OPEN_CLOSE))
    {
        TRACE(2,"[%s] ERROR: status = %d",__func__, role->ctl.status);
        ret = AF_RES_FAILD;
        goto _exit;
    }

    role->ctl.pp_index = PP_PING;
    role->ctl.pp_cnt = 0;
    role->ctl.first_hdlr_proc = true;

    play_ping_pang_flag = 0;
    record_ping_pang_flag = 0;

    if(stream == AUD_STREAM_PLAYBACK)
    {
        csi_dcache_clean_range(role->dma_buf_ptr, role->dma_buf_size);
    }
    else
    {
        csi_dcache_invalid_range(role->dma_buf_ptr, role->dma_buf_size);
    }

    audio_device.start((uint32_t)(role->dma_buf_ptr), role->dma_buf_size/2, (uint8_t)stream);
    if(stream == AUD_STREAM_CAPTURE)
    {
        audio_device.start((uint32_t)(af_stream[AUD_STREAM_PLAYBACK].dma_buf_ptr), (af_stream[AUD_STREAM_PLAYBACK].dma_buf_size)/2, (uint8_t)AUD_STREAM_PLAYBACK);
    }

    audio_colse_sync_flag = 1;

    af_set_status(stream, AF_STATUS_STREAM_START_STOP);

    ret = AF_RES_SUCCESS;

_exit:

    return ret;
}

uint32_t af_stream_stop(enum AUD_STREAM_T stream)
{
    struct af_stream_cfg_t *role;
    enum AF_RESULT_T ret;

    role = af_get_stream_role(stream);

    //check stream is start and not stop
    if (role->ctl.status != (AF_STATUS_OPEN_CLOSE | AF_STATUS_STREAM_OPEN_CLOSE | AF_STATUS_STREAM_START_STOP))
    {
        TRACE(2,"[%s] ERROR: status = %d",__func__, role->ctl.status);
        ret = AF_RES_FAILD;
        goto _exit;
    }

    // 播放和录音停止时都是设置这个标志，如果是播放直接停止即可，如果是录音，需先停录音dma，再停播放dma
    play_dma_stop_flag = 1;
    af_clear_status(stream, AF_STATUS_STREAM_START_STOP);

    ret = AF_RES_SUCCESS;

_exit:

    return ret;
}

uint32_t af_stream_close(enum AUD_STREAM_T stream)
{
    struct af_stream_cfg_t *role;
    enum AF_RESULT_T ret;

    role = af_get_stream_role(stream);

    //check stream is stop and not close.
    if(role->ctl.status != (AF_STATUS_OPEN_CLOSE | AF_STATUS_STREAM_OPEN_CLOSE))
    {
        TRACE(2,"[%s] ERROR: status = %d",__func__, role->ctl.status);
        ret = AF_RES_FAILD;
        goto _exit;
    }

    if(audio_colse_sync_flag)
    {
        osSemaphoreAcquire(audio_colse_sync_sem, osWaitForever);
        audio_colse_sync_flag = 0;
    }

    audio_device.close((uint8_t)stream);
    audio_device.poweroff((uint8_t)stream);

    if(stream == AUD_STREAM_CAPTURE)
    {
        if(play_buf_for_record != NULL)
        {
            xy_free(play_buf_for_record);
            play_buf_for_record = NULL;
        }
    }

    role->handler = NULL;
    role->ctl.pp_index = PP_PING;
    role->dma_buf_ptr = NULL;
    role->dma_buf_size = 0;

    af_clear_status(stream, AF_STATUS_STREAM_OPEN_CLOSE);

    ret = AF_RES_SUCCESS;

_exit:

    return ret;
}

uint32_t af_close(void)
{
    struct af_stream_cfg_t *role;

    for (uint8_t stream=0; stream < AUD_STREAM_NUM; stream++)
    {
        role = af_get_stream_role((enum AUD_STREAM_T)stream);
        role->ctl.status = AF_STATUS_NULL;
    }

#ifdef AF_DEVICE_INT_CODEC
    codec_int_close(CODEC_CLOSE_FORCED);
#endif

    return AF_RES_SUCCESS;
}

