#include "alsa_config.h"
#include "alsa.h"
#include "alsa_extra.h"
#include <stdlib.h>
#include <string.h>
#include "audioflinger.h"
#include "hal_trace.h"
#include "alsa_instance.h"
#include "alsa_deamon.h"
#include "alsa_os.h"
#include "alsa_playback.h"
#include "alsa_capture.h"
#include "heap_api.h"

struct alsa_handle
{
    alsa_mode_t mode;
    alsa_playback_t *playback;
#ifdef ALSA_SUPPORT_CAPTURE
    alsa_capture_t *capture;
#endif
};

static inline bool is_playback_mode(alsa_handle_t *h)
{
    if (h->mode & ALSA_MODE_OUT)
        return true;
    else
        return false;
}

static inline bool is_capture_mode(alsa_handle_t *h)
{
    if (h->mode & ALSA_MODE_IN)
        return true;
    else
        return false;
}

static inline bool is_pull_push_mode(alsa_handle_t *h)
{
    if (h->mode & ALSA_MODE_DATA_PULL_PUSH)
        return true;
    else
        return false;
}

alsa_handle_t *alsa_open(alsa_mode_t mode, uint32_t sample_rate, uint8_t channels, uint8_t bits)
{
    alsa_handle_t *h = NULL;
    alsa_playback_t *playback = NULL;
#ifdef ALSA_SUPPORT_CAPTURE
    alsa_capture_t *capture = NULL;
#endif

    h = (alsa_handle_t *)audio_calloc(1, sizeof(alsa_handle_t));
    if (!h)
    {
        TRACE(0, "%s h alloc error", __func__);
        goto error_exit;
    }

    h->mode = mode;

#if (AUDIO_CODEC_TYPE == 0)
    // pwm audio只支持8k采样率
    if(sample_rate != 8000)
    {
        goto error_exit;
    }
#endif

    if (is_playback_mode(h))
    {
        playback = alsa_playback_open(sample_rate, channels, bits);
        if (!playback)
        {
            TRACE(0, "%s alsa_playback_open error", __func__);
            goto error_exit;
        }
        h->playback = playback;
    }
#ifdef ALSA_SUPPORT_CAPTURE
    else if (is_capture_mode(h))
    {
        capture = alsa_capture_open(sample_rate, channels, bits);
        if (!capture)
        {
            TRACE(0, "%s alsa_capture_open error", __func__);
            goto error_exit;
        }
        h->capture = capture;
    }
#endif
    else
    {
        TRACE(0, "%s unsupport mode %d", __func__, mode);
        goto error_exit;
    }

    return h;

error_exit:

    if (h)
    {
        audio_free(h);
    }
    if (playback)
    {
        alsa_playback_close(playback);
    }
#ifdef ALSA_SUPPORT_CAPTURE
    if (capture)
    {
        alsa_capture_close(capture);
    }
#endif
    return NULL;
}

int alsa_set_data_pull_push_callback(alsa_handle_t *h, alsa_data_pull_push_cb_t cb)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_pull_push_mode(h))
    {
        if (is_playback_mode(h))
            return alsa_playbakc_set_data_push_callback(h->playback, cb);
#ifdef ALSA_SUPPORT_CAPTURE
        if (is_capture_mode(h))
            return alsa_capture_set_data_pull_callback(h->capture, cb);
#endif
    }
    return -1;
}

int alsa_start(alsa_handle_t *h)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_start(h->playback);
    }
#ifdef ALSA_SUPPORT_CAPTURE
    else if (is_capture_mode(h))
    {
        return alsa_capture_start(h->capture);
    }
#endif
    else
    {
        TRACE(0, "%s mode %d not support", __func__, h->mode);
        return -1;
    }
}

int alsa_pause(alsa_handle_t *h)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_pause(h->playback);
    }
#ifdef ALSA_SUPPORT_CAPTURE
    else if (is_capture_mode(h))
    {
        return alsa_capture_pause(h->capture);
    }
#endif
    else
    {
        TRACE(0, "%s mode %d not support", __func__, h->mode);
        return -1;
    }
}

int alsa_stop(alsa_handle_t *h)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_stop(h->playback, ALSA_PLAYBACK_STOP_NORMAL);
    }
#ifdef ALSA_SUPPORT_CAPTURE
    else if (is_capture_mode(h))
    {
        return alsa_capture_stop(h->capture);
    }
#endif
    else
    {
        TRACE(0, "%s mode %d not support", __func__, h->mode);
        return -1;
    }
}


int alsa_stop_immediate_with_fadeout(alsa_handle_t *h)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_stop(h->playback, ALSA_PLAYBACK_STOP_FADEOUT);
    }
#ifdef ALSA_SUPPORT_CAPTURE
    else if (is_capture_mode(h))
    {
        TRACE(0, "capture not support %s", __func__);
        return -1;
    }
#endif
    else
    {
        TRACE(0, "%s mode %d not support", __func__, h->mode);
        return -1;
    }
}

int alsa_flush(alsa_handle_t *h)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_flush(h->playback);
    }
#ifdef ALSA_SUPPORT_CAPTURE
    else if (is_capture_mode(h))
    {
        return alsa_capture_flush(h->capture);
    }
#endif
    else
    {
        TRACE(0, "%s mode %d not support", __func__, h->mode);
        return -1;
    }
}

int alsa_flush_with_fade_out(alsa_handle_t *h)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_flush(h->playback);
    }
#ifdef ALSA_SUPPORT_CAPTURE
    else if (is_capture_mode(h))
    {
        return alsa_capture_flush(h->capture);
    }
#endif
    else
    {
        TRACE(0, "%s mode %d not support", __func__, h->mode);
        return -1;
    }
}

int alsa_close(alsa_handle_t *h)
{
    int ret;
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        ret = alsa_playback_close(h->playback);
    }
#ifdef ALSA_SUPPORT_CAPTURE
    else if (is_capture_mode(h))
    {
        ret = alsa_capture_close(h->capture);
    }
#endif
    else
    {
        TRACE(0, "%s mode %d not support", __func__, h->mode);
        ret = -1;
    }

    audio_free(h);

    return ret;
}

int alsa_write(alsa_handle_t *h, uint8_t *buf, uint32_t length)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (!is_playback_mode(h))
    {
        TRACE(0, "%s mode %d not support", __func__, h->mode);
        return -1;
    }

    return alsa_playback_write(h->playback, buf, length);
}

int alsa_read(alsa_handle_t *h, uint8_t *buf, uint32_t length)
{
#ifdef ALSA_SUPPORT_CAPTURE
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        TRACE(0, "%s mode %d not support", __func__, h->mode);
        return -1;
    }

    return alsa_capture_read(h->capture, buf, length);
#else
    TRACE(0, "ALSA_SUPPORT_CAPTURE not enable. so not support %s", __func__);
    return 0;
#endif
}

int alsa_register_pcm_state_callback(alsa_handle_t *h, alsa_pcm_state_callback_t cb, void *arg)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_register_pcm_state_callback(h->playback, cb, arg);
    }
#ifdef ALSA_SUPPORT_CAPTURE
    else if (is_capture_mode(h))
    {
        return alsa_capture_register_pcm_state_callback(h->capture, cb, arg);
    }
#endif
    else
    {
        TRACE(0, "%s mode %d not support", __func__, h->mode);
        return -1;
    }
}

int alsa_pcm_sate_changed(alsa_stream_t stream, alsa_pcm_state_t pcm_state, uint8_t instance_id)
{
    void *user_handle = alsa_instance_get_user_arg_by_id(instance_id);
    TRACE(0, "%s stream %d, state %d , id %d, handle %p", __func__, stream, pcm_state, instance_id, user_handle);

    if (stream == ALSA_STREAM_PLAYBACK)
    {
        return alsa_playback_pmc_state_changed((alsa_playback_t *)user_handle, pcm_state);
    }
    else
    {
        TRACE(0, "%s current not support", __func__);
        return -1;
    }
}

void alsa_init(void)
{
    alsa_instance_init();

#ifdef ALSA_SUPPORT_CAPTURE
    alsa_deamon_init(alsa_playback_data_from_user,
                     alsa_capture_data_to_user,
                     alsa_pcm_sate_changed);
#else
    alsa_deamon_init(alsa_playback_data_from_user,
                     NULL,
                     alsa_pcm_sate_changed);
#endif

}

int alsa_set_user_type(alsa_handle_t *h, alsa_user_type_t type)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_set_user_type(h->playback, type);
    }
    else
    {
        TRACE(0, "%s current mode %d not support", __func__, h->mode);
        return -1;
    }
}

int alsa_set_maxdelaytime(alsa_handle_t *h, int delay_ms)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_set_maxdelaytime(h->playback, delay_ms);
    }
    else
    {
        TRACE(0, "%s current mode %d not support", __func__, h->mode);
        return -1;
    }
}

int alsa_set_trigger_level(alsa_handle_t *h, alsa_trigger_level_t level)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_set_trigger_level(h->playback, level);
    }
    else
    {
        TRACE(0, "%s current mode %d not support", __func__, h->mode);
        return -1;
    }
}

int alsa_set_threshold(alsa_handle_t *h, uint32_t threshold)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_set_threshold(h->playback, threshold);
    }
    else
    {
        TRACE(0, "%s current mode %d not support", __func__, h->mode);
        return -1;
    }
}

int alsa_sw_gain_bypass(alsa_handle_t *h, uint8_t bypass)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_sw_gain_bypass(h->playback, bypass);
    }
    else
    {
        TRACE(0, "%s current mode %d not support", __func__, h->mode);
        return -1;
    }
}

int alsa_sw_gain_fade_inout_bypass(alsa_handle_t *h, uint8_t bypass)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_sw_gain_fade_inout_bypass(h->playback, bypass);
    }
    else
    {
        TRACE(0, "%s current mode %d not support", __func__, h->mode);
        return -1;
    }
}

int alsa_sw_gain_fade_inout_coef(alsa_handle_t *h, float coef)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_sw_gain_fade_inout_coef(h->playback, coef);
    }
    else
    {
        TRACE(0, "%s current mode %d not support", __func__, h->mode);
        return -1;
    }
}
int alsa_sw_gain_pre_compensation_enable(alsa_handle_t *h, uint8_t enable, float compensation_db)
{
    if (!h)
    {
        TRACE(0, "%s h NULL error", __func__);
        return -1;
    }

    if (is_playback_mode(h))
    {
        return alsa_playback_sw_gain_pre_compensation_enable(h->playback, enable, compensation_db);
    }
    else
    {
        TRACE(0, "%s current mode %d not support", __func__, h->mode);
        return -1;
    }
}
