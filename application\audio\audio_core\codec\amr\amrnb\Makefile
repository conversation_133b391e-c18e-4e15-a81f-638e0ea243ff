# Makefile.in generated by automake 1.16.5 from Makefile.am.
# amrnb/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.






am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/opencore-amr
pkgincludedir = $(includedir)/opencore-amr
pkglibdir = $(libdir)/opencore-amr
pkglibexecdir = $(libexecdir)/opencore-amr
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-gnu
host_triplet = x86_64-pc-linux-gnu
#am__append_1 = -DPV_CPU_ARCH_VERSION=5 -DPV_COMPILER=1
am__append_2 = -x c -std=c99
am__append_3 = \
    $(DEC_SRC_DIR)/agc.cpp \
        $(DEC_SRC_DIR)/amrdecode.cpp \
        $(DEC_SRC_DIR)/a_refl.cpp \
        $(DEC_SRC_DIR)/b_cn_cod.cpp \
        $(DEC_SRC_DIR)/bgnscd.cpp \
        $(DEC_SRC_DIR)/c_g_aver.cpp \
        $(DEC_SRC_DIR)/d1035pf.cpp \
        $(DEC_SRC_DIR)/d2_11pf.cpp \
        $(DEC_SRC_DIR)/d2_9pf.cpp \
        $(DEC_SRC_DIR)/d3_14pf.cpp \
        $(DEC_SRC_DIR)/d4_17pf.cpp \
        $(DEC_SRC_DIR)/d8_31pf.cpp \
        $(DEC_SRC_DIR)/dec_amr.cpp \
        $(DEC_SRC_DIR)/dec_gain.cpp \
        $(DEC_SRC_DIR)/dec_input_format_tab.cpp \
        $(DEC_SRC_DIR)/dec_lag3.cpp \
        $(DEC_SRC_DIR)/dec_lag6.cpp \
        $(DEC_SRC_DIR)/d_gain_c.cpp \
        $(DEC_SRC_DIR)/d_gain_p.cpp \
        $(DEC_SRC_DIR)/d_plsf_3.cpp \
        $(DEC_SRC_DIR)/d_plsf_5.cpp \
        $(DEC_SRC_DIR)/d_plsf.cpp \
        $(DEC_SRC_DIR)/dtx_dec.cpp \
        $(DEC_SRC_DIR)/ec_gains.cpp \
        $(DEC_SRC_DIR)/ex_ctrl.cpp \
        $(DEC_SRC_DIR)/if2_to_ets.cpp \
        $(DEC_SRC_DIR)/int_lsf.cpp \
        $(DEC_SRC_DIR)/lsp_avg.cpp \
        $(DEC_SRC_DIR)/ph_disp.cpp \
        $(DEC_SRC_DIR)/post_pro.cpp \
        $(DEC_SRC_DIR)/preemph.cpp \
        $(DEC_SRC_DIR)/pstfilt.cpp \
        $(DEC_SRC_DIR)/qgain475_tab.cpp \
        $(DEC_SRC_DIR)/sp_dec.cpp \
        $(DEC_SRC_DIR)/wmf_to_ets.cpp

am__append_4 = interf_dec.h
#am__append_5 = -DDISABLE_AMRNB_DECODER
am__append_6 = \
    $(ENC_SRC_DIR)/amrencode.cpp \
        $(ENC_SRC_DIR)/autocorr.cpp \
        $(ENC_SRC_DIR)/c1035pf.cpp \
        $(ENC_SRC_DIR)/c2_11pf.cpp \
        $(ENC_SRC_DIR)/c2_9pf.cpp \
        $(ENC_SRC_DIR)/c3_14pf.cpp \
        $(ENC_SRC_DIR)/c4_17pf.cpp \
        $(ENC_SRC_DIR)/c8_31pf.cpp \
        $(ENC_SRC_DIR)/calc_cor.cpp \
        $(ENC_SRC_DIR)/calc_en.cpp \
        $(ENC_SRC_DIR)/cbsearch.cpp \
        $(ENC_SRC_DIR)/cl_ltp.cpp \
        $(ENC_SRC_DIR)/cod_amr.cpp \
        $(ENC_SRC_DIR)/convolve.cpp \
        $(ENC_SRC_DIR)/cor_h.cpp \
        $(ENC_SRC_DIR)/cor_h_x2.cpp \
        $(ENC_SRC_DIR)/cor_h_x.cpp \
        $(ENC_SRC_DIR)/corrwght_tab.cpp \
        $(ENC_SRC_DIR)/div_32.cpp \
        $(ENC_SRC_DIR)/dtx_enc.cpp \
        $(ENC_SRC_DIR)/enc_lag3.cpp \
        $(ENC_SRC_DIR)/enc_lag6.cpp \
        $(ENC_SRC_DIR)/enc_output_format_tab.cpp \
        $(ENC_SRC_DIR)/ets_to_if2.cpp \
        $(ENC_SRC_DIR)/ets_to_wmf.cpp \
        $(ENC_SRC_DIR)/g_adapt.cpp \
        $(ENC_SRC_DIR)/gain_q.cpp \
        $(ENC_SRC_DIR)/g_code.cpp \
        $(ENC_SRC_DIR)/g_pitch.cpp \
        $(ENC_SRC_DIR)/hp_max.cpp \
        $(ENC_SRC_DIR)/inter_36.cpp \
        $(ENC_SRC_DIR)/inter_36_tab.cpp \
        $(ENC_SRC_DIR)/l_abs.cpp \
        $(ENC_SRC_DIR)/lag_wind.cpp \
        $(ENC_SRC_DIR)/lag_wind_tab.cpp \
        $(ENC_SRC_DIR)/l_comp.cpp \
        $(ENC_SRC_DIR)/levinson.cpp \
        $(ENC_SRC_DIR)/l_extract.cpp \
        $(ENC_SRC_DIR)/lflg_upd.cpp \
        $(ENC_SRC_DIR)/l_negate.cpp \
        $(ENC_SRC_DIR)/lpc.cpp \
        $(ENC_SRC_DIR)/ol_ltp.cpp \
        $(ENC_SRC_DIR)/pitch_fr.cpp \
        $(ENC_SRC_DIR)/pitch_ol.cpp \
        $(ENC_SRC_DIR)/p_ol_wgh.cpp \
        $(ENC_SRC_DIR)/pre_big.cpp \
        $(ENC_SRC_DIR)/pre_proc.cpp \
        $(ENC_SRC_DIR)/prm2bits.cpp \
        $(ENC_SRC_DIR)/qgain475.cpp \
        $(ENC_SRC_DIR)/qgain795.cpp \
        $(ENC_SRC_DIR)/q_gain_c.cpp \
        $(ENC_SRC_DIR)/q_gain_p.cpp \
        $(ENC_SRC_DIR)/qua_gain.cpp \
        $(ENC_SRC_DIR)/s10_8pf.cpp \
        $(ENC_SRC_DIR)/set_sign.cpp \
        $(ENC_SRC_DIR)/sid_sync.cpp \
        $(ENC_SRC_DIR)/sp_enc.cpp \
        $(ENC_SRC_DIR)/spreproc.cpp \
        $(ENC_SRC_DIR)/spstproc.cpp \
        $(ENC_SRC_DIR)/ton_stab.cpp \
        $(ENC_SRC_DIR)/vad1.cpp

am__append_7 = interf_enc.h
#am__append_8 = -DDISABLE_AMRNB_ENCODER
subdir = amrnb
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/libtool.m4 \
	$(top_srcdir)/m4/ltoptions.m4 $(top_srcdir)/m4/ltsugar.m4 \
	$(top_srcdir)/m4/ltversion.m4 $(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__amrnbinclude_HEADERS_DIST) \
	$(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_CLEAN_FILES = opencore-amrnb.pc
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libdir)" "$(DESTDIR)$(pkgconfigdir)" \
	"$(DESTDIR)$(amrnbincludedir)"
LTLIBRARIES = $(lib_LTLIBRARIES)
libopencore_amrnb_la_LIBADD =
am__libopencore_amrnb_la_SOURCES_DIST = wrapper.cpp \
	$(DEC_SRC_DIR)/agc.cpp $(DEC_SRC_DIR)/amrdecode.cpp \
	$(DEC_SRC_DIR)/a_refl.cpp $(DEC_SRC_DIR)/b_cn_cod.cpp \
	$(DEC_SRC_DIR)/bgnscd.cpp $(DEC_SRC_DIR)/c_g_aver.cpp \
	$(DEC_SRC_DIR)/d1035pf.cpp $(DEC_SRC_DIR)/d2_11pf.cpp \
	$(DEC_SRC_DIR)/d2_9pf.cpp $(DEC_SRC_DIR)/d3_14pf.cpp \
	$(DEC_SRC_DIR)/d4_17pf.cpp $(DEC_SRC_DIR)/d8_31pf.cpp \
	$(DEC_SRC_DIR)/dec_amr.cpp $(DEC_SRC_DIR)/dec_gain.cpp \
	$(DEC_SRC_DIR)/dec_input_format_tab.cpp \
	$(DEC_SRC_DIR)/dec_lag3.cpp $(DEC_SRC_DIR)/dec_lag6.cpp \
	$(DEC_SRC_DIR)/d_gain_c.cpp $(DEC_SRC_DIR)/d_gain_p.cpp \
	$(DEC_SRC_DIR)/d_plsf_3.cpp $(DEC_SRC_DIR)/d_plsf_5.cpp \
	$(DEC_SRC_DIR)/d_plsf.cpp $(DEC_SRC_DIR)/dtx_dec.cpp \
	$(DEC_SRC_DIR)/ec_gains.cpp $(DEC_SRC_DIR)/ex_ctrl.cpp \
	$(DEC_SRC_DIR)/if2_to_ets.cpp $(DEC_SRC_DIR)/int_lsf.cpp \
	$(DEC_SRC_DIR)/lsp_avg.cpp $(DEC_SRC_DIR)/ph_disp.cpp \
	$(DEC_SRC_DIR)/post_pro.cpp $(DEC_SRC_DIR)/preemph.cpp \
	$(DEC_SRC_DIR)/pstfilt.cpp $(DEC_SRC_DIR)/qgain475_tab.cpp \
	$(DEC_SRC_DIR)/sp_dec.cpp $(DEC_SRC_DIR)/wmf_to_ets.cpp \
	$(ENC_SRC_DIR)/amrencode.cpp $(ENC_SRC_DIR)/autocorr.cpp \
	$(ENC_SRC_DIR)/c1035pf.cpp $(ENC_SRC_DIR)/c2_11pf.cpp \
	$(ENC_SRC_DIR)/c2_9pf.cpp $(ENC_SRC_DIR)/c3_14pf.cpp \
	$(ENC_SRC_DIR)/c4_17pf.cpp $(ENC_SRC_DIR)/c8_31pf.cpp \
	$(ENC_SRC_DIR)/calc_cor.cpp $(ENC_SRC_DIR)/calc_en.cpp \
	$(ENC_SRC_DIR)/cbsearch.cpp $(ENC_SRC_DIR)/cl_ltp.cpp \
	$(ENC_SRC_DIR)/cod_amr.cpp $(ENC_SRC_DIR)/convolve.cpp \
	$(ENC_SRC_DIR)/cor_h.cpp $(ENC_SRC_DIR)/cor_h_x2.cpp \
	$(ENC_SRC_DIR)/cor_h_x.cpp $(ENC_SRC_DIR)/corrwght_tab.cpp \
	$(ENC_SRC_DIR)/div_32.cpp $(ENC_SRC_DIR)/dtx_enc.cpp \
	$(ENC_SRC_DIR)/enc_lag3.cpp $(ENC_SRC_DIR)/enc_lag6.cpp \
	$(ENC_SRC_DIR)/enc_output_format_tab.cpp \
	$(ENC_SRC_DIR)/ets_to_if2.cpp $(ENC_SRC_DIR)/ets_to_wmf.cpp \
	$(ENC_SRC_DIR)/g_adapt.cpp $(ENC_SRC_DIR)/gain_q.cpp \
	$(ENC_SRC_DIR)/g_code.cpp $(ENC_SRC_DIR)/g_pitch.cpp \
	$(ENC_SRC_DIR)/hp_max.cpp $(ENC_SRC_DIR)/inter_36.cpp \
	$(ENC_SRC_DIR)/inter_36_tab.cpp $(ENC_SRC_DIR)/l_abs.cpp \
	$(ENC_SRC_DIR)/lag_wind.cpp $(ENC_SRC_DIR)/lag_wind_tab.cpp \
	$(ENC_SRC_DIR)/l_comp.cpp $(ENC_SRC_DIR)/levinson.cpp \
	$(ENC_SRC_DIR)/l_extract.cpp $(ENC_SRC_DIR)/lflg_upd.cpp \
	$(ENC_SRC_DIR)/l_negate.cpp $(ENC_SRC_DIR)/lpc.cpp \
	$(ENC_SRC_DIR)/ol_ltp.cpp $(ENC_SRC_DIR)/pitch_fr.cpp \
	$(ENC_SRC_DIR)/pitch_ol.cpp $(ENC_SRC_DIR)/p_ol_wgh.cpp \
	$(ENC_SRC_DIR)/pre_big.cpp $(ENC_SRC_DIR)/pre_proc.cpp \
	$(ENC_SRC_DIR)/prm2bits.cpp $(ENC_SRC_DIR)/qgain475.cpp \
	$(ENC_SRC_DIR)/qgain795.cpp $(ENC_SRC_DIR)/q_gain_c.cpp \
	$(ENC_SRC_DIR)/q_gain_p.cpp $(ENC_SRC_DIR)/qua_gain.cpp \
	$(ENC_SRC_DIR)/s10_8pf.cpp $(ENC_SRC_DIR)/set_sign.cpp \
	$(ENC_SRC_DIR)/sid_sync.cpp $(ENC_SRC_DIR)/sp_enc.cpp \
	$(ENC_SRC_DIR)/spreproc.cpp $(ENC_SRC_DIR)/spstproc.cpp \
	$(ENC_SRC_DIR)/ton_stab.cpp $(ENC_SRC_DIR)/vad1.cpp \
	$(COMMON_SRC_DIR)/add.cpp $(COMMON_SRC_DIR)/az_lsp.cpp \
	$(COMMON_SRC_DIR)/bitno_tab.cpp \
	$(COMMON_SRC_DIR)/bitreorder_tab.cpp \
	$(COMMON_SRC_DIR)/c2_9pf_tab.cpp $(COMMON_SRC_DIR)/div_s.cpp \
	$(COMMON_SRC_DIR)/extract_h.cpp \
	$(COMMON_SRC_DIR)/extract_l.cpp \
	$(COMMON_SRC_DIR)/gains_tbl.cpp $(COMMON_SRC_DIR)/gc_pred.cpp \
	$(COMMON_SRC_DIR)/get_const_tbls.cpp \
	$(COMMON_SRC_DIR)/gmed_n.cpp $(COMMON_SRC_DIR)/gray_tbl.cpp \
	$(COMMON_SRC_DIR)/grid_tbl.cpp $(COMMON_SRC_DIR)/int_lpc.cpp \
	$(COMMON_SRC_DIR)/inv_sqrt.cpp \
	$(COMMON_SRC_DIR)/inv_sqrt_tbl.cpp \
	$(COMMON_SRC_DIR)/l_deposit_h.cpp \
	$(COMMON_SRC_DIR)/l_deposit_l.cpp $(COMMON_SRC_DIR)/log2.cpp \
	$(COMMON_SRC_DIR)/log2_norm.cpp $(COMMON_SRC_DIR)/log2_tbl.cpp \
	$(COMMON_SRC_DIR)/lsfwt.cpp $(COMMON_SRC_DIR)/l_shr_r.cpp \
	$(COMMON_SRC_DIR)/lsp_az.cpp $(COMMON_SRC_DIR)/lsp.cpp \
	$(COMMON_SRC_DIR)/lsp_lsf.cpp \
	$(COMMON_SRC_DIR)/lsp_lsf_tbl.cpp \
	$(COMMON_SRC_DIR)/lsp_tab.cpp $(COMMON_SRC_DIR)/mult_r.cpp \
	$(COMMON_SRC_DIR)/negate.cpp $(COMMON_SRC_DIR)/norm_l.cpp \
	$(COMMON_SRC_DIR)/norm_s.cpp \
	$(COMMON_SRC_DIR)/overflow_tbl.cpp \
	$(COMMON_SRC_DIR)/ph_disp_tab.cpp $(COMMON_SRC_DIR)/pow2.cpp \
	$(COMMON_SRC_DIR)/pow2_tbl.cpp $(COMMON_SRC_DIR)/pred_lt.cpp \
	$(COMMON_SRC_DIR)/q_plsf_3.cpp \
	$(COMMON_SRC_DIR)/q_plsf_3_tbl.cpp \
	$(COMMON_SRC_DIR)/q_plsf_5.cpp \
	$(COMMON_SRC_DIR)/q_plsf_5_tbl.cpp \
	$(COMMON_SRC_DIR)/q_plsf.cpp \
	$(COMMON_SRC_DIR)/qua_gain_tbl.cpp \
	$(COMMON_SRC_DIR)/reorder.cpp $(COMMON_SRC_DIR)/residu.cpp \
	$(COMMON_SRC_DIR)/round.cpp $(COMMON_SRC_DIR)/set_zero.cpp \
	$(COMMON_SRC_DIR)/shr.cpp $(COMMON_SRC_DIR)/shr_r.cpp \
	$(COMMON_SRC_DIR)/sqrt_l.cpp $(COMMON_SRC_DIR)/sqrt_l_tbl.cpp \
	$(COMMON_SRC_DIR)/sub.cpp $(COMMON_SRC_DIR)/syn_filt.cpp \
	$(COMMON_SRC_DIR)/weight_a.cpp \
	$(COMMON_SRC_DIR)/window_tab.cpp
am__objects_1 = agc.lo amrdecode.lo a_refl.lo \
	b_cn_cod.lo bgnscd.lo c_g_aver.lo \
	d1035pf.lo d2_11pf.lo d2_9pf.lo d3_14pf.lo \
	d4_17pf.lo d8_31pf.lo dec_amr.lo \
	dec_gain.lo dec_input_format_tab.lo \
	dec_lag3.lo dec_lag6.lo d_gain_c.lo \
	d_gain_p.lo d_plsf_3.lo d_plsf_5.lo \
	d_plsf.lo dtx_dec.lo ec_gains.lo \
	ex_ctrl.lo if2_to_ets.lo int_lsf.lo \
	lsp_avg.lo ph_disp.lo post_pro.lo \
	preemph.lo pstfilt.lo qgain475_tab.lo \
	sp_dec.lo wmf_to_ets.lo
am__objects_2 = amrencode.lo autocorr.lo \
	c1035pf.lo c2_11pf.lo c2_9pf.lo c3_14pf.lo \
	c4_17pf.lo c8_31pf.lo calc_cor.lo \
	calc_en.lo cbsearch.lo cl_ltp.lo \
	cod_amr.lo convolve.lo cor_h.lo \
	cor_h_x2.lo cor_h_x.lo corrwght_tab.lo \
	div_32.lo dtx_enc.lo enc_lag3.lo \
	enc_lag6.lo enc_output_format_tab.lo \
	ets_to_if2.lo ets_to_wmf.lo g_adapt.lo \
	gain_q.lo g_code.lo g_pitch.lo hp_max.lo \
	inter_36.lo inter_36_tab.lo l_abs.lo \
	lag_wind.lo lag_wind_tab.lo l_comp.lo \
	levinson.lo l_extract.lo lflg_upd.lo \
	l_negate.lo lpc.lo ol_ltp.lo pitch_fr.lo \
	pitch_ol.lo p_ol_wgh.lo pre_big.lo \
	pre_proc.lo prm2bits.lo qgain475.lo \
	qgain795.lo q_gain_c.lo q_gain_p.lo \
	qua_gain.lo s10_8pf.lo set_sign.lo \
	sid_sync.lo sp_enc.lo spreproc.lo \
	spstproc.lo ton_stab.lo vad1.lo
am_libopencore_amrnb_la_OBJECTS = wrapper.lo $(am__objects_1) \
	$(am__objects_2) add.lo az_lsp.lo bitno_tab.lo \
	bitreorder_tab.lo c2_9pf_tab.lo div_s.lo extract_h.lo \
	extract_l.lo gains_tbl.lo gc_pred.lo get_const_tbls.lo \
	gmed_n.lo gray_tbl.lo grid_tbl.lo int_lpc.lo inv_sqrt.lo \
	inv_sqrt_tbl.lo l_deposit_h.lo l_deposit_l.lo log2.lo \
	log2_norm.lo log2_tbl.lo lsfwt.lo l_shr_r.lo lsp_az.lo lsp.lo \
	lsp_lsf.lo lsp_lsf_tbl.lo lsp_tab.lo mult_r.lo negate.lo \
	norm_l.lo norm_s.lo overflow_tbl.lo ph_disp_tab.lo pow2.lo \
	pow2_tbl.lo pred_lt.lo q_plsf_3.lo q_plsf_3_tbl.lo q_plsf_5.lo \
	q_plsf_5_tbl.lo q_plsf.lo qua_gain_tbl.lo reorder.lo residu.lo \
	round.lo set_zero.lo shr.lo shr_r.lo sqrt_l.lo sqrt_l_tbl.lo \
	sub.lo syn_filt.lo weight_a.lo window_tab.lo
libopencore_amrnb_la_OBJECTS = $(am_libopencore_amrnb_la_OBJECTS)
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/a_refl.Plo ./$(DEPDIR)/add.Plo \
	./$(DEPDIR)/agc.Plo ./$(DEPDIR)/amrdecode.Plo \
	./$(DEPDIR)/amrencode.Plo ./$(DEPDIR)/autocorr.Plo \
	./$(DEPDIR)/az_lsp.Plo ./$(DEPDIR)/b_cn_cod.Plo \
	./$(DEPDIR)/bgnscd.Plo ./$(DEPDIR)/bitno_tab.Plo \
	./$(DEPDIR)/bitreorder_tab.Plo ./$(DEPDIR)/c1035pf.Plo \
	./$(DEPDIR)/c2_11pf.Plo ./$(DEPDIR)/c2_9pf.Plo \
	./$(DEPDIR)/c2_9pf_tab.Plo ./$(DEPDIR)/c3_14pf.Plo \
	./$(DEPDIR)/c4_17pf.Plo ./$(DEPDIR)/c8_31pf.Plo \
	./$(DEPDIR)/c_g_aver.Plo ./$(DEPDIR)/calc_cor.Plo \
	./$(DEPDIR)/calc_en.Plo ./$(DEPDIR)/cbsearch.Plo \
	./$(DEPDIR)/cl_ltp.Plo ./$(DEPDIR)/cod_amr.Plo \
	./$(DEPDIR)/convolve.Plo ./$(DEPDIR)/cor_h.Plo \
	./$(DEPDIR)/cor_h_x.Plo ./$(DEPDIR)/cor_h_x2.Plo \
	./$(DEPDIR)/corrwght_tab.Plo ./$(DEPDIR)/d1035pf.Plo \
	./$(DEPDIR)/d2_11pf.Plo ./$(DEPDIR)/d2_9pf.Plo \
	./$(DEPDIR)/d3_14pf.Plo ./$(DEPDIR)/d4_17pf.Plo \
	./$(DEPDIR)/d8_31pf.Plo ./$(DEPDIR)/d_gain_c.Plo \
	./$(DEPDIR)/d_gain_p.Plo ./$(DEPDIR)/d_plsf.Plo \
	./$(DEPDIR)/d_plsf_3.Plo ./$(DEPDIR)/d_plsf_5.Plo \
	./$(DEPDIR)/dec_amr.Plo ./$(DEPDIR)/dec_gain.Plo \
	./$(DEPDIR)/dec_input_format_tab.Plo ./$(DEPDIR)/dec_lag3.Plo \
	./$(DEPDIR)/dec_lag6.Plo ./$(DEPDIR)/div_32.Plo \
	./$(DEPDIR)/div_s.Plo ./$(DEPDIR)/dtx_dec.Plo \
	./$(DEPDIR)/dtx_enc.Plo ./$(DEPDIR)/dummy.Plo \
	./$(DEPDIR)/ec_gains.Plo ./$(DEPDIR)/enc_lag3.Plo \
	./$(DEPDIR)/enc_lag6.Plo ./$(DEPDIR)/enc_output_format_tab.Plo \
	./$(DEPDIR)/ets_to_if2.Plo ./$(DEPDIR)/ets_to_wmf.Plo \
	./$(DEPDIR)/ex_ctrl.Plo ./$(DEPDIR)/extract_h.Plo \
	./$(DEPDIR)/extract_l.Plo ./$(DEPDIR)/g_adapt.Plo \
	./$(DEPDIR)/g_code.Plo ./$(DEPDIR)/g_pitch.Plo \
	./$(DEPDIR)/gain_q.Plo ./$(DEPDIR)/gains_tbl.Plo \
	./$(DEPDIR)/gc_pred.Plo ./$(DEPDIR)/get_const_tbls.Plo \
	./$(DEPDIR)/gmed_n.Plo ./$(DEPDIR)/gray_tbl.Plo \
	./$(DEPDIR)/grid_tbl.Plo ./$(DEPDIR)/hp_max.Plo \
	./$(DEPDIR)/if2_to_ets.Plo ./$(DEPDIR)/int_lpc.Plo \
	./$(DEPDIR)/int_lsf.Plo ./$(DEPDIR)/inter_36.Plo \
	./$(DEPDIR)/inter_36_tab.Plo ./$(DEPDIR)/inv_sqrt.Plo \
	./$(DEPDIR)/inv_sqrt_tbl.Plo ./$(DEPDIR)/l_abs.Plo \
	./$(DEPDIR)/l_comp.Plo ./$(DEPDIR)/l_deposit_h.Plo \
	./$(DEPDIR)/l_deposit_l.Plo ./$(DEPDIR)/l_extract.Plo \
	./$(DEPDIR)/l_negate.Plo ./$(DEPDIR)/l_shr_r.Plo \
	./$(DEPDIR)/lag_wind.Plo ./$(DEPDIR)/lag_wind_tab.Plo \
	./$(DEPDIR)/levinson.Plo ./$(DEPDIR)/lflg_upd.Plo \
	./$(DEPDIR)/log2.Plo ./$(DEPDIR)/log2_norm.Plo \
	./$(DEPDIR)/log2_tbl.Plo ./$(DEPDIR)/lpc.Plo \
	./$(DEPDIR)/lsfwt.Plo ./$(DEPDIR)/lsp.Plo \
	./$(DEPDIR)/lsp_avg.Plo ./$(DEPDIR)/lsp_az.Plo \
	./$(DEPDIR)/lsp_lsf.Plo ./$(DEPDIR)/lsp_lsf_tbl.Plo \
	./$(DEPDIR)/lsp_tab.Plo ./$(DEPDIR)/mult_r.Plo \
	./$(DEPDIR)/negate.Plo ./$(DEPDIR)/norm_l.Plo \
	./$(DEPDIR)/norm_s.Plo ./$(DEPDIR)/ol_ltp.Plo \
	./$(DEPDIR)/overflow_tbl.Plo ./$(DEPDIR)/p_ol_wgh.Plo \
	./$(DEPDIR)/ph_disp.Plo ./$(DEPDIR)/ph_disp_tab.Plo \
	./$(DEPDIR)/pitch_fr.Plo ./$(DEPDIR)/pitch_ol.Plo \
	./$(DEPDIR)/post_pro.Plo ./$(DEPDIR)/pow2.Plo \
	./$(DEPDIR)/pow2_tbl.Plo ./$(DEPDIR)/pre_big.Plo \
	./$(DEPDIR)/pre_proc.Plo ./$(DEPDIR)/pred_lt.Plo \
	./$(DEPDIR)/preemph.Plo ./$(DEPDIR)/prm2bits.Plo \
	./$(DEPDIR)/pstfilt.Plo ./$(DEPDIR)/q_gain_c.Plo \
	./$(DEPDIR)/q_gain_p.Plo ./$(DEPDIR)/q_plsf.Plo \
	./$(DEPDIR)/q_plsf_3.Plo ./$(DEPDIR)/q_plsf_3_tbl.Plo \
	./$(DEPDIR)/q_plsf_5.Plo ./$(DEPDIR)/q_plsf_5_tbl.Plo \
	./$(DEPDIR)/qgain475.Plo ./$(DEPDIR)/qgain475_tab.Plo \
	./$(DEPDIR)/qgain795.Plo ./$(DEPDIR)/qua_gain.Plo \
	./$(DEPDIR)/qua_gain_tbl.Plo ./$(DEPDIR)/reorder.Plo \
	./$(DEPDIR)/residu.Plo ./$(DEPDIR)/round.Plo \
	./$(DEPDIR)/s10_8pf.Plo ./$(DEPDIR)/set_sign.Plo \
	./$(DEPDIR)/set_zero.Plo ./$(DEPDIR)/shr.Plo \
	./$(DEPDIR)/shr_r.Plo ./$(DEPDIR)/sid_sync.Plo \
	./$(DEPDIR)/sp_dec.Plo ./$(DEPDIR)/sp_enc.Plo \
	./$(DEPDIR)/spreproc.Plo ./$(DEPDIR)/spstproc.Plo \
	./$(DEPDIR)/sqrt_l.Plo ./$(DEPDIR)/sqrt_l_tbl.Plo \
	./$(DEPDIR)/sub.Plo ./$(DEPDIR)/syn_filt.Plo \
	./$(DEPDIR)/ton_stab.Plo ./$(DEPDIR)/vad1.Plo \
	./$(DEPDIR)/weight_a.Plo ./$(DEPDIR)/window_tab.Plo \
	./$(DEPDIR)/wmf_to_ets.Plo ./$(DEPDIR)/wrapper.Plo
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
AM_V_lt = $(am__v_lt_$(V))
am__v_lt_ = $(am__v_lt_$(AM_DEFAULT_VERBOSITY))
am__v_lt_0 = --silent
am__v_lt_1 = 
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_$(V))
am__v_CC_ = $(am__v_CC_$(AM_DEFAULT_VERBOSITY))
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_$(V))
am__v_CCLD_ = $(am__v_CCLD_$(AM_DEFAULT_VERBOSITY))
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
CXXCOMPILE = $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS)
LTCXXCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CXXFLAGS) $(CXXFLAGS)
AM_V_CXX = $(am__v_CXX_$(V))
am__v_CXX_ = $(am__v_CXX_$(AM_DEFAULT_VERBOSITY))
am__v_CXX_0 = @echo "  CXX     " $@;
am__v_CXX_1 = 
CXXLD = $(CXX)
CXXLINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CXXLD) $(AM_CXXFLAGS) \
	$(CXXFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CXXLD = $(am__v_CXXLD_$(V))
am__v_CXXLD_ = $(am__v_CXXLD_$(AM_DEFAULT_VERBOSITY))
am__v_CXXLD_0 = @echo "  CXXLD   " $@;
am__v_CXXLD_1 = 
SOURCES = $(libopencore_amrnb_la_SOURCES) \
	$(nodist_EXTRA_libopencore_amrnb_la_SOURCES)
DIST_SOURCES = $(am__libopencore_amrnb_la_SOURCES_DIST)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
DATA = $(pkgconfig_DATA)
am__amrnbinclude_HEADERS_DIST = interf_dec.h interf_enc.h
HEADERS = $(amrnbinclude_HEADERS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
am__DIST_COMMON = $(srcdir)/Makefile.in $(srcdir)/opencore-amrnb.pc.in \
	$(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = ${SHELL} '/home/<USER>/work/amrcodec/opencore-amr-0.1.6/missing' aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AR = ar
AUTOCONF = ${SHELL} '/home/<USER>/work/amrcodec/opencore-amr-0.1.6/missing' autoconf
AUTOHEADER = ${SHELL} '/home/<USER>/work/amrcodec/opencore-amr-0.1.6/missing' autoheader
AUTOMAKE = ${SHELL} '/home/<USER>/work/amrcodec/opencore-amr-0.1.6/missing' automake-1.16
AWK = gawk
CC = gcc
CCDEPMODE = depmode=gcc3
CFLAGS = -g -O2
CPPFLAGS = 
CSCOPE = cscope
CTAGS = ctags
CXX = g++
CXXCPP = g++ -E
CXXDEPMODE = depmode=gcc3
CXXFLAGS = -g -O2
CYGPATH_W = echo
DEFS = -DPACKAGE_NAME=\"opencore-amr\" -DPACKAGE_TARNAME=\"opencore-amr\" -DPACKAGE_VERSION=\"0.1.6\" -DPACKAGE_STRING=\"opencore-amr\ 0.1.6\" -DPACKAGE_BUGREPORT=\"http://sourceforge.net/projects/opencore-amr/\" -DPACKAGE_URL=\"\" -DPACKAGE=\"opencore-amr\" -DVERSION=\"0.1.6\" -DHAVE_STDIO_H=1 -DHAVE_STDLIB_H=1 -DHAVE_STRING_H=1 -DHAVE_INTTYPES_H=1 -DHAVE_STDINT_H=1 -DHAVE_STRINGS_H=1 -DHAVE_SYS_STAT_H=1 -DHAVE_SYS_TYPES_H=1 -DHAVE_UNISTD_H=1 -DSTDC_HEADERS=1 -DHAVE_DLFCN_H=1 -DLT_OBJDIR=\".libs/\" -DHAVE_LIBM=1 -DHAVE_STDINT_H=1 -DHAVE_STDLIB_H=1 -DHAVE_STRING_H=1 -DHAVE__BOOL=1 -DHAVE_STDBOOL_H=1 -DHAVE_MEMSET=1
DEPDIR = .deps
DLLTOOL = false
DSYMUTIL = 
DUMPBIN = 
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /bin/grep -E
ETAGS = etags
EXEEXT = 
FGREP = /bin/grep -F
GREP = /bin/grep
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
LD = /usr/bin/ld -m elf_x86_64
LDFLAGS = 
LIBOBJS = 
LIBS = -lm 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LIPO = 
LN_S = ln -s
LTLIBOBJS = 
LT_SYS_LIBRARY_PATH = 
MAINT = #
MAKEINFO = ${SHELL} '/home/<USER>/work/amrcodec/opencore-amr-0.1.6/missing' makeinfo
MANIFEST_TOOL = :
MKDIR_P = /bin/mkdir -p
NM = /usr/bin/nm -B
NMEDIT = 
OBJDUMP = objdump
OBJEXT = o
OPENCORE_AMRNB_VERSION = 0:5:0
OPENCORE_AMRWB_VERSION = 0:5:0
OTOOL = 
OTOOL64 = 
PACKAGE = opencore-amr
PACKAGE_BUGREPORT = http://sourceforge.net/projects/opencore-amr/
PACKAGE_NAME = opencore-amr
PACKAGE_STRING = opencore-amr 0.1.6
PACKAGE_TARNAME = opencore-amr
PACKAGE_URL = 
PACKAGE_VERSION = 0.1.6
PATH_SEPARATOR = :
RANLIB = ranlib
SED = /bin/sed
SET_MAKE = 
SHELL = /bin/sh
STRIP = strip
VERSION = 0.1.6
abs_builddir = /home/<USER>/work/amrcodec/opencore-amr-0.1.6/amrnb
abs_srcdir = /home/<USER>/work/amrcodec/opencore-amr-0.1.6/amrnb
abs_top_builddir = /home/<USER>/work/amrcodec/opencore-amr-0.1.6
abs_top_srcdir = /home/<USER>/work/amrcodec/opencore-amr-0.1.6
ac_ct_AR = ar
ac_ct_CC = gcc
ac_ct_CXX = g++
ac_ct_DUMPBIN = 
am__include = include
am__leading_dot = .
am__quote = 
am__tar = tar --format=ustar -chf - "$$tardir"
am__untar = tar -xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-gnu
build_alias = 
build_cpu = x86_64
build_os = linux-gnu
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = ${prefix}
host = x86_64-pc-linux-gnu
host_alias = 
host_cpu = x86_64
host_os = linux-gnu
host_vendor = pc
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /home/<USER>/work/amrcodec/opencore-amr-0.1.6/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = ${prefix}/var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /home/<USER>/work/amrcodec/opencore-amr-0.1.6/dist
program_transform_name = s,x,x,
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = .
sysconfdir = ${prefix}/etc
target_alias = 
top_build_prefix = ../
top_builddir = ..
top_srcdir = ..

# Just set OC_BASE to the opencore root, or set AMR_BASE directly to
# a detached gsm_amr directory
OC_BASE = $(top_srcdir)/opencore
AMR_BASE = $(OC_BASE)/codecs_v2/audio/gsm_amr
DEC_DIR = $(AMR_BASE)/amr_nb/dec
ENC_DIR = $(AMR_BASE)/amr_nb/enc
COMMON_DIR = $(AMR_BASE)/amr_nb/common
DEC_SRC_DIR = $(DEC_DIR)/src
ENC_SRC_DIR = $(ENC_DIR)/src
COMMON_SRC_DIR = $(COMMON_DIR)/src
OSCL = $(top_srcdir)/oscl
AM_CFLAGS = -I$(OSCL) -I$(DEC_SRC_DIR) -I$(COMMON_DIR)/include \
	-I$(DEC_DIR)/include -I$(AMR_BASE)/common/dec/include \
	-I$(ENC_SRC_DIR) $(am__append_1) $(am__append_2) \
	$(am__append_5) $(am__append_8)
#libopencore_amrnb_la_LINK = $(CXXLINK) $(libopencore_amrnb_la_LDFLAGS)
libopencore_amrnb_la_LINK = $(LINK) $(libopencore_amrnb_la_LDFLAGS)
nodist_EXTRA_libopencore_amrnb_la_SOURCES = dummy.c
AM_CXXFLAGS = $(AM_CFLAGS)
amrnbincludedir = $(includedir)/opencore-amrnb
amrnbinclude_HEADERS = $(am__append_4) $(am__append_7)
pkgconfigdir = $(libdir)/pkgconfig
pkgconfig_DATA = opencore-amrnb.pc
lib_LTLIBRARIES = libopencore-amrnb.la
libopencore_amrnb_la_LDFLAGS = -version-info 0:5:0 -no-undefined -export-symbols $(top_srcdir)/amrnb/opencore-amrnb.sym
EXTRA_DIST = $(top_srcdir)/amrnb/opencore-amrnb.sym

# Our sources to include. There are certain sources we exclude and they are
# $(DEC_SRC_DIR)/decoder_gsm_amr.cpp
# $(DEC_SRC_DIR)/pvgsmamrdecoder.cpp
# $(ENC_SRC_DIR)/gsmamr_encoder_wrapper.cpp
# $(COMMON_SRC_DIR)/bits2prm.cpp
# $(COMMON_SRC_DIR)/copy.cpp
# $(COMMON_SRC_DIR)/div_32.cpp
# $(COMMON_SRC_DIR)/l_abs.cpp
# $(COMMON_SRC_DIR)/r_fft.cpp
# $(COMMON_SRC_DIR)/vad1.cpp
# $(COMMON_SRC_DIR)/vad2.cpp
libopencore_amrnb_la_SOURCES = wrapper.cpp $(am__append_3) \
	$(am__append_6) $(COMMON_SRC_DIR)/add.cpp \
	$(COMMON_SRC_DIR)/az_lsp.cpp $(COMMON_SRC_DIR)/bitno_tab.cpp \
	$(COMMON_SRC_DIR)/bitreorder_tab.cpp \
	$(COMMON_SRC_DIR)/c2_9pf_tab.cpp $(COMMON_SRC_DIR)/div_s.cpp \
	$(COMMON_SRC_DIR)/extract_h.cpp \
	$(COMMON_SRC_DIR)/extract_l.cpp \
	$(COMMON_SRC_DIR)/gains_tbl.cpp $(COMMON_SRC_DIR)/gc_pred.cpp \
	$(COMMON_SRC_DIR)/get_const_tbls.cpp \
	$(COMMON_SRC_DIR)/gmed_n.cpp $(COMMON_SRC_DIR)/gray_tbl.cpp \
	$(COMMON_SRC_DIR)/grid_tbl.cpp $(COMMON_SRC_DIR)/int_lpc.cpp \
	$(COMMON_SRC_DIR)/inv_sqrt.cpp \
	$(COMMON_SRC_DIR)/inv_sqrt_tbl.cpp \
	$(COMMON_SRC_DIR)/l_deposit_h.cpp \
	$(COMMON_SRC_DIR)/l_deposit_l.cpp $(COMMON_SRC_DIR)/log2.cpp \
	$(COMMON_SRC_DIR)/log2_norm.cpp $(COMMON_SRC_DIR)/log2_tbl.cpp \
	$(COMMON_SRC_DIR)/lsfwt.cpp $(COMMON_SRC_DIR)/l_shr_r.cpp \
	$(COMMON_SRC_DIR)/lsp_az.cpp $(COMMON_SRC_DIR)/lsp.cpp \
	$(COMMON_SRC_DIR)/lsp_lsf.cpp \
	$(COMMON_SRC_DIR)/lsp_lsf_tbl.cpp \
	$(COMMON_SRC_DIR)/lsp_tab.cpp $(COMMON_SRC_DIR)/mult_r.cpp \
	$(COMMON_SRC_DIR)/negate.cpp $(COMMON_SRC_DIR)/norm_l.cpp \
	$(COMMON_SRC_DIR)/norm_s.cpp \
	$(COMMON_SRC_DIR)/overflow_tbl.cpp \
	$(COMMON_SRC_DIR)/ph_disp_tab.cpp $(COMMON_SRC_DIR)/pow2.cpp \
	$(COMMON_SRC_DIR)/pow2_tbl.cpp $(COMMON_SRC_DIR)/pred_lt.cpp \
	$(COMMON_SRC_DIR)/q_plsf_3.cpp \
	$(COMMON_SRC_DIR)/q_plsf_3_tbl.cpp \
	$(COMMON_SRC_DIR)/q_plsf_5.cpp \
	$(COMMON_SRC_DIR)/q_plsf_5_tbl.cpp \
	$(COMMON_SRC_DIR)/q_plsf.cpp \
	$(COMMON_SRC_DIR)/qua_gain_tbl.cpp \
	$(COMMON_SRC_DIR)/reorder.cpp $(COMMON_SRC_DIR)/residu.cpp \
	$(COMMON_SRC_DIR)/round.cpp $(COMMON_SRC_DIR)/set_zero.cpp \
	$(COMMON_SRC_DIR)/shr.cpp $(COMMON_SRC_DIR)/shr_r.cpp \
	$(COMMON_SRC_DIR)/sqrt_l.cpp $(COMMON_SRC_DIR)/sqrt_l_tbl.cpp \
	$(COMMON_SRC_DIR)/sub.cpp $(COMMON_SRC_DIR)/syn_filt.cpp \
	$(COMMON_SRC_DIR)/weight_a.cpp \
	$(COMMON_SRC_DIR)/window_tab.cpp
all: all-am

.SUFFIXES:
.SUFFIXES: .c .cpp .lo .o .obj
$(srcdir)/Makefile.in: # $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign amrnb/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign amrnb/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: # $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): # $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
opencore-amrnb.pc: $(top_builddir)/config.status $(srcdir)/opencore-amrnb.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}

libopencore-amrnb.la: $(libopencore_amrnb_la_OBJECTS) $(libopencore_amrnb_la_DEPENDENCIES) $(EXTRA_libopencore_amrnb_la_DEPENDENCIES) 
	$(AM_V_GEN)$(libopencore_amrnb_la_LINK) -rpath $(libdir) $(libopencore_amrnb_la_OBJECTS) $(libopencore_amrnb_la_LIBADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

include ./$(DEPDIR)/a_refl.Plo # am--include-marker
include ./$(DEPDIR)/add.Plo # am--include-marker
include ./$(DEPDIR)/agc.Plo # am--include-marker
include ./$(DEPDIR)/amrdecode.Plo # am--include-marker
include ./$(DEPDIR)/amrencode.Plo # am--include-marker
include ./$(DEPDIR)/autocorr.Plo # am--include-marker
include ./$(DEPDIR)/az_lsp.Plo # am--include-marker
include ./$(DEPDIR)/b_cn_cod.Plo # am--include-marker
include ./$(DEPDIR)/bgnscd.Plo # am--include-marker
include ./$(DEPDIR)/bitno_tab.Plo # am--include-marker
include ./$(DEPDIR)/bitreorder_tab.Plo # am--include-marker
include ./$(DEPDIR)/c1035pf.Plo # am--include-marker
include ./$(DEPDIR)/c2_11pf.Plo # am--include-marker
include ./$(DEPDIR)/c2_9pf.Plo # am--include-marker
include ./$(DEPDIR)/c2_9pf_tab.Plo # am--include-marker
include ./$(DEPDIR)/c3_14pf.Plo # am--include-marker
include ./$(DEPDIR)/c4_17pf.Plo # am--include-marker
include ./$(DEPDIR)/c8_31pf.Plo # am--include-marker
include ./$(DEPDIR)/c_g_aver.Plo # am--include-marker
include ./$(DEPDIR)/calc_cor.Plo # am--include-marker
include ./$(DEPDIR)/calc_en.Plo # am--include-marker
include ./$(DEPDIR)/cbsearch.Plo # am--include-marker
include ./$(DEPDIR)/cl_ltp.Plo # am--include-marker
include ./$(DEPDIR)/cod_amr.Plo # am--include-marker
include ./$(DEPDIR)/convolve.Plo # am--include-marker
include ./$(DEPDIR)/cor_h.Plo # am--include-marker
include ./$(DEPDIR)/cor_h_x.Plo # am--include-marker
include ./$(DEPDIR)/cor_h_x2.Plo # am--include-marker
include ./$(DEPDIR)/corrwght_tab.Plo # am--include-marker
include ./$(DEPDIR)/d1035pf.Plo # am--include-marker
include ./$(DEPDIR)/d2_11pf.Plo # am--include-marker
include ./$(DEPDIR)/d2_9pf.Plo # am--include-marker
include ./$(DEPDIR)/d3_14pf.Plo # am--include-marker
include ./$(DEPDIR)/d4_17pf.Plo # am--include-marker
include ./$(DEPDIR)/d8_31pf.Plo # am--include-marker
include ./$(DEPDIR)/d_gain_c.Plo # am--include-marker
include ./$(DEPDIR)/d_gain_p.Plo # am--include-marker
include ./$(DEPDIR)/d_plsf.Plo # am--include-marker
include ./$(DEPDIR)/d_plsf_3.Plo # am--include-marker
include ./$(DEPDIR)/d_plsf_5.Plo # am--include-marker
include ./$(DEPDIR)/dec_amr.Plo # am--include-marker
include ./$(DEPDIR)/dec_gain.Plo # am--include-marker
include ./$(DEPDIR)/dec_input_format_tab.Plo # am--include-marker
include ./$(DEPDIR)/dec_lag3.Plo # am--include-marker
include ./$(DEPDIR)/dec_lag6.Plo # am--include-marker
include ./$(DEPDIR)/div_32.Plo # am--include-marker
include ./$(DEPDIR)/div_s.Plo # am--include-marker
include ./$(DEPDIR)/dtx_dec.Plo # am--include-marker
include ./$(DEPDIR)/dtx_enc.Plo # am--include-marker
include ./$(DEPDIR)/dummy.Plo # am--include-marker
include ./$(DEPDIR)/ec_gains.Plo # am--include-marker
include ./$(DEPDIR)/enc_lag3.Plo # am--include-marker
include ./$(DEPDIR)/enc_lag6.Plo # am--include-marker
include ./$(DEPDIR)/enc_output_format_tab.Plo # am--include-marker
include ./$(DEPDIR)/ets_to_if2.Plo # am--include-marker
include ./$(DEPDIR)/ets_to_wmf.Plo # am--include-marker
include ./$(DEPDIR)/ex_ctrl.Plo # am--include-marker
include ./$(DEPDIR)/extract_h.Plo # am--include-marker
include ./$(DEPDIR)/extract_l.Plo # am--include-marker
include ./$(DEPDIR)/g_adapt.Plo # am--include-marker
include ./$(DEPDIR)/g_code.Plo # am--include-marker
include ./$(DEPDIR)/g_pitch.Plo # am--include-marker
include ./$(DEPDIR)/gain_q.Plo # am--include-marker
include ./$(DEPDIR)/gains_tbl.Plo # am--include-marker
include ./$(DEPDIR)/gc_pred.Plo # am--include-marker
include ./$(DEPDIR)/get_const_tbls.Plo # am--include-marker
include ./$(DEPDIR)/gmed_n.Plo # am--include-marker
include ./$(DEPDIR)/gray_tbl.Plo # am--include-marker
include ./$(DEPDIR)/grid_tbl.Plo # am--include-marker
include ./$(DEPDIR)/hp_max.Plo # am--include-marker
include ./$(DEPDIR)/if2_to_ets.Plo # am--include-marker
include ./$(DEPDIR)/int_lpc.Plo # am--include-marker
include ./$(DEPDIR)/int_lsf.Plo # am--include-marker
include ./$(DEPDIR)/inter_36.Plo # am--include-marker
include ./$(DEPDIR)/inter_36_tab.Plo # am--include-marker
include ./$(DEPDIR)/inv_sqrt.Plo # am--include-marker
include ./$(DEPDIR)/inv_sqrt_tbl.Plo # am--include-marker
include ./$(DEPDIR)/l_abs.Plo # am--include-marker
include ./$(DEPDIR)/l_comp.Plo # am--include-marker
include ./$(DEPDIR)/l_deposit_h.Plo # am--include-marker
include ./$(DEPDIR)/l_deposit_l.Plo # am--include-marker
include ./$(DEPDIR)/l_extract.Plo # am--include-marker
include ./$(DEPDIR)/l_negate.Plo # am--include-marker
include ./$(DEPDIR)/l_shr_r.Plo # am--include-marker
include ./$(DEPDIR)/lag_wind.Plo # am--include-marker
include ./$(DEPDIR)/lag_wind_tab.Plo # am--include-marker
include ./$(DEPDIR)/levinson.Plo # am--include-marker
include ./$(DEPDIR)/lflg_upd.Plo # am--include-marker
include ./$(DEPDIR)/log2.Plo # am--include-marker
include ./$(DEPDIR)/log2_norm.Plo # am--include-marker
include ./$(DEPDIR)/log2_tbl.Plo # am--include-marker
include ./$(DEPDIR)/lpc.Plo # am--include-marker
include ./$(DEPDIR)/lsfwt.Plo # am--include-marker
include ./$(DEPDIR)/lsp.Plo # am--include-marker
include ./$(DEPDIR)/lsp_avg.Plo # am--include-marker
include ./$(DEPDIR)/lsp_az.Plo # am--include-marker
include ./$(DEPDIR)/lsp_lsf.Plo # am--include-marker
include ./$(DEPDIR)/lsp_lsf_tbl.Plo # am--include-marker
include ./$(DEPDIR)/lsp_tab.Plo # am--include-marker
include ./$(DEPDIR)/mult_r.Plo # am--include-marker
include ./$(DEPDIR)/negate.Plo # am--include-marker
include ./$(DEPDIR)/norm_l.Plo # am--include-marker
include ./$(DEPDIR)/norm_s.Plo # am--include-marker
include ./$(DEPDIR)/ol_ltp.Plo # am--include-marker
include ./$(DEPDIR)/overflow_tbl.Plo # am--include-marker
include ./$(DEPDIR)/p_ol_wgh.Plo # am--include-marker
include ./$(DEPDIR)/ph_disp.Plo # am--include-marker
include ./$(DEPDIR)/ph_disp_tab.Plo # am--include-marker
include ./$(DEPDIR)/pitch_fr.Plo # am--include-marker
include ./$(DEPDIR)/pitch_ol.Plo # am--include-marker
include ./$(DEPDIR)/post_pro.Plo # am--include-marker
include ./$(DEPDIR)/pow2.Plo # am--include-marker
include ./$(DEPDIR)/pow2_tbl.Plo # am--include-marker
include ./$(DEPDIR)/pre_big.Plo # am--include-marker
include ./$(DEPDIR)/pre_proc.Plo # am--include-marker
include ./$(DEPDIR)/pred_lt.Plo # am--include-marker
include ./$(DEPDIR)/preemph.Plo # am--include-marker
include ./$(DEPDIR)/prm2bits.Plo # am--include-marker
include ./$(DEPDIR)/pstfilt.Plo # am--include-marker
include ./$(DEPDIR)/q_gain_c.Plo # am--include-marker
include ./$(DEPDIR)/q_gain_p.Plo # am--include-marker
include ./$(DEPDIR)/q_plsf.Plo # am--include-marker
include ./$(DEPDIR)/q_plsf_3.Plo # am--include-marker
include ./$(DEPDIR)/q_plsf_3_tbl.Plo # am--include-marker
include ./$(DEPDIR)/q_plsf_5.Plo # am--include-marker
include ./$(DEPDIR)/q_plsf_5_tbl.Plo # am--include-marker
include ./$(DEPDIR)/qgain475.Plo # am--include-marker
include ./$(DEPDIR)/qgain475_tab.Plo # am--include-marker
include ./$(DEPDIR)/qgain795.Plo # am--include-marker
include ./$(DEPDIR)/qua_gain.Plo # am--include-marker
include ./$(DEPDIR)/qua_gain_tbl.Plo # am--include-marker
include ./$(DEPDIR)/reorder.Plo # am--include-marker
include ./$(DEPDIR)/residu.Plo # am--include-marker
include ./$(DEPDIR)/round.Plo # am--include-marker
include ./$(DEPDIR)/s10_8pf.Plo # am--include-marker
include ./$(DEPDIR)/set_sign.Plo # am--include-marker
include ./$(DEPDIR)/set_zero.Plo # am--include-marker
include ./$(DEPDIR)/shr.Plo # am--include-marker
include ./$(DEPDIR)/shr_r.Plo # am--include-marker
include ./$(DEPDIR)/sid_sync.Plo # am--include-marker
include ./$(DEPDIR)/sp_dec.Plo # am--include-marker
include ./$(DEPDIR)/sp_enc.Plo # am--include-marker
include ./$(DEPDIR)/spreproc.Plo # am--include-marker
include ./$(DEPDIR)/spstproc.Plo # am--include-marker
include ./$(DEPDIR)/sqrt_l.Plo # am--include-marker
include ./$(DEPDIR)/sqrt_l_tbl.Plo # am--include-marker
include ./$(DEPDIR)/sub.Plo # am--include-marker
include ./$(DEPDIR)/syn_filt.Plo # am--include-marker
include ./$(DEPDIR)/ton_stab.Plo # am--include-marker
include ./$(DEPDIR)/vad1.Plo # am--include-marker
include ./$(DEPDIR)/weight_a.Plo # am--include-marker
include ./$(DEPDIR)/window_tab.Plo # am--include-marker
include ./$(DEPDIR)/wmf_to_ets.Plo # am--include-marker
include ./$(DEPDIR)/wrapper.Plo # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(COMPILE) -c -o $@ $<

.c.obj:
	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
	$(AM_V_CC)$(LTCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Plo
#	$(AM_V_CC)source='$<' object='$@' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LTCOMPILE) -c -o $@ $<

.cpp.o:
	$(AM_V_CXX)$(CXXCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	$(AM_V_CXX)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXXCOMPILE) -c -o $@ $<

.cpp.obj:
	$(AM_V_CXX)$(CXXCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	$(AM_V_CXX)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXXCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.cpp.lo:
	$(AM_V_CXX)$(LTCXXCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Plo
#	$(AM_V_CXX)source='$<' object='$@' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LTCXXCOMPILE) -c -o $@ $<

agc.lo: $(DEC_SRC_DIR)/agc.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT agc.lo -MD -MP -MF $(DEPDIR)/agc.Tpo -c -o agc.lo `test -f '$(DEC_SRC_DIR)/agc.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/agc.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/agc.Tpo $(DEPDIR)/agc.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/agc.cpp' object='agc.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o agc.lo `test -f '$(DEC_SRC_DIR)/agc.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/agc.cpp

amrdecode.lo: $(DEC_SRC_DIR)/amrdecode.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT amrdecode.lo -MD -MP -MF $(DEPDIR)/amrdecode.Tpo -c -o amrdecode.lo `test -f '$(DEC_SRC_DIR)/amrdecode.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/amrdecode.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/amrdecode.Tpo $(DEPDIR)/amrdecode.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/amrdecode.cpp' object='amrdecode.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o amrdecode.lo `test -f '$(DEC_SRC_DIR)/amrdecode.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/amrdecode.cpp

a_refl.lo: $(DEC_SRC_DIR)/a_refl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT a_refl.lo -MD -MP -MF $(DEPDIR)/a_refl.Tpo -c -o a_refl.lo `test -f '$(DEC_SRC_DIR)/a_refl.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/a_refl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/a_refl.Tpo $(DEPDIR)/a_refl.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/a_refl.cpp' object='a_refl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o a_refl.lo `test -f '$(DEC_SRC_DIR)/a_refl.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/a_refl.cpp

b_cn_cod.lo: $(DEC_SRC_DIR)/b_cn_cod.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT b_cn_cod.lo -MD -MP -MF $(DEPDIR)/b_cn_cod.Tpo -c -o b_cn_cod.lo `test -f '$(DEC_SRC_DIR)/b_cn_cod.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/b_cn_cod.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/b_cn_cod.Tpo $(DEPDIR)/b_cn_cod.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/b_cn_cod.cpp' object='b_cn_cod.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o b_cn_cod.lo `test -f '$(DEC_SRC_DIR)/b_cn_cod.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/b_cn_cod.cpp

bgnscd.lo: $(DEC_SRC_DIR)/bgnscd.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT bgnscd.lo -MD -MP -MF $(DEPDIR)/bgnscd.Tpo -c -o bgnscd.lo `test -f '$(DEC_SRC_DIR)/bgnscd.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/bgnscd.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/bgnscd.Tpo $(DEPDIR)/bgnscd.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/bgnscd.cpp' object='bgnscd.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o bgnscd.lo `test -f '$(DEC_SRC_DIR)/bgnscd.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/bgnscd.cpp

c_g_aver.lo: $(DEC_SRC_DIR)/c_g_aver.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT c_g_aver.lo -MD -MP -MF $(DEPDIR)/c_g_aver.Tpo -c -o c_g_aver.lo `test -f '$(DEC_SRC_DIR)/c_g_aver.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/c_g_aver.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/c_g_aver.Tpo $(DEPDIR)/c_g_aver.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/c_g_aver.cpp' object='c_g_aver.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o c_g_aver.lo `test -f '$(DEC_SRC_DIR)/c_g_aver.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/c_g_aver.cpp

d1035pf.lo: $(DEC_SRC_DIR)/d1035pf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT d1035pf.lo -MD -MP -MF $(DEPDIR)/d1035pf.Tpo -c -o d1035pf.lo `test -f '$(DEC_SRC_DIR)/d1035pf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d1035pf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/d1035pf.Tpo $(DEPDIR)/d1035pf.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/d1035pf.cpp' object='d1035pf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o d1035pf.lo `test -f '$(DEC_SRC_DIR)/d1035pf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d1035pf.cpp

d2_11pf.lo: $(DEC_SRC_DIR)/d2_11pf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT d2_11pf.lo -MD -MP -MF $(DEPDIR)/d2_11pf.Tpo -c -o d2_11pf.lo `test -f '$(DEC_SRC_DIR)/d2_11pf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d2_11pf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/d2_11pf.Tpo $(DEPDIR)/d2_11pf.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/d2_11pf.cpp' object='d2_11pf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o d2_11pf.lo `test -f '$(DEC_SRC_DIR)/d2_11pf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d2_11pf.cpp

d2_9pf.lo: $(DEC_SRC_DIR)/d2_9pf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT d2_9pf.lo -MD -MP -MF $(DEPDIR)/d2_9pf.Tpo -c -o d2_9pf.lo `test -f '$(DEC_SRC_DIR)/d2_9pf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d2_9pf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/d2_9pf.Tpo $(DEPDIR)/d2_9pf.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/d2_9pf.cpp' object='d2_9pf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o d2_9pf.lo `test -f '$(DEC_SRC_DIR)/d2_9pf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d2_9pf.cpp

d3_14pf.lo: $(DEC_SRC_DIR)/d3_14pf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT d3_14pf.lo -MD -MP -MF $(DEPDIR)/d3_14pf.Tpo -c -o d3_14pf.lo `test -f '$(DEC_SRC_DIR)/d3_14pf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d3_14pf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/d3_14pf.Tpo $(DEPDIR)/d3_14pf.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/d3_14pf.cpp' object='d3_14pf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o d3_14pf.lo `test -f '$(DEC_SRC_DIR)/d3_14pf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d3_14pf.cpp

d4_17pf.lo: $(DEC_SRC_DIR)/d4_17pf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT d4_17pf.lo -MD -MP -MF $(DEPDIR)/d4_17pf.Tpo -c -o d4_17pf.lo `test -f '$(DEC_SRC_DIR)/d4_17pf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d4_17pf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/d4_17pf.Tpo $(DEPDIR)/d4_17pf.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/d4_17pf.cpp' object='d4_17pf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o d4_17pf.lo `test -f '$(DEC_SRC_DIR)/d4_17pf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d4_17pf.cpp

d8_31pf.lo: $(DEC_SRC_DIR)/d8_31pf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT d8_31pf.lo -MD -MP -MF $(DEPDIR)/d8_31pf.Tpo -c -o d8_31pf.lo `test -f '$(DEC_SRC_DIR)/d8_31pf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d8_31pf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/d8_31pf.Tpo $(DEPDIR)/d8_31pf.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/d8_31pf.cpp' object='d8_31pf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o d8_31pf.lo `test -f '$(DEC_SRC_DIR)/d8_31pf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d8_31pf.cpp

dec_amr.lo: $(DEC_SRC_DIR)/dec_amr.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT dec_amr.lo -MD -MP -MF $(DEPDIR)/dec_amr.Tpo -c -o dec_amr.lo `test -f '$(DEC_SRC_DIR)/dec_amr.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_amr.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/dec_amr.Tpo $(DEPDIR)/dec_amr.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/dec_amr.cpp' object='dec_amr.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o dec_amr.lo `test -f '$(DEC_SRC_DIR)/dec_amr.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_amr.cpp

dec_gain.lo: $(DEC_SRC_DIR)/dec_gain.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT dec_gain.lo -MD -MP -MF $(DEPDIR)/dec_gain.Tpo -c -o dec_gain.lo `test -f '$(DEC_SRC_DIR)/dec_gain.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_gain.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/dec_gain.Tpo $(DEPDIR)/dec_gain.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/dec_gain.cpp' object='dec_gain.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o dec_gain.lo `test -f '$(DEC_SRC_DIR)/dec_gain.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_gain.cpp

dec_input_format_tab.lo: $(DEC_SRC_DIR)/dec_input_format_tab.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT dec_input_format_tab.lo -MD -MP -MF $(DEPDIR)/dec_input_format_tab.Tpo -c -o dec_input_format_tab.lo `test -f '$(DEC_SRC_DIR)/dec_input_format_tab.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_input_format_tab.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/dec_input_format_tab.Tpo $(DEPDIR)/dec_input_format_tab.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/dec_input_format_tab.cpp' object='dec_input_format_tab.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o dec_input_format_tab.lo `test -f '$(DEC_SRC_DIR)/dec_input_format_tab.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_input_format_tab.cpp

dec_lag3.lo: $(DEC_SRC_DIR)/dec_lag3.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT dec_lag3.lo -MD -MP -MF $(DEPDIR)/dec_lag3.Tpo -c -o dec_lag3.lo `test -f '$(DEC_SRC_DIR)/dec_lag3.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_lag3.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/dec_lag3.Tpo $(DEPDIR)/dec_lag3.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/dec_lag3.cpp' object='dec_lag3.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o dec_lag3.lo `test -f '$(DEC_SRC_DIR)/dec_lag3.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_lag3.cpp

dec_lag6.lo: $(DEC_SRC_DIR)/dec_lag6.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT dec_lag6.lo -MD -MP -MF $(DEPDIR)/dec_lag6.Tpo -c -o dec_lag6.lo `test -f '$(DEC_SRC_DIR)/dec_lag6.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_lag6.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/dec_lag6.Tpo $(DEPDIR)/dec_lag6.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/dec_lag6.cpp' object='dec_lag6.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o dec_lag6.lo `test -f '$(DEC_SRC_DIR)/dec_lag6.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_lag6.cpp

d_gain_c.lo: $(DEC_SRC_DIR)/d_gain_c.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT d_gain_c.lo -MD -MP -MF $(DEPDIR)/d_gain_c.Tpo -c -o d_gain_c.lo `test -f '$(DEC_SRC_DIR)/d_gain_c.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d_gain_c.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/d_gain_c.Tpo $(DEPDIR)/d_gain_c.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/d_gain_c.cpp' object='d_gain_c.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o d_gain_c.lo `test -f '$(DEC_SRC_DIR)/d_gain_c.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d_gain_c.cpp

d_gain_p.lo: $(DEC_SRC_DIR)/d_gain_p.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT d_gain_p.lo -MD -MP -MF $(DEPDIR)/d_gain_p.Tpo -c -o d_gain_p.lo `test -f '$(DEC_SRC_DIR)/d_gain_p.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d_gain_p.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/d_gain_p.Tpo $(DEPDIR)/d_gain_p.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/d_gain_p.cpp' object='d_gain_p.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o d_gain_p.lo `test -f '$(DEC_SRC_DIR)/d_gain_p.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d_gain_p.cpp

d_plsf_3.lo: $(DEC_SRC_DIR)/d_plsf_3.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT d_plsf_3.lo -MD -MP -MF $(DEPDIR)/d_plsf_3.Tpo -c -o d_plsf_3.lo `test -f '$(DEC_SRC_DIR)/d_plsf_3.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d_plsf_3.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/d_plsf_3.Tpo $(DEPDIR)/d_plsf_3.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/d_plsf_3.cpp' object='d_plsf_3.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o d_plsf_3.lo `test -f '$(DEC_SRC_DIR)/d_plsf_3.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d_plsf_3.cpp

d_plsf_5.lo: $(DEC_SRC_DIR)/d_plsf_5.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT d_plsf_5.lo -MD -MP -MF $(DEPDIR)/d_plsf_5.Tpo -c -o d_plsf_5.lo `test -f '$(DEC_SRC_DIR)/d_plsf_5.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d_plsf_5.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/d_plsf_5.Tpo $(DEPDIR)/d_plsf_5.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/d_plsf_5.cpp' object='d_plsf_5.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o d_plsf_5.lo `test -f '$(DEC_SRC_DIR)/d_plsf_5.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d_plsf_5.cpp

d_plsf.lo: $(DEC_SRC_DIR)/d_plsf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT d_plsf.lo -MD -MP -MF $(DEPDIR)/d_plsf.Tpo -c -o d_plsf.lo `test -f '$(DEC_SRC_DIR)/d_plsf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d_plsf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/d_plsf.Tpo $(DEPDIR)/d_plsf.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/d_plsf.cpp' object='d_plsf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o d_plsf.lo `test -f '$(DEC_SRC_DIR)/d_plsf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/d_plsf.cpp

dtx_dec.lo: $(DEC_SRC_DIR)/dtx_dec.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT dtx_dec.lo -MD -MP -MF $(DEPDIR)/dtx_dec.Tpo -c -o dtx_dec.lo `test -f '$(DEC_SRC_DIR)/dtx_dec.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dtx_dec.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/dtx_dec.Tpo $(DEPDIR)/dtx_dec.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/dtx_dec.cpp' object='dtx_dec.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o dtx_dec.lo `test -f '$(DEC_SRC_DIR)/dtx_dec.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dtx_dec.cpp

ec_gains.lo: $(DEC_SRC_DIR)/ec_gains.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT ec_gains.lo -MD -MP -MF $(DEPDIR)/ec_gains.Tpo -c -o ec_gains.lo `test -f '$(DEC_SRC_DIR)/ec_gains.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/ec_gains.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/ec_gains.Tpo $(DEPDIR)/ec_gains.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/ec_gains.cpp' object='ec_gains.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o ec_gains.lo `test -f '$(DEC_SRC_DIR)/ec_gains.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/ec_gains.cpp

ex_ctrl.lo: $(DEC_SRC_DIR)/ex_ctrl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT ex_ctrl.lo -MD -MP -MF $(DEPDIR)/ex_ctrl.Tpo -c -o ex_ctrl.lo `test -f '$(DEC_SRC_DIR)/ex_ctrl.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/ex_ctrl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/ex_ctrl.Tpo $(DEPDIR)/ex_ctrl.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/ex_ctrl.cpp' object='ex_ctrl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o ex_ctrl.lo `test -f '$(DEC_SRC_DIR)/ex_ctrl.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/ex_ctrl.cpp

if2_to_ets.lo: $(DEC_SRC_DIR)/if2_to_ets.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT if2_to_ets.lo -MD -MP -MF $(DEPDIR)/if2_to_ets.Tpo -c -o if2_to_ets.lo `test -f '$(DEC_SRC_DIR)/if2_to_ets.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/if2_to_ets.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/if2_to_ets.Tpo $(DEPDIR)/if2_to_ets.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/if2_to_ets.cpp' object='if2_to_ets.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o if2_to_ets.lo `test -f '$(DEC_SRC_DIR)/if2_to_ets.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/if2_to_ets.cpp

int_lsf.lo: $(DEC_SRC_DIR)/int_lsf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT int_lsf.lo -MD -MP -MF $(DEPDIR)/int_lsf.Tpo -c -o int_lsf.lo `test -f '$(DEC_SRC_DIR)/int_lsf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/int_lsf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/int_lsf.Tpo $(DEPDIR)/int_lsf.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/int_lsf.cpp' object='int_lsf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o int_lsf.lo `test -f '$(DEC_SRC_DIR)/int_lsf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/int_lsf.cpp

lsp_avg.lo: $(DEC_SRC_DIR)/lsp_avg.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT lsp_avg.lo -MD -MP -MF $(DEPDIR)/lsp_avg.Tpo -c -o lsp_avg.lo `test -f '$(DEC_SRC_DIR)/lsp_avg.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/lsp_avg.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/lsp_avg.Tpo $(DEPDIR)/lsp_avg.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/lsp_avg.cpp' object='lsp_avg.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o lsp_avg.lo `test -f '$(DEC_SRC_DIR)/lsp_avg.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/lsp_avg.cpp

ph_disp.lo: $(DEC_SRC_DIR)/ph_disp.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT ph_disp.lo -MD -MP -MF $(DEPDIR)/ph_disp.Tpo -c -o ph_disp.lo `test -f '$(DEC_SRC_DIR)/ph_disp.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/ph_disp.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/ph_disp.Tpo $(DEPDIR)/ph_disp.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/ph_disp.cpp' object='ph_disp.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o ph_disp.lo `test -f '$(DEC_SRC_DIR)/ph_disp.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/ph_disp.cpp

post_pro.lo: $(DEC_SRC_DIR)/post_pro.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT post_pro.lo -MD -MP -MF $(DEPDIR)/post_pro.Tpo -c -o post_pro.lo `test -f '$(DEC_SRC_DIR)/post_pro.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/post_pro.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/post_pro.Tpo $(DEPDIR)/post_pro.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/post_pro.cpp' object='post_pro.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o post_pro.lo `test -f '$(DEC_SRC_DIR)/post_pro.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/post_pro.cpp

preemph.lo: $(DEC_SRC_DIR)/preemph.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT preemph.lo -MD -MP -MF $(DEPDIR)/preemph.Tpo -c -o preemph.lo `test -f '$(DEC_SRC_DIR)/preemph.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/preemph.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/preemph.Tpo $(DEPDIR)/preemph.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/preemph.cpp' object='preemph.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o preemph.lo `test -f '$(DEC_SRC_DIR)/preemph.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/preemph.cpp

pstfilt.lo: $(DEC_SRC_DIR)/pstfilt.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT pstfilt.lo -MD -MP -MF $(DEPDIR)/pstfilt.Tpo -c -o pstfilt.lo `test -f '$(DEC_SRC_DIR)/pstfilt.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/pstfilt.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/pstfilt.Tpo $(DEPDIR)/pstfilt.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/pstfilt.cpp' object='pstfilt.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o pstfilt.lo `test -f '$(DEC_SRC_DIR)/pstfilt.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/pstfilt.cpp

qgain475_tab.lo: $(DEC_SRC_DIR)/qgain475_tab.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT qgain475_tab.lo -MD -MP -MF $(DEPDIR)/qgain475_tab.Tpo -c -o qgain475_tab.lo `test -f '$(DEC_SRC_DIR)/qgain475_tab.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/qgain475_tab.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/qgain475_tab.Tpo $(DEPDIR)/qgain475_tab.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/qgain475_tab.cpp' object='qgain475_tab.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o qgain475_tab.lo `test -f '$(DEC_SRC_DIR)/qgain475_tab.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/qgain475_tab.cpp

sp_dec.lo: $(DEC_SRC_DIR)/sp_dec.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT sp_dec.lo -MD -MP -MF $(DEPDIR)/sp_dec.Tpo -c -o sp_dec.lo `test -f '$(DEC_SRC_DIR)/sp_dec.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/sp_dec.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/sp_dec.Tpo $(DEPDIR)/sp_dec.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/sp_dec.cpp' object='sp_dec.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o sp_dec.lo `test -f '$(DEC_SRC_DIR)/sp_dec.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/sp_dec.cpp

wmf_to_ets.lo: $(DEC_SRC_DIR)/wmf_to_ets.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT wmf_to_ets.lo -MD -MP -MF $(DEPDIR)/wmf_to_ets.Tpo -c -o wmf_to_ets.lo `test -f '$(DEC_SRC_DIR)/wmf_to_ets.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/wmf_to_ets.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/wmf_to_ets.Tpo $(DEPDIR)/wmf_to_ets.Plo
#	$(AM_V_CXX)source='$(DEC_SRC_DIR)/wmf_to_ets.cpp' object='wmf_to_ets.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o wmf_to_ets.lo `test -f '$(DEC_SRC_DIR)/wmf_to_ets.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/wmf_to_ets.cpp

amrencode.lo: $(ENC_SRC_DIR)/amrencode.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT amrencode.lo -MD -MP -MF $(DEPDIR)/amrencode.Tpo -c -o amrencode.lo `test -f '$(ENC_SRC_DIR)/amrencode.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/amrencode.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/amrencode.Tpo $(DEPDIR)/amrencode.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/amrencode.cpp' object='amrencode.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o amrencode.lo `test -f '$(ENC_SRC_DIR)/amrencode.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/amrencode.cpp

autocorr.lo: $(ENC_SRC_DIR)/autocorr.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT autocorr.lo -MD -MP -MF $(DEPDIR)/autocorr.Tpo -c -o autocorr.lo `test -f '$(ENC_SRC_DIR)/autocorr.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/autocorr.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/autocorr.Tpo $(DEPDIR)/autocorr.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/autocorr.cpp' object='autocorr.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o autocorr.lo `test -f '$(ENC_SRC_DIR)/autocorr.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/autocorr.cpp

c1035pf.lo: $(ENC_SRC_DIR)/c1035pf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT c1035pf.lo -MD -MP -MF $(DEPDIR)/c1035pf.Tpo -c -o c1035pf.lo `test -f '$(ENC_SRC_DIR)/c1035pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/c1035pf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/c1035pf.Tpo $(DEPDIR)/c1035pf.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/c1035pf.cpp' object='c1035pf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o c1035pf.lo `test -f '$(ENC_SRC_DIR)/c1035pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/c1035pf.cpp

c2_11pf.lo: $(ENC_SRC_DIR)/c2_11pf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT c2_11pf.lo -MD -MP -MF $(DEPDIR)/c2_11pf.Tpo -c -o c2_11pf.lo `test -f '$(ENC_SRC_DIR)/c2_11pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/c2_11pf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/c2_11pf.Tpo $(DEPDIR)/c2_11pf.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/c2_11pf.cpp' object='c2_11pf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o c2_11pf.lo `test -f '$(ENC_SRC_DIR)/c2_11pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/c2_11pf.cpp

c2_9pf.lo: $(ENC_SRC_DIR)/c2_9pf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT c2_9pf.lo -MD -MP -MF $(DEPDIR)/c2_9pf.Tpo -c -o c2_9pf.lo `test -f '$(ENC_SRC_DIR)/c2_9pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/c2_9pf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/c2_9pf.Tpo $(DEPDIR)/c2_9pf.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/c2_9pf.cpp' object='c2_9pf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o c2_9pf.lo `test -f '$(ENC_SRC_DIR)/c2_9pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/c2_9pf.cpp

c3_14pf.lo: $(ENC_SRC_DIR)/c3_14pf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT c3_14pf.lo -MD -MP -MF $(DEPDIR)/c3_14pf.Tpo -c -o c3_14pf.lo `test -f '$(ENC_SRC_DIR)/c3_14pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/c3_14pf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/c3_14pf.Tpo $(DEPDIR)/c3_14pf.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/c3_14pf.cpp' object='c3_14pf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o c3_14pf.lo `test -f '$(ENC_SRC_DIR)/c3_14pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/c3_14pf.cpp

c4_17pf.lo: $(ENC_SRC_DIR)/c4_17pf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT c4_17pf.lo -MD -MP -MF $(DEPDIR)/c4_17pf.Tpo -c -o c4_17pf.lo `test -f '$(ENC_SRC_DIR)/c4_17pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/c4_17pf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/c4_17pf.Tpo $(DEPDIR)/c4_17pf.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/c4_17pf.cpp' object='c4_17pf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o c4_17pf.lo `test -f '$(ENC_SRC_DIR)/c4_17pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/c4_17pf.cpp

c8_31pf.lo: $(ENC_SRC_DIR)/c8_31pf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT c8_31pf.lo -MD -MP -MF $(DEPDIR)/c8_31pf.Tpo -c -o c8_31pf.lo `test -f '$(ENC_SRC_DIR)/c8_31pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/c8_31pf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/c8_31pf.Tpo $(DEPDIR)/c8_31pf.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/c8_31pf.cpp' object='c8_31pf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o c8_31pf.lo `test -f '$(ENC_SRC_DIR)/c8_31pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/c8_31pf.cpp

calc_cor.lo: $(ENC_SRC_DIR)/calc_cor.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT calc_cor.lo -MD -MP -MF $(DEPDIR)/calc_cor.Tpo -c -o calc_cor.lo `test -f '$(ENC_SRC_DIR)/calc_cor.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/calc_cor.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/calc_cor.Tpo $(DEPDIR)/calc_cor.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/calc_cor.cpp' object='calc_cor.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o calc_cor.lo `test -f '$(ENC_SRC_DIR)/calc_cor.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/calc_cor.cpp

calc_en.lo: $(ENC_SRC_DIR)/calc_en.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT calc_en.lo -MD -MP -MF $(DEPDIR)/calc_en.Tpo -c -o calc_en.lo `test -f '$(ENC_SRC_DIR)/calc_en.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/calc_en.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/calc_en.Tpo $(DEPDIR)/calc_en.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/calc_en.cpp' object='calc_en.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o calc_en.lo `test -f '$(ENC_SRC_DIR)/calc_en.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/calc_en.cpp

cbsearch.lo: $(ENC_SRC_DIR)/cbsearch.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cbsearch.lo -MD -MP -MF $(DEPDIR)/cbsearch.Tpo -c -o cbsearch.lo `test -f '$(ENC_SRC_DIR)/cbsearch.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/cbsearch.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/cbsearch.Tpo $(DEPDIR)/cbsearch.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/cbsearch.cpp' object='cbsearch.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cbsearch.lo `test -f '$(ENC_SRC_DIR)/cbsearch.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/cbsearch.cpp

cl_ltp.lo: $(ENC_SRC_DIR)/cl_ltp.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cl_ltp.lo -MD -MP -MF $(DEPDIR)/cl_ltp.Tpo -c -o cl_ltp.lo `test -f '$(ENC_SRC_DIR)/cl_ltp.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/cl_ltp.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/cl_ltp.Tpo $(DEPDIR)/cl_ltp.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/cl_ltp.cpp' object='cl_ltp.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cl_ltp.lo `test -f '$(ENC_SRC_DIR)/cl_ltp.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/cl_ltp.cpp

cod_amr.lo: $(ENC_SRC_DIR)/cod_amr.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cod_amr.lo -MD -MP -MF $(DEPDIR)/cod_amr.Tpo -c -o cod_amr.lo `test -f '$(ENC_SRC_DIR)/cod_amr.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/cod_amr.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/cod_amr.Tpo $(DEPDIR)/cod_amr.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/cod_amr.cpp' object='cod_amr.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cod_amr.lo `test -f '$(ENC_SRC_DIR)/cod_amr.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/cod_amr.cpp

convolve.lo: $(ENC_SRC_DIR)/convolve.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT convolve.lo -MD -MP -MF $(DEPDIR)/convolve.Tpo -c -o convolve.lo `test -f '$(ENC_SRC_DIR)/convolve.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/convolve.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/convolve.Tpo $(DEPDIR)/convolve.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/convolve.cpp' object='convolve.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o convolve.lo `test -f '$(ENC_SRC_DIR)/convolve.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/convolve.cpp

cor_h.lo: $(ENC_SRC_DIR)/cor_h.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cor_h.lo -MD -MP -MF $(DEPDIR)/cor_h.Tpo -c -o cor_h.lo `test -f '$(ENC_SRC_DIR)/cor_h.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/cor_h.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/cor_h.Tpo $(DEPDIR)/cor_h.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/cor_h.cpp' object='cor_h.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cor_h.lo `test -f '$(ENC_SRC_DIR)/cor_h.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/cor_h.cpp

cor_h_x2.lo: $(ENC_SRC_DIR)/cor_h_x2.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cor_h_x2.lo -MD -MP -MF $(DEPDIR)/cor_h_x2.Tpo -c -o cor_h_x2.lo `test -f '$(ENC_SRC_DIR)/cor_h_x2.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/cor_h_x2.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/cor_h_x2.Tpo $(DEPDIR)/cor_h_x2.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/cor_h_x2.cpp' object='cor_h_x2.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cor_h_x2.lo `test -f '$(ENC_SRC_DIR)/cor_h_x2.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/cor_h_x2.cpp

cor_h_x.lo: $(ENC_SRC_DIR)/cor_h_x.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cor_h_x.lo -MD -MP -MF $(DEPDIR)/cor_h_x.Tpo -c -o cor_h_x.lo `test -f '$(ENC_SRC_DIR)/cor_h_x.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/cor_h_x.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/cor_h_x.Tpo $(DEPDIR)/cor_h_x.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/cor_h_x.cpp' object='cor_h_x.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cor_h_x.lo `test -f '$(ENC_SRC_DIR)/cor_h_x.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/cor_h_x.cpp

corrwght_tab.lo: $(ENC_SRC_DIR)/corrwght_tab.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT corrwght_tab.lo -MD -MP -MF $(DEPDIR)/corrwght_tab.Tpo -c -o corrwght_tab.lo `test -f '$(ENC_SRC_DIR)/corrwght_tab.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/corrwght_tab.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/corrwght_tab.Tpo $(DEPDIR)/corrwght_tab.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/corrwght_tab.cpp' object='corrwght_tab.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o corrwght_tab.lo `test -f '$(ENC_SRC_DIR)/corrwght_tab.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/corrwght_tab.cpp

div_32.lo: $(ENC_SRC_DIR)/div_32.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT div_32.lo -MD -MP -MF $(DEPDIR)/div_32.Tpo -c -o div_32.lo `test -f '$(ENC_SRC_DIR)/div_32.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/div_32.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/div_32.Tpo $(DEPDIR)/div_32.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/div_32.cpp' object='div_32.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o div_32.lo `test -f '$(ENC_SRC_DIR)/div_32.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/div_32.cpp

dtx_enc.lo: $(ENC_SRC_DIR)/dtx_enc.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT dtx_enc.lo -MD -MP -MF $(DEPDIR)/dtx_enc.Tpo -c -o dtx_enc.lo `test -f '$(ENC_SRC_DIR)/dtx_enc.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/dtx_enc.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/dtx_enc.Tpo $(DEPDIR)/dtx_enc.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/dtx_enc.cpp' object='dtx_enc.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o dtx_enc.lo `test -f '$(ENC_SRC_DIR)/dtx_enc.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/dtx_enc.cpp

enc_lag3.lo: $(ENC_SRC_DIR)/enc_lag3.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT enc_lag3.lo -MD -MP -MF $(DEPDIR)/enc_lag3.Tpo -c -o enc_lag3.lo `test -f '$(ENC_SRC_DIR)/enc_lag3.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/enc_lag3.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/enc_lag3.Tpo $(DEPDIR)/enc_lag3.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/enc_lag3.cpp' object='enc_lag3.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o enc_lag3.lo `test -f '$(ENC_SRC_DIR)/enc_lag3.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/enc_lag3.cpp

enc_lag6.lo: $(ENC_SRC_DIR)/enc_lag6.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT enc_lag6.lo -MD -MP -MF $(DEPDIR)/enc_lag6.Tpo -c -o enc_lag6.lo `test -f '$(ENC_SRC_DIR)/enc_lag6.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/enc_lag6.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/enc_lag6.Tpo $(DEPDIR)/enc_lag6.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/enc_lag6.cpp' object='enc_lag6.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o enc_lag6.lo `test -f '$(ENC_SRC_DIR)/enc_lag6.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/enc_lag6.cpp

enc_output_format_tab.lo: $(ENC_SRC_DIR)/enc_output_format_tab.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT enc_output_format_tab.lo -MD -MP -MF $(DEPDIR)/enc_output_format_tab.Tpo -c -o enc_output_format_tab.lo `test -f '$(ENC_SRC_DIR)/enc_output_format_tab.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/enc_output_format_tab.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/enc_output_format_tab.Tpo $(DEPDIR)/enc_output_format_tab.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/enc_output_format_tab.cpp' object='enc_output_format_tab.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o enc_output_format_tab.lo `test -f '$(ENC_SRC_DIR)/enc_output_format_tab.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/enc_output_format_tab.cpp

ets_to_if2.lo: $(ENC_SRC_DIR)/ets_to_if2.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT ets_to_if2.lo -MD -MP -MF $(DEPDIR)/ets_to_if2.Tpo -c -o ets_to_if2.lo `test -f '$(ENC_SRC_DIR)/ets_to_if2.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/ets_to_if2.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/ets_to_if2.Tpo $(DEPDIR)/ets_to_if2.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/ets_to_if2.cpp' object='ets_to_if2.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o ets_to_if2.lo `test -f '$(ENC_SRC_DIR)/ets_to_if2.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/ets_to_if2.cpp

ets_to_wmf.lo: $(ENC_SRC_DIR)/ets_to_wmf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT ets_to_wmf.lo -MD -MP -MF $(DEPDIR)/ets_to_wmf.Tpo -c -o ets_to_wmf.lo `test -f '$(ENC_SRC_DIR)/ets_to_wmf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/ets_to_wmf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/ets_to_wmf.Tpo $(DEPDIR)/ets_to_wmf.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/ets_to_wmf.cpp' object='ets_to_wmf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o ets_to_wmf.lo `test -f '$(ENC_SRC_DIR)/ets_to_wmf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/ets_to_wmf.cpp

g_adapt.lo: $(ENC_SRC_DIR)/g_adapt.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT g_adapt.lo -MD -MP -MF $(DEPDIR)/g_adapt.Tpo -c -o g_adapt.lo `test -f '$(ENC_SRC_DIR)/g_adapt.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/g_adapt.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/g_adapt.Tpo $(DEPDIR)/g_adapt.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/g_adapt.cpp' object='g_adapt.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o g_adapt.lo `test -f '$(ENC_SRC_DIR)/g_adapt.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/g_adapt.cpp

gain_q.lo: $(ENC_SRC_DIR)/gain_q.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gain_q.lo -MD -MP -MF $(DEPDIR)/gain_q.Tpo -c -o gain_q.lo `test -f '$(ENC_SRC_DIR)/gain_q.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/gain_q.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/gain_q.Tpo $(DEPDIR)/gain_q.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/gain_q.cpp' object='gain_q.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gain_q.lo `test -f '$(ENC_SRC_DIR)/gain_q.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/gain_q.cpp

g_code.lo: $(ENC_SRC_DIR)/g_code.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT g_code.lo -MD -MP -MF $(DEPDIR)/g_code.Tpo -c -o g_code.lo `test -f '$(ENC_SRC_DIR)/g_code.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/g_code.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/g_code.Tpo $(DEPDIR)/g_code.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/g_code.cpp' object='g_code.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o g_code.lo `test -f '$(ENC_SRC_DIR)/g_code.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/g_code.cpp

g_pitch.lo: $(ENC_SRC_DIR)/g_pitch.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT g_pitch.lo -MD -MP -MF $(DEPDIR)/g_pitch.Tpo -c -o g_pitch.lo `test -f '$(ENC_SRC_DIR)/g_pitch.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/g_pitch.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/g_pitch.Tpo $(DEPDIR)/g_pitch.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/g_pitch.cpp' object='g_pitch.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o g_pitch.lo `test -f '$(ENC_SRC_DIR)/g_pitch.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/g_pitch.cpp

hp_max.lo: $(ENC_SRC_DIR)/hp_max.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT hp_max.lo -MD -MP -MF $(DEPDIR)/hp_max.Tpo -c -o hp_max.lo `test -f '$(ENC_SRC_DIR)/hp_max.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/hp_max.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/hp_max.Tpo $(DEPDIR)/hp_max.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/hp_max.cpp' object='hp_max.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o hp_max.lo `test -f '$(ENC_SRC_DIR)/hp_max.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/hp_max.cpp

inter_36.lo: $(ENC_SRC_DIR)/inter_36.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT inter_36.lo -MD -MP -MF $(DEPDIR)/inter_36.Tpo -c -o inter_36.lo `test -f '$(ENC_SRC_DIR)/inter_36.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/inter_36.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/inter_36.Tpo $(DEPDIR)/inter_36.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/inter_36.cpp' object='inter_36.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o inter_36.lo `test -f '$(ENC_SRC_DIR)/inter_36.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/inter_36.cpp

inter_36_tab.lo: $(ENC_SRC_DIR)/inter_36_tab.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT inter_36_tab.lo -MD -MP -MF $(DEPDIR)/inter_36_tab.Tpo -c -o inter_36_tab.lo `test -f '$(ENC_SRC_DIR)/inter_36_tab.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/inter_36_tab.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/inter_36_tab.Tpo $(DEPDIR)/inter_36_tab.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/inter_36_tab.cpp' object='inter_36_tab.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o inter_36_tab.lo `test -f '$(ENC_SRC_DIR)/inter_36_tab.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/inter_36_tab.cpp

l_abs.lo: $(ENC_SRC_DIR)/l_abs.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT l_abs.lo -MD -MP -MF $(DEPDIR)/l_abs.Tpo -c -o l_abs.lo `test -f '$(ENC_SRC_DIR)/l_abs.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/l_abs.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/l_abs.Tpo $(DEPDIR)/l_abs.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/l_abs.cpp' object='l_abs.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o l_abs.lo `test -f '$(ENC_SRC_DIR)/l_abs.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/l_abs.cpp

lag_wind.lo: $(ENC_SRC_DIR)/lag_wind.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT lag_wind.lo -MD -MP -MF $(DEPDIR)/lag_wind.Tpo -c -o lag_wind.lo `test -f '$(ENC_SRC_DIR)/lag_wind.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/lag_wind.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/lag_wind.Tpo $(DEPDIR)/lag_wind.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/lag_wind.cpp' object='lag_wind.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o lag_wind.lo `test -f '$(ENC_SRC_DIR)/lag_wind.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/lag_wind.cpp

lag_wind_tab.lo: $(ENC_SRC_DIR)/lag_wind_tab.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT lag_wind_tab.lo -MD -MP -MF $(DEPDIR)/lag_wind_tab.Tpo -c -o lag_wind_tab.lo `test -f '$(ENC_SRC_DIR)/lag_wind_tab.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/lag_wind_tab.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/lag_wind_tab.Tpo $(DEPDIR)/lag_wind_tab.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/lag_wind_tab.cpp' object='lag_wind_tab.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o lag_wind_tab.lo `test -f '$(ENC_SRC_DIR)/lag_wind_tab.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/lag_wind_tab.cpp

l_comp.lo: $(ENC_SRC_DIR)/l_comp.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT l_comp.lo -MD -MP -MF $(DEPDIR)/l_comp.Tpo -c -o l_comp.lo `test -f '$(ENC_SRC_DIR)/l_comp.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/l_comp.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/l_comp.Tpo $(DEPDIR)/l_comp.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/l_comp.cpp' object='l_comp.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o l_comp.lo `test -f '$(ENC_SRC_DIR)/l_comp.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/l_comp.cpp

levinson.lo: $(ENC_SRC_DIR)/levinson.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT levinson.lo -MD -MP -MF $(DEPDIR)/levinson.Tpo -c -o levinson.lo `test -f '$(ENC_SRC_DIR)/levinson.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/levinson.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/levinson.Tpo $(DEPDIR)/levinson.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/levinson.cpp' object='levinson.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o levinson.lo `test -f '$(ENC_SRC_DIR)/levinson.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/levinson.cpp

l_extract.lo: $(ENC_SRC_DIR)/l_extract.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT l_extract.lo -MD -MP -MF $(DEPDIR)/l_extract.Tpo -c -o l_extract.lo `test -f '$(ENC_SRC_DIR)/l_extract.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/l_extract.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/l_extract.Tpo $(DEPDIR)/l_extract.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/l_extract.cpp' object='l_extract.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o l_extract.lo `test -f '$(ENC_SRC_DIR)/l_extract.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/l_extract.cpp

lflg_upd.lo: $(ENC_SRC_DIR)/lflg_upd.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT lflg_upd.lo -MD -MP -MF $(DEPDIR)/lflg_upd.Tpo -c -o lflg_upd.lo `test -f '$(ENC_SRC_DIR)/lflg_upd.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/lflg_upd.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/lflg_upd.Tpo $(DEPDIR)/lflg_upd.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/lflg_upd.cpp' object='lflg_upd.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o lflg_upd.lo `test -f '$(ENC_SRC_DIR)/lflg_upd.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/lflg_upd.cpp

l_negate.lo: $(ENC_SRC_DIR)/l_negate.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT l_negate.lo -MD -MP -MF $(DEPDIR)/l_negate.Tpo -c -o l_negate.lo `test -f '$(ENC_SRC_DIR)/l_negate.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/l_negate.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/l_negate.Tpo $(DEPDIR)/l_negate.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/l_negate.cpp' object='l_negate.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o l_negate.lo `test -f '$(ENC_SRC_DIR)/l_negate.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/l_negate.cpp

lpc.lo: $(ENC_SRC_DIR)/lpc.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT lpc.lo -MD -MP -MF $(DEPDIR)/lpc.Tpo -c -o lpc.lo `test -f '$(ENC_SRC_DIR)/lpc.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/lpc.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/lpc.Tpo $(DEPDIR)/lpc.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/lpc.cpp' object='lpc.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o lpc.lo `test -f '$(ENC_SRC_DIR)/lpc.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/lpc.cpp

ol_ltp.lo: $(ENC_SRC_DIR)/ol_ltp.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT ol_ltp.lo -MD -MP -MF $(DEPDIR)/ol_ltp.Tpo -c -o ol_ltp.lo `test -f '$(ENC_SRC_DIR)/ol_ltp.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/ol_ltp.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/ol_ltp.Tpo $(DEPDIR)/ol_ltp.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/ol_ltp.cpp' object='ol_ltp.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o ol_ltp.lo `test -f '$(ENC_SRC_DIR)/ol_ltp.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/ol_ltp.cpp

pitch_fr.lo: $(ENC_SRC_DIR)/pitch_fr.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT pitch_fr.lo -MD -MP -MF $(DEPDIR)/pitch_fr.Tpo -c -o pitch_fr.lo `test -f '$(ENC_SRC_DIR)/pitch_fr.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/pitch_fr.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/pitch_fr.Tpo $(DEPDIR)/pitch_fr.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/pitch_fr.cpp' object='pitch_fr.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o pitch_fr.lo `test -f '$(ENC_SRC_DIR)/pitch_fr.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/pitch_fr.cpp

pitch_ol.lo: $(ENC_SRC_DIR)/pitch_ol.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT pitch_ol.lo -MD -MP -MF $(DEPDIR)/pitch_ol.Tpo -c -o pitch_ol.lo `test -f '$(ENC_SRC_DIR)/pitch_ol.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/pitch_ol.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/pitch_ol.Tpo $(DEPDIR)/pitch_ol.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/pitch_ol.cpp' object='pitch_ol.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o pitch_ol.lo `test -f '$(ENC_SRC_DIR)/pitch_ol.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/pitch_ol.cpp

p_ol_wgh.lo: $(ENC_SRC_DIR)/p_ol_wgh.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT p_ol_wgh.lo -MD -MP -MF $(DEPDIR)/p_ol_wgh.Tpo -c -o p_ol_wgh.lo `test -f '$(ENC_SRC_DIR)/p_ol_wgh.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/p_ol_wgh.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/p_ol_wgh.Tpo $(DEPDIR)/p_ol_wgh.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/p_ol_wgh.cpp' object='p_ol_wgh.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o p_ol_wgh.lo `test -f '$(ENC_SRC_DIR)/p_ol_wgh.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/p_ol_wgh.cpp

pre_big.lo: $(ENC_SRC_DIR)/pre_big.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT pre_big.lo -MD -MP -MF $(DEPDIR)/pre_big.Tpo -c -o pre_big.lo `test -f '$(ENC_SRC_DIR)/pre_big.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/pre_big.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/pre_big.Tpo $(DEPDIR)/pre_big.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/pre_big.cpp' object='pre_big.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o pre_big.lo `test -f '$(ENC_SRC_DIR)/pre_big.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/pre_big.cpp

pre_proc.lo: $(ENC_SRC_DIR)/pre_proc.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT pre_proc.lo -MD -MP -MF $(DEPDIR)/pre_proc.Tpo -c -o pre_proc.lo `test -f '$(ENC_SRC_DIR)/pre_proc.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/pre_proc.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/pre_proc.Tpo $(DEPDIR)/pre_proc.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/pre_proc.cpp' object='pre_proc.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o pre_proc.lo `test -f '$(ENC_SRC_DIR)/pre_proc.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/pre_proc.cpp

prm2bits.lo: $(ENC_SRC_DIR)/prm2bits.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT prm2bits.lo -MD -MP -MF $(DEPDIR)/prm2bits.Tpo -c -o prm2bits.lo `test -f '$(ENC_SRC_DIR)/prm2bits.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/prm2bits.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/prm2bits.Tpo $(DEPDIR)/prm2bits.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/prm2bits.cpp' object='prm2bits.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o prm2bits.lo `test -f '$(ENC_SRC_DIR)/prm2bits.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/prm2bits.cpp

qgain475.lo: $(ENC_SRC_DIR)/qgain475.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT qgain475.lo -MD -MP -MF $(DEPDIR)/qgain475.Tpo -c -o qgain475.lo `test -f '$(ENC_SRC_DIR)/qgain475.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/qgain475.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/qgain475.Tpo $(DEPDIR)/qgain475.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/qgain475.cpp' object='qgain475.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o qgain475.lo `test -f '$(ENC_SRC_DIR)/qgain475.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/qgain475.cpp

qgain795.lo: $(ENC_SRC_DIR)/qgain795.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT qgain795.lo -MD -MP -MF $(DEPDIR)/qgain795.Tpo -c -o qgain795.lo `test -f '$(ENC_SRC_DIR)/qgain795.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/qgain795.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/qgain795.Tpo $(DEPDIR)/qgain795.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/qgain795.cpp' object='qgain795.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o qgain795.lo `test -f '$(ENC_SRC_DIR)/qgain795.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/qgain795.cpp

q_gain_c.lo: $(ENC_SRC_DIR)/q_gain_c.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT q_gain_c.lo -MD -MP -MF $(DEPDIR)/q_gain_c.Tpo -c -o q_gain_c.lo `test -f '$(ENC_SRC_DIR)/q_gain_c.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/q_gain_c.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/q_gain_c.Tpo $(DEPDIR)/q_gain_c.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/q_gain_c.cpp' object='q_gain_c.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o q_gain_c.lo `test -f '$(ENC_SRC_DIR)/q_gain_c.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/q_gain_c.cpp

q_gain_p.lo: $(ENC_SRC_DIR)/q_gain_p.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT q_gain_p.lo -MD -MP -MF $(DEPDIR)/q_gain_p.Tpo -c -o q_gain_p.lo `test -f '$(ENC_SRC_DIR)/q_gain_p.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/q_gain_p.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/q_gain_p.Tpo $(DEPDIR)/q_gain_p.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/q_gain_p.cpp' object='q_gain_p.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o q_gain_p.lo `test -f '$(ENC_SRC_DIR)/q_gain_p.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/q_gain_p.cpp

qua_gain.lo: $(ENC_SRC_DIR)/qua_gain.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT qua_gain.lo -MD -MP -MF $(DEPDIR)/qua_gain.Tpo -c -o qua_gain.lo `test -f '$(ENC_SRC_DIR)/qua_gain.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/qua_gain.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/qua_gain.Tpo $(DEPDIR)/qua_gain.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/qua_gain.cpp' object='qua_gain.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o qua_gain.lo `test -f '$(ENC_SRC_DIR)/qua_gain.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/qua_gain.cpp

s10_8pf.lo: $(ENC_SRC_DIR)/s10_8pf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT s10_8pf.lo -MD -MP -MF $(DEPDIR)/s10_8pf.Tpo -c -o s10_8pf.lo `test -f '$(ENC_SRC_DIR)/s10_8pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/s10_8pf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/s10_8pf.Tpo $(DEPDIR)/s10_8pf.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/s10_8pf.cpp' object='s10_8pf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o s10_8pf.lo `test -f '$(ENC_SRC_DIR)/s10_8pf.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/s10_8pf.cpp

set_sign.lo: $(ENC_SRC_DIR)/set_sign.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT set_sign.lo -MD -MP -MF $(DEPDIR)/set_sign.Tpo -c -o set_sign.lo `test -f '$(ENC_SRC_DIR)/set_sign.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/set_sign.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/set_sign.Tpo $(DEPDIR)/set_sign.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/set_sign.cpp' object='set_sign.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o set_sign.lo `test -f '$(ENC_SRC_DIR)/set_sign.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/set_sign.cpp

sid_sync.lo: $(ENC_SRC_DIR)/sid_sync.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT sid_sync.lo -MD -MP -MF $(DEPDIR)/sid_sync.Tpo -c -o sid_sync.lo `test -f '$(ENC_SRC_DIR)/sid_sync.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/sid_sync.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/sid_sync.Tpo $(DEPDIR)/sid_sync.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/sid_sync.cpp' object='sid_sync.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o sid_sync.lo `test -f '$(ENC_SRC_DIR)/sid_sync.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/sid_sync.cpp

sp_enc.lo: $(ENC_SRC_DIR)/sp_enc.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT sp_enc.lo -MD -MP -MF $(DEPDIR)/sp_enc.Tpo -c -o sp_enc.lo `test -f '$(ENC_SRC_DIR)/sp_enc.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/sp_enc.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/sp_enc.Tpo $(DEPDIR)/sp_enc.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/sp_enc.cpp' object='sp_enc.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o sp_enc.lo `test -f '$(ENC_SRC_DIR)/sp_enc.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/sp_enc.cpp

spreproc.lo: $(ENC_SRC_DIR)/spreproc.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT spreproc.lo -MD -MP -MF $(DEPDIR)/spreproc.Tpo -c -o spreproc.lo `test -f '$(ENC_SRC_DIR)/spreproc.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/spreproc.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/spreproc.Tpo $(DEPDIR)/spreproc.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/spreproc.cpp' object='spreproc.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o spreproc.lo `test -f '$(ENC_SRC_DIR)/spreproc.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/spreproc.cpp

spstproc.lo: $(ENC_SRC_DIR)/spstproc.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT spstproc.lo -MD -MP -MF $(DEPDIR)/spstproc.Tpo -c -o spstproc.lo `test -f '$(ENC_SRC_DIR)/spstproc.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/spstproc.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/spstproc.Tpo $(DEPDIR)/spstproc.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/spstproc.cpp' object='spstproc.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o spstproc.lo `test -f '$(ENC_SRC_DIR)/spstproc.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/spstproc.cpp

ton_stab.lo: $(ENC_SRC_DIR)/ton_stab.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT ton_stab.lo -MD -MP -MF $(DEPDIR)/ton_stab.Tpo -c -o ton_stab.lo `test -f '$(ENC_SRC_DIR)/ton_stab.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/ton_stab.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/ton_stab.Tpo $(DEPDIR)/ton_stab.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/ton_stab.cpp' object='ton_stab.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o ton_stab.lo `test -f '$(ENC_SRC_DIR)/ton_stab.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/ton_stab.cpp

vad1.lo: $(ENC_SRC_DIR)/vad1.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT vad1.lo -MD -MP -MF $(DEPDIR)/vad1.Tpo -c -o vad1.lo `test -f '$(ENC_SRC_DIR)/vad1.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/vad1.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/vad1.Tpo $(DEPDIR)/vad1.Plo
#	$(AM_V_CXX)source='$(ENC_SRC_DIR)/vad1.cpp' object='vad1.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o vad1.lo `test -f '$(ENC_SRC_DIR)/vad1.cpp' || echo '$(srcdir)/'`$(ENC_SRC_DIR)/vad1.cpp

add.lo: $(COMMON_SRC_DIR)/add.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT add.lo -MD -MP -MF $(DEPDIR)/add.Tpo -c -o add.lo `test -f '$(COMMON_SRC_DIR)/add.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/add.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/add.Tpo $(DEPDIR)/add.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/add.cpp' object='add.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o add.lo `test -f '$(COMMON_SRC_DIR)/add.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/add.cpp

az_lsp.lo: $(COMMON_SRC_DIR)/az_lsp.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT az_lsp.lo -MD -MP -MF $(DEPDIR)/az_lsp.Tpo -c -o az_lsp.lo `test -f '$(COMMON_SRC_DIR)/az_lsp.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/az_lsp.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/az_lsp.Tpo $(DEPDIR)/az_lsp.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/az_lsp.cpp' object='az_lsp.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o az_lsp.lo `test -f '$(COMMON_SRC_DIR)/az_lsp.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/az_lsp.cpp

bitno_tab.lo: $(COMMON_SRC_DIR)/bitno_tab.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT bitno_tab.lo -MD -MP -MF $(DEPDIR)/bitno_tab.Tpo -c -o bitno_tab.lo `test -f '$(COMMON_SRC_DIR)/bitno_tab.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/bitno_tab.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/bitno_tab.Tpo $(DEPDIR)/bitno_tab.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/bitno_tab.cpp' object='bitno_tab.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o bitno_tab.lo `test -f '$(COMMON_SRC_DIR)/bitno_tab.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/bitno_tab.cpp

bitreorder_tab.lo: $(COMMON_SRC_DIR)/bitreorder_tab.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT bitreorder_tab.lo -MD -MP -MF $(DEPDIR)/bitreorder_tab.Tpo -c -o bitreorder_tab.lo `test -f '$(COMMON_SRC_DIR)/bitreorder_tab.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/bitreorder_tab.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/bitreorder_tab.Tpo $(DEPDIR)/bitreorder_tab.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/bitreorder_tab.cpp' object='bitreorder_tab.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o bitreorder_tab.lo `test -f '$(COMMON_SRC_DIR)/bitreorder_tab.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/bitreorder_tab.cpp

c2_9pf_tab.lo: $(COMMON_SRC_DIR)/c2_9pf_tab.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT c2_9pf_tab.lo -MD -MP -MF $(DEPDIR)/c2_9pf_tab.Tpo -c -o c2_9pf_tab.lo `test -f '$(COMMON_SRC_DIR)/c2_9pf_tab.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/c2_9pf_tab.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/c2_9pf_tab.Tpo $(DEPDIR)/c2_9pf_tab.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/c2_9pf_tab.cpp' object='c2_9pf_tab.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o c2_9pf_tab.lo `test -f '$(COMMON_SRC_DIR)/c2_9pf_tab.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/c2_9pf_tab.cpp

div_s.lo: $(COMMON_SRC_DIR)/div_s.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT div_s.lo -MD -MP -MF $(DEPDIR)/div_s.Tpo -c -o div_s.lo `test -f '$(COMMON_SRC_DIR)/div_s.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/div_s.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/div_s.Tpo $(DEPDIR)/div_s.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/div_s.cpp' object='div_s.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o div_s.lo `test -f '$(COMMON_SRC_DIR)/div_s.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/div_s.cpp

extract_h.lo: $(COMMON_SRC_DIR)/extract_h.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT extract_h.lo -MD -MP -MF $(DEPDIR)/extract_h.Tpo -c -o extract_h.lo `test -f '$(COMMON_SRC_DIR)/extract_h.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/extract_h.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/extract_h.Tpo $(DEPDIR)/extract_h.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/extract_h.cpp' object='extract_h.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o extract_h.lo `test -f '$(COMMON_SRC_DIR)/extract_h.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/extract_h.cpp

extract_l.lo: $(COMMON_SRC_DIR)/extract_l.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT extract_l.lo -MD -MP -MF $(DEPDIR)/extract_l.Tpo -c -o extract_l.lo `test -f '$(COMMON_SRC_DIR)/extract_l.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/extract_l.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/extract_l.Tpo $(DEPDIR)/extract_l.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/extract_l.cpp' object='extract_l.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o extract_l.lo `test -f '$(COMMON_SRC_DIR)/extract_l.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/extract_l.cpp

gains_tbl.lo: $(COMMON_SRC_DIR)/gains_tbl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gains_tbl.lo -MD -MP -MF $(DEPDIR)/gains_tbl.Tpo -c -o gains_tbl.lo `test -f '$(COMMON_SRC_DIR)/gains_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/gains_tbl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/gains_tbl.Tpo $(DEPDIR)/gains_tbl.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/gains_tbl.cpp' object='gains_tbl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gains_tbl.lo `test -f '$(COMMON_SRC_DIR)/gains_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/gains_tbl.cpp

gc_pred.lo: $(COMMON_SRC_DIR)/gc_pred.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gc_pred.lo -MD -MP -MF $(DEPDIR)/gc_pred.Tpo -c -o gc_pred.lo `test -f '$(COMMON_SRC_DIR)/gc_pred.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/gc_pred.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/gc_pred.Tpo $(DEPDIR)/gc_pred.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/gc_pred.cpp' object='gc_pred.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gc_pred.lo `test -f '$(COMMON_SRC_DIR)/gc_pred.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/gc_pred.cpp

get_const_tbls.lo: $(COMMON_SRC_DIR)/get_const_tbls.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT get_const_tbls.lo -MD -MP -MF $(DEPDIR)/get_const_tbls.Tpo -c -o get_const_tbls.lo `test -f '$(COMMON_SRC_DIR)/get_const_tbls.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/get_const_tbls.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/get_const_tbls.Tpo $(DEPDIR)/get_const_tbls.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/get_const_tbls.cpp' object='get_const_tbls.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o get_const_tbls.lo `test -f '$(COMMON_SRC_DIR)/get_const_tbls.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/get_const_tbls.cpp

gmed_n.lo: $(COMMON_SRC_DIR)/gmed_n.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gmed_n.lo -MD -MP -MF $(DEPDIR)/gmed_n.Tpo -c -o gmed_n.lo `test -f '$(COMMON_SRC_DIR)/gmed_n.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/gmed_n.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/gmed_n.Tpo $(DEPDIR)/gmed_n.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/gmed_n.cpp' object='gmed_n.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gmed_n.lo `test -f '$(COMMON_SRC_DIR)/gmed_n.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/gmed_n.cpp

gray_tbl.lo: $(COMMON_SRC_DIR)/gray_tbl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gray_tbl.lo -MD -MP -MF $(DEPDIR)/gray_tbl.Tpo -c -o gray_tbl.lo `test -f '$(COMMON_SRC_DIR)/gray_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/gray_tbl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/gray_tbl.Tpo $(DEPDIR)/gray_tbl.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/gray_tbl.cpp' object='gray_tbl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gray_tbl.lo `test -f '$(COMMON_SRC_DIR)/gray_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/gray_tbl.cpp

grid_tbl.lo: $(COMMON_SRC_DIR)/grid_tbl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT grid_tbl.lo -MD -MP -MF $(DEPDIR)/grid_tbl.Tpo -c -o grid_tbl.lo `test -f '$(COMMON_SRC_DIR)/grid_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/grid_tbl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/grid_tbl.Tpo $(DEPDIR)/grid_tbl.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/grid_tbl.cpp' object='grid_tbl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o grid_tbl.lo `test -f '$(COMMON_SRC_DIR)/grid_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/grid_tbl.cpp

int_lpc.lo: $(COMMON_SRC_DIR)/int_lpc.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT int_lpc.lo -MD -MP -MF $(DEPDIR)/int_lpc.Tpo -c -o int_lpc.lo `test -f '$(COMMON_SRC_DIR)/int_lpc.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/int_lpc.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/int_lpc.Tpo $(DEPDIR)/int_lpc.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/int_lpc.cpp' object='int_lpc.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o int_lpc.lo `test -f '$(COMMON_SRC_DIR)/int_lpc.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/int_lpc.cpp

inv_sqrt.lo: $(COMMON_SRC_DIR)/inv_sqrt.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT inv_sqrt.lo -MD -MP -MF $(DEPDIR)/inv_sqrt.Tpo -c -o inv_sqrt.lo `test -f '$(COMMON_SRC_DIR)/inv_sqrt.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/inv_sqrt.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/inv_sqrt.Tpo $(DEPDIR)/inv_sqrt.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/inv_sqrt.cpp' object='inv_sqrt.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o inv_sqrt.lo `test -f '$(COMMON_SRC_DIR)/inv_sqrt.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/inv_sqrt.cpp

inv_sqrt_tbl.lo: $(COMMON_SRC_DIR)/inv_sqrt_tbl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT inv_sqrt_tbl.lo -MD -MP -MF $(DEPDIR)/inv_sqrt_tbl.Tpo -c -o inv_sqrt_tbl.lo `test -f '$(COMMON_SRC_DIR)/inv_sqrt_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/inv_sqrt_tbl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/inv_sqrt_tbl.Tpo $(DEPDIR)/inv_sqrt_tbl.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/inv_sqrt_tbl.cpp' object='inv_sqrt_tbl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o inv_sqrt_tbl.lo `test -f '$(COMMON_SRC_DIR)/inv_sqrt_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/inv_sqrt_tbl.cpp

l_deposit_h.lo: $(COMMON_SRC_DIR)/l_deposit_h.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT l_deposit_h.lo -MD -MP -MF $(DEPDIR)/l_deposit_h.Tpo -c -o l_deposit_h.lo `test -f '$(COMMON_SRC_DIR)/l_deposit_h.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/l_deposit_h.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/l_deposit_h.Tpo $(DEPDIR)/l_deposit_h.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/l_deposit_h.cpp' object='l_deposit_h.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o l_deposit_h.lo `test -f '$(COMMON_SRC_DIR)/l_deposit_h.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/l_deposit_h.cpp

l_deposit_l.lo: $(COMMON_SRC_DIR)/l_deposit_l.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT l_deposit_l.lo -MD -MP -MF $(DEPDIR)/l_deposit_l.Tpo -c -o l_deposit_l.lo `test -f '$(COMMON_SRC_DIR)/l_deposit_l.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/l_deposit_l.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/l_deposit_l.Tpo $(DEPDIR)/l_deposit_l.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/l_deposit_l.cpp' object='l_deposit_l.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o l_deposit_l.lo `test -f '$(COMMON_SRC_DIR)/l_deposit_l.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/l_deposit_l.cpp

log2.lo: $(COMMON_SRC_DIR)/log2.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT log2.lo -MD -MP -MF $(DEPDIR)/log2.Tpo -c -o log2.lo `test -f '$(COMMON_SRC_DIR)/log2.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/log2.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/log2.Tpo $(DEPDIR)/log2.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/log2.cpp' object='log2.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o log2.lo `test -f '$(COMMON_SRC_DIR)/log2.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/log2.cpp

log2_norm.lo: $(COMMON_SRC_DIR)/log2_norm.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT log2_norm.lo -MD -MP -MF $(DEPDIR)/log2_norm.Tpo -c -o log2_norm.lo `test -f '$(COMMON_SRC_DIR)/log2_norm.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/log2_norm.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/log2_norm.Tpo $(DEPDIR)/log2_norm.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/log2_norm.cpp' object='log2_norm.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o log2_norm.lo `test -f '$(COMMON_SRC_DIR)/log2_norm.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/log2_norm.cpp

log2_tbl.lo: $(COMMON_SRC_DIR)/log2_tbl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT log2_tbl.lo -MD -MP -MF $(DEPDIR)/log2_tbl.Tpo -c -o log2_tbl.lo `test -f '$(COMMON_SRC_DIR)/log2_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/log2_tbl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/log2_tbl.Tpo $(DEPDIR)/log2_tbl.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/log2_tbl.cpp' object='log2_tbl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o log2_tbl.lo `test -f '$(COMMON_SRC_DIR)/log2_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/log2_tbl.cpp

lsfwt.lo: $(COMMON_SRC_DIR)/lsfwt.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT lsfwt.lo -MD -MP -MF $(DEPDIR)/lsfwt.Tpo -c -o lsfwt.lo `test -f '$(COMMON_SRC_DIR)/lsfwt.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/lsfwt.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/lsfwt.Tpo $(DEPDIR)/lsfwt.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/lsfwt.cpp' object='lsfwt.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o lsfwt.lo `test -f '$(COMMON_SRC_DIR)/lsfwt.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/lsfwt.cpp

l_shr_r.lo: $(COMMON_SRC_DIR)/l_shr_r.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT l_shr_r.lo -MD -MP -MF $(DEPDIR)/l_shr_r.Tpo -c -o l_shr_r.lo `test -f '$(COMMON_SRC_DIR)/l_shr_r.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/l_shr_r.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/l_shr_r.Tpo $(DEPDIR)/l_shr_r.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/l_shr_r.cpp' object='l_shr_r.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o l_shr_r.lo `test -f '$(COMMON_SRC_DIR)/l_shr_r.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/l_shr_r.cpp

lsp_az.lo: $(COMMON_SRC_DIR)/lsp_az.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT lsp_az.lo -MD -MP -MF $(DEPDIR)/lsp_az.Tpo -c -o lsp_az.lo `test -f '$(COMMON_SRC_DIR)/lsp_az.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/lsp_az.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/lsp_az.Tpo $(DEPDIR)/lsp_az.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/lsp_az.cpp' object='lsp_az.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o lsp_az.lo `test -f '$(COMMON_SRC_DIR)/lsp_az.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/lsp_az.cpp

lsp.lo: $(COMMON_SRC_DIR)/lsp.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT lsp.lo -MD -MP -MF $(DEPDIR)/lsp.Tpo -c -o lsp.lo `test -f '$(COMMON_SRC_DIR)/lsp.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/lsp.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/lsp.Tpo $(DEPDIR)/lsp.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/lsp.cpp' object='lsp.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o lsp.lo `test -f '$(COMMON_SRC_DIR)/lsp.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/lsp.cpp

lsp_lsf.lo: $(COMMON_SRC_DIR)/lsp_lsf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT lsp_lsf.lo -MD -MP -MF $(DEPDIR)/lsp_lsf.Tpo -c -o lsp_lsf.lo `test -f '$(COMMON_SRC_DIR)/lsp_lsf.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/lsp_lsf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/lsp_lsf.Tpo $(DEPDIR)/lsp_lsf.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/lsp_lsf.cpp' object='lsp_lsf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o lsp_lsf.lo `test -f '$(COMMON_SRC_DIR)/lsp_lsf.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/lsp_lsf.cpp

lsp_lsf_tbl.lo: $(COMMON_SRC_DIR)/lsp_lsf_tbl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT lsp_lsf_tbl.lo -MD -MP -MF $(DEPDIR)/lsp_lsf_tbl.Tpo -c -o lsp_lsf_tbl.lo `test -f '$(COMMON_SRC_DIR)/lsp_lsf_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/lsp_lsf_tbl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/lsp_lsf_tbl.Tpo $(DEPDIR)/lsp_lsf_tbl.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/lsp_lsf_tbl.cpp' object='lsp_lsf_tbl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o lsp_lsf_tbl.lo `test -f '$(COMMON_SRC_DIR)/lsp_lsf_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/lsp_lsf_tbl.cpp

lsp_tab.lo: $(COMMON_SRC_DIR)/lsp_tab.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT lsp_tab.lo -MD -MP -MF $(DEPDIR)/lsp_tab.Tpo -c -o lsp_tab.lo `test -f '$(COMMON_SRC_DIR)/lsp_tab.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/lsp_tab.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/lsp_tab.Tpo $(DEPDIR)/lsp_tab.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/lsp_tab.cpp' object='lsp_tab.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o lsp_tab.lo `test -f '$(COMMON_SRC_DIR)/lsp_tab.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/lsp_tab.cpp

mult_r.lo: $(COMMON_SRC_DIR)/mult_r.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT mult_r.lo -MD -MP -MF $(DEPDIR)/mult_r.Tpo -c -o mult_r.lo `test -f '$(COMMON_SRC_DIR)/mult_r.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/mult_r.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/mult_r.Tpo $(DEPDIR)/mult_r.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/mult_r.cpp' object='mult_r.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o mult_r.lo `test -f '$(COMMON_SRC_DIR)/mult_r.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/mult_r.cpp

negate.lo: $(COMMON_SRC_DIR)/negate.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT negate.lo -MD -MP -MF $(DEPDIR)/negate.Tpo -c -o negate.lo `test -f '$(COMMON_SRC_DIR)/negate.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/negate.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/negate.Tpo $(DEPDIR)/negate.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/negate.cpp' object='negate.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o negate.lo `test -f '$(COMMON_SRC_DIR)/negate.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/negate.cpp

norm_l.lo: $(COMMON_SRC_DIR)/norm_l.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT norm_l.lo -MD -MP -MF $(DEPDIR)/norm_l.Tpo -c -o norm_l.lo `test -f '$(COMMON_SRC_DIR)/norm_l.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/norm_l.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/norm_l.Tpo $(DEPDIR)/norm_l.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/norm_l.cpp' object='norm_l.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o norm_l.lo `test -f '$(COMMON_SRC_DIR)/norm_l.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/norm_l.cpp

norm_s.lo: $(COMMON_SRC_DIR)/norm_s.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT norm_s.lo -MD -MP -MF $(DEPDIR)/norm_s.Tpo -c -o norm_s.lo `test -f '$(COMMON_SRC_DIR)/norm_s.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/norm_s.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/norm_s.Tpo $(DEPDIR)/norm_s.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/norm_s.cpp' object='norm_s.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o norm_s.lo `test -f '$(COMMON_SRC_DIR)/norm_s.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/norm_s.cpp

overflow_tbl.lo: $(COMMON_SRC_DIR)/overflow_tbl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT overflow_tbl.lo -MD -MP -MF $(DEPDIR)/overflow_tbl.Tpo -c -o overflow_tbl.lo `test -f '$(COMMON_SRC_DIR)/overflow_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/overflow_tbl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/overflow_tbl.Tpo $(DEPDIR)/overflow_tbl.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/overflow_tbl.cpp' object='overflow_tbl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o overflow_tbl.lo `test -f '$(COMMON_SRC_DIR)/overflow_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/overflow_tbl.cpp

ph_disp_tab.lo: $(COMMON_SRC_DIR)/ph_disp_tab.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT ph_disp_tab.lo -MD -MP -MF $(DEPDIR)/ph_disp_tab.Tpo -c -o ph_disp_tab.lo `test -f '$(COMMON_SRC_DIR)/ph_disp_tab.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/ph_disp_tab.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/ph_disp_tab.Tpo $(DEPDIR)/ph_disp_tab.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/ph_disp_tab.cpp' object='ph_disp_tab.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o ph_disp_tab.lo `test -f '$(COMMON_SRC_DIR)/ph_disp_tab.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/ph_disp_tab.cpp

pow2.lo: $(COMMON_SRC_DIR)/pow2.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT pow2.lo -MD -MP -MF $(DEPDIR)/pow2.Tpo -c -o pow2.lo `test -f '$(COMMON_SRC_DIR)/pow2.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/pow2.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/pow2.Tpo $(DEPDIR)/pow2.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/pow2.cpp' object='pow2.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o pow2.lo `test -f '$(COMMON_SRC_DIR)/pow2.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/pow2.cpp

pow2_tbl.lo: $(COMMON_SRC_DIR)/pow2_tbl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT pow2_tbl.lo -MD -MP -MF $(DEPDIR)/pow2_tbl.Tpo -c -o pow2_tbl.lo `test -f '$(COMMON_SRC_DIR)/pow2_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/pow2_tbl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/pow2_tbl.Tpo $(DEPDIR)/pow2_tbl.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/pow2_tbl.cpp' object='pow2_tbl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o pow2_tbl.lo `test -f '$(COMMON_SRC_DIR)/pow2_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/pow2_tbl.cpp

pred_lt.lo: $(COMMON_SRC_DIR)/pred_lt.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT pred_lt.lo -MD -MP -MF $(DEPDIR)/pred_lt.Tpo -c -o pred_lt.lo `test -f '$(COMMON_SRC_DIR)/pred_lt.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/pred_lt.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/pred_lt.Tpo $(DEPDIR)/pred_lt.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/pred_lt.cpp' object='pred_lt.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o pred_lt.lo `test -f '$(COMMON_SRC_DIR)/pred_lt.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/pred_lt.cpp

q_plsf_3.lo: $(COMMON_SRC_DIR)/q_plsf_3.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT q_plsf_3.lo -MD -MP -MF $(DEPDIR)/q_plsf_3.Tpo -c -o q_plsf_3.lo `test -f '$(COMMON_SRC_DIR)/q_plsf_3.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/q_plsf_3.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/q_plsf_3.Tpo $(DEPDIR)/q_plsf_3.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/q_plsf_3.cpp' object='q_plsf_3.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o q_plsf_3.lo `test -f '$(COMMON_SRC_DIR)/q_plsf_3.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/q_plsf_3.cpp

q_plsf_3_tbl.lo: $(COMMON_SRC_DIR)/q_plsf_3_tbl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT q_plsf_3_tbl.lo -MD -MP -MF $(DEPDIR)/q_plsf_3_tbl.Tpo -c -o q_plsf_3_tbl.lo `test -f '$(COMMON_SRC_DIR)/q_plsf_3_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/q_plsf_3_tbl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/q_plsf_3_tbl.Tpo $(DEPDIR)/q_plsf_3_tbl.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/q_plsf_3_tbl.cpp' object='q_plsf_3_tbl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o q_plsf_3_tbl.lo `test -f '$(COMMON_SRC_DIR)/q_plsf_3_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/q_plsf_3_tbl.cpp

q_plsf_5.lo: $(COMMON_SRC_DIR)/q_plsf_5.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT q_plsf_5.lo -MD -MP -MF $(DEPDIR)/q_plsf_5.Tpo -c -o q_plsf_5.lo `test -f '$(COMMON_SRC_DIR)/q_plsf_5.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/q_plsf_5.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/q_plsf_5.Tpo $(DEPDIR)/q_plsf_5.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/q_plsf_5.cpp' object='q_plsf_5.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o q_plsf_5.lo `test -f '$(COMMON_SRC_DIR)/q_plsf_5.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/q_plsf_5.cpp

q_plsf_5_tbl.lo: $(COMMON_SRC_DIR)/q_plsf_5_tbl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT q_plsf_5_tbl.lo -MD -MP -MF $(DEPDIR)/q_plsf_5_tbl.Tpo -c -o q_plsf_5_tbl.lo `test -f '$(COMMON_SRC_DIR)/q_plsf_5_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/q_plsf_5_tbl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/q_plsf_5_tbl.Tpo $(DEPDIR)/q_plsf_5_tbl.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/q_plsf_5_tbl.cpp' object='q_plsf_5_tbl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o q_plsf_5_tbl.lo `test -f '$(COMMON_SRC_DIR)/q_plsf_5_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/q_plsf_5_tbl.cpp

q_plsf.lo: $(COMMON_SRC_DIR)/q_plsf.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT q_plsf.lo -MD -MP -MF $(DEPDIR)/q_plsf.Tpo -c -o q_plsf.lo `test -f '$(COMMON_SRC_DIR)/q_plsf.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/q_plsf.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/q_plsf.Tpo $(DEPDIR)/q_plsf.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/q_plsf.cpp' object='q_plsf.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o q_plsf.lo `test -f '$(COMMON_SRC_DIR)/q_plsf.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/q_plsf.cpp

qua_gain_tbl.lo: $(COMMON_SRC_DIR)/qua_gain_tbl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT qua_gain_tbl.lo -MD -MP -MF $(DEPDIR)/qua_gain_tbl.Tpo -c -o qua_gain_tbl.lo `test -f '$(COMMON_SRC_DIR)/qua_gain_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/qua_gain_tbl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/qua_gain_tbl.Tpo $(DEPDIR)/qua_gain_tbl.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/qua_gain_tbl.cpp' object='qua_gain_tbl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o qua_gain_tbl.lo `test -f '$(COMMON_SRC_DIR)/qua_gain_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/qua_gain_tbl.cpp

reorder.lo: $(COMMON_SRC_DIR)/reorder.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT reorder.lo -MD -MP -MF $(DEPDIR)/reorder.Tpo -c -o reorder.lo `test -f '$(COMMON_SRC_DIR)/reorder.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/reorder.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/reorder.Tpo $(DEPDIR)/reorder.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/reorder.cpp' object='reorder.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o reorder.lo `test -f '$(COMMON_SRC_DIR)/reorder.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/reorder.cpp

residu.lo: $(COMMON_SRC_DIR)/residu.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT residu.lo -MD -MP -MF $(DEPDIR)/residu.Tpo -c -o residu.lo `test -f '$(COMMON_SRC_DIR)/residu.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/residu.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/residu.Tpo $(DEPDIR)/residu.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/residu.cpp' object='residu.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o residu.lo `test -f '$(COMMON_SRC_DIR)/residu.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/residu.cpp

round.lo: $(COMMON_SRC_DIR)/round.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT round.lo -MD -MP -MF $(DEPDIR)/round.Tpo -c -o round.lo `test -f '$(COMMON_SRC_DIR)/round.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/round.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/round.Tpo $(DEPDIR)/round.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/round.cpp' object='round.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o round.lo `test -f '$(COMMON_SRC_DIR)/round.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/round.cpp

set_zero.lo: $(COMMON_SRC_DIR)/set_zero.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT set_zero.lo -MD -MP -MF $(DEPDIR)/set_zero.Tpo -c -o set_zero.lo `test -f '$(COMMON_SRC_DIR)/set_zero.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/set_zero.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/set_zero.Tpo $(DEPDIR)/set_zero.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/set_zero.cpp' object='set_zero.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o set_zero.lo `test -f '$(COMMON_SRC_DIR)/set_zero.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/set_zero.cpp

shr.lo: $(COMMON_SRC_DIR)/shr.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT shr.lo -MD -MP -MF $(DEPDIR)/shr.Tpo -c -o shr.lo `test -f '$(COMMON_SRC_DIR)/shr.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/shr.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/shr.Tpo $(DEPDIR)/shr.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/shr.cpp' object='shr.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o shr.lo `test -f '$(COMMON_SRC_DIR)/shr.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/shr.cpp

shr_r.lo: $(COMMON_SRC_DIR)/shr_r.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT shr_r.lo -MD -MP -MF $(DEPDIR)/shr_r.Tpo -c -o shr_r.lo `test -f '$(COMMON_SRC_DIR)/shr_r.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/shr_r.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/shr_r.Tpo $(DEPDIR)/shr_r.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/shr_r.cpp' object='shr_r.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o shr_r.lo `test -f '$(COMMON_SRC_DIR)/shr_r.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/shr_r.cpp

sqrt_l.lo: $(COMMON_SRC_DIR)/sqrt_l.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT sqrt_l.lo -MD -MP -MF $(DEPDIR)/sqrt_l.Tpo -c -o sqrt_l.lo `test -f '$(COMMON_SRC_DIR)/sqrt_l.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/sqrt_l.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/sqrt_l.Tpo $(DEPDIR)/sqrt_l.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/sqrt_l.cpp' object='sqrt_l.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o sqrt_l.lo `test -f '$(COMMON_SRC_DIR)/sqrt_l.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/sqrt_l.cpp

sqrt_l_tbl.lo: $(COMMON_SRC_DIR)/sqrt_l_tbl.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT sqrt_l_tbl.lo -MD -MP -MF $(DEPDIR)/sqrt_l_tbl.Tpo -c -o sqrt_l_tbl.lo `test -f '$(COMMON_SRC_DIR)/sqrt_l_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/sqrt_l_tbl.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/sqrt_l_tbl.Tpo $(DEPDIR)/sqrt_l_tbl.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/sqrt_l_tbl.cpp' object='sqrt_l_tbl.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o sqrt_l_tbl.lo `test -f '$(COMMON_SRC_DIR)/sqrt_l_tbl.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/sqrt_l_tbl.cpp

sub.lo: $(COMMON_SRC_DIR)/sub.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT sub.lo -MD -MP -MF $(DEPDIR)/sub.Tpo -c -o sub.lo `test -f '$(COMMON_SRC_DIR)/sub.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/sub.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/sub.Tpo $(DEPDIR)/sub.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/sub.cpp' object='sub.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o sub.lo `test -f '$(COMMON_SRC_DIR)/sub.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/sub.cpp

syn_filt.lo: $(COMMON_SRC_DIR)/syn_filt.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT syn_filt.lo -MD -MP -MF $(DEPDIR)/syn_filt.Tpo -c -o syn_filt.lo `test -f '$(COMMON_SRC_DIR)/syn_filt.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/syn_filt.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/syn_filt.Tpo $(DEPDIR)/syn_filt.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/syn_filt.cpp' object='syn_filt.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o syn_filt.lo `test -f '$(COMMON_SRC_DIR)/syn_filt.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/syn_filt.cpp

weight_a.lo: $(COMMON_SRC_DIR)/weight_a.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT weight_a.lo -MD -MP -MF $(DEPDIR)/weight_a.Tpo -c -o weight_a.lo `test -f '$(COMMON_SRC_DIR)/weight_a.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/weight_a.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/weight_a.Tpo $(DEPDIR)/weight_a.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/weight_a.cpp' object='weight_a.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o weight_a.lo `test -f '$(COMMON_SRC_DIR)/weight_a.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/weight_a.cpp

window_tab.lo: $(COMMON_SRC_DIR)/window_tab.cpp
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT window_tab.lo -MD -MP -MF $(DEPDIR)/window_tab.Tpo -c -o window_tab.lo `test -f '$(COMMON_SRC_DIR)/window_tab.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/window_tab.cpp
	$(AM_V_at)$(am__mv) $(DEPDIR)/window_tab.Tpo $(DEPDIR)/window_tab.Plo
#	$(AM_V_CXX)source='$(COMMON_SRC_DIR)/window_tab.cpp' object='window_tab.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o window_tab.lo `test -f '$(COMMON_SRC_DIR)/window_tab.cpp' || echo '$(srcdir)/'`$(COMMON_SRC_DIR)/window_tab.cpp

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-pkgconfigDATA: $(pkgconfig_DATA)
	@$(NORMAL_INSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgconfigdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pkgconfigdir)" || exit $$?; \
	done

uninstall-pkgconfigDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgconfigdir)'; $(am__uninstall_files_from_dir)
install-amrnbincludeHEADERS: $(amrnbinclude_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(amrnbinclude_HEADERS)'; test -n "$(amrnbincludedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(amrnbincludedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(amrnbincludedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(amrnbincludedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(amrnbincludedir)" || exit $$?; \
	done

uninstall-amrnbincludeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(amrnbinclude_HEADERS)'; test -n "$(amrnbincludedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(amrnbincludedir)'; $(am__uninstall_files_from_dir)

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(LTLIBRARIES) $(DATA) $(HEADERS)
installdirs:
	for dir in "$(DESTDIR)$(libdir)" "$(DESTDIR)$(pkgconfigdir)" "$(DESTDIR)$(amrnbincludedir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libLTLIBRARIES clean-libtool \
	mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/a_refl.Plo
	-rm -f ./$(DEPDIR)/add.Plo
	-rm -f ./$(DEPDIR)/agc.Plo
	-rm -f ./$(DEPDIR)/amrdecode.Plo
	-rm -f ./$(DEPDIR)/amrencode.Plo
	-rm -f ./$(DEPDIR)/autocorr.Plo
	-rm -f ./$(DEPDIR)/az_lsp.Plo
	-rm -f ./$(DEPDIR)/b_cn_cod.Plo
	-rm -f ./$(DEPDIR)/bgnscd.Plo
	-rm -f ./$(DEPDIR)/bitno_tab.Plo
	-rm -f ./$(DEPDIR)/bitreorder_tab.Plo
	-rm -f ./$(DEPDIR)/c1035pf.Plo
	-rm -f ./$(DEPDIR)/c2_11pf.Plo
	-rm -f ./$(DEPDIR)/c2_9pf.Plo
	-rm -f ./$(DEPDIR)/c2_9pf_tab.Plo
	-rm -f ./$(DEPDIR)/c3_14pf.Plo
	-rm -f ./$(DEPDIR)/c4_17pf.Plo
	-rm -f ./$(DEPDIR)/c8_31pf.Plo
	-rm -f ./$(DEPDIR)/c_g_aver.Plo
	-rm -f ./$(DEPDIR)/calc_cor.Plo
	-rm -f ./$(DEPDIR)/calc_en.Plo
	-rm -f ./$(DEPDIR)/cbsearch.Plo
	-rm -f ./$(DEPDIR)/cl_ltp.Plo
	-rm -f ./$(DEPDIR)/cod_amr.Plo
	-rm -f ./$(DEPDIR)/convolve.Plo
	-rm -f ./$(DEPDIR)/cor_h.Plo
	-rm -f ./$(DEPDIR)/cor_h_x.Plo
	-rm -f ./$(DEPDIR)/cor_h_x2.Plo
	-rm -f ./$(DEPDIR)/corrwght_tab.Plo
	-rm -f ./$(DEPDIR)/d1035pf.Plo
	-rm -f ./$(DEPDIR)/d2_11pf.Plo
	-rm -f ./$(DEPDIR)/d2_9pf.Plo
	-rm -f ./$(DEPDIR)/d3_14pf.Plo
	-rm -f ./$(DEPDIR)/d4_17pf.Plo
	-rm -f ./$(DEPDIR)/d8_31pf.Plo
	-rm -f ./$(DEPDIR)/d_gain_c.Plo
	-rm -f ./$(DEPDIR)/d_gain_p.Plo
	-rm -f ./$(DEPDIR)/d_plsf.Plo
	-rm -f ./$(DEPDIR)/d_plsf_3.Plo
	-rm -f ./$(DEPDIR)/d_plsf_5.Plo
	-rm -f ./$(DEPDIR)/dec_amr.Plo
	-rm -f ./$(DEPDIR)/dec_gain.Plo
	-rm -f ./$(DEPDIR)/dec_input_format_tab.Plo
	-rm -f ./$(DEPDIR)/dec_lag3.Plo
	-rm -f ./$(DEPDIR)/dec_lag6.Plo
	-rm -f ./$(DEPDIR)/div_32.Plo
	-rm -f ./$(DEPDIR)/div_s.Plo
	-rm -f ./$(DEPDIR)/dtx_dec.Plo
	-rm -f ./$(DEPDIR)/dtx_enc.Plo
	-rm -f ./$(DEPDIR)/dummy.Plo
	-rm -f ./$(DEPDIR)/ec_gains.Plo
	-rm -f ./$(DEPDIR)/enc_lag3.Plo
	-rm -f ./$(DEPDIR)/enc_lag6.Plo
	-rm -f ./$(DEPDIR)/enc_output_format_tab.Plo
	-rm -f ./$(DEPDIR)/ets_to_if2.Plo
	-rm -f ./$(DEPDIR)/ets_to_wmf.Plo
	-rm -f ./$(DEPDIR)/ex_ctrl.Plo
	-rm -f ./$(DEPDIR)/extract_h.Plo
	-rm -f ./$(DEPDIR)/extract_l.Plo
	-rm -f ./$(DEPDIR)/g_adapt.Plo
	-rm -f ./$(DEPDIR)/g_code.Plo
	-rm -f ./$(DEPDIR)/g_pitch.Plo
	-rm -f ./$(DEPDIR)/gain_q.Plo
	-rm -f ./$(DEPDIR)/gains_tbl.Plo
	-rm -f ./$(DEPDIR)/gc_pred.Plo
	-rm -f ./$(DEPDIR)/get_const_tbls.Plo
	-rm -f ./$(DEPDIR)/gmed_n.Plo
	-rm -f ./$(DEPDIR)/gray_tbl.Plo
	-rm -f ./$(DEPDIR)/grid_tbl.Plo
	-rm -f ./$(DEPDIR)/hp_max.Plo
	-rm -f ./$(DEPDIR)/if2_to_ets.Plo
	-rm -f ./$(DEPDIR)/int_lpc.Plo
	-rm -f ./$(DEPDIR)/int_lsf.Plo
	-rm -f ./$(DEPDIR)/inter_36.Plo
	-rm -f ./$(DEPDIR)/inter_36_tab.Plo
	-rm -f ./$(DEPDIR)/inv_sqrt.Plo
	-rm -f ./$(DEPDIR)/inv_sqrt_tbl.Plo
	-rm -f ./$(DEPDIR)/l_abs.Plo
	-rm -f ./$(DEPDIR)/l_comp.Plo
	-rm -f ./$(DEPDIR)/l_deposit_h.Plo
	-rm -f ./$(DEPDIR)/l_deposit_l.Plo
	-rm -f ./$(DEPDIR)/l_extract.Plo
	-rm -f ./$(DEPDIR)/l_negate.Plo
	-rm -f ./$(DEPDIR)/l_shr_r.Plo
	-rm -f ./$(DEPDIR)/lag_wind.Plo
	-rm -f ./$(DEPDIR)/lag_wind_tab.Plo
	-rm -f ./$(DEPDIR)/levinson.Plo
	-rm -f ./$(DEPDIR)/lflg_upd.Plo
	-rm -f ./$(DEPDIR)/log2.Plo
	-rm -f ./$(DEPDIR)/log2_norm.Plo
	-rm -f ./$(DEPDIR)/log2_tbl.Plo
	-rm -f ./$(DEPDIR)/lpc.Plo
	-rm -f ./$(DEPDIR)/lsfwt.Plo
	-rm -f ./$(DEPDIR)/lsp.Plo
	-rm -f ./$(DEPDIR)/lsp_avg.Plo
	-rm -f ./$(DEPDIR)/lsp_az.Plo
	-rm -f ./$(DEPDIR)/lsp_lsf.Plo
	-rm -f ./$(DEPDIR)/lsp_lsf_tbl.Plo
	-rm -f ./$(DEPDIR)/lsp_tab.Plo
	-rm -f ./$(DEPDIR)/mult_r.Plo
	-rm -f ./$(DEPDIR)/negate.Plo
	-rm -f ./$(DEPDIR)/norm_l.Plo
	-rm -f ./$(DEPDIR)/norm_s.Plo
	-rm -f ./$(DEPDIR)/ol_ltp.Plo
	-rm -f ./$(DEPDIR)/overflow_tbl.Plo
	-rm -f ./$(DEPDIR)/p_ol_wgh.Plo
	-rm -f ./$(DEPDIR)/ph_disp.Plo
	-rm -f ./$(DEPDIR)/ph_disp_tab.Plo
	-rm -f ./$(DEPDIR)/pitch_fr.Plo
	-rm -f ./$(DEPDIR)/pitch_ol.Plo
	-rm -f ./$(DEPDIR)/post_pro.Plo
	-rm -f ./$(DEPDIR)/pow2.Plo
	-rm -f ./$(DEPDIR)/pow2_tbl.Plo
	-rm -f ./$(DEPDIR)/pre_big.Plo
	-rm -f ./$(DEPDIR)/pre_proc.Plo
	-rm -f ./$(DEPDIR)/pred_lt.Plo
	-rm -f ./$(DEPDIR)/preemph.Plo
	-rm -f ./$(DEPDIR)/prm2bits.Plo
	-rm -f ./$(DEPDIR)/pstfilt.Plo
	-rm -f ./$(DEPDIR)/q_gain_c.Plo
	-rm -f ./$(DEPDIR)/q_gain_p.Plo
	-rm -f ./$(DEPDIR)/q_plsf.Plo
	-rm -f ./$(DEPDIR)/q_plsf_3.Plo
	-rm -f ./$(DEPDIR)/q_plsf_3_tbl.Plo
	-rm -f ./$(DEPDIR)/q_plsf_5.Plo
	-rm -f ./$(DEPDIR)/q_plsf_5_tbl.Plo
	-rm -f ./$(DEPDIR)/qgain475.Plo
	-rm -f ./$(DEPDIR)/qgain475_tab.Plo
	-rm -f ./$(DEPDIR)/qgain795.Plo
	-rm -f ./$(DEPDIR)/qua_gain.Plo
	-rm -f ./$(DEPDIR)/qua_gain_tbl.Plo
	-rm -f ./$(DEPDIR)/reorder.Plo
	-rm -f ./$(DEPDIR)/residu.Plo
	-rm -f ./$(DEPDIR)/round.Plo
	-rm -f ./$(DEPDIR)/s10_8pf.Plo
	-rm -f ./$(DEPDIR)/set_sign.Plo
	-rm -f ./$(DEPDIR)/set_zero.Plo
	-rm -f ./$(DEPDIR)/shr.Plo
	-rm -f ./$(DEPDIR)/shr_r.Plo
	-rm -f ./$(DEPDIR)/sid_sync.Plo
	-rm -f ./$(DEPDIR)/sp_dec.Plo
	-rm -f ./$(DEPDIR)/sp_enc.Plo
	-rm -f ./$(DEPDIR)/spreproc.Plo
	-rm -f ./$(DEPDIR)/spstproc.Plo
	-rm -f ./$(DEPDIR)/sqrt_l.Plo
	-rm -f ./$(DEPDIR)/sqrt_l_tbl.Plo
	-rm -f ./$(DEPDIR)/sub.Plo
	-rm -f ./$(DEPDIR)/syn_filt.Plo
	-rm -f ./$(DEPDIR)/ton_stab.Plo
	-rm -f ./$(DEPDIR)/vad1.Plo
	-rm -f ./$(DEPDIR)/weight_a.Plo
	-rm -f ./$(DEPDIR)/window_tab.Plo
	-rm -f ./$(DEPDIR)/wmf_to_ets.Plo
	-rm -f ./$(DEPDIR)/wrapper.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-amrnbincludeHEADERS install-pkgconfigDATA

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-libLTLIBRARIES

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/a_refl.Plo
	-rm -f ./$(DEPDIR)/add.Plo
	-rm -f ./$(DEPDIR)/agc.Plo
	-rm -f ./$(DEPDIR)/amrdecode.Plo
	-rm -f ./$(DEPDIR)/amrencode.Plo
	-rm -f ./$(DEPDIR)/autocorr.Plo
	-rm -f ./$(DEPDIR)/az_lsp.Plo
	-rm -f ./$(DEPDIR)/b_cn_cod.Plo
	-rm -f ./$(DEPDIR)/bgnscd.Plo
	-rm -f ./$(DEPDIR)/bitno_tab.Plo
	-rm -f ./$(DEPDIR)/bitreorder_tab.Plo
	-rm -f ./$(DEPDIR)/c1035pf.Plo
	-rm -f ./$(DEPDIR)/c2_11pf.Plo
	-rm -f ./$(DEPDIR)/c2_9pf.Plo
	-rm -f ./$(DEPDIR)/c2_9pf_tab.Plo
	-rm -f ./$(DEPDIR)/c3_14pf.Plo
	-rm -f ./$(DEPDIR)/c4_17pf.Plo
	-rm -f ./$(DEPDIR)/c8_31pf.Plo
	-rm -f ./$(DEPDIR)/c_g_aver.Plo
	-rm -f ./$(DEPDIR)/calc_cor.Plo
	-rm -f ./$(DEPDIR)/calc_en.Plo
	-rm -f ./$(DEPDIR)/cbsearch.Plo
	-rm -f ./$(DEPDIR)/cl_ltp.Plo
	-rm -f ./$(DEPDIR)/cod_amr.Plo
	-rm -f ./$(DEPDIR)/convolve.Plo
	-rm -f ./$(DEPDIR)/cor_h.Plo
	-rm -f ./$(DEPDIR)/cor_h_x.Plo
	-rm -f ./$(DEPDIR)/cor_h_x2.Plo
	-rm -f ./$(DEPDIR)/corrwght_tab.Plo
	-rm -f ./$(DEPDIR)/d1035pf.Plo
	-rm -f ./$(DEPDIR)/d2_11pf.Plo
	-rm -f ./$(DEPDIR)/d2_9pf.Plo
	-rm -f ./$(DEPDIR)/d3_14pf.Plo
	-rm -f ./$(DEPDIR)/d4_17pf.Plo
	-rm -f ./$(DEPDIR)/d8_31pf.Plo
	-rm -f ./$(DEPDIR)/d_gain_c.Plo
	-rm -f ./$(DEPDIR)/d_gain_p.Plo
	-rm -f ./$(DEPDIR)/d_plsf.Plo
	-rm -f ./$(DEPDIR)/d_plsf_3.Plo
	-rm -f ./$(DEPDIR)/d_plsf_5.Plo
	-rm -f ./$(DEPDIR)/dec_amr.Plo
	-rm -f ./$(DEPDIR)/dec_gain.Plo
	-rm -f ./$(DEPDIR)/dec_input_format_tab.Plo
	-rm -f ./$(DEPDIR)/dec_lag3.Plo
	-rm -f ./$(DEPDIR)/dec_lag6.Plo
	-rm -f ./$(DEPDIR)/div_32.Plo
	-rm -f ./$(DEPDIR)/div_s.Plo
	-rm -f ./$(DEPDIR)/dtx_dec.Plo
	-rm -f ./$(DEPDIR)/dtx_enc.Plo
	-rm -f ./$(DEPDIR)/dummy.Plo
	-rm -f ./$(DEPDIR)/ec_gains.Plo
	-rm -f ./$(DEPDIR)/enc_lag3.Plo
	-rm -f ./$(DEPDIR)/enc_lag6.Plo
	-rm -f ./$(DEPDIR)/enc_output_format_tab.Plo
	-rm -f ./$(DEPDIR)/ets_to_if2.Plo
	-rm -f ./$(DEPDIR)/ets_to_wmf.Plo
	-rm -f ./$(DEPDIR)/ex_ctrl.Plo
	-rm -f ./$(DEPDIR)/extract_h.Plo
	-rm -f ./$(DEPDIR)/extract_l.Plo
	-rm -f ./$(DEPDIR)/g_adapt.Plo
	-rm -f ./$(DEPDIR)/g_code.Plo
	-rm -f ./$(DEPDIR)/g_pitch.Plo
	-rm -f ./$(DEPDIR)/gain_q.Plo
	-rm -f ./$(DEPDIR)/gains_tbl.Plo
	-rm -f ./$(DEPDIR)/gc_pred.Plo
	-rm -f ./$(DEPDIR)/get_const_tbls.Plo
	-rm -f ./$(DEPDIR)/gmed_n.Plo
	-rm -f ./$(DEPDIR)/gray_tbl.Plo
	-rm -f ./$(DEPDIR)/grid_tbl.Plo
	-rm -f ./$(DEPDIR)/hp_max.Plo
	-rm -f ./$(DEPDIR)/if2_to_ets.Plo
	-rm -f ./$(DEPDIR)/int_lpc.Plo
	-rm -f ./$(DEPDIR)/int_lsf.Plo
	-rm -f ./$(DEPDIR)/inter_36.Plo
	-rm -f ./$(DEPDIR)/inter_36_tab.Plo
	-rm -f ./$(DEPDIR)/inv_sqrt.Plo
	-rm -f ./$(DEPDIR)/inv_sqrt_tbl.Plo
	-rm -f ./$(DEPDIR)/l_abs.Plo
	-rm -f ./$(DEPDIR)/l_comp.Plo
	-rm -f ./$(DEPDIR)/l_deposit_h.Plo
	-rm -f ./$(DEPDIR)/l_deposit_l.Plo
	-rm -f ./$(DEPDIR)/l_extract.Plo
	-rm -f ./$(DEPDIR)/l_negate.Plo
	-rm -f ./$(DEPDIR)/l_shr_r.Plo
	-rm -f ./$(DEPDIR)/lag_wind.Plo
	-rm -f ./$(DEPDIR)/lag_wind_tab.Plo
	-rm -f ./$(DEPDIR)/levinson.Plo
	-rm -f ./$(DEPDIR)/lflg_upd.Plo
	-rm -f ./$(DEPDIR)/log2.Plo
	-rm -f ./$(DEPDIR)/log2_norm.Plo
	-rm -f ./$(DEPDIR)/log2_tbl.Plo
	-rm -f ./$(DEPDIR)/lpc.Plo
	-rm -f ./$(DEPDIR)/lsfwt.Plo
	-rm -f ./$(DEPDIR)/lsp.Plo
	-rm -f ./$(DEPDIR)/lsp_avg.Plo
	-rm -f ./$(DEPDIR)/lsp_az.Plo
	-rm -f ./$(DEPDIR)/lsp_lsf.Plo
	-rm -f ./$(DEPDIR)/lsp_lsf_tbl.Plo
	-rm -f ./$(DEPDIR)/lsp_tab.Plo
	-rm -f ./$(DEPDIR)/mult_r.Plo
	-rm -f ./$(DEPDIR)/negate.Plo
	-rm -f ./$(DEPDIR)/norm_l.Plo
	-rm -f ./$(DEPDIR)/norm_s.Plo
	-rm -f ./$(DEPDIR)/ol_ltp.Plo
	-rm -f ./$(DEPDIR)/overflow_tbl.Plo
	-rm -f ./$(DEPDIR)/p_ol_wgh.Plo
	-rm -f ./$(DEPDIR)/ph_disp.Plo
	-rm -f ./$(DEPDIR)/ph_disp_tab.Plo
	-rm -f ./$(DEPDIR)/pitch_fr.Plo
	-rm -f ./$(DEPDIR)/pitch_ol.Plo
	-rm -f ./$(DEPDIR)/post_pro.Plo
	-rm -f ./$(DEPDIR)/pow2.Plo
	-rm -f ./$(DEPDIR)/pow2_tbl.Plo
	-rm -f ./$(DEPDIR)/pre_big.Plo
	-rm -f ./$(DEPDIR)/pre_proc.Plo
	-rm -f ./$(DEPDIR)/pred_lt.Plo
	-rm -f ./$(DEPDIR)/preemph.Plo
	-rm -f ./$(DEPDIR)/prm2bits.Plo
	-rm -f ./$(DEPDIR)/pstfilt.Plo
	-rm -f ./$(DEPDIR)/q_gain_c.Plo
	-rm -f ./$(DEPDIR)/q_gain_p.Plo
	-rm -f ./$(DEPDIR)/q_plsf.Plo
	-rm -f ./$(DEPDIR)/q_plsf_3.Plo
	-rm -f ./$(DEPDIR)/q_plsf_3_tbl.Plo
	-rm -f ./$(DEPDIR)/q_plsf_5.Plo
	-rm -f ./$(DEPDIR)/q_plsf_5_tbl.Plo
	-rm -f ./$(DEPDIR)/qgain475.Plo
	-rm -f ./$(DEPDIR)/qgain475_tab.Plo
	-rm -f ./$(DEPDIR)/qgain795.Plo
	-rm -f ./$(DEPDIR)/qua_gain.Plo
	-rm -f ./$(DEPDIR)/qua_gain_tbl.Plo
	-rm -f ./$(DEPDIR)/reorder.Plo
	-rm -f ./$(DEPDIR)/residu.Plo
	-rm -f ./$(DEPDIR)/round.Plo
	-rm -f ./$(DEPDIR)/s10_8pf.Plo
	-rm -f ./$(DEPDIR)/set_sign.Plo
	-rm -f ./$(DEPDIR)/set_zero.Plo
	-rm -f ./$(DEPDIR)/shr.Plo
	-rm -f ./$(DEPDIR)/shr_r.Plo
	-rm -f ./$(DEPDIR)/sid_sync.Plo
	-rm -f ./$(DEPDIR)/sp_dec.Plo
	-rm -f ./$(DEPDIR)/sp_enc.Plo
	-rm -f ./$(DEPDIR)/spreproc.Plo
	-rm -f ./$(DEPDIR)/spstproc.Plo
	-rm -f ./$(DEPDIR)/sqrt_l.Plo
	-rm -f ./$(DEPDIR)/sqrt_l_tbl.Plo
	-rm -f ./$(DEPDIR)/sub.Plo
	-rm -f ./$(DEPDIR)/syn_filt.Plo
	-rm -f ./$(DEPDIR)/ton_stab.Plo
	-rm -f ./$(DEPDIR)/vad1.Plo
	-rm -f ./$(DEPDIR)/weight_a.Plo
	-rm -f ./$(DEPDIR)/window_tab.Plo
	-rm -f ./$(DEPDIR)/wmf_to_ets.Plo
	-rm -f ./$(DEPDIR)/wrapper.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-amrnbincludeHEADERS uninstall-libLTLIBRARIES \
	uninstall-pkgconfigDATA

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-generic clean-libLTLIBRARIES clean-libtool cscopelist-am \
	ctags ctags-am distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am \
	install-amrnbincludeHEADERS install-data install-data-am \
	install-dvi install-dvi-am install-exec install-exec-am \
	install-html install-html-am install-info install-info-am \
	install-libLTLIBRARIES install-man install-pdf install-pdf-am \
	install-pkgconfigDATA install-ps install-ps-am install-strip \
	installcheck installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags tags-am uninstall uninstall-am \
	uninstall-amrnbincludeHEADERS uninstall-libLTLIBRARIES \
	uninstall-pkgconfigDATA

.PRECIOUS: Makefile

    # Mention a dummy pure C file to trigger generation of the $(LINK) variable

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
