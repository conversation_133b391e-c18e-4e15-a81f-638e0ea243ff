/**********************************************************************/
/**
 * @file yx_ia_wifi.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-11
 * @version V1.0
 * @brief wifi模块接口适配
 **********************************************************************/
#ifndef YX_IA_WIFI_H
#define YX_IA_WIFI_H

#include "yx_type.h"

/**
 * @brief wifi scan事件枚举类型定义
 * @ingroup ia_wifi
 */
typedef enum {
    WIFISCAN_EVENT_EXIT = 0,    /**< 扫描结束事件枚举 */
    WIFISCAN_EVENT_GET,         /**< 扫描到热点事件枚举 */
    WIFISCAN_EVENT_MAX          /**< 枚举最大值 */
} yx_ia_wifi_event_e;

/**
 * @brief 单个热点信息结构体定义
 * @ingroup ia_wifi
 */
typedef struct {
    INT8U   mac[6];             /**< MAC地址，唯一标识一个网络设备 */
    INT32S  rssi;               /**< 接收信号强度指示,单位dBm（分贝毫瓦）,值为负数,数值越接近0,信号越强*/
    INT32U  channel;            /**< Wi-Fi接入点的工作信道 */
} yx_ia_wifi_info_t;

/**
 * @brief 设置wifi扫描参数
 * @ingroup ia_wifi
 * @param[in] round_num 扫描轮数,默认值为3,取值范围[1, 3]
 * @param[in] max_bssid_num 最大bssid数量,默认值为5,取值范围[4, 10]
 * @param[in] priority 优先级,0为PS优先,1为wifi扫描优先,默认值为0,取值范围[0, 1]
 * @param[in] timeout 扫描超时时间,单位(秒),默认值为25
 * @param[in] cb 扫描结果回调函数
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 * @note 扫描参数设置一次之后，对之后的所有扫描都生效
 */
INT32S yx_ia_wifi_set_param(INT8U round_num, INT8U max_bssid_num, INT8U priority, 
    INT8U timeout, VOID (*cb)(yx_ia_wifi_event_e type, yx_ia_wifi_info_t *spot_info));

/**
 * @brief 根据参数开始一次wifi扫描，该函数为阻塞函数
 * @ingroup ia_wifi
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 * @warning 扫描操作会释放rrc资源,必须在没有数据通信的情况下调用此接口
 */
INT32S yx_ia_wifi_scan_start(VOID);

/**
 * @brief 强行停止wifi扫描
 * @ingroup ia_wifi
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 * @note 通常不需要调用，扫描到达指定数量或者超时后自动结束
 */
INT32S yx_ia_wifi_scan_stop(VOID);

/**
 * @brief 打开wifi扫描设备
 * @ingroup ia_wifi
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_wifi_scan_open(VOID);

/**
 * @brief 关闭wifi扫描设备
 * @ingroup ia_wifi
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 */
INT32S yx_ia_wifi_scan_close(VOID);


#endif /* YX_IA_WIFI_H */