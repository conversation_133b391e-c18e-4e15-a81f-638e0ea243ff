#ifndef __ALSA_CAPTURE_H__
#define __ALSA_CAPTURE_H__

#include <stdint.h>
#include "alsa.h"
#include "alsa_extra.h"

typedef struct alsa_capture alsa_capture_t;

alsa_capture_t *alsa_capture_open(uint32_t sample_rate,
                                  uint8_t channels,
                                  uint8_t bits);

int alsa_capture_set_data_pull_callback(alsa_capture_t *capture, alsa_data_pull_push_cb_t cb);

int alsa_capture_start(alsa_capture_t *capture);

int alsa_capture_pause(alsa_capture_t *capture);

int alsa_capture_stop(alsa_capture_t *capture);

int alsa_capture_flush(alsa_capture_t *capture);

int alsa_capture_close(alsa_capture_t *capture);

int alsa_capture_read(alsa_capture_t *capture, uint8_t *ptr, uint32_t length);

uint32_t alsa_capture_data_to_user(uint8_t *data, uint32_t length);

int alsa_capture_register_pcm_state_callback(alsa_capture_t *capture,
                                             alsa_pcm_state_callback_t cb,
                                             void *arg);

#endif