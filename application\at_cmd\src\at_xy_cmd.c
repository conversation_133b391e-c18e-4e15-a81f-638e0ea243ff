/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "app_basic_config.h"
#include "xy_at_api.h"
#include "oss_nv.h"
#include "xy_fs.h"
#include "softap_nv.h"
#include "xy_system.h"
#include "pin_ctl.h"
#include "lwip/ip_addr.h"
#include "lwip/netdb.h"
#include "xy_tcpip_api.h"
#include "gw_netif_api.h"
#include "usb_api.h"
#include "ip_log.h"
#include "xy_flash.h"
#include "pkt_process.h"
#if DM_CTCC
#include "telecom_dm.h"
#endif
#include "flash_proxy.h"


extern void xy_flash_write_safe(const void* flash_addr, const void* ram_addr, uint32_t size);

/*******************************************************************************
 *						Local function implementations						   *
 ******************************************************************************/
const char gRNstr[] = "\r\n";

#if (FLASH_LIMIT)  /*简化版NV设置测试命令，仅用于整形参数的读写。开启后可以节省近10K FLASH空间，但只能通过地址偏移来设置读取参数值*/
int at_NV_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_REQ)
	{
		char cmd[10] = {0};
		char param[20] = {0};
		uint32_t  val = 0;

		if(at_parse_param("%10s,", at_buf,cmd) != XY_OK)
		{
			goto ERRCMD;
		}
		/*强行将出厂NV全局数据写入原始出厂NV，仅限极特殊测试场景使用*/
		else if (!strcmp(cmd, "FAC"))
		{
			xy_flash_write_safe((void*)(NV_MAIN_SOFTAP_FACTORY_BASE+4), g_softap_fac_nv, sizeof(softap_fac_nv_t));
		}
		/*AT+NV=SET,<softap_fac_nv_t param offset>,<param val>[,<param len>]   len可以不填，默认为1字节*/		
		else if (!strcmp(cmd, "SET"))
		{
			at_parse_param(",%s,%d,", at_buf,param,&val);

			/*高频率使用的明文命令，包括：CLOSEDEBUG、DEEPSLEEP、SLEEP、LIGHTSLEEP、WFI、DONGLE、GWNET、SUBNET、USBMODE、DTRPIN、LOGPORT、LOG、WDT、SIM0DET、SIM1DET*/
			if(!strcmp(param, "CLOSEDEBUG"))
			{
				g_softap_fac_nv->off_debug = val;
				SAVE_FAC_PARAM(off_debug);
				SAVE_MAIN_FAC_PARAM(off_debug);
			}
			else if (!strcmp(param, "TEST"))
		    {
		        g_softap_fac_nv->test = val;
		        SAVE_FAC_PARAM(test);
		    }
			else if (!strcmp(param, "SPECIAL"))
		    {
		        g_softap_fac_nv->special = val;
		        SAVE_FAC_PARAM(special);
		    }
			else if (!strcmp(param, "DEEPSLEEP"))
		    {
		        g_softap_fac_nv->deepsleep_enable = val;
				SOFTAP_FAC_CACHE_CLEAN(deepsleep_enable);
		        SAVE_FAC_PARAM(deepsleep_enable);
		    }
			else if (!strcmp(param, "SLEEP"))
		    {
		        g_softap_fac_nv->sleep_enable = val;
		        SOFTAP_FAC_CACHE_CLEAN(sleep_enable);
		        SAVE_FAC_PARAM(sleep_enable);
		    }
			else if (!strcmp(param, "LIGHTSLEEP"))
		    {
		        g_softap_fac_nv->lightsleep_enable = val;
		        SOFTAP_FAC_CACHE_CLEAN(lightsleep_enable);
		        SAVE_FAC_PARAM(lightsleep_enable);
		    }
		    else if (!strcmp(param, "WFI"))
		    {
		        g_softap_fac_nv->wfi_enable = val;
				SOFTAP_FAC_CACHE_CLEAN(wfi_enable);
		        SAVE_FAC_PARAM(wfi_enable);
		    }
			else if (!strcmp(param, "DTRPIN"))
		    {
		        g_softap_fac_nv->dtrpin_ctl = val;	
		        SAVE_FAC_PARAM(dtrpin_ctl);
		    }
			else if (!strcmp(param, "DONGLE"))  
			{
				g_softap_fac_nv->dongle = val;
				SAVE_FAC_PARAM(dongle);
			}
			else if (!strcmp(param, "GWNET"))
		    {
		        g_softap_fac_nv->gw_act_mode = val;
				SAVE_FAC_PARAM(gw_act_mode);
		    }
			else if (!strcmp(param, "SUBNET")) 
		    {
		        g_softap_fac_nv->ue_ip4_type = val;
		        SAVE_FAC_PARAM(ue_ip4_type);
		    }
			else if (!strcmp(param,"USBMODE"))
		    {
		    	g_softap_fac_nv->usb_mode = val;
		        SAVE_FAC_PARAM(usb_mode);
		    }
			else if (!strcmp(param, "USBNODE"))
			{
				g_softap_fac_nv->usb_node = val/4;
				SAVE_FAC_PARAM(usb_node);
			}
			else if (!strcmp(param, "LOGPORT"))
			{
				g_softap_fac_nv->log_port = val;
				SAVE_FAC_PARAM(log_port);
			}
			else if (!strcmp(param, "LOG"))
			{
				g_softap_fac_nv->open_log = val;
				SAVE_FAC_PARAM(open_log);
			}
			else if (!strcmp(param, "WDT"))
			{
				g_softap_fac_nv->utc_wdt_sec = (uint8_t)val;
				SAVE_FAC_PARAM(utc_wdt_sec);
				SAVE_MAIN_FAC_PARAM(utc_wdt_sec);
			}	
			else if (!strcmp(param, "DOWNFLOW"))
		    {
		    	g_softap_fac_nv->down_min_bytes = val;
		        SAVE_FAC_PARAM(down_min_bytes);
		    }
			else if (!strcmp(param, "UPFLOW"))
		    {
		    	g_softap_fac_nv->up_min_bytes = val;
		        SAVE_FAC_PARAM(up_min_bytes);
		    }
			else if (!strcmp(param, "SIM0DET"))
			{
				g_softap_fac_nv->sim0_det = val;	
				SAVE_FAC_PARAM(sim0_det);
			}
			else if (!strcmp(param, "SIM1DET"))
			{
				g_softap_fac_nv->sim1_det = val;	
				SAVE_FAC_PARAM(sim1_det);
			}
			else if (!strcmp(param, "CTSPIN"))
			{
				g_softap_fac_nv->lpuart_cts_pin = val;
				SAVE_FAC_PARAM(lpuart_cts_pin);
			}
			else if (!strcmp(param, "RTSPIN"))
			{
				g_softap_fac_nv->lpuart_rts_pin = val;
				SAVE_FAC_PARAM(lpuart_rts_pin);
			}
			else if (!strcmp(param, "VBATMODE"))
			{
				g_softap_fac_nv->vbat_mode = val;	
				SAVE_FAC_PARAM(vbat_mode);
				SAVE_MAIN_FAC_PARAM(vbat_mode);
			}
			/*AT+NV=SET,<softap_fac_nv_t param offset>,<param val>[,<param len>]   len可以不填，默认为1字节*/		
			else if(is_digit_str(param))
			{
				uint32_t  val = 0;
				uint32_t  param2 = 0;
				uint32_t  param3 = 0;
				at_parse_param(",%d,%d,%d", at_buf,&val,&param2,&param3);
			
				if(param3 == 0)
					param3 = 1;
				
				SAVE_FAC_PARAM2(val,param2,param3);
			}
#if XY_PPP
			else if(!strcmp(param, "PPPLOGIN"))
			{
				int auth_type = 0;
				char username[13] = {0};
				char password[13] = {0};

				if (at_parse_param(",,%d(0-255),%13s,%13s", at_buf, &auth_type, username, password) != XY_OK)
				{
					*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
					return AT_END;
				}
				g_app_basic_cfg.ppp_auth_type = auth_type;
				SAVE_APP_BASIC_CFG_PARAM(ppp_auth_type);
				strncpy(g_app_basic_cfg.ppp_username, username, 12);				
				SAVE_APP_BASIC_CFG_PARAM(ppp_username);
				strncpy(g_app_basic_cfg.ppp_password, password, 12);				
				SAVE_APP_BASIC_CFG_PARAM(ppp_password);				
			}
			else if (!strcmp(param, "PPPCID"))
			{
				g_app_basic_cfg.ppp_cid = val;
				SAVE_APP_BASIC_CFG_PARAM(ppp_cid);
			}	
#endif
			else
				return AT_FORWARD;
		}
		/*AT+NV=GET,<softap_fac_nv_t param offset>[,<param len>]   len可以不填，默认为1字节*/
		else if (!strcmp(cmd, "GET"))
		{
			uint32_t val = -1;

			*prsp_cmd = xy_malloc(1000);

			at_parse_param(",%s,", at_buf,param);

			if(!strcmp(param, "CLOSEDEBUG"))
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->off_debug);
			else if (!strcmp(param, "DEEPSLEEP"))
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->deepsleep_enable);
			else if (!strcmp(param, "SLEEP"))
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->sleep_enable);
			else if (!strcmp(param, "LIGHTSLEEP"))
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->lightsleep_enable);
			else if (!strcmp(param, "SLEEPSTATE"))
			{
				int atHandle = get_current_ttyFd();
				char *at_str = xy_malloc(200);
				extern int dtr_have_locked(void);
				snprintf(at_str, 200, "\r\nAP Lock State:dslock:%d, slock:%d, lslock:%d, dtr_have_locked:%d\r\n", is_sleep_locked(WL_DEEPSLEEP), is_sleep_locked(WL_SLEEP), is_sleep_locked(WL_LIGHTSLEEP), dtr_have_locked());
				snprintf(at_str + strlen(at_str), 200 - strlen(at_str), "\r\nEnable DSLEEP:%d,%d, SLEEP:%d,%d\r\n",g_softap_fac_nv->deepsleep_enable,g_softap_fac_nv->deepsleep_threshold,g_softap_fac_nv->sleep_enable,g_softap_fac_nv->sleep_threshold);
				snprintf(at_str + strlen(at_str), 200 - strlen(at_str), "\r\nlpuart_wakup:%d, dtrpin_ctl:%d\r\n",g_softap_fac_nv->lpuart_wakup,g_softap_fac_nv->dtrpin_ctl);
				appAtWriteImmediately(atHandle, at_str, strlen(at_str));
				xy_free(at_str);
				goto FORWARD;
			}
			else if (!strcmp(param, "DTRPIN"))
				sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->dtrpin_ctl);	
			else if (!strcmp(param, "DONGLE"))    
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->dongle);
			else if (!strcmp(param, "SPECIAL"))    
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->special);
			else if (!strcmp(param, "GWNET"))   
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->gw_act_mode);
			else if (!strcmp(param, "SUBNET"))    
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->ue_ip4_type);
			else if (!strcmp(param, "USBMODE"))
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->usb_mode);
			else if (!strcmp(param, "USBNODE"))
				sprintf(*prsp_cmd, "%s%u",gRNstr,g_softap_fac_nv->usb_node*4);  
			else if (!strcmp(param, "LOGPORT"))
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->log_port);
			else if (!strcmp(param, "LOG"))
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->open_log);
			else if (!strcmp(param, "WDT"))
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->utc_wdt_sec);
			else if (!strcmp(param, "DOWNFLOW"))
				sprintf(*prsp_cmd, "%s%u",gRNstr,g_softap_fac_nv->down_min_bytes); 
			else if (!strcmp(param, "UPFLOW"))
				sprintf(*prsp_cmd, "%s%u",gRNstr,g_softap_fac_nv->up_min_bytes); 
			else if (!strcmp(param, "SIM0DET"))
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->sim0_det);
			else if (!strcmp(param, "SIM1DET"))
				sprintf(*prsp_cmd, "%s%d",gRNstr, g_softap_fac_nv->sim1_det);
			else if (!strcmp(param, "CTSPIN"))
				sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->lpuart_cts_pin);
			else if (!strcmp(param, "RTSPIN"))
				sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->lpuart_rts_pin);
			else if (!strcmp(param, "VBATMODE"))
				sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->vbat_mode);
#if XY_PPP
			else if(!strcmp(param, "PPPLOGIN"))
				sprintf(*prsp_cmd, "\r\n+PPPLOGIN: %d,\"%s\",\"%s\"", g_app_basic_cfg.ppp_auth_type, g_app_basic_cfg.ppp_username, g_app_basic_cfg.ppp_password);	
			else if (!strcmp(param, "PPPCID"))
				sprintf(*prsp_cmd, "%s%u",gRNstr,g_app_basic_cfg.ppp_cid);								
#endif
			/*产线生产有查版本号需求，不可去除*/
			else if (!strcmp(param, "VER"))
				sprintf(*prsp_cmd, "%s%s,%s,%s,%s",gRNstr, g_softap_fac_nv->manufacturer, g_softap_fac_nv->modul_ver, g_softap_fac_nv->hardver, g_softap_fac_nv->versionExt);
			else if (!strcmp(param, "EXTVER"))
				sprintf(*prsp_cmd, "%s%s",gRNstr, g_softap_fac_nv->versionExt);			
			else if (!strcmp(param, "HARDVER"))
				sprintf(*prsp_cmd, "%s%s",gRNstr, g_softap_fac_nv->hardver);							
			/*AT+NV=GET,<softap_fac_nv_t param offset>[,<param len>]   len可以不填，默认为1字节*/
			else if(is_digit_str(param))
			{
				uint32_t  val = 0;
				uint32_t  param2 = 0;
				at_parse_param(",%d,%d", at_buf,&val,&param2);

				if(param2 == 0)
					param2 = 1;
				
				READ_FAC_PARAM2(val,val,param2);

				*prsp_cmd = xy_malloc(40);
				sprintf(*prsp_cmd, "%s%d",gRNstr,val);	
			}
			else
			{
FORWARD:
                if (*prsp_cmd != NULL)
                {
                    xy_free(*prsp_cmd);
                    *prsp_cmd = NULL;
                }
                return AT_FORWARD;
			}
		}
		else
			goto ERRCMD;
	}
#if (!AT_TEST_OFF)
	else
    {
		*prsp_cmd = xy_malloc(256);
		snprintf(*prsp_cmd,256,"AT+NV=SET,<offset>,<val[,<len>]\r\nAT+NV=GET,<offset>[,<len>]\r\n");

		return AT_END;
    }
#endif
	return AT_END;
ERRCMD:
	*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
	return AT_END;
}	

 #else

static int simple_set_val(char *param, int val)
{
    if (!strcmp(param, "DEEPSLEEP"))
    {
        g_softap_fac_nv->deepsleep_enable = val;
		SOFTAP_FAC_CACHE_CLEAN(deepsleep_enable);
        SAVE_FAC_PARAM(deepsleep_enable);
    }
	else if (!strcmp(param, "SLEEP"))
    {
        g_softap_fac_nv->sleep_enable = val;
        SOFTAP_FAC_CACHE_CLEAN(sleep_enable);
        SAVE_FAC_PARAM(sleep_enable);
    }
	else if (!strcmp(param, "MPU"))
    {
        if(val == 0)
			flash_protect_disable();
		else
			flash_protect_enable();
    }
	else if (!strcmp(param, "MULTIMAX"))
    {
        g_softap_fac_nv->int_Multi_Max = val;
        SOFTAP_FAC_CACHE_CLEAN(int_Multi_Max);
        SAVE_FAC_PARAM(int_Multi_Max);
    }
	else if (!strcmp(param, "MULTITIMEOUT"))
    {
        g_softap_fac_nv->int_Multi_Timeout = val;
        SOFTAP_FAC_CACHE_CLEAN(int_Multi_Timeout);
        SAVE_FAC_PARAM(int_Multi_Timeout);
    }
    else if (!strcmp(param, "USBMODE"))
    {
    	g_softap_fac_nv->usb_mode = val;
        SAVE_FAC_PARAM(usb_mode);
    }
#if USB_DEVICE
	else if (!strcmp(param, "USBTEST"))
    {
		if (val == 3)//usb net link down
		{
            usb_gw_process_deactive();
			return 1;
		}else if (val == 4)//usb net link up
		{
            usb_gw_process_active();
			return 1;
		}
    }
#endif
	else if (!strcmp(param, "USBNODE"))
    {
    	g_softap_fac_nv->usb_node = val/4;
        SAVE_FAC_PARAM(usb_node);
    }
	else if (!strcmp(param, "MODEMNODE"))
    {
    	g_app_basic_cfg.modem_node = val/4;
        SAVE_APP_BASIC_CFG_PARAM(modem_node);
    }
	else if (!strcmp(param, "DOWNFLOW"))
    {
    	g_softap_fac_nv->down_min_bytes = val;
        SAVE_FAC_PARAM(down_min_bytes);
    }
	else if (!strcmp(param, "UPFLOW"))
    {
    	g_softap_fac_nv->up_min_bytes = val;
        SAVE_FAC_PARAM(up_min_bytes);
    }
    else if (!strcmp(param, "SUBNET"))
    {
        g_softap_fac_nv->ue_ip4_type = val;
        SAVE_FAC_PARAM(ue_ip4_type);
    }
	else if (!strcmp(param, "PPPCID"))
    {
		g_app_basic_cfg.ppp_cid = val;
		SAVE_APP_BASIC_CFG_PARAM(ppp_cid);
    }
	else if (!strcmp(param, "USBCID"))
    {
		g_app_basic_cfg.usb_cid = val;
		SAVE_APP_BASIC_CFG_PARAM(usb_cid);
    }
    else if (!strcmp(param, "LIGHTSLEEP"))
    {
        g_softap_fac_nv->lightsleep_enable = val;
        SOFTAP_FAC_CACHE_CLEAN(lightsleep_enable);
        SAVE_FAC_PARAM(lightsleep_enable);
    }
    else if (!strcmp(param, "WFI"))
    {
        g_softap_fac_nv->wfi_enable = val;
		SOFTAP_FAC_CACHE_CLEAN(wfi_enable);
        SAVE_FAC_PARAM(wfi_enable);
    }
	else if (!strcmp(param, "DEEPSLEEPTHRESHOLD")) //必须重启生效
    {
        g_softap_fac_nv->deepsleep_threshold = val;
        SAVE_FAC_PARAM(deepsleep_threshold);
    }
	else if (!strcmp(param, "SLEEPTHRESHOLD")) //必须重启生效
    {
        g_softap_fac_nv->sleep_threshold = val;
        SAVE_FAC_PARAM(sleep_threshold);
    }
	else if (!strcmp(param, "FREQHOP"))
    {
        g_softap_fac_nv->freq_hop = val;
        SAVE_FAC_PARAM(freq_hop);
    }
	else if (!strcmp(param, "LOGPERIOD"))
    {
        g_softap_fac_nv->log_period = val;
        SAVE_FAC_PARAM(log_period);
    }
	else if (!strcmp(param, "VDDIO"))
    {
        g_softap_fac_nv->vdd1p8_sel = val;
        SAVE_FAC_PARAM(vdd1p8_sel);
		SAVE_MAIN_FAC_PARAM(vdd1p8_sel);
    }
	else if (!strcmp(param, "AUDIOCFG"))
    {
        g_softap_fac_nv->audio_cfg = val;
        SAVE_FAC_PARAM(audio_cfg);
    }
    else if (!strcmp(param, "LPMMODE") || !strcmp(param, "LOCK"))
    {
        g_softap_fac_nv->sleep_mode = val;
        SAVE_FAC_PARAM(sleep_mode);
    }
	else if(!strcmp(param, "URCCFG"))
	{
		g_softap_fac_nv->urc_cfg = val;
		SAVE_FAC_PARAM(urc_cfg);
	}
	else if(!strcmp(param, "URCCACHE"))
	{
		g_softap_fac_nv->urc_cache = val;
		SAVE_FAC_PARAM(urc_cache);
	}
	else if(!strcmp(param, "DONGLE"))
	{
		g_softap_fac_nv->dongle = val;
		SAVE_FAC_PARAM(dongle);
	}
	else if (!strcmp(param, "ZONE"))  
	{
		g_softap_fac_nv->zone = val;
		SAVE_FAC_PARAM(zone);
	}
	else if(!strcmp(param, "SPECIAL"))
	{
		g_softap_fac_nv->special = val;
		SAVE_FAC_PARAM(special);
	}
	else if(!strcmp(param, "ASSERTSUM"))
	{
		g_softap_fac_nv->assertSum = val;
		SAVE_FAC_PARAM(assertSum);
	}
	else if(!strcmp(param, "LOCALRCVWND"))
	{
		g_app_basic_cfg.local_tcp_rcv_wnd = g_local_tcp_rcv_wnd = val;
		SAVE_APP_BASIC_CFG_PARAM(local_tcp_rcv_wnd);
	}
	else if(!strcmp(param, "MTU"))
	{
		g_app_basic_cfg.mtu = val;
		SAVE_APP_BASIC_CFG_PARAM(mtu);
	}
	else if(!strcmp(param, "TCPBUFF"))
	{
		g_app_basic_cfg.sockDlfcLimit = val;
		SAVE_APP_BASIC_CFG_PARAM(sockDlfcLimit);
	}
	else if (!strcmp(param, "ADCRANGE"))
    {
        g_softap_fac_nv->adc_volrange = (uint16_t)val;
        SAVE_FAC_PARAM(adc_volrange);
    }
    else if (!strcmp(param, "ATERRRPT"))
    {
        g_softap_fac_nv->at_err_report = val;
        SAVE_FAC_PARAM(at_err_report);
    }
    else if (!strcmp(param, "SLEEPDELAY"))
    {
        g_softap_fac_nv->sleep_delay = (uint8_t)val;
        SAVE_FAC_PARAM(sleep_delay);
    }
	else if (!strcmp(param, "OFFWAKUP"))
    {
        g_softap_fac_nv->off_self_wakup = (uint8_t)val;
        SAVE_FAC_PARAM(off_self_wakup);
    }
	else if (!strcmp(param, "OFFRCWAKUP"))
    {
        g_softap_fac_nv->off_rc_wakeup = (uint8_t)val;
        SAVE_FAC_PARAM(off_rc_wakeup);
    }
    else if (!strcmp(param, "WDT"))
    {
        g_softap_fac_nv->utc_wdt_sec = (uint8_t)val;
        SAVE_FAC_PARAM(utc_wdt_sec);
		SAVE_MAIN_FAC_PARAM(utc_wdt_sec);
    }
	else if (!strcmp(param, "WIRESHARK"))
    {
        g_softap_fac_nv->log_wireshark = (uint8_t)val;
        SAVE_FAC_PARAM(log_wireshark);
    }
	else if (!strcmp(param, "DBGLOG"))
    {
        g_softap_fac_nv->debug_log_tx = (uint8_t)val;
        SAVE_FAC_PARAM(debug_log_tx);
		SAVE_MAIN_FAC_PARAM(debug_log_tx);
    }
	else if (!strcmp(param, "FASTCONNREL"))
    {
        g_softap_fac_nv->fast_conn_rel = (uint8_t)val;
        SAVE_FAC_PARAM(fast_conn_rel);
    }
    else if (!strcmp(param, "LPUARTRBLEN"))
    {
        g_softap_fac_nv->lpuart_rb_len = val;
        SAVE_FAC_PARAM(lpuart_rb_len);
    }
    else if (!strcmp(param, "UARTSET"))
    {
        g_softap_fac_nv->at_uart_rate = val;
        SAVE_FAC_PARAM(at_uart_rate);
		xy_fremove(BSP_VAR_NV_FS);
    }
    else if (!strcmp(param, "MIPSDBG"))
    {
        g_softap_fac_nv->mips_debug = val;
        SAVE_FAC_PARAM(mips_debug);
    }
	else if (!strcmp(param, "TEST"))
    {
        g_softap_fac_nv->test = val;
        SAVE_FAC_PARAM(test);
    }
    else if (!strcmp(param, "LOG"))
    {
        g_softap_fac_nv->open_log = val;
        SAVE_FAC_PARAM(open_log);
    }
	else if (!strcmp(param, "LOGSPEEDLIMIT"))
    {
        g_softap_fac_nv->log_speed_limit = val;
        SAVE_FAC_PARAM(log_speed_limit);
    }
    else if (!strcmp(param, "CLOSEDEBUG"))
    {
        g_softap_fac_nv->off_debug = val;
        SAVE_FAC_PARAM(off_debug);
		SAVE_MAIN_FAC_PARAM(off_debug);
    }
	else if (!strcmp(param, "OFFCHECK"))
    {
        g_softap_fac_nv->off_check = val;
        SAVE_FAC_PARAM(off_check);
    }
    else if (!strcmp(param, "PIN1"))
    {
        g_softap_fac_nv->state_pin1 = val;
        SAVE_FAC_PARAM(state_pin1);
    }
	else if (!strcmp(param, "PIN2"))
    {
        g_softap_fac_nv->state_pin2 = val;
        SAVE_FAC_PARAM(state_pin2);
    }
	else if (!strcmp(param, "PIN3"))
    {
        g_softap_fac_nv->state_pin3 = val;
        SAVE_FAC_PARAM(state_pin3);
    }
	else if (!strcmp(param, "ADC0"))
    {
        g_softap_fac_nv->pad_adc0 = val;
        SAVE_FAC_PARAM(pad_adc0);
    }
	else if (!strcmp(param, "ADC1"))
    {
        g_softap_fac_nv->pad_adc1 = val;
        SAVE_FAC_PARAM(pad_adc1);
    }
	else if (!strcmp(param, "PWM0"))
    {
        g_softap_fac_nv->pad_pwm0 = val;
        SAVE_FAC_PARAM(pad_pwm0);
    }
	else if (!strcmp(param, "PWM1"))
    {
        g_softap_fac_nv->pad_pwm1 = val;
        SAVE_FAC_PARAM(pad_pwm1);
    }
	else if (!strcmp(param, "IO0"))
    {
        g_softap_fac_nv->pad_io0 = val;
        SAVE_FAC_PARAM(pad_io0);
    }
	else if (!strcmp(param, "IO1"))
    {
        g_softap_fac_nv->pad_io1 = val;
        SAVE_FAC_PARAM(pad_io1);
    }
	else if (!strcmp(param, "IO2"))
    {
        g_softap_fac_nv->pad_io2 = val;
        SAVE_FAC_PARAM(pad_io2);
    }
	else if (!strcmp(param, "IO3"))
    {
        g_softap_fac_nv->pad_io3 = val;
        SAVE_FAC_PARAM(pad_io3);
    }
    else if (!strcmp(param, "CTSPIN"))
    {
        g_softap_fac_nv->lpuart_cts_pin = val;
        SAVE_FAC_PARAM(lpuart_cts_pin);
    }
    else if (!strcmp(param, "RTSPIN"))
    {
        g_softap_fac_nv->lpuart_rts_pin = val;
        SAVE_FAC_PARAM(lpuart_rts_pin);
    }
    else if (!strcmp(param, "ATDTRPIN"))
    {
        g_softap_fac_nv->lpuart_dtr_pin = val;
        SAVE_FAC_PARAM(lpuart_dtr_pin);
    }
    else if (!strcmp(param, "DCDPIN"))
    {
        g_softap_fac_nv->lpuart_dcd_pin = val;
        SAVE_FAC_PARAM(lpuart_dcd_pin);
    }	
    else if (!strcmp(param, "GWNET"))
    {
        g_softap_fac_nv->gw_act_mode = val;
		SAVE_FAC_PARAM(gw_act_mode);
    }
    else if (!strcmp(param, "SIMTEST"))
    {
        g_softap_fac_nv->sim_test = val;
		SOFTAP_FAC_CACHE_CLEAN(sim_test);
        SAVE_FAC_PARAM(sim_test);
    }
    else if (!strcmp(param, "LOGPORT"))
    {
        g_softap_fac_nv->log_port = val;
        SAVE_FAC_PARAM(log_port);
    }
    else if (!strcmp(param, "TCPUPWND"))
    {
        g_app_basic_cfg.tcpUlAckWnd = val;	
        SAVE_APP_BASIC_CFG_PARAM(tcpUlAckWnd);
    }
    else if (!strcmp(param, "TCPDOWNWND"))
    {
        g_app_basic_cfg.tcpDlAckWnd = val;	
        SAVE_APP_BASIC_CFG_PARAM(tcpDlAckWnd);
    }
    else if (!strcmp(param, "DM2"))
    {
        g_softap_fac_nv->dm2_output_vol = val;	
        SAVE_FAC_PARAM(dm2_output_vol);
    }
    else if (!strcmp(param, "INDUCTOR"))
    {
        g_softap_fac_nv->inductor = val;	
        SAVE_FAC_PARAM(inductor);
		SAVE_MAIN_FAC_PARAM(inductor);
    }
    else if (!strcmp(param, "VBATMODE"))
    {
        g_softap_fac_nv->vbat_mode = val;	
        SAVE_FAC_PARAM(vbat_mode);
		SAVE_MAIN_FAC_PARAM(vbat_mode);
    }
    else if (!strcmp(param, "SLEEP1P8"))
    {
        g_softap_fac_nv->sleep_1p8 = val;	
        SAVE_FAC_PARAM(sleep_1p8);
    }
    else if (!strcmp(param, "RESETMODE"))
    {
        g_softap_fac_nv->reset_mode = val;	
        SAVE_FAC_PARAM(reset_mode);
    }
    else if (!strcmp(param, "WKUPRST"))
    {
        g_softap_fac_nv->wkuprst_ctl = val;	
        SAVE_FAC_PARAM(wkuprst_ctl);
    }
    else if (!strcmp(param, "POWERSAVE"))
    {
        g_softap_fac_nv->power_save = val;	
        SAVE_FAC_PARAM(power_save);
    }
	else if (!strcmp(param, "RFPOWER"))
    {
        HWREG(RETENTION_RF_POWER_CTRL) = val;	/*RF温控降发送功率，低两字节表示温度阈值，高两字节表示功率调制步值*/
    }
    else if (!strcmp(param, "SIM0DET"))
    {
        g_softap_fac_nv->sim0_det = val;	
        SAVE_FAC_PARAM(sim0_det);
    }
    else if (!strcmp(param, "SIM1DET"))
    {
        g_softap_fac_nv->sim1_det = val;	
        SAVE_FAC_PARAM(sim1_det);
    }
    else if (!strcmp(param, "PWRKEY"))
    {
        g_softap_fac_nv->pwrkey_ctl = val;	
        SAVE_FAC_PARAM(pwrkey_ctl);
    }
    else if (!strcmp(param, "CLOSESIM1"))
    {
        g_softap_fac_nv->close_sim1 = val;	
        SAVE_FAC_PARAM(close_sim1);
    }
    else if (!strcmp(param, "DTRPIN"))
    {
        g_softap_fac_nv->dtrpin_ctl = val;	
        SAVE_FAC_PARAM(dtrpin_ctl);
    }
	else if (!strcmp(param, "ATWAKUP"))
    {
        g_softap_fac_nv->lpuart_wakup = val;	
        SAVE_FAC_PARAM(lpuart_wakup);
    }
	else if (!strcmp(param, "RIPIN"))
    {
        g_softap_fac_nv->ripin_ctl = val;
        SAVE_FAC_PARAM(ripin_ctl);
    }
	else if(!strcmp(param, "PCTRRM"))
	{
		g_softap_fac_nv->pctrrm = val;
		SAVE_FAC_PARAM(pctrrm);
	}
	else if (!strcmp(param, "DNSVALID"))
    {
        g_app_basic_cfg.dns_ttl_invaild = val;
        SAVE_APP_BASIC_CFG_PARAM(dns_ttl_invaild);
    }
	else if (!strcmp(param, "ACKNUM"))
    {
        g_app_basic_cfg.ack_num = val;
        SAVE_APP_BASIC_CFG_PARAM(ack_num);
		ulTcpAckMergeInit();
    }
	else if (!strcmp(param, "ACKDELAY"))
    {
        g_app_basic_cfg.ack_delay = val / 10;
        SAVE_APP_BASIC_CFG_PARAM(ack_delay);
		ulTcpAckMergeInit();
    }
	else if (!strcmp(param, "FTPLINK"))
    {
        g_app_basic_cfg.ftp_links = val;
        SAVE_APP_BASIC_CFG_PARAM(ftp_links);
    }
	else if (!strcmp(param, "TCPSERVWND"))
    {
        g_app_basic_cfg.tcpServAdjAckWnd = val;
        SAVE_APP_BASIC_CFG_PARAM(tcpServAdjAckWnd);
    }
	else if (!strcmp(param, "TCPSERVRETRY"))
    {
        g_app_basic_cfg.tcpServRetryCount = val;
        SAVE_APP_BASIC_CFG_PARAM(tcpServRetryCount);
    }
	else if (!strcmp(param, "TCPSERVPORT"))
    {
        g_app_basic_cfg.tcpServPort = val;
        SAVE_APP_BASIC_CFG_PARAM(tcpServPort);
    }
	else if (!strcmp(param, "SOCKRDMAX"))
    {
        g_app_basic_cfg.sockSingleReadMax = val;
        SAVE_APP_BASIC_CFG_PARAM(sockSingleReadMax);
    }		
	else if (!strcmp(param, "DNSPRIO"))
    {
        g_app_basic_cfg.dns_priority = val;
        SAVE_APP_BASIC_CFG_PARAM(dns_priority);
    }
    else if (!strcmp(param, "USBLINK"))
    {
		g_app_basic_cfg.usbnet_linkdown = val;
		SAVE_APP_BASIC_CFG_PARAM(usbnet_linkdown);
    }
	else if(!strcmp(param, "GNSSBAUDRATE"))
	{
		g_softap_fac_nv->gnss_baudrate = val;
		SAVE_FAC_PARAM(gnss_baudrate);
	}
	else if(!strcmp(param, "DLPRIO"))
	{
		g_app_basic_cfg.dlProcThrdPrio = val;
		SAVE_APP_BASIC_CFG_PARAM(dlProcThrdPrio);
	}
    else
        return 0;
    return 1;
}

//AT+NV=SET,IPLOG,<operation>
static int32_t ip_log_process(char *at_buf)
{
	int32_t operation = -1;
	if (at_parse_param(",,%d(0|1)", at_buf, &operation)!= XY_OK)
	{
		return XY_ERR;	
	}
	else
	{
		if(operation  == 0)//close ip log
		{
			ip_log_stop();
		}
		else if(operation == 1) //start ip_log
		{
			uint32_t remote_ip = 0x6400a8c0;
			ip_log_set_ip(0, remote_ip);
			ip_log_set_port(0, 8134);
			ip_log_start();
		}
		else if(operation == 2)//start ip_log
		{
			char server_ip[20] = {0};
			int32_t server_port = -1;
			if (at_parse_param(",,,%24s(),%d(0-65535)", at_buf, server_ip, &server_port)!= XY_OK)
			{
				return XY_ERR;
			}

			struct in_addr addr;
			addr.s_addr = inet_addr(server_ip);
			xy_printf(0,XYAPP, INFO_LOG, "server_ip%s,ip:%d",server_ip,addr.s_addr);
			ip_log_set_ip(0, addr.s_addr);
			ip_log_set_port(0, server_port);
			ip_log_start();
		}


		return XY_OK;
	}
}

static int simple_get_val(char *param, char **prsp_cmd)
{
	if (!strcmp(param, "LOGPERIOD"))
		sprintf(*prsp_cmd, "%s%u",gRNstr,g_softap_fac_nv->log_period);
	else if (!strcmp(param, "ADCRANGE"))
		sprintf(*prsp_cmd, "%s%u",gRNstr,g_softap_fac_nv->adc_volrange);

	else if (!strcmp(param, "USBMODE"))
		sprintf(*prsp_cmd, "%s%u",gRNstr,g_softap_fac_nv->usb_mode&0x3F);	 

	else if (!strcmp(param, "USBNODE"))
		sprintf(*prsp_cmd, "%s%u",gRNstr,g_softap_fac_nv->usb_node*4); 
	
	else if (!strcmp(param, "MODEMNODE"))
		sprintf(*prsp_cmd, "%s%u",gRNstr,g_app_basic_cfg.modem_node*4);

	else if (!strcmp(param, "DOWNFLOW"))
		sprintf(*prsp_cmd, "%s%u",gRNstr,g_softap_fac_nv->down_min_bytes); 

	else if (!strcmp(param, "UPFLOW"))
		sprintf(*prsp_cmd, "%s%u",gRNstr,g_softap_fac_nv->up_min_bytes); 
	
	else if (!strcmp(param, "SUBNET"))
		sprintf(*prsp_cmd, "%s%u",gRNstr,g_softap_fac_nv->ue_ip4_type);
	
	else if (!strcmp(param, "PPPCID"))
		sprintf(*prsp_cmd, "%s%u",gRNstr,g_app_basic_cfg.ppp_cid);	
	
	else if (!strcmp(param, "USBCID"))
		sprintf(*prsp_cmd, "%s%u",gRNstr,g_app_basic_cfg.usb_cid);

	else if (!strcmp(param, "ATERRRPT"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->at_err_report);

	else if(!strcmp(param, "SLEEPDELAY"))
		sprintf(*prsp_cmd,"%s%u",gRNstr,g_softap_fac_nv->sleep_delay);

	else if(!strcmp(param, "OFFWAKUP"))
		sprintf(*prsp_cmd,"%s%u",gRNstr,g_softap_fac_nv->off_self_wakup);
		
	else if(!strcmp(param, "OFFRCWAKUP"))
		sprintf(*prsp_cmd,"%s%u",gRNstr,g_softap_fac_nv->off_rc_wakeup);

	else if(!strcmp(param, "LPMMODE") || !strcmp(param, "LOCK"))
		sprintf(*prsp_cmd,"%s%d",gRNstr,g_softap_fac_nv->sleep_mode);
		
	else if (!strcmp(param, "WDT"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->utc_wdt_sec);

	else if (!strcmp(param, "WIRESHARK"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->log_wireshark);

	else if (!strcmp(param, "URCCFG"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->urc_cfg);

	else if (!strcmp(param, "URCCACHE"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->urc_cache);

	else if (!strcmp(param, "VER"))
		sprintf(*prsp_cmd, "%s%s,%s,%s,%s",gRNstr,g_softap_fac_nv->manufacturer, g_softap_fac_nv->modul_ver, g_softap_fac_nv->hardver, g_softap_fac_nv->versionExt);

	else if(!strcmp(param, "LOCALRCVWND"))
		sprintf(*prsp_cmd, "%s%u",gRNstr,g_app_basic_cfg.local_tcp_rcv_wnd);

	else if(!strcmp(param, "MTU"))
		sprintf(*prsp_cmd, "%s%u",gRNstr,g_app_basic_cfg.mtu);

	else if(!strcmp(param, "TCPBUFF"))
		sprintf(*prsp_cmd, "%s%u",gRNstr,g_app_basic_cfg.sockDlfcLimit);

	else if (!strcmp(param, "DBGLOG"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->debug_log_tx);

	else if (!strcmp(param, "LPUARTRBLEN"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->lpuart_rb_len);

	else if (!strcmp(param, "UARTSET"))
		sprintf(*prsp_cmd, "%s%d,%d",gRNstr,g_softap_fac_nv->at_uart_rate*2400,(g_softap_var_nv->at_ipr & 0x7FFF)*2400);

	else if (!strcmp(param, "DONGLE"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->dongle);

	else if (!strcmp(param, "ZONE"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->zone);

	else if (!strcmp(param, "SPECIAL"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->special);

	else if (!strcmp(param, "ASSERTSUM"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->assertSum);
	
	else if (!strcmp(param, "CLOSEDEBUG"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->off_debug);
	
	else if (!strcmp(param, "OFFCHECK"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->off_check);

	else if (!strcmp(param, "MIPSDBG"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->mips_debug);

	else if (!strcmp(param, "FASTCONNREL"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->fast_conn_rel);
	
	else if (!strcmp(param, "TEST"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->test);
		
	else if (!strcmp(param, "DEEPSLEEP"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->deepsleep_enable);
	
	else if (!strcmp(param, "SLEEP"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->sleep_enable);

	else if (!strcmp(param, "LIGHTSLEEP"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->lightsleep_enable);

	else if (!strcmp(param, "DEEPSLEEPTHRESHOLD"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->deepsleep_threshold);

	else if (!strcmp(param, "SLEEPTHRESHOLD"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->sleep_threshold);
	else if (!strcmp(param, "FREQHOP"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->freq_hop);
	else if (!strcmp(param, "VDDIO"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->vdd1p8_sel);
	else if (!strcmp(param, "AUDIOCFG"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->audio_cfg);

	else if (!strcmp(param, "DSLEEPADVANCE"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,gCpLpmDebugInfo->dsleep_full_advance_100us);

	else if (!strcmp(param, "SMALLADVANCE"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,gCpLpmDebugInfo->dsleep_small_advance_100us);

	else if (!strcmp(param, "SLEEPADVANCE"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,gCpLpmDebugInfo->sleep_advance_100us);

	else if (!strcmp(param, "EXTVER"))
		sprintf(*prsp_cmd, "%s%s",gRNstr,g_softap_fac_nv->versionExt);
	
	else if (!strcmp(param, "HARDVER"))
		sprintf(*prsp_cmd, "%s%s",gRNstr,g_softap_fac_nv->hardver);

	else if (!strcmp(param, "LOG"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->open_log);

	else if (!strcmp(param, "LOGSPEEDLIMIT"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->log_speed_limit);

	else if (!strcmp(param, "LOGSPEEDMAX"))
		sprintf(*prsp_cmd, "%s%lx",gRNstr,*(volatile uint64_t*)(LOG_SPEED_MAX));

	else if (!strcmp(param, "WFI"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->wfi_enable);
		
	else if (!strcmp(param, "PIN1"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->state_pin1);
	
	else if (!strcmp(param, "PIN2"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->state_pin2);
	
	else if (!strcmp(param, "PIN3"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->state_pin3);

	else if (!strcmp(param, "ADC0"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->pad_adc0);

	else if (!strcmp(param, "ADC1"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->pad_adc1);

	else if (!strcmp(param, "PWM0"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->pad_pwm0);

	else if (!strcmp(param, "PWM1"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->pad_pwm1);

	else if (!strcmp(param, "IO0"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->pad_io0);

	else if (!strcmp(param, "IO1"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->pad_io1);

	else if (!strcmp(param, "IO2"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->pad_io2);

	else if (!strcmp(param, "IO3"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->pad_io3);

	else if (!strcmp(param, "CTSPIN"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->lpuart_cts_pin);

	else if (!strcmp(param, "RTSPIN"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->lpuart_rts_pin);

	else if (!strcmp(param, "ATDTRPIN"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->lpuart_dtr_pin);

	else if (!strcmp(param, "DCDPIN"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->lpuart_dcd_pin);

	else if (!strcmp(param, "GWNET"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->gw_act_mode);

	else if (!strcmp(param, "SIMTEST"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->sim_test);
	else if (!strcmp(param, "LOGPORT"))
			sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->log_port);
	else if (!strcmp(param, "TCPUPWND"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_app_basic_cfg.tcpUlAckWnd);
	else if (!strcmp(param, "TCPDOWNWND"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_app_basic_cfg.tcpDlAckWnd);
	else if (!strcmp(param, "DM2"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->dm2_output_vol);
	else if (!strcmp(param, "INDUCTOR"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->inductor);
	else if (!strcmp(param, "VBATMODE"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->vbat_mode);
	else if (!strcmp(param, "SLEEP1P8"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->sleep_1p8);
	else if (!strcmp(param, "RESETMODE"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->reset_mode);
	else if (!strcmp(param, "WKUPRST"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->wkuprst_ctl);
	else if (!strcmp(param, "POWERSAVE"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->power_save);
	else if (!strcmp(param, "RFPOWER"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,HWREG(RETENTION_RF_POWER_CTRL));
	else if (!strcmp(param, "SIM0DET"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->sim0_det);
	else if (!strcmp(param, "SIM1DET"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->sim1_det);
	else if (!strcmp(param, "PWRKEY"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->pwrkey_ctl);
	else if (!strcmp(param, "CLOSESIM1"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->close_sim1);
	else if (!strcmp(param, "DTRPIN"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->dtrpin_ctl);
	else if (!strcmp(param, "ATWAKUP"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->lpuart_wakup);
	else if (!strcmp(param, "RIPIN"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->ripin_ctl);
	else if(!strcmp(param, "PCTRRM"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->pctrrm);
	else if(!strcmp(param, "DNSVALID"))
		sprintf(*prsp_cmd,"%s%u",gRNstr,g_app_basic_cfg.dns_ttl_invaild);
	else if (!strcmp(param, "MULTIMAX"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->int_Multi_Max);
	else if (!strcmp(param, "MULTITIMEOUT"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->int_Multi_Timeout);
	else if(!strcmp(param, "ACKNUM"))
		sprintf(*prsp_cmd,"%s%u",gRNstr,g_app_basic_cfg.ack_num);
	else if(!strcmp(param, "ACKDELAY"))
		sprintf(*prsp_cmd,"%s%u",gRNstr,g_app_basic_cfg.ack_delay * 10);
	else if (!strcmp(param, "FTPLINK"))
		sprintf(*prsp_cmd,"%s%u",gRNstr,g_app_basic_cfg.ftp_links);
	else if (!strcmp(param, "TCPSERVWND"))
		sprintf(*prsp_cmd,"%s%u",gRNstr,g_app_basic_cfg.tcpServAdjAckWnd);
	else if (!strcmp(param, "TCPSERVRETRY"))
		sprintf(*prsp_cmd,"%s%u",gRNstr,g_app_basic_cfg.tcpServRetryCount);
	else if (!strcmp(param, "TCPSERVPORT"))
		sprintf(*prsp_cmd,"%s%u",gRNstr,g_app_basic_cfg.tcpServPort);
	else if (!strcmp(param, "SOCKRDMAX"))
		sprintf(*prsp_cmd,"%s%u",gRNstr,g_app_basic_cfg.sockSingleReadMax);
	else if (!strcmp(param, "DNSPRIO"))
		sprintf(*prsp_cmd,"%s%u",gRNstr,g_app_basic_cfg.dns_priority);
	else if (!strcmp(param, "USBLINK"))
		sprintf(*prsp_cmd,"%s%u",gRNstr,g_app_basic_cfg.usbnet_linkdown);
	else if(!strcmp(param, "GNSSBAUDRATE"))
		sprintf(*prsp_cmd, "%s%d",gRNstr,g_softap_fac_nv->gnss_baudrate);
	else if(!strcmp(param, "DLPRIO"))
		sprintf(*prsp_cmd, "%s%d",gRNstr, g_app_basic_cfg.dlProcThrdPrio);
	else
		return 0;
	return 1;
}


/*******************************************************************************
 *						Global function implementations 					   *
 ******************************************************************************/
int at_NV_req(char *at_buf, char **prsp_cmd)
{
	int atHandle = get_current_ttyFd();
	if (g_req_type == AT_CMD_REQ)
	{
		char cmd[10] = {0};
		char param[20] = {0};
		char verval[30] = {0};
		int val = 0;

		if (at_parse_param("%10s,", at_buf, cmd) != XY_OK)
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			return AT_END;
		}
		/*强行将出厂NV全局数据写入原始出厂NV，仅限极特殊测试场景使用*/
		else if (!strcmp(cmd, "FAC"))
		{
			xy_flash_write_safe((void*)(NV_MAIN_SOFTAP_FACTORY_BASE+4), g_softap_fac_nv, sizeof(softap_fac_nv_t));
			return AT_END;
		}
		else if (!strcmp(cmd, "SET"))
		{
			if (at_parse_param(",%20s,", at_buf, param) != XY_OK)
				goto ERR;

			/*AT+NV=SET,<softap_fac_nv_t param offset>,<param val>[,<param len>]   len可以不填，默认为1字节*/		
			else if(is_digit_str(param))
			{
				uint32_t  val = 0;
				uint32_t  param2 = 0;
				uint32_t  param3 = 0;
				at_parse_param(",%d,%d,%d", at_buf,&val,&param2,&param3);
			
				if(param3 == 0)
					param3 = 1;
				
				SAVE_FAC_PARAM2(val,param2,param3);
			}

			else if (!strcmp(param, "MODULVER"))
			{
				if (at_parse_param(",,%20s", at_buf, verval))
					goto ERR;

				memset(g_softap_fac_nv->modul_ver, 0, sizeof(g_softap_fac_nv->modul_ver));
				memcpy(g_softap_fac_nv->modul_ver, verval, strlen(verval));
				SAVE_FAC_PARAM(modul_ver);
			}
			else if (!strcmp(param, "HARDVER"))
			{
				if (at_parse_param(",,%20s", at_buf, verval))
					goto ERR;
				memset(g_softap_fac_nv->hardver, 0, sizeof(g_softap_fac_nv->hardver));
				memcpy(g_softap_fac_nv->hardver, verval, strlen(verval));
				SAVE_FAC_PARAM(hardver);
			}
			else if (!strcmp(param, "VERSIONEXT"))
			{
				if (at_parse_param(",,%28s", at_buf, verval))
					goto ERR;
				memset(g_softap_fac_nv->versionExt, 0, sizeof(g_softap_fac_nv->versionExt));
				memcpy(g_softap_fac_nv->versionExt, verval, strlen(verval));
				SAVE_FAC_PARAM(versionExt);
			}
            else if(!strcmp(param, "CPJLINK"))
            {
                if (at_parse_param(",,%1d,%1d", at_buf, &verval[0], &verval[1]))
                {
                    goto ERR;
                }
                g_softap_fac_nv->cpjlink[0] = verval[0];
                g_softap_fac_nv->cpjlink[1] = verval[1];	
                SAVE_FAC_PARAM(cpjlink[0]);
                SAVE_FAC_PARAM(cpjlink[1]);
				SAVE_MAIN_FAC_PARAM(cpjlink[0]);
                SAVE_MAIN_FAC_PARAM(cpjlink[1]);
            }
			else if(!strcmp(param, "IPLOG"))
			{
				if(ip_log_process(at_buf) != XY_OK)
				{
					*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
				}	
				return AT_END;
			}	
            else if(!strcmp(param, "CSPLOG"))
            {
                if (at_parse_param(",,%1d,%1d", at_buf, &verval[0], &verval[1]))
                {
                    goto ERR;
                }
                g_softap_fac_nv->csp_log_tx = verval[0];
                g_softap_fac_nv->csp_log_rx = verval[1];	
                SAVE_FAC_PARAM(csp_log_tx);
				SAVE_FAC_PARAM(csp_log_rx);
            }
			/*AT+NV=SET,GWIP,4,************* 或 fe80::1234*/
			else if (!strcmp(param, "GWIP"))
			{
				int type = 4;
				char *ip_str = NULL;
				if (at_parse_param(",,%d,%47p", at_buf, &type, &ip_str) != XY_OK)
				{
					*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
					return AT_END;
				}
				if (type == 4)
				{
					ip4_addr_t ip4 = {0};
					inet_aton(ip_str, &ip4);
					g_app_basic_cfg.def_gw_ip4 = ip4.addr;
					SAVE_APP_BASIC_CFG_PARAM(def_gw_ip4);
				}			
				else /*AT+NV=SET,GWIP,fe80::1234*/
				{
					ip6_addr_t ip6 = {0};
					inet6_aton(ip_str, &ip6);
					memcpy(g_app_basic_cfg.def_gw_ip6_local, ip6.addr, sizeof(g_app_basic_cfg.def_gw_ip6_local));
					SAVE_APP_BASIC_CFG_PARAM(def_gw_ip6_local);
				}
			}	
			else if(!strcmp(param, "PPPLOGIN"))
			{
				int auth_type = 0;
				char username[13] = {0};
				char password[13] = {0};

				if (at_parse_param(",,%d(0-255),%13s,%13s", at_buf, &auth_type, username, password) != XY_OK)
				{
					*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
					return AT_END;
				}
				g_app_basic_cfg.ppp_auth_type = auth_type;
				SAVE_APP_BASIC_CFG_PARAM(ppp_auth_type);
				strncpy(g_app_basic_cfg.ppp_username, username, 12);				
				SAVE_APP_BASIC_CFG_PARAM(ppp_username);
				strncpy(g_app_basic_cfg.ppp_password, password, 12);				
				SAVE_APP_BASIC_CFG_PARAM(ppp_password);				
			}
			else if(!strcmp(param, "DNSIP"))
			{
				int type = 4;
				char *dnsStr1 = NULL;
				char *dnsStr2 = NULL;

				if (at_parse_param(",,%d(4|6),%47p,%47p", at_buf, &type, &dnsStr1, &dnsStr2) != XY_OK)
				{
					*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
					return AT_END;
				}
				int aton_ret;
				ip_addr_t atonAddr = {0};

				if (dnsStr1 != NULL)
				{
					// 检测host是否是IP地址字符串格式，是则返回1
					aton_ret = ipaddr_aton(dnsStr1, &atonAddr);
					if (aton_ret)
					{
						if (atonAddr.type == IPADDR_TYPE_V4)
						{
							g_app_basic_cfg.def_pridns4 = ip_2_ip4(&atonAddr)->addr;
							SAVE_APP_BASIC_CFG_PARAM(def_pridns4);
						}
						else if (atonAddr.type == IPADDR_TYPE_V6)
						{
							memcpy(g_app_basic_cfg.def_pridns6, ip_2_ip6(&atonAddr), 16);
							SAVE_APP_BASIC_CFG_PARAM(def_pridns6);
						}
					}
				}

				if (dnsStr2 != NULL)
				{
					// 检测host是否是IP地址字符串格式，是则返回1
					aton_ret = ipaddr_aton(dnsStr2, &atonAddr);
					if (aton_ret)
					{
						if (atonAddr.type == IPADDR_TYPE_V4)
						{
							g_app_basic_cfg.def_secdns4 = ip_2_ip4(&atonAddr)->addr;
							SAVE_APP_BASIC_CFG_PARAM(def_secdns4);
						}
						else if (atonAddr.type == IPADDR_TYPE_V6)
						{
							memcpy(g_app_basic_cfg.def_secdns6, ip_2_ip6(&atonAddr), 16);
							SAVE_APP_BASIC_CFG_PARAM(def_secdns6);
						}
					}
				}
			}							
            else
			{
                if (at_parse_param(",%20s,%d", at_buf, param, &val) != XY_OK)
                    goto ERR;

                if (simple_set_val(param, val) == 1)
                    return AT_END;
				else
					return AT_FORWARD;
            }
			return AT_END;
		}
		else if (!strcmp(cmd, "GET"))
		{
            char sub_param[15] = {0};
            if (at_parse_param(",%20s,%15s", at_buf, param, sub_param) != XY_OK)
				goto ERR;

			*prsp_cmd = xy_malloc(500);

            if (simple_get_val(param, prsp_cmd) == 1)
            {
				return AT_END;
            }
			/*AT+NV=GET,<softap_fac_nv_t param offset>[,<param len>]   len可以不填，默认为1字节*/
			else if(is_digit_str(param))
			{
				uint32_t  val = 0;
				uint32_t  param2 = 0;
				at_parse_param(",%d,%d", at_buf,&val,&param2);

				if(param2 == 0)
					param2 = 1;
				
				READ_FAC_PARAM2(val,val,param2);

				*prsp_cmd = xy_malloc(40);
				sprintf(*prsp_cmd, "%s%d",gRNstr,val);	
			}
			/*该返回值与AT+QGDCNT? 查询的结果应该保持一致，若出入较大，表明有SoC内部异常丢包*/
			else if(!strcmp(param, "FLOW"))
            {
                sprintf(*prsp_cmd, "\r\n%d,%d,%d,%d",GetUplinkBytes(),GetUplinkAllBytes(),GetDownlinkBytes(),GetDownlinkAllBytes());
            }
            else if(!strcmp(param, "CPJLINK"))
            {
                sprintf(*prsp_cmd, "\r\n%d,%d", g_softap_fac_nv->cpjlink[0], g_softap_fac_nv->cpjlink[1]);
            }
            else if(!strcmp(param, "CSPLOG"))
            {
                sprintf(*prsp_cmd, "\r\n%d,%d", g_softap_fac_nv->csp_log_tx, g_softap_fac_nv->csp_log_rx);
            }
			else if (!strcmp(param, "FAC"))
			{
                sprintf(*prsp_cmd,   \
				"\r\noff_debug=%d,utc_wdt_sec=%d,deepsleep_enable=%d,deepsleep_threshold=%d,sleep_delay=%d,sleep_enable=%d,lightsleep_enable=%d,wfi_enable=%d,lpuart_wakup=%d,dtrpin_ctl=%d,ue_ip4_type=%d,log_wireshark=%d,open_log=%d,gw_act_mode=%d,log_port=%d,usb_mode=%d",  \
                    g_softap_fac_nv->off_debug,g_softap_fac_nv->utc_wdt_sec,g_softap_fac_nv->deepsleep_enable,g_softap_fac_nv->deepsleep_threshold,g_softap_fac_nv->sleep_delay,g_softap_fac_nv->sleep_enable,g_softap_fac_nv->lightsleep_enable,g_softap_fac_nv->wfi_enable,  \
                    g_softap_fac_nv->lpuart_wakup,g_softap_fac_nv->dtrpin_ctl,g_softap_fac_nv->ue_ip4_type,g_softap_fac_nv->log_wireshark,g_softap_fac_nv->open_log,g_softap_fac_nv->gw_act_mode,g_softap_fac_nv->log_port,g_softap_fac_nv->usb_mode);
			}
			else if (!strcmp(param, "FTL"))
			{
				int addr = 0;
				hexstr2int(sub_param, &addr);
                extern int ftl_read_write_num(uint32_t addr);
				sprintf(*prsp_cmd, "\r\n%d", (int)ftl_read_write_num(addr));
			}
#if USB_DEVICE
            else if (!strcmp(param, "MAC"))
            {
            	uint8_t gw_mac[6] = {0};
            	uint8_t *pc_mac = ux_get_mac_address();
                sprintf(*prsp_cmd, "\r\nPC MAC,0x%x:0x%x:0x%x:0x%x:0x%x:0x%x", pc_mac[0],pc_mac[1],pc_mac[2],pc_mac[3],pc_mac[4],pc_mac[5]);
				
				gwEthHwaddrGet((struct eth_addr *)gw_mac);
				sprintf(*prsp_cmd + strlen(*prsp_cmd),"\r\nGW MAC,0x%x:0x%x:0x%x:0x%x:0x%x:0x%x", gw_mac[0],gw_mac[1],gw_mac[2],gw_mac[3],gw_mac[4],gw_mac[5]);
				
            }
#endif
			else if (!strcmp(param, "TICK"))
			{
				long int tick = osKernelGetTickCount();
				sprintf(*prsp_cmd, "\r\n+CPTICK:%ld", tick);
			}
			else if (!strcmp(param, "MODULVER"))
			{
				sprintf(*prsp_cmd, "\r\n+MODULVER:%s", g_softap_fac_nv->modul_ver);
			}
			else if (!strcmp(param, "HARDVER"))
			{
				sprintf(*prsp_cmd, "\r\n+HARDVER:%s", g_softap_fac_nv->hardver);
			}
			else if (!strcmp(param, "VERSIONEXT"))
			{
				sprintf(*prsp_cmd, "\r\n+VERSIONEXT:%s", g_softap_fac_nv->versionExt);
			}
			else if (!strcmp(param, "SLEEPSTATE"))
			{
				char *at_str = xy_malloc(200);
				extern int dtr_have_locked(void);
				snprintf(at_str, 200, "\r\nAP Lock State:dslock:%d, slock:%d, lslock:%d, dtr_have_locked:%d\r\n", is_sleep_locked(WL_DEEPSLEEP), is_sleep_locked(WL_SLEEP), is_sleep_locked(WL_LIGHTSLEEP), dtr_have_locked());
				snprintf(at_str + strlen(at_str), 200 - strlen(at_str), "\r\nEnable DSLEEP:%d,%d, SLEEP:%d,%d\r\n",g_softap_fac_nv->deepsleep_enable,g_softap_fac_nv->deepsleep_threshold,g_softap_fac_nv->sleep_enable,g_softap_fac_nv->sleep_threshold);
				snprintf(at_str + strlen(at_str), 200 - strlen(at_str), "\r\nlpuart_wakup:%d, dtrpin_ctl:%d\r\n",g_softap_fac_nv->lpuart_wakup,g_softap_fac_nv->dtrpin_ctl);
				appAtWriteImmediately(atHandle, at_str, strlen(at_str));
				xy_free(at_str);
				goto FORWARD;
			}
			else if (!strcmp(param, "FLASHID"))
			{
				uint8_t uid[16];
				uint32_t len = 0;
				uint32_t i = 0;

				flash_proxy_get_uid(uid, sizeof(uid));
				len += sprintf(*prsp_cmd + len, "\r\n");
				for(i = 0; i < 16; i ++)
				{
					len += sprintf(*prsp_cmd + len, "%02X", ((uint8_t *)uid)[i]);
				}
			}
			else if (!strcmp(param, "GWIP"))
			{
				int n;
				char ip4_str[XY_IP4ADDR_STRLEN] = {0};
				char ip6_str[XY_IP6ADDR_STRLEN] = {0};
				ip4_addr_t ip4 = {
					.addr = g_app_basic_cfg.def_gw_ip4
				};
				ip6_addr_t ip6;
				
				inet_ntop(AF_INET, &ip4, ip4_str, XY_IP4ADDR_STRLEN);
				memcpy(ip6.addr, g_app_basic_cfg.def_gw_ip6_local, sizeof(ip6.addr));
				ip6_addr_clear_zone(&ip6);				
				inet_ntop(AF_INET6, &ip6, ip6_str, XY_IP6ADDR_STRLEN);
				sprintf(*prsp_cmd, "\r\n+GWIP: \"%s\",\"%s\"", ip4_str, ip6_str);				
			}
			else if(!strcmp(param, "PPPLOGIN"))
			{
				sprintf(*prsp_cmd, "\r\n+PPPLOGIN: %d,\"%s\",\"%s\"", g_app_basic_cfg.ppp_auth_type, g_app_basic_cfg.ppp_username, g_app_basic_cfg.ppp_password);							
			}
			else if(!strcmp(param, "DNSIP"))
			{
				int sLen = 0;
				char dnsStrV4[XY_IP4ADDR_STRLEN];
				char dnsStrV6[XY_IP6ADDR_STRLEN];

				dnsStrV4[0] = '\0';
				inet_ntop(AF_INET, &g_app_basic_cfg.def_pridns4, dnsStrV4, XY_IP4ADDR_STRLEN);
				sLen += sprintf(*prsp_cmd + sLen, "\r\n+DNSIP: V4:primary:\"%s\",", dnsStrV4);

				dnsStrV4[0] = '\0';
				inet_ntop(AF_INET, &g_app_basic_cfg.def_secdns4, dnsStrV4, XY_IP4ADDR_STRLEN);
				sLen += sprintf(*prsp_cmd + sLen, "secondary:\"%s\"", dnsStrV4);

				dnsStrV6[0] = '\0';
				inet_ntop(AF_INET6, g_app_basic_cfg.def_pridns6, dnsStrV6, XY_IP6ADDR_STRLEN);
				sLen += sprintf(*prsp_cmd + sLen, "\r\n+DNSIP: V6:primary:\"%s\",", dnsStrV6);

				dnsStrV6[0] = '\0';
				inet_ntop(AF_INET6, g_app_basic_cfg.def_secdns6, dnsStrV6, XY_IP6ADDR_STRLEN);
				sLen += sprintf(*prsp_cmd + sLen, "secondary:\"%s\"", dnsStrV6);
			}	
			else
			{
FORWARD:
                if (*prsp_cmd != NULL)
                {
                    xy_free(*prsp_cmd);
                    *prsp_cmd = NULL;
                }
                return AT_FORWARD;
			}
			return AT_END;
		}
	ERR:
		*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
	}
#if (!AT_TEST_OFF)
	else if (g_req_type == AT_CMD_TEST)
	{
		char *str = "\r\n+NV:WDT+ARMSTACK+CLOSEDEBUG+NPSMR+DOWNDATA+DEEPSLEEP+STANDBY+EXTVER+HARDVER+VERTYPE+DM+WFI+PSM+VER+UARTSET+PIN(SWD+JTAG+AT+LOG+LED)";
		*prsp_cmd = xy_malloc(strlen(str) + 1);
		strcpy(*prsp_cmd, str);
	}
#endif
	else
		*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);

	return AT_END;
}
#endif

//恢复出厂设置,即擦除工作态出厂NV
int at_ANDF_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_ACTIVE)
    {
		if(*(at_buf-2) != '&')
			return AT_FORWARD;

		int8_t value = 0;
        if (at_parse_param("%1d[0-0]", at_buf, &value) != XY_OK)
        {
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
		}

        do_at_and_f();
		return AT_FORWARD;
    }
#if (!AT_TEST_OFF)
	else if (g_req_type == AT_CMD_TEST)
    {
		return AT_END;
    }
#endif
	else
	{
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
    return AT_END;
}

// 设置命令回显模式：0关闭，1开启
int at_ATE_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_ACTIVE)
    {
		if (at_strncasecmp(at_buf, "0V1"))
        {
            // Todo: 在Windows系统中建立PPP拨号连接时会发ATE0V1
        }
		else
		{
			uint8_t echo_mode = g_softap_fac_nv->ate;  /* 中移ATE默认为ATE0 */

			if (at_parse_param("%1d[0-1]", at_buf, &echo_mode) != XY_OK)
			{
				*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
			}

			set_ate_mode(echo_mode);
		}	
    }
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
    return AT_END;
}

int at_ATO_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_ACTIVE)
    {
        int atHandle = get_current_ttyFd();

        if (atPassthStateResumeByATO(atHandle) == XY_OK)
            return AT_ASYN;
        else
        {
            appAtResp(atHandle, PASSTH_RSP_NOCARR);
            return AT_ASYN;
        }
    }
    else
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);

    return AT_END;
}

//恢复AT命令控制的工作态NV参数
int at_ATZ_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_ACTIVE)
    {
        int8_t value = 0;
        if (at_parse_param("%1d[0-0],", at_buf, &value) != XY_OK)
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

        int ret = do_at_z();
		if (ret == XY_OK)
			return AT_FORWARD;

        *prsp_cmd = AT_PLAT_CME_ERR(ret);
    }
    else
	{
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
	return AT_END;
}

//显示一些AT命令当前的配置
int at_ANDV_req(char *at_buf, char **prsp_cmd)
{
	(void) at_buf;
    if (g_req_type == AT_CMD_ACTIVE)
    {
        *prsp_cmd = xy_malloc(256);
        do_at_and_v(*prsp_cmd);
    }
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
    return AT_END;
}

//写AT命令控制的出厂NV参数到工作态出厂NV中
int at_ANDW_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_ACTIVE)
    {
		if(*(at_buf-2) != '&')
			return AT_FORWARD;

        int8_t num = 0;
        if (at_parse_param("%1d[0-0]", at_buf, &num) != XY_OK)
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }
        int ret = do_at_and_w();
		if (ret == XY_OK)
			return AT_FORWARD;
        *prsp_cmd = AT_PLAT_CME_ERR(ret);
    }
	else
	{
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
	return AT_END;
}

int at_ATQ_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_ACTIVE)
    {
        if (at_strncasecmp(at_buf, "0E0V1") == 1)
        {
            set_atq_mode(0);
            set_ate_mode(0);
            set_atv_mode(1);
        }
        else
        {
            uint8_t val = 0;
            if (at_parse_param("%1d(0-1)", at_buf, &val) != XY_OK)
            {
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }
            set_atq_mode(val);
        }   
    }
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
    return AT_END;
}

int at_ATV_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_ACTIVE)
    {
        uint8_t val = 0;
        if (at_parse_param("%1d(0-1)", at_buf, &val) != XY_OK)
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }
        set_atv_mode(val);
    }
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
    return AT_END;
}

// TODO:设置自动应答前振铃次数，不属于平台范畴，但是用PC机测试PPP时会用到，暂在这里进行打桩
int at_ATS0_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_REQ)
    {
        uint8_t val = 0;
        if (at_parse_param("%1d(0-255),", at_buf, &val) != XY_OK)
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }
        set_ats_val(0, val);
    }
    else if (g_req_type == AT_CMD_QUERY)
    {
        *prsp_cmd = xy_malloc(32);
        snprintf(*prsp_cmd, 32, "\r\n%03d", get_ats_val(0));
    }
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
    return AT_END;
}

// 设置命令行终止符
int at_ATS3_req(char *at_buf, char **prsp_cmd)
{
	if(g_req_type == AT_CMD_REQ)
	{
		uint8_t val = 0;
        if (at_parse_param("%1d(0-127),", at_buf, &val) != XY_OK)
        {
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
		}
		set_ats_val(3, val);
	}
	else if(g_req_type == AT_CMD_QUERY)
	{
		*prsp_cmd = xy_malloc(32);
		snprintf(*prsp_cmd, 32, "\r\n%03d", get_ats_val(3));
	}
	else
	{
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
	return AT_END;
}

//设置响应格式符
int at_ATS4_req(char *at_buf, char **prsp_cmd)
{
	if(g_req_type == AT_CMD_REQ)
	{
		uint8_t val = 0;
		if(at_parse_param("%1d(0-127),", at_buf, &val) != XY_OK)
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
		}
		set_ats_val(4, val);
	}
	else if(g_req_type == AT_CMD_QUERY)
	{
		*prsp_cmd = xy_malloc(32);
		snprintf(*prsp_cmd, 32, "\r\n%03d", get_ats_val(4));
	}
	else
	{
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
	return AT_END;
}

//设置命令行编辑字符
int at_ATS5_req(char *at_buf, char **prsp_cmd)
{
	if(g_req_type == AT_CMD_REQ)
	{
		uint8_t val = 0;
		if(at_parse_param("%1d(0-127),", at_buf, &val) != XY_OK)
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
		}
		set_ats_val(5, val);
	}
    else if (g_req_type == AT_CMD_QUERY)
    {
		*prsp_cmd = xy_malloc(32);
		snprintf(*prsp_cmd, 32, "\r\n%03d", get_ats_val(5));
	}
	else
	{
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
	return AT_END;
}


#if NO_USE
int at_NUESTATS_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_REQ)
	{
		char subcmd[32] = {0};
		if (at_parse_param("%32s", at_buf, subcmd) != XY_OK)
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			return AT_END;
		}

		if (at_strcasecmp(subcmd, "APPSMEM"))
		{
#define NUESTATS_APPMEM_PREFIX "\r\nNUESTATS:APPSMEM,"
			uint32_t len = 0;
			osHeapInfo_T heapInfo = {0};
			osHeapInfoGet(&heapInfo);
			*prsp_cmd = (char *)xy_malloc(320);
			len = sprintf(*prsp_cmd, "%sCurrent Allocated:%d", NUESTATS_APPMEM_PREFIX, heapInfo.AllocatedHeapSize);
			len += sprintf(*prsp_cmd + len, "%sTotal Free:%d", NUESTATS_APPMEM_PREFIX, heapInfo.FreeRemainHeapSize);
			len += sprintf(*prsp_cmd + len, "%sMax Free:%d", NUESTATS_APPMEM_PREFIX, heapInfo.MaxSizeFreeBlock);
			len += sprintf(*prsp_cmd + len, "%sNum Allocs:%d", NUESTATS_APPMEM_PREFIX, heapInfo.AllocatedBlockNum);
			len += sprintf(*prsp_cmd + len, "%sNum Frees:%d\r\n", NUESTATS_APPMEM_PREFIX, heapInfo.FreedBlockNum);
			return AT_END;
		}
		/* AP内存统计信息, 非对标 */
		else if (at_strcasecmp(subcmd, "APMEMINFO"))
		{
			uint32_t len = 0;
			*prsp_cmd = (char *)xy_malloc(960);
			extern uint32_t gMallocFailNum;
			osHeapInfo_T heapInfo = {0};
			osHeapInfoGet(&heapInfo);
			len = sprintf(*prsp_cmd, 
				"\r\n"
				"-------AP HEAP INFO-------\r\n"
				"HeapTotalSize:%u\r\n"
				"AllocatedHeapSize:%u\r\n"
				"FreeRemainHeapSize:%u\r\n"
				"MinimumEverFreeHeapSize:%u\r\n"
				"AllocatedBlockNum:%u\r\n"
				"FreedBlockNum:%u\r\n"
				"MallocFail:%u\r\n",
				heapInfo.TotalHeapSize, heapInfo.AllocatedHeapSize, heapInfo.FreeRemainHeapSize,
				heapInfo.MinimumEverFreeHeapSize, heapInfo.AllocatedBlockNum, heapInfo.FreedBlockNum, gMallocFailNum);
		
			void* startAddr = NULL;
			void* endAddr = NULL;
			size_t availSpace = GetAPbinReservedFlash(&startAddr, &endAddr);	
			len += sprintf(*prsp_cmd + len, "\r\n-------AP Bin FLASH INFO-------\r\n");
			len += sprintf(*prsp_cmd + len, "AP Bin flash total size:0x%x\r\n", (uint32_t)AP_FLASH_BASE_LEN);
			len += sprintf(*prsp_cmd + len, "Remaining space start 0x%x, len:0x%x,\r\n", startAddr,availSpace);

			fs_size_info_t fs_info = {0};
			xy_fsize_info("D:", &fs_info);
			len += sprintf(*prsp_cmd + len, "\r\n-------FS INFO-------\r\n");
			len += sprintf(*prsp_cmd + len, "fs total:0x%x, startaddr:0x%x\r\nfs remain:0x%x\r\n", WORKING_FS_LEN, WORKING_FS_BASE, fs_info.lfs_remain_size);
			return AT_END;
		}
		else if (at_strcasecmp(subcmd, "APSTACK"))
		{
			/*Task:线程名,Pri:优先级，Stack:初始栈大小，UsedMax:栈使用峰值，State:栈状态*/
			*prsp_cmd = (char*)xy_malloc(3000);
			uint32_t len = 0;
			len = sprintf(*prsp_cmd, "\r\n-------AP STACK INFO-------\r\n");
			osTaskStackInfo_T *info = osTaskStackInfoGet();
			while (info != NULL)
			{
				len += sprintf(*prsp_cmd + len, "\r\nTask:%s,\t Pri:%d,\t Stack:%d,\t UsedMax:%d,\t State:%d",
								   info->taskName, info->prority, info->initSpace, info->maxUsedSpace, info->state);
				info = info->next;
			}
			osTaskStackInfoFree();
			return AT_END;
		}
	}
	return AT_FORWARD;
}
#endif
extern int set_nat_work_mode(int mode);
/*AT+NSET=STORAGE_OPER,1 表示入库测试场景，重启后生效*/
int at_NSET_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_REQ)
    {
		char param1[20] = {0};
		int val = -1;

		if (at_parse_param("%20s,", at_buf, param1) != XY_OK)
		{
            return AT_FORWARD;
        }

		if (at_strcasecmp(param1, "STORAGE_OPER"))
		{
			if (at_parse_param(",%d", at_buf, &val) != XY_OK)
			{
                return AT_FORWARD;
            }
			if (val == 1)
			{
				g_softap_fac_nv->gw_act_mode = 1;
				g_app_basic_cfg.usbnet_sta_urc = 1;
				g_softap_fac_nv->usb_node = (512/4);
        		SAVE_FAC_PARAM(usb_node);
                SAVE_FAC_PARAM(gw_act_mode);
                SAVE_APP_BASIC_CFG_PARAM(usbnet_sta_urc);    
				set_nat_work_mode(2);
#if DM_CTCC
				telecom_dm_set_module_switch(1);
#endif				
			}	
		}		
	}

    return AT_FORWARD;
}



/*获取ChipID*/
int at_AUTHID_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_QUERY)
	{
		uint8_t uid[16];
		uint32_t len = 0;
		uint32_t i = 0;

		*prsp_cmd = (char *)xy_malloc(64);
		flash_proxy_get_uid(uid, sizeof(uid));
		for (i = 0; i < 16; i++)
		{
			len += sprintf(*prsp_cmd + len, "%02X", ((uint8_t *)uid)[i]);
		}
	}
	return AT_END;
}

