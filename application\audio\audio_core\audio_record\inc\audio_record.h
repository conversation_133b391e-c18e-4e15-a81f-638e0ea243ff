#pragma once

#include <stdint.h>
#include "xy_fs.h"
#include "kfifo.h"
#include "xy_audio.h"

typedef enum {
    REC_ERR_OK = 0,
    REC_ERR_FOPEN_FAILED = -1,
    REC_ERR_ALSA_OPEN_FAILED = -2,
    REC_ERR_MSGQ_CREAT_FAILED = -3,
    REC_ERR_REC_THREAD_CREAT_FAILED = -4,
    REC_ERR_ALSA_START_FAILED = -5,
    REC_ERR_ALSA_NOT_OPEN = -6,
    REC_ERR_DEST_FULL = -7,
    REC_ERR_STOP = -8,
} rec_err_t;

typedef enum {
    REC_FORMAT_PCM = 0,
    REC_FORMAT_MP3,  // not supported yet
    REC_FORMAT_AMR,

    REC_FORMAT_MAX
} rec_format_t;

typedef enum {
    REC_IDLE = 0,
    REC_START,
    REC_STARTED,
    REC_PAUSE,
    REC_STOP,
    REC_STOPED,
} rec_state_t;

typedef struct _rec_msg {
    void * pcm_data;
    uint32_t pcm_data_len;
} rec_msg_t;

typedef struct _audio_rec audio_rec_t;
typedef struct _audio_dest audio_dest_t;

typedef enum
{
    AUDIO_DEST_TYPE_INVALID = 0,

    AUDIO_DEST_TYPE_BUFFER = 1,
    AUDIO_DEST_TYPE_FILE = 2,
    AUDIO_DEST_TYPE_STREAM = 3,

    AUDIO_DEST_MAX,
} audio_dest_type_t;

typedef struct kfifo rec_stream_t;

typedef struct _dest_arg{
    audio_dest_type_t type;
    uint32_t dest_len;
    uint32_t write_len;
    uint32_t duration_ms;
    uint32_t real_duration_ms;
    union
    {
        XY_FILE * file;
        uint8_t ** buf;
        rec_stream_t * stream;
    } u;
}dest_arg_t;

typedef int32_t (*audio_dest_write_t)(void *buf, uint32_t size, dest_arg_t *dest_arg);

typedef struct _audio_dest{
    audio_dest_write_t write;
    dest_arg_t * dest_arg;
}audio_dest_t;

typedef int32_t (*audio_encoder_process_t)(uint8_t * data, uint32_t len, audio_rec_t *audio_rec);
typedef struct _audio_encode{
    audio_encoder_process_t process;
}audio_encode_t;

typedef rec_state_t (*rec_state_get_t)(void);

struct _audio_rec{
    audio_dest_t *dest;
    rec_state_get_t state_get;
    rec_enc_t enc;
};

typedef enum
{
    AUDIO_REQ_REC_INVALID = 0,

    AUDIO_REQ_FILE_REC,
    AUDIO_REQ_BUFFER_REC,
    AUDIO_REQ_STREAM_REC,

    AUDIO_REQ_REC_MAX,
} audio_rec_req_type_t;

typedef struct _audio_rec_req{
    audio_rec_req_type_t type;
    rec_enc_t enc;
    uint32_t sample_rate;
    union
    {
        char * file;
        uint8_t ** buf;
        rec_stream_t * stream;
    } u;

    uint32_t len;
    uint32_t duration_ms;
    rec_complete_cb_t complete_cb;
} audio_rec_req_t;

int32_t audio_rec_req(audio_rec_req_t * req);
int32_t audio_rec_open(uint32_t sample_rate);
uint32_t audio_rec_stream_get(uint8_t * buf, uint32_t len);
int audio_rec_start(void);
void audio_rec_stop(void);
void audio_rec_pause(void);
void audio_rec_resume(void);
int audio_rec_close(void);

