/**
 * @file        xy_at_api.h  
 * @brief       AT-related interfaces and definitions for user development  
 * @warning     Users are prohibited from modifying any code in the AT framework  
 *              or calling any interfaces not exposed in this header without permission!  
 */  
#pragma once  

#include "at_com.h"  
#include "at_context.h"  
#include "at_error.h"  
#include "at_response.h"  
#include "at_utils.h"  
#include <stdbool.h>  
#include <stddef.h>  
#include <stdint.h>  
#include <string.h>  

#ifdef __cplusplus  
extern "C" {  
#endif  

/*******************************************************************************  
 *                       Global function declarations                          *  
 ******************************************************************************/  

/**
 * @brief Sends URC (Unsolicited Result Code) string to specified AT channel (excluding result codes)  
 * @param atHandle [IN] The AT channel identifier, obtained by calling get_current_ttyFd() in the AT command parsing function. See @ref AT_TTY_FD.  
 * @param str [IN] String data to send  
 * @note URC may be buffered if AT command is being processed or in passthrough mode  
 * @warning Never use for intermediate results to avoid sending OK before actual data  
 */  
void appAtURC(int atHandle, char *str);  

/**
 * @brief The application module sends URC (Unsolicited Result Code) data stream to the specified AT channel, excluding result codes.  
 * @param atHandle [IN] The AT channel identifier, obtained by calling get_current_ttyFd() in the AT command parsing function. See @ref AT_TTY_FD. 
 * @param data [IN] Data stream to send  
 * @param dataLen [IN] Data length in bytes  
 * @note Prefer appAtURC() for plaintext URCs  
 * @warning Never use for intermediate results to avoid sending OK before actual data  
 */  
void appAtRawURC(int atHandle, char *data, uint32_t dataLen);  

/**
 * @brief Writes data immediately to AT channel (typically for intermediate results,excluding result codes)  
 * @param atHandle [IN] The AT channel identifier, obtained by calling get_current_ttyFd() in the AT command parsing function. See @ref AT_TTY_FD.   
 * @param data [IN] Data to send  
 * @param dataLen [IN] Data length  
 * @note Data bypasses buffers. Used for:  
 *       - Intermediate results  
 *       - Special characters in passthrough mode  
 */  
void appAtWriteImmediately(int atHandle, char *data, uint32_t dataLen);  

/**
 * @brief The application module sends response strings with result codes ('OK'/'ERROR'/'NO CARRIER') to the specified AT channel. 
 *        This API is strictly prohibited within functions registered via at_basic_req.
 * @param atHandle [IN] The AT channel identifier, obtained by calling get_current_ttyFd() in the AT command parsing function. See @ref AT_TTY_FD.    
 * @param data [IN] Response string  
 * @note Resets AT context after sending  
 */  
void appAtResp(int atHandle, char *data);  

/**
 * @brief Broadcasts URC (Unsolicited Result Code) to all AT channels. Typically used for BSP platform URCs and 3GPP URCs
 * @param data [IN] URC data to be transmitted
 * @param dataLen [IN] Length of URC data
 * @note For non-command-triggered proactive reports, e.g., 3GPP's "+CTZEU"/"+POWERON:"
 */
void sysAtURC(void *data, uint32_t dataLen);

/**
 * @brief [Xinyi Internal Use Only] Debug URC output at BSP level (may cause garbled characters or data loss)
 * @param data [IN] Debug URC message, e.g., "\r\n+DBGINFO:NV ERROR\r\n"
 * @warning Output enabled only after executing AT+NV=SET,CLOSEDEBUG,0. This interface is strictly for debug purposes and must not be used for functional AT commands.
 * @attention Business modules must use xy_printf instead of this interface.
 */
void send_debug_str_to_ext(char *buf);

/**
 * @brief Retrieves the channel handle of the AT command currently being processed by the AT controller
 * @return Handle ID (@ref AT_TTY_FD)
 * @warning Only callable within parsing functions registered via at_basic_req. Calling from business threads will trigger assertion!
 * @warning Strictly limited to AT commands processed by the at_proxy main thread - assertions will occur otherwise!
 */
int get_current_ttyFd();

/**
 * @brief Gets current baud rate of LPUART when used as AT channel (default: 115200)
 * @note Return value of 0 indicates either LPUART is not configured as AT channel or baud rate auto-negotiation is in progress
 * @warning This interface only applies to LPUART. Not valid for USB-enumerated AT ports or other UART AT channels.
 */
uint32_t atUartBaudRateGet(void);

/**
 * @brief  Parses each parameter value according to fmt format (similar to scanf). The parsed return value variables must be passed by address.
 * @param fmt  [IN] Format string for AT command parameters, where () indicates mandatory parameters and [] indicates optional parameters. Supported parameter types:
 *
 *       %d,%1d,%2d correspond to int, char, and short integer parameters (support both hex and decimal values). 
 *       Parentheses can be added to indicate parameter constraints:
 *       - %d(0-100) indicates an optional 4-byte integer parameter with valid range 0-100
 *       - %d(0|0x20|0x100) indicates valid values must be one of 0, 0x20, or 0x100
 *  
 *       %c is equivalent to %1d, parsing uint8_t character variables
 *
 *       %m corresponds to uint64_t integer parameters
 * 
 *       %f corresponds to double floating-point parameters
 *
 *       %l works like %d but validates string length for %p or %h parameters (only one %l allowed).Primarily paired with %h; if %h is absent, pairs with %p instead.
 * 
 *       %s corresponds to string parameters.May include a numeric value to specify string buffer size (e.g. %10s). If actual string length exceeds (specified number - 1), a parameter error will be reported.
 *
 *       %p corresponds to long string parameters. For long strings: passes pointer to string data. May be paired with %l for length checking
 *
 *       %h corresponds to hex string parameters (automatically converted to bitstream); typically used in combination with %l.
 *
 * @param buf  [IN] Pointer to AT command parameter string (e.g., "2,5,cmnet")
 * @param va_args ... [IN/OUT] Address space for each parsed parameter
 * @note 
 * @warning   This scanf-like interface combines parameter validation and parsing to simplify AT command development.
 */
int at_parse_param(char *fmt, char *buf, ...);


/**
 * @brief  Specialized parser for escaped string parameters (primarily for HTTP-like commands). Input strings must be quoted!!!
 * @param fmt  [IN] Same usage as at_parse_param
 * @param buf  [IN] Pointer to input string (e.g., "2,5,cmnet")
 * @param parse_num  [OUT] Actual number of parameters parsed (e.g., AT+HTTPHEADER=1,"" yields parse_num=2; AT+HTTPHEADER=1  parse_num=1)
 * @param va_args ... [IN/OUT] Address space for each parsed parameter
 * @return Parsing result (see @AT_ERRNO_E)
 * @note Escape sequence examples: 
 *       - "\101xinyin\x42" → "AxinyiB"
 *       - "\r\nxinyi\?" → "'\r''\n'xinyi?" (converts 2-byte \r to 1-byte ASCII)
 */
int at_parse_param_escape(char *fmt, char *buf, int *parse_num, ...);

/**
 * @brief Maintains same parameter usage as at_parse_param but adds:
 *        1. Strict parameter count validation
 *        2. Special character handling (,\",\r) in final string parameter
 * @note Key differences from at_parse_param:
 *       - at_parse_param: No strict count checking (partial parsing)
 *       - at_parse_param_all: Full parameter validation (rejects extra params)
 * @example "AT+CFUN=0,7\r\n" fails with XY_Err_Parameter (extra param '7')
 * @example "AT+MIPSEND=1,10,\"TEST,\rTE\"T\"" → last param="TEST,\rTE\"T"
 */
int at_parse_param_all(char *fmt, char *buf, ...);

/**
 * @brief  Case-insensitive string comparison, primarily used for string parameter recognition during AT command parsing (e.g., "IPV6" vs "ipv6")
 * @return 1 if strings are identical (case-insensitive)
 *         0 if strings differ
 */
bool at_strcasecmp(const char *s1, const char *s2);

#ifdef __cplusplus
}
#endif