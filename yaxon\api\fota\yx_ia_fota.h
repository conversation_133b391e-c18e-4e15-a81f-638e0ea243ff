/**********************************************************************/
/**
 * @file yx_ia_fota.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-13
 * @version V1.0
 * @brief fota模块接口适配
 **********************************************************************/
#ifndef YX_IA_FOTA_H
#define YX_IA_FOTA_H

#include "yx_type.h"
#include "module_id_def.h"
#define IA_FOTA_ERRNO            (0x80 + YX_FUNC_MODULE_ID_FOTA)

/**
 * @brief 错误码枚举
 * @ingroup ia_fota
 */
typedef enum {
    IA_FOTA_SUCCESS = RTN_OK,                                   /**< 成功 */
    IA_FOTA_INVALID_PARAM_ERR = (IA_FOTA_ERRNO << 24) | 0x01,   /**< 参数错误 */
    IA_FOTA_EXECUTE_FAILED    = (IA_FOTA_ERRNO << 24) | 0x02,   /**< 执行失败 */
    IA_FOTA_MEMORY_MALLOC_ERR = (IA_FOTA_ERRNO << 24) | 0x03,   /**< 内存分配失败 */
    IA_FOTA_PROTOCOL_ERR      = (IA_FOTA_ERRNO << 24) | 0x04,   /**< 协议类型不支持 */
    IA_FOTA_FIRMWARE_ERR      = (IA_FOTA_ERRNO << 24) | 0x05,   /**< 固件类型不支持 */
    IA_FOTA_DOWNLOAD_ERR      = (IA_FOTA_ERRNO << 24) | 0x06,   /**< 下载失败 */
    IA_FOTA_VERIFY_ERR        = (IA_FOTA_ERRNO << 24) | 0x07,   /**< 固件校验失败 */
} ia_fota_errno_e;

/**
 * @brief fota升级固件类型枚举定义
 * @ingroup ia_fota
 */
typedef enum {
    FOTA_FIREWARE_TYPE_MPU     = 0x01,    /**< MPU固件升级(差分升级) */
    FOTA_FIREWARE_TYPE_MPU_APP = 0x02,    /**< MPU应用升级(应用全量) */
    FOTA_FIREWARE_TYPE_MAX                /**< 枚举最大值 */
} yx_ia_fota_firmware_e;

/**
 * @brief fota升级协议类型枚举定义
 * @ingroup ia_fota
 */
typedef enum {
    FOTA_PROTOCOL_TYPE_FTP = 0,   /**< ftp协议 */
    FOTA_PROTOCOL_TYPE_HTTP,      /**< http协议 */
    FOTA_PROTOCOL_TYPE_MAX        /**< 枚举最大值 */
} yx_ia_fota_protocol_e;

/**
 * @brief fota升级接口,用于应用全量升级和差分升级
 * @ingroup ia_fota
 * @param[in] firmware 升级类型, @see yx_ia_fota_firmware_e
 * @param[in] protocal 升级协议, @see yx_ia_fota_protocol_e
 * @param[in] timeout 升级超时时间,单位(秒)
 * @param[in] url 升级包地址,例如"http://192.168.1.1/path/to/file"
 * @param[in] cb 升级结果回调函数
 * @return INT32S 
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败 
 * @note 该接口返回值只代表接口调用是否成功，并不代表升级过程是否成功，实际结果需要通过回调函数获取
 * @note 回调结果表示固件下载和写入是否成功，回调成功后，需要调用一次重启开始升级
 * @warning 该接口阻塞，不能在主线程中调用,reach平台目前仅支持通过http协议进行MPU固件fota升级
 */
INT32S yx_ia_fota_start(INT8U firmware, INT32U protocal, INT32U timeout, CHAR *url, VOID (*cb)(INT8U firmware, INT32S result, INT8U percent));

/**
 * @brief 获取fota空间总大小
 * @ingroup ia_fota
 * @return INT32S 
 * @retval fota空间总字节数
 * @note 返回值<0表示获取失败
 * @warning 首次调用前需调用yx_ia_erase_fota_data()初始化fota空间，否则获取到的总大小不准确
 */
INT32S yx_ia_get_fota_total_size(VOID);

/**
 * @brief 获取已用fota空间大小（下载的fota包大小）
 * @ingroup ia_fota
 * @return INT32S 
 * @retval 已用fota空间字节数
 * @note 返回值<0表示获取失败
 * @warning 首次调用前需调用yx_ia_erase_fota_data()初始化fota空间，否则获取到的总大小不准确
 */
INT32S yx_ia_get_fota_used_size(VOID);

/**
 * @brief 擦除fota分区,同时初始化
 * @ingroup ia_fota
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval 其他值 操作失败
 */
INT32S yx_ia_erase_fota_data(VOID);

/**
 * @brief 写入fota分区
 * @ingroup ia_fota
 * @param[in] buf 数据缓冲区
 * @param[in] size 数据长度
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval 其他值 操作失败
 * @note 接口内部自动偏移
 * @warning 首次调用前需调用yx_ia_erase_fota_data()初始化，否则写入可能失败
 */
INT32S yx_ia_write_fota_data(const INT8U *buf, INT32U size);

/**
 * @brief 读取fota数据
 * @param[out] buf 读取数据缓冲区
 * @param[in] offset 读取起始偏移
 * @param[in] size 读取长度
 * @return INT32S
 * @retval 实际读取字节数，失败返回负值
 * @note 由调用者管理偏移offset和读取长度size，接口内部不做越界判断
 * @warning 首次调用前需调用yx_ia_erase_fota_data()初始化，否则读取可能失败
 */
INT32S yx_ia_read_fota_data(INT8U *buf, INT32U offset, INT32U size);

/**
 * @brief 校验fota数据
 * @return INT32S
 * @retval RTN_OK(0) 校验成功
 * @retval 其他值 校验失败
 * @note 该接口仅支持校验MPU差分升级包，不支持对其他类型固件包进行校验操作
 */
INT32S yx_ia_verify_fota_data(VOID);

/**
 * @brief 设置fota升级标识
 * @ingroup ia_fota
 * @return INT32S 
 * @retval RTN_OK(0) 成功
 * @retval RTN_ERR(-1) 失败
 * @note 该接口用于设置mpu fota升级标识，首先确保mpu固件下载和校验成功，准备升级前调用一次即可，会自动重启。
 */
INT32S yx_ia_set_mpu_fota_upd_flag(VOID);

#endif /* YX_IA_FOTA_H */