#include "at_response.h"
#include "app_basic_config.h"
#include "at_cmd_basic.h"
#include "at_com.h"
#include "at_error.h"
#include "at_tcpip_api.h"
#include "lwip/netdb.h"
#include "net_adapt.h"
#include "oss_nv.h"
#include "xy_at_api.h"
#include "xy_defwan_api.h"
#include "pdp_dial.h"
#include "xy_system.h"
#include "xy_tcpip_api.h"
#include "xy_wan_api.h"
#include "pkt_process.h"


/* AT+XYACT=<cid> 查询某CID对应的TCPIP网路是否可用，扩展AT命令*/
int at_XYACT_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_REQ)
    {
        uint8_t cid = 0;
        if (at_parse_param("%1d(),", at_buf, &cid) != XY_OK)
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }
        if (!is_Ps_Cid_Vaild(cid))
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

        *prsp_cmd = xy_malloc(32);
        snprintf(*prsp_cmd, 32, "%d", xy_Wan_is_ok(cid) ? 1 : 0);
    }
    else if (g_req_type == AT_CMD_QUERY)
    {
        *prsp_cmd = xy_malloc(32);
        snprintf(*prsp_cmd, 32, "%d", xy_DefWan_is_ok()? 1 : 0);
    }
    return AT_END;
}

#if VER_QUEC

/*AT+QNETDEVCTL=<type>,<cid>[,<URC_en>] 连接USB网卡进行网卡拨号。用户可通过"CGDCONT"命令进行PDP激活的APN等配置*/
int at_QNETDEVCTL_req(char *at_buf, char **prsp_cmd)
{
    if (g_req_type == AT_CMD_REQ)
    {
        int cid = -1, type = -1, urc_en = 0;
        int old_net_cid = g_app_basic_cfg.usb_cid; 

		if (at_parse_param("%d(0|1|3),%d(),%d[0-1]", at_buf, &type, &cid, &urc_en) != XY_OK)
        {
			*prsp_cmd = AT_PLAT_ERR(XY_Err_Parameter);
			return AT_END;
		}

        if (!is_Ps_Cid_Vaild(cid))
        {
 			*prsp_cmd = AT_PLAT_ERR(XY_Err_Parameter);
			return AT_END;           
        }

        if (type == 3)  /* 自动激活CID，并组网，重启后生效*/
        {
            // 仅当type=3时，当前配置重启仍生效，保存参数配置，重启或对应cid激活成功时自动连接网卡
            g_softap_fac_nv->gw_act_mode = 1;
            g_app_basic_cfg.usb_cid = cid;            
            g_app_basic_cfg.usbnet_sta_urc = urc_en;
            SAVE_FAC_PARAM(gw_act_mode);
            SAVE_APP_BASIC_CFG_PARAM(usb_cid);
            SAVE_APP_BASIC_CFG_PARAM(usbnet_sta_urc);
            if (!xy_Wan_is_ok(cid))
            {
                /*用户可自行通过"CGDCONT"命令进行PDP激活的APN等配置*/
				start_data_call(0,cid);
                return XY_OK;
            }             
        }
        else if (type == 0) /*去活CID*/
        {
            if (old_net_cid == INVAILD_CID_FLAG || cid != old_net_cid)
            {
                *prsp_cmd = AT_PLAT_ERR(XY_Err_Parameter);
                return AT_END;  
            }
            // type=0时，立即生效，清空已保存参数配置
            g_softap_fac_nv->gw_act_mode = 0;
            g_app_basic_cfg.usb_cid = INVAILD_CID_FLAG;   
            g_app_basic_cfg.usbnet_sta_urc = 0;	
            SAVE_FAC_PARAM(gw_act_mode);
            SAVE_APP_BASIC_CFG_PARAM(usb_cid);
            SAVE_APP_BASIC_CFG_PARAM(usbnet_sta_urc);
        }
        else  /*type=1,仅立即连接网卡一次。需用户确保此时cid已激活，此处执行子网的激活流程*/
        {
            // 当type=1时，仅立即连接网卡一次，配置不保存，清空已保存参数配置
            g_softap_fac_nv->gw_act_mode = 0;
            g_app_basic_cfg.usb_cid = INVAILD_CID_FLAG;                
            g_app_basic_cfg.usbnet_sta_urc = 0;	
            SAVE_FAC_PARAM(gw_act_mode);
            SAVE_APP_BASIC_CFG_PARAM(usb_cid);
            SAVE_APP_BASIC_CFG_PARAM(usbnet_sta_urc); 
            if (!xy_Wan_is_ok(cid))
            {
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_PdpOpen);
                return AT_END;
            }
            g_app_basic_cfg.usb_cid = cid;
            g_app_basic_cfg.usbnet_sta_urc = urc_en;                            	
        }	

        usb_netdev_ctl_msg_t msg = {.contextID = cid, .type = type};
        if (at_usb_netdev_ctl_async(&msg) != XY_OK)
        {
            *prsp_cmd = AT_PLAT_ERR(XY_Err_NotAllowed);
            return AT_END;
        }  
	}
#if (!AT_TEST_OFF)
    else if (g_req_type == AT_CMD_TEST)
    {
        *prsp_cmd = xy_malloc(64);
        snprintf(*prsp_cmd, 64, "(0,1,3),(%d-%d),(0,1)", CID_MIN_VAL, CID_MAX_VAL);
    }
#endif
	else if (g_req_type == AT_CMD_QUERY)
    {
        *prsp_cmd = xy_malloc(64);
        if (g_app_basic_cfg.usb_cid == INVAILD_CID_FLAG)
        {
            snprintf(*prsp_cmd, 64, "0,0,0,0");
        }
        else
        {
            snprintf(*prsp_cmd, 64, "%d,%d,%d,%d", ((g_softap_fac_nv->gw_act_mode == 1) ? 3 : 1), g_app_basic_cfg.usb_cid, g_app_basic_cfg.usbnet_sta_urc, usbNetDevWanIsOk());
        }
    }
    else
        *prsp_cmd = AT_PLAT_ERR(XY_Err_Parameter);

    return AT_END;
}


void atDefAsyncDnsFoundCallback(uint8_t source, uint8_t atHandle, int8_t errCode, char* name, uint32_t ttl, uint8_t answer_num, const ip_addr_t *ipaddr)
{
	int i;
	char ipStr[40] = {0};
	char* reportUrc = xy_malloc(240);
	uint16_t len = 0;
	if (errCode != 0)
	{
        set_at_tcpip_err(XY_Err_DnsFail);
		sprintf(reportUrc, "\r\n+QIURC: \"dnsgip\",%d\r\n", XY_Err_DnsFail);
	}
	else
	{
		len += sprintf(reportUrc, "\r\n+QIURC: \"dnsgip\",0,%d,%d\r\n", answer_num, ttl);
		for (i = 0; i < answer_num; i++)
		{
			memset(ipStr, 0, 40);
			ipaddr_ntoa_r(&ipaddr[i], ipStr, 40);
			len += sprintf(reportUrc + len, "\r\n+QIURC: \"dnsgip\",\"%s\"\r\n", ipStr);
		}
	}

    appAtURC(atHandle, reportUrc);
	xy_free(reportUrc);
}

/*********************** EC600N DNS/NTP 命令 **********************************/
/* AT+QIDNSCFG=<contextID>[,<pridnsaddr>[,<secdnsaddr>]] */
int at_QIDNSCFG_req(char *at_buf, char **prsp_cmd)
{
    int ret = XY_OK;
    if (g_req_type == AT_CMD_REQ)
	{
		dns_cfg_param_t param = {0};
		// DNS地址字符串的最大长度为XY_IPADDR_STRLEN_MAX
        if (at_parse_param("%1d,%47p[],%47p[]", at_buf, &param.contextID, &param.pridns, &param.secdns) != XY_OK)
        {
            ret = XY_Err_Parameter;
            *prsp_cmd = AT_PLAT_ERR(ret);
            goto END;
        }

        if (!is_Ps_Cid_Vaild(param.contextID))
		{
			ret = XY_Err_Parameter;
			*prsp_cmd = AT_PLAT_ERR(ret);
			goto END;
		}

		if (!xy_Wan_is_ok(param.contextID))
		{
            ret = XY_Err_PdpOpen;
            *prsp_cmd = AT_PLAT_ERR(ret);
            goto END;
		}        
        // 查询cid对应PDP的DNS地址
        if (param.pridns == NULL && param.secdns == NULL)
        {
            *prsp_cmd = xy_malloc(128);
            char *pri_dns = xy_malloc(XY_IPADDR_STRLEN_MAX);
            char *sec_dns = xy_malloc(XY_IPADDR_STRLEN_MAX);

            xy_dns_get(param.contextID, 0, pri_dns);
            xy_dns_get(param.contextID, 1, sec_dns);
            snprintf(*prsp_cmd, 128, "%d,\"%s\",\"%s\"", param.contextID, pri_dns, sec_dns);
            xy_free(pri_dns);
            xy_free(sec_dns);
        }
        else
        {
            ret = at_dns_config(&param);
            *prsp_cmd = AT_PLAT_ERR(ret);
        }
	}
#if (!AT_TEST_OFF)
	else if (g_req_type == AT_CMD_TEST)
	{
        *prsp_cmd = xy_malloc(60);
        snprintf(*prsp_cmd, 60, "(%d-%d),<pridnsaddr>,<secdnsaddr>",CID_MIN_VAL, CID_MAX_VAL);
	}
#endif
	else
	{
        ret = XY_Err_NotSupport;
        *prsp_cmd = AT_PLAT_ERR(ret);
    }
END:
    set_at_tcpip_err(ret);
    return AT_END;
}

/* AT+QDNSGIP=<contextID>,<hostname> */
int at_QIDNSGIP_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_REQ)
	{
		uint8_t cid;
        char* host = NULL;
        uint8_t atHandle = (uint8_t)get_current_ttyFd();
        struct addrinfo hint = {0};

        if (at_parse_param("%1d(),%p()", at_buf, &cid, &host) != XY_OK)
        {
            *prsp_cmd = AT_TCPIP_ERR_BUILD(XY_Err_Parameter);
            return AT_END;
        }

        if (!is_Ps_Cid_Vaild(cid))
		{
			*prsp_cmd = AT_TCPIP_ERR_BUILD(XY_Err_Parameter);
			return AT_END;
		}

        if (!xy_Wan_is_ok(cid))
        {
			*prsp_cmd = AT_TCPIP_ERR_BUILD(XY_Err_Netif_NotReady);
			return AT_END;
        }
  		
		hint.ai_socktype = SOCK_DGRAM;
		hint.ai_family = AF_UNSPEC;
        if (getalladdrinfo_async(cid, host, &hint, SOURCE_DNS, atHandle, atDefAsyncDnsFoundCallback) != 0)
        {
            *prsp_cmd = AT_TCPIP_ERR_BUILD(XY_Err_DnsFail);
            return AT_END;
        }
		set_at_tcpip_err(XY_OK);
    }
#if (!AT_TEST_OFF)
	else if (g_req_type == AT_CMD_TEST)
	{
        *prsp_cmd = xy_malloc(50);
        snprintf(*prsp_cmd, 50, "(%d-%d),<hostname>", CID_MIN_VAL, CID_MAX_VAL);
		set_at_tcpip_err(XY_OK);
    }
#endif
	else
	{
        *prsp_cmd = AT_TCPIP_ERR_BUILD(XY_Err_Parameter);
    }
	return AT_END;
}



// AT+QIGETERROR 查询上一个AT命令错误代码
int at_QIGETERROR_req(char *at_buf, char **prsp_cmd)
{
    (void) at_buf;
 	if (g_req_type == AT_CMD_ACTIVE)
	{
        *prsp_cmd = xy_malloc(64);
        int err = get_at_tcpip_err();
        snprintf(*prsp_cmd, 64, "%d,%s", err, get_plat_err_info(err));
    }
    else if (g_req_type != AT_CMD_TEST)
    {
        *prsp_cmd = AT_TCPIP_ERR_BUILD(XY_Err_NotAllowed);
    }
    return AT_END;
}
#endif

#if VER_CM
/**
 * @brief  设置域名解析服务器
 * @arg 设置类AT命令：
 * AT+MDNSCFG="ip"[,<address1>[,<address2>]],设置/查询ipv4DNS服务器地址；
 * AT+MDNSCFG="ipv6"[,<address1>[,<address2>]],设置/查询ipv6DNS服务器地址；
 * AT+MDNSCFG="priority"[,<priority>],设置/查询解析协议优先级，默认值 1。0:支持解析 IPv4、IPv6 地址，IPv4 优先; 1:支持解析 IPv4、IPv6 地址，IPv6 优先;
 * AT+MDNSCFG="timeout"[,<time>][,<retries>],time:设置/查询DNS一次请求等待服务器响应的超时时间，范围1~60s，默认10s；retries:DNS最大请求次数，解析失败或超时后将进行重试请求，范围：1~9,默认值3;
 * AT+MDNSCFG="retires"[,<retries>],DNS最大请求次数，解析失败或超时后将进行重试请求，范围：1~9。默认值3;
 * @note IPv4和IPv6各支持两个DNS服务器，根据输入地址识别类型，设置后立即生效。
 */
int at_MDNSCFG_req(char *at_buf, char **prsp_cmd)
{
	int ret = XY_OK;
	if (g_req_type == AT_CMD_REQ)
	{
		char param1[10] = {0};
		int val = -1;

		if (at_parse_param("%10s,", at_buf, param1) != XY_OK)
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			return AT_END;
		}

		if (at_strcasecmp(param1, "ip") || at_strcasecmp(param1, "ipv6"))
		{
			dns_cfg_param_t param = {0};
			param.contextID = xy_get_DefWan_Cid();
			// DNS地址字符串的最大长度为XY_IPADDR_STRLEN_MAX
			if (at_parse_param(",%47p[],%47p[]", at_buf, &param.pridns, &param.secdns) != XY_OK)
			{
				ret = XY_Err_Parameter;
				*prsp_cmd = AT_PLAT_ERR(ret);
				goto END;
			}
			if (!xy_DefWan_is_ok())
			{
				ret = XY_Err_Netif_NotReady;
				*prsp_cmd = AT_PLAT_ERR(ret);
				goto END;
			}
			// 查询cid对应PDP的DNS地址
			if (param.pridns == NULL && param.secdns == NULL)
			{
				*prsp_cmd = xy_malloc(128);
				char *pri_dns = xy_malloc(XY_IPADDR_STRLEN_MAX);
				char *sec_dns = xy_malloc(XY_IPADDR_STRLEN_MAX);

				if (at_strcasecmp(param1, "ip"))
				{
					xy_dns_get(param.contextID, 0, pri_dns);
					xy_dns_get(param.contextID, 1, sec_dns);
				}
				else
				{
					xy_dns_get(param.contextID, 2, pri_dns);
					xy_dns_get(param.contextID, 3, sec_dns);
				}
				snprintf(*prsp_cmd, 128, "\"%s\",\"%s\",\"%s\"", param1, pri_dns, sec_dns);
				xy_free(pri_dns);
				xy_free(sec_dns);
			}
			else
			{
				if (at_strcasecmp(param1, "ip"))
				{
					ret = set_ipv4_dns_server(param.contextID, param.pridns, param.secdns);
				}
				else
				{
					ret = set_ipv6_dns_server(param.contextID, param.pridns, param.secdns);
				}
				*prsp_cmd = AT_PLAT_ERR(ret);
			}
		}
		else if (at_strcasecmp(param1, "priority"))
		{
			int priority = -1;
			if (at_parse_param(",%d[0-1]", at_buf, &priority) != XY_OK)
			{
				*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
				return AT_END;
			}
			if (priority == -1)
			{
				*prsp_cmd = xy_malloc(60);
				sprintf(*prsp_cmd, "\"priority\",%d", (g_app_basic_cfg.dns_priority == NETCONN_DNS_IPV6_IPV4) ? 1 : 0);
			}
			else
			{
				g_app_basic_cfg.dns_priority = (priority == 1) ? NETCONN_DNS_IPV6_IPV4 : NETCONN_DNS_IPV4_IPV6;
				SAVE_APP_BASIC_CFG_PARAM(dns_priority);
			}
		}
		else if (at_strcasecmp(param1, "timeout"))
		{
			int interval = -1;
			int retries = -1;
			if (at_parse_param(",%d[1-60],%d[1-9]", at_buf, &interval, &retries) != XY_OK)
			{
				*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
				return AT_END;
			}
			if (interval == -1 && retries == -1)
			{
				*prsp_cmd = xy_malloc(60);
				sprintf(*prsp_cmd, "\"timeout\",%d,%d",g_app_basic_cfg.dns_interval,g_app_basic_cfg.dns_retries);
			}
			else
			{
				if (interval != -1)
				{
					g_app_basic_cfg.dns_interval = interval;
					SAVE_APP_BASIC_CFG_PARAM(dns_interval);
				}
				if (retries != -1)
				{
					g_app_basic_cfg.dns_retries = retries;
					SAVE_APP_BASIC_CFG_PARAM(dns_retries);
				}
			}
		}
		else if (at_strcasecmp(param1, "retries"))
		{
			int retries = -1;
			if (at_parse_param(",%d[1-9]", at_buf, &retries) != XY_OK)
			{
				*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
				return AT_END;
			}
			if (retries == -1)
			{
				*prsp_cmd = xy_malloc(60);
				sprintf(*prsp_cmd, "\"retries\",%d",g_app_basic_cfg.dns_retries);
			}
			else
			{
				g_app_basic_cfg.dns_retries = retries;
				SAVE_APP_BASIC_CFG_PARAM(dns_retries);
			}
		}
		else
		{
			ret = XY_Err_Parameter;
			*prsp_cmd = AT_PLAT_ERR(XY_Err_Parameter);
			goto END;
		}
	}
#if (!AT_TEST_OFF)
	else if (g_req_type == AT_CMD_TEST)
	{
#define MDNSCFG_URC_HEAD "\r\n+MDNSCFG: "
		int strlen;
		*prsp_cmd = xy_malloc2(128);
		if (*prsp_cmd == NULL)
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_NoMemory);
			return AT_END;
		}
		strlen = sprintf(*prsp_cmd, "%s\"ip\",,", MDNSCFG_URC_HEAD);
		strlen += sprintf(*prsp_cmd + strlen, "%s\"ipv6\",,", MDNSCFG_URC_HEAD);
		strlen += sprintf(*prsp_cmd + strlen, "%s\"priority\",(0,1)", MDNSCFG_URC_HEAD);
		strlen += sprintf(*prsp_cmd + strlen, "%s\"timeout\",(1-60),(1-9)", MDNSCFG_URC_HEAD);
	}
#endif
	else
	{
		ret = XY_Err_Parameter;
		*prsp_cmd = AT_PLAT_ERR(ret);
	}
END:
	return AT_END;
}
#endif

