#pragma once

/*******************************************************************************
 *                           Include header files                              *
 ******************************************************************************/
#include <stdbool.h>
#include <stdint.h>

/*******************************************************************************
 *                             Macro definitions                               *
 ******************************************************************************/
#define DIFF_VALUE ('a'-'A')

/*******************************************************************************
 *                             Type definitions                                *
 ******************************************************************************/
typedef struct
{
	char ch;          //转义字符 如\a 中的a
	char esc_ch;      //转义字符对应的ASCII码
} esc_item_t;

/**
 * @brief at请求类型, @see @ref AT_REQ_TYPE_E
 */
extern char g_req_type;

/*******************************************************************************
 *                       Global function declarations                          *
 ******************************************************************************/
bool is_digit_str(char *str);

bool is_hex_str(char *str, uint8_t check_head);

bool is_at_char(char c);
bool is_at_str(char *str,int len);

/**
 * @brief 用于参数解析时，找到第n个特殊字符c后的下一个字符地址
 * @param s  [IN] AT命令字符串
 * @param c  [IN] 特殊匹配字符，如',' 或 '"'
 * @param n  [IN] 匹配第几个特殊字符吧，取值必须大于等于1
 * @return   匹配成功后的下一个字符地址，NULL表示未匹配成功
 * @warning  通常用于多个参数的解析处理
 */
char *at_strnchr(char *s, int c, int n);


/**
 * @brief 用于AT字符串头部前缀的模糊匹配，实现时考虑了source头部可能存在的"\r\nAT"等特殊字符干扰。
 * @param source  [IN] AT命令字符串，如“\r\rAT+NRB\r\n”
 * @param substr  [IN] 前缀特征字符串，如“NRB”
 * @return  source匹配成功后的下一个字符地址，NULL表示未匹配成功
 * @warning  前缀有效字符串必须严格匹配，例如source="AT+QCCLK",substr="CCLK",则匹配失败。substr="QCCLK"才能匹配成功
 */
char *at_prefix_strstr(char *source, char * substr);


/**
 * @brief  不区分大小写的严格字符串比较，常用于AT命令参数解析时，字符串参数的识别。例如“IPV6” “ipv6”比较结果是相等的。
 */
bool at_strcasecmp(const char *s1, const char *s2);

/**
 * @brief  不区分大小写的父子字符串比较，严格从第一个字符匹配。通常用于特殊字符串头的识别，如参数字符串中头部关键字识别等
 * @param father [IN] 父字符串，如 “UFS:LITTLEFS”
 * @param son    [IN] 子字符串，如 “Ufs:”
 */
bool at_strncasecmp(const char *father, const char *son);



/* 检测是否为OK应答结果码;返回 1 表示匹配成功 */
bool Is_AT_Rsp_OK(char *str);

/*  识别是否为“ERROR”应答报文，并解析出对应的错误码；返回0表示非ERROR结果码 */
int Get_AT_errno(char *str);

/**
 * @brief  识别字符串是否含应答结果，即是否含“OK”或“ERROR”
 * @param  str 待处理的at数据
 * @note
 */
bool Is_Result_AT_str(char *str);

/* 构建“OK”应答字符串，使用者需释放内存空间 */
char *at_ok_build();

/**
 * @brief 解析字符串中的转义字符，保存于原位置，字符串长度可能缩小
 * @param input [IN]/[OUT]   需要转换的字符
 * @note
 */
void parase_esc_string(char *input);


/**
 * brief 解析字符串中的转义字符，保存于原位置，字符串长度可能缩小
 *
 * param [IN]/[OUT] str               需要转换的字符
 * param [OUT] data_len         转换后的字符串长度
 * return  转换后字符串首地址。NULL表示转义失败，通常为字符内容异常
 * @note   推荐使用parase_esc_string
 */
char *parase_esc_string2(char* str, int *data_len);


/**
 * @brief 获取字符串中下一个双引号位置，排除\"转义字符干扰
 * @param data [IN] 输入字符串
 * @return 返回下一个双引号位置，若未找到，返回NULL
 * @note
 */
char *find_next_double_quato(char *data);



