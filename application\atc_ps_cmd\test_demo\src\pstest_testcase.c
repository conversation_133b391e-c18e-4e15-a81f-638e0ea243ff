#if PS_TEST_MODE
#include "pstest.h"
#include "pstest_api.h"
#include "atc_ps.h"

/************************
api: 候选频点；时间
**************************/
char ps_test_check_rrc_state()
{
    unsigned char i;

    for(i = 0;i < 6; i++)
    {
        if(Test_False == ps_test_send_at_wait_urc("AT+CSCON?\r\n","+CSCON","+CSCON: 0,0",5))
        {
            ps_test_wait_time(10);
            continue;
        }
        return Test_True;
    }
    return Test_False;
}
/************************
api: 候选频点；时间
**************************/
void ps_test_info_ask()
{
    unsigned char i;
    unsigned char* pQNTP;
    ps_test_send_at_wait_urc("AT+NLOCKF?\r\n","\r\n+NLOCKF",NULL,20);
    ps_test_send_at_wait_urc("AT+QNTP=1,ntp7.aliyun.com\r\n","+QNTP","+QNTP: 0",45);
    ps_test_get_net_time();
}

/************************
api：关机
**************************/
void ps_test_cfun0_api()
{
    unsigned char i;
    for(i = 0; i < 6; i++)    //if 6times cfun0,cannot receive OK ,will reboot。
    {
        if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=0\r\n","\r\nOK",NULL,60))
        {
            continue;
        }
        break;
    }
    ps_test_wait_time(10);
}
/************************
api：开机后等待idle
**************************/
UCHAR ps_test_cfun1_to_idle_api()
{
    unsigned char i ,j;
    unsigned long ulCfunStarttime = 0;
    unsigned long ulCfunEndtime = 0;
    unsigned long ulspeedtime = 0;
    for(i = 0; i < 3; i++)
    {
        ps_test_cfun0_api();
        ulCfunStarttime = osKernelGetTickCount();
        if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=1\r\n","\r\n+CGEV: ME PDN ACT",NULL,300))
        {
            continue;
        }
        ulCfunEndtime = osKernelGetTickCount();
        ulspeedtime = ulCfunEndtime - ulCfunStarttime;
        xy_printf( 0, ATC_AP_T, WARN_LOG,"[ps_test_cfun1_to_idle_api]attach%d spend time = %ld", i, ulCfunEndtime - ulCfunStarttime);
        ps_test_info_ask();

        if(Test_False == ps_test_check_rrc_state())
        {
            continue;
        }
        return ulspeedtime;
    }
    return Test_False;
}
/****************************
TEST: cops扫网
*****************************/
UCHAR ps_test_cops_plmn_search()
{
    unsigned char ucCaseResult = Test_True;
    unsigned char i = 0;
    unsigned char ucFailNum = 0;
    ril_neighbor_cell_info_t* cell_info = NULL;
    cell_info  = (ril_neighbor_cell_info_t*)AtcAp_Malloc(sizeof(ril_neighbor_cell_info_t));
     
    for(i = 1; i <= 3 ; i++)
    {
        ps_test_cfun1_to_idle_api();
        ps_test_wait_time(60);

        if(Test_False == ps_test_send_at_wait_urc("AT+COPS=?\r\n","\r\n+COPS",NULL,600))
        {
            ucFailNum++;
            continue;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=0\r\n","\r\n+CGEV: ME DETACH",NULL,60))
        {
            ucFailNum++;
            continue;
        }
        ps_test_wait_time(10);
    }
    AtcAp_Free(cell_info);
    return ucCaseResult;
}

/****************************
TEST: 强制扫频
*****************************/
UCHAR ps_test_freq_scan()
{
    unsigned char ucCaseResult = Test_True;
    unsigned char i = 0;
    unsigned char ucFailNum = 0;
    unsigned char ucExceedThresholdNum = 0;
    unsigned long ulCfunStarttime = 0;
    unsigned long ulCfunEndtime = 0;
    unsigned long ulSpendTimeall = 0;
    
    if(Test_False == ps_test_send_at_wait_urc("AT+NSET=BANDSCAN,1\r\n","\r\nOK",NULL,60))
    {
        return Test_False;
    }
    for(i = 1; i <= 10 ; i++)
    {
        ps_test_cfun0_api();
        ulCfunStarttime = osKernelGetTickCount();
        if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=1\r\n","\r\n+CGEV: ME PDN ACT",NULL,300))
        {
            ucFailNum++;
            continue;
        }
        ulCfunEndtime = osKernelGetTickCount();
        ulSpendTimeall += ulCfunEndtime - ulCfunStarttime;
        if((ulCfunEndtime - ulCfunStarttime) > 180000)
        {
            ucExceedThresholdNum++;
            xy_printf( 0, ATC_AP_T, WARN_LOG,"[ps_test_freq_scan]attach%d spend time = %ld, time over 1min = %d", i, ulCfunEndtime - ulCfunStarttime, ucExceedThresholdNum);
        }
        else
        {
            xy_printf( 0, ATC_AP_T, WARN_LOG,"[ps_test_freq_scan]attach%d spend time = %ld", i, ulCfunEndtime - ulCfunStarttime);
        }
        ps_test_info_ask();
        if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=0\r\n","\r\n+CGEV: ME DETACH",NULL,60))
        {
            ucFailNum++;
            continue;
        }
        ps_test_wait_time(10);
    }
    ps_test_send_at_wait_urc("AT+NSET=BANDSCAN,0\r\n","\r\nOK",NULL,60);
    xy_printf( 0, ATC_AP_T, WARN_LOG,"[ps_test_freq_scan]runtimes = %d ,pass time = %d,scan attach speed average time = %ld", 10, 10 - ucFailNum, ulSpendTimeall / (10 - ucFailNum));
    if(ucFailNum >= 5)
    {
        ucCaseResult = Test_False;
    }
    return ucCaseResult;
}

/****************************
TEST: 空闲态开关机
*****************************/
UCHAR ps_test_idel_cfun01()
{
    unsigned char ucCaseResult = Test_True;
    unsigned char i = 0;
    unsigned char ucFailNum = 0;
    unsigned char ucExceedThresholdNum = 0;
    unsigned long ulCfunStarttime = 0;
    unsigned long ulCfunEndtime = 0;
    unsigned long ulSpendTimeall = 0;
    
    for(i = 1; i <= 10 ; i++)
    {
        ps_test_cfun0_api();
        ulCfunStarttime = osKernelGetTickCount();
        if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=1\r\n","\r\n+CGEV: ME PDN ACT",NULL,180))
        {
            ucFailNum++;
            continue;
        }
        ulCfunEndtime = osKernelGetTickCount();
        ulSpendTimeall += ulCfunEndtime - ulCfunStarttime;
        if((ulCfunEndtime - ulCfunStarttime) > 60000)
        {
            ucExceedThresholdNum++;
            xy_printf( 0, ATC_AP_T, WARN_LOG,"[ps_test_idel_cfun01]attach%d spend time = %ld, time over 1min = %d", i, ulCfunEndtime - ulCfunStarttime, ucExceedThresholdNum);
        }
        else
        {
            xy_printf( 0, ATC_AP_T, WARN_LOG,"[ps_test_idel_cfun01]attach%d spend time = %ld", i, ulCfunEndtime - ulCfunStarttime);
        }
        ps_test_info_ask();
        if(Test_False == ps_test_check_rrc_state())
        {
            ucFailNum++;
            continue;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=0\r\n","\r\n+CGEV: ME DETACH",NULL,60))
        {
            ucFailNum++;
            continue;
        }
        ps_test_wait_time(10);
    }
    xy_printf( 0, ATC_AP_T, WARN_LOG,"[ps_test_idel_cfun01]runtimes = %d ,pass time = %d,attach speed average time = %ld", 10, 10 - ucFailNum, ulSpendTimeall / (10 - ucFailNum));
    if(ucFailNum >= 5)
    {
        ucCaseResult = Test_False;
    }
    return ucCaseResult;
}



/****************************
ps_default_test004
TEST: 上行灌包
*****************************/
UCHAR ps_test_up_perf()
{
    unsigned char ucCaseResult = Test_True;
    unsigned char i = 0;
    unsigned char ucFailNum = 0;
    unsigned long ulCfunStarttime = 0;
    unsigned long ulCfunEndtime = 0;
    unsigned long ulSpendTimeall = 0;
    
    for(i = 1; i <= 1 ; i++)
    {
        ps_test_cfun0_api();
        if(Test_False == ps_test_cfun1_to_idle_api())
        {
            ucFailNum++;
            continue;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+NL2THP=1\r\n","\r\nOK",NULL,10))
        {
            ucFailNum++;
            continue;
        }
        ps_test_wait_time(10);
        // if(Test_False == ps_test_send_at_wait_urc("AT+XYPERF=1,139.224.112.6,10003,udp,1024,2k,120,1\r\n","\r\nOK",NULL,20))
        // {
        //     ucFailNum++;
        //     continue;
        // }

        // ps_test_wait_time(10);

        // if(Test_False == ps_test_wait_urc("\r\n+XYPERF:finished",120))
        // {
        //     ucFailNum++;
        //     continue;
        // }
        if(Test_False == ps_test_send_at_wait_urc("AT+TEST=SDLIMIT,800\r\n","\r\nOK",NULL,15))
        {
            ucFailNum++;
            continue;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+NSET=\"ULGB\",300,10000\r\n","\r\nOK",NULL,15))
        {
            ucFailNum++;
            continue;
        }
        ps_test_wait_time(300);
        if(Test_False == ps_test_send_at_wait_urc("AT+NL2THP=0\r\n","\r\nOK",NULL,10))
        {
            ucFailNum++;
            continue;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+TEST=SDLIMIT,1000000\r\n","\r\nOK",NULL,15))
        {
            ucFailNum++;
            continue;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=0\r\n","\r\n+CGEV: ME DETACH",NULL,60))
        {
            ucFailNum++;
            continue;
        }
        ps_test_wait_time(10);
    }
    if(ucFailNum >= 0)
    {
        ucCaseResult = Test_False;
    }
    return ucCaseResult;
}

/****************************
ps_default_test005
TEST: 下行灌包
*****************************/
UCHAR ps_test_down_perf()
{
    unsigned char ucCaseResult = Test_True;
    unsigned char i = 0;
    unsigned char ucFailNum = 0;
    unsigned short usSocketPort = g_softap_fac_nv->usPSTestSocketPort + 1;
    unsigned char pcmd[100] = {0};
    for(i = 1; i <= 1 ; i++)
    {
        // if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=0\r\n","\r\nOK",NULL,60))
        // {
        //     // ps_test_send_at_wait_urc("AT+NV=SET,RATETEST,0","\r\nOK",NULL,10);
        //     return Test_False;
        // }
        ps_test_cfun0_api();
        if(Test_False == ps_test_cfun1_to_idle_api())
        {
            ucFailNum++;
            continue;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+NL2THP=1\r\n","\r\nOK",NULL,10))
        {
            ucFailNum++;
            continue;
        }
        ps_test_wait_time(10);
        // sprintf(pcmd,"AT+SOCKETTEST=\"SOCK\",17,139.224.131.190,%d,0\r\n", usSocketPort);
        // if(Test_False == ps_test_send_at_wait_urc(pcmd,"\r\nSOCKET TEST SOCK fd =",30))
        // {
        //     ucFailNum++;
        //     continue;
        // }
        // if(Test_False == ps_test_send_at_wait_urc("AT+SOCKETTEST=\"SEND\",1,0011223344556677889900112233445566778899001122334455667788990011223344556677889900112233445566778899\r\n","\r\nSOCKET send data ret = 100",60))
        // {
        //     ucFailNum++;
        //     continue;
        // }
        // ps_test_wait_time(600);
        // if(Test_False == ps_test_send_at_wait_urc("AT+SOCKETTEST=\"CLOSE\",1\r\n","\r\nOK",20))
        // {
        //     ucFailNum++;
        //     continue;
        // }
        
        sprintf(pcmd,"AT+QIOPEN=1,1,\"UDP\",\"139.224.131.190\",%d,0,0\r\n", usSocketPort);
        if(Test_False == ps_test_send_at_wait_urc(pcmd,"\r\n+QIOPEN:","\r\n+QIOPEN: 1,0",60))
        {
            ucFailNum++;
            continue;
        }
        ps_test_wait_time(10);
        if(Test_False == ps_test_send_at_wait_urc("AT+QISENDEX=1,0011223344556677889900112233445566778899001122334455667788990011223344556677889900112233445566778899\r\n","\r\nSEND OK",NULL,60))
        {
            ucFailNum++;
            continue;
        }
        if(Test_False == ps_test_wait_urc("\r\n+QIURC:",60))
        {
            ucFailNum++;
            continue;
        }
        ps_test_wait_time(10);
        if(Test_False == ps_test_send_at_wait_urc("AT+QIRD=1\r\n","\r\n0011223344556677889900112233445566778899001122334455667788990011223344556677889900112233445566778899",NULL,60))
        {
            ucFailNum++;
            continue;
        }
        ps_test_wait_time(600);
        if(Test_False == ps_test_send_at_wait_urc("AT+QICLOSE=1\r\n","\r\nOK",NULL,20))
        {
            ucFailNum++;
            continue;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+NL2THP=0\r\n","\r\nOK",NULL,10))
        {
            ucFailNum++;
            continue;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=0\r\n","\r\n+CGEV: ME DETACH",NULL,60))
        {
            ucFailNum++;
            continue;
        }
        ps_test_wait_time(10);
    }
    // ps_test_send_at_wait_urc("AT+NV=SET,RATETEST,0","\r\nOK",NULL,10);
    if(ucFailNum >= 0)
    {
        ucCaseResult = Test_False;
    }
    return ucCaseResult;
}

/****************************
ps_default_test006
TEST: 寻呼
*****************************/
UCHAR ps_test_paging()
{
    unsigned char ucCaseResult = Test_True;
    unsigned char i = 0,j = 0;
    unsigned char ucFailNum = 0;
    //unsigned short usSocketPort = g_softap_fac_nv->usPSTestSocketPort;
    //unsigned char pcmd[100] = {0};

    if(Test_False == ps_test_cfun1_to_idle_api())
    {
        return Test_False;
    }
    ps_test_wait_time(10);
#if USR_CUSTOM7
    ps_test_send_at_wait_urc("AT+NV=SET,FASTCONNREL,1\r\n","OK",NULL,10);
    ps_test_send_at_wait_urc("AT+QSCLKEX=0,1\r\n","OK",NULL,10);
#endif
    ps_test_send_at_wait_urc("AT+QICLOSE=1\r\n","OK",NULL,10);
    if(Test_False == ps_test_send_at_wait_urc("AT+QIOPEN=1,1,\"TCP\",\"139.224.131.190\",398,0,1\r\n","+QIOPEN: 1,0",NULL,10))
    {
        return Test_False;
    }
    ps_test_wait_time(2);
    if(Test_False == ps_test_send_at_wait_urc("AT+QICFG=\"send/auto\",1,121,\"ffffff7b226576656e74223a22686561727462656174222c22646964223a225651475831393334353135545a5044222c22737461747573223a22736c656570222c22746f6b656e223a2265696e796f696931726961222c2261636b223a2231227d4064656c6179403940\",0,1,30\r\n","+QIURC: \"recv\",1,1",NULL,10))
    {
        return Test_False;
    }
    for(j = 1;j <= 30 ;j++)
    {
        ps_test_wait_time(115);

        if(Test_False == ps_test_wait_urc("+QIURC: \"recv\",1,1",60))
        {
            ucFailNum++;
            continue;
        }
        ps_test_wait_time(2);
        if(Test_False == ps_test_send_at_wait_urc("AT+CSCON?\r\n","+CSCON","+CSCON: 0,0",5))
        {
            xy_printf( 0, ATC_AP_T, WARN_LOG,"[ps_test_paging]idle fail");
            continue;
        }
    }
    if(Test_False == ps_test_send_at_wait_urc("AT+QICLOSE=1\r\n","\r\nOK",NULL,20))
    {
        return Test_False;
    }
    if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=0\r\n","\r\n+CGEV: ME DETACH",NULL,60))
    {
        return Test_False;
    }
    ps_test_wait_time(10);
    if(ucFailNum >= 0)
    {
        ucCaseResult = Test_False;
    }
    return ucCaseResult;
}
/****************************
ps_default_test007
TEST: 多cid激活，业务
*****************************/
UCHAR ps_test_mutil_default_cid()
{
    unsigned char ucCaseResult = Test_True;
    unsigned char i = 0;
    unsigned char ucFailNum = 0;
    for(i = 1 ; i <= 1; i++)
    {
        ps_test_cfun0_api();
        if(Test_False == ps_test_cfun1_to_idle_api())
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGDCONT=2,IP\r\n","\r\nOK",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGACT=1,2\r\n","\r\n+CGEV: ME PDN ACT",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGDCONT=3,IPV4V6\r\n","\r\nOK",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGDCONT=4,IP\r\n","\r\nOK",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGACT=1,3,4\r\n","\r\n+CGEV: ME PDN ACT",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+QPING=1,***************,60,1\r\n","\r\n+QPING: 0,1","\r\n+QPING: 0,1,1",63))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+QPING=2,***************,60,1\r\n","\r\n+QPING: 0,1","\r\n+QPING: 0,1,1",63))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+QPING=3,***************,60,1\r\n","\r\n+QPING: 0,1","\r\n+QPING: 0,1,1",63))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+QPING=4,***************,60,1\r\n","\r\n+QPING: 0,1","\r\n+QPING: 0,1,1",63))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGACT=0,2,3,4\r\n","\r\n+CGEV: ME PDN DEACT",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGDCONT=2\r\n","\r\nOK",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGDCONT=3\r\n","\r\nOK",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGDCONT=4\r\n","\r\nOK",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False != ps_test_send_at_wait_urc("AT+CGDCONT?\r\n","\r\n+CGDCONT: 2",NULL,10))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=0\r\n","\r\n+CGEV: ME DETACH",NULL,60))
        {
            ucFailNum++;
            break;
        }
        ps_test_wait_time(10);
    }
    if(ucFailNum > 0)
    {
        ps_test_send_at_wait_urc("AT+CGATT=0\r\n","\r\nOK",NULL,60);
        ps_test_send_at_wait_urc("AT+CGDCONT=2\r\n","\r\nOK",NULL,60);
        ps_test_send_at_wait_urc("AT+CGDCONT=3\r\n","\r\nOK",NULL,60);
        ps_test_send_at_wait_urc("AT+CGDCONT=4\r\n","\r\nOK",NULL,60);
    }
    return ucCaseResult;
}
/****************************
ps_default_test008
TEST: 专有承载激活,去激活
*****************************/
UCHAR ps_test_secondary_cid()
{
    unsigned char ucCaseResult = Test_True;
    unsigned char i = 0;
    unsigned char ucFailNum = 0;

    for(i = 1 ; i <= 1; i++)
    {
        ps_test_cfun0_api();
        if(Test_False == ps_test_cfun1_to_idle_api())
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGDCONT=2,1\r\n","\r\nOK",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGACT=1,2\r\n","\r\n+CGEV: ME PDN ACT",NULL,60))
        {
            xy_printf(0, ATC_AP_T, WARN_LOG,"[ps_test_default_test008] NOT SUPPORT Proprietary bearer");
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+QPING=1,***************,60,1\r\n","\r\n+QPING: 0,1","\r\n+QPING: 0,1,1",63))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGTFT=2,1,1,\"172.168.70.151.255.255.255.255\",17,\"1.65535\",\"1.65535\",,,,3\r\n","\r\nOK",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGEQOS=2,2,251,13,251,13\r\n","\r\nOK",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGCMOD=0,2\r\n","\r\nOK",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGACT=0,2\r\n","\r\n+CGEV: ME PDN DEACT",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CGDSCONT=2\r\n","\r\nOK",NULL,60))
        {
            ucFailNum++;
            break;
        }
        if(Test_False != ps_test_send_at_wait_urc("AT+CGDSCONT?\r\n","\r\n+CGDSCONT: 2",NULL,10))
        {
            ucFailNum++;
            break;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=0\r\n","\r\n+CGEV: ME DETACH",NULL,60))
        {
            ucFailNum++;
            break;
        }
        ps_test_wait_time(10);
    }
    if(ucFailNum > 0)
    {
        ps_test_send_at_wait_urc("AT+CGATT=0\r\n","\r\nOK",NULL,60);
        ps_test_send_at_wait_urc("AT+CGDSCONT=2\r\n","\r\nOK",NULL,60);
    }
    return ucCaseResult;
}
/****************************
ps_default_test009
TEST: 算法遍历
*****************************/
UCHAR ps_test_security_algorithm()
{
    unsigned char ucCaseResult = Test_True;
    unsigned char i = 0;
    unsigned char ucFailNum = 0;
    unsigned char ucExceedThresholdNum = 0;
    unsigned long ulCfunStarttime = 0;
    unsigned long ulCfunEndtime = 0;
    unsigned char* pucSecalgMode;
    unsigned char  aucSecalgMode[][9] = {"NULL","ZUC","AES","no_SNOW3G","SNOW3G","no_AES","no_ZUC","ALL"};

    pucSecalgMode = AtcAp_Malloc(30);
    for(i = 8; i <= 15 ; i++)
    {
        if(i == 8)
        {
            continue;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=0\r\n","\r\nOK",NULL,60))
        {
            ps_test_send_at_wait_urc("AT+NSET=SECALG,15\r\n","\r\nOK",NULL,60);
            AtcAp_Free(pucSecalgMode);
            return Test_False;
        }
        ps_test_wait_time(10);
        sprintf(pucSecalgMode,"AT+NSET=SECALG,%d\r\n",i);
        xy_printf(0,ATC_AP_T,WARN_LOG,"[ps_test_default_test009] will set %s" ,aucSecalgMode[i-8]);
        if(Test_False == ps_test_send_at_wait_urc(pucSecalgMode,"\r\nOK",NULL,10))
        {
            ucFailNum++;
            continue;
        }
        ulCfunStarttime = osKernelGetTickCount();
        if(Test_False == ps_test_cfun1_to_idle_api())
        {
            ucFailNum++;
            continue;
        }
        ulCfunEndtime = osKernelGetTickCount();
        if((ulCfunEndtime - ulCfunStarttime) > 60000)
        {
            ucExceedThresholdNum++;
            xy_printf( 0, ATC_AP_T, WARN_LOG,"[ps_test_default_test001]attach%d spend time = %ld, time over 1min = %d", i, ulCfunEndtime - ulCfunStarttime, ucExceedThresholdNum);
        }
        else
        {
            xy_printf( 0, ATC_AP_T, WARN_LOG,"[ps_test_default_test001]attach%d spend time = %ld", i, ulCfunEndtime - ulCfunStarttime);
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+QPING=1,***************,60,1\r\n","\r\n+QPING: 0,1","\r\n+QPING: 0,1,1",63))
        {
            xy_printf(0,ATC_AP_T,WARN_LOG,"[ps_test_default_test009] %s,ping fail" ,aucSecalgMode[i-8]);
            ucFailNum++;
            continue;
        }
        if(Test_False == ps_test_send_at_wait_urc("AT+CFUN=0\r\n","\r\n+CGEV: ME DETACH",NULL,60))
        {
            ucFailNum++;
            continue;
        }
        ps_test_wait_time(10);
    }
    if(ucFailNum >= 5)
    {
        ucCaseResult = Test_False;
    }
    AtcAp_Free(pucSecalgMode);
    return ucCaseResult;
}
/****************************
ps_default_test010
TEST: 空闲态ping
*****************************/
UCHAR ps_test_idle_ping_200()
{
    unsigned char ucCaseResult = Test_True;
    unsigned char i = 0;
    unsigned char ucFailNum = 0;
    
    if(Test_False == ps_test_cfun1_to_idle_api())
    {
        return Test_False;
    }
    for(i = 1; i <= 100 ; i++)
    {
        if(Test_False == ps_test_send_at_wait_urc("AT+QPING=1,***************,60,1\r\n","\r\n+QPING: 0,1","\r\n+QPING: 0,1,1",63))
        {
            ucFailNum++;
            continue;
        }
       if(Test_False == ps_test_check_rrc_state())
        {
            ucFailNum++;
            continue;
        }
    }
    if(ucFailNum >= 50)
    {
        xy_printf( 0, ATC_AP_T, WARN_LOG,"[ps_test_default_test010]fail NUM > 50");
        ucCaseResult = Test_False;
    }
    return ucCaseResult;
}
/****************************
ps_default_test011
TEST: 连接态ping
*****************************/
UCHAR ps_test_connect_ping_200()
{
    unsigned char ucCaseResult = Test_True;
    unsigned char i = 0;
    unsigned char ucFailNum = 0;
    
    if(Test_False == ps_test_cfun1_to_idle_api())
    {
        return Test_False;
    }
    for(i = 1; i <= 200 ; i++)
    {
        if(Test_False == ps_test_send_at_wait_urc("AT+QPING=1,***************,60,1\r\n","\r\n+QPING: 0,1","\r\n+QPING: 0,1,1",63))
        {
            ucFailNum++;
            continue;
        }
    }
    if(ucFailNum >= 200)
    {
        xy_printf( 0, ATC_AP_T, WARN_LOG,"[ps_test_default_test010]fail NUM > 200");
        ucCaseResult = Test_False;
    }
    return ucCaseResult;
}

/****************************
ps_default_test012
TEST: 空闲态待机
*****************************/
UCHAR ps_test_idle_keep()
{
    unsigned char ucCaseResult = Test_True;
    unsigned char i = 0;
    unsigned char ucFailNum = 0;
    
    if(Test_False == ps_test_cfun1_to_idle_api())
    {
        return Test_False;
    }
    for(i = 1; i <= 60 ; i++)
    {
        if(Test_False == ps_test_send_at_wait_urc("AT+NUESTATS=CELL\r\n","\r\nOK",NULL,60))
        {
            return Test_False;
        }
        ps_test_wait_time(60);
    }
    return ucCaseResult;
}
#endif