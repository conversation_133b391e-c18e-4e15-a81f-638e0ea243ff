/*******************************************************************************
 *系统级AT命令，只能调用xy_system.h/xy_lpm.h相关API接口*
 ******************************************************************************/
#include "at_cmd_basic.h"
#include "at_com.h"
#include "at_error.h"
#include "factory_nv.h"
#include "oss_nv.h"
#include "softap_nv.h"
#include "xy_flash.h"
#include "xy_lpm.h"
#include "xy_ps_api.h"
#include "xy_sleep_lock.h"
#include "xy_system.h"
#include "xy_utils.h"
#include "xy_at_api.h"

/*AT+RESET=<mode> */
int at_RESET_req(char *at_buf, char **prsp_cmd)
{
	(void) at_buf;

	if (g_req_type == AT_CMD_ACTIVE || g_req_type == AT_CMD_REQ)
	{	
		int mode = 0;
		
		if (at_parse_param("%d[0-1]", at_buf, &mode) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

		xy_cfun_excute(NET_CFUN5);
		appAtWriteImmediately(get_current_ttyFd(), "\r\nOK\r\n\r\nRESETING\r\n", strlen("\r\nOK\r\n\r\nRESETING\r\n"));
		
		/*AP核应用直接触发复位*/
		if(mode == 0)
		{
			erase_nv_flash();
			xy_flash_erase((void*)WORKING_FS_BASE, WORKING_FS_LEN);
			xy_Soft_Reset(SOFT_RB_BY_RESET);
		}
		else /*在IDLE线程入口处执行复位*/
		{
			xy_Soft_Reset_safe(1);   
		}
		
		return AT_ASYN;
	}
	else
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);

	return AT_END;
}

// AT+NRB[=<mode>] 
int at_NRB_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_ACTIVE || g_req_type == AT_CMD_REQ)
	{
		int mode = 0;
		if (at_parse_param("%d[0-1]", at_buf, &mode) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

		xy_cfun_excute(NET_CFUN5);
		appAtWriteImmediately(get_current_ttyFd(), "\r\nOK\r\n\r\nREBOOTING\r\n", strlen("\r\nOK\r\n\r\nREBOOTING\r\n"));

		/*AP核应用直接触发复位，可能造成低概率NV尚未保存到FLASH中就复位了*/
		if(mode == 0)
		{
			xy_Soft_Reset(SOFT_RB_BY_NRB);
		}
		else /*在IDLE线程入口处执行复位，需要确保NV保存不在IDLE线程中执行，否则会造成NV保存未执行*/
		{
			xy_Soft_Reset_safe(0);	
			return AT_ASYN;
		}
	}
	else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }

	return AT_END;
}

/*AT+LOCK=<anable>,<lock type> 系统锁命令*/
int at_LOCK_req(char *at_buf, char **prsp_cmd)
{
	int enable = 0;
	uint32_t type = WL_ALL;

	if (g_req_type == AT_CMD_REQ)
	{
		if (at_parse_param("%d(0-1),%d[0-255]", at_buf,&enable,&type) != XY_OK)
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
		}
		
		if (enable == 1)
		{
			sys_lock(type);
		}
		else if (enable == 0)
		{
			sys_unlock(type);
		}
		else
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
		}	
	}
	return AT_END;
}


