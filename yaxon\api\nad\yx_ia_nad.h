/**********************************************************************/
/**
 * @file yx_ia_nad.c
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-13
 * @version V1.0
 * @brief 网络接入设备的适配
 **********************************************************************/
#ifndef YX_IA_NAD_H
#define YX_IA_NAD_H

#include "yx_type.h"
#include "module_id_def.h"
#define NAD_ERRNO            (0x80 + YX_FUNC_MODULE_ID_YX_GSM)
#define NAD_MNC_LEN          3
#define NAD_MCC_LEN          3

/**
 * @brief 错误码枚举
 * @ingroup ia_nad
 */
typedef enum {
    IA_NAD_SUUCESS = 0,                                    /**< 成功 */
    IA_NAD_INVALID_PARAM_ERR = (NAD_ERRNO << 24) | 0x01,   /**< 参数错误 */
    IA_NAD_ADAPTER_PARAM_ERR = (NAD_ERRNO << 24) | 0x02,   /**< 适配错误 */

    IA_NAD_EXECUTE_FAILED    = (NAD_ERRNO << 24) | 0x03,   /**< 执行失败 */
} ia_nad_errno_e;

/**
 * @brief SIM卡状态枚举
 * @ingroup ia_nad
 */
typedef enum {
    SIM_STATE_READY = 0,        /**< SIM卡已就绪 */
	SIM_STATE_SIMPIN,           /**< 等待验证SIM卡的PIN码 */
	SIM_STATE_SIMPUK,           /**< 等待验证SIM卡的PUK码 */
    SIM_STATE_NOINSERTED,       /**< SIM卡未插入 */
    SIM_STATE_MAX
} ia_sim_state_e;

/**
 * @brief 驻网状态枚举(PS状态)
 * @ingroup ia_nad
 */
typedef enum {
    NAD_STATE_NOT_REGISTERED = 0,  /**< 未注册 */
    NAD_STATE_REGISTERED,          /**< 已注册 */
    NAD_STATE_NOT_ACTIVE,          /**< 未激活 */
    NAD_STATE_HOME,                /**< 本地网络 */
    NAD_STATE_ROAMING,             /**< 漫游状态 */
    NAD_STATE_SEARCHING,           /**< 搜索网络中 */
    NAD_STATE_REG_DENIED,          /**< 注册被拒绝 */
    NAD_STATE_UNKNOWN,             /**< 未知状态 */
    NAD_STATE_MAX
} ia_nad_state_e;

/**
 * @brief 网络制式枚举
 * @ingroup ia_nad
 */
typedef enum {
    NAD_MODE_AUTO = 0,          /**< 自动模式 */
    NAD_MODE_GSM,               /**< GSM模式  */
    NAD_MODE_LTE,               /**< LTE模式  */
    NAD_MODE_MAX
} ia_nad_mode_e;

/**
 * @brief 网络调制解调器功能枚举
 * @ingroup ia_nad
 */
typedef enum {
    NAD_MODEM_CFUN_MINI = 0,          /**< 最小功能, 未使用 */
    NAD_MODEM_CFUN_WORK = 1,          /**< 工作模式  */
    NAD_MODEM_CFUN_FLIGHT = 4,        /**< 飞行模式  */
    NAD_MODEM_CFUN_MAX
} ia_nad_modem_e;

/**
 * @brief 运营商类型枚举
 * @ingroup ia_nad
 */
typedef enum {
    NAD_OP_CMCC = 0,              /**< 中国移动 */
    NAD_OP_CUCC,                  /**< 中国联通 */
    NAD_OP_CTCC,                  /**< 中国电信 */
    NAD_OP_MAX
} ia_nad_op_type_e;

/**
 * @brief 小区信息
 * @ingroup ia_nad
 */
typedef struct {
    INT8U  operator;              /**< 运营商类型 @see ia_nad_type_e */
    CHAR   mcc[NAD_MCC_LEN+1];    /**< 移动设备国家代码 */
    CHAR   mnc[NAD_MNC_LEN+1];    /**< 移动设备网络代码 */
    INT32U lac;                   /**< 本地区域代码:GSM */
    INT32U tac;                   /**< 跟踪区域代码:LTE */
    INT32U cid;                   /**< 基站编号 */
    INT32U pcid;                  /**< 小区编号 */
    INT32U sinr;                  /**< 信噪比 */
    INT32U rsrq;                  /**< 参考接收信号质量 */
    INT32U rsrp;                  /**< 参考接收信号功率 */
    INT32U rssi;                  /**< 接收信号强度指示:dbm */
} ia_nad_op_info_t;

/**
 * @brief 初始化网络模块
 *
 * @param nsim [in]当前的SIM卡号, 0表示主卡(默认), 1表示次卡
 * @retval IA_NAD_SUUCESS(0):成功 其它值:失败 @see ia_nad_errno_e
 * @note 执行下面所有接口前必须先调用一次本接口
 * @ingroup ia_nad
 */
INT32U yx_ia_nad_init(INT8U nsim);

/**
 * @brief 获取SIM卡状态
 *
 * @param nsim [in]当前的SIM卡号, 0表示主卡(默认), 1表示次卡
 * @param state [out]SIM卡状态 @see ia_sim_state_e
 * @retval IA_NAD_SUUCESS(0):成功 其它值:失败 @see ia_nad_errno_e
 * @ingroup ia_nad
 */
INT32U yx_ia_nad_get_sim_state(INT8U nsim, INT8U *state);

/**
 * @brief 获取IMEI
 *
 * @param nsim [in]当前的SIM卡号, 0表示主卡(默认), 1表示次卡
 * @param imei [out]设备的IMEI号, 有效位:15
 * @param size [in]设备的IMEI号内存大小, 建议值:16
 * @retval IA_NAD_SUUCESS(0):成功 其它值:失败 @see ia_nad_errno_e
 * @note 执行前先初始化指向IMEI内存的指针
 * @ingroup ia_nad
 */
INT32U yx_ia_nad_get_imei(INT8U nsim, CHAR *imei, INT8U size);

/**
 * @brief 获取IMSI
 *
 * @param nsim [in]当前的SIM卡号, 0表示主卡(默认), 1表示次卡
 * @param imsi [out]设备的IMSI号, 有效位:15
 * @param size [in]设备的IMSI号内存大小, 建议值:16
 * @retval IA_NAD_SUUCESS(0):成功 其它值:失败 @see ia_nad_errno_e
 * @note 执行前先初始化指向IMSI内存的指针
 * @ingroup ia_nad
 */
INT32U yx_ia_nad_get_imsi(INT8U nsim, CHAR *imsi, INT8U size);

/**
 * @brief 获取ICCID
 *
 * @param nsim [in]当前的SIM卡号, 0表示主卡(默认), 1表示次卡
 * @param iccid [out]设备的ICCID号, 有效位:20
 * @param size [in]设备的ICCID号内存大小, 建议值:32
 * @retval IA_NAD_SUUCESS(0):成功 其它值:失败 @see ia_nad_errno_e
 * @note 执行前先初始化指向ICCID内存的指针
 * @ingroup ia_nad
 */
INT32U yx_ia_nad_get_iccid(INT8U nsim, CHAR *iccid, INT8U size);

/**
 * @brief 设置网络制式
 *
 * @param nsim [in]当前的SIM卡号, 0表示主卡(默认), 1表示次卡
 * @param mode [in]网络制式 @see ia_nad_mode_e
 * @retval IA_NAD_SUUCESS(0):成功 其它值:失败 @see ia_nad_errno_e
 * @ingroup ia_nad
 */
INT32U yx_ia_nad_set_cnmp(INT8U nsim, INT8U mode);

/**
 * @brief 获取网络制式
 *
 * @param nsim [in]当前的SIM卡号, 0表示主卡(默认), 1表示次卡
 * @param mode [out]网络制式 @see ia_nad_mode_e
 * @retval IA_NAD_SUUCESS(0):成功 其它值:失败 @see ia_nad_errno_e
 * @ingroup ia_nad
 */
INT32U yx_ia_nad_get_cnmp(INT8U nsim, INT8U *mode);

/**
 * @brief 设置调试解调器功能
 *
 * @param nsim [in]当前的SIM卡号, 0表示主卡(默认), 1表示次卡
 * @param mode [in]Modem模式 @see ia_nad_modem_e
 * @retval IA_NAD_SUUCESS(0):成功 其它值:失败 @see ia_nad_errno_e
 * @ingroup ia_nad
 */
INT32U yx_ia_nad_set_cfun(INT8U nsim, INT8U cfun);

/**
 * @brief 获取调试解调器功能
 *
 * @param nsim [in]当前的SIM卡号, 0表示主卡(默认), 1表示次卡
 * @param mode [out]Modem模式 @see ia_nad_modem_e
 * @retval IA_NAD_SUUCESS(0):成功 其它值:失败 @see ia_nad_errno_e
 * @ingroup ia_nad
 */
INT32U yx_ia_nad_get_cfun(INT8U nsim, INT8U *cfun);

/**
 * @brief 获取网络信号强度
 *
 * @param nsim [in]当前的SIM卡号, 0表示主卡(默认), 1表示次卡
 * @param csq [out]网络信号强度, 
 * @retval IA_NAD_SUUCESS(0):成功 其它值:失败 @see ia_nad_errno_e
 * @ingroup ia_nad
 */
INT32U yx_ia_nad_get_csq(INT8U nsim, INT8U *csq);

/**
 * @brief 获取驻网状态(PS状态)
 *
 * @param nsim [in]当前的SIM卡号, 0表示主卡(默认), 1表示次卡
 * @param creg [out]驻网状态 @see ia_nad_state_e
 * @param cgreg [out]是否驻网,可传空值不获取 @see ia_nad_state_e
 * @retval IA_NAD_SUUCESS(0):成功 其它值:失败 @see ia_nad_errno_e
 * @ingroup ia_nad
 */
INT32U yx_ia_nad_get_cgreg(INT8U nsim, INT8U *creg, INT8U *cgreg);

/**
 * @brief 获取小区信息
 *
 * @param nsim [in]当前的SIM卡号, 0表示主卡(默认), 1表示次卡
 * @param cpsi [out]小区信息 @see ia_nad_op_info_t
 * @retval IA_NAD_SUUCESS(0):成功 其它值:失败 @see ia_nad_errno_e
 * @ingroup ia_nad
 */
INT32U yx_ia_nad_get_cpsi(INT8U nsim, ia_nad_op_info_t *cpsi);

#endif