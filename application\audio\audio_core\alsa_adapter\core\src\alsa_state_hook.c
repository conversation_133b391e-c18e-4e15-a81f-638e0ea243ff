#include "alsa_config.h"
#include "alsa_extra.h"
#include "hal_trace.h"

#ifdef ALSA_STATE_HOOK_EN
#ifdef ALSA_AUDIO_PROCESS_RENDER_EN
#include "audio_user_process.h"
#endif
#include <string.h>

static struct alsa_state_hook_t g_alsa_state_hook = {0};

int alsa_state_open_hook_ivk(int sample_rate, int channels, int bits)
{
    if (g_alsa_state_hook.open)
    {
        int format = 0;
        switch (bits)
        {
        case 16:
            format = ALSA_STATE_HOOK_PCM_FORMAT_S16;
            break;
        case 24:
            format = ALSA_STATE_HOOK_PCM_FORMAT_S24;
            break;
        case 32:
            format = ALSA_STATE_HOOK_PCM_FORMAT_S32;
            break;
        default:
            ASSERT(0, "%s %d unsupport bits %d", __func__, __LINE__, bits);
        }
        return g_alsa_state_hook.open(1, g_alsa_state_hook.user_arg, sample_rate, channels, format);
    }
    return -1;
}

int alsa_state_start_hook_ivk(void)
{
    if (g_alsa_state_hook.start)
    {
        return g_alsa_state_hook.start(g_alsa_state_hook.user_arg);
    }
    return -1;
}

int alsa_state_process_hook_ivk(const uint8_t *input, uint32_t len)
{
    if (g_alsa_state_hook.process)
    {
        return g_alsa_state_hook.process(g_alsa_state_hook.user_arg, input, len);
    }
    return -1;
}

int alsa_state_stop_hook_ivk(void)
{
    if (g_alsa_state_hook.stop)
    {
        return g_alsa_state_hook.stop(g_alsa_state_hook.user_arg);
    }
    return -1;
}

int alsa_state_close_hook_ivk(void)
{
    if (g_alsa_state_hook.close)
    {
        return g_alsa_state_hook.close(g_alsa_state_hook.close);
    }
    return -1;
}

int alsa_state_hook_register(struct alsa_state_hook_t *state_hook)
{
    memset(&g_alsa_state_hook, 0, sizeof(g_alsa_state_hook));
    g_alsa_state_hook.open = state_hook->open;
    g_alsa_state_hook.start = state_hook->start;
    g_alsa_state_hook.process = state_hook->process;
    g_alsa_state_hook.stop = state_hook->stop;
    g_alsa_state_hook.flush = state_hook->flush;
    g_alsa_state_hook.close = state_hook->close;
    g_alsa_state_hook.user_arg = state_hook->user_arg;
#ifdef ALSA_AUDIO_PROCESS_RENDER_EN
    audio_user_notify_register((struct audio_user_notify_hook_t *)state_hook);
#endif
    return 0;
}

int alsa_state_hook_unregister(void)
{
    memset(&g_alsa_state_hook, 0, sizeof(g_alsa_state_hook));
#ifdef ALSA_AUDIO_PROCESS_RENDER_EN
    audio_user_notify_unregister();
#endif
    return 0;
}

#endif // ALSA_STATE_HOOK_EN