使用注意

1. AT&<command><param> 命令， &作用和+一样， 以AT&W0为例，在at_cmd_regist.c中注册参考如下  {"W", at_W_req},  
   AT框架仅支持command为1字节大小的英文字符，同时param长度也必须为1且为数字字符，诸如AT&WW0  AT&W12 AT&12 这种命令不支持，如有业务特殊需求，请及时告知；
2. AT<command>命令， 类似于ATE ATD这样的命令，在at_cmd_regist.c中注册参考如下  {"ATE", at_ATE_req}，不得按照如下方式定义{"E", at_ATE_req}。


延迟锁说明：
SLEEP唤醒后，不会默认申请延迟锁，而是在at_recv_from_lpuart中添加startAtLpuartLockAndTimer(5)或at_delaylock_act()。
at_delaylock_act()内部实现，当发现小于等于115200，不会脏乱，进而无需申请锁，随时可进SLEEP；高的波特率才申请锁。
高于115200的AT唤醒SLEEP，第一条必脏乱，不做处理，不做应答，且会申请5秒锁，以供外部MCU再发送后续AT命令。

波特率自适应：
一旦开启波特率自适应，上电开机或重启开机后，必须使用“AT\r\n”命令进行波特率自适应。因为用于触发自适应的AT命令不会被真实处理，仅在底层直接回复“\r\nOK\r\n”，以指示外部MCU可以继续正常AT命令交互。