/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "at_com.h"
#include "at_context.h"
#include "at_ctl.h"
#include "at_error.h"
#include "at_passthrough.h"
#include "posix_io.h"
#include "softap_nv.h"
#include "xy_at_api.h"
#include "xy_lpm.h"
#include "xy_ps_api.h"
#include "xy_sleep_lock.h"
#include "xy_system.h"

/*******************************************************************************
 *                             Macro definitions                               *
 ******************************************************************************/
#define SINGLE_AT_MAX_LEN  		    8192
#define AT_LPUART_RESERVED_ALLOC    256
#define AT_COMBINE_CMD_MAX_LEN		320		/* 级联命令最大长度 */

/*******************************************************************************
 *						AT Farps function implementations					   *
 ******************************************************************************/
osTimerId_t gAtLpuartLockTimer = NULL;
void atLpuartLockTimeout(osTimerId_t arg)
{
    sleep_unlock(g_at_lpuart_lock, WL_ALL);
    osMutexAcquire(gAtLpuartLock_Mutex, osWaitForever);
    osTimerDelete(gAtLpuartLockTimer);
    gAtLpuartLockTimer = NULL;
    xy_printf(0, XYAPP, WARN_LOG, "[LOCK]atLpuartLockTimeout UNLOCK!!!\n");
    osMutexRelease(gAtLpuartLock_Mutex);
}

void startAtLpuartLockAndTimer(uint32_t sec)
{
    /* 增加at lpuart锁 */
    sleep_lock(g_at_lpuart_lock, WL_ALL);
	xy_printf(0, XYAPP, WARN_LOG, "[LOCK]startAtLpuartLockAndTimer!!!\n");
    osMutexAcquire(gAtLpuartLock_Mutex, osWaitForever);
    if (gAtLpuartLockTimer == NULL) 
    {
        osTimerAttr_t attr = {0};
        attr.name = "atLock";
        gAtLpuartLockTimer = osTimerNew((osTimerFunc_t)atLpuartLockTimeout, osTimerOnce, NULL, &attr);
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps][startAtLpuartLockAndTimer]lock at lpuart");
    }
    osTimerStart(gAtLpuartLockTimer, sec*1000);
    osMutexRelease(gAtLpuartLock_Mutex);
}

void stopAtLpuartLockAndTimer(void)
{
    sleep_unlock(g_at_lpuart_lock, WL_ALL);
    osMutexAcquire(gAtLpuartLock_Mutex, osWaitForever);
    if (gAtLpuartLockTimer != NULL)
    {
        osTimerDelete(gAtLpuartLockTimer);
        gAtLpuartLockTimer = NULL;
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps][stopAtLpuartLockAndTimer]unlock at lpuart");
    }
    osMutexRelease(gAtLpuartLock_Mutex);
}

static void insertAtRxCmdToListTail(atRxCmdList_t* rxCmdList, at_cmd_cache_t *newNode)
{
	if (rxCmdList->head == NULL)
    {
		// 如果链表为空，新节点既是头节点也是尾节点
		rxCmdList->head = newNode;
		rxCmdList->tail = newNode;
    }
    else 
    {
        // 否则，将新节点插入到尾节点后面，并更新尾节点
        rxCmdList->tail->next = newNode;
        rxCmdList->tail = newNode;
    }
}

static at_cmd_cache_t* popAtRxCmdFromHead(atRxCmdList_t* rxCmdList)
{
	if (rxCmdList->head == NULL)
	{
		return NULL;
	}

    at_cmd_cache_t *temp = rxCmdList->head; // 保存头节点
    rxCmdList->head = rxCmdList->head->next; // 更新头节点

    if (rxCmdList->head == NULL)
    {
        // 如果链表为空，更新尾节点为NULL
        rxCmdList->tail = NULL;
    }

    return temp; // 返回弹出的数据
}

void insertAtCombToTail(at_context_t *ctx, bool fistAT, char *data, uint32_t bufferIndex)
{
	at_cmd_cache_t *newNode = (at_cmd_cache_t *)xy_malloc(sizeof(at_cmd_cache_t));
	newNode->data = (char *)xy_malloc(bufferIndex + 4);
	if (fistAT)
	{
		/* 第一条AT自带前缀，不需要拷贝AT */
		sprintf(newNode->data, "%s\r", data); 
		xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps][parseATCombCommand]first combine at:%s", newNode->data);
	}
	else
	{
		sprintf(newNode->data, "AT%s\r", data);
		xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps][parseATCombCommand]combine at:%s", newNode->data);
	}

	newNode->fd = ctx->fd;
	newNode->len = strlen(newNode->data);
	newNode->next = NULL;

    insertAtRxCmdToListTail(&ctx->rxCombCmdList, newNode);
}

void freeAtRxCmdList(atRxCmdList_t* rxCmdlist)
{
	at_cmd_cache_t *current = rxCmdlist->head;
	at_cmd_cache_t *next;
	while (current != NULL)
	{
		next = current->next;
		if(current->data != NULL)
			xy_free(current->data);
		xy_free(current);
		current = next;
	}
	rxCmdlist->head = NULL;
	rxCmdlist->tail = NULL;
}

void parseATCombCommand(at_context_t *ctx) 
{
	xy_assert(ctx != NULL);
	uint32_t i = 0;
    int bufferIndex = 0;
    bool inQuotes = false;
	bool firstAT = true;
	char *command = (char *)ctx->g_farps_rcv_mem;

	if (command == NULL ||
		strchr(command, ';') == NULL ||
		(ctx->state & RCV_REQ_NOW) ||
		ctx->g_have_rcved_len > AT_COMBINE_CMD_MAX_LEN)
	{
		/* 未支持平台AT级联、命令中不存在分号、当前有at命令在处理、总长度超过级联命令最大长度限制直接退出 */
		return;
	}

	xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps][parseATCombCommand]plat at combine enable,have recv len:%d,fd:%d,state:%d", ctx->g_have_rcved_len, ctx->fd, ctx->state);
	
	char* buffer = (char*)xy_malloc(AT_COMBINE_CMD_MAX_LEN);

    // 遍历命令
	for (i = 0; (command[i] != '\r' && i < ctx->g_have_rcved_len); i++)
	{
		char currentChar = command[i];
		// 处理引号内的内容
		if (currentChar == '"')
		{
			inQuotes = !inQuotes; // 切换引号状态
		}

		// 如果遇到分号且不在引号内，则分割命令
		if (currentChar == ';' && !inQuotes)
		{
			buffer[bufferIndex] = '\0';
			if (bufferIndex > 0)
			{			
				if (firstAT)
				{		
					insertAtCombToTail(ctx, firstAT, buffer, bufferIndex);
					firstAT = false;
				}
				else
				{
					insertAtCombToTail(ctx, firstAT, buffer, bufferIndex);
				}
			}
			bufferIndex = 0; // 重置缓冲区索引
		}
		else
		{
			buffer[bufferIndex++] = currentChar; // 将字符存入缓冲区
		}
	}

	// 处理最后一个命令
	if (bufferIndex > 0)
	{
		if (!firstAT)
		{
			buffer[bufferIndex] = '\0';
			xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps][parseATCombCommand]last at:%s", buffer);
			insertAtCombToTail(ctx, firstAT, buffer, bufferIndex);
		}
	}
	xy_free(buffer);
	return;
}

void insertAtToCacheCmdList(at_context_t *ctx)
{
	at_cmd_cache_t *newNode = (at_cmd_cache_t *)xy_malloc(sizeof(at_cmd_cache_t));
    newNode->data = (char *)xy_malloc(ctx->g_have_rcved_len);
    newNode->len = ctx->g_have_rcved_len;
    newNode->fd = ctx->fd;
    newNode->next = NULL;
    memcpy(newNode->data, ctx->g_farps_rcv_mem, ctx->g_have_rcved_len);

    insertAtRxCmdToListTail(&ctx->rxCacheCmdList, newNode);
}

uint8_t getAtRxCmdListNum(atRxCmdList_t* rxCmdList)
{
    if (rxCmdList->head == NULL)
    {
        return 0;
    }

    uint32_t num = 0;
    at_cmd_cache_t *current = rxCmdList->head;
    // 遍历所有有效节点
    while (current != NULL) {
        num++;
        current = current->next;
    }
    xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps][getAtRxCmdListNum]list num:%d", num);
    return num;
}

void resetRecvAtContext(at_context_t *ctx)
{
    if (ctx->g_farps_rcv_mem != NULL)
        xy_free(ctx->g_farps_rcv_mem);
    ctx->g_farps_rcv_mem = NULL;
    ctx->g_have_rcved_len = 0;
    ctx->g_rcv_buf_len = 0;
}

static bool isValidATCommand(at_context_t *ctx) 
{
    uint32_t i = 0;
    char* rcv_mem_ptr = ctx->g_farps_rcv_mem;
    if (ctx->g_have_rcved_len < 3)
    {
        xy_printf(0, PLATFORM_AP, WARN_LOG, "[AtFarps][isValidATCommand]AT len:%d < 3, drop!", ctx->g_have_rcved_len);
        SEND_ERR_RSP_TO_TTY(XY_Err_DropMore, ctx->fd);
        return false;     
    }
    for (i = 0; i < ctx->g_have_rcved_len - 2; i++)
    {
        // 查找AT前缀（不区分大小写）
        if ((ctx->g_farps_rcv_mem[i] == 'A' || ctx->g_farps_rcv_mem[i] == 'a') &&
            (ctx->g_farps_rcv_mem[i + 1] == 'T' || ctx->g_farps_rcv_mem[i + 1] == 't'))
        {
            if (i > 0)
            {
                /* AT不在起始位置，更新接收数据长度,更新数据接收内存 */
                ctx->g_have_rcved_len -= i;
                char* tmp = (char*)xy_malloc(ctx->g_have_rcved_len);
                memcpy(tmp, &ctx->g_farps_rcv_mem[i], ctx->g_have_rcved_len);
                xy_free(ctx->g_farps_rcv_mem);
                ctx->g_farps_rcv_mem = tmp;
            }
            return true;
        }
    }
    xy_printf(0, PLATFORM_AP, WARN_LOG, "[AtFarps][isValidATCommand]AT symbol not found");
    SEND_ERR_RSP_TO_TTY(XY_Err_Prefix, ctx->fd);
    return false;
}

static bool single_atcmd_proc(at_context_t *ctx)
{	
	//debug打印,用于确认at命令接收情况
    if (ctx->g_have_rcved_len <= AT_DEBUG_LOG_MAX_LEN)
    {
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps]tty%d recv %d len at:%s", ctx->fd, ctx->g_have_rcved_len, ctx->g_farps_rcv_mem);
		debug_log_print("[single_atcmd_proc] tty%d recv %d len at:%s\n", ctx->fd, ctx->g_have_rcved_len, ctx->g_farps_rcv_mem);
    }
    else
    {
        /* 单AT长度超4000，打印前缀部分 */
        char at_partial_debug[64] = {0};
        memcpy(at_partial_debug, ctx->g_farps_rcv_mem, 63);
        xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps]tty%d recv %d len long at:%s", ctx->fd, ctx->g_have_rcved_len, at_partial_debug);
		debug_log_print("[single_atcmd_proc] tty%d recv %d len long at:%s\n", ctx->fd, ctx->g_have_rcved_len, at_partial_debug);
    }
    
    if (at_strncasecmp(ctx->g_farps_rcv_mem, "AT+ASSERT\r"))
    {
		/* Assert AT command must be executed immediately without sending to the AT control thread */
        xy_assert(0);
    }

    /* 列表搜网+COPS=?命令耗时过长，协议栈通过ABORT名利取消列表搜网 */
    extern bool xy_atc_PsCancelAtCmdProc(unsigned short usDataLen, unsigned char *pucData, int ittyFd);
    if (xy_atc_PsCancelAtCmdProc(ctx->g_have_rcved_len, ctx->g_farps_rcv_mem, ctx->fd))
    {
        return 0;
    }

	osMutexAcquire(ctx->rxCmd_mux, osWaitForever);

	/* 处理级联命令 */
	parseATCombCommand(ctx);

	if (ctx->state & RCV_REQ_NOW)
	{
		//上一条命令未处理完就收到NRB，直接执行正常软复位流程，不再发送到at_ctl线程，避免上条命令处理结束时清空“AT+NRB”的上下文，造成死机
		if (at_strncasecmp(ctx->g_farps_rcv_mem, "AT+NRB"))
		{
			xy_cfun_excute(NET_CFUN5);
			appAtWriteImmediately(ctx->fd, "\r\nOK\r\n\r\nREBOOTING\r\n", strlen("\r\nOK\r\n\r\nREBOOTING\r\n"));
			xy_Soft_Reset_safe(0);
		}
		else
		{
			char new_prefix[10] = {0};					
			memcpy(new_prefix, ctx->g_farps_rcv_mem, 9);
			/* 缓存队列已满报8007,级联命令不考虑瀑布式场景 */
			if (getAtRxCmdListNum(&ctx->rxCacheCmdList) >= AT_RECV_CMD_CACHE_NUM || getAtRxCmdListNum(&ctx->rxCombCmdList) > 0)
			{
				SEND_ERR_RSP_TO_TTY(XY_Err_ChannelBusy, ctx->fd);
				xy_printf(0, PLATFORM_AP, WARN_LOG, "[AtFarps]at busy block:%s,new:%s,fd:%d\r\n", ctx->at_cmd_prefix, new_prefix, ctx->fd);

				if (IS_DEBUG_MODE())
				{
					char *atstr = xy_malloc(80);
					if (strlen(ctx->at_cmd_prefix) != 0)
						snprintf(atstr, 80, "\r\n+DBGINFO:FARPS BUSY:%s,new:%s,fd:%d\r\n", ctx->at_cmd_prefix, new_prefix, ctx->fd);
					else
						snprintf(atstr, 80, "\r\n+DBGINFO:at_ctl block,new:%s,fd:%d\r\n", new_prefix, ctx->fd);
					xy_printf(0, PLATFORM_AP, WARN_LOG, "%s",atstr);
					xy_free(atstr);
				}	
			}
			else
			{
                /* 待处理的非级联AT命令加入缓存命令列表 */
                insertAtToCacheCmdList(ctx);
                xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps]tty:%d at cmd:%s cached and wait to send", ctx->fd, new_prefix);
			}
		}
		osMutexRelease(ctx->rxCmd_mux);
		return 0;
	}
	else
	{
		if (getAtRxCmdListNum(&ctx->rxCombCmdList) > 0)
		{
			/* 存在级联命令，发送第一条命令，其他命令级联命令根据第一条命令结果进行处理，若命令报错，清空后续所有级联命令 */
			at_cmd_cache_t *command = popAtRxCmdFromHead(&ctx->rxCombCmdList);
			osMutexRelease(ctx->rxCmd_mux);
			ctx->state |= RCV_REQ_NOW;
			//发送给at_ctl线程处理，g_farps_rcv_mem指向at数据字符串，末尾有'\0',g_have_rcved_len不包含'\0'
			send_msg_2_atctl(AT_MSG_RCV_STR_FROM_FARPS, command->data, command->len, ctx->fd);
			xy_free(command->data);
			xy_free(command);
			return 1;		
		}
	}
	osMutexRelease(ctx->rxCmd_mux);

	/* Mark at channel in busy state */
	ctx->state |= RCV_REQ_NOW;

    //发送给at_ctl线程处理，g_farps_rcv_mem指向at数据字符串，末尾有'\0',g_have_rcved_len不包含'\0'
	send_msg_2_atctl(AT_MSG_RCV_STR_FROM_FARPS, ctx->g_farps_rcv_mem, ctx->g_have_rcved_len, ctx->fd);

	return 1;
}

/*检查是否收到尾部特征\r*/
bool trim_and_find_end_symbol(at_context_t *ctx)
{
	uint32_t len = ctx->g_have_rcved_len;

	xy_assert(ctx->g_farps_rcv_mem != NULL);

	//从字符串末尾向前遍历, 删除多余的'\r'和'\n',直到找到第一个非'\r' '\n'字符
	while (len > 0 && (ctx->g_farps_rcv_mem[len - 1] == '\r' || ctx->g_farps_rcv_mem[len - 1] == '\n'))
	{
		if (ctx->g_farps_rcv_mem[len - 1] == '\r' && len > 1 && ctx->g_farps_rcv_mem[len - 2] != '\r' && ctx->g_farps_rcv_mem[len - 2] != '\n')
		{
			if (len < ctx->g_have_rcved_len)
			{
				/* 将重复的'\r'和'\n'替换为'\0'并减少长度 */
				uint32_t offset = ctx->g_have_rcved_len - len;
				memset(ctx->g_farps_rcv_mem + len, 0x0, offset);
				ctx->g_have_rcved_len -= offset;
			}
			return true;
		}
		len--;
	}

	return false;
}

static int at_farps_data_realloc(void* at_ctx, char* data, size_t data_len)
{
    at_context_t *ctx = (at_context_t *)at_ctx;
    int fd = ctx->fd;
	/* lpuart外设处理at命令，如果一次收到超过256字节数据时，直接按收到的长度malloc，不进行realloc,主要应对SLEEP唤醒场景下，lpuart底层可能上报较长at命令 */
    bool uartRealloc = (IS_AT_TTY_PHY(fd) && data_len < AT_LPUART_RESERVED_ALLOC) ? true : false;
	if (ctx->g_have_rcved_len + data_len > SINGLE_AT_MAX_LEN)
	{
		xy_printf(0, PLATFORM_AP, WARN_LOG, "tty:%d input buf too long:%d!", fd, ctx->g_have_rcved_len + data_len);
		debug_log_print("[at_farps_data_realloc] tty:%d input buf too long:%d!\n", fd, ctx->g_have_rcved_len + data_len);
		SEND_ERR_RSP_TO_TTY(XY_Err_NotAllowed, fd);
		return XY_ERR;
	}
    if (ctx->g_farps_rcv_mem == NULL)
	{
        ctx->g_farps_rcv_mem = uartRealloc ? xy_malloc(AT_LPUART_RESERVED_ALLOC) : xy_malloc2(data_len + 1);
        if(ctx->g_farps_rcv_mem == NULL)
        {
        	ATRECV_NET_LOG("tty:%d malloc but mem not enough!", fd);			
            xy_printf(0, PLATFORM_AP, WARN_LOG, "tty:%d malloc but mem not enough!", fd);
			debug_log_print("[at_farps_data_realloc] tty:%d malloc but mem not enough!\n", fd);
			SEND_ERR_RSP_TO_TTY(XY_Err_NoMemory,fd);
			return XY_ERR;
        }
		ctx->g_rcv_buf_len = uartRealloc ? AT_LPUART_RESERVED_ALLOC : data_len + 1;
	}

	if (ctx->g_rcv_buf_len < (ctx->g_have_rcved_len + data_len + 1))
    {
        char* tmp = NULL;
        tmp = uartRealloc ? xy_malloc(ctx->g_rcv_buf_len + AT_LPUART_RESERVED_ALLOC) : xy_malloc2(ctx->g_rcv_buf_len + data_len);

        if (tmp == NULL)
        {
        	ATRECV_NET_LOG("tty:%d realloc but mem not enough!", fd);			
            xy_printf(0, PLATFORM_AP, WARN_LOG, "tty:%d realloc but mem not enough!", fd);
			debug_log_print("[at_farps_data_realloc] tty:%d realloc but mem not enough!\n", fd);
			SEND_ERR_RSP_TO_TTY(XY_Err_NoMemory,fd);
            return XY_ERR;
        }
        
        if (ctx->g_have_rcved_len != 0)
            memcpy(tmp, ctx->g_farps_rcv_mem, ctx->g_have_rcved_len);
        xy_free(ctx->g_farps_rcv_mem);
        ctx->g_farps_rcv_mem = tmp;
        ctx->g_rcv_buf_len = uartRealloc ? (ctx->g_rcv_buf_len + AT_LPUART_RESERVED_ALLOC) : (ctx->g_rcv_buf_len + data_len);
        xy_printf(0, PLATFORM_AP, DEBUG_LOG, "tty:%d realloc and at buffer total size:%d", fd, ctx->g_rcv_buf_len);
		debug_log_print("[at_farps_data_realloc] tty:%d realloc and at buffer total size:%d\n", fd, ctx->g_rcv_buf_len);
    }
	memcpy(ctx->g_farps_rcv_mem + ctx->g_have_rcved_len, data, data_len);
	ctx->g_have_rcved_len += data_len;
	*(ctx->g_farps_rcv_mem + ctx->g_have_rcved_len) = '\0';
    return XY_OK;
}

/**
 * @brief 级联命令场景，用于处理平台命令中间结果与OK结果码组合在一起返回的场景，此函数去除OK结果码
 * @warning 平台命令需保证OK结果码格式为\r\nOK\r\n,否则会有格式问题
 */
static void removeAtTailOkAndOutput(char* buffer, size_t size, int ttyFd)
{
	if (size > 6 && buffer != NULL)
	{
		if (buffer[size - 1] == '\n' && 
			buffer[size - 2] == '\r' && 
			(buffer[size - 3] == 'K' || buffer[size - 3] == 'k') && 
			(buffer[size - 4] == 'O' || buffer[size - 4] == 'o') &&
			buffer[size - 5] == '\n' &&
			buffer[size - 6] == '\r')
		{
			xy_printf(0, PLATFORM_AP, INFO_LOG, "[removeAtTailOkAndOutput]fd:%d,size:%d", ttyFd, size - 6);
			/* 输出除尾部\r\nOK\r\n以外的数据 */
			at_posix_write(ttyFd, (uint8_t *)buffer, size - 6);
		}
	}
}

bool at_send_to_tty(int tty_Fd, void *buf, size_t size, bool result_directly)
{
	xy_assert(osCoreGetState() != osCoreInCritical); //不能在锁中断状态下调用该接口！
	xy_assert(tty_Fd>=0 && tty_Fd<AT_FD_MAX);
	
    at_context_t *ctx = &g_at_tty_ctx[tty_Fd];
	bool isResult = false;

    xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps][at_send_to_tty]send to ttyfd:%d,size=%d", tty_Fd, size);
	debug_log_print("[at_send_to_tty]send to ttyFd,size=%d,ttyfd=%d\n", size, tty_Fd);

	/* 避免和single_at_proc同时处理级联命令，用于保护级联命令链表 */
	osMutexAcquire(ctx->rxCmd_mux, osWaitForever);

	isResult = Is_Result_AT_str((char *)buf);
	ctx->error_no = Get_AT_errno(buf);
	/* 有级联命令待处理 */
	if (isResult && getAtRxCmdListNum(&ctx->rxCombCmdList) > 0)
	{
		if (ctx->error_no != XY_OK)
		{
			/* 级联命令出错,释放所有待处理级联命令 */
			freeAtRxCmdList(&ctx->rxCombCmdList);
			xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps][at_send_to_tty]combine at error:%d", ctx->error_no);
		}
		else
		{
			at_cmd_cache_t *command = popAtRxCmdFromHead(&ctx->rxCombCmdList);
			if (command != NULL)
			{
				/* 裁剪平台命令可能存在的\r\nOK\r\n */
				removeAtTailOkAndOutput(buf, size, ctx->fd);
				/* 前一个命令返回OK, 将下一个级联命令输出 */
				ctx->state |= RCV_REQ_NOW;
				xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps][at_send_to_tty]pop combine at:%s", command->data);
				send_msg_2_atctl(AT_MSG_RCV_STR_FROM_FARPS, command->data, command->len, command->fd);
				xy_free(command->data);
				xy_free(command);
				osMutexRelease(ctx->rxCmd_mux);
				return 1;
			}
		}
	}

    at_posix_write(ctx->fd, (uint8_t *)buf, size);

	//返回OK或者错误,以及接收到透传模式退出字符的时候处理！
	if (result_directly || isResult)
	{
		reset_ctx(ctx);
		/* at urc缓存上报 */
		at_send_urc_cache(ctx->fd);
		/* 检测at发送缓存队列里是否有命令待发送 */
        if (getAtRxCmdListNum(&ctx->rxCacheCmdList) > 0)
        {
            at_cmd_cache_t *command = popAtRxCmdFromHead(&ctx->rxCacheCmdList);
			ctx->state |= RCV_REQ_NOW;
			char new_prefix[10] = {0};
			memcpy(new_prefix, command->data, 9);
			xy_printf(0, PLATFORM_AP, INFO_LOG, "[AtFarps][at_send_to_tty]tty:%d at cmd:%s out cache to at_ctl", ctx->fd, new_prefix);
			send_msg_2_atctl(AT_MSG_RCV_STR_FROM_FARPS, command->data, command->len, command->fd);
			xy_free(command->data);
			xy_free(command);
		}
	}

    osMutexRelease(ctx->rxCmd_mux);
	return 1;
}

bool at_recv_from_lpuart(char *buf, size_t data_len)
{
	at_context_t *ctx = &g_at_tty_ctx[AT_LPUART_FD];

	/* 透传模式的数据拦截处理 */
    if (atPassthStateDataInput(AT_LPUART_FD, buf, data_len) == XY_OK)
		return true;
	
#if (XY_GNSS1)   /*AT命令与GNSS信令包，仅靠首字符来区分，且同时只能处理一条请求*/
	extern int RcvGnssStreamFromATtty(int srcfd, char *data, uint32_t data_len);
	if(ctx->g_have_rcved_len == 0 && RcvGnssStreamFromATtty(AT_LPUART_FD, buf, data_len))
	{
		return true;
	}
#endif 
#if (XY_GNSS2)

#endif

	/* 以\r作为尾部特征，进而后续尾部的\n要丢弃。透传模式退出后，也可能收到CTRL+Z的上报，需丢弃 */
	if (ctx->g_have_rcved_len == 0 && (*buf == '\n' || *buf == PASSTH_CTRLZ))
	{		
		if (data_len < 3)
		{
			/* 数据小于3直接丢弃 */
        	ATRECV_NET_LOG("first char:%d and len<3,drop!", *buf);
			if (data_len == 1)
			{
				xy_printf(0, PLATFORM_AP, WARN_LOG, "[AtFarps][at_recv_from_lpuart]first char:%d and len < 3, drop!", *buf);
				debug_log_print("[AtFarps][at_recv_from_lpuart]first char:%d and len < 3, drop!", *buf);
			}
			else
			{
				xy_printf(0, PLATFORM_AP, WARN_LOG, "[AtFarps][at_recv_from_lpuart]first char:%d second char:%d, and len < 3, drop!", *buf, *(buf + 1));
				debug_log_print("[AtFarps][at_recv_from_lpuart]first char:%d second char:%d, and len < 3, drop!", *buf, *(buf + 1));
			}
            return false;
		}
		else
		{
			buf++;
			data_len--;
		}
	}
	
    if (at_farps_data_realloc(ctx, buf, data_len) != XY_OK)
    {
        goto END_PROC;
    }
 
    if (trim_and_find_end_symbol(ctx))
	{
        /* 检测是否包含AT前缀 */
        if (isValidATCommand(ctx) == false)
        {
        	/*脏数据，添加接收延迟锁*/
        	startAtLpuartLockAndTimer(5);
            goto END_PROC;
        }
        /* release at lpuart sleep lock */
        stopAtLpuartLockAndTimer();
        /* Add delay sleep lock */
        at_delaylock_act();

        if (get_ate_mode() == 1)
        {
            /* 回显输出，包含末尾的\r或者\r\n */
            at_posix_write(ctx->fd, ctx->g_farps_rcv_mem, ctx->g_have_rcved_len);
        }
        single_atcmd_proc(ctx);
    }
	else
	{
        /* Add at lpuart sleep lock */
        startAtLpuartLockAndTimer(5);
        return true;
	}

END_PROC:
    resetRecvAtContext(ctx);
    return true;
}

bool at_send_to_lpuart(void *buf, size_t size)
{
	return at_send_to_tty(AT_LPUART_FD, buf, size, false);
}

bool at_recv_from_cmux1(char *buf, size_t data_len)
{
#if XY_CMUX
	at_context_t *ctx = &g_at_tty_ctx[AT_CMUX1_FD];

    /* 透传模式的数据拦截处理 */
    if (atPassthStateDataInput(AT_CMUX1_FD, buf, data_len) == XY_OK)
		return true;

	/* 以\r作为尾部特征，进而后续尾部的\n要丢弃。透传模式退出后，也可能收到CTRL+Z的上报，需丢弃 */
	if (ctx->g_have_rcved_len == 0 && (*buf == '\n' || *buf == PASSTH_CTRLZ))
	{		
		if (data_len < 3)
		{
			/* 数据小于3直接丢弃 */
			ATRECV_NET_LOG("first char:%d and len<3,drop!", *buf);	
            xy_printf(0, PLATFORM_AP, WARN_LOG, "[AtFarps]first char:%d and len < 3, drop!", *buf);
            return false;
		}
		else
		{
			buf++;
			data_len--;
		}
	}

    if (at_farps_data_realloc(ctx, buf, data_len) != XY_OK)
    {
        goto END_PROC;
    }

    if (trim_and_find_end_symbol(ctx))
	{
        /* 检测是否包含AT前缀 */
        if (isValidATCommand(ctx) == false)
        {
        	/*脏数据，添加接收延迟锁*/
        	startAtLpuartLockAndTimer(5);
            goto END_PROC;
        }
        /* release at lpuart sleep lock */
        stopAtLpuartLockAndTimer();
        /* Add delay sleep lock */
        at_delaylock_act();

        if (get_ate_mode() == 1)
        {
            /* 回显输出，包含末尾的\r或者\r\n */
            at_posix_write(ctx->fd, ctx->g_farps_rcv_mem, ctx->g_have_rcved_len);
        }
        single_atcmd_proc(ctx);
    }
    else
	{
        /* Add at lpuart sleep lock */
        startAtLpuartLockAndTimer(5);
        return true;
	}

END_PROC:
    resetRecvAtContext(ctx);
#else
	UNUSED_ARG(buf);
	UNUSED_ARG(data_len);
#endif /* XY_CMUX */
    return true;
}

bool at_recv_from_cmux2(char *buf, size_t data_len)
{
#if XY_CMUX
	at_context_t *ctx = &g_at_tty_ctx[AT_CMUX2_FD];

    /* 透传模式的数据拦截处理 */
    if (atPassthStateDataInput(AT_CMUX2_FD, buf, data_len) == XY_OK)
		return true;

	/* 以\r作为尾部特征，进而后续尾部的\n要丢弃。透传模式退出后，也可能收到CTRL+Z的上报，需丢弃 */
	if (ctx->g_have_rcved_len == 0 && (*buf == '\n' || *buf == PASSTH_CTRLZ))
	{		
		if (data_len < 3)
		{
			/* 数据小于3直接丢弃 */
			ATRECV_NET_LOG("first char:%d and len<3,drop!", *buf);			
            xy_printf(0, PLATFORM_AP, WARN_LOG, "first char:%d and len < 3, drop!", *buf);
            return false;
		}
		else
		{
			buf++;
			data_len--;
		}
	}

    if (at_farps_data_realloc(ctx, buf, data_len) != XY_OK)
    {
        goto END_PROC;
    }

    if (trim_and_find_end_symbol(ctx))
	{
        /* 检测是否包含AT前缀 */
        if (isValidATCommand(ctx) == false)
        {
        	/*脏数据，添加接收延迟锁*/
        	startAtLpuartLockAndTimer(5);
            goto END_PROC;
        }
        /* release at lpuart sleep lock */
        stopAtLpuartLockAndTimer();
        /* Add delay sleep lock */
        at_delaylock_act();

        if (get_ate_mode() == 1)
        {
            /* 回显输出，包含末尾的\r或者\r\n */
            at_posix_write(ctx->fd, ctx->g_farps_rcv_mem, ctx->g_have_rcved_len);
        }
        single_atcmd_proc(ctx);
    }
    else
	{	
        /* Add at lpuart sleep lock */
        startAtLpuartLockAndTimer(5);
        return true;
	}

END_PROC:
    resetRecvAtContext(ctx);
#else
	UNUSED_ARG(buf);
	UNUSED_ARG(data_len);
#endif /* XY_CMUX */
    return true;
}

bool at_send_to_cmux1(void *buf, size_t size)
{
#if XY_CMUX
	return at_send_to_tty(AT_CMUX1_FD, buf, size, false);
#else
	UNUSED_ARG(buf);
	UNUSED_ARG(size);
	return true;
#endif /* XY_CMUX */
}

bool at_send_to_cmux2(void *buf, size_t size)
{
#if XY_CMUX
	return at_send_to_tty(AT_CMUX2_FD, buf, size, false);
#else
	UNUSED_ARG(buf);
	UNUSED_ARG(size);
	return true;
#endif /* XY_CMUX */
}

#if LOG_AT
bool at_recv_from_log(char *buf, size_t data_len)
{
	at_context_t *ctx = &g_at_tty_ctx[AT_LOG_FD];

	/* 以\r作为尾部特征，进而后续尾部的\n要丢弃。另外有些外部at工具命令结束符为\r\n\0, 因此也需要考虑\0的情况 */
	if (ctx->g_have_rcved_len == 0 && *buf == '\n')
	{		
		if (data_len < 3)
		{
			/* 数据小于3直接丢弃 */
			return false;
		}
		else
		{
			buf++;
			data_len--;
		}
	}

    if (at_farps_data_realloc(ctx, buf, data_len) != XY_OK)
        goto END_PROC;

    if (trim_and_find_end_symbol(ctx))
	{
        /* 增加延迟锁 */
        at_delaylock_act();
        /* 检测是否包含AT前缀 */
        if (isValidATCommand(ctx) == false)
        {
            goto END_PROC;
        }
        if (get_ate_mode() == 1)
        {
            /* 回显输出，包含末尾的\r或者\r\n */
            at_posix_write(ctx->fd, ctx->g_farps_rcv_mem, ctx->g_have_rcved_len);
        }
        single_atcmd_proc(ctx);
    }
    else
	{	
        return true;
	}

END_PROC:
    resetRecvAtContext(ctx);
    return true;
}

bool at_send_to_log(void *buf, size_t size)
{
    return at_send_to_tty(AT_LOG_FD, buf, size, false);
}
#endif /* LOG_AT */

bool at_recv_from_usb(char *buf, size_t data_len)
{
	at_context_t *ctx = &g_at_tty_ctx[AT_USB_FD];

    /* 透传模式的数据拦截处理 */
    if (atPassthStateDataInput(AT_USB_FD, buf, data_len) == XY_OK)	
		return true;

#if (XY_GNSS1)   /*AT命令与GNSS信令包，仅靠首字符来区分，且同时只能处理一条请求*/
	extern int RcvGnssStreamFromATtty(int srcfd, char *data, uint32_t data_len);
	if(ctx->g_have_rcved_len == 0 && RcvGnssStreamFromATtty(AT_USB_FD, buf, data_len))
	{
		return true;
	}
#endif

	/* 以\r作为尾部特征，进而后续尾部的\n要丢弃。透传模式退出后，也可能收到CTRL+Z的上报，需丢弃 */
	if (ctx->g_have_rcved_len == 0 && (*buf == '\n' || *buf == PASSTH_CTRLZ))
	{		
		if (data_len < 3)
		{
			/* 数据小于3直接丢弃 */
			ATRECV_NET_LOG("first char:%d and len<3,drop!", *buf);			
            xy_printf(0, PLATFORM_AP, WARN_LOG, "first char:%d and len < 3, drop!", *buf);
			return false;
		}
		else
		{
			buf++;
			data_len--;
		}
	}

    if (at_farps_data_realloc(ctx, buf, data_len) != XY_OK)
        goto END_PROC;

    if (trim_and_find_end_symbol(ctx))
	{
        /* 检测是否包含AT前缀 */
        if (isValidATCommand(ctx) == false)
        {
            goto END_PROC;
        }
        if (get_ate_mode() == 1)
        {
            /* 回显输出，包含末尾的\r或者\r\n */
            at_posix_write(ctx->fd, ctx->g_farps_rcv_mem, ctx->g_have_rcved_len);
        }
        single_atcmd_proc(ctx);
    }
	else
	{	
        return true;
	}

END_PROC:
    resetRecvAtContext(ctx);
    return true;
}

bool at_send_to_usb(void *buf, size_t size)
{
	return at_send_to_tty(AT_USB_FD, buf, size, false);
}

bool at_recv_from_usb_modem(char *buf, size_t data_len)
{
	at_context_t *ctx = &g_at_tty_ctx[AT_USB_MODEM_FD];

    /* 透传模式的数据拦截处理 */
    if (atPassthStateDataInput(AT_USB_MODEM_FD, buf, data_len) == XY_OK)	
		return true;

	/* 以\r作为尾部特征，进而后续尾部的\n要丢弃。透传模式退出后，也可能收到CTRL+Z的上报，需丢弃 */
	if (ctx->g_have_rcved_len == 0 && (*buf == '\n' || *buf == PASSTH_CTRLZ))
	{		
		if (data_len < 3)
		{
			/* 数据小于3直接丢弃 */
			ATRECV_NET_LOG("first char:%d and len<3,drop!", *buf);			
            xy_printf(0, PLATFORM_AP, WARN_LOG, "[at_recv_from_usb_modem]first char:%d and len < 3, drop!", *buf);
			return false;
		}
		else
		{
			buf++;
			data_len--;
		}
	}

    if (at_farps_data_realloc(ctx, buf, data_len) != XY_OK)
        goto END_PROC;

    if (trim_and_find_end_symbol(ctx))
	{
        /* 检测是否包含AT前缀 */
        if (isValidATCommand(ctx) == false)
        {
            goto END_PROC;
        }
        if (get_ate_mode() == 1)
        {
            /* 回显输出，包含末尾的\r或者\r\n */
            at_posix_write(ctx->fd, ctx->g_farps_rcv_mem, ctx->g_have_rcved_len);
        }
        single_atcmd_proc(ctx);
    }
	else
	{	
        return true;
	}

END_PROC:
    resetRecvAtContext(ctx);
    return true;
}

bool at_send_to_usb_modem(void *buf, size_t size)
{
	return at_send_to_tty(AT_USB_MODEM_FD, buf, size, false);
}

#if PS_TEST_MODE
extern void ps_test_recv_at(uint8_t* pURC_data, uint16_t ulDataLen);

bool at_recv_from_ps_test(char *buf, size_t data_len)
{
	at_context_t *ctx = &g_at_tty_ctx[AT_PS_TEST_FD];

    /* 透传模式的数据拦截处理 */
    if (atPassthStateDataInput(AT_PS_TEST_FD, buf, data_len) == XY_OK)	
		return true;

	/* 以\r作为尾部特征，进而后续尾部的\n要丢弃。另外有些外部at工具命令结束符为\r\n\0, 因此也需要考虑\0的情况 */
	if (ctx->g_have_rcved_len == 0 && *buf == '\n')
	{		
		if (data_len < 3)
		{
			/* 数据小于3直接丢弃 */
			return false;
		}
		else
		{
			buf++;
			data_len--;
		}
	}

    if (at_farps_data_realloc(ctx, buf, data_len) != XY_OK)
        goto END_PROC;

    if (trim_and_find_end_symbol(ctx))
    {
        /* 检测是否包含AT前缀 */
        if (isValidATCommand(ctx) == false)
        {
            goto END_PROC;
        }
        single_atcmd_proc(ctx);
    }
    else
	{	
        /* PS测试命令由软件发送，不需要大喘气定时器，正常情况下不会走到这里 */
        return true;
	}

END_PROC:
    resetRecvAtContext(ctx);
    return true;
}

bool at_send_to_ps_test(void *buf, size_t size)
{
	xy_assert(osCoreGetState() != osCoreInCritical); //不能在锁中断状态下调用该接口！
    at_context_t *ctx = &g_at_tty_ctx[AT_PS_TEST_FD];
    char *repStr = NULL;

    //返回OK或者错误,以及接收到透传模式退出字符的时候处理！
	if (Is_Result_AT_str((char *)buf))
	{
		//update err no
		ctx->error_no = Get_AT_errno(buf);
		reset_ctx(ctx);
	}

    // xy_printf(0, PLATFORM_AP, INFO_LOG, "at[%d] send_to_ps_test:%s", srcFd, (char *)buf);

    ps_test_recv_at(buf,size);
	return 1;
}
#endif /* PS_TEST_MODE */

#if UART_AT
bool at_recv_from_uart(char *buf, size_t data_len)
{
	at_context_t *ctx = &g_at_tty_ctx[AT_UART_FD];

	/* 透传模式的数据拦截处理 */
    if (atPassthStateDataInput(AT_UART_FD, buf, data_len) == XY_OK)
		return true;
	
#if (XY_GNSS1)   /*AT命令与GNSS信令包，仅靠首字符来区分，且同时只能处理一条请求*/
	extern int RcvGnssStreamFromATtty(int srcfd, char *data, uint32_t data_len);
	if(ctx->g_have_rcved_len == 0 && RcvGnssStreamFromATtty(AT_UART_FD, buf, data_len))
	{
		return true;
	}
#endif 
#if (XY_GNSS2)

#endif

	/* 以\r作为尾部特征，进而后续尾部的\n要丢弃。透传模式退出后，也可能收到CTRL+Z的上报，需丢弃 */
	if (ctx->g_have_rcved_len == 0 && (*buf == '\n' || *buf == PASSTH_CTRLZ))
	{		
		if (data_len < 3)
		{
			/* 数据小于3直接丢弃 */
        	ATRECV_NET_LOG("first char:%d and len<3,drop!", *buf);				
            xy_printf(0, PLATFORM_AP, WARN_LOG, "[AtFarps][at_recv_from_uart]first char:%d and len < 3, drop!", *buf);
			debug_log_print("[at_recv_from_uart]first char:%d and len < 3, drop!\n", *buf);
            return false;
		}
		else
		{
			buf++;
			data_len--;
		}
	}
	
    if (at_farps_data_realloc(ctx, buf, data_len) != XY_OK)
    {
        goto END_PROC;
    }
 
    if (trim_and_find_end_symbol(ctx))
	{
        /* 检测是否包含AT前缀 */
        if (isValidATCommand(ctx) == false)
        {
        	/*脏数据，添加接收延迟锁*/
        	startAtLpuartLockAndTimer(5);
            goto END_PROC;
        }
        /* release at lpuart sleep lock */
        stopAtLpuartLockAndTimer();
        /* Add delay sleep lock */
        at_delaylock_act();

        if (get_ate_mode() == 1)
        {
            /* 回显输出，包含末尾的\r或者\r\n */
            at_posix_write(ctx->fd, ctx->g_farps_rcv_mem, ctx->g_have_rcved_len);
        }
        single_atcmd_proc(ctx);
    }
	else
	{
        /* Add at lpuart sleep lock */
        startAtLpuartLockAndTimer(5);
        return true;
	}

END_PROC:
    resetRecvAtContext(ctx);
    return true;
}

bool at_send_to_uart(void *buf, size_t size)
{
	return at_send_to_tty(AT_UART_FD, buf, size, false);
}

#endif /* UART_AT */

