/**********************************************************************/
/**
 * @file doxygen_group_def.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-02-27
 * @version V1.0
 * @brief 仅供Doxygen工具用。不需要被include，只要doxygen能找到即可
 **********************************************************************/

/**
 * @defgroup interface_adapter 接口适配
 * 提供模组统一雅迅接口。封装模组SDK接口，并向功能组件或业务层提供统一雅迅接口
 */
/**
 * @defgroup common_modules 通用功能组件
 * 提供功能封装的统一雅迅接口。调用适配接口，并向业务层提供统一雅迅接口
 */
/**
 * @defgroup app_modules 业务组件
 * 实现业务需求。调用适配接口或功能组件接口，完成业务逻辑需求
 */

/**
 * @defgroup cm_fw 主框架功能模块
 * 负责程序主入口加载及框架初始化
 * @ingroup common_modules
 */

/**
 * @defgroup cm_core 核心功能模块
 * 负责消息处理
 * @ingroup common_modules
 */

/**
 * @defgroup cm_storage 数据存储功能模块
 * 负责消息处理
 * @ingroup common_modules
 */

/**
 * @defgroup yx_timer 业务通用定时器模块
 * 负责业务通用定时器管理
 * @ingroup common_modules
 */

/**
 * @defgroup yx_debug 调试信息输出模块
 * 负责调试信息输出管理
 * @ingroup common_modules
 */

/**
 * @defgroup yx_utils 公共工具类模块
 * 负责公共工具类管理
 * @ingroup common_modules
 */

/**
 * @defgroup system 系统通用功能模块
 * 负责系统通用功能封装
 * @ingroup common_modules
 */

/**
 * @defgroup gsm 网络控制功能模块
 * 负责gsm网络功能封装
 * @ingroup common_modules
 */

/**
 * @defgroup cm_comm 通信功能模块
 * 负责通信链路、数据流处理、交互流程等
 * @ingroup common_modules
 */

/**
 * @defgroup cm_gnss 定位服务功能模块
 * 负责定位服务，北斗定位、基站定位、AGPS等
 * @ingroup common_modules
 */

/**
 * @defgroup net 网络控制功能模块
 * 负责(gprs,tcp)网络功能封装
 * @ingroup common_modules
 */
/**
 * @defgroup filesys 应用文件管理模块
 * 负责对应用数据区文件进行管理
 * @ingroup common_modules
 */

/**
 * @defgroup yx_wifi WiFi扫描模块
 * 负责WiFi扫描管理
 * @ingroup common_modules
 */

/**
 * @defgroup yx_sensor_hit 传感器-加速度模块
 * 负责传感器-加速度模块管理
 * @ingroup common_modules
 */

/**
 * @defgroup cm_dsc 数据共享中心模块
 * 负责各模块的共享数据管理
 * @ingroup common_modules
 */

  /**
 * @defgroup yx_pe_io PE-IO模块
 * 负责IO控制模块管理
 * @ingroup common_modules
 */

/**
 * @defgroup ia_power 电源控制模块
 * 提供电源控制相关的模组接口适配
 * @ingroup interface_adapter
 */

/**
 * @defgroup ia_sys 系统控制模块
 * 提供系统控制相关的模组接口适配
 * @ingroup interface_adapter
 */

/**
 * @defgroup ia_nad 网络接入设备模块
 * 提供网络接入相关的模组接口适配
 * @ingroup interface_adapter
 */

/**
 * @defgroup ia_i2c I2C控制模块
 * 提供I2C相关的模组接口适配
 * @ingroup interface_adapter
 */

/**
 * @defgroup ia_wifi wifi扫描控制模块
 * 提供wifi扫描相关的模组接口适配
 * @ingroup interface_adapter
 */

/**
 * @defgroup ia_gprs 网络接入设备模块
 * 提供GPRS相关的模组接口适配
 * @ingroup interface_adapter
 */

/**
 * @defgroup ia_fota 差分升级和应用全量升级模块
 * 提供差分升级和应用全量升级相关的模组接口适配
 * @ingroup interface_adapter
 */

/**
 * @defgroup ia_crypto 加解密模块
 * 提供加密解密相关的接口适配
 * @ingroup interface_adapter
 */

/**
 * @defgroup ia_adc ADC模块
 * 提供ADC相关的接口适配
 * @ingroup interface_adapter
 */

/**
 * @defgroup ia_gpio GPIO模块
 * 提供GPIO相关的接口适配
 * @ingroup interface_adapter
 */

/**
 * @defgroup ia_fs FS模块
 * 提供fs相关的接口适配
 * @ingroup interface_adapter
 */

/**
 * @defgroup ia_http HTTP模块
 * 提供HTTP相关的接口适配
 * @ingroup interface_adapter
 */

/**
 * @defgroup ia_lbs 基站定位模块
 * 提供基站定位相关的接口适配
 * @ingroup interface_adapter
 */

 /**
 * @defgroup ia_mqtt MQTT模块
 * 提供MQTT相关的接口适配
 * @ingroup interface_adapter
 */

/**
 * @defgroup ia_can CAN模块
 * 提供CAN相关的接口适配
 * @ingroup interface_adapter
 */