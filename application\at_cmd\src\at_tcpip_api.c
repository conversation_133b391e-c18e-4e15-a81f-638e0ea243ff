/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "at_tcpip_api.h"
#include "at_com.h"
#include "at_error.h"
#include "lwip/netdb.h"
#include "lwip/sockets.h"
#include "net_adapt.h"
#include "oss_nv.h"
#include "ps_netif_api.h"
#include "xy_at_api.h"
#include "xy_defwan_api.h"
#include "xy_tcpip_api.h"
#include "xy_wan_api.h"
#include "gw_netif_api.h"
#include "main_proxy.h"
#include "app_basic_config.h"
#include "gw_netif_api.h"

/*******************************************************************************
 *							  Macro definitions								   *
 ******************************************************************************/

/*******************************************************************************
 *						  Local variable definitions						   *
 ******************************************************************************/
osThreadId_t dns_thd_handle = NULL;
osThreadId_t ntp_thd_handle = NULL;

const char *ntp_urc[NTP_MAX] = {"QNTP", "CMNTP", "MNTP"};

/*******************************************************************************
 *						function implementations						       *
 ******************************************************************************/
int set_ipv4_dns_server(uint8_t cid, char *pre_dns, char *sec_dns)
{
	if (dns_thd_handle != NULL)
	{
		/* 有dns查询正在执行不允许操作 */
		xy_printf(0, PLATFORM_AP, WARN_LOG, "ipv4 dns_config but dns query is executing");
		return XY_Err_NotAllowed;
	}

	ip_addr_t dns_t = {0};
	if (pre_dns == NULL)
	{
		int uicc_type = -1;
		xy_get_UICC_TYPE(&uicc_type);

		//若SIM卡PLMN为46011、46003、46005（电信），则 ipv4 默认值为"*********"，其余ipv4默认值为"***************"
		if (uicc_type == 1)
			ipaddr_aton("*********", &dns_t);
		else
			ipaddr_aton("***************", &dns_t);
		xy_dns_set2(cid, &dns_t, 0);
	}
	else if (xy_IpAddr_Check(pre_dns, IPV4_TYPE) && (ipaddr_aton(pre_dns, &dns_t)))
	{
		xy_dns_set2(cid, &dns_t, 0);
	}
	else
	{
		xy_printf(0, PLATFORM_AP, WARN_LOG, "set ipv4 pridns:%s err", pre_dns);
		return XY_Err_Parameter;
	}

	if (sec_dns == NULL)
	{
		ipaddr_aton("************", &dns_t);
		xy_dns_set2(cid, &dns_t, 1);
	}
	else if (xy_IpAddr_Check(sec_dns, IPV4_TYPE) && (ipaddr_aton(sec_dns, &dns_t)))
	{
		xy_dns_set2(cid, &dns_t, 1);
	}
	else
	{
		xy_printf(0, PLATFORM_AP, WARN_LOG, "set ipv4 secdns:%s err", sec_dns);
		return XY_Err_Parameter;
	}
	return XY_OK;
}

int set_ipv6_dns_server(uint8_t cid, char *pre_dns, char *sec_dns)
{
	if (dns_thd_handle != NULL)
	{
		/* 有dns查询正在执行不允许操作 */
		xy_printf(0, PLATFORM_AP, WARN_LOG, "ipv6 dns_config but dns query is executing");
		return XY_Err_NotAllowed;
	}

	ip_addr_t dns_t = {0};
	if (pre_dns == NULL)
	{
		ipaddr_aton("2400:3200::1", &dns_t);
		xy_dns_set2(cid, &dns_t, 2);
	}
	else if (xy_IpAddr_Check(pre_dns, IPV6_TYPE) && (ipaddr_aton(pre_dns, &dns_t)))
	{
		xy_dns_set2(cid, &dns_t, 2);
	}
	else
	{
		xy_printf(0, PLATFORM_AP, WARN_LOG, "set ipv6 pridns:%s err", pre_dns);
		return XY_Err_Parameter;
	}

	if (sec_dns == NULL)
	{
		ipaddr_aton("2001:4860:4860::8888", &dns_t);
		xy_dns_set2(cid, &dns_t, 3);
	}
	else if (xy_IpAddr_Check(sec_dns, IPV6_TYPE) && (ipaddr_aton(sec_dns, &dns_t)))
	{
		xy_dns_set2(cid, &dns_t, 3);
	}
	else
	{
		xy_printf(0, PLATFORM_AP, WARN_LOG, "set ipv6 secdns:%s err", sec_dns);
		return XY_Err_Parameter;
	}

	return XY_OK;
}

int at_dns_config(dns_cfg_param_t *arg)
{
	xy_assert(arg != NULL);

	if (dns_thd_handle != NULL)
	{
		/* 有dns查询正在执行不允许操作 */
		xy_printf(0, PLATFORM_AP, WARN_LOG, "at_dns_config but dns query is executing");     
		return XY_Err_NotAllowed;
	}    

	// 主DNS可为NULL
	if (arg->pridns != NULL)
	{
		if (!xy_dns_set(arg->contextID, arg->pridns, 0))
		{
			xy_printf(0, PLATFORM_AP, WARN_LOG, "at_dns_config set pridns:%s err", arg->pridns);
			return XY_Err_Parameter;
		}
	} 

	// 辅DNS可为NULL
	if (arg->secdns != NULL)
	{
		if (!xy_dns_set(arg->contextID, arg->secdns, 1))
		{
			xy_printf(0, PLATFORM_AP, WARN_LOG, "at_dns_config set secdns:%s err", arg->secdns);
			return XY_Err_NotAllowed;
		}
	}

	return XY_OK;
}
extern void get_zone_str(char *zone);
void query_ntp_task(void *arg)
{
	xy_assert (arg != NULL);

	ntp_query_param_t *param = (ntp_query_param_t *)arg;
	char *prsp = xy_malloc(128);
	int ret = xy_query_ntp_sync(param);

	if (ret != XY_OK)
	{
		if (param->query_type == MNTP)
		{
			if (ret == XY_Err_DnsFail)
				ret = 1;
			else
				ret = 2;
		}
		sprintf(prsp, "\r\n+%s: %d\r\n", ntp_urc[param->query_type], ret);
	}
	else
	{
		char zone[4] = {0};
		if (1 == param->update)
			xy_set_GMT_time(&param->walltime);

		get_zone_str(zone);
		if (param->query_type == MNTP)
		{
			sprintf(prsp, "\r\n+%s: 0,\"%02u/%02u/%02u,%02u:%02u:%02u%s\"\r\n", ntp_urc[param->query_type], param->walltime.tm_year % 100, param->walltime.tm_mon, param->walltime.tm_mday,
					param->walltime.tm_hour, param->walltime.tm_min, param->walltime.tm_sec, zone);
		}
		else
		{
			// +QNTP: 0,"2019/09/09,01:32:42+32"
			sprintf(prsp, "\r\n+%s: 0,\"%04u/%02u/%02u,%02u:%02u:%02u%s\"\r\n", ntp_urc[param->query_type], param->walltime.tm_year, param->walltime.tm_mon, param->walltime.tm_mday,
					param->walltime.tm_hour, param->walltime.tm_min, param->walltime.tm_sec, zone);
		}
	}
#if !USR_CUSTOM8
	appAtURC(param->atHandle, prsp);
#endif
	xy_free(prsp);
	/* 异步线程需要释放内存 */
	if (param->host != NULL)
		xy_free(param->host);
	xy_free(param);

	ntp_thd_handle = NULL;
	osThreadExit();
}

int at_query_ntp_async(ntp_query_param_t *arg)
{
	if (arg->host != NULL && strlen(arg->host) >= DNS_MAX_NAME_LENGTH)
		return XY_Err_Parameter;

    // 有ntp查询正在执行不允许操作
	if (ntp_thd_handle != NULL)
		return XY_Err_NotAllowed;

	if (!xy_Wan_is_ok(arg->contextID))
		return XY_Err_Netif_NotReady;

	// query_dns_task处理完成释放
	ntp_query_param_t *param = (ntp_query_param_t *)xy_malloc(sizeof(ntp_query_param_t));
	memcpy(param, arg, sizeof(ntp_query_param_t));
	// host参数为NULL时，用默认的NTP查询服务器域名
	if (arg->host != NULL && strlen(arg->host) > 0)
	{
		param->host = xy_malloc(strlen(arg->host) + 1);
		strcpy(param->host, arg->host);
	}
	else
		param->host = NULL;

	osThreadAttr_t thread_attr = {0};
	thread_attr.name 		= "ntpQ";
	thread_attr.priority 	= osPriorityNormal;
	thread_attr.stack_size 	= 0x800;
	ntp_thd_handle = osThreadNew((osThreadFunc_t)(query_ntp_task), param, &thread_attr);
	osThreadSetLowPowerFlag(ntp_thd_handle, osWfiPermit);
	return XY_OK;
}


/*AT命令定制功能，用于UE终端的IP地址分配及子网网关的激活去活*/
int at_usb_netdev_ctl_async(usb_netdev_ctl_msg_t *msg)
{
	return send_msg_2_proxy(PROXY_MSG_USB_NETDEV_CTL, (void *)msg, sizeof(usb_netdev_ctl_msg_t), osWaitForever);
}


/* 此URC接口的修改对其余的版本无影响，不用区分版本 */
void net_dev_status_urc_report(int status, void *urc_info)
{
#if ATCTL_EN
	(void)urc_info;
	
	/* 网关状态变化URC上报在USB网卡连接过程中只调用一次 */
	static uint8_t urcReportFlag = 0;

	if (status)
	{
		/* 网关状态变化URC上报开关打开时才进行开机主动上报 */
		if (urcReportFlag == 0 && 
			g_app_basic_cfg.usbnet_sta_urc == 1 && g_softap_fac_nv->gw_act_mode < 2)
		{
			urcReportFlag = 1;
			sysAtURC("\r\n+QNETDEVSTATUS: 1\r\n", strlen("\r\n+QNETDEVSTATUS: 1\r\n"));
		}
	}	
	else
	{
		/* 网关状态变化URC上报 */
		if (urcReportFlag == 1 && g_softap_fac_nv->gw_act_mode < 2)
		{
			urcReportFlag = 0;
			sysAtURC("\r\n+QNETDEVSTATUS: 0\r\n", strlen("\r\n+QNETDEVSTATUS: 0\r\n"));
		}
	}
#endif // ATCTL_EN
}
