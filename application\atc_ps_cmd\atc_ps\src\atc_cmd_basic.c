
/*******************************************************************************
@(#)Copyright(C)2016,HuaChang Technology (Dalian) Co,. LTD.
File name   : atc_cmd_basic.c
Description : 
Function List:
History:
1. Dep2_066    2016.12.20  Create
*******************************************************************************/
#include <ctype.h>
#include "atc_ps.h"



/*******************************************************************************
  MODULE    : ATC_CFUN_LTE_Command
  FORMAT    : +CFUN=<fun>[,<rst>]
  FUNCTION  : <fun>:
                0: minimum functionality
                1: full functionality
                4: disable (turn off) both MT transmit and receive RF circuits
                5: fast into minimum functionality
              <rst>:
                0:do not reset the MT before setting it to <fun> power level
                1:reset the MT before setting it to <fun> power level
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CFUN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CFUN_PARAMETER* ptEvent_Cfun = (ST_ATC_CFUN_PARAMETER*)pEventBuffer;

    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if (AtcAp_CmdParamPrase(D_ATC_CMD_CFUN_FORMAT, pCommandBuffer, &ptEvent_Cfun->ucFun, NULL, &ptEvent_Cfun->ucRst) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Cfun->usEvent = D_ATC_EVENT_CFUN;

        if(0 == ptEvent_Cfun->ucFun && g_AtcApInfo.ucUserAtFlg == ATC_AP_FALSE)
        {
            g_AtcApInfo.usAtRspOKFlg = ATC_AP_TRUE;
        }
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Cfun->usEvent = D_ATC_EVENT_CFUN_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

/*******************************************************************************
  MODULE    : ATC_CGATT_LTE_Command
  FORMAT    : AT+CGATT=<state>
  FUNCTION  : 
  <state>: integer type; indicates the state of PS attachment
    0   detached
    1   attached
  NOTE      :
  HISTORY   :

*******************************************************************************/
unsigned char ATC_CGATT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Cgatt = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CGATT_FORMAT, pCommandBuffer, &ptEvent_Cgatt->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Cgatt->usEvent = D_ATC_EVENT_CGATT;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Cgatt->usEvent = D_ATC_EVENT_CGATT_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CGACT_LTE_Command
  FORMAT    : AT+CGACT=[<state>[,<cid>[,<cid>[,...]]]]
  FUNCTION  : <state>: integer type; indicates the state of PDP context activation. The default value is manufacturer specific.
                0   deactivated
                1   activated
              <cid>: integer type; specifies a particular PDP context definition (see the +CGDCONT and +CGDSCONT commands).
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGACT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char i = 0;
    ST_ATC_CGACT_PARAMETER*  ptEvent_Cgact = (ST_ATC_CGACT_PARAMETER*)pEventBuffer;
    unsigned char ucOffset = 0;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CGACT_FORMAT, pCommandBuffer, &(ptEvent_Cgact->ucState)) == ATC_AP_FALSE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        for(i = 0; i <= D_MAX_CNT_CID; i++)
        {
            if(AtcAp_FindNextComma(pCommandBuffer + ucOffset) == ATC_FALSE)
            {
                break;
            }
            else if(D_MAX_CNT_CID == i)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ucOffset += AtcAp_FindNextComma(pCommandBuffer + ucOffset);
            if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer + ucOffset,&(ptEvent_Cgact->aucCid[i])) == ATC_AP_FALSE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ptEvent_Cgact->ucCidNum++;
        }
        ptEvent_Cgact->usEvent = D_ATC_EVENT_CGACT;
#if VER_CM
        if(ptEvent_Cgact->ucState == 1)
        {
            xy_set_cid_limit_flg(0);
        }
#endif
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Cgact->usEvent = D_ATC_EVENT_CGACT_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}
/*******************************************************************************
  MODULE    : ATC_CGDCONT_LTE_Command
  FORMAT    : AT+CGDCONT=<cid>[,<PDP_type>[,<APN>[,<PDP_addr>[,<d_comp>[,<h_comp>[,[,[,[,[,<NSLPI>[,<securePCO>]]]]]]]]]]]
  FUNCTION  : <cid>:        integer type;(0-15)
              <PDP_type>:   string type; specifies the type of packet data protocol. The default value is manufacturer specific.
                            {IP,IPV6,IPV4V6,Non-IP}
              <APN>: string type(lenth <= 100); a logical name that is used to select the GGSN or the external packet data network.
                                If the value is null or omitted, then the subscription value will be requested.
              <PDP_addr>: string type //NOT support; identifies the MT in the address space applicable to the PDP.
              <d_comp>: integer type; controls PDP data compression (applicable for SNDCP only)
                        0:  OFF
              <h_comp>: integer type; controls PDP header compression
                        0:  OFF
              <NSLPI>: integer type; indicates the NAS signalling priority requested for this PDP context:
                        0:  indicates that this PDP context is to be activated with the value for the low priority indicator configured in the MT.
                        1:  indicates that this PDP context is is to be activated with the value for the low priority indicator set to "MS is not configured for NAS signalling low priority".
              <securePCO>: integer type. Specifies if security protected transmission of PCO is requested or not 
                        0:  Security protected transmission of PCO is not requested
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGDCONT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char *pucPDPtypeData = NULL;
    unsigned char *pucApnValue    = NULL;
    unsigned char ucNSLPI          = 0xFF;
    unsigned char ucSecurePco      = 0xFF;
    unsigned char i;
    ST_ATC_CGDCONT_PARAMETER* ptEvent_Param = (ST_ATC_CGDCONT_PARAMETER*)pEventBuffer;
    //unsigned char ucNoUseValue;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CGDCONT_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucCid,
                            &ptEvent_Param->ucPdpTypeFlg,
                            &pucPDPtypeData,
                            NULL,
                            &pucApnValue,
                            NULL,
                            &ptEvent_Param->ucD_comp,
                            NULL,
                            &ptEvent_Param->ucH_comp,
                            &ptEvent_Param->ucNSLPIFlag,
                            &ptEvent_Param->ucNSLPI,
                            &ptEvent_Param->ucSecurePcoFlag,
                            &ptEvent_Param->ucSecurePco) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(D_ATC_FLAG_TRUE == ptEvent_Param->ucPdpTypeFlg)
        {
#if VER_CM
            for(i = 0; i < 6 ; i++)
            {
                if(0 == strcmp(pucPDPtypeData,(unsigned char*)ATC_PdpType_Table[i].pucStr))
                {
                    ptEvent_Param->ucPdpTypeValue = ATC_PdpType_Table[i].ucStrVal;
                    break;
                }
            }
            
            if(i == 6)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
#else
            for(i = 0; i < 9 ; i++)
            {
                if(0 == strcmp((const char*)pucPDPtypeData,(const char*)ATC_PdpType_Table[i].pucStr))
                {
                    ptEvent_Param->ucPdpTypeValue = ATC_PdpType_Table[i].ucStrVal;
                    break;
                }
            }
            if(i == 9)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
#endif
        }
        if(pucApnValue != NULL)
        {
            ptEvent_Param->ucApnLen = strlen((const char*)pucApnValue);
            if(ptEvent_Param->ucApnLen != 0)
            {
                if(ptEvent_Param->ucApnLen <= sizeof(ptEvent_Param->aucApnValue))
                {
                    AtcAp_MemCpy(ptEvent_Param->aucApnValue, pucApnValue, ptEvent_Param->ucApnLen);
                }
                else
                {
                    ptEvent_Param->pucApnValue = (unsigned char*)AtcAp_Malloc(ptEvent_Param->ucApnLen);
                    AtcAp_MemCpy(ptEvent_Param->pucApnValue, pucApnValue, ptEvent_Param->ucApnLen);
                }
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGDCONT;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGDCONT_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
MODULE    : ATC_CGCONTRDP_LTE_Command
FORMAT    : AT+CGCONTRDP[=<cid>]
FUNCTION  : <cid>: integer type(active); specifies a particular non secondary PDP context definition.
    The parameter is local to the TE-MT interface and is used in other PDP context-related commands
NOTE      :
HISTORY   :
1.  JN   2018.11.26   create
*******************************************************************************/
unsigned char ATC_CGCONTRDP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CGCONTRDP_PARAMETER* ptEvent_Param = (ST_ATC_CGCONTRDP_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer,
                            &ptEvent_Param->ucCid) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->ucCidFlag = 1;
        ptEvent_Param->usEvent = D_ATC_EVENT_CGCONTRDP;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGCONTRDP;
        ptEvent_Param->ucCidFlag = 0;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CREG_LTE_Command
  FORMAT    : AT+CREG=<n>
  FUNCTION  : <n>: integer type;
    0   disable network registration unsolicited result code
    1   enable network registration unsolicited result code 
        +CREG:<stat>
    2   enable network registration and location information unsolicited result code 
        +CREG: <stat>[,[<lac>],[<ci>],[<AcT>]]
    3   enable network registration, location information and cause value information unsolicited result code 
        +CREG: <stat>[,[<lac>],[<ci>],[<AcT>][,<cause_type>,<reject_cause>]]
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CREG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CREG_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CREG;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CREG_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}
/*******************************************************************************
  MODULE    : ATC_CGREG_LTE_Command
  FORMAT    : AT+CREG=[<n>]
  FUNCTION  : <n>: integer type
    0   disable network registration unsolicited result code
    1   enable network registration unsolicited result code 
        +CREG: <stat>
    2   enable network registration and location information unsolicited result code 
        +CREG: <stat>[,[<lac>],[<ci>],[<AcT>]]
    3   enable network registration, location information and cause value information unsolicited result code 
        +CREG: <stat>[,[<lac>],[<ci>],[<AcT>][,<cause_type>,<reject_cause>]]
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGREG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CGREG_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGREG;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGREG_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}
/*******************************************************************************
  MODULE    : ATC_CEREG_LTE_Command
  FORMAT    : AT+CEREG=<n>
  FUNCTION  : <n>: integer type;
    0   disable network registration unsolicited result code
    1   enable network registration unsolicited result code 
        +CEREG: <stat>
    2   enable network registration and location information unsolicited result code 
        +CEREG: <stat>[,[<tac>],[<ci>],[<AcT>]]
    3   enable network registration, location information and EMM cause value information unsolicited result code 
        +CEREG: <stat>[,[<tac>],[<ci>],[<AcT>][,<cause_type>,<reject_cause>]]
    4   For a UE that wants to apply PSM, enable network registration and location information unsolicited result code 
        +CEREG: <stat>[,[<tac>],[<ci>],[<AcT>][,,[,[<Active-Time>],[<Periodic-TAU>]]]]
    5   For a UE that wants to apply PSM, enable network registration, location information and EMM cause value information unsolicited result code 
        +CEREG: <stat>[,[<tac>],[<ci>],[<AcT>][,[<cause_type>],[<reject_cause>][,[<Active-Time>],[<Periodic-TAU>]]]]
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CEREG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CEREG_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CEREG;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CEREG_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CSCON_LTE_Command
  FORMAT    : AT+CSCON=<n>
  FUNCTION  : <n>: integer type
                0   disable unsolicited result code
                1   enable unsolicited result code +CSCON: <mode>
                2   enable unsolicited result code +CSCON: <mode>[,<state>]
                3   enable unsolicited result code +CSCON: <mode>[,<state>[,<access>]]
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CSCON_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CSCON_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CSCON;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CSCON_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CMEE_LTE_Command
  FORMAT    : AT+CMEE=<n>
  FUNCTION  : <n>:
        0   disable +CME ERROR: <err> result code and use ERROR instead
        1   enable +CME ERROR: <err> result code and use numeric <err> values
        2   enable +CME ERROR: <err> result code and use verbose <err> values
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CMEE_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CMEE_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CMEE;
#if VER_CM
        g_AtcApInfo.ucCmeeValue = ptEvent_Param->ucValue;
#else
        if(ptEvent_Param->ucValue == g_softap_fac_nv->cmee_mode)
        {
            AtcAp_SendOkRsp();
            return D_ATC_COMMAND_END;
        }
        if(ATC_AP_FALSE == g_softap_fac_nv->cmee_mode_bak_Flg)
        {
            g_softap_fac_nv->cmee_mode_bak_Flg = ATC_AP_TRUE;
            g_softap_fac_nv->cmee_mode_bak = g_softap_fac_nv->cmee_mode;
            g_softap_fac_nv->cmee_mode = ptEvent_Param->ucValue;
            SAVE_SOFTAP_FAC();
        }
        else
        {
            g_softap_fac_nv->cmee_mode = ptEvent_Param->ucValue;
            SAVE_FAC_PARAM(cmee_mode);
        }
        SOFTAP_FAC_CACHE_CLEAN(cmee_mode);
#endif
        AtcAp_SendOkRsp();
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CMEE_R;
        AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"\r\n+CMEE:%d\r\n", api_GetCmeeValue());
        AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        AtcAp_SendOkRsp();
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;
}

/*******************************************************************************
  MODULE    : ATC_CGSN_LTE_Command
  FORMAT    : AT+CGSN[=<snt>]
  FUNCTION  : <snt>: integer type;
    0   returns <sn>
    1   returns the IMEI (International Mobile station Equipment Identity)
    2   returns the IMEISV (International Mobile station Equipment Identity and Software Version number)
    3   returns the SVN (Software Version Number)
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGSN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER*  ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CGSN_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGSN;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGSN;
#if (VER_QUEC && !USR_CUSTOM2) || (USR_CUSTOM4) || (USR_CUSTOM6) || (USR_CUSTOM12)
        ptEvent_Param->ucValue = 1;
#else
        ptEvent_Param->ucValue = 0;
#endif
    }
#if (VER_QUEC) && (!USR_CUSTOM2)
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGSN;
        ptEvent_Param->ucValue = 1;
    }
#endif
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CESQ_LTE_Command
  FORMAT    : AT+CESQ
  FUNCTION  : 
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CESQ_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CESQ_PARAMETER*  ptEvent_Param = (ST_ATC_CESQ_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CESQ;
    }
#if PS_TEST_MODE
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CESQ_FORMAT, pCommandBuffer, &ptEvent_Param->usTimerVal )== ATC_AP_FALSE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CESQ;
    }
#endif
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CSQ_LTE_Command
  FORMAT    : AT+CSQ
  FUNCTION  : 
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CSQ_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CSQ;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}


/*******************************************************************************
  MODULE    : ATC_CGPADDR_LTE_Command
  FORMAT    : AT+CGPADDR[=<cid>[,<cid>[,...]]]
  FUNCTION  : <cid>: integer type(1-15)list of defined <cid>;specifies a particular PDP context definition (see the +CGDCONT and +CGDSCONT commands).
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGPADDR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char i = 0;
    ST_ATC_CGPADDR_PARAMETER*  ptEvent_Param = (ST_ATC_CGPADDR_PARAMETER*)pEventBuffer;
    unsigned char ucOffset = 0;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        for(i = 0; i < D_MAX_CNT_CID; i++)
        {
            if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer + ucOffset, &(ptEvent_Param->aucCid[i]) )== ATC_AP_FALSE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ptEvent_Param->ucCidNum++;
            if(AtcAp_FindNextComma(pCommandBuffer + ucOffset) == ATC_FALSE)
            {
                break;
            }
            if(D_MAX_CNT_CID - 1 == i)
            {
                return D_ATC_COMMAND_TOO_MANY_PARAMETERS;
            }
            ucOffset += AtcAp_FindNextComma(pCommandBuffer + ucOffset);
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGPADDR;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGPADDR;
        ptEvent_Param->ucAllCidFlg = D_ATC_FLAG_TRUE;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}





/*******************************************************************************
  MODULE    : ATC_CPSMS_LTE_Command
  FORMAT    : AT+CPSMS=[<mode>[,[,[,<Requested_Periodic-TAU>[,<Requested_Active-Time>]]]]]
  FUNCTION  : <mode>: integer type. Indication to disable or enable the use of PSM in the UE.
                0   Disable the use of PSM
                1   Enable the use of PSM
                2   Disable the use of PSM and discard all parameters for PSM or, if available, reset to the manufacturer specific default values.
            <Requested_Periodic-TAU>: string type; one byte in an 8 bit format. 
            <Requested_Active-Time>: string type; one byte in an 8 bit format.
                0 0 0 | 0 0 0 0 0
                unit  ; value
  NOTE      :
  HISTORY   :

*******************************************************************************/
unsigned char ATC_CPSMS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char*  pucTauTime = NULL;
    unsigned char*  pucActTime = NULL;
    unsigned char ucCtr = 0;
    ST_ATC_CPSMS_PARAMETER*  ptEvent_Param = (ST_ATC_CPSMS_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CPSMS_FORMAT, pCommandBuffer,
                                &(ptEvent_Param->ucMode),
                                &(ptEvent_Param->ucReqPeriTAUFlg),
                                &(pucTauTime),
                                &(ptEvent_Param->ucReqActTimeFlag),
                                &(pucActTime)) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(ptEvent_Param->ucReqPeriTAUFlg == D_ATC_FLAG_TRUE)
        {
            if(strlen(pucTauTime) != 8)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            for (ucCtr = 0; ucCtr < 8; ucCtr++)
            {
                if ((pucTauTime[ucCtr] == '0') || (pucTauTime[ucCtr] == '1'))
                {
                    pucTauTime[ucCtr] = (unsigned char)(pucTauTime[ucCtr] - '0');
                }
                else
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                (ptEvent_Param->ucReqPeriTAU) |= pucTauTime[ucCtr] << (7 - ucCtr);
            }
        }
        if(ptEvent_Param->ucReqActTimeFlag == D_ATC_FLAG_TRUE)
        {
            if(strlen(pucActTime) != 8)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            for (ucCtr = 0; ucCtr < 8; ucCtr++)
            {
                if ((pucActTime[ucCtr] == '0') || (pucActTime[ucCtr] == '1'))
                {
                    pucActTime[ucCtr] = (unsigned char)(pucActTime[ucCtr] - '0');
                }
                else
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                (ptEvent_Param->ucReqActTime) |= pucActTime[ucCtr] << (7 - ucCtr);
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CPSMS;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CPSMS_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
MODULE    : ATC_CNEC_LTE_Command
FORMAT    : AT+CNEC=[<n>]
FUNCTION  : [<n>]
0   Disable unsolicited error reporting
8   Enable unsolicited result code +CNEC_EMM to report EPS mobility management errors coded as specified in 3GPP TS 24.301 [83] Table 9.9.3.9.1
16  Enable unsolicited result code +CNEC_ESM to report EPS session management errors coded as specified in 3GPP TS 24.301 [83] Table 9.9.4.4.1
24  8+16
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_CNEC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER* ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;
       
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if (AtcAp_CmdParamPrase(D_ATC_CMD_CNEC_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CNEC;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CNEC_R;
    }    
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

/*******************************************************************************
  MODULE    : ATC_CLAC_LTE_Command
  FORMAT    : AT+CLAC
  FUNCTION  : 
  NOTE      : 
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CLAC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT* ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;
    ST_ATC_COMMAND_ANAL_TABLE *pWorkJumpTblptr = NULL;
    int i = 0;
       
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CLAC;
        pWorkJumpTblptr = (ST_ATC_COMMAND_ANAL_TABLE *)ATC_Plus_CommandTable;
    
        for(i = 0; 0 != strcmp(pWorkJumpTblptr[i].LetterData,""); i++ )
        {
            AtcAp_StrPrintf_AtcRspBuf("\r\nAT+%s",pWorkJumpTblptr[i].LetterData);
            if(strlen(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf) > 638)
            {
                AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
                g_AtcApInfo.stAtRspInfo.usRspLen = 0;
            }
        }

        if(strlen(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf) > 0)
        {
            AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
            g_AtcApInfo.stAtRspInfo.usRspLen = 0;
        }
        //send_at_cmd_list();

        AtcAp_StrPrintf_AtcRspBuf("\r\n");
        AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        AtcAp_SendOkRsp();

    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;
}

/*******************************************************************************
  MODULE    : ATC_CGEREP_Command
  FORMAT    : AT+CGEREP=[<mode>[,<bfr>]]
  FUNCTION  : <mode>: integer type(0-2),2:don't have function
    0   buffer unsolicited result codes in the MT; if MT result code buffer is full, the oldest ones can be discarded. No codes are forwarded to the TE.
    1   discard unsolicited result codes when MT_TE link is reserved (e.g. in on_line data mode); otherwise forward them directly to the TE
    <bfr>:(0,1),not used
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGEREP_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CGEREP_PARAMETER* ptEvent_Param = (ST_ATC_CGEREP_PARAMETER*)pEventBuffer;
       
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if (AtcAp_CmdParamPrase(D_ATC_CMD_CGEREP_FORMAT, pCommandBuffer,
                                &ptEvent_Param->ucMode,
                                &ptEvent_Param->ucBfrFlag,
                                &ptEvent_Param->ucBfr) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGEREP;
#if VER_CM
        if(0 == (ptEvent_Param->ucMode))
        {
            ptEvent_Param->ucBfrFlag = D_ATC_FLAG_TRUE;
            ptEvent_Param->ucBfr = 0;
        }
#endif 
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGEREP_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;


}

/*******************************************************************************
  MODULE    : ATC_COPS_LTE_Command
  FORMAT    : AT+COPS=[<mode>[,<format>[,<oper>[,<AcT>]]]]
  FUNCTION  : 
<mode>: integer type
    0   automatic (<oper> field is ignored)
    1   manual (<oper> field shall be present, and <AcT> optionally)
    2   deregister from network
    3   set only <format> (for read command +COPS?), do not attempt registration/deregistration (<oper> and <AcT> fields are ignored); this value is not applicable in read command response
    4   manual/automatic (<oper> field shall be present); if manual selection fails, automatic mode (<mode>=0) is entered
<format>: integer type
    0   long format alphanumeric <oper>
    1   short format alphanumeric <oper>
    2   numeric <oper>
<AcT>: 7    E-UTRAN
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_COPS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_COPS_PARAMETER* ptEvent_Param = (ST_ATC_COPS_PARAMETER*)pEventBuffer;
    unsigned char *PucOperData = NULL;
    char         *pucPlmnStr;
    
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if (AtcAp_CmdParamPrase(D_ATC_CMD_COPS_FORMAT, pCommandBuffer,
                                &ptEvent_Param->ucMode,
                                &ptEvent_Param->ucFormatFlag,
                                &ptEvent_Param->ucFormat,
                                &ptEvent_Param->ucPerFlag,
                                &PucOperData,
                                &ptEvent_Param->ucActFlg,
                                &ptEvent_Param->ucAct) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
#if VER_CM
        if((ptEvent_Param->ucMode == 3 && (ptEvent_Param->ucFormatFlag == 0 || ptEvent_Param->ucPerFlag == 1 || ptEvent_Param->ucActFlg == 1))
            || ((ptEvent_Param->ucMode == 0 || ptEvent_Param->ucMode == 2) && (ptEvent_Param->ucPerFlag == 1 || ptEvent_Param->ucActFlg == 1))
            || ((ptEvent_Param->ucMode == 1 || ptEvent_Param->ucMode == 4) && ptEvent_Param->ucPerFlag == 0))
#else
        if((ptEvent_Param->ucMode == 3 && (ptEvent_Param->ucFormatFlag == 0 || ptEvent_Param->ucPerFlag == 1 || ptEvent_Param->ucActFlg == 1))
            || ((ptEvent_Param->ucMode == 0 || ptEvent_Param->ucMode == 2) && (ptEvent_Param->ucFormatFlag == 1 || ptEvent_Param->ucPerFlag == 1 || ptEvent_Param->ucActFlg == 1))
            || ((ptEvent_Param->ucMode == 1 || ptEvent_Param->ucMode == 4) && (ptEvent_Param->ucFormatFlag == 0 ||ptEvent_Param->ucPerFlag == 0)))
#endif
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(ptEvent_Param->ucPerFlag == D_ATC_FLAG_TRUE)
        {
            if(D_ATC_FLAG_TRUE == ptEvent_Param->ucFormatFlag
                && (PLMN_FORMAT_SHORT_ALPHA == ptEvent_Param->ucFormat || PLMN_FORMAT_LONG_ALPHA == ptEvent_Param->ucFormat))
            {
                pucPlmnStr = AtcAp_GetPlmnStrByName(ptEvent_Param->ucFormat, PucOperData);
                if(NULL == pucPlmnStr)
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                strcpy(PucOperData, pucPlmnStr);
            }

            if(PucOperData != NULL)
            {
                if(ATC_AP_FALSE == AtcAp_ConvertOperStr2Hex(PucOperData, &ptEvent_Param->aucPer[0], sizeof(ptEvent_Param->aucPer)))
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_COPS;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_COPS_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CTZR_LTE_Command
  FORMAT    : AT+CTZR=[<reporting>]
  FUNCTION  : <reporting>: integer type value indicating:
    0   disable time zone change event reporting.
    1   Enable time zone change event reporting by unsolicited result code 
        +CTZV: <tz>.
    2   Enable extended time zone and local time reporting by unsolicited result code 
        +CTZE: <tz>,<dst>,[<time>].
    3   Enable extended time zone and universal time reporting by unsolicited result code 
        +CTZEU: <tz>,<dst>,[<utime>].
  NOTE      :
  HISTORY   :
      1.  JN   2018.11.26   create
*******************************************************************************/
unsigned char ATC_CTZR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER* ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;
       
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if (AtcAp_CmdParamPrase(D_ATC_CMD_CTZR_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CTZR;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CTZR_R;
    }    
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
MODULE    : ATC_CTZU_LTE_Command
FORMAT    : AT+CTZU=[<onoff>]
FUNCTION  : 
<onoff>: integer type value indicating
0   Disable automatic time zone update via NITZ.
1   Enable automatic time zone update via NITZ.
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_CTZU_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER* ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;
    unsigned char     ucCtzuMode = 0;
       
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    { 
#if VER_CM
        if (AtcAp_CmdParamPrase(D_ATC_CMD_CTZU_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
#else
        if (AtcAp_CmdParamPrase(D_ATC_CMD_CTZU_FORMAT, pCommandBuffer, &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
#endif
        ptEvent_Param->usEvent = D_ATC_EVENT_CTZR;
#if VER_CM
        if(ptEvent_Param->ucValue == 2)
        {
            ptEvent_Param->ucValue = 1;
        }
        else if(ptEvent_Param->ucValue == 1)
        {
            ptEvent_Param->ucValue = 3;
        }
#endif
        g_app_basic_cfg.nitz = ptEvent_Param->ucValue;
        SAVE_APP_BASIC_CFG_PARAM(nitz);
        AtcAp_SendOkRsp();
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CTZR_R;
#if VER_CM
        if(g_app_basic_cfg.nitz == 3)
        {
            ucCtzuMode = 1;
        }
        else if(g_app_basic_cfg.nitz == 1)
        {
            ucCtzuMode = 2;
        }
        else
        {
            ucCtzuMode = g_app_basic_cfg.nitz;
        }
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CTZU:%d\r\n", ucCtzuMode);
#else
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CTZU:%d\r\n", g_app_basic_cfg.nitz);
#endif
        AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        AtcAp_SendOkRsp();
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;
}

/*******************************************************************************
  MODULE    : ATC_CGPIAF_LTE_Command
  FORMAT    : AT+CGPIAF=[<IPv6_AddressFormat>[,<IPv6_SubnetNotation>[,<IPv6_LeadingZeros>[,<IPv6_CompressZeros>]]]]
  FUNCTION  : Set the IPv6 display format
  ucIpv6AddressFormatFlag:      0 :decimal ;    
                                1 :hexadecimal
  The following parameters only take effect in hexadecimal format
    ucIpv6SubnetNotationFlag:     0 :2001:0DB8:0000:CD30:0000:0000:0000:0000 ffff:ffff:ffff:FFF0:0000:0000:0000:0000 ;    
                                  1:2001:0DB8:0000:CD30:0000:0000:0000:0000/60
    ucIpv6LeadingZerosFlag:       0 :2001:DB8:0:CD30:0:0:0:0  
                                  1:2001:0DB8:0000:CD30:0000:0000:0000:0000
    ucIpv6CompressZerosFlag:      0 :2001:DB8:0:CD30:0:0:0:0  
                                  1:2001:DB8:0:CD30::
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGPIAF_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CGPIAF_PARAMETER* ptEvent_Param = (ST_ATC_CGPIAF_PARAMETER*)pEventBuffer;
       
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if (AtcAp_CmdParamPrase(D_ATC_CMD_CGPIAF_FORMAT, pCommandBuffer,
                                &ptEvent_Param->ucIpv6AddressFormatFlag,
                                &ptEvent_Param->ucIpv6AddressFormat,
                                &ptEvent_Param->ucIpv6SubnetNotationFlag,
                                &ptEvent_Param->ucIpv6SubnetNotation,
                                &ptEvent_Param->ucIpv6LeadingZerosFlag,
                                &ptEvent_Param->ucIpv6LeadingZeros,
                                &ptEvent_Param->ucIpv6CompressZerosFlag,
                                &ptEvent_Param->ucIpv6CompressZeros) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGPIAF;
        if(ptEvent_Param->ucIpv6AddressFormat != g_softap_fac_nv->ucIpv6AddressFormat && ptEvent_Param->ucIpv6AddressFormatFlag == 1)
        {
            g_softap_fac_nv->ucIpv6AddressFormat = ptEvent_Param->ucIpv6AddressFormat;
            SAVE_FAC_PARAM(ucIpv6AddressFormat);
        }
        if(ptEvent_Param->ucIpv6SubnetNotation != g_softap_fac_nv->ucIpv6SubnetNotation && ptEvent_Param->ucIpv6SubnetNotationFlag == 1)
        {
            g_softap_fac_nv->ucIpv6SubnetNotation = ptEvent_Param->ucIpv6SubnetNotation;
            SAVE_FAC_PARAM(ucIpv6SubnetNotation);
        }
        if(ptEvent_Param->ucIpv6LeadingZeros != g_softap_fac_nv->ucIpv6LeadingZeros && ptEvent_Param->ucIpv6LeadingZerosFlag == 1)
        {
            g_softap_fac_nv->ucIpv6LeadingZeros = ptEvent_Param->ucIpv6LeadingZeros;
            SAVE_FAC_PARAM(ucIpv6LeadingZeros);
        }
        if(ptEvent_Param->ucIpv6CompressZeros != g_softap_fac_nv->ucIpv6CompressZeros && ptEvent_Param->ucIpv6CompressZerosFlag == 1)
        {
            g_softap_fac_nv->ucIpv6CompressZeros = ptEvent_Param->ucIpv6CompressZeros;
            SAVE_FAC_PARAM(ucIpv6CompressZeros);
        }
        AtcAp_SendOkRsp();
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGPIAF_R;
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CGPIAF:%d,%d,%d,%d\r\n",
                              g_softap_fac_nv->ucIpv6AddressFormat,g_softap_fac_nv->ucIpv6SubnetNotation,
                              g_softap_fac_nv->ucIpv6LeadingZeros,g_softap_fac_nv->ucIpv6CompressZeros);
        AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        AtcAp_SendOkRsp();
    }    
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;
}


/*******************************************************************************
MODULE    : ATC_CPOL_LTE_Command
FORMAT    : AT+CPOL=[<index>][,<format>[,<oper>[,<GSM_AcT>,<GSM_Compact_AcT>,<UTRAN_AcT>,<E-UTRAN_AcT>]]]
FUNCTION  : 
<indexn>: integer type; the order number of operator in the SIM/USIM preferred operator list
<format>: integer type
    0   long format alphanumeric <oper>
    1   short format alphanumeric <oper>
    2   numeric <oper>
<opern>: string type; <format> indicates if the format is alphanumeric or numeric (see +COPS)
<GSM_AcTn>: integer type; GSM access technology:
    0   access technology not selected
    1   access technology selected
<GSM_Compact_AcTn>: integer type; GSM compact access technology
    0   access technology not selected
    1   access technology selected
<UTRAN_AcTn>: integer type; UTRAN access technology
    0   access technology not selected
    1   access technology selected
<E-UTRAN_AcTn>: integer type; E-UTRAN access technology
    0   access technology not selected
    1   access technology selected
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_CPOL_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CPOL_PARAMETER* ptEvent_Param = (ST_ATC_CPOL_PARAMETER*)pEventBuffer;
    unsigned char *PucOperData = NULL;
    char         *pucPlmnStr;
    unsigned char ucFormatFlg = D_ATC_FLAG_FALSE;
    unsigned char aucActFlg[4]= { 0 };
    
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if (AtcAp_CmdParamPrase(D_ATC_CMD_CPOL_FORMAT, pCommandBuffer,
                                &ptEvent_Param->indexFlg,
                                &ptEvent_Param->index,
                                &ptEvent_Param->ucFormatFlg,
                                &ptEvent_Param->ucFormat,
                                &ptEvent_Param->ucOperFlg,
                                &PucOperData,
                                &aucActFlg[0],
                                &ptEvent_Param->ucGSMAcT,
                                &aucActFlg[1],
                                &ptEvent_Param->ucGSMCompactAcT,
                                &aucActFlg[2],
                                &ptEvent_Param->ucUTRANAcT,
                                &aucActFlg[3],
                                &ptEvent_Param->ucEUTRANAcT) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CPOL;
    
        if(aucActFlg[0] == D_ATC_FLAG_FALSE 
            && (aucActFlg[1] == D_ATC_FLAG_FALSE ) 
            && (aucActFlg[2] == D_ATC_FLAG_FALSE ) 
            && (aucActFlg[3] == D_ATC_FLAG_FALSE )) 
        {
            ptEvent_Param->ucActFlg = D_ATC_FLAG_FALSE;
        }
        else if(aucActFlg[0] == D_ATC_FLAG_TRUE 
            && (aucActFlg[1] == D_ATC_FLAG_TRUE ) 
            && (aucActFlg[2] == D_ATC_FLAG_TRUE ) 
            && (aucActFlg[3] == D_ATC_FLAG_TRUE )) 
        {
            ptEvent_Param->ucActFlg = D_ATC_FLAG_TRUE;
        }
        else
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(ptEvent_Param->ucFormatFlg == 0)
        {
            ptEvent_Param->ucFormat = PLMN_FORMAT_NUMERIC;
        }
        if((D_ATC_FLAG_TRUE == ptEvent_Param->ucFormatFlg && D_ATC_FLAG_TRUE == ptEvent_Param->ucOperFlg)
            && (PLMN_FORMAT_SHORT_ALPHA == ptEvent_Param->ucFormat || PLMN_FORMAT_LONG_ALPHA == ptEvent_Param->ucFormat))
        {
            pucPlmnStr = AtcAp_GetPlmnStrByName(ptEvent_Param->ucFormat, PucOperData);
            if(NULL == pucPlmnStr)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            strcpy(PucOperData, pucPlmnStr);
            ptEvent_Param->ucFormat = PLMN_FORMAT_NUMERIC;
        }

        if(PucOperData != NULL)
        {
            AtcAp_MemCpy(ptEvent_Param->aucOper,PucOperData,strlen(PucOperData));
            ptEvent_Param->ucOperLen = strlen(ptEvent_Param->aucOper);
        }
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CPOL_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
MODULE    : ATC_COPN_LTE_Command
FORMAT    : AT+COPN
FUNCTION  : 
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_COPN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;
    long unsigned int i;

    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_COPN;
#if VER_CM
        for(i = 0; i < sizeof(ATC_OperatonName_Table_ZY)/sizeof(ST_ATC_AP_PLMN_NAME_TABLE); i++)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+COPN:\"%s\",\"%s\",\"%s\"",
                ATC_OperatonName_Table_ZY[i].pucOper, ATC_OperatonName_Table_ZY[i].pucLongName, ATC_OperatonName_Table_ZY[i].pucShortName);
            if(i == sizeof(ATC_OperatonName_Table_ZY)/sizeof(ST_ATC_AP_PLMN_NAME_TABLE) - 1)
            {
                AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
            }
            AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        }
#else
        for(i = 0; i < sizeof(ATC_OperatonName_Table)/sizeof(ST_ATC_AP_OPERTION_NAME_TABLE); i++)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+COPN:\"%s\",\"%s\"", ATC_OperatonName_Table[i].pucOper, ATC_OperatonName_Table[i].pucName);
            if(i == sizeof(ATC_OperatonName_Table)/sizeof(ST_ATC_AP_OPERTION_NAME_TABLE) - 1)
            {
                AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
            }
            AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        }
#endif
        AtcAp_SendOkRsp();
    }    
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;
}

/*******************************************************************************
MODULE    : ATC_CSCS_LTE_Command
FORMAT    : AT+CSCS=[<chset>]
FUNCTION  : 
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_CSCS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER* ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;
    unsigned char         *pucCscsStr = NULL;
    unsigned char i;
    
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if (AtcAp_CmdParamPrase(D_ATC_CMD_CSCS_FORMAT, pCommandBuffer, NULL, &pucCscsStr) != ATC_AP_TRUE
            || pucCscsStr == NULL)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }

        for(i = 0; i < sizeof(ATC_CharSet_Table) / sizeof(ST_ATC_STR_TABLE); i++)
        {
            if(0 == strcmp((const char*)ATC_CharSet_Table[i].pucStr, pucCscsStr))
            {
                ptEvent_Param->ucValue = ATC_CharSet_Table[i].ucStrVal;
                break;
            }
        }

        if(i == sizeof(ATC_CharSet_Table) / sizeof(ST_ATC_STR_TABLE))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CSCS;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CSCS_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
MODULE    : ATC_CGMI_LTE_Command
FORMAT    : AT+CGMI
FUNCTION  : 
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_CGMI_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT* ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;
    
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    { 
        ptEvent_Param->usEvent = D_ATC_EVENT_CGMI;
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s\r\n", g_softap_fac_nv->manufacturer);
        AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        AtcAp_SendOkRsp();
    }    
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;
}

/*******************************************************************************
MODULE    : ATC_CGMM_LTE_Command
FORMAT    : AT+CGMM
FUNCTION  : 
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_CGMM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT* ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;
    
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGMM;
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s\r\n", g_softap_fac_nv->modul_ver);
        AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        AtcAp_SendOkRsp();
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_END;
}

/*******************************************************************************
MODULE    : ATC_CGMR_LTE_Command
FORMAT    : AT+CGMR
FUNCTION  : 
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_CGMR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT* ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;
    
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    { 
        ptEvent_Param->usEvent = D_ATC_EVENT_CGMR;
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s\r\n", g_softap_fac_nv->versionExt);
        AtcAp_SendDataInd((unsigned char*)ptEvent_Param);
        AtcAp_SendOkRsp();
    }    
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
MODULE    : ATC_CIPCA_LTE_Command
FORMAT    : AT+CIPCA=[<n>[,<AttachWithoutPDN>]]
FUNCTION  : 
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_CIPCA_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CIPCA_PARAMETER* ptEvent_Param = (ST_ATC_CIPCA_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CIPCA_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucN,
                            NULL,
                            &ptEvent_Param->ucAttWithoutPDN) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CIPCA;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CIPCA_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}


/*******************************************************************************
MODULE    : ATC_CGAUTH_LTE_Command
FORMAT    : AT+CGAUTH=<cid>[,<auth_prot>[,<userid>[,<password>]]]
FUNCTION  : 
<cid>: integer type. Specifies a particular PDP context definition 
<auth_prot>: integer type. Authentication protocol used for this PDP context.
    0	None. Used to indicate that no authentication protocol is used for this PDP context. Username and password are removed if previously specified.
    1	PAP
    2	CHAP
<userid>: String type. User name for access to the IP network.
<password>: String type. Password for access to the IP network.
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_CGAUTH_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CGAUTH_PARAMETER* ptEvent_Param = (ST_ATC_CGAUTH_PARAMETER*)pEventBuffer;
    unsigned char  *pucUsername = NULL;
    unsigned char  *pucPassword = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CGAUTH_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucCid,
                            NULL,
                            &ptEvent_Param->ucAuthProt,
                            NULL,
                            &pucUsername,
                            NULL,
                            &pucPassword) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(pucUsername != NULL)
        {
            ptEvent_Param->ucUsernameLen = strlen(pucUsername);
            AtcAp_MemCpy(ptEvent_Param->aucUsername, pucUsername, ptEvent_Param->ucUsernameLen);
        }
        if(pucUsername == NULL && pucPassword != NULL)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        else if(pucPassword != NULL)
        {
            ptEvent_Param->ucPasswordLen = strlen(pucPassword);
            AtcAp_MemCpy(ptEvent_Param->aucPassword, pucPassword, ptEvent_Param->ucPasswordLen);
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGAUTH;

    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGAUTH_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
MODULE    : ATC_CEER_LTE_Command
FORMAT    : AT+CEER
FUNCTION  : 
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_CEER_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT* ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CEER;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}


/*******************************************************************************
MODULE    : ATC_CNUM_LTE_Command
FORMAT    : AT+CNUM
FUNCTION  : 
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_CNUM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT* ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CNUM;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

#ifdef ESM_DEDICATED_EPS_BEARER
/*******************************************************************************
  MODULE    : ATC_CGDSCONT_LTE_Command
  FORMAT    : AT+CGDSCONT=[<cid>,<p_cid>[,<d_comp>[,<h_comp>]]]
  FUNCTION  : <cid>: integer type; which specifies a particular PDP context definition. 
            <p_cid>: integer type(active primary contexts); specifies a particular PDP context definition which has been specified by use of the +CGDCONT command. 
            <d_comp>: integer type (0)
            <h_comp>: integer type (0)
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGDSCONT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CGDSCONT_PARAMETER* ptEvent_Param = (ST_ATC_CGDSCONT_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CGDSCONT_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucCid,
                            &ptEvent_Param->ucP_cidFlg,
                            &ptEvent_Param->ucP_cid,
                            NULL,
                            &ptEvent_Param->ucD_comp,
                            NULL,
                            &ptEvent_Param->ucH_comp) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGDSCONT;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGDSCONT_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CGSCONTRDP_LTE_Command
  FORMAT    : AT+CGSCONTRDP[=<cid>]
  FUNCTION  : <cid>: integer type(active); specifies a particular active secondary PDP context or Traffic Flows definition.
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGSCONTRDP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CGSCONTRDP_PARAMETER* ptEvent_Param = (ST_ATC_CGSCONTRDP_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer,
                            &ptEvent_Param->ucCid) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGSCONTRDP;
        ptEvent_Param->ucCidFlag = 1;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGSCONTRDP;
        ptEvent_Param->ucCidFlag = 0;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}
/*******************************************************************************
  MODULE    : ATC_CGEQOS_LTE_Command
  FORMAT    : AT+CGEQOS=[<cid>[,<QCI>[,<DL_GBR>,<UL_GBR>[,<DL_MBR>,<UL_MBR]]]]
  FUNCTION  : <cid>: integer type; specifies a particular EPS Traffic Flows definition in EPS and a PDP Context definition in UMTS/GPRS
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGEQOS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CGEQOS_PARAMETER* ptEvent_Param = (ST_ATC_CGEQOS_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {

        if(AtcAp_CmdParamPrase(D_ATC_CMD_CGEQOS_FORMAT, pCommandBuffer, 
                            &ptEvent_Param->ucCid,
                            &ptEvent_Param->ucQciFlag,
                            &ptEvent_Param->ucQci,
                            &ptEvent_Param->ucDl_GbrFlag,
                            &ptEvent_Param->ulDl_Gbr,
                            &ptEvent_Param->ucUl_GbrFlag,
                            &ptEvent_Param->ulUl_Gbr,
                            &ptEvent_Param->ucDl_MbrFlag,
                            &ptEvent_Param->ulDl_Mbr,
                            &ptEvent_Param->ucUl_MbrFlag,
                            &ptEvent_Param->ulUl_Mbr) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(ptEvent_Param->ucQciFlag == 1)
        {
            if((ptEvent_Param->ucQci > 9 && ptEvent_Param->ucQci <75)
                || (ptEvent_Param->ucQci > 75 && ptEvent_Param->ucQci < 79)
                || (ptEvent_Param->ucQci > 79 && ptEvent_Param->ucQci < 128))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGEQOS;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGEQOS_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CGEQOSRDP_LTE_Command
  FORMAT    : AT+CGEQOSRDP[=<cid>]
  FUNCTION  : <cid>: integer type(active); specifies a particular Traffic Flows definition in EPS and a PDP Context definition in UMTS/GPRS
  NOTE      :
  HISTORY   :
      1.  tianchengbin   2018.11.26   create
*******************************************************************************/
unsigned char ATC_CGEQOSRDP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CGEQOSRDP_PARAMETER* ptEvent_Param = (ST_ATC_CGEQOSRDP_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer,
                            &ptEvent_Param->ucCid) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGEQOSRDP;
        ptEvent_Param->ucCidFlag = 1;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGEQOSRDP;
        ptEvent_Param->ucCidFlag = 0;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CGTFT_LTE_Command
  FORMAT    : AT+CGTFT=[<cid>,[<packet filter identifier>,<evaluation precedence index>
            [,<remote address and subnet mask>[,<protocol number (ipv4) / next header (ipv6)>
            [,<local port range>[,<remote port range>[,<ipsec security parameter index (spi)>
            [,<type of service (tos) (ipv4) and mask / traffic class (ipv6) and mask>[,<flow label (ipv6)>[,<direction>[,<local address and subnet mask>]]]]]]]]]]]
  FUNCTION  : 
  NOTE      : Modifying the code from "case4" to "case10" for TTCN scripts 22.6.2 and 22.6.3,
              modify the return value of every "case D_ATC_PARAM_EMPTY" to make it "D_ATC_COMMAND_OK"
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGTFT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char *pInd = NULL;
    ST_ATC_CGTFT_PARAMETER* ptEvent_Param =  (ST_ATC_CGTFT_PARAMETER*)pEventBuffer;
    unsigned char * pucRemoteAddressAndSubnetMaskValue = NULL;
    unsigned char * pucLocalPortRangeValue             = NULL;
    unsigned char * pucRemotePortRangeValue            = NULL;
    unsigned char * pucIpsecSpiValue                   = NULL;
    unsigned char * pucTosAndMask_TracfficClassAndMaskValue = NULL;
    unsigned char * pucLocalAddressAndSubnetMaskValue  = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        ptEvent_Param->ucPacketFilterIdentifier = 0xFF;
        ptEvent_Param->ulFlowLabel = 0xFFFFFFFF;
        ptEvent_Param->ucDirection = 0xFF;
        if (AtcAp_CmdParamPrase(D_ATC_CMD_CGTFT_FORMAT, pCommandBuffer,
                           &ptEvent_Param->ucCid,
                           &ptEvent_Param->ucPacketFilterIdentifierFlag,
                           &ptEvent_Param->ucPacketFilterIdentifier,
                           &ptEvent_Param->ucEvaluationPrecedenceIndexFlg, 
                           &ptEvent_Param->ucEvaluationPrecedenceIndex,
                           NULL,
                           &pucRemoteAddressAndSubnetMaskValue,
                           &ptEvent_Param->ucProtocolNumber_NextHeaderFlag,
                           &ptEvent_Param->ucProtocolNumber_NextHeader,
                           NULL,
                           &pucLocalPortRangeValue,
                           NULL,
                           &pucRemotePortRangeValue,
                           &ptEvent_Param->ucIpsecSpiFlag,
                           &ptEvent_Param->ulIpsecSpi,
                           NULL,
                           &pucTosAndMask_TracfficClassAndMaskValue,
                           &ptEvent_Param->ucFlowLabelFlag,
                           &ptEvent_Param->ulFlowLabel,
                           &ptEvent_Param->ucDirectionFlag,
                           &ptEvent_Param->ucDirection,
                           NULL,
                           &pucLocalAddressAndSubnetMaskValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        } 
        ptEvent_Param->ucCidFlag = 1;/*<cid>*/
     
        if(pucRemoteAddressAndSubnetMaskValue != NULL)/*opt:<remote address and subnet mask>*/
        {
            if(ATC_OK != ATC_ConvertIpAddrAndSubnetMask(pucRemoteAddressAndSubnetMaskValue, strlen(pucRemoteAddressAndSubnetMaskValue), 
                                                        ptEvent_Param->aucRemoteAddressAndSubnetMaskValue,
                                                        &ptEvent_Param->ucRemoteAddressAndSubnetMaskLen))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        } 
        if(pucLocalPortRangeValue != NULL)/*opt:<local port range>*/
        {
            if(ATC_OK != ATC_ConvertPortRangeValue(pucLocalPortRangeValue, strlen(pucLocalPortRangeValue), ptEvent_Param->ausLocalPortRangeValue, &ptEvent_Param->ucLocalPortRangeLen))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }               
        }
        if(pucRemotePortRangeValue != NULL)/*opt:<remote port range>*/
        {
            if(ATC_OK != ATC_ConvertPortRangeValue(pucRemotePortRangeValue, strlen(pucRemotePortRangeValue), ptEvent_Param->ausRemotePortRangeValue, &ptEvent_Param->ucRemotePortRangeLen))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }               
        }  
        if(pucTosAndMask_TracfficClassAndMaskValue != NULL)/* opt:<type of service (tos) (ipv4) and mask / traffic class (ipv6) and mask>*/
        {
            if(ATC_OK != ATC_ConvertDotSeptNumParams2Char(pucTosAndMask_TracfficClassAndMaskValue, strlen(pucTosAndMask_TracfficClassAndMaskValue), 2, 
                                                          ptEvent_Param->aucTosAndMask_TracfficClassAndMaskValue, 
                                                          &ptEvent_Param->ucTosAndMask_TracfficClassAndMaskLen))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
 
        if(pucLocalAddressAndSubnetMaskValue != NULL)
        {
            if(ATC_OK != ATC_ConvertIpAddrAndSubnetMask(pucLocalAddressAndSubnetMaskValue, strlen(pucLocalAddressAndSubnetMaskValue),
                                                         ptEvent_Param->aucLocalAddressAndSubnetMaskValue, &ptEvent_Param->ucLocalAddressAndSubnetMaskLen))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }                
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGTFT;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGTFT_R;
    }
        
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CGTFTRDP_LTE_Command
  FORMAT    : AT+CGTFTRDP[=<cid>]
  FUNCTION  :<cid>: integer type(active ); Specifies a particular secondary or non secondary PDP context definition or Traffic Flows definition
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGTFTRDP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CGTFTRDP_PARAMETER* ptEvent_Param = (ST_ATC_CGTFTRDP_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer,
                            &ptEvent_Param->ucCid) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGTFTRDP;
        ptEvent_Param->ucCidFlag = 1;

    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGTFTRDP;
        ptEvent_Param->ucCidFlag = 0;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CGCMOD_LTE_Command
  FORMAT    : +CGCMOD[=<cid>[,<cid>[,...]]]
  FUNCTION  : <cid>: integer type(active); specifies a particular PDP context definition
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGCMOD_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char i = 0;
    ST_ATC_CGCMOD_PARAMETER*  ptEvent_Param = (ST_ATC_CGCMOD_PARAMETER*)pEventBuffer;
    unsigned char ucOffset = 0;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        for(i = 0; i < D_MAX_CNT_CID; i++)
        {
            if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer + ucOffset, &(ptEvent_Param->aucCid[i])) == ATC_AP_FALSE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            ptEvent_Param->ucCidNum++;
            if(AtcAp_FindNextComma(pCommandBuffer + ucOffset) == ATC_FALSE)
            {
                break;
            }

            if(D_MAX_CNT_CID - 1 == i)
            {
                return D_ATC_COMMAND_TOO_MANY_PARAMETERS;
            }
            ucOffset += AtcAp_FindNextComma(pCommandBuffer + ucOffset);
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGCMOD;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

#endif

/*******************************************************************************
  MODULE    : ATC_CEDRXS_LTE_Command
  FORMAT    : AT+CEDRXS=[<mode>,[,<AcT-type>[,<Requested_eDRX_value>]]]  [,<Requested_Paging_time_window>]
  FUNCTION  :<mode>: integer type, indicates to disable or enable the use of eDRX in the UE. This parameter is applicable to all specified types of access technology, i.e. the most recent setting of <mode> will take effect for all specified values of <AcT>.
                0   Disable the use of eDRX
                1   Enable the use of eDRX
                2   Enable the use of eDRX and enable the unsolicited result code
                    +CEDRXP: <AcT-type>[,<Requested_eDRX_value>[,<NW-provided_eDRX_value>[,<Paging_time_window>]]] 
                3   Disable the use of eDRX and discard all parameters for eDRX or, if available, reset to the manufacturer specific default values. 
            <AcT-type>:4    E-UTRAN (WB-S1 mode)
            <Requested_eDRX_value>: string type; half a byte in a 4 bit format. ("0000-1101")
  NOTE      :
  HISTORY   :

*******************************************************************************/
unsigned char ATC_CEDRXS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    unsigned char* puceDRXData = NULL;
    unsigned char* pucPtwValue = NULL;
    unsigned char ucCtr = 0;
    ST_ATC_CEDRXS_PARAMETER*  ptEvent_Param = (ST_ATC_CEDRXS_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CEDRXS_FORMAT, pCommandBuffer,
                                &(ptEvent_Param->ucMode),
                                &(ptEvent_Param->ucActTypeFlg),
                                &(ptEvent_Param->ucActType),
                                &(ptEvent_Param->ucEDRXValueFlag),
                                &(puceDRXData),
                                &(ptEvent_Param->ucPtwValueFlag),
                                &(pucPtwValue)) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(ptEvent_Param->ucEDRXValueFlag == D_ATC_FLAG_TRUE)
        {
            if(strlen(puceDRXData) != 4)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            for (ucCtr = 0; ucCtr < 4; ucCtr++)
            {
                if ((puceDRXData[ucCtr] == '0') || (puceDRXData[ucCtr] == '1'))
                {
                    puceDRXData[ucCtr] = (unsigned char)(puceDRXData[ucCtr] - '0');
                }
                else
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                (ptEvent_Param->ucEDRXValue) |= puceDRXData[ucCtr] << (3 - ucCtr);
            }
        }
        if(ptEvent_Param->ucPtwValueFlag == D_ATC_FLAG_TRUE)
        {
            if(strlen(pucPtwValue) != 4)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            for (ucCtr = 0; ucCtr < 4; ucCtr++)
            {
                if ((pucPtwValue[ucCtr] == '0') || (pucPtwValue[ucCtr] == '1'))
                {
                    pucPtwValue[ucCtr] = (unsigned char)(pucPtwValue[ucCtr] - '0');
                }
                else
                {
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
                (ptEvent_Param->ucPtwValue) |= pucPtwValue[ucCtr] << (3 - ucCtr);
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CEDRXS;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CEDRXS_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CEDRXRDP_LTE_Command
  FORMAT    : AT+CEDRXRDP
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.  tianchengbin   2018.11.26   create
*******************************************************************************/
unsigned char ATC_CEDRXRDP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT*  ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CEDRXRDP;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

#ifndef _FLASH_OPTIMIZE_
/*******************************************************************************
  MODULE    : ATC_CSODCP_LTE_Command
  FORMAT    : AT+CSODCP=<cid>,<cpdata_length>,<cpdata>[,<RAI>[,<type_of_user_data>]]   [,<sequence>]
  FUNCTION  : 
            <cid>: integer type. A numeric parameter which specifies a particular PDP context or EPS bearer context definition. 
                The <cid> parameter is local to the TE-MT interface and 
                identifies the PDP or EPS bearer contexts which have been setup via AT command (see the +CGDCONT and +CGDSCONT commands).
            <cpdata_length>: integer type.(0-1600)
            <cpdata>:HEX (0-1600)*2
            <RAI>:integer type. Indicates the value of the release assistance indication,
                0   No information available.
                1   The MT expects that exchange of datawill be completed with the transmission of the ESM DATA TRANSPORT message.
                2   The MT expects that exchange of data will be completed with the receipt of an ESM DATA TRANSPORT message.
            <type_of_user_data>:integer type. Indicates whether the user data that is transmitted is regular or exceptional.
                0   Regular data.
                1   Exception data.
            [,<sequence>](1-255),not 3GPP parament
                when data send or lost ,Will have URC "+CSODCPR:<cid>,<sequence>,<status>"
  NOTE      :
  HISTORY   :
*******************************************************************************/

unsigned char ATC_CSODCP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CSODCP_PARAMETER* ptEvent_Param = (ST_ATC_CSODCP_PARAMETER*)pEventBuffer;
    unsigned char  *pData = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CSODCP_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucCid,
                            &ptEvent_Param->usCpdataLength,
                            NULL,
                            &pData,
                            &ptEvent_Param->ucRAIFlag,
                            &ptEvent_Param->ucRAI,
                            &ptEvent_Param->ucTUserDataFlag,
                            &ptEvent_Param->ucTUserData,
                            NULL,
                            &ptEvent_Param->ucSequence) != ATC_AP_TRUE)

        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(pData == NULL)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        else
        {
            if((strlen(pData)/2) != ptEvent_Param->usCpdataLength)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
            if(0 == ptEvent_Param->usCpdataLength)
            {
                ptEvent_Param->pucCpdata = NULL;
            }
            else
            {
                ptEvent_Param->pucCpdata = AtcAp_Malloc(ptEvent_Param->usCpdataLength + 1);
                if(Atc_HexStrToHexDigit_LongStr(pData, strlen(pData), ptEvent_Param->pucCpdata) == ATC_AP_FALSE)
                {
                    AtcAp_Free(ptEvent_Param->pucCpdata);
                    return D_ATC_COMMAND_PARAMETER_ERROR;
                }
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CSODCP;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CRTDCP_LTE_Command
  FORMAT    : AT+CRTDCP=[<reporting>]
  FUNCTION  : <reporting>: integer type, controlling reporting of mobile terminated control plane data events
                0   Disable reporting of MT control plane data.
                1   Enable reporting of MT control plane data by the unsolicited result code +CRTDCP.
  NOTE      :
  HISTORY   :

*******************************************************************************/
unsigned char ATC_CRTDCP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CRTDCP_PARAMETER* ptEvent_Param = (ST_ATC_CRTDCP_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CRTDCP_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucReporting) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CRTDCP;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CRTDCP_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CCIOTOPT_LTE_Command
  FORMAT    : AT+CCIOTOPT=[<n>,[<supported_UE_opt>[,<preferred_UE_opt>]]]
  FUNCTION  : 
<n>: integer type. Enables or disables reporting of unsolicited result code +CCIOTOPTI.
    0   Disable reporting.
    1   Enable reporting.
    3   Disable reporting and reset the parameters for CIoT EPS optimization to the default values.
<supported_UE_opt>: integer type; indicates the UE's support for CIoT EPS optimizations.
    0   No support.
    1   Support for control plane CIoT EPS optimization.
    2   Support for user plane CIoT EPS optimization.
    3   Support for both control plane CIoT EPS optimization and user plane CIoT EPS optimization.
<preferred_UE_opt>: integer type; indicates the UE's preference for CIoT EPS optimizations.
    0   No preference.
    1   Preference for control plane CIoT EPS optimization.
    2   Preference for user plane CIoT EPS optimization.
  NOTE      :
  HISTORY   :
      1.  jiangna   2018.11.07.05   create
*******************************************************************************/
unsigned char ATC_CCIOTOPT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CCIOTOPT_PARAMETER* ptEvent_Param = (ST_ATC_CCIOTOPT_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {

        if(AtcAp_CmdParamPrase(D_ATC_CMD_CCIOTOPT_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucN,
                            &ptEvent_Param->ucSuppUeOptFlag,
                            &ptEvent_Param->ucSupportUeOpt,
                            &ptEvent_Param->ucPreUeOptFlag,
                            &ptEvent_Param->ucPreferredUeOpt) != ATC_AP_TRUE
            || (2 == ptEvent_Param->ucN)
            || (1 == ptEvent_Param->ucSupportUeOpt))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CCIOTOPT;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CCIOTOPT_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
  
}


/*******************************************************************************
MODULE    : ATC_CNMPSD_LTE_Command
FORMAT    : AT+CNMPSD
FUNCTION  : 
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_CNMPSD_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT* ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CNMPSD;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
MODULE    : ATC_CACDC_LTE_Command
FORMAT    : +CACDC=<OSid>,<OSappid>,<start-stop-indication>
FUNCTION  : 
NOTE      :
HISTORY   :
*******************************************************************************/
unsigned char ATC_CACDC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CACDC_PARAMETER* ptEvent_Param = (ST_ATC_CACDC_PARAMETER*)pEventBuffer;
    unsigned char *pOsId = NULL;
    unsigned char *pucOsAppId = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CACDC_FORMAT, pCommandBuffer,
                            &pOsId,
                            &pucOsAppId,
                            &ptEvent_Param->ucIndication) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(Atc_HexStrToHexDigit_LongStr(pOsId, strlen(pOsId), ptEvent_Param->aucOsId) == ATC_AP_FALSE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->ucOsAppIdLen = strlen(pucOsAppId);
        AtcAp_MemCpy(ptEvent_Param->aucOsAppId, pucOsAppId, ptEvent_Param->ucOsAppIdLen);
        ptEvent_Param->usEvent = D_ATC_EVENT_CACDC;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}
#endif

/*******************************************************************************
  MODULE    : ATC_CGAPNRC_LTE_Command
  FORMAT    : AT+CGAPNRC[=<cid>]
  FUNCTION  : <cid>: integer type(list of <cid>s associated with active contexts); 
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGAPNRC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CGAPNRC_PARAMETER* ptEvent_Param = (ST_ATC_CGAPNRC_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CID_ONCE_RANGE, pCommandBuffer,
                            &ptEvent_Param->ucCid) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGAPNRC;
        ptEvent_Param->ucCidFlag = 1;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CGAPNRC;
        ptEvent_Param->ucCidFlag = 0;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}
///////////////////////////////sim//////////////////////////////////////////


/*******************************************************************************
  MODULE    : ATC_CIMI_LTE_Command
  FORMAT    : AT+CIMI
  FUNCTION  : 
  NOTE      :
  HISTORY   :

*******************************************************************************/
unsigned char ATC_CIMI_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_COM_EVENT* ptEvent_Param = (ST_ATC_CMD_COM_EVENT*)pEventBuffer;
       
    if (g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CIMI;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CSIM_Command
  FORMAT    : AT+CSIM=<length>,<command>
  FUNCTION  : 
<length>: integer type; length of the characters that are sent to TE in <command> or <response> (two times the actual length of the command or response)
<command>: command passed on by the MT to the SIM in the format as described in 3GPP TS 51.011 [28] 
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CSIM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CSIM_PARAMETER* ptEvent_Param = (ST_ATC_CSIM_PARAMETER*)pEventBuffer;
    unsigned char *pComData = NULL;
    unsigned short usComDataLen = 0;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CSIM_FORMAT, pCommandBuffer,
                            &usComDataLen,
                            &pComData) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(strlen(pComData) % 2 != 0
            || strlen(pComData) != usComDataLen
            || strlen(pComData) < 8 
            || strlen(pComData) > D_ATC_AP_USIM_MAX_APDU_SIZE * 2)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usLength = usComDataLen / 2;
        if(ptEvent_Param->usLength <= sizeof(ptEvent_Param->aucCommand))
        {
            if(Atc_HexStrToHexDigit_LongStr(pComData, strlen(pComData), ptEvent_Param->aucCommand) == ATC_AP_FALSE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        else
        {
            ptEvent_Param->pucCommand = AtcAp_Malloc(ptEvent_Param->usLength + 1);
            if(Atc_HexStrToHexDigit_LongStr(pComData, strlen(pComData), ptEvent_Param->pucCommand) == ATC_AP_FALSE)
            {
                AtcAp_Free(ptEvent_Param->pucCommand);
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CSIM;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

/*******************************************************************************
  MODULE    : ATC_CCHO_Command
  FORMAT    : AT+CCHO=<dfname>
  FUNCTION  : <dfname>: all selectable applications in the UICC are referenced by a DF name coded on 1 to 16 bytes
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CCHO_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CCHO_PARAMETER* ptEvent_Param = (ST_ATC_CCHO_PARAMETER*)pEventBuffer;
    unsigned char  *pDfname = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CCHO_FORMAT, pCommandBuffer, &pDfname) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(strlen(pDfname) % 2 != 0)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        
        ptEvent_Param->ucLength = strlen(pDfname) / 2;
        if(Atc_HexStrToHexDigit_LongStr(pDfname, strlen(pDfname), ptEvent_Param->aucDfName) == ATC_AP_FALSE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CCHO;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

/*******************************************************************************
  MODULE    : ATC_CCHC_Command
  FORMAT    : AT+CCHC=<sessionid>
  FUNCTION  : <sessionid>: integer type; a session Id to be used in order to target a specific application on the smart card using logical channels mechanism
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CCHC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER* ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CCHC_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CCHC;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}

/*******************************************************************************
  MODULE    : ATC_CGLA_Command
  FORMAT    : AT+CGLA=<sessionid>,<length>,<command>
  FUNCTION  : 
  <sessionid>: integer type;
  <length>: integer type;
  <command>: command passed on by the MT to the UICC in the format as described in 3GPP TS 31.101 [65] 
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CGLA_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CGLA_PARAMETER* ptEvent_Param = (ST_ATC_CGLA_PARAMETER*)pEventBuffer;
    unsigned char *pComData = NULL;
    unsigned short usComDatalen = 0;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CGLA_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucSesionId,
                            &usComDatalen,
                            &pComData) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(strlen(pComData) % 2 != 0
            || strlen(pComData) != usComDatalen)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usLength = usComDatalen / 2;
        if(ptEvent_Param->usLength <= sizeof(ptEvent_Param->aucCommand))
        {
            if(Atc_HexStrToHexDigit_LongStr(pComData, usComDatalen, ptEvent_Param->aucCommand) == ATC_AP_FALSE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        else
        {
            ptEvent_Param->pucCommand = AtcAp_Malloc( ptEvent_Param->usLength + 1);
            if(Atc_HexStrToHexDigit_LongStr(pComData, usComDatalen, ptEvent_Param->pucCommand) == ATC_AP_FALSE)
            {
                AtcAp_Free(ptEvent_Param->pucCommand);
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CGLA;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CRSM_LTE_Command
  FORMAT    : AT+CRSM=<command>[,<fileid>[,<P1>,<P2>,<P3>[,<data>[,<pathid>]]]]
  FUNCTION  : 
  NOTE      :
  HISTORY   :
*******************************************************************************/
unsigned char ATC_CRSM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CRSM_PARAMETER* ptEvent_Param = (ST_ATC_CRSM_PARAMETER*)pEventBuffer;
    unsigned char *pData = NULL;
    unsigned char *pPathid = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CRSM_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucCommand,
                            NULL,
                            &ptEvent_Param->ulField,
                            NULL,
                            &ptEvent_Param->ucP1,
                            NULL,
                            &ptEvent_Param->ucP2,
                            NULL,
                            &ptEvent_Param->ucP3,
                            NULL,
                            &pData,
                            NULL,
                            &pPathid) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if((NULL != pPathid)
            && (strlen(pPathid)) % 2 == 0)
        {
            ptEvent_Param->ucPathLen = strlen(pPathid) / 2;
            if(Atc_HexStrToHexDigit_LongStr(pPathid, strlen(pPathid), ptEvent_Param->aucPathId) == ATC_AP_FALSE)
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }

        if(NULL != pData 
            && (strlen(pData)) % 2 == 0 )
        {
            ptEvent_Param->ucDataLen = strlen(pData) / 2;
            ptEvent_Param->pucData = AtcAp_Malloc(ptEvent_Param->ucDataLen + 1);
            if(Atc_HexStrToHexDigit_LongStr(pData, strlen(pData), ptEvent_Param->pucData) == ATC_AP_FALSE)
            {
                AtcAp_Free(ptEvent_Param->pucData);
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CRSM;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CPWD_LTE_Command
  FORMAT    : AT+CPWD=<fac>,<oldpwd>,<newpwd>
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.   tianchengbin   2018.12.18   create
*******************************************************************************/
unsigned char ATC_CPWD_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CPWD_PARAMETER* ptEvent_Param = (ST_ATC_CPWD_PARAMETER*)pEventBuffer;
    unsigned char *pOldPwd = NULL;
    unsigned char *pNewPwd = NULL;
    unsigned char *pFac = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CPWD_FORMAT, pCommandBuffer,
                            &pFac,
                            &pOldPwd,
                            &pNewPwd) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(AtcAp_StrCaseCmp(pFac,"SC") == 0)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->ucFac = D_ATC_PARAM_FAC_SC;
        ptEvent_Param->ucOldPwdLen = strlen(pOldPwd);
        ptEvent_Param->ucNewPwdLen = strlen(pNewPwd);
        AtcAp_MemSet(ptEvent_Param->aucOldPwd, 0xff, D_ATC_P_CLCK_PASSWD_SIZE_MAX);
        AtcAp_MemSet(ptEvent_Param->aucNewPwd, 0xff, D_ATC_P_CLCK_PASSWD_SIZE_MAX);
        if((ptEvent_Param->ucOldPwdLen < 4 || ATC_OK != ATC_PassWordStrCheck(pOldPwd, ptEvent_Param->aucOldPwd))
            ||(ptEvent_Param->ucNewPwdLen < 4 || ATC_OK != ATC_PassWordStrCheck(pNewPwd, ptEvent_Param->aucNewPwd)))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CPWD;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CPIN_LTE_Command
  FORMAT    : AT+CPIN=<pin>[,<newpin>]
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.   tianchengbin   2018.12.18   create
*******************************************************************************/
unsigned char ATC_CPIN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CPIN_PARAMETER* ptEvent_Param = (ST_ATC_CPIN_PARAMETER*)pEventBuffer;
    unsigned char *pOldPwd = NULL;
    unsigned char *pNewPwd = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CPIN_FORMAT, pCommandBuffer,
                            &pOldPwd,
                            &ptEvent_Param->ucNewPinFlg,
                            &pNewPwd) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        AtcAp_MemSet(ptEvent_Param->aucPin, 0xff, D_ATC_P_CLCK_PASSWD_SIZE_MAX);
        AtcAp_MemSet(ptEvent_Param->aucNewPin, 0xff, D_ATC_P_CLCK_PASSWD_SIZE_MAX);
        if(strlen(pOldPwd) < 4 || ATC_OK != ATC_PassWordStrCheck(pOldPwd, ptEvent_Param->aucPin))
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(ptEvent_Param->ucNewPinFlg == 1)
        {
            if(strlen(pNewPwd) < 4 || ATC_OK != ATC_PassWordStrCheck(pNewPwd, ptEvent_Param->aucNewPin))
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CPIN;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CPIN_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CLCK_LTE_Command
  FORMAT    : AT+CLCK=<fac>,<mode>[,<passwd>]
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.   tianchengbin   2018.12.18   create
*******************************************************************************/
unsigned char ATC_CLCK_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CLCK_PARAMETER* ptEvent_Param = (ST_ATC_CLCK_PARAMETER*)pEventBuffer;
    unsigned char *pPwd = NULL;
    unsigned char *pFac = NULL;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CLCK_FORMAT, pCommandBuffer, 
                            &pFac,
                            &ptEvent_Param->ucMode,
                            NULL,
                            &pPwd) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        if(AtcAp_StrCaseCmp(pFac,"SC") == 0)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->ucFac = D_ATC_PARAM_FAC_SC;
        AtcAp_MemSet(ptEvent_Param->aucPassWd, 0xff, D_ATC_P_CLCK_PASSWD_SIZE_MAX);
        if(pPwd != NULL)
        {
            if(strlen(pPwd) != 0
                && ATC_OK == ATC_PassWordStrCheck(pPwd, ptEvent_Param->aucPassWd))
            {
                ptEvent_Param->ucPassWdFlag = D_ATC_FLAG_TRUE;
                ptEvent_Param->ucPassWdLen = strlen(pPwd);
            }
            else
            {
                return D_ATC_COMMAND_PARAMETER_ERROR;
            }
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CLCK;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CPINR_LTE_Command
  FORMAT    : AT+CPINR[=<sel_code>]
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.   tianchengbin   2018.12.18   create
*******************************************************************************/
unsigned char ATC_CPINR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CPINR_PARAMETER* ptEvent_Param = (ST_ATC_CPINR_PARAMETER*)pEventBuffer;
    unsigned char*  pucSelCode = NULL;
    
    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CPINR_FORMAT, pCommandBuffer, &(pucSelCode)) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR; 
        }
        ptEvent_Param->ucLen = strlen(pucSelCode);
        AtcAp_MemCpy(ptEvent_Param->aucSelCode, pucSelCode, ptEvent_Param->ucLen);
        ptEvent_Param->usEvent = D_ATC_EVENT_CPINR;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_ACTIVE)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CPINR;
        ptEvent_Param->ucNoParamValue = 1;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;
}

/*******************************************************************************
  MODULE    : ATC_CEMODE_LTE_Command
  FORMAT    : AT+CEMODE=<mode>
  FUNCTION  : 
  NOTE      :
  HISTORY   :
      1.   tianchengbin   2018.12.18   create
*******************************************************************************/
unsigned char ATC_CEMODE_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer)
{
    ST_ATC_CMD_PARAMETER* ptEvent_Param = (ST_ATC_CMD_PARAMETER*)pEventBuffer;

    if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_REQ)
    {
        if(AtcAp_CmdParamPrase(D_ATC_CMD_CEMODE_FORMAT, pCommandBuffer,
                            &ptEvent_Param->ucValue) != ATC_AP_TRUE)
        {
            return D_ATC_COMMAND_PARAMETER_ERROR;
        }
        ptEvent_Param->usEvent = D_ATC_EVENT_CEMODE;
    }
    else if(g_AtcApInfo.ucATCCmdType == D_ATC_CMD_QUERY)
    {
        ptEvent_Param->usEvent = D_ATC_EVENT_CEMODE_R;
    }
    else
    {
        return D_ATC_COMMAND_SYNTAX_ERROR;
    }
    return D_ATC_COMMAND_OK;

}