#ifndef __ALSA_DEAMON_H__
#define __ALSA_DEAMON_H__

#include <stdint.h>
#include "hal_aud.h"
#include "alsa.h"

typedef enum
{
    POSITION_PING,
    POSITION_PANG,
} alsa_playback_cb_position_t;

typedef enum alsa_stream
{
    ALSA_STREAM_UNKONWN = 0,
    ALSA_STREAM_PLAYBACK,
    ALSA_STREAM_CAPTURE,
    ALSA_STREAM_MAX,
} alsa_stream_t;

typedef uint32_t (*alsa_playback_frome_user_callback_t)(uint8_t *, uint32_t);
typedef uint32_t (*alsa_capture_to_user_callback_t)(uint8_t *, uint32_t);
typedef int (*alsa_pcm_state_changed_callback_t)(alsa_stream_t stream, alsa_pcm_state_t, uint8_t);

#ifdef __cplusplus
extern "C"
{
#endif

    void alsa_deamon_init(alsa_playback_frome_user_callback_t playback_from_user_cb,
                          alsa_capture_to_user_callback_t capture_to_user_cb,
                          alsa_pcm_state_changed_callback_t pcm_state_changed_cb);

    int alsa_deamon_pcm_state_notify(alsa_stream_t stream, alsa_pcm_state_t state, uint8_t user_id);

    /* playback */
    int alsa_deamon_playback_open(uint8_t user_id,
                                  enum AUD_SAMPRATE_T sample_rate,
                                  enum AUD_BITS_T bits,
                                  enum AUD_CHANNEL_NUM_T ch_num);

    int alsa_deamon_playback_start(uint8_t user_id);

    int alsa_deamon_playback_stop(uint8_t user_id, uint8_t immediate);

    int alsa_deamon_playback_close(uint8_t user_id);

    alsa_state_t alsa_deamon_get_playback_state(void);

    alsa_state_t alsa_deamon_get_playback_user_state(uint8_t user_id);

    uint32_t alsa_deamon_get_playback_dma_buffer_size(void);

    int alsa_deamon_playback_cb_lock(void);

    int alsa_deamon_playback_cb_unlock(void);

    int alsa_deamon_underrun_state_notify(alsa_pcm_state_t state, uint8_t alsa_user_id);

    /* capture */
    int alsa_deamon_capture_open(uint8_t user_id,
                                 enum AUD_SAMPRATE_T sample_rate,
                                 enum AUD_BITS_T bits,
                                 enum AUD_CHANNEL_NUM_T ch_num);

    int alsa_deamon_capture_start(uint8_t user_id);

    int alsa_deamon_capture_stop(uint8_t user_id);

    int alsa_deamon_capture_close(uint8_t user_id);

    alsa_state_t alsa_deamon_get_capture_state(void);

    uint8_t alsa_capture_volume_get(void);

    void alsa_capture_volume_update(void);

#ifdef __cplusplus
}
#endif

#endif /* __ALSA_DEAMON_H__ */
