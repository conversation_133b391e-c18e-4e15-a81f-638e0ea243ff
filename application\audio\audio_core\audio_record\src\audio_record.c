#include "cmsis_os2.h"
#include "audio_record.h"
#include "alsa.h"
#include "alsa_config_5.h"
#include "xy_system.h"
#include "xy_fs.h"
#include "interf_enc.h"
#include "alsa_capture.h"

#ifdef XY_AUDIO_RECORD

// 流式录音缓冲区大小
#define AUDIO_REC_STREAM_BUFFER_SIZE         (32*1024)
// 录音到buffer的buffer最小大小，不足时依此大小为单位扩大
#define AUDIO_REC_BUFFER_SIZE         (16*1024)

static osThreadId_t rec_thread_handler = NULL;
static osMessageQueueId_t rec_msg = NULL;
static alsa_capture_t * rec_alsa_handle = NULL;

static rec_state_t audio_rec_state;
static int8_t audio_rec_sleep_lock = -1;

static void audio_rec_state_set(rec_state_t rec_state);
static rec_state_t audio_rec_state_get(void);

static osMutexId_t stream_mutex_lock = NULL;

extern int32_t audio_encode_pcm_process(uint8_t * data, uint32_t len, audio_rec_t *audio_rec);
extern int32_t audio_encode_amr_process(uint8_t * data, uint32_t len, audio_rec_t *audio_rec);

static audio_encode_t audio_encode[REC_FORMAT_MAX] = {
    [REC_FORMAT_PCM] = {.process = audio_encode_pcm_process},
#if ENCODER_SUPPORT_AMR
    [REC_FORMAT_AMR] = {.process = audio_encode_amr_process},
#endif
};
static audio_encoder_process_t audio_encode_process;

static rec_stream_t * rec_stream = NULL;
static audio_rec_req_t rec_req;

static int32_t write_dest(void *buf, uint32_t size, dest_arg_t * dest_arg);

static dest_arg_t audio_dest_arg;
static audio_dest_t audio_dest = {
    .write = write_dest,
    .dest_arg = &audio_dest_arg,
};

static audio_rec_t audio_rec = {
    .dest = &audio_dest,
    .state_get = audio_rec_state_get,
};

static int capture_callback(uint8_t *data, uint32_t length)
{
	uint32_t temp_length;
    uint16_t * result_buffer;
    uint16_t * temp_data;

    temp_length = length / 2;
    result_buffer = (uint16_t*)xy_malloc(temp_length);
    temp_data = (uint16_t*)data;

    for(uint32_t i = 0; i < temp_length/2; i++)
    {
        result_buffer[i] = *temp_data;
        temp_data += 2;  //跳过右声道的数据
    }

	rec_msg_t  msg = {
		.pcm_data = result_buffer,
        .pcm_data_len = temp_length,
        };

	osMessageQueuePut(rec_msg, &msg, 0, 0);

	return 0;
}

static void audio_record_thread(void *arg)
{
    rec_msg_t msg = {0};
    int ret = REC_ERR_OK;

    while (1) 
    {
        if(osMessageQueueGet(rec_msg, &msg, NULL, osWaitForever) != osOK)
        {
            continue;
        }

        if(audio_rec_state_get() != REC_STOPED)
        {
            ret = audio_encode_process(msg.pcm_data, msg.pcm_data_len, &audio_rec);
            if(ret != REC_ERR_OK) // 目的容器写满、用户主动停止录音或者编码相关错误，停止录音
            {
                audio_rec_state_set(REC_STOPED);
                if((ret == REC_ERR_DEST_FULL) || (ret == REC_ERR_STOP)) // 目的容器写满或者用户主动停止录音，不上报错误码
                {
                    ret = REC_ERR_OK;
                }
            }
        }

        xy_free(msg.pcm_data);

        if(audio_rec_state_get() == REC_STOPED)
        {
            audio_rec_close();
            if(rec_req.complete_cb != NULL)
            {
                rec_req.complete_cb(audio_rec.dest->dest_arg->write_len, audio_rec.dest->dest_arg->real_duration_ms, ret);
                rec_req.complete_cb = NULL;
            }
        }
    }
}

static int32_t write_dest(void *buf, uint32_t size, dest_arg_t *dest_arg)
{
    int32_t len = 0;

    switch (dest_arg->type)
    {
        case AUDIO_DEST_TYPE_BUFFER:
            if(size > (dest_arg->dest_len - dest_arg->write_len))
            {
                uint8_t * buf_tmp;
                dest_arg->dest_len += AUDIO_REC_BUFFER_SIZE;
                buf_tmp = (uint8_t*)xy_malloc(dest_arg->dest_len);
                memcpy(buf_tmp, *(dest_arg->u.buf), dest_arg->write_len);
                xy_free(*(dest_arg->u.buf));
                *(dest_arg->u.buf) = buf_tmp;
            }
            memcpy(*(dest_arg->u.buf) + dest_arg->write_len, buf, size);
            dest_arg->write_len += size;
            len = size;
            break;
        case AUDIO_DEST_TYPE_FILE:
            len = xy_fwrite(buf, 1, size, dest_arg->u.file);
            debug_assert(len >= 0);
            if(len > 0)
            {
                dest_arg->write_len += len;
            }
            break;
        case AUDIO_DEST_TYPE_STREAM:
            osMutexAcquire(stream_mutex_lock, osWaitForever);
            len = kfifo_put(dest_arg->u.stream, buf, size);
            osMutexRelease(stream_mutex_lock);
            dest_arg->write_len += len;
            break;
        default:
            xy_assert(0);
            break;
    }

    return len;
}

int32_t audio_rec_req(audio_rec_req_t * req)
{
    int32_t ret;
    rec_format_t format;

    memcpy(&rec_req, req, sizeof(audio_rec_req_t));

    audio_rec.enc = rec_req.enc;

    switch (req->type)
    {
        case AUDIO_REQ_FILE_REC:
            audio_dest.dest_arg->type = AUDIO_DEST_TYPE_FILE;
            audio_dest.dest_arg->u.file = xy_fopen(req->u.file, "w");
            if(audio_dest.dest_arg->u.file == NULL)
            {
                ret = REC_ERR_FOPEN_FAILED;
                goto exit;
            }
            break;
        case AUDIO_REQ_BUFFER_REC:
            audio_dest.dest_arg->type = AUDIO_DEST_TYPE_BUFFER;
            audio_dest.dest_arg->u.buf = req->u.buf;
            break;
        case AUDIO_REQ_STREAM_REC:
            audio_dest.dest_arg->type = AUDIO_DEST_TYPE_STREAM;
            audio_dest.dest_arg->u.stream = xy_malloc(sizeof(rec_stream_t));
            kfifo_init(audio_dest.dest_arg->u.stream, xy_malloc(AUDIO_REC_STREAM_BUFFER_SIZE), AUDIO_REC_STREAM_BUFFER_SIZE);

            if(stream_mutex_lock == NULL)
            {
                stream_mutex_lock = osMutexNew(NULL);
            }

            break;
        default:
            xy_assert(0);
            break;
    }

    switch (req->enc)
    {
        case REC_ENC_PCM:
            format = REC_FORMAT_PCM;
            break;
        case REC_ENC_MP3: // not supported yet
            xy_assert(0);
            break;
        case REC_ENC_AMR475:
        case REC_ENC_AMR515:
        case REC_ENC_AMR59:
        case REC_ENC_AMR67:
        case REC_ENC_AMR74:
        case REC_ENC_AMR795:
        case REC_ENC_AMR102:
        case REC_ENC_AMR122:
        case REC_ENC_AMRDTX:
            format = REC_FORMAT_AMR;
            break;
        default:
            xy_assert(0);
            break;
    }

    if(req->duration_ms == 0)
    {
        audio_dest.dest_arg->duration_ms = 0xffffffff;
    }
    else
    {
        audio_dest.dest_arg->duration_ms = req->duration_ms;
    }
    audio_dest.dest_arg->write_len = 0;
    audio_dest.dest_arg->real_duration_ms = 0;

    if(req->type == AUDIO_REQ_BUFFER_REC)
    {
        *(audio_dest.dest_arg->u.buf) = (uint8_t*)xy_malloc(AUDIO_REC_BUFFER_SIZE);
        audio_dest.dest_arg->dest_len = AUDIO_REC_BUFFER_SIZE;
    }

    audio_encode_process = audio_encode[format].process;
    xy_assert(audio_encode_process != NULL);

    ret = audio_rec_open(req->sample_rate);
    if(ret != REC_ERR_OK)
    {
        goto exit;
    }
    ret = audio_rec_start();
    if(ret != REC_ERR_OK)
    {
        goto exit;
    }

    return ret;

exit:

    audio_rec_stop();
    audio_rec_close();

    return ret;
}

uint32_t audio_rec_stream_get(uint8_t * buf, uint32_t len)
{
    uint32_t ret = 0;
    if((audio_dest.dest_arg->type == AUDIO_DEST_TYPE_STREAM) && (audio_dest.dest_arg->u.stream != NULL))
    {
        osMutexAcquire(stream_mutex_lock, osWaitForever);
        ret = kfifo_get(audio_dest.dest_arg->u.stream, buf, len);
        osMutexRelease(stream_mutex_lock);
    }

    return ret;
}

int32_t audio_rec_open(uint32_t sample_rate)
{
    int32_t ret = REC_ERR_OK;

    osThreadAttr_t attr = {0};

    if(audio_rec_sleep_lock == -1)
    {
        audio_rec_sleep_lock = create_sleep_lock("audio_rec");
    }
    
    if(audio_rec_sleep_lock != -1)
    {
        sleep_lock(audio_rec_sleep_lock, WL_ALL|WL_WFI);
    }
    else
    {
        xy_assert(0);
    }

	rec_alsa_handle = alsa_capture_open(sample_rate, 2, 16);
    if(rec_alsa_handle == NULL)
    {
        ret = REC_ERR_ALSA_OPEN_FAILED;
        goto exit;
    }

	alsa_capture_set_data_pull_callback(rec_alsa_handle, capture_callback);
    if(rec_msg == NULL)
    {
        rec_msg = osMessageQueueNew(32, sizeof(rec_msg_t), NULL);
        if(rec_msg == NULL)
        {
            ret = REC_ERR_MSGQ_CREAT_FAILED;
            goto exit;
        }
    }

    if(rec_thread_handler == NULL)
    {
        attr.name = "audio_rec";
        attr.stack_size = 0x2000;
        attr.priority = osPriorityNormal;

        rec_thread_handler = osThreadNew(audio_record_thread, NULL, &attr);
        if(rec_thread_handler == NULL)
        {
            ret = REC_ERR_REC_THREAD_CREAT_FAILED;
            goto exit;
        }
    }

    return REC_ERR_OK;

exit:
    if(rec_alsa_handle)
    {
        alsa_capture_close(rec_alsa_handle);
        rec_alsa_handle = NULL;
    }
    if(audio_rec_sleep_lock != -1)
    {
        sleep_unlock(audio_rec_sleep_lock, WL_ALL|WL_WFI);
    }
    return ret;
}

int audio_rec_start(void)
{
    int ret = REC_ERR_OK;

    audio_rec_state_set(REC_START);

    if(rec_alsa_handle)
    {
        if(alsa_capture_start(rec_alsa_handle))
        {
            ret = REC_ERR_ALSA_START_FAILED;
        }
    }
    else
    {
        ret = REC_ERR_ALSA_NOT_OPEN;
    }

    return ret;
}

int audio_rec_close(void)
{
    if(rec_alsa_handle)
    {
        alsa_capture_stop(rec_alsa_handle);
        alsa_capture_close(rec_alsa_handle);
        rec_alsa_handle = NULL;
    }

    if(audio_dest.dest_arg->type == AUDIO_DEST_TYPE_FILE)
    {
        if(audio_dest.dest_arg->u.file)
        {
            xy_fclose(audio_dest.dest_arg->u.file);
            audio_dest.dest_arg->u.file = NULL;
        }
    }
    else if(audio_dest.dest_arg->type == AUDIO_DEST_TYPE_STREAM)
    {
        osMutexAcquire(stream_mutex_lock, osWaitForever);
        if(audio_dest.dest_arg->u.stream)
        {
            xy_free((audio_dest.dest_arg->u.stream)->buffer);
            xy_free(audio_dest.dest_arg->u.stream);
            audio_dest.dest_arg->u.stream = NULL;
        }
        osMutexRelease(stream_mutex_lock);
    }

    audio_rec_state_set(REC_IDLE);

    if(audio_rec_sleep_lock != -1)
    {
        sleep_unlock(audio_rec_sleep_lock, WL_ALL|WL_WFI);
    }
    return REC_ERR_OK;
}

static void audio_rec_state_set(rec_state_t state)
{
    osCoreEnterCritical();
    audio_rec_state = state;
    osCoreExitCritical();
}

static rec_state_t audio_rec_state_get(void)
{
    rec_state_t state;
    osCoreEnterCritical();
    state = audio_rec_state;
    osCoreExitCritical();

    return state;
}

void audio_rec_pause(void)
{
    osCoreEnterCritical();
    if(audio_rec_state == REC_START)
    {
        audio_rec_state = REC_PAUSE;
    }
    osCoreExitCritical();
}

void audio_rec_resume(void)
{
    osCoreEnterCritical();
    if(audio_rec_state == REC_PAUSE)
    {
        audio_rec_state = REC_START;
    }
    osCoreExitCritical();
}

void audio_rec_stop(void)
{
    audio_rec_state_set(REC_STOP);
}

#endif  // XY_AUDIO_RECORD