/** 
* @file         xy_atc_interface.h
* @brief        该头文件给出的是3GPP相关的AT命令主动上报对应的结构体。\n
***  3GPP相关AT命令处理接口，包括两个部分，一个是主动上报类消息的处理机制xy_atc_registerPSEventCallback；\n
***  另一个是用户触发的AT请求类消息的处理xy_atc_interface_call
* @attention    3GPP细节较多，用户二次开发时请务必确认清楚后再执行相关AT的修改
*
*/

#ifndef _ATC_AP_INTERFACE_H_
#define _ATC_AP_INTERFACE_H_
#if IS_DSP_CORE
#include "pscfg.h"
#endif
#include "factory_nv.h"

//#define HW_LM_SUPPORT
#ifdef HW_LM_SUPPORT
#include "xy_hw_lm_interface.h"
#endif

#if (XY4100LD || XY4100LDFE)
#else
#define SIMULATOR_UICC
#endif

#define LTE_SMS_FEATURE

//these macros are only used for AtcAp
//#define CSG_FEATURE

#define _FLASH_OPTIMIZE_

/* For Result */
#define D_RESULT_SUCCESS        0
#define D_RESULT_FAILURE        1

//#define  D_MAX_ATCID                            11
//#define  D_MAX_CNT_CID                          4

#define  D_ATC_MAX_CID                          16 // can't be less than D_MAX_ATCID

#define D_ATC_MAX_PACKET_FILTER                 16
#define D_MAX_PARSED_HEADER_EBID                D_ATC_MAX_PACKET_FILTER

#define D_APN_LEN                               100
#define D_ATC_NBAND_MAX_BNAD_NUM                23

#define D_EPS_IMEI_LEN                          15
#define EMM_SIM_UICC_ID_LEN                     10
#define D_ATC_CRTDCP_IND_LENGTH                 26
#define D_ATC_NRNPDM_IND_LENGTH                 26
#define D_SMS_DA_SIZE_MAX                       12
#define D_SMS_CMGS_INPUT_TEXT_SIZE_MAX          2048

#define D_ATC_P_CSCA_IND_SCA_SIZE_MAX           22
#define D_ATC_P_CDA_IND_DA_SIZE_MAX             22
#define D_ATC_P_CSMP_IND_VP_SIZE_MAX            20

#define D_SMS_SCA_SIZE_MAX                        12

#define D_ATC_ADDR_MAX_SIZE                   (20+1)
#define D_ATC_REPORT_ADDR_MAX_SIZE            (D_ATC_ADDR_MAX_SIZE+1)

#define EPS_CSODCP_CPDATA_LENGTH_MAX            1600

#define D_ATC_NONIP_DATA_LEN_MAX                1358

#define D_ATC_P_CPWD_OLDPWD_SIZE_MAX            8                                       /* +CPWD:old pwd max length                 */
#define D_ATC_P_CPWD_NEWPWD_SIZE_MAX            8                                       /* +CPWD:new pwd max length                 */
#define D_ATC_P_CPIN_PIN_SIZE_MAX               8                                       /* +CPIN:pin max size                       */
#define D_ATC_P_CPIN_NEWPIN_SIZE_MAX            8                                       /* +CPIN:newpin max size                    */
#define D_ATC_P_CLCK_PASSWD_SIZE_MAX            8                                       /* +CLCK:<passwd>                           */
#define D_ATC_P_NSET_INS_SIZE_MAX               20
#define D_ATC_P_SIMUUICC_INS_SIZE_MAX           20
#define D_ATC_AP_USIM_MAX_APDU_SIZE             261

#define D_ATC_SIMUUICC_IMSI_SIZE_MAX           15
#define NAS_AUTH_KEY_LTH   16
#define NAS_AUTH_OP_LTH    16

#define D_ATC_SIMUUICC_FILE_ID_MAX                4
#define D_ATC_SIMUUICC_FILE_CONTENT_MAX           32

#define D_ATC_PARAM_FAC_SC                      0                                       /* <fac>: "SC"                              */
#define D_ATC_PARAM_FAC_AO                      1                                       /* <fac>: "AO"                              */
#define D_ATC_PARAM_FAC_OI                      2                                       /* <fac>: "OI"                              */
#define D_ATC_PARAM_FAC_OX                      3                                       /* <fac>: "OX"                              */
#define D_ATC_PARAM_FAC_AI                      4                                       /* <fac>: "AI"                              */
#define D_ATC_PARAM_FAC_IR                      5                                       /* <fac>: "IR"                              */
#define D_ATC_PARAM_FAC_PS                      6                                       /* <fac>: "PS"                              */
#define D_ATC_PARAM_FAC_PN                      7                                       /* <fac>: "PN"                              */
#define D_ATC_PARAM_FAC_PU                      8                                       /* <fac>: "PU"                              */
#define D_ATC_PARAM_FAC_PP                      9                                       /* <fac>: "PP"                              */
#define D_ATC_PARAM_FAC_PC                      10                                      /* <fac>: "PC"                              */
#define D_ATC_PARAM_FAC_FD                      11                                      /* <fac>: "FD"                              */
#define D_ATC_PARAM_FAC_P2                      12                                        /* <fac>: "P2"                              */
#define D_ATC_PARAM_FAC_AB                      13                                        /* <fac>: "AB"                              */
#define D_ATC_PARAM_FAC_AG                      14                                        /* <fac>: "AG"                              */
#define D_ATC_PARAM_FAC_AC                      15                                        /* <fac>: "AC"                              */
#define D_ATC_PARAM_FAC_CS                      16                                      /* <fac>: "CS"                              */
#define D_ATC_PARAM_FAC_PF                      17                                      /* <fac>: "PF"                              */
#define D_ATC_PARAM_FAC_NT                      18                                      /* <fac>: "NT"                              */
#define D_ATC_PARAM_FAC_NM                      19                                      /* <fac>: "NM"                              */
#define D_ATC_PARAM_FAC_NS                      20                                      /* <fac>: "NS"                              */
#define D_ATC_PARAM_FAC_NA                      21                                      /* <fac>: "NA"                              */

#define D_ATC_DL_GBR_MAX                        10000000
#define D_ATC_UL_GBR_MAX                        10000000
#define D_ATC_DL_MBR_MAX                        10000000
#define D_ATC_UL_MBR_MAX                        10000000

/* local flag */                             
#define D_ATC_FLAG_FALSE                     0
#define D_ATC_FLAG_TRUE                      1

#define D_ATC_CGEV_NW_DETACH                0
#define D_ATC_CGEV_ME_DETACH                1
#define D_ATC_CGEV_ME_PDN_ACT               2
#define D_ATC_CGEV_NW_ACT                   3
#define D_ATC_CGEV_ME_ACT                   4
#define D_ATC_CGEV_NW_PDN_DEACT             5
#define D_ATC_CGEV_ME_PDN_DEACT             6
#define D_ATC_CGEV_NW_DEACT                 7
#define D_ATC_CGEV_ME_DEACT                 8
#define D_ATC_CGEV_NW_MODIFY                9
#define D_ATC_CGEV_ME_MODIFY                10
#define D_ATC_CGEV_OOS                      11
#define D_ATC_CGEV_IS                       12
#define D_ATC_CGEV_MAX                      16

/* register PS eventId */
#ifndef PS_REG_EVENT
#define PS_REG_EVENT
#define D_XY_PS_REG_EVENT_SIMST              0x00000001 //ATC_MSG_SIMST_IND_STRU
#define D_XY_PS_REG_EVENT_XYIPDNS            0x00000002 //ATC_MSG_XYIPDNS_IND_STRU
#define D_XY_PS_REG_EVENT_CRTDCP             0x00000004 //ATC_MSG_CRTDCP_IND_STRU
#define D_XY_PS_REG_EVENT_CGAPNRC            0x00000008 //ATC_MSG_CGAPNRC_IND_STRU
#define D_XY_PS_REG_EVENT_CGEV               0x00000010 //ATC_MSG_CGEV_IND_STRU
#define D_XY_PS_REG_EVENT_CEREG              0x00000020 //ATC_MSG_CEREG_IND_STRU
#define D_XY_PS_REG_EVENT_CSCON              0x00000040 //ATC_MSG_CSCON_IND_STRU
#define D_XY_PS_REG_EVENT_NPTWEDRXP          0x00000080 //ATC_MSG_NPTWEDRXP_IND_STRU
#define D_XY_PS_REG_EVENT_CEDRXP             0x00000100 //ATC_MSG_CEDRXP_IND_STRU
#define D_XY_PS_REG_EVENT_CCIOTOPTI          0x00000200 //ATC_MSG_CCIOTOPTI_IND_STRU
#define D_XY_PS_REG_EVENT_CESQ_Ind           0x00000400 //ATC_MSG_CESQ_IND_STRU, Report RSRP/SINR every 10 seconds
#define D_XY_PS_REG_EVENT_LOCALTIMEINFO      0x00000800 //ATC_MSG_LOCALTIMEINFO_IND_STRU
#define D_XY_PS_REG_EVENT_PDNIPADDR          0x00001000 //ATC_MSG_PdnIPAddr_IND_STRU
#define D_XY_PS_REG_EVENT_NGACTR             0x00002000 //ATC_MSG_NGACTR_IND_STRU
#define D_XY_PS_REG_EVENT_CMT                0x00004000 //ATC_MSG_CMT_IND_STRU
#define D_XY_PS_REG_EVENT_CMOLRE             0x00008000 //ATC_MSG_CMOLRE_IND_STRU
#define D_XY_PS_REG_EVENT_CMOLRG             0x00010000 //ATC_MSG_CMOLRG_IND_STRU
#define D_XY_PS_REG_EVENT_NRNPDM             0x01000000 //ATC_MSG_NRNPDM_IND_STRU
#define D_XY_PS_REG_EVENT_MNBIOTEVENT        0x02000000 //ATC_MSG_MNBIOTEVENT_IND_STRU
#define D_XY_PS_REG_EVENT_CMTI               0x04000000 //ATC_MSG_CMTI_IND_STRU
#define D_XY_PS_REG_EVENT_CDS                0x08000000 //ATC_MSG_CDS_IND_STRU
#define D_XY_PS_REG_EVENT_HW_LM_INTERFACE    0x10000000 //LM_INTERFACE_IND_STRU
#define D_XY_PS_REG_EVENT_CDSI               0x20000000 //ATC_MSG_CDSI_IND_STRU
#define D_XY_PS_REG_EVENT_WIFISCAN           0x40000000 //ATC_MSG_WIFISCAN_IND_STRU
//#define D_XY_PS_REG_EVENT_QIACTEX            0x60000000 //ATC_MSG_QIACTEX_IND_STRU
//#define D_XY_PS_REG_EVENT_QIDEACTEX          0x80000000 //ATC_MSG_QIDEACTEX_IND_STRU

#endif
/* XY internal use only */
#define D_XY_PS_REG_EVENT_PSINFO             0x00020000 //ATC_MSG_PSINFO_IND_STRU
#define D_XY_PS_REG_EVENT_PINSTATUS          0x00040000 //ATC_MSG_PIN_STATUS_IND_STRU
#define D_XY_PS_REG_EVENT_CELLSRCH           0x00080000 //ATC_MSG_CELLSRCH_TEST_STRU
#define D_XY_PS_REG_EVENT_L2_THP             0x00100000 //ATC_MSG_L2_THP_IND_STRU
#define D_XY_PS_REG_EVENT_MALLOC_ADDR        0x00200000 //ATC_MSG_MALLOC_ADDR_IND_STRU
#define D_XY_PS_REG_EVENT_NoCarrier          0x00400000 //ATC_MSG_NO_CARRIER_IND_STRU
#define D_XY_PS_REG_EVENT_IPSN               0x00800000 //ATC_MSG_IPSN_IND_STRU

/* ATC command event table  */
/* Basic */
enum {
    D_ATC_EVENT_CGSN=1,                 /* 1 */
    D_ATC_EVENT_CGSN_T,                 /* 2 */
    D_ATC_EVENT_CEREG,                  /* 3 */
    D_ATC_EVENT_CEREG_R,                /* 4 */
    D_ATC_EVENT_CEREG_T,                /* 5 */
    D_ATC_EVENT_CGATT,                  /* 6 */
    D_ATC_EVENT_CGATT_R,                /* 7 */
    D_ATC_EVENT_CGATT_T,                /* 8 */
    D_ATC_EVENT_CIMI,                   /* 9 */
    D_ATC_EVENT_CIMI_T,                 /* 10 */
    D_ATC_EVENT_CGDCONT,                /* 11 */
    D_ATC_EVENT_CGDCONT_R,              /* 12 */
    D_ATC_EVENT_CGDCONT_T,              /* 13 */
    D_ATC_EVENT_CFUN,                   /* 14 */
    D_ATC_EVENT_CFUN_R,                 /* 15 */
    D_ATC_EVENT_CFUN_T,                 /* 16 */
    D_ATC_EVENT_CMEE,                   /* 17 */
    D_ATC_EVENT_CMEE_R,                 /* 18 */
    D_ATC_EVENT_CMEE_T,                 /* 19 */
    D_ATC_EVENT_CLAC,                   /* 20 */
    D_ATC_EVENT_CLAC_T,                 /* 21 */
    D_ATC_EVENT_CESQ,                   /* 22 */
    D_ATC_EVENT_CESQ_T,                 /* 23 */
    D_ATC_EVENT_CGPADDR,                /* 24 */
    D_ATC_EVENT_CGPADDR_T,              /* 25 */
    D_ATC_EVENT_CGACT,                  /* 26 */
    D_ATC_EVENT_CGACT_R,                /* 27 */
    D_ATC_EVENT_CGACT_T,                /* 28 */
    D_ATC_EVENT_CSODCP,                 /* 29 */
    D_ATC_EVENT_CSODCP_T,               /* 30 */
    D_ATC_EVENT_CRTDCP,                 /* 31 */
    D_ATC_EVENT_CRTDCP_R,               /* 32 */
    D_ATC_EVENT_CRTDCP_T,               /* 33 */
    D_ATC_EVENT_SIMST_R,                /* 34 */
    D_ATC_EVENT_CEDRXS,                 /* 35 */
    D_ATC_EVENT_CEDRXS_R,               /* 36 */
    D_ATC_EVENT_CEDRXS_T,               /* 37 */
    D_ATC_EVENT_CPSMS,                  /* 38 */
    D_ATC_EVENT_CPSMS_R,                /* 39 */
    D_ATC_EVENT_CPSMS_T,                /* 40 */
    D_ATC_EVENT_CGAPNRC,                /* 41 */
    D_ATC_EVENT_CGAPNRC_T,              /* 42 */
    D_ATC_EVENT_CGDSCONT,               /* 43 */
    D_ATC_EVENT_CGDSCONT_R,             /* 44 */
    D_ATC_EVENT_CGDSCONT_T,             /* 45 */
    D_ATC_EVENT_CGTFT,                  /* 46 */
    D_ATC_EVENT_CGTFT_R,                /* 47 */
    D_ATC_EVENT_CGTFT_T,                /* 48 */
    D_ATC_EVENT_CGEQOS,                 /* 49 */
    D_ATC_EVENT_CGEQOS_R,               /* 50 */
    D_ATC_EVENT_CGEQOS_T,               /* 51 */
    D_ATC_EVENT_CGCMOD,                 /* 52 */
    D_ATC_EVENT_CGCMOD_T,               /* 53 */
    D_ATC_EVENT_CSMS,                   /* 54 */
    D_ATC_EVENT_CSMS_R,                 /* 55 */
    D_ATC_EVENT_CSMS_T,                 /* 56 */
    D_ATC_EVENT_CMGF,                   /* 57 */
    D_ATC_EVENT_CMGF_R,                 /* 58 */
    D_ATC_EVENT_CMGF_T,                 /* 59 */
    D_ATC_EVENT_CPMS,                   /* 60 */
    D_ATC_EVENT_CPMS_R,                 /* 61 */
    D_ATC_EVENT_CPMS_T,                 /* 62 */
    D_ATC_EVENT_CSCA,                   /* 63 */
    D_ATC_EVENT_CSCA_R,                 /* 64 */
    D_ATC_EVENT_CSCA_T,                 /* 65 */
    D_ATC_EVENT_CSMP,                   /* 66 */
    D_ATC_EVENT_CSMP_R,                 /* 67 */
    D_ATC_EVENT_CSMP_T,                 /* 68 */
    D_ATC_EVENT_CNMI,                   /* 69 */
    D_ATC_EVENT_CNMI_R,                 /* 70 */
    D_ATC_EVENT_CNMI_T,                 /* 71 */
    D_ATC_EVENT_CMGW,                   /* 72 */
    D_ATC_EVENT_CMGW_T,                 /* 73 */
    D_ATC_EVENT_CMGR,                   /* 74 */
    D_ATC_EVENT_CMGR_T,                 /* 75 */
    D_ATC_EVENT_CMGD,                   /* 76 */
    D_ATC_EVENT_CMGD_T,                 /* 77 */
    D_ATC_EVENT_CMGL,                   /* 78 */
    D_ATC_EVENT_CMGL_T,                 /* 79 */
    D_ATC_EVENT_CMSS,                   /* 80 */
    D_ATC_EVENT_CMSS_T,                 /* 81 */
    D_ATC_EVENT_CMGS,                   /* 82 */
    D_ATC_EVENT_CMGS_T,                 /* 83 */
    D_ATC_EVENT_CNMA,                   /* 84 */
    D_ATC_EVENT_CNMA_T,                 /* 85 */
    D_ATC_EVENT_COPS,                   /* 86 */
    D_ATC_EVENT_COPS_R,                 /* 87 */
    D_ATC_EVENT_COPS_T,                 /* 88 */
    D_ATC_EVENT_CSIM,                   /* 89 */
    D_ATC_EVENT_CSIM_T,                 /* 90 */
    D_ATC_EVENT_CCHC,                   /* 91 */
    D_ATC_EVENT_CCHC_T,                 /* 92 */
    D_ATC_EVENT_CCHO,                   /* 93 */
    D_ATC_EVENT_CCHO_T,                 /* 94 */
    D_ATC_EVENT_CGLA,                   /* 95 */
    D_ATC_EVENT_CGLA_T,                 /* 96 */
    D_ATC_EVENT_CRSM,                   /* 97 */
    D_ATC_EVENT_CRSM_T,                 /* 98 */
    D_ATC_EVENT_CSCON,                  /* 99 */
    D_ATC_EVENT_CSCON_R,                /* 100 */
    D_ATC_EVENT_CSCON_T,                /* 101 */
    D_ATC_EVENT_CGEREP,                 /* 102 */
    D_ATC_EVENT_CGEREP_R,               /* 103 */
    D_ATC_EVENT_CGEREP_T,               /* 104 */
    D_ATC_EVENT_CCIOTOPT,               /* 105 */
    D_ATC_EVENT_CCIOTOPT_R,             /* 106 */
    D_ATC_EVENT_CCIOTOPT_T,             /* 107 */
    D_ATC_EVENT_CEDRXRDP,               /* 108 */
    D_ATC_EVENT_CEDRXRDP_T,             /* 109 */
    D_ATC_EVENT_CGEQOSRDP,              /* 110 */
    D_ATC_EVENT_CGEQOSRDP_T,            /* 111 */
    D_ATC_EVENT_CTZR,                   /* 112 */
    D_ATC_EVENT_CTZR_R,                 /* 113 */
    D_ATC_EVENT_CTZR_T,                 /* 114 */
    D_ATC_EVENT_CGCONTRDP,              /* 115 */
    D_ATC_EVENT_CGCONTRDP_T,            /* 116 */
    D_ATC_EVENT_CPIN,                   /* 117 */
    D_ATC_EVENT_CPIN_R,                 /* 118 */
    D_ATC_EVENT_CPIN_T,                 /* 119 */
    D_ATC_EVENT_CLCK,                   /* 120 */
    D_ATC_EVENT_CLCK_T,                 /* 121 */
    D_ATC_EVENT_CPWD,                   /* 122 */
    D_ATC_EVENT_CPWD_T,                 /* 123 */
    D_ATC_EVENT_NUESTATS,               /* 124 */
    D_ATC_EVENT_NUESTATS_T,             /* 125 */
    D_ATC_EVENT_NEARFCN,                /* 126 */
    D_ATC_EVENT_NEARFCN_R,              /* 127 */
    D_ATC_EVENT_NEARFCN_T,              /* 128 */
    D_ATC_EVENT_NBAND,                  /* 129 */
    D_ATC_EVENT_NBAND_R,                /* 130 */
    D_ATC_EVENT_NBAND_T,                /* 131 */
    D_ATC_EVENT_NCONFIG,                /* 132 */
    D_ATC_EVENT_NCONFIG_R,              /* 133 */
    D_ATC_EVENT_NCONFIG_T,              /* 134 */
    D_ATC_EVENT_NCCID,                  /* 135 */
    D_ATC_EVENT_NCCID_T,                /* 136 */
    D_ATC_EVENT_NCSEARFCN,              /* 137 */
    D_ATC_EVENT_RAI,                    /* 138 */
    D_ATC_EVENT_NFPLMN,                 /* 139 */
    D_ATC_EVENT_NL2THP,                 /* 140 */
    D_ATC_EVENT_NL2THP_R,               /* 141 */
    D_ATC_EVENT_NL2THP_T,               /* 142 */
    D_ATC_EVENT_CSQ,                    /* 143 */
    D_ATC_EVENT_CSQ_T,                  /* 144 */
    D_ATC_EVENT_NSET,                   /* 145 */
    D_ATC_EVENT_NSET_R,                 /* 146 */
    D_ATC_EVENT_CMOLR,                  /* 147 */
    D_ATC_EVENT_CMOLR_R,                /* 148 */
    D_ATC_EVENT_CMOLR_T,                /* 149 */
    D_ATC_EVENT_CEER,                   /* 150 */
    D_ATC_EVENT_CEER_T,                 /* 151 */
    D_ATC_EVENT_CIPCA,                  /* 152 */
    D_ATC_EVENT_CIPCA_R,                /* 153 */
    D_ATC_EVENT_CIPCA_T,                /* 154 */
    D_ATC_EVENT_CGAUTH,                 /* 155 */
    D_ATC_EVENT_CGAUTH_R,               /* 156 */
    D_ATC_EVENT_CGAUTH_T,               /* 157 */
    D_ATC_EVENT_CNMPSD,                 /* 158 */
    D_ATC_EVENT_CNMPSD_T,               /* 159 */
    D_ATC_EVENT_CPINR,                  /* 160 */
    D_ATC_EVENT_CPINR_T,                /* 161 */
    D_ATC_EVENT_CMGC,                   /* 162 */
    D_ATC_EVENT_CMGC_T,                 /* 163 */
    D_ATC_EVENT_CMMS,                   /* 164 */
    D_ATC_EVENT_CMMS_R,                 /* 165 */
    D_ATC_EVENT_CMMS_T,                 /* 166 */
    D_ATC_EVENT_NPOWERCLASS,            /* 167 */
    D_ATC_EVENT_NPOWERCLASS_R,          /* 168 */
    D_ATC_EVENT_NPOWERCLASS_T,          /* 169 */
    D_ATC_EVENT_NPTWEDRXS,              /* 170 */
    D_ATC_EVENT_NPTWEDRXS_R,            /* 171 */
    D_ATC_EVENT_NPTWEDRXS_T,            /* 172 */
    D_ATC_EVENT_NPIN,                   /* 173 */
    D_ATC_EVENT_NPIN_T,                 /* 174 */
    D_ATC_EVENT_NTSETID,                /* 175 */
    D_ATC_EVENT_NTSETID_T,              /* 176 */
    D_ATC_EVENT_NCIDSTATUS,             /* 177 */
    D_ATC_EVENT_NCIDSTATUS_R,           /* 178 */
    D_ATC_EVENT_NCIDSTATUS_T,           /* 179 */
    D_ATC_EVENT_NGACTR,                 /* 180 */
    D_ATC_EVENT_NGACTR_R,               /* 181 */
    D_ATC_EVENT_NGACTR_T,               /* 182 */
    D_ATC_EVENT_NPOPB,                  /* 183 */
    D_ATC_EVENT_NPOPB_R,                /* 184 */
    D_ATC_EVENT_NIPINFO,                /* 185 */
    D_ATC_EVENT_NIPINFO_R,              /* 186 */
    D_ATC_EVENT_NIPINFO_T,              /* 187 */
    D_ATC_EVENT_NQPODCP,                /* 188 */
    D_ATC_EVENT_NQPODCP_T,              /* 189 */
    D_ATC_EVENT_NSNPD,                  /* 190 */
    D_ATC_EVENT_NSNPD_T,                /* 191 */
    D_ATC_EVENT_NQPNPD,                 /* 192 */
    D_ATC_EVENT_NQPNPD_T,               /* 193 */
    D_ATC_EVENT_CNEC,                   /* 194 */
    D_ATC_EVENT_CNEC_R,                 /* 195 */
    D_ATC_EVENT_CNEC_T,                 /* 196 */
    D_ATC_EVENT_NRNPDM,                 /* 197 */
    D_ATC_EVENT_NRNPDM_R,               /* 198 */
    D_ATC_EVENT_NRNPDM_T,               /* 199 */
    D_ATC_EVENT_NCPCDPR,                /* 200 */
    D_ATC_EVENT_NCPCDPR_R,              /* 201 */
    D_ATC_EVENT_NCPCDPR_T,              /* 202 */
    D_ATC_EVENT_CEID,                   /* 203 */
    D_ATC_EVENT_CEID_T,                 /* 204 */
    D_ATC_EVENT_MNBIOTEVENT,            /* 205 */
    D_ATC_EVENT_MNBIOTEVENT_R,          /* 206 */
    D_ATC_EVENT_MNBIOTEVENT_T,          /* 207 */
    D_ATC_EVENT_CGPIAF,                 /* 208 */
    D_ATC_EVENT_CGPIAF_R,               /* 209 */
    D_ATC_EVENT_CGPIAF_T,               /* 210 */
    D_ATC_EVENT_CGSCONTRDP,             /* 211 */
    D_ATC_EVENT_CGSCONTRDP_T,           /* 212 */
    D_ATC_EVENT_CGTFTRDP,               /* 213 */
    D_ATC_EVENT_CGTFTRDP_T,             /* 214 */
    D_ATC_EVENT_NPLMNS,                 /* 215 */
    D_ATC_EVENT_NPLMNS_R,               /* 216 */
    D_ATC_EVENT_NPLMNS_T,               /* 217 */
    D_ATC_EVENT_NLOCKF,                 /* 218 */
    D_ATC_EVENT_NLOCKF_R,               /* 219 */
    D_ATC_EVENT_NLOCKF_T,               /* 220 */
    D_ATC_EVENT_NCSG,                   /* 221 */
    D_ATC_EVENT_NCSG_R,                 /* 222 */
    D_ATC_EVENT_NCSG_T,                 /* 223 */
    D_ATC_EVENT_CACDC,                  /* 224 */
    D_ATC_EVENT_CACDC_T,                /* 225 */
    D_ATC_EVENT_CREG,                   /* 226 */
    D_ATC_EVENT_CREG_R,                 /* 227 */
    D_ATC_EVENT_CREG_T,                 /* 228 */
    D_ATC_EVENT_PINGXY,                 /* 229 */
    D_ATC_EVENT_AT_ABORT,               /* 230 */
    D_ATC_EVENT_PSTEST,                 /* 231 */
    D_ATC_EVENT_QICSGP,                 /* 232 */
    D_ATC_EVENT_QICSGP_R,               /* 233 */
    D_ATC_EVENT_QICSGP_T,               /* 234 */
    D_ATC_EVENT_QIACT,                  /* 235 */
    D_ATC_EVENT_QIACT_R,                /* 236 */
    D_ATC_EVENT_QIACT_T,                /* 237 */
    D_ATC_EVENT_QIDEACT,                /* 238 */
    D_ATC_EVENT_QIDEACT_R,              /* 239 */
    D_ATC_EVENT_QIDEACT_T,              /* 240 */
    D_ATC_EVENT_QGSN,                   /* 241 */
    D_ATC_EVENT_QGSN_T,                 /* 242 */
    D_ATC_EVENT_QCCID,                  /* 243 */
    D_ATC_EVENT_QCCID_T,                /* 244 */
    D_ATC_EVENT_CGREG,                  /* 245 */
    D_ATC_EVENT_CGREG_R,                /* 246 */
    D_ATC_EVENT_CGREG_T,                /* 247 */
    D_ATC_EVENT_QSPN,                   /* 248 */
    D_ATC_EVENT_QSPN_R,                 /* 249 */
    D_ATC_EVENT_QSPN_T,                 /* 250 */
    D_ATC_EVENT_QENG,                   /* 251 */
    D_ATC_EVENT_QENG_T,                 /* 252 */
    D_ATC_EVENT_GSN,                    /* 253 */
    D_ATC_EVENT_GSN_R,                  /* 254 */
    D_ATC_EVENT_GSN_T,                  /* 255 */
    D_ATC_EVENT_CSCS,                   /* 256 */
    D_ATC_EVENT_CSCS_R,                 /* 257 */
    D_ATC_EVENT_CSCS_T,                 /* 258 */
    D_ATC_EVENT_QINISTAT,               /* 259 */
    D_ATC_EVENT_QINISTAT_T,             /* 260 */
    D_ATC_EVENT_QSIMDET,                /* 261 */
    D_ATC_EVENT_QSIMDET_R,              /* 262 */
    D_ATC_EVENT_QSIMDET_T,              /* 263 */
    D_ATC_EVENT_QSIMSTAT,               /* 264 */
    D_ATC_EVENT_QSIMSTAT_R,             /* 265 */
    D_ATC_EVENT_QSIMSTAT_T,             /* 266 */
    D_ATC_EVENT_CPOL,                   /* 267 */
    D_ATC_EVENT_CPOL_R,                 /* 268 */
    D_ATC_EVENT_CPOL_T,                 /* 269 */
    D_ATC_EVENT_COPN,                   /* 270 */
    D_ATC_EVENT_COPN_T,                 /* 271 */
    D_ATC_EVENT_QNWINFO,                /* 272 */
    D_ATC_EVENT_QNWINFO_T,              /* 273 */
    D_ATC_EVENT_QCSQ,                   /* 274 */
    D_ATC_EVENT_QCSQ_R,                 /* 275 */
    D_ATC_EVENT_QCSQ_T,                 /* 276 */
    D_ATC_EVENT_QCSQ_E,                 /* 277 */
    D_ATC_EVENT_CSCB,                   /* 278 */
    D_ATC_EVENT_CSCB_R,                 /* 279 */
    D_ATC_EVENT_CSCB_T,                 /* 280 */
    D_ATC_EVENT_CSDH,                   /* 281 */
    D_ATC_EVENT_CSDH_R,                 /* 282 */
    D_ATC_EVENT_CSDH_T,                 /* 283 */
    D_ATC_EVENT_QCMGS,                  /* 284 */
    D_ATC_EVENT_QCMGS_T,                /* 285 */
    D_ATC_EVENT_QCMGR,                  /* 286 */
    D_ATC_EVENT_QCMGR_T,                /* 287 */
    D_ATC_EVENT_CGSMS,                  /* 288 */
    D_ATC_EVENT_CGSMS_R,                /* 289 */
    D_ATC_EVENT_CGSMS_T,                /* 290 */
    D_ATC_EVENT_QGDCNT,                 /* 291 */
    D_ATC_EVENT_QGDCNT_R,               /* 292 */
    D_ATC_EVENT_QGDCNT_T,               /* 293 */
    D_ATC_EVENT_QAUGDCNT,               /* 294 */
    D_ATC_EVENT_QAUGDCNT_R,             /* 295 */
    D_ATC_EVENT_QAUGDCNT_T,             /* 296 */
    D_ATC_EVENT_NITZ,                   /* 297 */
    D_ATC_EVENT_NITZ_R,                 /* 298 */
    D_ATC_EVENT_CTZU,                   /* 299 */
    D_ATC_EVENT_CTZU_R,                 /* 300 */
    D_ATC_EVENT_CTZU_T,                 /* 301 */
    D_ATC_EVENT_CGMR,                   /* 302 */
    D_ATC_EVENT_CGMR_T,                 /* 303 */
    D_ATC_EVENT_GMR,                    /* 304 */
    D_ATC_EVENT_GMR_T,                  /* 305 */
    D_ATC_EVENT_QCELL_R,                /* 306 */
    D_ATC_EVENT_QCELL_T,                /* 307 */
    D_ATC_EVENT_QCELLEX,                /* 308 */
    D_ATC_EVENT_QCELLEX_T,              /* 309 */
    D_ATC_EVENT_QPINC,                  /* 310 */
    D_ATC_EVENT_QPINC_R,                /* 311 */
    D_ATC_EVENT_QPINC_T,                /* 312 */
    D_ATC_EVENT_MLOCKFREQ,              /* 313 */
    D_ATC_EVENT_MLOCKFREQ_R,            /* 314 */
    D_ATC_EVENT_MLOCKFREQ_T,            /* 315 */
    D_ATC_EVENT_MCSEARFCN,              /* 316 */
    D_ATC_EVENT_MCSEARFCN_T,            /* 317 */
    D_ATC_EVENT_MUESTATS,               /* 318 */
    D_ATC_EVENT_MUESTATS_T,             /* 319 */
    D_ATC_EVENT_MPSRAT,                 /* 320 */
    D_ATC_EVENT_MPSRAT_R,               /* 321 */
    D_ATC_EVENT_MPSRAT_T,               /* 322 */
    D_ATC_EVENT_MEMMTIMER,              /* 323 */
    D_ATC_EVENT_MEMMTIMER_R,            /* 324 */
    D_ATC_EVENT_MEMMTIMER_T,            /* 325 */
    D_ATC_EVENT_MUECONFIG,              /* 326 */
    D_ATC_EVENT_MUECONFIG_R,            /* 327 */
    D_ATC_EVENT_MUECONFIG_T,            /* 328 */
    D_ATC_EVENT_MWIFISCANCFG,           /* 329 */
    D_ATC_EVENT_MWIFISCANCFG_R,         /* 330 */
    D_ATC_EVENT_MWIFISCANCFG_T,         /* 331 */
    D_ATC_EVENT_MWIFISCANSTART,         /* 332 */
    D_ATC_EVENT_MWIFISCANSTOP,          /* 333 */
    D_ATC_EVENT_MWIFISCANQUERY,         /* 334 */
    D_ATC_EVENT_CNUM,                   /* 335 */
    D_ATC_EVENT_CNUM_T,                 /* 336 */
    D_ATC_EVENT_ATI,                    /* 337 */
    D_ATC_EVENT_GMI,                    /* 338 */
    D_ATC_EVENT_GMI_T,                  /* 339 */
    D_ATC_EVENT_CGMI,                   /* 340 */
    D_ATC_EVENT_CGMI_T,                 /* 341 */
    D_ATC_EVENT_GMM,                    /* 342 */
    D_ATC_EVENT_GMM_T,                  /* 343 */
    D_ATC_EVENT_CGMM,                   /* 344 */
    D_ATC_EVENT_CGMM_T,                 /* 345 */
    D_ATC_EVENT_PSTESTMODE,             /* 346 */
    D_ATC_EVENT_PSTESTMODE_R,           /* 347 */
    D_ATC_EVENT_QBLACKCELL,             /* 348 */
    D_ATC_EVENT_QBLACKCELL_R,           /* 349 */
    D_ATC_EVENT_QBLACKCELLCFG,          /* 350 */
    D_ATC_EVENT_QBLACKCELLCFG_R,        /* 351 */
    D_ATC_EVENT_QBLACKCELLCFG_T,        /* 352 */
    D_ATC_EVENT_SIMSWITCH,              /* 353 */
    D_ATC_EVENT_SIMSWITCH_R,            /* 354 */
    D_ATC_EVENT_SIMSWITCH_T,            /* 355 */
    D_ATC_EVENT_QCFG,                   /* 356 */
    D_ATC_EVENT_QCFG_R,                 /* 357 */
    D_ATC_EVENT_QCFG_T,                 /* 358 */
    D_ATC_EVENT_F,                      /* 359 */
    D_ATC_EVENT_W,                      /* 360 */
    D_ATC_EVENT_Z,                      /* 361 */
    D_ATC_EVENT_PCTTESTINFO,            /* 362 */
    D_ATC_EVENT_PCTTESTINFO_R,          /* 363 */
    D_ATC_EVENT_NPREEARFCN,             /* 364 */
    D_ATC_EVENT_NPREEARFCN_R,           /* 365 */
    D_ATC_EVENT_QSCLKEX,                /* 366 */
    D_ATC_EVENT_QSCLKEX_R,              /* 367 */
    D_ATC_EVENT_QSCLKEX_T,              /* 368 */
    D_ATC_EVENT_NWDRX,                  /* 369 */
    D_ATC_EVENT_SIMUUICC,               /* 370 */
    D_ATC_EVENT_SIMUUICC_R,             /* 371 */
    D_ATC_EVENT_NWSDVOLT,               /* 372 */
    D_ATC_EVENT_NWSDVOLT_R,             /* 373 */
    D_ATC_EVENT_NWSDVOLT_T,             /* 374 */
    D_ATC_EVENT_QWIFISCAN,              /* 375 */
    D_ATC_EVENT_QWIFISCAN_R,            /* 376 */
    D_ATC_EVENT_QWIFISCAN_T,            /* 377 */
    D_ATC_EVENT_CCED,                   /* 378 */
    D_ATC_EVENT_CCED_T,                 /* 379 */
    D_ATC_EVENT_CEMODE,                 /* 380 */
    D_ATC_EVENT_CEMODE_R,               /* 381 */
    D_ATC_EVENT_CEMODE_T,               /* 382 */
    D_ATC_EVENT_QIACTEX,                /* 383 */
    D_ATC_EVENT_QIACTEX_R,              /* 384 */
    D_ATC_EVENT_QIACTEX_T,              /* 385 */
    D_ATC_EVENT_QIDEACTEX,              /* 386 */
    D_ATC_EVENT_QIDEACTEX_T,            /* 387 */
    D_ATC_EVENT_ICCID_R,                /* 388 */
    D_ATC_EVENT_BAND_R,                 /* 389 */
    D_ATC_EVENT_BANDIND_R,              /* 390 */
    D_ATC_EVENT_QCELLINFO_R,            /* 391 */
    D_ATC_EVENT_QCELLINFO_T,            /* 392 */
    D_ATC_EVENT_QINDCFG,                /* 392 */
    D_ATC_EVENT_QINDCFG_R,              /* 391 */
    D_ATC_EVENT_QINDCFG_T,              /* 392 */
    D_ATC_EVENT_CIDACT,                 /* 393 */
    D_ATC_EVENT_QDSIM,                  /* 394 */
    D_ATC_EVENT_QDSIM_R,                /* 395 */
    D_ATC_EVENT_QDSIM_T,                /* 396 */
    D_ATC_EVENT_CUSD,                   /* 397 */
    D_ATC_EVENT_CUSD_R,                 /* 398 */
    D_ATC_EVENT_CUSD_T,                 /* 399 */
    D_ATC_EVENT_QEHPLMN,                /* 400 */
    D_ATC_EVENT_QEHPLMN_R,              /* 401 */
    D_ATC_EVENT_QEHPLMN_T,              /* 402 */
    D_ATC_EVENT_CCID,                   /* 403 */
    D_ATC_EVENT_ECSIMCFG,               /* 404 */
    D_ATC_EVENT_ECSIMCFG_R,             /* 405 */
    D_ATC_EVENT_ECSIMCFG_T,             /* 406 */
    D_ATC_EVENT_QUTCTIME,               /* 407 */
    D_ATC_EVENT_NULL
};

typedef enum {
    D_ATC_AP_AT_CMD_RST = 5000,         /* 5000 */
    D_ATC_AP_SMS_PDU_IND,               /* 5001 */
    D_ATC_AP_SIMST_IND,                 /* 5002 */
    D_ATC_AP_CRTDCP_IND,                /* 5003 */
    D_ATC_AP_CGAPNRC_IND,               /* 5004 */
    D_ATC_AP_CGEV_IND,                  /* 5005 */
    D_ATC_AP_CEREG_IND,                 /* 5006 */
    D_ATC_AP_CREG_IND,                  /* 5007 */
    D_ATC_AP_CGREG_IND,                 /* 5008 */
    D_ATC_AP_CSCON_IND,                 /* 5009 */
    D_ATC_AP_NPTWEDRXP_IND,             /* 5010 */
    D_ATC_AP_CEDRXP_IND,                /* 5011 */
    D_ATC_AP_CCIOTOPTI_IND,             /* 5012 */
    D_ATC_AP_CMTI_IND,                  /* 5013 */
    D_ATC_AP_CMT_IND,                   /* 5014 */
    D_ATC_AP_CDS_IND,                   /* 5015 */
    D_ATC_AP_CDSI_IND,                  /* 5016 */
    D_ATC_AP_PINSTATUS_IND,             /* 5017 */
    D_ATC_AP_L2THP_IND,                 /* 5018 */
    D_ATC_AP_XYIPDNS_IND,               /* 5019 */
    D_ATC_AP_MALLOCADDR_IND,            /* 5020 */
    D_ATC_AP_IPSN_IND,                  /* 5021 */
    D_ATC_AP_CMOLRE_IND,                /* 5022 */
    D_ATC_AP_CMOLRG_IND,                /* 5023 */
    D_ATC_AP_IPADDR_IND,                /* 5024 */
    D_ATC_AP_NGACTR_IND,                /* 5025 */
    D_ATC_AP_LOCALTIMEINFO_IND,         /* 5026 */
    D_ATC_NO_CARRIER_IND,               /* 5027 */
    D_ATC_AP_SMS_PDU_REQ,               /* 5028 */
    D_ATC_AP_SMS_TEXT_REQ,              /* 5029 */
    D_ATC_AP_CELL_SRCH_IND,             /* 5030 */
    D_ATC_AP_EVENT_CESQ_IND,            /* 5031 */
    D_ATC_AP_PSINFO_IND,                /* 5032 */
    D_ATC_AP_CSODCPR_IND,               /* 5033 */
    D_ATC_AP_NSNPDR_IND,                /* 5034 */
    D_ATC_AP_NIPINFO_IND,               /* 5035 */
    D_ATC_AP_NQPODCP_IND,               /* 5036 */
    D_ATC_AP_CNEC_IND,                  /* 5037 */
    D_ATC_AP_NRNPDM_IND,                /* 5038 */
    D_ATC_AP_MNBIOTEVENT_IND,           /* 5039 */
    D_ATC_AP_QSIMSTAT_IND,              /* 5040 */
    D_ATC_AP_BACKOFFTIMER_IND,          /* 5041 */
    D_ATC_AP_QIURC_PDPDEACT_IND,        /* 5042 */
    D_HW_LM_INTERFACE_IND,              /* 5043 */
    D_HW_LM_INTERFACE_REQ,              /* 5044 */
    D_HW_LM_INTERFACE_CNF,              /* 5045 */
    D_ATC_AP_TEST_INFO_IND,             /* 5046 */
    D_ATC_AP_CIEV_IND,                  /* 5047 */
    D_ATC_AP_SIM_DATA_DOWNLOAD_IND,     /* 5048 */
    D_ATC_AP_QWIFISCAN_IND,             /* 5049 */
    D_ATC_AP_QIACTEX_IND,               /* 5050 */
    D_ATC_AP_QIDEACTEX_IND,             /* 5051 */
    D_ATC_AP_MIPCALL_IND,               /* 5052 */
    D_ATC_AP_QUTCTIME_IND,               /* 5053 */
} ATC_AP_EXTEND_EVENT_ID;

enum 
{
    D_ATC_NCONFIG_AUTOCONNECT   = 0,
    D_ATC_NCONFIG_CELL_RESELECTION,
    D_ATC_NCONFIG_ENABLE_BIP,
    D_ATC_NCONFIG_BARRING_RELEASE_DELAY,
    D_ATC_NCONFIG_RELEASE_VERSION,
    D_ATC_NCONFIG_SYNC_TIME_PERIOD,
    D_ATC_NCONFIG_PCO_IE_TYPE,
    D_ATC_NCONFIG_NON_IP_NO_SMS_ENABLE,
    D_ATC_NCONFIG_T3324_T3412_EXT_CHANGE_REPORT,
    D_ATC_NCONFIG_MAX,
};

enum
{
    D_ATC_UECONFIG_DEFAPN   = 0,
    D_ATC_UECONFIG_AUTOCONN,
    D_ATC_UECONFIG_RELVER,
    D_ATC_UECONFIG_REESTABLISH,
    D_ATC_UECONFIG_EMULTICAR,
    D_ATC_UECONFIG_MAX,
};

enum
{
    D_ATC_QCFG_GPRSATTACH   = 0,
    D_ATC_QCFG_NWSCANMODE,
    D_ATC_QCFG_NWSCANSEQ,
    D_ATC_QCFG_ROAMSERVICE,
    D_ATC_QCFG_SERVICEDOMAIN,
    D_ATC_QCFG_BAND,
    D_ATC_QCFG_IOTOPMODE,
    D_ATC_QCFG_CREG_EMERGENCY,
    D_ATC_QCFG_NW_QPTMZ,
    D_ATC_QCFG_PDP_RETRYTIMES,
    D_ATC_QCFG_MAX,
};

#ifndef D_PDP_TYPE_INVALID
#define D_PDP_TYPE_INVALID      0
#define D_PDP_TYPE_IPV4         1
#define D_PDP_TYPE_IPV6         2
#define D_PDP_TYPE_IPV4V6       3
#define D_PDP_TYPE_NonIP        5
#endif

#define D_CHAR_SET_SELETE_GSM   0
#define D_CHAR_SET_SELETE_IRA   1
#define D_CHAR_SET_SELETE_UCS2  2
#define D_CHAR_SET_SELETE_HEX   3


#if IS_DSP_CORE
typedef struct atc_data_req
{
    MSG_HEADER_STRU             MsgHead;
    unsigned char               ucAtCmdFlg;
    unsigned char               ucSeqNum;
    unsigned char               ucAplNum;
    unsigned short              usMsgLen;
    unsigned char               aucMsgData[4];
} ST_ATC_DATA_REQ;
#endif

typedef struct
{
    unsigned char               ucSeqNum;
    unsigned char               ucAtCmdFlg;
    unsigned short              usMsgLen;
    unsigned char               aucMsgData[4];
} ATC_INTER_CORE_MSG_M3ToDSP_STRU;

typedef struct
{
    unsigned int                ulMsgName;
} ATC_AP_MSG_HEADER_STRU;

typedef struct
{
    ATC_AP_MSG_HEADER_STRU      MsgHead;
    unsigned long               ulSemaId;     //0: AtCmd, >0: appInterfaceCmd
#define D_DATA_REQ_TYEP_AT_CMD          0
#define D_DATA_REQ_TYEP_MSG             1
    unsigned char               ucReqType:2;
    unsigned char               ucNoWaitCnfFlg:2;
    unsigned char               ucExternalFlg:4;
    unsigned char               ucAplNum;
    unsigned char               ucAtChannelId;
    unsigned short              usMsgLen;
    unsigned char               aucMsgData[4];
} ATC_AP_MSG_DATA_REQ_STRU;

/* the message of PS send to ATC_AP */
typedef struct
{
    ATC_AP_MSG_HEADER_STRU      MsgHead;
    unsigned short              usMsgLen;
    unsigned char               ucSeqNum;
    unsigned char               ucPadding;
    unsigned char               aucMsgData[4];
} ATC_MSG_DATA_IND_STRU;

/************************************************************************/
/*                 Structure used for command analysis                  */
/************************************************************************/
typedef struct {
    unsigned short  usEvent;
}ST_ATC_CMD_COM_EVENT;

typedef struct {
    unsigned short  usEvent;
    unsigned short  usValue;
}ST_ATC_CMD_SHORT_PARAMETER;

/* +CGDCONT command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucCid;                                                                       /* PDP Context Identifier               */
    unsigned char  ucPdpTypeFlg;                                                                /* PDP type number value flag           */
    unsigned char  ucPdpTypeValue;                                                              /* PDP type number value                */
    unsigned char  ucD_comp;
    unsigned char  ucH_comp;
    unsigned char  ucNSLPIFlag;
    unsigned char  ucNSLPI;
    unsigned char  ucSecurePcoFlag;
    unsigned char  ucSecurePco;
    unsigned char  ucApnLen;                                                                    /* Access point name length             */
    unsigned char  aucApnValue[64];   //ucApnLen <= 64
    unsigned char* pucApnValue;       //ucApnLen > 64
} ST_ATC_CGDCONT_PARAMETER;

/* D* command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucGPRS_SC;                                                                   /*  GPRS Service Code                   */
    unsigned char  ucCalledAddr;                                                                /*  Called address                      */
    unsigned char  ucL2P;                                                                       /*  PPP                                 */
    unsigned char  ucCid;                                                                       /*  PDP Context Identifier              */
} ST_ATC_DPS_PARAMETER;

/* temp for NPSMR by xuejin li*/
typedef struct {
    unsigned short usEvent;
    unsigned long  ulPSMRTimer;
} ST_ATC_PSMR_PARAMETER;

/* H E S3 S4 S5 V &F O +CMEE +CREG +CREG ^SPN  +CSTA +CRC  +CLIP  +CLIR  +CHLD  +COLP */
/* +CSMS +CMGF +CGSMS +CMGL +CGATT +RESET  ^CGREG ^CCATT ^CMFL CMMS RAI */
/* Command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucValue;
}ST_ATC_CMD_PARAMETER;

/* LNBACT */
/* +CGSN command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucSnt;
}ST_ATC_CGSN_PARAMETER,
 ST_ATC_QGSN_PARAMETER;

/* +CGDSCONT command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucCid;
    unsigned char  ucP_cidFlg;
    unsigned char  ucP_cid;
    unsigned char  ucD_comp;
    unsigned char  ucH_comp;
    unsigned char  ucImCnSignallingFlagInd;
    unsigned char  ucImCnSignallingFlagIndFlag;
}ST_ATC_CGDSCONT_PARAMETER;    
    
/* +CGEQOS command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucCid;
    unsigned char  ucQciFlag;
    unsigned char  ucQci;
    unsigned char  ucDl_GbrFlag;
    unsigned int   ulDl_Gbr;
    unsigned char  ucUl_GbrFlag;
    unsigned int   ulUl_Gbr;
    unsigned char  ucDl_MbrFlag;
    unsigned int   ulDl_Mbr;
    unsigned char  ucUl_MbrFlag;
    unsigned int   ulUl_Mbr;
}ST_ATC_CGEQOS_PARAMETER;

/* +CGCMOD command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucCidNum;
    unsigned char  aucCid[D_MAX_CNT_CID];
}ST_ATC_CGCMOD_PARAMETER;

/* +CGTFT command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucCidFlag;
    unsigned char  ucCid;
    unsigned char  ucPacketFilterIdentifierFlag;
    unsigned char  ucPacketFilterIdentifier;
    unsigned char  ucEvaluationPrecedenceIndexFlg;
    unsigned char  ucEvaluationPrecedenceIndex;
    unsigned char  ucRemoteAddressAndSubnetMaskLen;
    unsigned char  aucRemoteAddressAndSubnetMaskValue[32];
    unsigned char  ucProtocolNumber_NextHeaderFlag;
    unsigned char  ucProtocolNumber_NextHeader;
    unsigned char  ucLocalPortRangeLen;
    unsigned short ausLocalPortRangeValue[2];
    unsigned char  ucRemotePortRangeLen;
    unsigned short ausRemotePortRangeValue[2];
    unsigned char  ucIpsecSpiFlag;
    unsigned int   ulIpsecSpi;
    unsigned char  ucTosAndMask_TracfficClassAndMaskLen;
    unsigned char  aucTosAndMask_TracfficClassAndMaskValue[2];
    unsigned char  ucFlowLabelFlag;
    unsigned int   ulFlowLabel;
    unsigned char  ucDirectionFlag;
    unsigned char  ucDirection;
    unsigned char  ucLocalAddressAndSubnetMaskLen;
    unsigned char  aucLocalAddressAndSubnetMaskValue[32];
}ST_ATC_CGTFT_PARAMETER;    

/* LNBACT */
/* +CEREG command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucN;
} ST_ATC_CEREG_PARAMETER,
  ST_ATC_CGREG_PARAMETER;

/* +CSODCP command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucCid;
    unsigned char  ucRAI;
    unsigned char  ucRAIFlag;
    unsigned char  ucSequence;
    unsigned char  ucTUserData;
    unsigned char  ucTUserDataFlag;
    unsigned short usCpdataLength;
    unsigned char  *pucCpdata;
}ST_ATC_CSODCP_PARAMETER;

/* +CGAPNRC command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucCidFlag;
    unsigned char  ucCid;
}ST_ATC_CGAPNRC_PARAMETER;

/* +CRTDCP command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucReportingFlag;
    unsigned char  ucReporting;
}ST_ATC_CRTDCP_PARAMETER;

/* +CEDRXS command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucMode;
    unsigned char  ucActTypeFlg;
    unsigned char  ucActType;
    unsigned char  ucEDRXValueFlag;
    unsigned char  ucEDRXValue;
    unsigned char  ucPtwValueFlag;
    unsigned char  ucPtwValue;
    unsigned char  ucNptwEDrxsCmdFlg;
}ST_ATC_CEDRXS_PARAMETER;

/* +CFUN command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucMode;
    unsigned char  ucReqPeriRAUFlg;
    unsigned char  ucReqPeriRAU;
    unsigned char  ucReqGPRSReadyTimerFlg;
    unsigned char  ucReqGPRSReadyTimer;
    unsigned char  ucReqPeriTAUFlg;
    unsigned char  ucReqPeriTAU;
    unsigned char  ucReqActTimeFlag;
    unsigned char  ucReqActTime;
}ST_ATC_CPSMS_PARAMETER;

/* +CFUN command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucFun;
    unsigned char  ucRst;
}ST_ATC_CFUN_PARAMETER;

/* +CFUN command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned short usTimerVal;  //extended param: non-3gpp
}ST_ATC_CESQ_PARAMETER;

/* +CGACT command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucCidNum;
    unsigned char  aucCid[D_MAX_CNT_CID];
    unsigned char  ucState;
}ST_ATC_CGACT_PARAMETER;

#ifdef LTE_SMS_FEATURE
/* +CNMI command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucMode;
    unsigned char  ucMtFlag;
    unsigned char  ucMt;
    unsigned char  ucBmFlag;
    unsigned char  ucBm;
    unsigned char  ucDsFlag;
    unsigned char  ucDs;
    unsigned char  ucBfrFlag;
    unsigned char  ucBfr;
}ST_ATC_CNMI_PARAMETER;

/* +CSCA command parameter structure */
typedef struct {
    unsigned short      usEvent;
    unsigned char       ucScaLen;                                                               /* <sca> length                         */ 
    unsigned char       aucScaData[4*D_ATC_P_CSCA_IND_SCA_SIZE_MAX];
    unsigned char       ucToscaFlag;                                                            /* <tosca> flag                         */
    unsigned char       ucTosca;                                                                /* <tosca>                              */
}ST_ATC_CSCA_PARAMETER;

/* +CSMP command parameter structure */
typedef struct {
    unsigned short      usEvent;
#define VP_NOT_PRESENT         0
#define VP_ENHANCED_FORMAT     1
#define VP_RELATIVE_FORMAT     2
#define VP_ABSOLUTE_FORMAT     3
    unsigned char       ucFo;
    unsigned char       ucVpLen;
    unsigned char       ucVp[D_ATC_P_CSMP_IND_VP_SIZE_MAX];
    unsigned char       ucPidFlag;
    unsigned char       ucPid;
    unsigned char       ucDcsFlag;
    unsigned char       ucDcs;
}ST_ATC_CSMP_PARAMETER;

typedef struct {
    unsigned short      usEvent;    //D_ATC_AP_SMS_PDU_REQ
    unsigned short      usCmdEvent; //+CMGS/CMGC/CNMA Evnet
    unsigned short      usPduLength;
    unsigned char*      pucPduData;
} ST_ATC_AP_SMS_PDU_PARAMETER;

typedef struct {
    unsigned short      usEvent;    //D_ATC_AP_SMS_TEXT_REQ
    unsigned short      usCmdEvent; //+CMGS/CMGC/CNMA Evnet
    unsigned short      usTextLength;
    unsigned char*      pucTextData;
} ST_ATC_AP_SMS_TEXT_PARAMETER;

/* +CMGS +CMGW +CMGC +CNMA ^CMGS command parameter structure */
typedef struct {
    unsigned short      usEvent;    // +CMGS/CMGC/CNMA 
//    unsigned char       ucSmsMode;
    unsigned char       ucStatFlag;
    unsigned char       ucStat;
    unsigned char       ucNum;
    unsigned char       ucPduLength;
    unsigned char       aucPduData[94];  //ucPduLength <= 94 
    unsigned char*      pucPduData;      //ucPduLength > 94
}ST_ATC_PDU_TPDU_PARAMETER;

/* +CMGS +CMGW +CMGC +CNMA ^CMGS command parameter structure */
typedef struct {
    unsigned short      usEvent;    // +CMGS/CMGC/CNMA 
    unsigned char       ucStatFlag;
    unsigned char       ucStat;
    unsigned char       ucStatLen;
    unsigned char       aucStat[10];
    unsigned char       ucFo;
    unsigned char       ucVp[D_ATC_P_CSMP_IND_VP_SIZE_MAX];
    unsigned char       ucCt;
    unsigned char       ucPid;
    unsigned char       ucDcs;
    unsigned char       ucMnFlag;
    unsigned char       ucMn;
    unsigned char       ucDaLenField;
    unsigned char       ucDaLen;
    unsigned char       aucDaData[4*D_ATC_P_CDA_IND_DA_SIZE_MAX];
    unsigned char       ucToDaFlag;
    unsigned char       ucToda;
    unsigned char       ucUid;
    unsigned char       ucMsgSeg;
    unsigned char       ucMsgTotal;
    unsigned short      usTextLength;
    unsigned char       *pucTextData;
}ST_ATC_TEXT_PARAMETER;

typedef struct {
    unsigned short      usEvent;    // +CMGR/+QCMGR
    unsigned char       ucIndex;
}ST_ATC_CMGR_PARAMETER,ST_ATC_QCMGR_PARAMETER;

typedef struct {
    unsigned short      usEvent;    // +CMGD
    unsigned char       ucIndex;
    unsigned char       ucDelFlag;
}ST_ATC_CMGD_PARAMETER;

typedef struct {
    unsigned short      usEvent;    // +CMGL
    unsigned char       ucStat;
    unsigned char       ucStatLen;
    unsigned char       aucStat[10];
}ST_ATC_CMGL_PARAMETER;

typedef struct {
    unsigned short      usEvent;    // +CMSS
    unsigned char       ucIndex;
    unsigned char       ucDaLen;
    unsigned char       ucDaLenField;
    unsigned char       aucDa[D_SMS_DA_SIZE_MAX];
    unsigned char       aucDaData[4*D_ATC_P_CDA_IND_DA_SIZE_MAX];
    unsigned char       ucToDaFlag;
    unsigned char       ucToda;
}ST_ATC_CMSS_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucMode;
    unsigned char                 ucMidsLen;
    unsigned char                 aucMids[100];
    unsigned char                 ucDcssLen;
    unsigned char                 aucDcss[50];
} ST_ATC_CSCB_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucDaLen;
    unsigned char                 aucDa[4*D_ATC_P_CDA_IND_DA_SIZE_MAX];
    unsigned char                 ucToDaFlag;
    unsigned char                 ucToda;
    unsigned char                 ucExtParamFlg;
    unsigned char                 ucUid;
    unsigned char                 ucMsgSeg;
    unsigned char                 ucMsgTotal;
    unsigned short                usTextLength;
    uint32_t                      pucTextData;
} ST_ATC_QCMGS_PARAMETER;

#endif

/* +CGDATA  command parameter structure */
typedef struct {
    unsigned short      usEvent;
    unsigned char       ucCidFlag;
    unsigned char       ucCid;
    unsigned char       ucL2pFlag;
    unsigned char       ucL2p;
}ST_ATC_CGDATA_PARAMETER;

/* +CGPADDR  command parameter structure */
typedef struct {
    unsigned short      usEvent;
    unsigned char       ucAllCidFlg;
    unsigned char       ucCidNum;
    unsigned char       aucCid[D_MAX_CNT_CID];
}ST_ATC_CGPADDR_PARAMETER;

typedef struct {
    unsigned short      usEvent;
    unsigned char       ucMode;
    unsigned char       ucSubSet;
    unsigned char       ucSubSetFlg;
    unsigned char       ucPortSpeed;
    unsigned char       ucPortSpeedFlg;
    unsigned short      usMaxFraSize;
    unsigned char       ucMaxFraSizeFlg;
    unsigned char       ucTimer1;
    unsigned char       ucTimer1Flg;
    unsigned char       ucMaxNum;
    unsigned char       ucMaxNumFlg;
    unsigned char       ucTimer2;
    unsigned char       ucTimer2Flg;
    unsigned char       ucTimer3;
    unsigned char       ucTimer3Flg; 
    unsigned char       ucWinSize;
    unsigned char       ucWinSizeFlg;
}ST_ATC_CMUX_PARAMETER;

/* +COPS command parameter structure */
typedef struct {
    unsigned short      usEvent;
    unsigned char       ucMode;
    unsigned char       ucFormatFlag;
    unsigned char       ucFormat;
    unsigned char       ucPerFlag;
    unsigned char       aucPer[3];
    unsigned char       ucActFlg;
    unsigned char       ucAct;
}ST_ATC_COPS_PARAMETER;

//shao add for USAT

/* +CSIM command parameter structure */
typedef struct {
    unsigned short      usEvent;
    unsigned short      usLength;
    unsigned char       aucCommand[96]; //usLength <= 96
    unsigned char*      pucCommand;     //usLength > 96
}ST_ATC_CSIM_PARAMETER;

typedef struct {
    unsigned short      usEvent;
    unsigned char       ucLength;
    unsigned char       aucDfName[16];
}ST_ATC_CCHO_PARAMETER;

typedef struct {
    unsigned short      usEvent;
    unsigned char       ucSesionId;

}ST_ATC_CCHC_PARAMETER;

typedef struct {
    unsigned short      usEvent;
    unsigned char       ucSesionId;
    unsigned short      usLength;
    unsigned char       aucCommand[96]; //usLength <= 96
    unsigned char*      pucCommand;     //usLength > 96
}ST_ATC_CGLA_PARAMETER;

typedef struct {
    unsigned short      usEvent;
    unsigned char       ucCommand;
    unsigned int        ulField;
    unsigned char       ucP1;
    unsigned char       ucP2;
    unsigned char       ucP3;
    unsigned char       ucPathLen;
    unsigned char       aucPathId[12];
    unsigned char       ucDataLen;
    unsigned char*      pucData;
}ST_ATC_CRSM_PARAMETER;

typedef struct {
    unsigned short      usEvent;
    unsigned char       ucNFlag;
    unsigned char       ucN;
    unsigned char       ucSuppUeOptFlag;
    unsigned char       ucSupportUeOpt;
    unsigned char       ucPreUeOptFlag;
    unsigned char       ucPreferredUeOpt;
}ST_ATC_CCIOTOPT_PARAMETER;

typedef struct {
    unsigned short      usEvent;
    unsigned char       ucCid;
    unsigned char       ucCidFlag;

}ST_ATC_CGEQOSRDP_PARAMETER;

/* +CPWD command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucFac;
    unsigned char  ucOldPwdLen;
    unsigned char  aucOldPwd[D_ATC_P_CPWD_OLDPWD_SIZE_MAX];
    unsigned char  ucNewPwdLen;
    unsigned char  aucNewPwd[D_ATC_P_CPWD_NEWPWD_SIZE_MAX];
}ST_ATC_CPWD_PARAMETER;

/* +CPIN command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucNewPinFlg;
    unsigned char  aucPin[D_ATC_P_CPIN_PIN_SIZE_MAX];
    unsigned char  aucNewPin[D_ATC_P_CPIN_NEWPIN_SIZE_MAX];
}ST_ATC_CPIN_PARAMETER;

/* +CLCK command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucFac;
    unsigned char  ucMode;
    unsigned char  ucPassWdFlag;
    unsigned char  ucPassWdLen;
    unsigned char  aucPassWd[D_ATC_P_CLCK_PASSWD_SIZE_MAX];
}ST_ATC_CLCK_PARAMETER;

typedef struct {
    unsigned char ucEventId;
    unsigned char ucCid;
    unsigned char ucPcid;
    unsigned char ucEventType;
    unsigned char ucReason;
#define  D_ATC_TFT_CHANG_FLG              0x01
#define  D_ATC_QOS_CHANG_FLG              0x02
#define  D_ATC_WLAN_CHANG_FLG             0x04
    unsigned char ucChangeReason;
    unsigned char ucCidOther;
    unsigned char ucWlanOffload;
}ST_ATC_CGEV_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucCid;
    unsigned char                 ucUsernameLen;
    unsigned char                 ucPasswordLen;
    unsigned char                 ucAuthProt;
    unsigned char                 aucUsername[16];
    unsigned char                 aucPassword[16];
    unsigned char                 ucCdmaPwd;   //+QICSGP only
} ST_ATC_CGAUTH_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucN;
    unsigned char                 ucAttWithoutPDN;
} ST_ATC_CIPCA_PARAMETER;

/* +CGEREP  command parameter structure */
typedef struct {
    unsigned short  usEvent;
    unsigned char   ucModeFlag;
    unsigned char   ucMode;
    unsigned char   ucBfrFlag;
    unsigned char   ucBfr;
}ST_ATC_CGEREP_PARAMETER;

/* +CTZR command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucReporting;
}ST_ATC_CTZR_PARAMETER;
/* +CGCONTRDP command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucCid;
    unsigned char  ucCidFlag;
}ST_ATC_CGCONTRDP_PARAMETER,
ST_ATC_CGSCONTRDP_PARAMETER,
ST_ATC_CGTFTRDP_PARAMETER;


#define D_ATC_PARAM_MAX_NSOCR         4
#define D_ATC_MAX_PARAM_NUM_2         2
#define D_ATC_MAX_PARAM_NUM_3         3
#define D_ATC_MAX_PARAM_NUM_4         4
#define D_ATC_MAX_PARAM_NUM_5         5

typedef struct
{
    unsigned short  usEvent;
#define ATC_NUESTATS_TYPE_RADIO         0
#define ATC_NUESTATS_TYPE_CELL          1
#define ATC_NUESTATS_TYPE_BLER          2
#define ATC_NUESTATS_TYPE_THP           3
#define ATC_NUESTATS_TYPE_APPSMEM       4
#define ATC_NUESTATS_SBAND              5
#define ATC_NUESTATS_TYPE_ALL           6
#define ATC_NUESTATS_MAX                7
#define ATC_NUESTATS_TYPE_NOPARAMETER   8
    unsigned char   mu8Type;
}ST_ATC_NUESTATS_PARAMETER,
 ST_ATC_MUESTATS_PARAMETER;

typedef struct
{
    unsigned short  usEvent;
    unsigned char   mu8Mode;
    unsigned char   mu8Padding;
    unsigned int    mu32Earfcn;
    unsigned short  mu16Pci;
    unsigned short  mu16Padding;
}ST_ATC_NEARFCN_PARAMETER;

typedef struct
{
    unsigned short  usEvent;
    unsigned char   mu8Num;
    unsigned char   mau8Band[D_ATC_NBAND_MAX_BNAD_NUM];
}ST_ATC_NBAND_PARAMETER;

typedef struct
{
    unsigned short  usEvent;
    unsigned char   mu8Func;
    unsigned short  mu16Val;
}ST_ATC_NCONFIG_PARAMETER;


typedef struct {
    unsigned short usEvent;
    unsigned char  ucValue;
    unsigned char  ucTimerLen;
}ST_ATC_NL2THP_PARAMETER;

/* +NSET command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucInsLen;                                                                     
    unsigned char  aucInsValue[D_ATC_P_NSET_INS_SIZE_MAX];      /* Instruction Name */
    unsigned char  ucParam1Flg;
    unsigned int   ulParam1;
    unsigned char  ucParam2Flg;
    unsigned int   ulParam2;
    unsigned char  ucLen;
    unsigned char  aucData[NVM_MAX_SN_LEN + 1];
} ST_ATC_NSET_PARAMETER;

typedef struct {
    unsigned char  ucImsiLen;
    unsigned char  aucImsi[D_ATC_SIMUUICC_IMSI_SIZE_MAX];
} ST_ATC_SIMUUICC_IMSI_PARAMETER;

typedef struct {
    unsigned char  ucCardNum;
    unsigned char  ucSubCardNum;
} ST_ATC_SIMUUICC_CARDNUM_PARAMETER;

typedef struct {
    unsigned char  ucAppType;
    unsigned char  aucFileId[D_ATC_SIMUUICC_FILE_ID_MAX+1];
    unsigned char  aucFileContent[D_ATC_SIMUUICC_FILE_CONTENT_MAX*2+1];
} ST_ATC_SIMUUICC_FILECONTENT_PARAMETER;

typedef struct {
    unsigned char  aucKey[NAS_AUTH_KEY_LTH*2 + 1];
    unsigned char  aucOp[NAS_AUTH_OP_LTH*2 + 1];
} ST_ATC_SIMUUICC_AUTH_PARAMETER;

/* +SIMUUICC command parameter structure */
typedef struct {
    unsigned short usEvent;
    unsigned char  ucInsLen;                                                                     
    unsigned char  aucInsValue[D_ATC_P_SIMUUICC_INS_SIZE_MAX];      /* Instruction Name */
    union
    {
        ST_ATC_SIMUUICC_CARDNUM_PARAMETER stCardNum;
        ST_ATC_SIMUUICC_FILECONTENT_PARAMETER stFileContent;
        ST_ATC_SIMUUICC_IMSI_PARAMETER    stImsi;
        ST_ATC_SIMUUICC_AUTH_PARAMETER    stAuth;
    } u;
} ST_ATC_SIMUUICC_PARAMETER;

#ifdef LCS_MOLR_ENABLE
#define NMEA_STRING_LEN                                     100
#define THIRD_PARTY_ADDRESS_LEN                             100
typedef struct
{
    unsigned short              usEvent;
    unsigned char               ucMethodFlg:1;
    unsigned char               ucVerAccFlg:1;
    unsigned char               ucHorAccFlg:1;
    unsigned char               ucPlane:1;                  /* 0: Control plane
                                                               1: Secure user plane (SUPL)
                                                             */
    unsigned char               ucPadding:1;               
                                                            /* ucMethod   : 0x01 
                                                               ucRepMode  : 0x02
                                                               usTimeOut  : 0x04
                                                               usInterval : 0x08
                                                               ucShapeRep : 0x10
                                                             */
    unsigned char               ucMethod:3;                 /* 0: Unassisted GPS. Autonomous GPS only, no use of assistance data.
                                                               1: A-GPS
                                                               2: A-GNSS
                                                               3: A-GPS and A-GNSS
                                                               4: Basic self location
                                                               5: Transfer to third party
                                                               6: Retrieval from third party
                                                             */
    
    unsigned char               ucEnableReportPos:2;        /* 0: Disables reporting and positioning
                                                               1: +CMOLRN: <NMEA-string>
                                                               2: +CMOLRG: <location_parameters>
                                                               3: +CMOLRG: <location_parameters>and +CMOLRN: <NMEA-string>
                                                             */
    unsigned char               ucHorAccSet:1;              /* 0: Horizontal accuracy not set/specified
                                                               1: Horizontal accuracy set in parameter <hor-acc>
                                                              */
    
    unsigned char               ucVerReq:1;                 /* 0: Vertical coordinate (altitude) is not requested, 2D location fix is acceptable. The parameters <ver-accset>and <ver-acc>do not apply. 
                                                               1: Vertical coordinate (altitude) is requested, 3D location fix is required
                                                             */
    unsigned char               ucVerAccSet:1;              /* 0: Vertical accuracy not set/specified
                                                               1: Vertical accuracy set/specified in parameter <ver-acc>
                                                             */

    unsigned char               ucVelReq:3;                 /* 0: Velocity not requested
                                                               1: Horizontal velocity requested
                                                               2: Horizontal velocity and vertical velocity requested
                                                               3: Horizontal velocity with uncertainty requested
                                                               4: Horizontal velocity with uncertainty and vertical velocity with uncertainty requested
                                                             */

    unsigned char               ucRepMode:1;                /* 0: Single report
                                                               1: Periodic reporting
                                                             */
    unsigned char               ucShapeRep:7;               /* 1: Ellipsoid point
                                                               2: Ellipsoid point with uncertainty circle
                                                               4: Ellipsoid point with uncertainty ellipse
                                                               8: Polygon
                                                               16: Ellipsoid point with altitude
                                                               32: Ellipsoid point with altitude and uncertainty ellipsoid
                                                               64: Ellipsoid arc
                                                             */                                                           
    unsigned char               ucNmeaRepLen;
    unsigned char               ucVerAcc;                   /* 0-127 */
    unsigned char               ucHorAcc;                   /* 0-127 */
//    unsigned char               ucNmeaRep;
//    unsigned char               aucNmeaRep[NMEA_STRING_LEN];
//    unsigned char               ucThirdPartyAddLen;
//    unsigned char               aucThirdPartyAddress[THIRD_PARTY_ADDRESS_LEN];
    
    unsigned short              usTimeOut;                  /* in seconds from 1 to 65535 */
    unsigned short              usInterval;                 /* periodic reporting only : in seconds from 1 to 65535, and must be greater than or equal to <timeout> */  
}ST_ATC_CMOLR_PARAMETER;
#endif

typedef struct {
    unsigned short                usEvent;
#define D_ATC_MAX_SEL_CODE_LEN     16
    unsigned char                 ucLen;
    unsigned char                 ucNoParamValue;
    unsigned char                 aucSelCode[D_ATC_MAX_SEL_CODE_LEN];
} ST_ATC_CPINR_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucVal;
} ST_ATC_CMMS_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned short                usBand;
    unsigned char                 ucPowerClass;
} ST_ATC_NPOWERCLASS_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucCommand;
    unsigned char                 ucParam1Len;
    unsigned char                 ucParam2Len;
#define D_ATC_MAX_PIN_LEN      8    
    unsigned char                 aucParam1[D_ATC_MAX_PIN_LEN];
    unsigned char                 aucParam2[D_ATC_MAX_PIN_LEN];
} ST_ATC_NPIN_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucSnt;
    unsigned char                 ucDataLen;
    unsigned char                 aucData[15];
} ST_ATC_NTSETID_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucCid;
} ST_ATC_NCIDSTATUS_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 n;
} ST_ATC_NGACTR_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucPlmnFlg;
    unsigned char                 ucBandNumFlg;
    unsigned char                 ucOperatorIndex;
    unsigned char                 ucBandNum;
    unsigned short                usOffset;
    unsigned int                  ulPlmn;
    unsigned int                  ulStartFreq;
} ST_ATC_NPOPB_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucN;
} ST_ATC_NIPINFO_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucCid;
} ST_ATC_NQPODCP_PARAMETER,
  ST_ATC_NQPNPD_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucCid;
    unsigned char                 ucRai;
    unsigned char                 ucTypeData;
    unsigned char                 ucSquence;
    unsigned short                usNonIpDataLen;
    unsigned char*                pucNonIpData;
} ST_ATC_NSNPD_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucN;
} ST_ATC_CNEC_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucReporting;
} ST_ATC_NRNPDM_PARAMETER;

typedef struct {
    unsigned short                usEvent;
#define D_ATC_NCPCDPR_IPV4_DNS_REQ  0
#define D_ATC_NCPCDPR_IPV6_DNS_REQ  1
    unsigned char                 ucParam;
    unsigned char                 ucState;
} ST_ATC_NCPCDPR_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucEnable;
} ST_ATC_MNBIOTEVENT_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucIpv6AddressFormatFlag;
    unsigned char                 ucIpv6AddressFormat;
    unsigned char                 ucIpv6SubnetNotationFlag;
    unsigned char                 ucIpv6SubnetNotation;
    unsigned char                 ucIpv6LeadingZerosFlag;
    unsigned char                 ucIpv6LeadingZeros;
    unsigned char                 ucIpv6CompressZerosFlag;
    unsigned char                 ucIpv6CompressZeros;
} ST_ATC_CGPIAF_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucMode;
    unsigned char                 ucEarfcnNum;
    unsigned int                  aulEarfcnList[NVM_MAX_CANDIDATE_FREQ_NUM];
    unsigned short                usPci;
} ST_ATC_NLOCKF_PARAMETER;

typedef struct {
    unsigned short                usEvent;
#define D_ATC_QCSG_MODE_CLOSE     0
#define D_ATC_QCSG_MODE_AUTO      1
#define D_ATC_QCSG_MODE_MANUAL    2
    unsigned char                 ucMode;
    unsigned char                 ucCsgIdFlg;
    unsigned int                  ulCsgId;
    unsigned char                 ucPerFlag;
    unsigned char                 aucPer[3];
} ST_ATC_NCSG_PARAMETER;

/* +CPMS command parameter structure */
typedef struct {
    unsigned short      usEvent;
    unsigned char       ucMem1Len;
    unsigned char       aucMem1[2];
    unsigned char       ucMem2Len;
    unsigned char       aucMem2[2];
    unsigned char       ucMem3Len;
    unsigned char       aucMem3[2]; 
}ST_ATC_CPMS_PARAMETER;

typedef struct {
    unsigned short                usEvent;    
    unsigned char                 aucOsId[16];
#define D_ATC_OS_APP_ID_MAX_LEN   64
    unsigned char                 ucOsAppIdLen;
    unsigned char                 aucOsAppId[D_ATC_OS_APP_ID_MAX_LEN + 1];
    unsigned char                 ucIndication;
} ST_ATC_CACDC_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucCid;
    unsigned char                 aucDescAddr[16]; //string format: 255.255.255.255
    unsigned short                usDataLen;
    unsigned short                usPingNum;
    unsigned char                 ucTimeOut;      //uint:s
    unsigned char                 ucIntervalTime; //uint:s
} ST_ATC_PINGXY_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucType;
    unsigned short                usDataLen;
    unsigned char*                pucData;
} ST_ATC_PSTEST_PARAMETER;

typedef struct {
    unsigned short                usEvent;
    unsigned char                 ucCid;
    unsigned char                 ucPdpType;
    unsigned char                 ucApnLen;
    unsigned char                 ucUsernameLen;
    unsigned char                 ucPasswordLen;
    unsigned char                 ucAuthProt;
    unsigned char                 aucUsername[16];
    unsigned char                 aucPassword[16];
    unsigned char                 aucApnValue[D_APN_LEN];
} ST_ATC_QICSGP_PARAMETER;

typedef struct {
    unsigned short usEvent;
    unsigned char  ucCid;
} ST_ATC_QIACT_PARAMETER,
  ST_ATC_QIDEACT_PARAMETER,
  ST_ATC_QIDEACTEX_PARAMETER;

typedef struct {
unsigned short usEvent;
unsigned char  ucCid;
unsigned char  ucMipcallFlg;
} ST_ATC_CIDACT_PARAMETER;

typedef struct {
  unsigned short usEvent;
  unsigned char  ucCid;
  unsigned char  ucMode;
} ST_ATC_QIACTEX_PARAMETER;

typedef struct {
  unsigned short usEvent;
#define D_ATC_QENG_TYPE_SERV_CELL    0
#define D_ATC_QENG_TYPE_NEIGHB_CELL  1
  unsigned char  ucType;
} ST_ATC_QENG_PARAMETER;

typedef struct {
    unsigned short usEvent;
    unsigned char  ucSntFlg;
    unsigned char  ucSnt;
} ST_ATC_GSN_PARAMETER;

typedef struct {
    unsigned short usEvent;
    unsigned char  ucChset;   /* 0:GSM,1:IRA,2:UCS2 */
} ST_ATC_CSCS_PARAMETER;

typedef struct {
    unsigned short      usEvent;
    unsigned char       indexFlg;
    unsigned char       index;
    unsigned char       ucFormatFlg;
    unsigned char       ucFormat;
    unsigned char       ucOperFlg;
    unsigned char       ucOperLen;
    unsigned char       aucOper[21];
    unsigned char       ucActFlg;
    unsigned char       ucGSMAcT;
    unsigned char       ucGSMCompactAcT;
    unsigned char       ucUTRANAcT;
    unsigned char       ucEUTRANAcT;
} ST_ATC_CPOL_PARAMETER;

typedef struct {
    unsigned short      usEvent;
    unsigned char       ucEnable;
    unsigned char       ucInsertLevl;
} ST_ATC_QSIMDET_PARAMETER;

typedef struct {
    unsigned short      usEvent;
    unsigned short      usValue;
} ST_ATC_QAUGDCNT_PARAMETER;

typedef struct {
    unsigned short      usEvent;
    unsigned char       ucMode;
    unsigned char       ucSaveMode; //only for +NITZ
} ST_ATC_NITZ_PARAMETER,
  ST_ATC_CTZU_PARAMETER;

typedef struct
{
  unsigned char       ucLteBandFlg;
  unsigned char       mu8Num;
  unsigned char       mau8Band[D_ATC_NBAND_MAX_BNAD_NUM];
  unsigned char       ucGsmBandFlg;
  unsigned char       aucGsmBand[D_ATC_QCFG_MAX_GSMBAND_NUM];
  unsigned char       ucNbiotBandFlg;
  unsigned char       aucNbiotBand[D_ATC_QCFG_MAX_NBIOTBAND_NUM];
  unsigned char       ucEffFlg;
  unsigned char       ucEffect;
} ST_ATC_QCFG_BAND_PARAMETER;

typedef struct
{
  unsigned char       ucValue;
  unsigned char       ucEffFlg;
  unsigned char       ucEffect;
} ST_ATC_QCFG_NORMAL_PARAMETER;

typedef struct 
{
  unsigned short      usEvent;
  unsigned char       ucFunc;
  ST_ATC_QCFG_NORMAL_PARAMETER    stFuncInfo;
  ST_ATC_QCFG_BAND_PARAMETER      stBandInfo;
} ST_ATC_QCFG_PARAMETER;

typedef struct {
  unsigned short      usEvent;
  unsigned char       ucFacility; /* 0:all, 1:SC,1:P2 */
} ST_ATC_QPINC_PARAMETER;

typedef struct {
    unsigned short      usEvent;
#define D_ATC_MWIFISCANCFG_OPT_HIDDEN    0
#define D_ATC_MWIFISCANCFG_OPT_PRIORITY  1
#define D_ATC_MWIFISCANCFG_OPT_MAX       2
    unsigned char       ucOpt;
    unsigned char       ucValue;
} ST_ATC_MWIFISCANCFG_PARAMETER;

typedef struct {
    unsigned short      usEvent;
    unsigned char       ucRoundFlg;
    unsigned char       ucRound;
    unsigned char       ucTimeoutFlg;
    unsigned char       ucTimeout;
    unsigned char       ucSsidLen;
    unsigned char       aucSsid[32];
    unsigned char       ucChannelFlg;
    unsigned char       ucChannel;
} ST_ATC_MWIFISCANSTART_PARAMETER;

typedef struct {
    unsigned short      usEvent;
    unsigned char       ucRoundFlg;
    unsigned char       ucRound;
    unsigned char       ucMaxIDNumFlg;
    unsigned char       ucMasIDNum;
    unsigned char       ucScantimeoutFlg;
    unsigned char       ucScantimeout;
    unsigned char       ucPriorityFlg;
    unsigned char       ucPriority;
    unsigned char       ucTimeoutFlg;
    unsigned int        uiTimeout;
} ST_ATC_QWIFISCAN_PARAMETER;

typedef struct
{
   unsigned char       ucPciFlg;
   unsigned short      usPci;
   unsigned int        ulEarfcn;
} ST_EARFCN_PCI_INFO;

typedef struct
{
#define D_LOCKFREQ_MODE_UNLOCK           0
#define D_LOCKFREQ_MODE_LOCK             1
#define D_LOCKFREQ_MODE_PRIORITY_FREQ    2 
    unsigned char              ucMode;
    unsigned char              ucNum;
    ST_EARFCN_PCI_INFO         aEarfcnInfo[16];
}API_LOCKFREQ_LIST_STRU;

typedef struct {
    unsigned short             usEvent;
    API_LOCKFREQ_LIST_STRU     LockFreqList;
} ST_ATC_MLOCKFREQ_PARAMETER;

typedef struct {
    unsigned short             usEvent;
    unsigned char              ucFunc;
    unsigned char              ucValue1;
    unsigned char              ucValue2Len;
    unsigned char              aucValue2[38];
} ST_ATC_MUECONFIG_PARAMETER;

typedef struct {
    unsigned short             usEvent;
    unsigned char              ucOpType; /* 0: add; 1: del */
    unsigned char              ucNum;
    ST_EARFCN_PCI_INFO         atEarfcnInfo[10];
} ST_ATC_QBLACKCELL_PARAMETER;

typedef struct {
    unsigned short             usEvent;
    unsigned short             usPSTestSocketPort;
    unsigned char              ucPsTestModeFlg;
    unsigned char              ucClosePhyLogFlg;
} ST_ATC_PSTESTMODE_PARAMETER;

typedef struct {
    unsigned short             usEvent;
    unsigned char              aucPCTTestSequence[15];
} ST_ATC_PCTTESTINFO_PARAMETER;

typedef struct {
    unsigned short             usEvent;
    unsigned char              ucOperatorIndex;
    unsigned int               ulEarfcn;
} ST_ATC_NPREEARFCN_PARAMETER;

typedef struct {
    unsigned short             usEvent;
    unsigned char              ucMode;
    unsigned char              ucIdle_time;         //  0xFF is invalid initial value
    unsigned short             usRetry_time;        //  0xFFFF is invalid initial value
} ST_ATC_QSCLKEX_PARAMETER;

typedef struct {
    unsigned short             usEvent;
    unsigned char              ucMode;
    unsigned char              ucValue;
} ST_ATC_CCED_PARAMETER;

typedef struct {
    unsigned short             usEvent;
    unsigned char              ucValueFlg;
    unsigned char              ucValue;
    unsigned char              ucSaveFlg;
    unsigned char              ucSaveValue;
} ST_ATC_QINDCFG_PARAMETER;

typedef struct {
    unsigned short                usEvent;
#define D_QEHPLMN_OPTION_DEL    0
#define D_QEHPLMN_OPTION_ADD    1
    unsigned char                 ucOption;
    unsigned char                 ucIndex;
    unsigned char                 ucNum;
    unsigned int                  aulPlmn[15];
} ST_ATC_QEHPLMN_PARAMETER;

typedef union
{
    ST_ATC_CMD_COM_EVENT               stCmdComParam;
    ST_ATC_CGDCONT_PARAMETER           stCgdcontParam;
    ST_ATC_DPS_PARAMETER               stDpsParam;
    ST_ATC_PSMR_PARAMETER              stPsmrParam;
    ST_ATC_CMD_PARAMETER               stCmdParam;
    ST_ATC_CGSN_PARAMETER              stCgsnParam;
    ST_ATC_CGDSCONT_PARAMETER          stCgdscontParam;
    ST_ATC_CGEQOS_PARAMETER            stCgeqosParam;
    ST_ATC_CGCMOD_PARAMETER            stCgcmodParam;
    ST_ATC_CGTFT_PARAMETER             stCgtftParam;
    ST_ATC_CEREG_PARAMETER             stCeregParam;
    ST_ATC_CSODCP_PARAMETER            stCsodcpParam;
    ST_ATC_CGAPNRC_PARAMETER           stCgapnrcParam;
    ST_ATC_CRTDCP_PARAMETER            stCrtdcpParam;
    ST_ATC_CEDRXS_PARAMETER            stCedrxsParam;
    ST_ATC_CPSMS_PARAMETER             stCpsmsParam;
    ST_ATC_CFUN_PARAMETER              stCfunParam;
    ST_ATC_CGACT_PARAMETER             stCgactParam;
#ifdef LTE_SMS_FEATURE    
    ST_ATC_CSCA_PARAMETER              stCscaParam;
    ST_ATC_AP_SMS_PDU_PARAMETER        stAtcApPduParam;
    ST_ATC_AP_SMS_TEXT_PARAMETER       stAtcApTextParam;
    ST_ATC_PDU_TPDU_PARAMETER          stPduTpduParam;
    ST_ATC_TEXT_PARAMETER              stTextParam;
    ST_ATC_QCMGS_PARAMETER             stQcmgsParam;
#endif
    ST_ATC_CGDATA_PARAMETER            stCgdataParam;
    ST_ATC_CGPADDR_PARAMETER           stCgpaddrParam;
    ST_ATC_CMUX_PARAMETER              stCmuxParam;
    ST_ATC_COPS_PARAMETER              stCopsParam;
    ST_ATC_CSIM_PARAMETER              stCsimParam;
    ST_ATC_CCHO_PARAMETER              stCchoParam;
    ST_ATC_CCHC_PARAMETER              stCchcParam;
    ST_ATC_CGLA_PARAMETER              stCglaParam;
    ST_ATC_CRSM_PARAMETER              stCrsmParam;
    ST_ATC_CCIOTOPT_PARAMETER          stCciotoptParam;
    ST_ATC_CGEQOSRDP_PARAMETER         stCgeqosrdpParam; 
    ST_ATC_CPWD_PARAMETER              stCpwdParam;
    ST_ATC_CPIN_PARAMETER              stCpinParam;
    ST_ATC_CLCK_PARAMETER              stClckParam;
    ST_ATC_CGEV_PARAMETER              stCgevParam;
    ST_ATC_CIPCA_PARAMETER             stCipcaParam;
    ST_ATC_CGAUTH_PARAMETER            stCgauthParam;
    ST_ATC_CGEREP_PARAMETER            stCgerepParam;
    ST_ATC_CTZR_PARAMETER              stCtzrParam;
    ST_ATC_CGCONTRDP_PARAMETER         stCgcontrdpParam;
    ST_ATC_NUESTATS_PARAMETER          stNuestatsParam;
    ST_ATC_NEARFCN_PARAMETER           stNearfcnParam;
    ST_ATC_NBAND_PARAMETER             stNbandParam;
    ST_ATC_NCONFIG_PARAMETER           stNconfigParam;
    //ST_ATC_SOCKET_INFO_STRU            stSocketInfoParam;
    //ST_ATC_UDP_DATA_STRU               stUdpDataParam;
    //ST_ATC_PING_DATA_STRU              stPingDataParam;
    ST_ATC_NSET_PARAMETER              stNsetParam;
    ST_ATC_CPINR_PARAMETER             stCpinrParam;
    ST_ATC_CMMS_PARAMETER              stCmmsParam;
    ST_ATC_NPOWERCLASS_PARAMETER       stNpowerclassParam;
    ST_ATC_NPIN_PARAMETER              stNpinParam;
    ST_ATC_NTSETID_PARAMETER           stNtsetIdParam;
    ST_ATC_NCIDSTATUS_PARAMETER        stNcidstatusParam;
    ST_ATC_NGACTR_PARAMETER            stNgactrParam;
    ST_ATC_NPOPB_PARAMETER             stNpopbParam;
    ST_ATC_NIPINFO_PARAMETER           stNipInfoParam;
    ST_ATC_NQPODCP_PARAMETER           stNqpodcpParam;
    ST_ATC_NSNPD_PARAMETER             stNsnpdParam;
    ST_ATC_CNEC_PARAMETER              stCnecParam;
    ST_ATC_NCPCDPR_PARAMETER           stNcpcdprParam;
    ST_ATC_MNBIOTEVENT_PARAMETER       stMnbiotEventParam;
    ST_ATC_CGSCONTRDP_PARAMETER        stCgscontrdpParam;
    ST_ATC_CGTFTRDP_PARAMETER          stCgtftrdpParam;
    ST_ATC_NLOCKF_PARAMETER            stNlockfParam;
#ifdef CSG_FEATURE
    ST_ATC_NCSG_PARAMETER              stNcsgParam;
#endif
    ST_ATC_CACDC_PARAMETER             stCacdcParam;
    ST_ATC_PINGXY_PARAMETER            stPingxyParam;
    ST_ATC_PSTEST_PARAMETER            stPstestParam;
    ST_ATC_QICSGP_PARAMETER            stQicsgpParam;
    ST_ATC_QENG_PARAMETER              stQengParam;
    ST_ATC_MLOCKFREQ_PARAMETER         stMlockfreqParam;
    ST_ATC_PCTTESTINFO_PARAMETER       stPCTTestInfo;
    ST_ATC_NPREEARFCN_PARAMETER        stNPreEarfcnParam;
    ST_ATC_QSCLKEX_PARAMETER           stQsclkexParam;
    ST_ATC_QWIFISCAN_PARAMETER         stQwifiscanParam;
    ST_ATC_CIDACT_PARAMETER            atCidactParam;
    ST_ATC_QEHPLMN_PARAMETER           stQEHPLMNParam;
}UN_ATC_CMD_EVENT;

/*******************************************************************************
* The message for PS send to ATC_AP
*******************************************************************************/
/****************************STRU of internal message *************************/
typedef struct  
{
#define  EPS_IPV6_LEN          16
    unsigned char     aucIpv6Addrs[EPS_IPV6_LEN];
}LTE_IPV6_ADDRESS_STRU;

typedef struct  
{
#define  EPS_IPV4_LEN           4
    unsigned char     aucIpv4Addrs[EPS_IPV4_LEN];
}LTE_IPV4_ADDRESS_STRU;

typedef struct  
{
    unsigned char                  ucCid;
    unsigned char                  ucPdpType;
    unsigned char                  ucPdpaddr1Flg;
    LTE_IPV4_ADDRESS_STRU          Pdpaddr1;
    unsigned char                  ucPdpaddr2Flg;
    LTE_IPV6_ADDRESS_STRU          PdpAddr2;
}LTE_ESM_PDP_ADRR_INFO_READ_STRU;

typedef struct  
{
    unsigned char                     ucCidNum;
    LTE_ESM_PDP_ADRR_INFO_READ_STRU   aPdpAddr[D_MAX_CNT_CID];
}API_ESM_PDP_ADRR_INFO_READ_STRU;

typedef struct  
{
    unsigned char                     ucCidNum;
    unsigned char                     aucCid[D_MAX_CNT_CID];
}API_ESM_DEFINED_CID_LIST_STRU;

typedef struct
{
    unsigned char     ucPacketFilterId:5;                                               /* Value range is from 1 to 16            */
    unsigned char     ucDirection:2; 
    unsigned char     ucProtocolNum_NextHeaderFlg:1;

    unsigned char     ucIpsecSPIFlg:1;
    unsigned char     ucFlowLabelFlg:1;
    unsigned char     ucLocalPortRangeLen:2;                                              /* <local port range>: string type. The string is given as dot-separated numeric (0-65535) parameters on the form "f.t".*/ 
    unsigned char     ucRemotePortRangeLen:2;                                             /* remote port range>: string type. The string is given as dot-separated numeric (0-65535) parameters on the form "f.t". */
    unsigned char     ucTypeOfServiceAndMaskLen:2;                                        /* <type of service (tos) (ipv4) and mask / traffic class (ipv6) and mask>: */
      
    unsigned char     ucEvaluationPrecedenceIndex;                                      /* The value range is from 0 to 255       */
    unsigned char     ucRemoteAddrAndSubMaskLen;                                        /* Length of local_addr and subnet_mask, 8 for IPv4,32 for IPv6    */
    unsigned char     aucRemoteAddrAndSubMask[32];                                      /* remote address and subnet mask */

    unsigned char     ucProtocolNum_NextHeader;                                         /* protocol number (ipv4) / next header (ipv6)>: integer type. Value range is from 0 to 255. */
                                                          /* <ipsec security parameter index (spi)>: numeric value in hexadecimal format. The value range is from 00000000 to FFFFFFFF.  */

    unsigned char     aucTypeOfServiceAndMask[2];         /* <flow label (ipv6)>: numeric value in hexadecimal format. The value range is from 00000 to FFFFF. Valid */
                                                                                        /*  for IPv6 only. */
                                                                                        /*  0  Pre-Release 7 TFT filter (see 3GPP TS 24.008 [8], table 10.5.162) */
                                                                                        /* 1 Uplink,  2 Downlink ,3 Birectional (Up & Downlink) */
    unsigned char     ucLocalAddrAndSubMaskLen;
    unsigned char     aucLocalAddrAndSubMask[32];                                       /* <local address and subnet mask>: string type. The string is given as dot-separated numeric (0-255) 
                                                                                           parameters on the form: 
                                                                                           "a1.a2.a3.a4.m1.m2.m3.m4" for IPv4 or 
                                                                                           "a1.a2.a3.a4.a5.a6.a7.a8.a9.a10.a11.a12.a13.a14.a15.a16.m1.m2.m3.m4.m5.m6.m7.m8.m9.m10.m11.m12.m13.
                                                                                           m14.m15.m16", for IPv6. 
                                                                                           When +CGPIAFis supported, its settings can influence the format of this parameter returned with the read form 
                                                                                           of +CGTFT */

    unsigned short    ausLocalPortRange[2];
    unsigned short    ausRemotePortRange[2];

    unsigned int      ulIpsecSPI;
    unsigned int      ulFlowLabel; 
} API_EPS_SET_PF_INFO;

typedef struct
{
    unsigned char        ucCid;                     

#define D_EPS_CONTEXT_STA_DEACT     0
#define D_EPS_CONTEXT_STA_ACTIVE    1
    unsigned char        ucState;                   
} EPS_CID_STATE_INFO;

typedef struct
{
    unsigned char        ucValidNum;                
    EPS_CID_STATE_INFO   aCidSta[D_MAX_CNT_CID];               
} API_EPS_CTXT_STATE_INFO_STRU;

typedef struct  
{
    unsigned char             ucCid;
    unsigned char             ucAdditionExcepReportFlg;                                         /* 0: NOT allowed, 1: allowed  */
    unsigned int              ulUplinkTimeUnit;                                                 /* unit is 1 min  */
    unsigned int              ulMaxUplinkRate;
    unsigned short            usMaxUplinkMsgSize;
}EPS_APN_RATE_CONTRL_STRU;

/* API_EPS_CGDSCONT_Para_Set_Req */
typedef struct
{
    unsigned char        ucCid;                                                         /* 1-10 */
    unsigned char        ucDelFlg;
    unsigned char        ucP_cid;                                                       /* 0-10 */
    unsigned char        ucImCnSignallingFlagIndFlg;
    unsigned char        ucImCnSignallingFlagInd;
} API_EPS_CGDSCONT_PARA_SET_REQ_STRU;

typedef struct
{
    unsigned char     ucProtocolNum_NextHeaderFlg:1;
    unsigned char     ucFlowLabelFlg:1;
    unsigned char     ucLocalPortRangeLen:2;                                              /* <local port range>: string type. The string is given as dot-separated numeric (0-65535) parameters on the form "f.t".*/ 
    unsigned char     ucRemotePortRangeLen:2;                                             /* remote port range>: string type. The string is given as dot-separated numeric (0-65535) parameters on the form "f.t". */
    unsigned char     ucTypeOfServiceAndMaskLen:2;                                        /* <type of service (tos) (ipv4) and mask / traffic class (ipv6) and mask>: */
    unsigned char     ucIpsecSPIFlg:1;
    unsigned char     ucPadding:7;
    unsigned char     ucRemoteAddrLen;
    unsigned char     ucLocalAddrLen;

    unsigned char     ucProtocolNum_NextHeader;                                         /* protocol number (ipv4) / next header (ipv6)>: integer type. Value range is from 0 to 255. */
    unsigned int      ulIpsecSPI;                                                       /* <ipsec security parameter index (spi)>: numeric value in hexadecimal format. The value range is from 00000000 to FFFFFFFF.  */
    unsigned char     aucTypeOfServiceAndMask[2];                                       /*string type. The string is given as dot-separated numeric (0-255) parameters on the form "t.m". */  
    unsigned short    ausLocalPortRange[2];
    unsigned short    ausRemotePortRange[2];


    unsigned int      ulFlowLabel; 
    unsigned char*    paucRemoteAddrAndSubMask;                                         /* remote address and subnet mask */
    unsigned char*    paucLocalAddrAndSubMask;                                       /* <local address and subnet mask>: string type. The string is given as dot-separated numeric (0-255) 
                                                                                               parameters on the form: 
                                                                                               "a1.a2.a3.a4.m1.m2.m3.m4" for IPv4 or 
                                                                                               "a1.a2.a3.a4.a5.a6.a7.a8.a9.a10.a11.a12.a13.a14.a15.a16.m1.m2.m3.m4.m5.m6.m7.m8.m9.m10.m11.m12.m13.
                                                                                               m14.m15.m16", for IPv6. 
                                                                                               When +CGPIAFis supported, its settings can influence the format of this parameter returned with the read form 
                                                                                               of +CGTFT */

} EPS_PacketFilter_Attr_STRU;


typedef struct
{
    unsigned char     ucPacketFilterId:4;                                               /* Value range is from 1 to 16            */
    unsigned char     ucDirection:2; 
    unsigned char     ucPadding:2;
    unsigned char     ucEvaluationPrecedenceIndex;                                      /* The value range is from 0 to 255       */
    EPS_PacketFilter_Attr_STRU* pPfAttr;
} EPS_PF_INFO;

typedef struct
{
    unsigned char     ucPacketFilterId:5;                                               /* Value range is from 1 to 16            */
    unsigned char     ucDirection:2; 
    unsigned char     ucProtocolNum_NextHeaderFlg:1;

    unsigned char     ucIpsecSPIFlg:1;
    unsigned char     ucFlowLabelFlg:1;
    unsigned char     ucLocalPortRangeLen:2;                                              /* <local port range>: string type. The string is given as dot-separated numeric (0-65535) parameters on the form "f.t".*/ 
    unsigned char     ucRemotePortRangeLen:2;                                             /* remote port range>: string type. The string is given as dot-separated numeric (0-65535) parameters on the form "f.t". */
    unsigned char     ucTypeOfServiceAndMaskLen:2;                                        /* <type of service (tos) (ipv4) and mask / traffic class (ipv6) and mask>: */
      
    unsigned char     ucEvaluationPrecedenceIndex;                                      /* The value range is from 0 to 255       */
    unsigned char     ucRemoteAddrAndSubMaskLen;                                        /* Length of local_addr and subnet_mask, 8 for IPv4,32 for IPv6    */
    uint64_t          pucRemoteAddrAndSubMask;                                         /* remote address and subnet mask */

    unsigned char     ucProtocolNum_NextHeader;                                         /* protocol number (ipv4) / next header (ipv6)>: integer type. Value range is from 0 to 255. */
                                                          /* <ipsec security parameter index (spi)>: numeric value in hexadecimal format. The value range is from 00000000 to FFFFFFFF.  */

    unsigned char     aucTypeOfServiceAndMask[2];                                       /*string type. The string is given as dot-separated numeric (0-255) parameters on the form "t.m". */
                                                         /* <flow label (ipv6)>: numeric value in hexadecimal format. The value range is from 00000 to FFFFF. Valid */
                                                                                        /*  for IPv6 only. */
                                                                                        /*  0  Pre-Release 7 TFT filter (see 3GPP TS 24.008 [8], table 10.5.162) */
                                                                                        /* 1 Uplink,  2 Downlink ,3 Birectional (Up & Downlink) */

    unsigned char     ucLocalAddrAndSubMaskLen;
    uint64_t          pucLocalAddrAndSubMask;                                           /* <local address and subnet mask>: string type. The string is given as dot-separated numeric (0-255) 
                                                                                           parameters on the form: 
                                                                                           "a1.a2.a3.a4.m1.m2.m3.m4" for IPv4 or 
                                                                                           "a1.a2.a3.a4.a5.a6.a7.a8.a9.a10.a11.a12.a13.a14.a15.a16.m1.m2.m3.m4.m5.m6.m7.m8.m9.m10.m11.m12.m13.
                                                                                           m14.m15.m16", for IPv6. 
                                                                                           When +CGPIAFis supported, its settings can influence the format of this parameter returned with the read form 
                                                                                           of +CGTFT */

    unsigned short    ausLocalPortRange[2];
    unsigned short    ausRemotePortRange[2];

    unsigned int      ulIpsecSPI;
    unsigned int      ulFlowLabel; 
} EPS_SET_PF_INFO;

/* API_EPS_CGDSCONT_Para_Read_Req */
typedef struct  
{
    unsigned char                           ucValidNum;
    API_EPS_CGDSCONT_PARA_SET_REQ_STRU       aSPdpCtxtPara[D_MAX_CNT_CID - 1];
}API_EPS_CGDSCONT_PARA_READ_REQ_STRU;

/* API_EPS_Qos_Para_Set_Req */
typedef struct
{
    unsigned char        ucQci;           
    unsigned char        ucBrFlg;                                                       /* For all non-GBR QCIs,the maximum and guaranteed bit rates shall be omitted */
    unsigned int         ulDlGbr;
    unsigned int         ulUlGbr;
    unsigned int         ulDlMbr;
    unsigned int         ulUlMbr;
} EPS_QOS_INFO;

typedef struct
{
    unsigned char           ucCid;                                                      /* rang: 0-10                       */
    unsigned char           ucDelFlg;
    EPS_QOS_INFO            stQosInfo;
}API_EPS_QOS_PARA_SET_REQ_STRU;

/* API_EPS_Qos_Para_Read_Req */
typedef struct
{
    unsigned char           ucCid;                                                      /* rang: 0-10                       */
    EPS_QOS_INFO            stQosInfo;
}EPS_QOS_PARA_READ_STRU;

typedef struct
{
    unsigned char           ucValidNum;
    EPS_QOS_PARA_READ_STRU  aQosInfo[10];
}API_EPS_QOS_PARA_READ_REQ_STRU;

/* API_EPS_OpeSelInf_Read_Req */ 
typedef struct
{
#define D_ATC_AP_COPS_MODE_MANUAL                    1
#define D_ATC_AP_COPS_MODE_MANUAL_AUTO               4
    unsigned char     ucMode;
    unsigned char     ucPlmnSelFlg;
#define PLMN_FORMAT_LONG_ALPHA  0
#define PLMN_FORMAT_SHORT_ALPHA 1
#define PLMN_FORMAT_NUMERIC     2
    unsigned char     ucFormat;
    unsigned int      ulPlmnNum;
#define PLMN_ACT_E_UTRAN 7
    unsigned char     ucAct;
    unsigned char     ucCsgIdFlg;
    unsigned int      uiCsgId;
    unsigned char     ucPsAttachFlg;
    unsigned char     aucNetworkName[17];
} API_EPS_OPESELINFO_READ_REQ_STRU;

typedef struct
{        
    unsigned char       ucN;
#define EPS_NO_SUPPORT                    0
#define EPS_SUPPORT_CP_ONLY               1
#define EPS_SUPPORT_UP_ONLY               2
#define EPS_DEFAULT_SUPPORT_UPCP          3   /*NAS Default value*/
    unsigned char       ucSupptUeOpt;
#define EPS_NO_PREFERENCE                 0
#define EPS_DEFAULT_PREFERENCE_FOR_CP     1 /*NAS Default value*/
#define EPS_PREFERENCE_FOR_UP             2
    unsigned char       ucPreferOpt;
    unsigned char       ucCciotoptN;
} API_CCIOTOPT_CONFIG_STRU;

/*  API_EPS_CEDRXS_Para_Set_Req   */
typedef struct  
{
    unsigned char  ucMode;
#define EMM_EDRX_DEFAULT_VALUE              2                                           /* The minimum EDRXValue */
#define EMM_EDRX_INVALID_VALUE              0xFF                                        /* The invalid EDRXValue */
    unsigned char  ucIsDefaultEDRXVal;
    unsigned char  ucEDRXValue;                                                         /* Do not support 0,1,4,6,7,8,   24008    10.5.5.32  */
#define EMM_PTW_DEFAULT_VALUE               3                                           /* The Default PTWVALUE */
#define EMM_PTW_INVALID_VALUE               0xFF                                        /* The invalid PTWVALUE */
    unsigned char  ucPTWValue;         
}EPS_CEDRXS_PARA_STRU;

typedef struct  
{
#define LTE_EDRX_NOT_USING                  0
#define LTE_EDRX_E_UTRAN_WBS1               4
    unsigned char                                   ucNptwEDrxMode;
    unsigned char                                   ucActType;
    unsigned char                                   ucReqDRXValue;                              /* Requested eDRX value */
    unsigned char                                   ucReqPagingTimeWin;                          /* Requested provided Paging_time_window */
    unsigned char                                   ucNWeDRXValueFlg;
    unsigned char                                   ucNWeDRXValue;                               /* NW provided eDRX value */
    unsigned char                                   ucPagingTimeWinFlg;
    unsigned char                                   ucPagingTimeWin;                             /* Paging_time_window */
}API_NWCEDRXS_PARA_STRU;

typedef struct
{
    unsigned char        ucApnAmbrFlag;
    unsigned int         ulDlApnAmbr;                                                   /* APN aggregate maximum bit rate for downlink  */
    unsigned int         ulUlApnAmbr;                                                   /* APN aggregate maximum bit rate for uplink    */
} EPS_APN_AMBR_INFO;

/* CGEQOSRDP */
typedef struct
{
    unsigned char           ucCid;                                                      /* rang: 0-10                       */
    EPS_QOS_INFO            stQosInfo;
    EPS_APN_AMBR_INFO       stApnAmbrInfo;
}EPS_CGEQOSRDP_PARA_READ_STRU;
typedef struct
{
    unsigned char                ucValidNum;
    EPS_CGEQOSRDP_PARA_READ_STRU  aQosApnAmbrInfo[D_MAX_CNT_CID];
}API_EPS_CGEQOSRDP_PARA_READ_REQ_STRU;

typedef struct
{
    unsigned char       ucPriDnsAddr_IPv4Flg:1;
    unsigned char       ucSecDnsAddr_IPv4Flg:1;
    unsigned char       ucPriDnsAddr_IPv6Flg:1;
    unsigned char       ucSecDnsAddr_IPv6Flg:1;
    unsigned char       :4;
    unsigned char       ucPriDnsAddr_IPv4[4];                                           /* Primary IPv4 DNS Address */               
    unsigned char       ucSecDnsAddr_IPv4[4];                                           /* Secondary IPv4 DNS Address */ 
    unsigned char       ucPriDnsAddr_IPv6[16];
    unsigned char       ucSecDnsAddr_IPv6[16];
} EPS_DNS_ADDR_STRU;

/* API_EPS_PDN_IPDNS_IND_STRU */
typedef struct
{
    unsigned char       ucCid;                                                          /* rang: 0-10                       */
    unsigned char       ucPdpType;                                                      /* PDP type number value            */
    unsigned char       aucIPv4Addr[4];
    unsigned char       aucIPv6Addr[16];
    unsigned short      usIPv4MTU;
    EPS_DNS_ADDR_STRU   stDnsAddr;
}API_EPS_PDN_IPDNS_IND_STRU;

typedef struct
{
//#define D_ATC_ADDR_LEN     32
    unsigned char   ucPdpType;
    unsigned char   ucCid;
    unsigned char   ucBearerId;
    unsigned char   ucApnLen;
    unsigned char   aucApn[D_APN_LEN];
    unsigned char   aucPdpAddrValue[12];          /* octet:0-7: IPV6, octet: 8-11: IPV4 */
    //unsigned char   ucGwAddrLen;
    //unsigned char aucGwAddr[2];
    //unsigned char   ucDNSPrimAddrLen;
    //unsigned char   aucDNSPrimAddr[D_ATC_ADDR_LEN];
    //unsigned char   ucDNSSecAddrLen;
    //unsigned char   aucDNSSecAddr[D_ATC_ADDR_LEN];
    //unsigned char   ucPCSCFPrimAddrLen;
    //unsigned char   aucPCSCFPrimAddr[D_ATC_ADDR_LEN];
    //unsigned char   ucPCSCFSecAddrLen;
    //unsigned char   aucPCSCFSecAddr[D_ATC_ADDR_LEN];
    //unsigned char   ucIMCNSignallingFlag;
    //unsigned char   ucIMCNSignalling;
    //unsigned char   ucLIPAIndFlag;
    //unsigned char   ucLIPAInd;
    unsigned char   ucIPv4MTUFlag;
    unsigned short  usIPv4MTU;
    //unsigned char   ucWLANOffloadFlag;
    //unsigned char   ucWLANOffload;
    //unsigned char   ucLocalAddrIndFlag;
    //unsigned char   ucLocalAddrInd;
    unsigned char   ucNonIPMTUFlag;
    unsigned short  usNonIPMTU;
    unsigned char   ucServingPLMNRateCtrValueFlag;
    unsigned short  usServingPLMNRateCtrValue;
    EPS_DNS_ADDR_STRU   stDnsAddr;
}EPS_CGCONTRDP_DYNAMIC_INFO;

typedef struct
{
    unsigned char                 ucValidNum;
    EPS_CGCONTRDP_DYNAMIC_INFO    aucPdpDynamicInfo[D_MAX_CNT_CID];
}API_CGCONTRDP_READ_DYNAMIC_PARA_STRU;

typedef struct
{
    unsigned char   ucCid;
    unsigned char   ucPcid;
    unsigned char   ucBearerId;
}EPS_CGSCONTRDP_DYNAMIC_INFO;

typedef struct
{
    unsigned char                 ucValidNum;
    EPS_CGSCONTRDP_DYNAMIC_INFO    aucPdpDynamicInfo[D_MAX_CNT_CID];
}API_CGSCONTRDP_READ_DYNAMIC_PARA_STRU;

/* Bind_EPS_OpeList_Search_Cnf */
typedef struct
{
#define D_NETWORK_STA_UNKNOWN       0
#define D_NETWORK_STA_AVAILABLE     1
#define D_NETWORK_STA_CURRENT       2
#define D_NETWORK_STA_FORBIDDEN     3
    unsigned char                   ucState;                   
    unsigned int                    ulPlmn;                                             /* MCC2+MCC1+MNC3+MCC3+MNC2+MNC1        */
    unsigned char                   aucFullNetworkName[17];
    unsigned char                   aucShortNetworkName[17];
} EPS_OPERATOR_INF;

typedef struct
{
    unsigned char               ucResult;                                              /* 0: PS_SUCC, 1:PS_FAIL */
    unsigned char               ucNum;
#define D_ATC_OPELIST_MAX_NUM   20
    EPS_OPERATOR_INF            aOpeInf[D_ATC_OPELIST_MAX_NUM];
} API_EPS_OPELIST_SRCH_CNF_STRU;

typedef struct
{
    unsigned char       ucLocalTimeZoneFlg;                                             /* ucLocalTimeZone valid flag               */
    unsigned char       ucLocalTimeZone;                                                /* Local Time Zone                          */
    unsigned char       ucUtAndLtzFlg;                                                  /* aucUtAndLtz valid flag                   */
#define D_ATC_UTANDLTZ_LEN    7
    unsigned char       aucUtAndLtz[D_ATC_UTANDLTZ_LEN];                                /* Universal Time And Local Time Zone       */
    unsigned char       ucNwDayltSavTimFlg;                                             /* ucNwDayltSavTim valid flag               */
    unsigned char       ucNwDayltSavTim; 
}LTE_NAS_LOCAL_TIME_STRU;

/* API_EMM_Read_Register_State  */
typedef struct
{
    unsigned int        ucOnlyUserRegEventIndFlg:1;
    unsigned int        OP_TacLac       : 1;
    //unsigned int        OP_Rac            : 1;
    unsigned int        OP_Act            : 1;
    unsigned int        OP_CellId        : 1;
    unsigned int        OP_CauseType    : 1;
    unsigned int        OP_RejectCause    : 1;
    unsigned int        OP_ActiveTime    : 1;
    unsigned int        OP_PeriodicTAU    : 1;
    unsigned int        OP_Spare    : 24;
#define LTE_EPS_REG_STATUS_NOT_REGISTERED       0
#define LTE_EPS_REG_STATUS_REG_HPLMN            1
#define LTE_EPS_REG_STATUS_REGINI_OR_SEARCH     2
#define LTE_EPS_REG_STATUS_REG_DENIED           3
#define LTE_EPS_REG_STATUS_UNKNOWN              4
#define LTE_EPS_REG_STATUS_REG_ROAMING          5
#define LTE_EPS_REG_STATUS_REG_EMERG_ONLY       8
    unsigned char        ucIndPara;   
    unsigned char        ucEPSRegStatus;
    #define LTE_E_UTRAN_WBS1_MODE               7
    unsigned char        ucAct;
    unsigned short       usTacOrLac;
   // unsigned short       usRac;
    unsigned int         ulCellId;
#define LTE_REJECT_EMM_CAUSE_VALUE              0
#define LTE_REJECT_SPEC_CAUSE_VALUE             1
    unsigned char        ucCauseType;
    unsigned char        ucRejectCause;
    unsigned char        ucActiveTime;
    unsigned char        ucPeriodicTAU;
} API_EPS_REG_STATUS_STRU;

/* API_IMSI_Read_Req */
typedef struct
{
    unsigned char        ucImsiLen;
#define D_EPS_IMSI_MAX_LEN                16
    unsigned char        aucImsi[D_EPS_IMSI_MAX_LEN];                                       /* read the IMEI (International Mobile station Equipment Identity) */
} API_EPS_IMSI_READ_STRU;

typedef struct
{        
    unsigned int        OP_State       : 1;
    unsigned int        OP_Access    : 1;
    unsigned int        OP_Spare    : 30;
    unsigned char        ucIndPara;   
#define EPS_SIGNALLING_IDLE           0
#define EPS_SIGNALLING_CONNECTED      1
    unsigned char        ucMode;
#define EPS_E_UTRAN_CONNECTED_STATE   7
    unsigned char        ucState;
#define EPS_E_UTRAN_TDD               3
#define EPS_E_UTRAN_FDD               4
    unsigned char       ucAccess;
} API_SIGNAL_CONN_STATUS_STRU;

#define     LRRC_NAS_MAX_CELL_NUM            6
typedef struct
{
    unsigned int               ulDlEarfcn;
    unsigned char              ucEarfcnOffset;
    unsigned char              ucDlBandWidth;
    unsigned char              ucBandInd;
    unsigned short             usPhyCellId;
    signed short               sRsrp;
    signed short               sRsrq;
    signed short               sSinr;
    signed short               sRssi;
    unsigned int               ulPlmn;
    unsigned int               ulCellId;
    unsigned short             usTac;
    signed short               sSrxlev;
}API_CELL_INFO_STRU;

typedef struct
{
    unsigned char ucCellNum;
    unsigned char ucEcl;
#define D_ACT_UNKNOWN      0
#define D_ACT_FDD_LTE      1
#define D_ACT_TDD_LTE      2
    unsigned char ucAct;
    unsigned char ucUlBandWidth;
    API_CELL_INFO_STRU aNCell[LRRC_NAS_MAX_CELL_NUM];
}API_CELL_LIST_STRU;

typedef struct
{
    unsigned char ucIntraInter; /*0: intra, 1: inter*/
    unsigned char ucPriority;
    unsigned char ucSIntraSearchP;
    unsigned char ucSNonIntraSearchP;
    unsigned char ucThreshServingLow;
    unsigned char ucThreshXHigh;
    unsigned char ucThreshXLow;
}API_CELL_RESEL_INFO_STRU;

typedef struct
{
    unsigned char       ucSupportBandNum;
    unsigned char       aucSuppBand[G_LRRC_SUPPORT_BAND_MAX_NUM];
}API_SUPP_BAND_LIST_STRU;

typedef struct
{
    unsigned char       ucCid;
#define   D_STATUS_AVAILABLE   0
#define   D_STATUS_NOT_EXIST   1
#define   D_STATUS_FLOW_CTRL   2
#define   D_STATUS_BACKOFF     3
    unsigned char       ucStatus;
    unsigned int        ulBackOffVal;
} EPS_CID_STATUS_INFO;

typedef struct
{
    unsigned char             ucNum;
    EPS_CID_STATUS_INFO       aCidStaus[D_MAX_CNT_CID];
} API_EPS_CID_STATUS_STRU;


#ifdef LCS_MOLR_ENABLE
/********************************** LCS MOLR RESULT ********************************/
typedef struct { 
    unsigned char                           ucUncertnSemiMajor;            /* INTEGER(0..127) */
    unsigned char                           ucUncertnSemiMin;              /* INTEGER(0..127) */
    unsigned char                           ucOrientMajorAxis;             /* INTEGER(0..179) */
} LCS_UNCERTN_ELLIP_STRU;

typedef struct { 
    unsigned char                           ucHeightAboveSurface;
    unsigned short                          usAlti;
} LCS_ALTI_STRU;

typedef struct { 
    unsigned char                           ucLatitudeSign;
    unsigned int                            uiDegreesLatitude;            /* INTEGER(0..8388607) */
} LCS_LATITUDE_STRU;

typedef struct { 
    LCS_LATITUDE_STRU               tLatitude;
    INT                             iDegreesLongitude;            /* INTEGER(-8388608..8388607) */
} LCS_COOR_STRU,LCS_SHAPE_ELLIP_POINT_STRU;

typedef struct { 
    LCS_COOR_STRU                   tCoordinate;
    unsigned char                           ucUncertn;                    /* INTEGER(0..127) */
} LCS_SHAPE_ELLIP_POINT_UNCERT_CIRCLE_STRU;

typedef struct { 
    LCS_COOR_STRU                   tCoordinate;
    LCS_UNCERTN_ELLIP_STRU          tUncertEllipse;
    unsigned char                           ucConfidence;                  /* INTEGER(0..100) */
} LCS_SHAPE_ELLIP_POINT_UNCERT_ELLIP_STRU;

typedef struct { 
    unsigned char                           ucCnt;
    LCS_COOR_STRU                   atCoordinate[15];
} LCS_SHAPE_POLYGON_STRU;

typedef struct { 
    LCS_COOR_STRU                   tCoordinate;
    LCS_ALTI_STRU                   tAltitude;
} LCS_SHAPE_ELLIP_POINT_ALT_STRU;

typedef struct { 
    LCS_COOR_STRU                   tCoordinate;
    LCS_ALTI_STRU                   tAltitude;
    LCS_UNCERTN_ELLIP_STRU          tUncertEllipse;
    unsigned char                           ucUncertnAlti;                 /* INTEGER(0..127) */
    unsigned char                           ucConfidence;                  /* INTEGER(0..100) */
} LCS_SHAPE_ELLIP_POINT_ALT_UNCERT_STRU;

typedef struct { 
    LCS_COOR_STRU                   tCoordinate;
    unsigned short                          usInnerRadius;                  /* INTEGER(0..65535) */
    unsigned char                           ucUncertnRadius;                /* INTEGER(0..127) */
    unsigned char                           ucOffsetAngle;                  /* INTEGER(0..179) */
    unsigned char                           ucIncludedAngle;                /* INTEGER(0..179) */
    unsigned char                           ucConfidence;                   /* INTEGER(0..100) */
} LCS_SHAPE_ELLIP_ARC_STRU;

typedef struct { 
#define LCS_SHAPE_ELLIP_POINT                           0
#define LCS_SHAPE_ELLIP_POINT_UNCERT_CIRCLE             1
#define LCS_SHAPE_ELLIP_POINT_UNCERT_ELLIP              3
#define LCS_SHAPE_POLYGON                               5
#define LCS_SHAPE_ELLIP_POINT_ALT                       8
#define LCS_SHAPE_ELLIP_POINT_ALT_UNCERT                9
#define LCS_SHAPE_ELLIP_ARC                             10
    unsigned char                                               ucShapeType;
    union  {
        LCS_SHAPE_ELLIP_POINT_STRU                      tEllipPoint;
        LCS_SHAPE_ELLIP_POINT_UNCERT_CIRCLE_STRU        tEllipPointUncertCircle;
        LCS_SHAPE_ELLIP_POINT_UNCERT_ELLIP_STRU         tEllipPointUncertEllip;
        LCS_SHAPE_POLYGON_STRU                          tPolygon;
        LCS_SHAPE_ELLIP_POINT_ALT_STRU                  tEllipPointWithAlti;
        LCS_SHAPE_ELLIP_POINT_ALT_UNCERT_STRU           tEllipPointWithAltiUncert;
        LCS_SHAPE_ELLIP_ARC_STRU                        tEllipArc;
    } u;
} LCS_SHAPE_DATA_STRU;

typedef struct
{
    unsigned short                                              usBearing;              
    unsigned short                                              usSpeed; 
} LCS_VEL_HORIZONTAL_STRU;

typedef struct
{
    unsigned char                                               ucDirect;              
    unsigned short                                              ucSpeed; 
} LCS_VEL_VERTICAL_STRU;

typedef struct { 
    unsigned char                                               OP_VertSpeed            : 1;
    unsigned char                                               OP_HorUncert            : 1;
    unsigned char                                               OP_VertUncert           : 1;
    unsigned char                                               OP_Spare                : 5;

    LCS_VEL_HORIZONTAL_STRU                             tHorizSpeed;
    LCS_VEL_VERTICAL_STRU                               tVertSpeed;             
    unsigned char                                               ucHorSpeedUncert;         
    unsigned char                                               ucVertSpeedUncert;
} LCS_VELOCITY_DATA_STRU;
#endif

/************************ PS->ATC_AP message ****************************/
//OK/ERR
typedef struct
{
    unsigned short              usEvent;
    unsigned char               ucResult;
    unsigned short              usErrCode;
#define   ATC_CMD_TYPE_CMEE  0
#define   ATC_CMD_TYPE_CMS   1
    unsigned char               ucCmdType;
} ST_ATC_AP_CMD_RST;

//+CGSN[=0/1/2/3];+QGSN
typedef struct
{
    unsigned short              usEvent;
#define   D_ATC_CGSN_TYPE_SN      0
#define   D_ATC_CGSN_TYPE_IMEI    1
#define   D_ATC_CGSN_TYPE_IMEISV  2
#define   D_ATC_CGSN_TYPE_SVN     3
    unsigned char               snt;
    unsigned char               ucLen;
    unsigned char               aucData[4];
} ATC_MSG_CGSN_CNF_STRU,
  ATC_MSG_QGSN_CNF_STRU;

 //+GSN
 typedef struct
 {
      unsigned short              usEvent;
      unsigned char               sntFlg;
      unsigned char               snt;
      unsigned char               ucLen;
      unsigned char               aucData[4];
 } ATC_MSG_GSN_CNF_STRU;

//+CEREG?
typedef struct
{
    unsigned short              usEvent;
    API_EPS_REG_STATUS_STRU     tRegisterState;
} ATC_MSG_CEREG_R_CNF_STRU,
  ATC_MSG_CGREG_R_CNF_STRU,
  ATC_MSG_CREG_R_CNF_STRU;

/* API_EMM_Read_Register_State  */
typedef struct
{
    unsigned int        ucOnlyUserRegEventIndFlg:1;
    unsigned int        OP_TacLac       : 1;
    //unsigned int        OP_Rac            : 1;
    unsigned int        OP_Act            : 1;
    unsigned int        OP_CellId        : 1;
    unsigned int        OP_CauseType    : 1;
    unsigned int        OP_RejectCause    : 1;
    unsigned int        OP_ucpadding    : 2;
    unsigned int        OP_Spare    : 24;
#define LTE_EPS_CREG_STATUS_NOT_REGISTERED       0
#define LTE_EPS_CREG_STATUS_REG_HPLMN            1
#define LTE_EPS_CREG_STATUS_REGINI_OR_SEARCH     2
#define LTE_EPS_CREG_STATUS_REG_DENIED           3
#define LTE_EPS_CREG_STATUS_UNKNOWN              4
#define LTE_EPS_CREG_STATUS_REG_ROAMING          5
#define LTE_EPS_CREG_STATUS_SMS_ONLY_HOME_NETWORK 6
#define LTE_EPS_CREG_STATUS_SMS_ONLY_ROAMING     7
#define LTE_EPS_CREG_STATUS_REG_EMERG_ONLY       8
    unsigned char        ucIndPara;   
    unsigned char        ucEPSRegStatus;
    #define LTE_E_UTRAN_WBS1_MODE               7
    unsigned char        ucAct;
    unsigned short       usTacOrLac;
    unsigned int         ulCellId;
#define LTE_REJECT_EMM_CAUSE_VALUE              0
#define LTE_REJECT_SPEC_CAUSE_VALUE             1
    unsigned char        ucCauseType;
    unsigned char        ucRejectCause;
} API_CREG_STATUS_STRU;

//+CGATT?
typedef struct
{
    unsigned short              usEvent;
    unsigned char               ucState;
} ATC_MSG_CGATT_R_CNF_STRU;

//+CIMI
typedef struct
{
    unsigned short              usEvent;
    API_EPS_IMSI_READ_STRU      stImsi;
} ATC_MSG_CIMI_CNF_STRU;

typedef struct
{
    unsigned char               ucAtCid;
    unsigned char               ucPdpType;
    unsigned char               aucApnValue[D_APN_LEN];
    unsigned char               ucH_comp;
    unsigned char               ucNSLPI;
    unsigned char               ucSecurePco;
} ATC_PDP_CONTEXT_STRU;

//+CGDCONT?
typedef struct
{
    unsigned short              usEvent;
    unsigned char               ucNum;
    ATC_PDP_CONTEXT_STRU        stPdpContext[D_MAX_CNT_CID];
} ATC_MSG_CGDCONT_R_CNF_STRU;

//+CFUN?
typedef struct
{
    unsigned short              usEvent;
    unsigned char               ucFunMode;
} ATC_MSG_CFUN_R_CNF_STRU;

//+CMEE=<val>/+CMEE?
typedef struct
{
    unsigned short              usEvent;
    unsigned char               ucErrMod;
} ATC_MSG_CMEE_CNF_STRU,
  ATC_MSG_CMEE_R_CNF_STRU;

//+CESQ
typedef struct
{
    unsigned short              usEvent;
    unsigned char               ucRxlev;
    unsigned char               ucBer;
    unsigned char               ucRscp;
    unsigned char               ucEcno;
    unsigned char               ucRsrq;
    unsigned char               ucRsrp;
    char                        cSinr;
} ATC_MSG_CESQ_CNF_STRU,
  ATC_MSG_CESQ_IND_STRU;

//+CSQ
typedef struct
{
    unsigned short              usEvent;
    unsigned char               ucRxlev;
    unsigned char               ucBer;
} ATC_MSG_CSQ_CNF_STRU;

//+CGPADDR[=<cid>]
typedef struct
{
    unsigned short                        usEvent;
    API_ESM_PDP_ADRR_INFO_READ_STRU       stPara; 
} ATC_MSG_CGPADDR_CNF_STRU;

//+CGPADDR=?
typedef struct
{
    unsigned short                        usEvent;
    API_ESM_DEFINED_CID_LIST_STRU         stPara; 
} ATC_MSG_CGPADDR_T_CNF_STRU;

//+CGACT?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucRusult;
    API_EPS_CTXT_STATE_INFO_STRU          stState; 
} ATC_MSG_CGACT_R_CNF_STRU;

//+CRTDCP=?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucCrtdcpRepValue;
} ATC_MSG_CRTDCP_R_CNF_STRU;

//+CEDRXS?;+NPTWEDRXS?
typedef struct
{
    unsigned short                        usEvent;
    API_NWCEDRXS_PARA_STRU                stPara;
} ATC_MSG_CEDRXS_R_CNF_STRU,
  ATC_MSG_NPTWEDRXS_R_CNF_STRU;

//+CPSMS?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucMode;
    unsigned char                         ucReqActTime;
    unsigned char                         ucReqPeriTAU;
} ATC_MSG_CPSMS_R_CNF_STRU;

//+CGAPNRC[=<cid>]
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucNum;
    EPS_APN_RATE_CONTRL_STRU              stEpsApnRateCtl[D_MAX_CNT_CID];
} ATC_MSG_CGAPNRC_CNF_STRU;

//+CGAPNRC=?
typedef struct
{
    unsigned short                        usEvent;
    API_ESM_DEFINED_CID_LIST_STRU          stPara;
} ATC_MSG_CGAPNRC_T_CNF_STRU;

//+CSCON?
typedef struct
{
    unsigned short                        usEvent;
    API_SIGNAL_CONN_STATUS_STRU           stPara;
} ATC_MSG_CSCON_R_CNF_STRU;

//+NL2THP?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucL2THPFlag;
    unsigned char                         ucTimerLen;
} ATC_MSG_NL2THP_R_CNF_STRU;

typedef struct
{
    short                                 rsrp;
    short                                 rssi;
    short                                 current_tx_power_level;
    unsigned int                          total_tx_time;
    unsigned int                          total_rx_time;
    unsigned int                          last_cell_ID;
    unsigned char                         last_ECL_value;
    short                                 last_snr_value;
    unsigned int                          last_earfcn_value;
    unsigned short                        last_pci_value;
    short                                 rsrq;
    unsigned int                          current_plmn;
    unsigned short                        current_tac;
    unsigned char                         band;
} ATC_NUESTATS_RSLT_RADIO_STRU;

typedef struct
{
    API_CELL_LIST_STRU                    stCellList;
} ATC_NUESTATS_RSLT_CELL_STRU;

typedef struct
{
    unsigned int                          rlc_ul_bler;
    unsigned int                          rlc_dl_bler;
    unsigned int                          mac_ul_bler;
    unsigned int                          mac_dl_bler;
    unsigned int                          total_bytes_transmit;
    unsigned int                          total_bytes_receive;
    
    unsigned int                          transport_blocks_send;
    unsigned int                          transport_blocks_receive;
    unsigned int                          transport_blocks_retrans;
    unsigned int                          total_ackOrNack_msg_receive;
} ATC_NUESTATS_RSLT_BLER_STRU;

typedef struct
{
    unsigned int                          rlc_ul;
    unsigned int                          rlc_dl;
    unsigned int                          mac_ul;
    unsigned int                          mac_dl;
} ATC_NUESTATS_RSLT_THP_STRU;

typedef struct
{
    unsigned int                          band;
} ATC_NUESTATS_RSLT_SBAND_STRU;

//+NUESTATS=<type>
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         type;
    unsigned char                         ucPadding;
    ATC_NUESTATS_RSLT_RADIO_STRU          stRadio;
    ATC_NUESTATS_RSLT_CELL_STRU           stCell;
    ATC_NUESTATS_RSLT_BLER_STRU           stBler;
    ATC_NUESTATS_RSLT_THP_STRU            stThp;
    ATC_NUESTATS_RSLT_SBAND_STRU          stBand;
} ATC_MSG_NUESTATS_CNF_STRU,
  ATC_MSG_MUESTATS_CNF_STRU;

//+NEARFCN?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         search_mode;
    unsigned int                          earfcn;
    unsigned short                        pci;
} ATC_MSG_NEARFCN_R_CNF_STRU;

//+NBAND?;+NBAND=?
typedef struct
{
    unsigned short                        usEvent;
    API_SUPP_BAND_LIST_STRU               stSupportBandList;
} ATC_MSG_NBAND_R_CNF_STRU,
  ATC_MSG_NBAND_T_CNF_STRU;

//+NCONFIG?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         autoconnect;
    unsigned char                         cell_reselection;
    unsigned char                         enable_bip;
    unsigned short                        barring_release_delay;
    unsigned char                         release_version;
    unsigned short                        sync_time_period;
    unsigned char                         pco_ie_type;
    unsigned char                         non_ip_no_sms_enable;
    unsigned char                         t3324_t3412_ext_chg_report;
} ATC_MSG_NCONFIG_R_CNF_STRU;

//+NCONFIG=?
typedef struct
{
    unsigned short                        usEvent;
#define D_ATC_NCONFIG_T_BIP_TYPE_SINGLE   0
#define D_ATC_NCONFIG_T_BIP_TYPE_NORMAL   1
    unsigned char                         ucBipSupportType;
    unsigned char                         enable_bip;
} ATC_MSG_NCONFIG_T_CNF_STRU;

//+NSET=<function>
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         aucInsValue[20];
    union
    {
        unsigned int                      ulValue;
        unsigned char                     aucValue[40];
    } u;
    unsigned char                         ucValue2Flg;
    unsigned int                          ulValue2;
} ATC_MSG_NSET_R_CNF_STRU;

//+CGDSCONT?
typedef struct
{
    unsigned short                        usEvent;
    API_EPS_CGDSCONT_PARA_READ_REQ_STRU   stPara;
} ATC_MSG_CGDSCONT_R_CNF_STRU;

//+CGDSCONT=?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucNum;
    unsigned char                         aucCid[D_ATC_MAX_CID];
    unsigned char                         ucP_CidNum;
    unsigned char                         aucP_Cid[D_ATC_MAX_CID];
} ATC_MSG_CGDSCONT_T_CNF_STRU;

//+CGSCONTRDP[=<cid>]
typedef struct
{
    unsigned short                        usEvent;
    API_CGSCONTRDP_READ_DYNAMIC_PARA_STRU  stPara;
} ATC_MSG_CGSCONTRDP_CNF_STRU;

//+CGSCONTRDP=?
typedef struct
{
    unsigned short                        usEvent;
    API_ESM_DEFINED_CID_LIST_STRU         stPara;
} ATC_MSG_CGSCONTRDP_T_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;   
    unsigned char                         ucAtCid;
    unsigned char                         ucPacketFilterNum;
    API_EPS_SET_PF_INFO                   atPacketFilter[D_ATC_MAX_PACKET_FILTER];
} ATC_MSG_CGTFT_R_CNF_STRU;

typedef struct
{
    unsigned char                         ucAtCid;
    unsigned char                         ucPacketFilterNum;
    unsigned char                         aucPacketFilterIdMapInfo[D_ATC_MAX_PACKET_FILTER];
    API_EPS_SET_PF_INFO                   atPacketFilter[D_ATC_MAX_PACKET_FILTER];
} ATC_ACTIVE_EPS_BEARER_TFT_INFO_STRU;

//CGTFTRDP
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucPacketNum;
    unsigned char                         ucCidNum;
    ATC_ACTIVE_EPS_BEARER_TFT_INFO_STRU   atBearerTft[1];
} ATC_MSG_CGTFTRDP_CNF_STRU;

//CGTFTRDP=?
typedef struct
{
    unsigned short                        usEvent;
    API_ESM_DEFINED_CID_LIST_STRU         stPara;
} ATC_MSG_CGTFTRDP_T_CNF_STRU;

//+CGEQOS?
typedef struct
{
    unsigned short                        usEvent;
    API_EPS_QOS_PARA_READ_REQ_STRU        stPara;
} ATC_MSG_CGEQOS_R_CNF_STRU;

//+CGCMOD=?
typedef struct
{
    unsigned short                        usEvent;
    API_ESM_DEFINED_CID_LIST_STRU         stPara;
} ATC_MSG_CGCMOD_T_CNF_STRU;

//+COPS?
typedef struct
{
    unsigned short                        usEvent;
    API_EPS_OPESELINFO_READ_REQ_STRU      stPara;
} ATC_MSG_COPS_R_CNF_STRU;

//+COPS=?
typedef struct
{
  unsigned short                          usEvent;
  API_EPS_OPELIST_SRCH_CNF_STRU           stPara;
} ATC_MSG_COPS_T_CNF_STRU;

//+CGEREP?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucCgerepMode;
    unsigned char                         ucCgerepBfr;
} ATC_MSG_CGEREP_R_CNF_STRU;

//+CCIOTOPT?
typedef struct
{
    unsigned short                        usEvent;
    API_CCIOTOPT_CONFIG_STRU              stPara;
} ATC_MSG_CCIOTOPT_R_CNF_STRU;

//+CEDRXRDP
typedef struct
{
    unsigned short                        usEvent;
    API_NWCEDRXS_PARA_STRU                stPara;
} ATC_MSG_CEDRXRDP_CNF_STRU;

//+CGEQOSRDP[=<cid>]
typedef struct
{
    unsigned short                        usEvent;
    API_EPS_CGEQOSRDP_PARA_READ_REQ_STRU  stPara;
} ATC_MSG_CGEQOSRDP_CNF_STRU;

//+CGEQOSRDP=?
typedef struct
{
    unsigned short                        usEvent;
    API_ESM_DEFINED_CID_LIST_STRU         stPara;
} ATC_MSG_CGEQOSRDP_T_CNF_STRU;

//+CTZR?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucCtzrReport;
} ATC_MSG_CTZR_R_CNF_STRU;

//+CGCONTRDP[=<cid>]
typedef struct
{
    unsigned short                        usEvent;
    API_CGCONTRDP_READ_DYNAMIC_PARA_STRU  stPara;
} ATC_MSG_CGCONTRDP_CNF_STRU,
  ATC_MSG_QIACT_R_CNF_STRU;

//+CGCONTRDP=?
typedef struct
{
    unsigned short                        usEvent;
    API_ESM_DEFINED_CID_LIST_STRU         stPara;
} ATC_MSG_CGCONTRDP_T_CNF_STRU;

//+CPIN?
typedef struct
{
    unsigned short                        usEvent;
    unsigned short                        ucPinStatus;
} ATC_MSG_CPIN_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
#define D_ATC_PIN_STATUS_PIN_REQUIRED     0
#define D_ATC_PIN_STATUS_PUK_REQUIRED     1
#define D_ATC_PIN_STATUS_PUK_BLOCK        2
    unsigned char                         ucPinStatus;
} ATC_MSG_PIN_STATUS_IND_STRU;

//+CLCK=<fac>,<mode>[,<passwd>]
typedef struct
{
    unsigned short                        usEvent;
#define D_ATC_CLCK_STATUS_ACTIVE       0
#define D_ATC_CLCK_STATUS_DEACTIVE     1
    unsigned short                        ucStatus;
} ATC_MSG_CLCK_CNF_STRU;

//+NPIN=<command>,<paremeter1>
typedef struct
{
    unsigned short                        usEvent;
    unsigned short                        usResult;
} ATC_MSG_NPIN_CNF_STRU;

typedef struct
{
#define D_ATC_CPINR_TYPE_PIN1      0
#define D_ATC_CPINR_TYPE_PUK1      1
#define D_ATC_CPINR_TYPE_PIN2      2
#define D_ATC_CPINR_TYPE_PUK2      3
    unsigned char                         ucPinType;
    unsigned char                         ucRetriesNum;
} ATC_PIN_RETRIES_RES_STRU;

//+CPINR[=<sel_code>]
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucNum;
    ATC_PIN_RETRIES_RES_STRU              aPinRetires[4];
} ATC_MSG_CPINR_CNF_STRU,
  ATC_MSG_QPINC_CNF_STRU;

//+CEER:<err>
typedef struct
{
    unsigned short                        usEvent;
#ifndef D_FAIL_CAUSE_TYPE_NULL
#define  D_FAIL_CAUSE_TYPE_NULL      0
#define  D_FAIL_CAUSE_TYPE_EMM       1
#define  D_FAIL_CAUSE_TYPE_ESM       2
#define  D_FAIL_CAUSE_TYPE_USER_OPT  3
#endif
    unsigned char                         ucType;
    unsigned char                         ucCause;
} ATC_MSG_CEER_IND_STRU;

//+CIPCA?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         n;
    unsigned char                         ucAttachWithOutPdn;
} ATC_MSG_CIPCA_R_CNF_STRU;

typedef struct
{
    unsigned char                         ucAtCid;
    unsigned char                         ucAuthProt;
    unsigned char                         aucUserName[D_PCO_AUTH_MAX_LEN];
    unsigned char                         aucPassword[D_PCO_AUTH_MAX_LEN];
} ATC_CGAUTH_INFO_STRU;

//+CGAUTH?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucNum;
    ATC_CGAUTH_INFO_STRU                  stCgauth[D_MAX_CNT_CID];
} ATC_MSG_CGAUTH_R_CNF_STRU;

typedef struct
{
    unsigned short                        usBand;
    unsigned char                         ucPowerClass;
} ATC_SUPPORT_BAND_INFO_STRU;

//+NPOWERCLASS?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         unNum;
    ATC_SUPPORT_BAND_INFO_STRU            stSupportBandInfo[G_LRRC_SUPPORT_BAND_MAX_NUM];
} ATC_MSG_NPOWERCLASS_R_CNF_STRU;

//+NPOWERCLASS=?
typedef struct
{
  unsigned short                        usEvent;
  unsigned char                         unNum;
  unsigned short                        ausBand[G_LRRC_SUPPORT_BAND_MAX_NUM];
} ATC_MSG_NPOWERCLASS_T_CNF_STRU;

//+NCIDSTATUS[=<cid>]
typedef struct
{
    unsigned short                        usEvent;
    API_EPS_CID_STATUS_STRU               stCidStatus;
} ATC_MSG_NCIDSTATUS_CNF_STRU,
  ATC_MSG_NCIDSTATUS_R_CNF_STRU;

typedef struct
{
    unsigned char                 ucRegMode;
    unsigned char                 ucRegState;
    unsigned short                usTac;
    unsigned int                  ulCellId;
    unsigned int                  ulActTime;
    unsigned int                  ulTauTime;
} ST_ATC_REG_CONTEXT_STRU;

//+NGACTR?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucNgactrN;
    ST_ATC_REG_CONTEXT_STRU               stRegContext;
    unsigned short                        usOperType;   /* 0:china telecom; 1:china mobile; 2:china unicom; 3:china broadnet; 4:TestCard; 0xFFFF:unknown */
} ATC_MSG_NGACTR_R_CNF_STRU;

typedef struct
{
    unsigned char     ucSuppBand;
    unsigned short    usOffset;
    unsigned int      ulStartFreq;
} ATC_NPOB_PRE_BAND_STRU;

typedef struct
{
    unsigned int                          aulPlmn[NVM_MAX_OPERATOR_PLMN_NUM];
    ATC_NPOB_PRE_BAND_STRU                aPreBand[NVM_MAX_PRE_BAND_NUM];
} ATC_NPOB_INFO_STRU;

//+NPOPB?
typedef struct
{
    unsigned short                        usEvent;
    ATC_NPOB_INFO_STRU                    stNpobList[NVM_MAX_OPERATOR_NUM];
} ATC_MSG_NPOPB_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucN;
} ATC_MSG_NIPINFO_R_CNF_STRU,
  ATC_MSG_CNEC_R_CNF_STRU;

typedef struct
{   
    unsigned char                         ucParam;
    unsigned char                         ucState;
} API_PDP_CONTEXT_DNS_SET_INFO;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucNum;
#define D_ATC_NCPCDPR_R_MAX_NUM           2
    API_PDP_CONTEXT_DNS_SET_INFO          stPdpCtxDnsSet[D_ATC_NCPCDPR_R_MAX_NUM];
} ATC_MSG_NCPCDPR_R_CNF_STRU;

//+CSMS=<service>;+CSMS?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucMsgService;
} ATC_MSG_CSMS_CNF_STRU,
  ATC_MSG_CSMS_R_CNF_STRU;

//+CMGF?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucValue;
} ATC_MSG_CMGF_CNF_STRU,ATC_MSG_CMGF_R_CNF_STRU,ATC_MSG_CPMS_T_CNF_STRU;

//+CPMS
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucMem1;
    unsigned char                         ucMem2;
    unsigned char                         ucMem3;
    unsigned char                         ucSmMemUsed;
    unsigned char                         ucSmMemTotal;
    unsigned char                         ucMeMemUsed;
    unsigned char                         ucMeMemTotal;
} ATC_MSG_CPMS_CNF_STRU,ATC_MSG_CPMS_R_CNF_STRU;

//+CSCA?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucSelectCharSet;
    unsigned char                         ucScaLen;
    unsigned char                         aucSca[D_SMS_SCA_SIZE_MAX];
    unsigned char                         ucToSca;
} ATC_MSG_CSCA_R_CNF_STRU;

//+CSMP?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucFo;
    unsigned char                         ucVpLen;
    unsigned char                         ucVp[D_ATC_P_CSMP_IND_VP_SIZE_MAX];
    unsigned char                         ucPid;              
    unsigned char                         ucDcs;
} ATC_MSG_CSMP_R_CNF_STRU;

//+CNMI?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucMode;                                       
    unsigned char                         ucMt;
    unsigned char                         ucBm;              
    unsigned char                         ucDs;
    unsigned char                         ucBfr;
} ATC_MSG_CNMI_R_CNF_STRU;

//+CMMS?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucCmmsN;
} ATC_MSG_CMMS_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned int                          aulPlmn[6][15];
} ATC_MSG_QEHPLMN_R_CNF_STRU;

//+CSCB?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucMode;
} ATC_MSG_CSCB_R_CNF_STRU;

//+CSDH?
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucShow;
} ATC_MSG_CSDH_R_CNF_STRU;

//+CMTI:<mem>,<index>
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucaMem[3];
    unsigned char                         ucNotURC;
    unsigned short                        usIndex;
} ATC_MSG_CMTI_IND_STRU,ATC_MSG_CDSI_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucIndex;
} ATC_MSG_CMGW_IND_STRU;

//+CMT:"HEX",<ucPduLen>\r\n<aucData>
typedef struct
{
    unsigned char                         ucPduLen;
    unsigned char                         ucDataLen;
    unsigned char                         aucData[190];
} ATC_PDU_CMT_IND_STRU,ATC_PDU_CMGR_CNF_STRU,ATC_PDU_CMGL_CNF_STRU,ATC_PDU_CDS_IND_STRU,ATC_PDU_QCMGR_CNF_STRU;

typedef struct
{
    unsigned char               ucShowHeadFlg;
    unsigned char               ucSelectCharSet;
    unsigned char               ucToSca;
    unsigned char               aucSca[D_ATC_REPORT_ADDR_MAX_SIZE];
    unsigned char               ucFo;
    unsigned char               ucPid;
    unsigned char               ucDcs;
    unsigned char               ucTooa;
    unsigned char               aucOa[D_ATC_REPORT_ADDR_MAX_SIZE];
    unsigned char               aucScts[20+1];
    unsigned char               aucVp[20+2+1];
    unsigned char               ucDataLen;
    unsigned char               aucData[160+1];
} ATC_TEXT_CMT_IND_STRU;

typedef struct
{
    unsigned char               ucSelectCharSet;
    unsigned char               ucFo;
    unsigned char               ucMr;
    unsigned char               ucRaLen;
    unsigned char               ucTora;
    unsigned char               aucRaData[D_SMS_DA_SIZE_MAX];
    unsigned char               aucRa[D_ATC_REPORT_ADDR_MAX_SIZE];
    unsigned char               aucScts[D_ATC_P_CSMP_IND_VP_SIZE_MAX+1]; 
    unsigned char               aucDt[D_ATC_P_CSMP_IND_VP_SIZE_MAX+1]; 
    unsigned char               ucSt;
} ATC_TEXT_CDS_IND_STRU;

typedef struct
{
    unsigned char               ucShowHeadFlg;
    unsigned char               ucSelectCharSet;
    unsigned char               ucFo;
    unsigned char               ucPid;
    unsigned char               ucDcs;
    unsigned char               ucCt;
    unsigned char               ucMr;
    unsigned char               ucSt;
    unsigned char               ucTooa;
    unsigned char               aucOa[D_ATC_REPORT_ADDR_MAX_SIZE];
    unsigned char               aucScts[20+1];    
    unsigned char               aucDt[20+1];
    unsigned char               ucDataLen;
    unsigned char               aucData[160+1];
} ATC_TEXT_CMGL_CNF_STRU;

typedef struct
{
    unsigned char               ucShowHeadFlg;
    unsigned char               ucSelectCharSet;
    unsigned char               ucToSca;
    unsigned char               aucSca[D_ATC_REPORT_ADDR_MAX_SIZE];
    unsigned char               ucFo;
    unsigned char               ucPid;
    unsigned char               ucDcs;
    unsigned char               ucCt;
    unsigned char               ucMn;
    unsigned char               ucMr;
    unsigned char               ucSt;
    unsigned char               ucTooa;
    unsigned char               aucOa[D_ATC_REPORT_ADDR_MAX_SIZE];
    unsigned char               aucScts[20+1];
    unsigned char               aucDt[20+1];
    unsigned char               aucVp[D_ATC_P_CSMP_IND_VP_SIZE_MAX+1];
    unsigned char               ucDataLen;
    unsigned char               aucData[160+1];
} ATC_TEXT_CMGR_CNF_STRU;

typedef struct
{
    unsigned char               ucShowHeadFlg;
    unsigned char               ucSelectCharSet;
    unsigned char               ucToSca;
    unsigned char               aucSca[D_ATC_REPORT_ADDR_MAX_SIZE];
    unsigned char               ucFo;
    unsigned char               ucPid;
    unsigned char               ucDcs;
    unsigned char               ucCt;
    unsigned char               ucMn;
    unsigned char               ucMr;
    unsigned char               ucSt;
    unsigned short              usConcatenatedReferenceNum;                            /* Concatenated short message reference number */
    unsigned char               ucMaxConcatenatedNum;                                  /* Maxumum number of short messages in the concatenated shor message */
    unsigned char               ucConcatenatedSequenceNum;
    unsigned char               ucTooa;
    unsigned char               aucOa[D_ATC_REPORT_ADDR_MAX_SIZE];
    unsigned char               aucScts[20+1];
    unsigned char               aucDt[20+1];
    unsigned char               aucVp[D_ATC_P_CSMP_IND_VP_SIZE_MAX+1];
    unsigned char               ucDataLen;
    unsigned char               aucData[160+1];
} ATC_TEXT_QCMGR_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucShowMode;
    unsigned char                         ucNotURC;
    union  
    {
        ATC_PDU_CMT_IND_STRU              stPduCmd;
        ATC_TEXT_CMT_IND_STRU             stTextCmd;
    } u;
} ATC_MSG_CMT_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucShowMode;
    unsigned char                         ucNotURC;
    union  
    {
        ATC_PDU_CDS_IND_STRU              stPduCmd;
        ATC_TEXT_CDS_IND_STRU             stTextCmd;
    } u;
} ATC_MSG_CDS_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucState;
    unsigned char                         ucShowMode;
    union  
    {
        ATC_PDU_CMGR_CNF_STRU              stPduCmd;
        ATC_TEXT_CMGR_CNF_STRU             stTextCmd;
    } u;
} ATC_MSG_CMGR_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucState;
    unsigned char                         ucShowMode;
    union  
    {
        ATC_PDU_QCMGR_CNF_STRU             stPduCmd;
        ATC_TEXT_QCMGR_CNF_STRU            stTextCmd;
    } u;
}ATC_MSG_QCMGR_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucIndex;
    unsigned char                         ucState;
    unsigned char                         ucShowMode;
    union  
    {
        ATC_PDU_CMGL_CNF_STRU              stPduCmd;
        ATC_TEXT_CMGL_CNF_STRU             stTextCmd;
    } u;
} ATC_MSG_CMGL_CNF_STRU;

//+CMGC=<length><CR>PDU<ctrl-Z>;
//+CMGS=<length><CR>PDU<ctrl-Z>
typedef struct
{
    unsigned short                        usEvent;
    unsigned short                        usCmdType;
    unsigned char                         usMsgService;/*related to csms set value*/
    unsigned char                         ucTpmr;
    unsigned char                         ucTpduLen;
    unsigned char                         aucTpdu[164];
} ATC_MSG_CMGC_CNF_STRU,ATC_MSG_CMSS_CNF_STRU,ATC_MSG_CMGS_CNF_STRU,ATC_MSG_QCMGS_CNF_STRU;


//+SIMST:<ucSimStatus>
typedef struct
{
    unsigned short                        usEvent;
#define    D_ATC_SIMST_NOT_PRESENT          0
#define    D_ATC_SIMST_SUCC_INIT            1
#define    D_ATC_SIMST_SUCC_NO_INIT         2
    unsigned char                         ucSimStatus; 
    API_EPS_IMSI_READ_STRU                stImsi;
    unsigned char                         ucTestModeFlg;  /* 0: normal mode; 1: test mode */
} ATC_MSG_SIMST_IND_STRU;

//+CRTDCP:<ucAtCid>,<usLen>,<pucReportData>
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucNonIpDataFlg;
    unsigned char                         ucNrnpdmRepValue; 
    unsigned char                         ucCrtdcpRepValue;
    unsigned char                         ucAtCid;
    unsigned short                        usLen;         //appending data after usLen
    unsigned char                        *pucReportData;
} ATC_MSG_CRTDCP_IND_STRU;

//+CGAPNRC:<cid>,...
typedef struct
{
    unsigned short                        usEvent;
    EPS_APN_RATE_CONTRL_STRU              stEpsApnRateCtl;
} ATC_MSG_CGAPNRC_IND_STRU;

//+CGEV:...
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucCgerepMode;
    unsigned char                         ucCgevEventId;
    ST_ATC_CGEV_PARAMETER                 stCgevPara;
} ATC_MSG_CGEV_IND_STRU;

//+CGREG:<stat>,...
typedef struct
{
    unsigned short                        usEvent;
    API_EPS_REG_STATUS_STRU               stPara;
} ATC_MSG_CEREG_IND_STRU,
  ATC_MSG_CREG_IND_STRU,
  ATC_MSG_CGREG_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucCsconN;
    API_SIGNAL_CONN_STATUS_STRU           stPara;
} ATC_MSG_CSCON_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    API_NWCEDRXS_PARA_STRU                stPara;
} ATC_MSG_NPTWEDRXP_IND_STRU,
  ATC_MSG_CEDRXP_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucCciotoptN;
    unsigned char                         ucSupptNwOpt;
} ATC_MSG_CCIOTOPTI_IND_STRU;


typedef struct
{
    unsigned short                        usEvent;
    unsigned int                          ulRlcUl;
    unsigned int                          ulRlcDl;
    unsigned int                          ulMacUl;
    unsigned int                          ulMacDl;
} ATC_MSG_L2_THP_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned int                          ulRlcUl;
    unsigned char                         aucICCIDstring[EMM_SIM_UICC_ID_LEN*2 + 1];
} ATC_MSG_UICCID_CNF_STRU,
  ATC_MSG_QCCID_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    API_EPS_PDN_IPDNS_IND_STRU            stPara;
} ATC_MSG_XYIPDNS_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned int                          ulAddr;
    unsigned short                        usNvRamLen;
} ATC_MSG_MALLOC_ADDR_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned short                        usIpSn;
#define D_ATC_IPSN_STATUS_FAIL   0
#define D_ATC_IPSN_STATUS_SUCC   1
    unsigned char                         ucStatus;
    unsigned char                         ucFlowCtrlStatusFlg;
    unsigned char                         ucFlowCtrlStatus;
} ATC_MSG_IPSN_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucAtCid;
    unsigned char                         ucIPv4Flg;
    unsigned char                         ucIPv6Flg;
    unsigned char                         aucIPv4Addr[4];
    unsigned char                         aucIPv6Addr[16];
} ATC_MSG_PdnIPAddr_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucNgactrN;
    unsigned char                         ucAtCid;
#define D_ATC_NGACTR_STATE_DEACT                 0
#define D_ATC_NGACTR_STATE_ACT                   1
    unsigned char                         state;
#define D_ATC_NGACTR_RST_SUCC                     0
#define D_ATC_NGACTR_RST_CID_UNDEF                1
#define D_ATC_NGACTR_RST_LOCAL_REJ                5
#define D_ATC_NGACTR_RST_APN_ERR                  6
#define D_ATC_NGACTR_RST_IPV4_ONLY                8
#define D_ATC_NGACTR_RST_IPV6_ONLY                9
#define D_ATC_NGACTR_RST_IP_ONLY                  10
#define D_ATC_NGACTR_RST_NONIP_ONLY               11
#define D_ATC_NGACTR_RST_SINGLE_IP_ONLY           12
#define D_ATC_NGACTR_RST_SERVICE_ERR              13
#define D_ATC_NGACTR_RST_CTX_MAX_NUM              14
#define D_ATC_NGACTR_RST_LAST_PDN_DIS_NOT_ALLOW   16
#define D_ATC_NGACTR_RST_UNHNOW_ERR               17
    unsigned char                         result;
} ATC_MSG_NGACTR_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
#define D_ATC_TYPE_LOCK_FREQ            0
#define D_ATC_TYPE_LOCK_CELL            1
#define D_ATC_TYPE_CELL                 2
#define D_ATC_TYPE_MIB                  3
#define D_ATC_TYPE_EARFCN               4
#define D_ATC_TYPE_REDIRECT             5
#define D_ATC_TYPE_SCAN_CAND_FREQ       6
#define D_ATC_TYPE_SCAN_STORE_FREQ      7
#define D_ATC_TYPE_SCAN_PRE_FREQ        8
#define D_ATC_TYPE_SCAN_LOCK_FREQLIST   9
#define D_ATC_TYPE_SCAN_PRE_BAND        10
#define D_ATC_TYPE_SCAN_ALL_BAND        11
    unsigned char                         ucFstSrchType;
    unsigned char                         ucCampOnType;
} ATC_MSG_CELLSRCH_TEST_STRU;

typedef struct
{
    unsigned short                        usEvent;
    API_EPS_OPELIST_SRCH_CNF_STRU         stPara;
} ATC_MSG_OPELIST_SRCH_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned short                        usCmdType;
    unsigned short                        usRspLen;
    unsigned char                         aucRsp[257];
} ATC_MSG_CSIM_CNF_STRU,
  ATC_MSG_CGLA_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucChannelId;
} ATC_MSG_CCHO_CNF_STRU,
  ATC_MSG_CCHC_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucCommand;
    unsigned char                         sw1;
    unsigned char                         sw2;
    unsigned short                        usRspLen;
    unsigned char                         aucResponse[4];
} ATC_MSG_CRSM_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucCtzrReport;
    LTE_NAS_LOCAL_TIME_STRU               stPara;
} ATC_MSG_LOCALTIMEINFO_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
} ATC_MSG_SMS_PDU_IND_STRU,
  ATC_MSG_PS_AT_IDLE_IND_STRU,
  ATC_MSG_NO_CARRIER_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
#define D_MSG_PSINFO_TYPE_SIMVCC          1
#define D_MSG_PSINFO_TYPE_NVWRITE         2
#define D_MSG_PSINFO_TYPE_SOFT_RESET      3
#define D_MSG_PSINFO_TYPE_TDD             4
#define D_MSG_PSINFO_TYPE_FDD             5
    unsigned char                         ucType;
    unsigned char                         ucValue;
} ATC_MSG_PSINFO_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucAtCid;
    unsigned char                         ucSequence;
    unsigned char                         ucStatus;   /* 0-err, 1-succ */
} ATC_MSG_CSODCPR_IND_STRU,
  ATC_MSG_NSNPDR_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucAtCid;
    unsigned char                         ucResult;
    unsigned char                         ucPdpType;
    unsigned char                         ucIPv4Flg;
    unsigned char                         ucIPv6Flg;
    unsigned char                         aucIPv4Addr[4];
    unsigned char                         aucIPv6Addr[16];
#define D_ATC_NIPINFO_CAUSE_IPV4_ONLY            1
#define D_ATC_NIPINFO_CAUSE_IPV6_ONLY            2
#define D_ATC_NIPINFO_CAUSE_SINGLE_ADDR_ONLY     3
#define D_ATC_NIPINFO_CAUSE_IPV6_RA_TIME_OUT     4
#define D_ATC_NIPINFO_CAUSE_UNKNOWN              5
    unsigned char                         ucFailCause;
} ATC_MSG_NIPINFO_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
#define D_ATC_CNEC_TYPE_EMM               0
#define D_ATC_CNEC_TYPE_ESM               1
    unsigned char                         ucType;
    unsigned char                         ucErrCode;
    unsigned char                         ucAtCidFlg;
    unsigned char                         ucAtCid;
} ATC_MSG_CNEC_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucPsmEnable;
#define D_ATC_MNBIOTEVENT_ENTER_PSM       0
#define D_ATC_MNBIOTEVENT_EXIT_PSM        1
    unsigned char                         ucPsmState;
} ATC_MSG_MNBIOTEVENT_IND_STRU;

typedef struct
{
    #define  Data_Sequence_MAX_NUM  255
    unsigned char       ucSequenceNum;
    unsigned char       Sequence[Data_Sequence_MAX_NUM];
}API_DATA_LIST_STRU;

typedef struct
{
    unsigned short                        usEvent;
    API_DATA_LIST_STRU                    stSequence;
} ATC_MSG_NQPODCP_CNF_STRU,
  ATC_MSG_NQPNPD_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucReporting;
} ATC_MSG_NRNPDM_R_CNF_STRU;

//+CEID
typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucLen;
#define D_ATC_SIM_EID_MAX_LEN   16    
    unsigned char                         aucEid[D_ATC_SIM_EID_MAX_LEN];
} ATC_MSG_CEID_CNF_STRU;

typedef struct {
    unsigned short                usEvent;
#define D_ATC_NPLMNS_NO_ACTIVE         0
#define D_ATC_NPLMNS_PLMN_SEARCHING    1
#define D_ATC_NPLMNS_PLMN_SELECTED     2
#define D_ATC_NPLMNS_OOS               3
    unsigned char                 ucState;
    unsigned int                  ulOosTimerLeftLen;
} ATC_MSG_NPLMNS_R_CNF_STRU;

typedef struct 
{
    unsigned short                usEvent;
    unsigned char                 ucEarLockFlg;
    unsigned char                 ucPciLockFlg;
    unsigned short                usLockPci;
    unsigned int                  ulLockEar;

    unsigned int                  aulEarfcnList[NVM_MAX_CANDIDATE_FREQ_NUM];
    unsigned char                 ucEarfcnNum;
} ATC_MSG_NLOCKF_R_CNF_STRU;

typedef struct
{
    unsigned char                 ucAllowFg;
    unsigned int                  ulCsgId;
    unsigned int                  ulPlmnId;
} EPS_CSGLIST_INFO_STRU;

typedef struct
{
    unsigned short                usEvent;
    unsigned char                 ucMode;       //0:D_ATC_QCSG_MODE_CLOSE, 1:D_ATC_QCSG_MODE_AUTO;2:D_ATC_QCSG_MODE_MANUAL
    unsigned char                 ucPlmnSelFlg;
    unsigned int                  ulPlmnNum;
    unsigned char                 ucCsgIdFlg;
    unsigned int                  uiCsgId;
} ATC_MSG_NCSG_R_CNF_STRU;

typedef struct
{
    unsigned short                usEvent;
    unsigned char                 ucResult;
    unsigned char                 ucAct;
    unsigned char                 unNum;

#define D_CGSLIST_MAX_NUM         20
    EPS_CSGLIST_INFO_STRU         astCsgList[D_CGSLIST_MAX_NUM];
} ATC_MSG_NCSG_T_CNF_STRU;

typedef struct
{
    unsigned char                 aucOsId[16];
    unsigned char                 aucOsappId[16 + 1];
    unsigned char                 ucAcdcCategory;
} EPS_ACDC_OS_CON_STRU;

typedef struct
{
    unsigned short                usEvent;
    unsigned char                 ucNum;
#define D_ACDCLIST_MAX_NUM        15
    EPS_ACDC_OS_CON_STRU          astAcdcOsConfig[D_ACDCLIST_MAX_NUM];
} ATC_MSG_CACDC_T_CNF_STRU;

typedef struct 
{
    unsigned short              usEvent;
    unsigned char               ucPdpType;
    unsigned char               aucApnValue[D_APN_LEN + 1];
    unsigned char               aucUserName[D_PCO_AUTH_MAX_LEN + 1];
    unsigned char               aucPassword[D_PCO_AUTH_MAX_LEN + 1];
    unsigned char               ucAuthProt;
} ATC_MSG_QICSGP_R_CNF_STRU;

typedef struct 
{
    unsigned short                        usEvent;
    unsigned char                         aucFullNetworkName[17];
    unsigned char                         aucShortNetworkName[17];
    unsigned char                         aucServProvName[17];
    unsigned char                         ucAlphabet;
    unsigned char                         ucRegPlmnFlg;
    unsigned int                          uiRegPlmn;
} ATC_MSG_QSPN_CNF_STRU;

typedef struct 
{
    unsigned short                        usEvent;
    unsigned char                         ucStatus;
} ATC_MSG_QINISTAT_CNF_STRU;

typedef struct 
{
#define D_EMM_STATE_NULL                0
#define D_EMM_STATE_DEREG               1
#define D_EMM_STATE_REG_INIT            2
#define D_EMM_STATE_REG                 3
#define D_EMM_STATE_DEREG_INIT          4
#define D_EMM_STATE_TAU_INIT            5
#define D_EMM_STATE_SR_INIT             6
#define D_EMM_STATE_UNKNOWN             7
    unsigned char                       ucEmmState;

#define D_EMM_MODE_IDLE                 0
#define D_EMM_MODE_PSM                  1
#define D_EMM_MODE_CONN                 2
#define D_EMM_MODE_UNKNOWN              3
    unsigned char                       ucEmmMode;

#define D_PLMN_STATE_NO_PLMN            0
#define D_PLMN_STATE_SERCHING           1
#define D_PLMN_STATE_SELECTED           2
#define D_PLMN_STATE_UNKNOWN            3
    unsigned char                       ucPlmnState;

#define D_PLMN_TYPE_HPLMN               0
#define D_PLMN_TYPE_EHPLMN              1
#define D_PLMN_TYPE_VPLMN               2
#define D_PLMN_TYPE_UPLMN               3
#define D_PLMN_TYPE_OPLMN               4
#define D_PLMN_TYPE_UNKNOWN             5
    unsigned char                       ucPlmnType;
    unsigned long                       ulSelectPlmn;
} ATC_QENG_PLMN_STATUS_STRU;

typedef struct
{
#define D_ATC_QENG_CELL_TYPE_SEARCH    0
#define D_ATC_QENG_CELL_TYPE_LIMSRV    1
#define D_ATC_QENG_CELL_TYPE_NOCONN    2
#define D_ATC_QENG_CELL_TYPE_CONNECT   3
    unsigned char                         state;
    unsigned char                         is_tdd;
    unsigned int                          plmn;
    unsigned int                          cell_ID;
    unsigned short                        pci_value;
    unsigned int                          earfcn_value;
    unsigned int                          freq_band_ind;
    unsigned int                          ul_bandwidth;
    unsigned int                          dl_bandwidth;
    unsigned short                        tac;
    short                                 rsrp;
    short                                 rsrq;
    short                                 rssi;
    short                                 sinr;
    short                                 srxlev;
} ATC_QENG_RSLT_SERVCELL_STRU;

typedef struct
{
    unsigned char                         intraOrInter;  /* 0:intra; 1:inter */ 
    unsigned int                          earfcn;
    unsigned short                        pci;
    short                                 rsrp;
    short                                 rsrq;
    short                                 rssi;
    short                                 sinr;
    unsigned int                          srxlev;  
    union
    {
        struct intra
        {
            unsigned int                  cell_resel_priority;
            unsigned int                  s_non_intra_search;
            unsigned int                  thresh_serving_low;
            unsigned int                  s_intra_search;
        } intra;
        
        struct inter
        {
            unsigned int                  threshX_low;
            unsigned int                  threshX_high;
            unsigned int                  cell_resel_priority;
        } inter;
    } u;
} ATC_QENG_RSLT_NEIGHBOURCELL_STRU;

typedef struct 
{
    unsigned short                        usEvent;
    unsigned char                         type;         /* 0:stServCell,1:stNeighbourCell */
    unsigned char                         ucNeighCellNum;
    union
    {
        ATC_QENG_RSLT_SERVCELL_STRU       stServCell;
        ATC_QENG_RSLT_NEIGHBOURCELL_STRU  aNeighbourCellList[LRRC_NAS_MAX_CELL_NUM];
    };
} ATC_MSG_QENG_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucEnable;
    unsigned char                         ucInsertLevl;
} ATC_MSG_QSIMDET_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucEnable;
    unsigned char                         ucState;  /*0:remove,1:insert,2:unknown */
} ATC_MSG_QSIMSTAT_R_CNF_STRU,
  ATC_MSG_QSIMSTAT_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucAct;      /* D_ACT_UNKNOWN, D_ACT_FDD_LTE, D_ACT_TDD_LTE */
    unsigned int                          ulOper;
    unsigned char                         ucBandType; /* 0: LTE */
    unsigned short                        usBand;
    unsigned short                        usChannel;
} ATC_MSG_QNWINFO_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucEnable;
} ATC_MSG_QCSQ_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucSysMode; /* 0: no service 1: lte*/
    short                                 sRssi;
    short                                 sRsrp;
    short                                 sSinr;
    short                                 sRsrq;
} ATC_MSG_QCSQ_E_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    uint64_t                              ullDataBytesSend;
    uint64_t                              ullDataBytesRecv;
} ATC_MSG_QGDCNT_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned short                        usValue;
} ATC_MSG_QAUGDCNT_R_CNF_STRU;

typedef struct
{
    unsigned int                          ulPlmn;
    unsigned char                         ucGSMAcT;
    unsigned char                         ucGSMCompactAcT;
    unsigned char                         ucUTRANAcT;
    unsigned char                         ucEUTRANAcT;
} ATC_CPOL_OPER_INFO_STRU;
  

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucFormat;
    unsigned char                         ucNum;
    ATC_CPOL_OPER_INFO_STRU               atOperList[20];
} ATC_MSG_CPOL_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucFormat;
    unsigned char                         ucMaxNum;
} ATC_MSG_CPOL_T_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucChset;
} ATC_MSG_CSCS_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucValue;
} ATC_MSG_CGSMS_R_CNF_STRU;

typedef struct
{
    unsigned char                         ucCellType;  //0:servcell,1:neighbourcell intra,2:neighbourcell inter
    unsigned char                         ucAct;       //0:LTE
    unsigned short                        usTac;
    unsigned short                        usPhyCellId;
    signed short                          sRsrp;
    signed short                          sRsrq;
    unsigned int                          ulPlmn;
    unsigned int                          ulCell_ID;
    unsigned int                          ulDlEarfcn;
    unsigned char                         ucRxLev;
    unsigned char                         ucBandInd;
    signed short                          sRssi;
    signed short                          sSinr;
} ATC_QCELL_CELLINFO_STRU;

typedef struct
{
    unsigned char                         ucNum;
    ATC_QCELL_CELLINFO_STRU               aCellInfo[LRRC_NAS_MAX_CELL_NUM];
} API_QCELL_CELLINFO_STRU;

//+QCELL?
typedef struct
{
    unsigned short                        usEvent;
    API_QCELL_CELLINFO_STRU               tCellInfo;
} ATC_MSG_QCELL_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    API_LOCKFREQ_LIST_STRU                LockFreqList;
} ATC_MSG_MLOCKFREQ_R_CNF_STRU;

typedef struct
{
    unsigned int                          ulStart;
    unsigned int                          ulEnd;
} ATC_FREQ_INFO_STRU;

typedef struct
{
    unsigned char                         nuNum;
    ATC_FREQ_INFO_STRU                    aFreq[64];
} API_SUPPORT_FREQ_LIST_STRU;

typedef struct
{
    unsigned short                        usEvent;
    API_SUPPORT_FREQ_LIST_STRU            FreqList;
} ATC_MSG_MLOCKFREQ_T_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucValue;
} ATC_MSG_MEMMTIMER_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucBackoffTimerId; /* 0:3346,1:3347,2:3448,3:3396 */
#define D_BACKOFFTIMER_EVNET_START         0
#define D_BACKOFFTIMER_EVNET_STOP          1
#define D_BACKOFFTIMER_EVNET_EXPIRE        2
#define D_BACKOFFTIMER_EVNET_DISABLE       3
    unsigned char                         ucEvent;
    uint32_t                              ulTimerLen_s;
    uint32_t                              ulTimerLeftLen_s;
} ATC_MSG_MEMMTIMER_IND_STRU;

typedef struct {
    unsigned short             usEvent;
    unsigned char              ucFunc;
    unsigned char              ucValue1;
    unsigned char              ucValue2Len;
    unsigned char              aucValue2[39];
} ATC_MSG_MUECONFIG_R_CNF_STRU;

typedef struct {
    unsigned short             usEvent;
    unsigned char              ucOpt;
    unsigned char              ucValue;
} ATC_MSG_MWIFISCANCFG_R_CNF_STRU;

typedef struct
{
    unsigned char              aucNum[21]; //char string
    unsigned char              ucType;
} API_MSISDN_INFO_STRU;

typedef struct {
    unsigned short             usEvent;
    unsigned char              ucCnt;
    API_MSISDN_INFO_STRU       atSisdn[10];
} ATC_MSG_CNUM_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucCid;
} ATC_MSG_QIURC_PDPDEACT_IND_STRU;

typedef struct {
    unsigned short             usEvent;
    unsigned char              ucNum;
    ST_EARFCN_PCI_INFO         atEarfcnInfo[10];
} ATC_MSG_QBLACKCELL_R_CNF_STRU;

typedef struct {
    unsigned short             usEvent;
    unsigned char              ucValue;
} ATC_MSG_QBLACKCELLCFG_R_CNF_STRU,
  ATC_MSG_SIMSWICH_R_CNF_STRU;

typedef struct {
  unsigned short             usEvent;
  unsigned char              ucSimFlg;
  unsigned char              ucSimState;
  unsigned char              ucSimNum;
} ATC_MSG_QDSIM_R_CNF_STRU;

typedef struct
{
    unsigned short             usEvent;
    unsigned char              ucInfoSize;
#define PSTEST_GET_PS_STATE_MAX_SIZE    120
    unsigned char              aucInfo[PSTEST_GET_PS_STATE_MAX_SIZE];
} ATC_MSG_PSTEST_INFO_IND_STRU;

typedef struct
{
    unsigned short             usEvent;
    unsigned char              aucPCTTestSequence[15];
} ATC_MSG_PCTTESTINFO_R_CNF_STRU;

typedef struct
{
    unsigned short             usEvent;
    unsigned char              ucSimSlot;
} ATC_MSG_ECSIMCFG_R_CNF_STRU;

#ifdef LCS_MOLR_ENABLE
typedef struct
{
    unsigned short                        usEvent;

    unsigned char                         method_flag:1;
    unsigned char                         hor_acc_flag:1;
    unsigned char                         ver_acc_flag:1;
    unsigned char                         rep_mode:1;
    unsigned char                         vel_req:3;
    unsigned char                         ucPadding:1;

    unsigned char                         enable:2;
    unsigned char                         method:3;
    unsigned char                         hor_acc_set:1;
    unsigned char                         ver_req:1;
    unsigned char                         ver_acc_set:1;

    unsigned char                         shape_rep:7;
    unsigned char                         plane:1;

    unsigned char                         hor_acc;
    unsigned char                         ver_acc;

    unsigned short                        timeout;
    unsigned short                        interval;
} ATC_MSG_CMOLR_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucErrCode;
} ATC_MSG_CMOLRE_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    LCS_SHAPE_DATA_STRU                   stShapeData;
    LCS_VELOCITY_DATA_STRU                stVelData;
} ATC_MSG_CMOLRG_IND_STRU;
#endif

typedef struct
{
    unsigned short                        usEvent;
#define D_CIEV_MESSAGE      1
#define D_CIEV_SMSFULL      2
    unsigned char                         ucType;
    unsigned char                         ucValue;
} ATC_MSG_CIEV_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucOperatorIndex;
    unsigned char                         ucNum;
    unsigned int                          aulearfcn[NVM_MAX_PRE_EARFCN_NUM];
} ATC_MSG_NPREEARFCN_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucmode;
    unsigned char                         ucIdletime;
    unsigned short                        usRetrytime;
} ATC_MSG_QSCLKEX_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned short                        usNWDrxValue;
} ATC_MSG_NWDRX_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         aucInsValue[20];
    union
    {
        unsigned char                     ucValue;
        unsigned char                     aucValue[40];
    } u;
} ATC_MSG_SIMUUICC_R_CNF_STRU;

typedef struct
{
    short                                 sRssi;
    unsigned char                         ucChannel;
    unsigned char                         aucMac[20];
} ATC_WIFI_INFO_STRU;

typedef struct
{
    unsigned short                        usEvent;
#define D_ATC_QWIFISCAN_CMD         0
#define D_ATC_MWIFISCAN_CMD         1
    unsigned char                         ucWifiType;// 0:qwifiscan,1:mwifiscanstart 
    unsigned char                         ucNum;
#define ATC_MAX_WIFI_INFO_NUM           10
    ATC_WIFI_INFO_STRU                    aWifiInfo[ATC_MAX_WIFI_INFO_NUM];
} ATC_MSG_QWIFISCAN_IND_STRU,
ATC_MSG_MWIFISCANQUERT_CNF_STRU;

typedef struct {
    unsigned short      usEvent;
    unsigned char       ucRound;
    unsigned char       ucMasIDNum;
    unsigned char       ucScantimeout;
    unsigned char       ucPriority;
    unsigned int        uiTimeout;
} ATC_MSG_QWIFISCAN_R_CNF_STRU;

typedef struct
{
    unsigned short             usEvent;
    unsigned char              ucValue;
    unsigned char              ucRssi;
    unsigned char              ucRoamingFlg;
    unsigned char              ucBandInfo;
    unsigned char              ucDlBandWith;
    unsigned char              ucBSJFlg;
    API_QCELL_CELLINFO_STRU    tCellInfo;
    API_EPS_IMSI_READ_STRU     tImsi;
} ATC_MSG_CCED_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucCEMode;
} ATC_MSG_CEMODE_CNF_STRU;

typedef struct
{
    unsigned char   ucPdpType;
    unsigned char   ucCid;
    unsigned char   aucPdpAddrValue[12];          /* octet:0-7: IPV6, octet: 8-11: IPV4 */
} QIACTEX_CID_INFO_STRU;

typedef struct
{
    unsigned short                        usEvent;
    QIACTEX_CID_INFO_STRU                 aucPdpDynamicInfo[D_MAX_CNT_CID];
    unsigned char                         ucValidNum;
}  ATC_MSG_QIACTEX_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned short                        usResult;
    unsigned char                         ucInterfaceReqFlg;
    unsigned char                         ucViewMode;
    QIACTEX_CID_INFO_STRU                 tPdpDynamicInfo;
}  ATC_MSG_QIACTEX_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned short                        usResult;
    unsigned char                         ucCid;
    unsigned char                         ucInterfaceReqFlg;
}  ATC_MSG_QIDEACTEX_IND_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucBand;
}  ATC_MSG_BANDIND_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucValue;
}  ATC_MSG_QINDCFG_R_CNF_STRU;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucCid;
    unsigned char                         ucPdpType;
}  ATC_MSG_MIPCALL_CNF_STRU;

typedef struct
{
    unsigned char       mu8Num;
    unsigned char       mau8Band[D_ATC_NBAND_MAX_BNAD_NUM];
    unsigned char       aucGsmBand[D_ATC_QCFG_MAX_GSMBAND_NUM];
    unsigned char       aucNbiotBand[D_ATC_QCFG_MAX_NBIOTBAND_NUM];
} ATC_MSG_QCFG_BAND_INFO;

typedef struct
{
    unsigned short                        usEvent;
    unsigned char                         ucFunc;
    unsigned char                         ucValue;
    unsigned char                         ucValue1;
    ATC_MSG_QCFG_BAND_INFO                stBandInfo;
} ATC_MSG_QCFG_R_CNF_STRU;

typedef struct {
    unsigned short             usEvent;
    unsigned char              ucValue;
} ATC_MSG_CUSD_R_CNF_STRU;

#define D_DATA_REQ_TYPE_AT_CMD              1
#define D_DATA_REQ_TYPE_USER_INTERFACE_REQ  2
typedef struct
{
#if IS_DSP_CORE
    
#endif
    unsigned char        ucAplNum;
    unsigned char        ucReqType;
    unsigned short       usLen;
    unsigned char        *pucData;
} T_ATC_DATA_REQ;

typedef struct
{
    unsigned int            year;
    unsigned char           month;//1..12
    unsigned char           day;//1..31
    unsigned char           hour;//0..23
    unsigned char           minute;//0..59
    unsigned char           second;//0..59
    unsigned char           millisecond;//millisecond * 10,1..100
    unsigned char           ucState;/* 1: read succ, 0: fail*/
    uint64_t                ullGpsTime;
}ATC_SIB16_TIMEINFO_STRU;

typedef struct
{
    unsigned short          usEvent;
    ATC_SIB16_TIMEINFO_STRU tSib16Info;
}ATC_MSG_QUTCTIME_IND_STRU;


extern void AtcAp_TaskEntry(void* pArgs);
extern void atc_ap_task_init();

#endif



