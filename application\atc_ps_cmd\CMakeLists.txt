get_sub_header_dirs(INCLUDE_ATPSCMD_DIRS ${CMAKE_CURRENT_LIST_DIR})

file(GLOB_RECURSE ATPSCMD_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/atc_ps_cmd/*.c"
)

#shorten_src_file_macro_without_warning(${ATPSCMD_SOURCES})
shorten_src_file_macro(${ATPSCMD_SOURCES})

target_sources(application
    PRIVATE 
        ${ATPSCMD_SOURCES}
)

# 2M版本放PSRAM做压缩
if(SPACE_OPTIMIZATION)
    relocate_code(CODE_LOCATION PSRAM CODE_TARGET application SOURCES_LIST ${ATPSCMD_SOURCES})
endif()
