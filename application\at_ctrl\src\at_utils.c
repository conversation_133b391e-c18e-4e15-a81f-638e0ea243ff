/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "at_utils.h"
#include "at_ctl.h"
#include "factory_nv.h"
#include "xy_at_api.h"
#include "xy_system.h"

/*******************************************************************************
 *						  Global variable definitions						   *
 ******************************************************************************/
/*
 * 转义字符表，用于转义字符转换ASCII码
*/
esc_item_t esc_table[] =
{
	{'a', '\a'},
	{'b', '\b'},
	{'f', '\f'},
	{'n', '\n'},
	{'r', '\r'},
	{'t', '\t'},
	{'v', '\v'},
	{'?', '\?'},
	{'\\', '\\'},
	{'\'', '\''},
	{'\"', '\"'},
};

/*******************************************************************************
 *						Global function implementations						   *
 ******************************************************************************/
 

bool is_digit_str(char *str)
{
    unsigned digit_len = 0;

	if(*str == '-'){
		str++;
	}
    while(isdigit((int)(*str))){
        str++, digit_len++;
    }
    while(*str == ' '){
        str++;
    }
    return *str == '\0' && digit_len > 0;
}

bool is_hex_str(char *str, uint8_t check_head)
{
    uint16_t digit_len = 0;

	if(check_head)
	{
		if(strncmp(str,"0x",2) == 0 || strncmp(str,"0X",2) == 0)
			str += 2;
		else
			return false;
	}

    while(isxdigit((int)(*str)))
	{
        str++;
		digit_len++;
    }

    while(*str == ' ')
	{
        str++;
    }

    return (*str == '\0' && digit_len > 0);	
}


/* 检测是否为OK应答结果码;返回 1 表示匹配成功 */
bool Is_AT_Rsp_OK(char *str)
{
	int ret = 0;
	char *temp = str;

	if (strnstr(str, "OK\r",strlen("OK\r")+5))
	{
		ret = 1;
	}
	else
	{
		int n = strlen(str);
		
		//+URC:XXXX\r\n\r\nOK\r\n;减少无效匹配
		if(n > 6)
		{
			temp = str+n-6;
		}

		if(strstr(temp,"OK\r") != NULL)
		{
			ret = 1;
		}
	}
	return  ret;
}

extern unsigned char api_GetCmeeValue();
int Get_AT_errno(char *str)
{
	int err_no = XY_OK;
	char *err_head = NULL;
	int n = strlen(str);

	if(api_GetCmeeValue() == 2)
	{
		if(strstr(str,"ERROR") != NULL)
		{
			err_no = XY_Err_Unknown;
		}
	}
	else
	{
		/* +UNILWCFG:<LwErrCode>\r\n\r\nERROR\r\n*/
		if(n > 15)
		{
			err_head = strstr(str+n-15,"ERROR");
		}
		/* +CME ERROR: XX*/
		else
		{
			err_head = strstr(str, "ERROR");
		}
		
		if(err_head)
		{
			err_no = XY_Err_Unknown;

			if(api_GetCmeeValue() == 1)
			{
		        if ((err_head = strchr(err_head, ':')) != NULL)
		        {
		            err_no = (int)strtol(err_head + 1, NULL, 10);
		        }
			}
	    }
	}

    return err_no;
}

bool Is_Result_AT_str(char *str)
{
	int ret = 0;
    char *tmp = str;
	char *tail = str;
    while (*tmp == '\r' || *tmp == '\n')
        tmp++;

    size_t len = strlen(tmp);

	if(len > 15)
	{
		tail = str + len - 15;
	}

    /* 末尾存在OK\r\n */
    if (len >= 4 && *(tmp + len - 1) == '\n' && *(tmp + len - 2) == '\r' 
        && (*(tmp + len - 3) == 'K' || *(tmp + len - 3) == 'k') 
        && (*(tmp + len - 4) == 'O' || *(tmp + len - 4) == 'o'))
    {
        ret = 1;
    }
	/* 末尾存在OK\r */
    else if (len >= 3 && *(tmp + len - 1) == '\r'
        && (*(tmp + len - 2) == 'K' || *(tmp + len - 2) == 'k')
        && (*(tmp + len - 3) == 'O' || *(tmp + len - 3) == 'o'))
    {
        ret = 1;
    }
    /* 头部存在OK\r\n */
    else if (len >= 4 && *(tmp + 3) == '\n' && *(tmp + 2) == '\r' 
        && (*(tmp + 1) == 'K' || *(tmp + 1) == 'k') 
        && (*tmp == 'O' || *tmp == 'o'))
    {
        ret = 1;
    }
    /* 头部存在\r\nOK\r */
    else if (len >= 5 && *tmp == '\r' && *(tmp + 1) == '\n' 
        && (*(tmp + 2) == 'O' || *(tmp + 2) == 'o') 
        && (*(tmp + 3) == 'K' || *(tmp + 3) == 'k')
		&& *(tmp + 4) == '\r')
    {
        ret = 1;
    }
    /* ERROR or CME ERROR: */
    else if (strnstr(tmp, "ERROR",strlen("ERROR")+10) || strnstr(tail, "ERROR",strlen("ERROR")+10))
    {
        ret = 1;
    }

	return ret;
}

char *at_ok_build()
{
	char *at_str;

	at_str = xy_malloc(7);
	snprintf(at_str, 7, AT_RSP_OK);
	return at_str;
}

bool is_at_char(char c)
{
    return (((((uint8_t)c) >= ' ') && (((uint8_t)c) <= '~')) || c == '\r' || c == '\n');
}

bool is_at_str(char *str,int len)
{
	int i = 0;

	while(i < len)
	{
    	if(!(is_at_char(*(str+i))))
			return false;
	}
	return true;
}

bool isHexChar(char ch)
{
	if ((ch >= '0' && ch <= '9') || (ch >= 'a' && ch <= 'f') || (ch >= 'A' && ch <= 'F'))
		return true;
	else
		return false;
}

/*将HEX字符转换为数值*/
char covertHextoNum(char ch)
{
	char temp=0;
	if (isdigit(ch))
		temp = (ch - '0');

	if (isalpha(ch))
		temp = (isupper(ch) ? ch - 55 : ch - 87);
	return (char)temp;
}

/**
 * brief 将HEX字符转换为数值，严格检查入参
 *
 * param [in] character      需要转换的字符
 * param [out] hex           转换后的数值
 * return  bool
 *
 * details More details
 */
bool covertHextoNum2(uint8_t character, uint8_t *hex)
{
    uint8_t temp_hex;

    if (hex == NULL)
    {
        return false;
    }

    if ((character >= '0') && (character <= '9'))
    {
        temp_hex = character - '0';
    }
    else if ((character >= 'a') && (character <= 'f'))
    {
        temp_hex = (character - 'a') + 10; /* 10: 0xa == 10 */
    }
    else if ((character >= 'A') && (character <= 'F'))
    {
        temp_hex = (character - 'A') + 10; /* 10: 0xa == 10 */
    }
    else
    {
        return false;
    }

    *hex = temp_hex;
    return true;
}

bool isOctChar(char ch)
{
	if ((ch >= '0' && ch <= '7'))
		return true;
	else 
		return false;
}

int covertEscToAscII(char* p)
{
	int len = -1;
	int index_val = *(p + 1);
	int i = 0;
	for (; i < (int)(sizeof(esc_table) / sizeof(esc_item_t)); i++)
	{
		if (index_val == esc_table[i].ch)
		{
			*p = esc_table[i].esc_ch;
			xy_printf(0,PLATFORM_AP, DEBUG_LOG, "match esc table and changed:%d", *p);
			return 1;
		}
	}

	//Hex
	if(index_val == 'x')
	{
		if(isHexChar(*(p + 2)) && isHexChar(*(p + 3)))
		{
			*p = (covertHextoNum(*(p + 2)) << 4) + covertHextoNum(*(p + 3));
			xy_printf(0,PLATFORM_AP, DEBUG_LOG, "hex changed:%d", *p);
			return 3;
		}
		else if(isHexChar(*(p + 2)))
		{
			*p = covertHextoNum(*(p + 2));
			xy_printf(0,PLATFORM_AP, DEBUG_LOG, "hex changed:%d", *p);
			return 2;			
		}
	}

	//Octal eg:/101
	if (isOctChar(*(p + 1)) && isOctChar(*(p + 2)) && isOctChar(*(p + 3)))
	{
		*p = (char)(((*(p + 1) - '0') << 6) + ((*(p + 2) - '0') << 3) + (*(p + 3) - '0'));
		xy_printf(0,PLATFORM_AP, DEBUG_LOG, "oct changed:%d", *p);
		return 3;
	}
	else if (isOctChar(*(p + 1)) && isOctChar(*(p + 2)))
	{
		*p = (char)(((*(p + 1) - '0') << 3) + (*(p + 2) - '0'));
		xy_printf(0,PLATFORM_AP, DEBUG_LOG, "oct changed:%d", *p);
		return 2;
	}
	else if (isOctChar(*(p + 1)))
	{
		*p = (char)(*(p + 1) - '0');
		xy_printf(0,PLATFORM_AP, DEBUG_LOG, "oct changed:%d", *p);
		return 1;
	}

	return len;
}

/*将入参input里的ESC类型字符串转义为普通ASCII字符串，字符串长度有可能变小*/
void parase_esc_string(char *input)
{
	xy_assert(input != NULL);
	char *p = input;
	int offset = 0;
	unsigned int i = 0;

	while ((p = strchr(p, '\\')) != NULL && *(p + 1) != '\0')
	{
		if ((offset = covertEscToAscII(p)) != -1)
		{
			if(strlen(p + offset + 1) + 1 < (unsigned int)(offset)) //eg: \101a --> Aa,  原先1a位置必须置0
			{
				memset(p + 1, '\0', (unsigned int)(offset));
			}	
			for (i = 0; i <= strlen(p + offset + 1); i++)
			{
				*(p + 1 + i) = *(p + offset + 1 + i);
			}
		}
		p = p + 1;
	};
}


/*将入参str中ESC字符转义为ASCII字符串，数据长度可能缩短，最终长度为data_len*/
char *parase_esc_string2(char* str, int *data_len)
{
    char    *esc = NULL;
    ssize_t length = 0;
    *data_len = 0;
    esc = str;
    if(esc != NULL)
    {
        char    *psrc, *pdst;
        length = strlen(esc);
        *data_len = length;

        //处理转义字符
        psrc = pdst = esc;
        for(; psrc < esc + length;)
        {
            if(*psrc != '\\')
            {
                *pdst = *psrc; 
            }
            else if(psrc + 1 < length + esc)
            {
                char low_char = tolower(*(psrc + 1));
                switch(low_char)
                {
                    //case ',':
                    //    *pdst = ',';
                    //    break;
                    case 'a':        //BEL
                        *pdst = 07; 
                        break;
                    case 'b':        //backspace
                        if(pdst > esc) // 前一个有字符
                        {
                            pdst -= 2;
                        }
                        else
                        {
                            pdst--;
                        }
                        break;
                    case 'f':        //FF
                        *pdst = 12; 
                        break;
                    case 'n':        //LF
                        *pdst = 10; 
                        break;
                    case 'r':        //CR
                        *pdst = 13; 
                        break;
                    case 't':        //TAB
                        *pdst = 9; 
                        break;
                    case 'v':        //VT
                        *pdst = 11; 
                        break;
                    case '\\':
                        *pdst = '\\';
                        break;
                    case '\'':
                        *pdst = '\'';
                        break;
                    case '\"':        //实际上AT命令无法输入"符号
                        *pdst = '\"';
                        break;
                    case '?':
                        *pdst = '?';
                        break;
                    case '0':        //oct
                    case '1':
                    case '2':
                    case '3':
                    case '4':
                    case '5':
                    case '6':
                    case '7':
                        if(*(psrc + 1) == '0')//判断输入\0时，格式不是\0dd，则判断为输入\0，不是\ddd
                        {
                            if((psrc + 3 > length + esc) ||
                                (( *(psrc + 2) >= '0' && *(psrc + 2) <= '7' && 
                                *(psrc + 3) >= '0' && *(psrc + 3) <= '7') == false)
                                ) 
                            {
                                *pdst = 0;
                                break;
                            }
                        }
                        if(psrc + 3 > length + esc)
                            return NULL;
                        if( *(psrc + 1) >= '0' && *(psrc + 1) <= '3' && 
                            *(psrc + 2) >= '0' && *(psrc + 2) <= '7' && 
                            *(psrc + 3) >= '0' && *(psrc + 3) <= '7')
                        {
                            *pdst = ((*(psrc + 1) - '0') << 6) + ((*(psrc + 2) - '0') << 3) + *(psrc + 3) - '0';
                        }
                        else
                        {
                            return NULL;
                        }
                        psrc += 2;
                        break;
                    case 'x':        //hex
                        if(psrc + 3 > length + esc)
                            return NULL;
                        if(covertHextoNum2(*(psrc + 2),pdst) != true || 
                            covertHextoNum2(*(psrc + 3),&low_char) != true)
                        {
                            return NULL;
                        }
                        *pdst = (*pdst << 4) + low_char;
                        psrc += 2;
                        break;
                    default:
                        *pdst = *psrc;
                        psrc--;
                        break;
                    
                }
                psrc++;
            }
            else        //last character is '\\'
            {
                *pdst = *psrc; 
            }
            pdst++;
            psrc++;
        }
        *pdst = '\0';
        *data_len = pdst - esc;
    }
    return esc;
}



char* find_next_double_quato(char* data)
{
	xy_assert(data != NULL);
	char *tmp = NULL;

	while(*data != '\0')
	{
		if(*data == '"' && *(data - 1) != '\\')
		{
			tmp = data;
			break;
		}
		data++;
	}

	return tmp;
}

/*仅用于参数解析时的第几个关键字符匹配，如逗号、双引号等，需要关注n取值正确*/
char * at_strnchr(char *s, int c, int n)
{
	char *match = NULL;
	int i = 0;

	if(n == 0)
		return NULL;
	
	do {
		if (*s == (char)c) {
			i++;
			if (i == n) {
				match = (char *)s;
				break;
			}
		}
	} while (*s++);

	return match;
}


/*用于AT字符串头部前缀的模糊匹配，实现时考虑了source头部可能存在的"\r\nAT"等特殊字符干扰*/
char * at_prefix_strstr(char * source, char * substr)
{
#define  PREFIX_NULL_LEN   5   /*"AT+" 特殊修饰符，需要跳过去进行匹配*/

	uint32_t n = strlen(substr);
	char *head;
	char temp = 0;

	//URC会携带头部空格
	while(*source == '\r' || *source == '\n')
		source++;
	
	if(strlen(source) > n+PREFIX_NULL_LEN)
	{
		temp = *(source+n+PREFIX_NULL_LEN);
		*(source+n+PREFIX_NULL_LEN) = 0;
	}

	head = strstr(source,substr);

	if(temp != 0)
		*(source+n+PREFIX_NULL_LEN) = temp;
	
	if(head == NULL)
		return NULL;
	
	/*前缀有效字符串必须严格匹配，例如source="AT+QCCLK",substr="CCLK",则匹配失败。substr="QCCLK"才能匹配成功*/
	else if(((head > source) && (isalpha((int)(*(head-1))) || isdigit((int)(*(head-1))))) || isalpha((int)(*(head+n))) || isdigit((int)(*(head+n))))
		return NULL;

	else
		return head+n;
}

/**
 * @brief  不区分大小写的字符串比较，常用于AT命令参数解析时，字符串参数的识别；例如“IPV6” “ipv6”
 * @return 1 表示一样，例如“IPV6” “ipv6”
 *         0 表示不相同
 */
bool at_strcasecmp(const char *s1, const char *s2)
{
    int ch1 = 0;
    int ch2 = 0;

    if(NULL == s1 || NULL == s2)
	{
        return 0;
    }

    do
	{
        if((ch1 = *(unsigned char *)s1++) >= 'a' && (ch1 <= 'z'))
		{
            ch1 -= DIFF_VALUE;
        }
        if((ch2 = *(unsigned char *)s2++) >= 'a' && (ch2 <= 'z'))
		{
            ch2 -= DIFF_VALUE;
        }
    }while(ch1 && (ch1 == ch2));

	if(ch1 == ch2)
		return 1;
	else
		return 0;
}

/*不区分大小写的父子字符串比较，严格从第一个字符匹配，通常用于特殊字符串头的识别*/
bool at_strncasecmp(const char *father, const char *son)
{
    int ch1 = 0;
    int ch2 = 0;
	int n = strlen(son);

    if(NULL == father || NULL == son)
	{
        return 0;
    }

    do
	{
        if((ch1 = *(unsigned char *)father++) >= 'a' && (ch1 <= 'z'))
		{
            ch1 -= DIFF_VALUE;
        }
        if((ch2 = *(unsigned char *)son++) >= 'a' && (ch2 <= 'z'))
		{
            ch2 -= DIFF_VALUE;
        }
    }while(--n && ch1 && (ch1 == ch2));

    if(ch1 == ch2)
		return 1;
	else
		return 0;
}


