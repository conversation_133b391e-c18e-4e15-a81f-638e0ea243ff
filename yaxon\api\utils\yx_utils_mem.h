/**********************************************************************/
/**
 * @file yx_utils_mem.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-11
 * @version V1.0
 * @brief 内存管理安全接口
 **********************************************************************/
#ifndef YX_UTILS_MEM_H
#define YX_UTILS_MEM_H

#include "yx_type.h"

/**
 * @brief 设置内存操作回调函数
 * @param callback 回调函数指针
 *  - err_code: 错误码，1表示内存分配失败
 * @return 无
 */
INT32S yx_utils_mem_err_cb(VOID (*diag_cb)(INT32S err_code));

/**
 * @brief 分配动态内存
 * @ingroup yx_utils
 * @param size 分配的空间大小
 * @return 分配到的内存指针; 如分配失败, 则返回0
 */
VOID* yx_utils_mem_malloc(INT32U size);

/**
 * @brief 释放动态分配的内存
 * @ingroup yx_utils
 * @param p 待释放的内存指针
 * @return 无
 */

VOID yx_utils_mem_free(VOID* p);

/**
 * @brief 安全内存初始化（封装 memset）
 * @ingroup yx_utils
 * @param dest 目标内存指针（Output）
 * @param value 填充值（会转换为 unsigned char）
 * @param n 填充字节数（最小值=0）
 * @return 成功返回 dest，失败返回 NULL
 */
VOID* yx_utils_mem_memset(VOID* dest, int value, INT32U n);

/**
 * @brief 内存拷贝（封装 memcpy）
 * @ingroup yx_utils
 * @param dest 目标内存指针（Output）
 * @param src 源内存指针（Input）
 * @param n 拷贝字节数（最小值=0）
 * @return 成功返回 dest，失败返回 NULL
 */
VOID* yx_utils_mem_memcpy(VOID* dest, const VOID* src, INT32U n);

/**
 * @brief 安全地将源内存区域的前 n 个字节复制到目标内存区域
 * @ingroup yx_utils
 * @param dest 目标内存区域指针，用于存储复制后的数据
 * @param dest_size 目标内存区域的大小（字节数）
 * @param src 源内存区域指针，需要被复制的数据
 * @param n 需要复制的字节数
 * @return 成功返回RTN_OK；
 *         如果目标或源内存区域为空指针 返回RTN_ERR
 *         或者目标或源内存区域大小为0 返回RTN_ERR
 *         或者 n 大于目标或源内存区域大小 返回RTN_ERR
 */
INT32S yx_utils_mem_memcpy_s(VOID* dest, INT32U dest_size, const VOID* src, INT32U n);

/**
 * @brief 安全内存比较（封装 memcmp）
 * @ingroup yx_utils
 * @param s1 内存区域1（Input）
 * @param s2 内存区域2（Input）
 * @param n 比较字节数（最小值=0）
 * @return s1 < s2 返回负数，s1 > s2 返回正数，相等或无效参数返回0
 */
INT8S yx_utils_mem_memcmp(const VOID* s1, const VOID* s2, INT32U n);

#endif
