/**********************************************************************/
/**
 * @file yx_ia_adc.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-14
 * @version V1.0
 * @brief adc接口适配
 **********************************************************************/
#ifndef YX_IA_ADC_H
#define YX_IA_ADC_H

#include "yx_type.h"

typedef enum {
    YX_IA_ADC_CHANNEL_0 = 0,
    YX_IA_ADC_CHANNEL_1,
    YX_IA_ADC_CHANNEL_2,
    YX_IA_ADC_CHANNEL_3,
    YX_IA_ADC_CHANNEL_MAX
} yx_ia_adc_e;

/**
 * @brief ADC读取
 * @ingroup ia_adc
 * @param[in] channel 雅迅ADC通道
 * @return INT32S
 * @retval 0或正数  ADC读数，单位mv
 * @retval RTN_ERR  失败
 */
INT32S yx_ia_adc_read(yx_ia_adc_e channel);

#endif /* YX_IA_ADC_H */