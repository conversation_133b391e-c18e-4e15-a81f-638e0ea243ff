#pragma once

/*******************************************************************************
 *							Include header files							   *
 ******************************************************************************/
#include "at_com.h"
#include "cmsis_os2.h"
#include <stdbool.h>
#include <stdint.h>
#include <stddef.h>

/*******************************************************************************
 *                             Macro definitions                               *
 ******************************************************************************/
/* at ctl thread */
#define AT_CTRL_THREAD_NAME         "at_ctl"
#define AT_CTRL_THREAD_STACKSIZE 	0x800
#define AT_CTRL_THREAD_PRIO 		osPriorityAboveNormal1	
#define AT_CTRL_QUEUE_SIZE          12

/* at proxy thread */
#define AT_PROXY_THREAD_NAME        "atproxy"
#if XY_AUDIO
#define AT_PROXY_THREAD_STACKSIZE 	0x1800      /* audio at命令调用auido接口栈比较大 */
#else
#define AT_PROXY_THREAD_STACKSIZE 	0xC00
#endif /* XY_AUDIO */
#define AT_PROXY_THREAD_PRIO 		AT_CTRL_THREAD_PRIO
#define AT_PROXY_QUEUE_SIZE         10

/* at recv thread */
#define AT_RECV_THREAD_NAME        "atrcv"
#define AT_RECV_THREAD_STACKSIZE 	0x1000
#define AT_RECV_THREAD_PRIO         osPriorityHigh

/*接收到的外部AT请求缓存条数，以应瀑布式多条AT请求的场景*/
#define AT_RECV_CMD_CACHE_NUM       10
/* at urc cache max num */
#define AT_URC_CACHE_MAX_NUM        20

/* AT框架允许的xy_printf最大长度，限制部分长AT命令打印 */
#define AT_DEBUG_LOG_MAX_LEN        3800
/* 受限于usb modem输出长度限制，输出超过限制长度立刻丢弃 */
#define USB_MODEM_AT_OUTPUT_LIMIT   8192
/* at_send_wait_rsp接口使用%a格式时，输出的最大数据长度 */
#define USR_AT_RECV_BUFFER_SIZE     1024

/*******************************************************************************
 *                             Type definitions                                *
 ******************************************************************************/
enum AT_MSG_ID
{
	AT_MSG_RCV_STR_FROM_FARPS,	//farps send  at cmd to  at_ctl
	AT_MSG_RCV_STR_FROM_NEARPS, //ps(cp/user rsp) send at cmd to at_ctl
	AT_MSG_RCV_FROM_GNSS,       //GNSS芯片发送来的AT命令
};
	
typedef struct at_msg
{
	int msg_id;       //@see @ref AT_MSG_ID
    int srcFd;        //@see @ref AT_TTY_FD
    int size;
  	int offset;       //at参数偏移长度
  	char data[0];
} at_msg_t;

/* AT通道核间信息结构体 */
typedef struct
{
    uint32_t     len;
    uint32_t     buf;
    int          ttyFd;
} icc_at_t;

typedef enum
{
    /*业务线程通过at_send_wait_rsp接口进行AT命令的收发，慎用！ */
    FARPS_USER_MIN = AT_FD_MAX,
    FARPS_XY_PROXY = FARPS_USER_MIN,
    FARPS_USER1,
    FARPS_USER2,
    FARPS_USER_MAX,
} AT_USR_SRC_FD;

/*******************************************************************************
 *                       Global variable declarations                          *
 ******************************************************************************/
extern at_cmd_t *g_at_basic_req;
extern int8_t g_at_lpuart_lock;
extern osMutexId_t gAtLpuartLock_Mutex;

/*******************************************************************************
 *                       Global function declarations                          *
 ******************************************************************************/
/**
 * @brief at模块初始化接口,在main函数中调用
 * @note 初始化at模块资源，如全局的at上下文,互斥锁,ps回调注册以及创建at_ctrl线程
 */
void at_ctl_init();

/**
 * @brief at外设数据接收线程初始化
 */
void at_recv_task_init(void);

/**
 * @brief at proxy模块初始化接口,在at_init函数中调用
 */
void at_proxy_init(void);

/**
 * @brief 发送at数据到at_ctrl线程 
 * @param msg_id [IN] 消息ID @see @ref AT_MSG_ID
 * @param data   [IN] at数据地址
 * @param size   [IN] at数据长度，不包括'\0'
 * @param ttyFd  [IN] at tty外设FD @see @ref AT_TTY_FD
 * @return XY_OK:成功 XY_Err_NotAllow：失败
 */
int send_msg_2_atctl(int msg_id, void *data, int size, int ttyFd);

/**
 * @brief 转发at命令到其他at上下文中
 * @param msg [IN] at消息 @see @ref at_msg_t
 * @note 一般用于将at命令转给3gpp处理的场景
 */
 void at_req_forward(at_msg_t *msg);

/**
 * @brief 处理lpuart外设发送过来的at数据
 * @param data [IN] at数据
 * @param size [IN] at数据长度
 */
 bool at_recv_from_lpuart(char *data, size_t size);

  /**
 * @brief 发送at数据给lpuart外设
 * @param data  [IN] 需要输出的数据，字符串形式
 * @param size  [IN] 输出的数据大小，不包含'\0'
 * @note  调用者需手动释放buf指向的数据
 */
 bool at_send_to_lpuart(void *data, size_t size);

/**
 * @brief 处理logview工具发送过来的at数据
 * @param data [IN] at数据
 * @param size [IN] at数据长度
 */
 bool at_recv_from_log(char *data, size_t size);

  /**
 * @brief 发送at数据给logview工具
 * @param data  [IN] 需要输出的数据，字符串形式
 * @param size  [IN] 输出的数据大小，不包含'\0'
 * @note  调用者需手动释放buf指向的数据
 */
 bool at_send_to_log(void *data, size_t size);

/**
 * @brief 处理usb modem口发送过来的数据，主要用于拨号上网
 * @param data [IN] 数据
 * @param size [IN] 数据长度
 */
 bool at_recv_from_usb_modem(char *data, size_t size);

  /**
 * @brief 发送at数据给usb modem口，由at_context ouput调用,内部在处理at命令输出前会进行一些特殊处理
 * 如重置at上下文，根据需求过滤urc信息，重新打开standby功能等
 * @param data  [IN] 需要输出的数据，字符串形式
 * @param size  [IN] 输出的数据大小，不包含'\0'
 * @note  调用者需手动释放buf指向的数据
 */
 bool at_send_to_usb_modem(void *data, size_t size);

/**
 * @brief 处理usb at口发送过来的at数据
 * @param data [IN] at数据
 * @param size [IN] at数据长度
 */
 bool at_recv_from_usb(char *data, size_t size);

 /**
 * @brief 发送at数据给usb at口,由at_context ouput调用,内部在处理at命令输出前会进行一些特殊处理
 * 如重置at上下文，根据需求过滤urc信息，重新打开standby功能等
 * @param data  [IN] 需要输出的数据，字符串形式
 * @param size  [IN] 输出的数据大小，不包含'\0'
 * @note  调用者需手动释放buf指向的数据
 */
 bool at_send_to_usb(void *data, size_t size);

/**
 * @brief 处理CMUX虚拟数据通道1发送过来的at数据
 * @param data [IN] at数据
 * @param size [IN] at数据长度
 */
 bool at_recv_from_cmux1(char *data, size_t size);

 /**
 * @brief 发送at数据给CMUX虚拟数据通道1
 * @param data  [IN] 需要输出的数据，字符串形式
 * @param size  [IN] 输出的数据大小，不包含'\0'
 * @note  调用者需手动释放buf指向的数据
 */
 bool at_send_to_cmux1(void *data, size_t size); 

/**
 * @brief 处理CMUX虚拟数据通道2发送过来的at数据
 * @param data [IN] at数据
 * @param size [IN] at数据长度
 */
 bool at_recv_from_cmux2(char *data, size_t size);

 /**
 * @brief 发送at数据给CMUX虚拟数据通道2
 * @param data  [IN] 需要输出的数据，字符串形式
 * @param size  [IN] 输出的数据大小，不包含'\0'
 * @note  调用者需手动释放buf指向的数据
 */
 bool at_send_to_cmux2(void *data, size_t size); 

 /**
 * @brief 处理uart0外设发送过来的at数据
 * @param data [IN] at数据
 * @param size [IN] at数据长度
 */
 bool at_recv_from_uart(char *data, size_t size);

  /**
 * @brief 发送at数据给uart0外设
 * @param data  [IN] 需要输出的数据，字符串形式
 * @param size  [IN] 输出的数据大小，不包含'\0'
 * @note  调用者需手动释放buf指向的数据
 */
 bool at_send_to_uart(void *data, size_t size);

 /**
 * @brief  通过指定FARPS上下文发送at数据,包括at_send_wait_rsp虚拟通道
 * @param buf [IN] at数据地址
 * @param size [IN] at数据长度，不包含'\0'
 * @param ttyFd [IN] 使用的外设fd, @see @ref AT_TTY_FD
 * @return  XY_OK: 写成功, XY_ERR: 写失败
 */
 bool at_send_to_farps(void *data, size_t data_len, int ttyFd);

 /**
 * @brief 接收nearps端发送过来的at数据
 * @param buf [IN] at数据地址
 * @param len [IN] at数据长度，不包含'\0'
 */
 void at_recv_from_CP(void *buf, size_t len);

 /**
 * @brief 发送at命令内容到nearps端，双核模式下，通过核间通道将内容传递给CP
 * @param buf [IN] at数据
 * @param len [IN] at数据长度，不包含'\0'
 * @param ttyFd [IN] 使用的外设fd, @see @ref AT_TTY_FD
 * @note at内容采用零拷贝机制
 */
int at_send_to_CP(void *buf, size_t size, int ttyFd);

 /**
  * @brief 启动阶段上报系统URC，如POWERON/NPSMR
  * @note 该接口内部可做定制，如移远上报Neul
  */
 void Sys_Up_URC(void);

 /**
 * @brief  AT请求正在执行过程中，缓存URC
 * @param  ttyFd [IN] 使用的外设fd, @see @ref AT_TTY_FD
 * @param  urc [IN] 主动上报的AT数据
 * @param  size [IN] 主动上报的AT数据长度
 * @note AT命令执行期间，缓存期间上报的URC数据，直到当前AT命令处理完成后，将缓存的URC数据统一上报
 */
 void at_add_urc_cache(int ttyFd, char *urc, size_t size);

 /**
 * @brief 待当前AT请求的应答结果发送完毕后，立即调用该接口将缓存的URC发送出去
 * @note AT命令执行期间，缓存期间上报的URC数据，直到当前AT命令处理完成后，将缓存的URC数据统一上报
 * @param  ttyFd [IN] 使用的外设fd，@see @ref AT_TTY_FD
 */
 void at_send_urc_cache(int ttyFd);

 /**
 * @brief 发送at消息到at proxy线程，一般用于基础平台及用户定义的at命令处理
 * @param  buff [IN] 传递的数据地址
 * @param  len [IN] 传递的数据长度
 */
 int send_msg_2_at_proxy(void *buff, int len);

/**
 * @brief at数据输出到相应tty,可输出URC或者结果码
 * @param tty_Fd [IN] 使用的外设fd, @see @ref AT_TTY_FD
 * @param buf [IN] 输出的数据地址
 * @param size [IN] 输出的数据长度
 * @param result_directly [IN] true:已经是结果码，内部无须再判断，用于透传结果码直接输出
 */
 bool at_send_to_tty(int tty_Fd, void *buf, size_t size, bool result_directly);

 /**
 * @brief at配置信息初始化
 */
void at_config_init(void);

/**
 * @brief 往指定AT通道写入数据
 * @param tty [IN] 使用的外设fd, @see @ref AT_TTY_FD
 * @param data [IN] 输出的数据地址
 * @param size [IN] 输出的数据长度
 */
int at_posix_write(int tty, const void *data, size_t size);

/**
 * @brief 往USB AT MODEM写入数据，需先使用at_posix_ioctl打开数据零拷贝功能，零拷贝数据结束后需使用at_posix_ioctl关闭数据零拷贝功能
 * @param tty [IN] 使用的外设fd, @see @ref AT_TTY_FD，只能为AT_USB_MODEM_FD
 * @param data [IN] 申请的需输出的数据内存
 * @param size [IN] 输出的数据长度
 * @warning 返回XY_OK时，data内存由接口内部释放；返回失败时，data内存由申请者自行释放
 * @note 目前仅PPP拨号上网使用，其他人不得随意使用
 */
int at_posix_write_zerocopy(int tty, const void *data, size_t size);

/**
 * @brief 操作指定AT通道的基本设备参数
 * @param tty [IN] 使用的外设fd, @see @ref AT_TTY_FD
 */
int at_posix_ioctl(int tty, unsigned long cmd, void *args);




