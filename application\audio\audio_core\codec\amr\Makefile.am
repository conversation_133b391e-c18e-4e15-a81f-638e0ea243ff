SUBDIRS = amrnb amrwb
if EXAMPLES
    SUBDIRS += test
endif

ACLOCAL_AMFLAGS = -I m4

noinst_HEADERS = oscl/oscl_base.h oscl/oscl_mem.h oscl/oscl_base_macros.h

EXTRA_DIST = $(top_srcdir)/LICENSE \
    $(top_srcdir)/opencore/README \
    $(top_srcdir)/opencore/NOTICE opencore/ChangeLog \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/patent_disclaimer.txt \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/common/dec/include \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/common/dec/build \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/common/dec/Android.mk \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/common/Android.mk \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/common/build \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/common/include \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/common/src/*.cpp \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/dec/Android.mk \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/dec/build \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/dec/include \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/dec/src/*.cpp \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/dec/src/*.h \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/enc/Android.mk \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/enc/build \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/enc/include \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/enc/src/*.cpp \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_nb/enc/src/*.h \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_wb/dec/Android.mk \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_wb/dec/build \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_wb/dec/include \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_wb/dec/src/*.cpp \
    $(top_srcdir)/opencore/codecs_v2/audio/gsm_amr/amr_wb/dec/src/*.h

