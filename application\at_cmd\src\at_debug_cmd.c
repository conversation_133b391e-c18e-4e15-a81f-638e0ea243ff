/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "app_basic_config.h"
#include "xy_at_api.h"
#include "oss_nv.h"
#include "xy_system.h"
#include "xy_flash.h"
#include "softap_nv.h"
#include "osAssistantUtils.h"
#include "xy_defwan_api.h"
#include "xy_tcpip_api.h"
#include "net_debug.h"
#include "app_utils.h"
#include "hal_gpio.h"

typedef struct
{
    uint8_t day;
    uint8_t mon;
    uint16_t year;
    uint8_t min;
    uint8_t hour;
    uint16_t padding;
} ver_dbg_info_t;

#if (FLASH_LIMIT==0)
void get_ip_pkt_info(int atHandle)
{
	int n;
	char *prsp_cmd = xy_malloc(2048);
    n  = sprintf(prsp_cmd,     "\r\n+AP_IPINFO:\r\nCID=%d", xy_get_DefWan_Cid());
    n += sprintf(prsp_cmd + n, "\r\nue_ip4_type=%d", g_softap_fac_nv->ue_ip4_type);
    n += sprintf(prsp_cmd + n, "\r\nIPtype=%d", xy_get_DefWan_IpType());
    n += sprintf(prsp_cmd + n, "\r\nWanIsOK=%d", xy_DefWan_is_ok());
    n += sprintf(prsp_cmd + n, "\r\nOOS=%d", ps_is_oos());
    n += sprintf(prsp_cmd + n, "\r\nnetMallocSucc=%u,netMallocFlow=%u,netMallocFail=%u,netFree=%d", gIpStatsInc.netMallocSucc,gIpStatsInc.netMallocFlow, gIpStatsInc.netMallocFail,gIpStatsInc.netFreeCnt);
	n += sprintf(prsp_cmd + n, "\r\nrecv USB:total=%d,FastForward(Succ=%d,Fail=%d),LwipForward(Succ=%d,Fail=%d),other(MsgFull=%d,EthDown=%d,Dirty=%d)", gIpStatsInc.recv_ux.pkt_total, \
		gIpStatsInc.recv_ux.fast_succ, gIpStatsInc.recv_ux.fast_drop, gIpStatsInc.recv_ux.lwip_succ, gIpStatsInc.recv_ux.lwip_drop, gIpStatsInc.recv_ux.msg_full, gIpStatsInc.recv_ux.eth_down, gIpStatsInc.recv_ux.dirty_pkt);
	n += sprintf(prsp_cmd + n, "\r\nsend USB:total=%d,FastForward(Succ=%d,Fail=%d),LwipForward(Succ=%d,Fail=%d),other(MsgFull=%d,EthDown=%d,Dirty=%d)", gIpStatsInc.send_ux.pkt_total, \
		gIpStatsInc.send_ux.fast_succ, gIpStatsInc.send_ux.fast_drop, gIpStatsInc.send_ux.lwip_succ, gIpStatsInc.send_ux.lwip_drop, gIpStatsInc.send_ux.msg_full, gIpStatsInc.send_ux.eth_down, gIpStatsInc.send_ux.dirty_pkt);
	n += sprintf(prsp_cmd + n, "\r\nspec remote[recv USB:addr=0x%X,total=%d,FastForward(Succ=%d,Fail=%d),LwipForward(Succ=%d,Fail=%d),other(MsgFull=%d,EthDown=%d,Dirty=%d);", gIpStatsInc.spec_ipaddr, gIpStatsInc.spec_recv_ux.pkt_total, gIpStatsInc.spec_recv_ux.fast_succ, \
		gIpStatsInc.spec_recv_ux.fast_drop, gIpStatsInc.spec_recv_ux.lwip_succ, gIpStatsInc.spec_recv_ux.lwip_drop, gIpStatsInc.spec_recv_ux.msg_full, gIpStatsInc.spec_recv_ux.eth_down, gIpStatsInc.spec_recv_ux.dirty_pkt);
	n += sprintf(prsp_cmd + n, "send USB:total=%d,FastForward(Succ=%d,Fail=%d),LwipForward(Succ=%d,Fail=%d),other(MsgFull=%d,EthDown=%d,Dirty=%d)]", gIpStatsInc.spec_send_ux.pkt_total, gIpStatsInc.spec_send_ux.fast_succ, \
		gIpStatsInc.spec_send_ux.fast_drop, gIpStatsInc.spec_send_ux.lwip_succ, gIpStatsInc.spec_send_ux.lwip_drop, gIpStatsInc.spec_send_ux.msg_full, gIpStatsInc.spec_send_ux.eth_down, gIpStatsInc.spec_send_ux.dirty_pkt);	
	n += sprintf(prsp_cmd + n, "\r\nsend PS:total=%d,FastForward(Succ=%d,Fail=%d),LwipForward(Succ=%d,Fail=%d),other(MsgFull=%d,EthDown=%d,Dirty=%d)", gIpStatsInc.send_ps.pkt_total, \
		gIpStatsInc.send_ps.fast_succ, gIpStatsInc.send_ps.fast_drop, gIpStatsInc.send_ps.lwip_succ, gIpStatsInc.send_ps.lwip_drop, gIpStatsInc.send_ps.msg_full, gIpStatsInc.send_ps.eth_down, gIpStatsInc.send_ps.dirty_pkt);
	n += sprintf(prsp_cmd + n, "\r\nrecv PS:total=%d,FastForward(Succ=%d,Fail=%d),LwipForward(Succ=%d,Fail=%d),other(MsgFull=%d,EthDown=%d,Dirty=%d)", gIpStatsInc.recv_ps.pkt_total, \
		gIpStatsInc.recv_ps.fast_succ, gIpStatsInc.recv_ps.fast_drop, gIpStatsInc.recv_ps.lwip_succ, gIpStatsInc.recv_ps.lwip_drop, gIpStatsInc.recv_ps.msg_full, gIpStatsInc.recv_ps.eth_down, gIpStatsInc.recv_ps.dirty_pkt);
	n += sprintf(prsp_cmd + n, "\r\nMsg Queue Used:Mbox(max=%d,fail=%d),ux_max=%d,main_proxy_max=%d", gIpStatsInc.mbox_used_max, gIpStatsInc.mbox_fail, gIpStatsInc.ux_queue_max, gIpStatsInc.main_proxy_queue_max);
#if LWIP_STATS
	stats_display(prsp_cmd + n);
#endif 
	appAtWriteImmediately(atHandle, prsp_cmd, strlen(prsp_cmd));
	xy_free(prsp_cmd); 
}
#endif

/*寄存器内存读写及版本烧录时间获取*/
int at_DEBUG_req(char *at_buf, char **prsp_cmd)
{
	uint8_t cmd[16] = {0};
	uint32_t addr = 0;
	uint32_t val = 0;
	uint32_t val2 = 0;
	uint32_t val3 = 0;
	int atHandle = get_current_ttyFd();
	
#if (FLASH_LIMIT==0)
	if (g_req_type == AT_CMD_TEST || g_req_type == AT_CMD_ACTIVE)
	{
        *prsp_cmd = xy_malloc(60);
        snprintf(*prsp_cmd, 60, "AT+DEBUG=GET|SET|DL|HEAP|TASK|NET|TIMER|FS|POOL|IMAGE");
		return AT_END;
	}
#endif	
	if (at_parse_param("%15s,%d,%d,%d,%d", at_buf, cmd, &addr, &val,&val2,&val3) != XY_OK)
	{
		*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
		return AT_END;
	}

	flash_protect_disable();

	// AT+DEBUG=WDT     rtc看门狗的构造
	if (!strcmp((const char *)(cmd), "WDT"))
	{
		osCoreEnterCritical();
		while(1);
	}
	// AT+DEBUG=GET,SOC  获取SOC芯片类型：A为1;LC为0X10;LD为0X20
	else if (!strcmp((const char *)(cmd), "SOC"))
	{
		extern volatile uint8_t product_version;
		*prsp_cmd = xy_malloc(30);
		sprintf(*prsp_cmd, "%d ",product_version);
	}
#if PS_TEST_MODE //场测板测试相关，配置GPIOD为高电平，打开外部SD卡供电
	else if(!strcmp((const char *)(cmd), "GS_POWERSW"))
	{
		uint8_t pinlevel = addr;			
		gpio_init_cfg(GPIO_PIN_D, GPIO_MODE_OUTPUT_PP, GPIO_FLOAT, pinlevel);	
	}
#endif

	// AT+DEBUG=GET,address  4字节内容读取，包括RAM/寄存器/FLASH等内存空间
	else if (!strcmp((const char *)(cmd), "GET"))
	{
		*prsp_cmd = xy_malloc(100);
		sprintf(*prsp_cmd, "0x%X ", (int)HWREG(addr));
	}
	// AT+DEBUG=SET,address,value   4字节RAM寄存器写入
	else if (!strcmp((const char *)(cmd), "SET"))
	{
		if(addr>FLASH_START_ADDR && addr<FLASH_START_ADDR+0x10000000)
			xy_flash_write((void*)addr, &val, 4);
		else
			HWREG(addr) = val;
	}
	// AT+DEBUG=RFLASH,address,len  读4K flash内容
	else if (!strcmp((const char *)(cmd), "RFLASH"))
	{
		char *data = xy_malloc(0x1000);
		char *hexdata = xy_malloc(0x2000 + 1);

		if (val == 0)
			val = 0x1000;

		xy_flash_read((void*)addr, data, val);
		bytes2hexstr(data, val, hexdata, 2 * val + 1);
		appAtWriteImmediately(atHandle, hexdata, strlen(hexdata));
		xy_free(data);
		xy_free(hexdata);
	}
	// AT+DEBUG=WFLASH,address,val   仅支持写FLASH的4字节的内容
	else if (!strcmp((const char *)(cmd), "WFLASH"))
	{
		xy_flash_write((void*)addr, &val, 4);
	}
	// AT+DEBUG=CFLASH,address,len  擦除FLASH
	else if (!strcmp((const char *)(cmd), "CFLASH"))
	{
		xy_flash_erase((void*)addr, val);
	}
	// AT+DEBUG=MFLASH,address,len,dest  将一块空间搬移到另一块空间，常用于RF NV备份等特殊用途
	else if (!strcmp((const char *)(cmd), "MFLASH"))
	{
		if(val == 0)
			val= 0X1000;
		char *data = xy_malloc(val);
		xy_flash_read((void*)addr, data, val);
		xy_flash_erase((void*)val2, val);
		xy_flash_write((void*)val2,data,val);
		xy_free(data);
	}
	// AT+DEBUG=COMPARE,address,len,dest  将两块FLASH区域内容进行比较
	else if (!strcmp((const char *)(cmd), "COMPARE"))
	{
		if(val == 0)
			val= 0X1000;
		char *data = xy_malloc(val);
		char *data1 = xy_malloc(val);
		xy_flash_read((void*)addr, data, val);
		xy_flash_read((void*)val2, data1, val);
		if(memcmp(data,data1,val) != 0 )
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Unknown);
		xy_free(data);
		xy_free(data1);
	}
	else if (!strcmp((const char *)(cmd), "FSSAVE"))
	{
		app_write_fs(APP_BASIC_CONFIG_FILE_NAME, (void *)&g_app_basic_cfg,SIZEOF_APPBASICCFG());
	}
	else if (!strcmp((const char *)(cmd), "FSDEL"))
	{
		xy_fremove(APP_BASIC_CONFIG_FILE_NAME);
	}
	// AT+DEBUG=DL  版本烧录时间点
	else if (!strcmp((const char *)(cmd), "DL"))
	{
		ver_dbg_info_t info = {0};
		*prsp_cmd = xy_malloc(320);

		sprintf(*prsp_cmd, "\r\n+AP core build time %s", get_build_time(false));
		xy_flash_read((void*)DEBUG_INFO_BASE, &info, sizeof(ver_dbg_info_t)); /*对应地址为0x60002F00*/
		sprintf(*prsp_cmd + strlen(*prsp_cmd), "\r\nVer download Time=%x/%x/%x-%x:%x", info.year, info.mon, info.day, info.hour, info.min);
		sprintf(*prsp_cmd + strlen(*prsp_cmd), "\r\nSoC=XY4100-%s, Hardware=%s, usr software=%s", SOC_VERTION, g_softap_fac_nv->hardver, get_sdk_version());
	}
	else if (!strcmp((const char *)(cmd), "FS"))
	{
		uint32_t len = 0;
		fs_size_info_t fs_info = {0};
	
		*prsp_cmd = (char *)xy_malloc(100);
		xy_fsize_info("D:", &fs_info);
		sprintf(*prsp_cmd, "fs total:%d,remain:%d\r\n", WORKING_FS_LEN, fs_info.lfs_remain_size);
	}

	else if (!strcmp((const char *)(cmd), "FOTA"))
	{
		uint32_t PktSize=0;
		uint32_t OldImgSize=0;
		uint32_t NewImgSize=0;
		uint32_t FOTA_REGION_size=0;
		void* startAddr = NULL;
		uint32_t availSpace = GetAPbinReservedFlash(&startAddr,NULL);	

		if (at_parse_param(",%d,%d,%d,%d",at_buf,&PktSize,&NewImgSize,&OldImgSize,&FOTA_REGION_size) != XY_OK)
		{
			*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
			return AT_END;
		}		
				
		/*AT+DEBUG=FOTA  查询AP核image剩余可用空间及FOTA包存储区空间*/
		if(PktSize == 0)
		{
			*prsp_cmd = (char *)xy_malloc(100);
			sprintf(*prsp_cmd, "AP image total:0x%x,Reserverd:0x%x + 0x%x. FOTA_PKT_REGION:0x%x + 0x%x,\r\n", (uint32_t)AP_FLASH_BASE_LEN,startAddr,availSpace,FOTA_PKT_REGION_BASE,FOTA_PKT_REGION_LEN);
		}
		else /*AT+DEBUG=FOTA,<升级包大小>,<AP新分区大小>[,<AP老分区大小>,<FOTA包存储区大小>]     ，仅对FullFOTA有意义，检测FOTA包是否放得下。可选参数填0表示选用当前运行版本的配置*/
		{
			uint32_t ImgMaxSize = 0;
			
			if(OldImgSize == 0)
				OldImgSize = AP_FLASH_BASE_LEN - availSpace;
			if(FOTA_REGION_size == 0)
				FOTA_REGION_size = FOTA_PKT_REGION_LEN;

			ImgMaxSize = (OldImgSize>NewImgSize ? OldImgSize:NewImgSize);
			if(((uint32_t)AP_FLASH_BASE_LEN)-ImgMaxSize+FOTA_REGION_size >  PktSize)
			{
				return AT_END;
			}
			else
			{
				*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_NoMemory);
				return AT_END;
			}
		}
	}
			
#if (FLASH_LIMIT==0)
	else if (!strcmp((const char *)(cmd), "HEAP"))
	{
		uint32_t len = 0;
		osHeapInfo_T heapInfo = {0};
		extern uint32_t gMallocFailNum;
		
		osHeapInfoGet(&heapInfo);
		char *rsp = (char *)xy_malloc(120);
		len = sprintf(rsp, "Total:%d\r\n", heapInfo.TotalHeapSize);
		len += sprintf(rsp + len, "Used:%d\r\n", heapInfo.AllocatedHeapSize);
		len += sprintf(rsp + len, "Free:%d\r\n", heapInfo.FreeRemainHeapSize);
		len += sprintf(rsp + len, "Min Free:%d\r\n", heapInfo.MinimumEverFreeHeapSize);
		len += sprintf(rsp + len, "Block Used:%d\r\n", heapInfo.AllocatedBlockNum);
		len += sprintf(rsp + len, "Block Free:%d\r\n", heapInfo.FreedBlockNum);
		len += sprintf(rsp + len, "MallocFail:%u\r\n",gMallocFailNum);
		appAtWriteImmediately(atHandle, rsp, strlen(rsp));
		xy_free(rsp);
		return AT_FORWARD;
	}
	else if(!strcmp((const char *)(cmd), "TASK"))
	{
		/*Task:线程名,Pri:优先级，Stack:初始栈大小，UsedMax:栈使用峰值，State:栈状态*/
		char *rsp = (char*)xy_malloc(3000);
		uint32_t len = 0;
		len = sprintf(rsp, "\r\n-------AP STACK INFO-------\r\n");
		osTaskStackInfo_T *info = osTaskStackInfoGet();
		while (info != NULL)
		{
			len += sprintf(rsp + len, "\r\nTask:%s,\t Pri:%d,\t Stack:%d,\t UsedMax:%d,\t State:%d",
							   info->taskName, info->prority, info->initSpace, info->maxUsedSpace, info->state);
			info = info->next;
		}
		osTaskStackInfoFree();
		
		appAtWriteImmediately(atHandle, rsp, strlen(rsp));
		xy_free(rsp);
		return AT_FORWARD;
	}
	else if(!strcmp((const char *)(cmd), "NET"))
	{
		get_ip_pkt_info(atHandle);
		return AT_FORWARD;
	}
	else if(!strcmp((const char *)(cmd), "LOG"))
	{
		diag_buffer_info_t log_info = {0};

		diag_buffer_get_info(&log_info);
		
		char *rsp = (char*)xy_malloc(100);
		sprintf(rsp, "\r\nAP alloc_fail %d\r\n", log_info.alloc_fail);
		appAtWriteImmediately(atHandle, rsp, strlen(rsp));
		xy_free(rsp);
		
		return AT_FORWARD;
	}
	else if(!strcmp((const char *)(cmd), "LPM"))
	{
		extern void get_lock_str(char **prsp_cmd);
		get_lock_str(prsp_cmd);
	}
	
#if( configUSE_TIMER_LPM_STATISTICS == 1 )
	else if(!strcmp((const char *)(cmd), "TIMER"))
	{
		uint32_t len = 0;
		*prsp_cmd = xy_malloc(320);
		TimerLowpowerInfo_T* tmrInfo = (TimerLowpowerInfo_T*)osTimerGetLpmInfo();
		uint32_t currentTick = osKernelGetTickCount();
		TimerLowpowerInfo_T* iter = tmrInfo;
		len = sprintf(*prsp_cmd, "\r\nCurrentTick:%d\r\n", currentTick);
		while (iter != NULL)
		{
			len += sprintf(*prsp_cmd + len, "\r\n%s:%d:%d\r\n", iter->pcTimerName, iter->xNextPeriodInTicks, iter->ucLowPowerFlag);
			iter = iter->pNext;
		}
		osTimerFreeLpmInfo();
	}
#endif
#endif
	else
	{
		return AT_FORWARD;
	}
	
	flash_protect_enable();
	
	return AT_END;
}

/*获取剩余RAM内存*/
int at_HEAPINFO_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_ACTIVE)
	{
		osHeapInfo_T heapInfo = {0};
		
		osHeapInfoGet(&heapInfo);
		*prsp_cmd = (char *)xy_malloc(64);
		snprintf(*prsp_cmd, 64, "\r\n^HEAPINFO: %dB/%dB, %dKB/%dKB", heapInfo.FreeRemainHeapSize, heapInfo.TotalHeapSize, heapInfo.FreeRemainHeapSize/1024, heapInfo.TotalHeapSize/1024);
	}
	return AT_END;
}

