/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "xy_system.h"
#include "xy_rtc.h"
#include "at_cmd_basic.h"
#include "atc_ps.h"
#include "factory_nv.h"
#include "oss_nv.h"
#include "xy_at_api.h"
#include "xy_atc_interface.h"
#include "xy_defwan_api.h"
#include "xy_ps_api.h"
#include "xy_tcpip_api.h"
#include "at_tcpip_api.h"
#include "xy_walltime.h"
#include "at_cmd_basic.h"
#include "oss_nv.h"
#include "factory_nv.h"
#include "softap_nv.h"
#include "net_adapt.h"
#include "app_basic_config.h"


//+CTZEU:<tz>,<dst>,[<utime>]   +CTZEU:+32,0,2019/03/29,21:12:56
void urc_CTZEU_Callback(unsigned long eventId, void *param, int paramLen)
{
	UNUSED_ARG(eventId);
	xy_assert(paramLen == sizeof(ATC_MSG_LOCALTIMEINFO_IND_STRU));
	ATC_MSG_LOCALTIMEINFO_IND_STRU *ctzeu_urc = (ATC_MSG_LOCALTIMEINFO_IND_STRU*)param;

	if(g_softap_fac_nv->test == 3)
		return;

	xy_walltime_t wall_time = {0};
	uint8_t zone = 0;
	uint8_t DayLightTime = 0;

	wall_time.tm_year = 2000 + (ctzeu_urc->stPara.aucUtAndLtz[0] & 0x0F) * 10 + ((ctzeu_urc->stPara.aucUtAndLtz[0] & 0xF0) >> 4);
	wall_time.tm_mon = (ctzeu_urc->stPara.aucUtAndLtz[1] & 0x0F) * 10 + ((ctzeu_urc->stPara.aucUtAndLtz[1] & 0xF0) >> 4);
	wall_time.tm_mday = (ctzeu_urc->stPara.aucUtAndLtz[2] & 0x0F) * 10 + ((ctzeu_urc->stPara.aucUtAndLtz[2] & 0xF0) >> 4);
	wall_time.tm_hour = (ctzeu_urc->stPara.aucUtAndLtz[3] & 0x0F) * 10 + ((ctzeu_urc->stPara.aucUtAndLtz[3] & 0xF0) >> 4);
	wall_time.tm_min = (ctzeu_urc->stPara.aucUtAndLtz[4] & 0x0F) * 10 + ((ctzeu_urc->stPara.aucUtAndLtz[4] & 0xF0) >> 4);
	wall_time.tm_sec = (ctzeu_urc->stPara.aucUtAndLtz[5] & 0x0F) * 10 + ((ctzeu_urc->stPara.aucUtAndLtz[5] & 0xF0) >> 4);

	zone = (ctzeu_urc->stPara.ucLocalTimeZone & 0x07) * 10 + ((ctzeu_urc->stPara.ucLocalTimeZone & 0xF0) >> 4);
	if(ctzeu_urc->stPara.ucNwDayltSavTimFlg == D_ATC_FLAG_TRUE)
	{
		DayLightTime = (ctzeu_urc->stPara.ucNwDayltSavTim & 3) * 4;
	}

	if((ctzeu_urc->stPara.ucLocalTimeZone & 0x08) == 0)
	{
		//zone += DayLightTime;
		g_softap_var_nv->g_zone = zone;
	}
	else
	{
		//zone -= DayLightTime;
		g_softap_var_nv->g_zone = -1 * zone;
	}

	xy_printf(0, PLATFORM_AP, INFO_LOG, "+CTZEU:%d,%d,%d/%d/%d,%d:%d:%d\r\n", zone,DayLightTime,wall_time.tm_year,wall_time.tm_mon,wall_time.tm_mday,wall_time.tm_hour,wall_time.tm_min,wall_time.tm_sec);

	xy_set_GMT_time(&wall_time);
	g_softap_var_nv->CTZE_time_ms = g_softap_var_nv->wall_time_ms;
}

// 获取当前通过网络同步过的最新时间，例如ATTACH、NTP，不受本地计时系统影响
int get_attached_time(xy_walltime_t *walltime)
{
	if (g_softap_var_nv->CTZE_time_ms)
	{
		xy_gmtime(g_softap_var_nv->CTZE_time_ms, walltime);
		return 1;
	}

	xy_printf(0,XYAPP, WARN_LOG, "3GPP not attach!");
	return 0;	
}

/**
  * @brief     获取当前系统本地时区信息，必须保证上电后已经ATTACH成功过，才能正确获取
  */
void get_zone_str(char *zone)
{
	int8_t positive_zone;

	if (g_softap_var_nv->g_zone >= 0)
	{
		positive_zone = g_softap_var_nv->g_zone;
		if (g_softap_var_nv->g_zone >= 10)
			sprintf(zone, "+%2d", positive_zone);
		else
#if VER_CM
			sprintf(zone, "+0%1d", positive_zone);
#else
			sprintf(zone, "+%1d", positive_zone);
#endif
	}
	else
	{
		positive_zone = 0 - g_softap_var_nv->g_zone;
		if (g_softap_var_nv->g_zone <= -10)
			sprintf(zone, "-%2d", positive_zone);
		else
#if VER_CM
		sprintf(zone, "-0%1d", positive_zone);
#else
		sprintf(zone, "-%1d", positive_zone);
#endif
	}
}

/**
 * @brief 用于at命令输出时间格式字符串
 * @param at_type [IN] 0：CCLK; 1：QLTS;
 * @param time_mode [IN] 0：显示当前同步的最新网络时间；1：显示当前GMT时间+时区；2：显示当前本地时间，通过“AT+CTZU=3”设置，或者将g_app_basic_cfg.nitz设为3
 * @param prsp_cmd [OUT] 用于保存at命令输出时间格式字符串
 * @return 1：表示成功
 * @warning 接口内部会申请prsp_cmd所占内存，使用完需自行释放。
 * 			接口内部根据保存到本地的时间快照信息计算时间，若本地无保存，则返回失败
 */
int get_walltime_str(int at_type, int time_mode, char **prsp_cmd)
{
	char zone[4] = {0};
	xy_walltime_t walltime = {0};

	// 输出当前通过网络同步的最新时间
	if (time_mode == 0)
	{
		if (get_attached_time(&walltime) == 0)
			return 0;
	}
	// 输出当前的GMT时间
	else if (time_mode == 1)
	{
		if (xy_get_GMT_time(&walltime) == 0)
			xy_get_RTC_time(&walltime);
	}
	// 输出当前时区时间,通过“AT+CTZU=3”设置，或者将g_app_basic_cfg.nitz设为3
	else
	{
		uint64_t now_msec = xy_get_GMT_time_ms();
		if (now_msec)
	    {
	    	int64_t msec = ((int64_t)now_msec + (int64_t)g_softap_var_nv->g_zone * 15 * 60 * 1000);
	        xy_gmtime((uint64_t)msec, &walltime);
	    }	
		else
		{
			xy_get_RTC_time(&walltime);
		}
	}

	get_zone_str(zone);

	*prsp_cmd = xy_malloc(100);
	//+CCLK: "19/01/13,11:41:23+32"
	if (at_type == 0)
	{
		if (g_app_basic_cfg.nitz == 0)
			snprintf(*prsp_cmd, 100, "\"%02d/%02u/%02u,%02u:%02u:%02u+00\"", walltime.tm_year % 100, walltime.tm_mon, walltime.tm_mday,
					 walltime.tm_hour, walltime.tm_min, walltime.tm_sec);
		else
			snprintf(*prsp_cmd, 100, "\"%02d/%02u/%02u,%02u:%02u:%02u%s\"", walltime.tm_year % 100, walltime.tm_mon, walltime.tm_mday,
					 walltime.tm_hour, walltime.tm_min, walltime.tm_sec, zone);
	}
	//+QLTS: "2019/01/13,03:40:48+32,0"	
	else
	{
		snprintf(*prsp_cmd, 100, "\"%04u/%02u/%02u,%02u:%02u:%02u%s,0\"", walltime.tm_year, walltime.tm_mon, walltime.tm_mday,
					walltime.tm_hour, walltime.tm_min, walltime.tm_sec, zone);
	}

	return 1;
}

//19/03/30,09:28:56+32---->xy_wall_clock+zone
int convert_wall_time(char *data, char *time, xy_walltime_t *wall_time, int *zone_sec)
{
	char *tag;
	char *next_tag;
	uint8_t month_table[13] = {0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

	tag = data;
	next_tag = strchr(tag, '/');
	if (next_tag == NULL)
		return XY_ERR;
	*next_tag = '\0';

	int temp_tag = (int)strtol(tag,NULL,10);
	if (temp_tag >= 0 && temp_tag < 70)
		wall_time->tm_year = 2000 + temp_tag;
	else if (temp_tag >= 70 && temp_tag <= 99)
		wall_time->tm_year = 1900 + temp_tag;
	else
		return XY_ERR;

	//闰年
	if ((0 == wall_time->tm_year % 4 && 0 != wall_time->tm_year % 100) || (0 == wall_time->tm_year % 400))
		month_table[2] = 29;
	
	tag = next_tag + 1;
	next_tag = strchr(tag, '/');
	if (next_tag == NULL)
		return XY_ERR;
	*next_tag = '\0';
	wall_time->tm_mon = (int)strtol(tag,NULL,10);

	if (wall_time->tm_mon > 12 || wall_time->tm_mon <= 0)
		return XY_ERR;

	tag = next_tag + 1;
	wall_time->tm_mday = (int)strtol(tag,NULL,10);

	if (wall_time->tm_mday <= 0 || wall_time->tm_mday > month_table[wall_time->tm_mon])
		return XY_ERR;

	tag = time;
	next_tag = strchr(tag, ':');
	if (next_tag == NULL)
		return XY_ERR;
	*next_tag = '\0';
	wall_time->tm_hour = (int)strtol(tag,NULL,10);

	if (wall_time->tm_hour >= 24)
		return XY_ERR;

	tag = next_tag + 1;
	next_tag = strchr(tag, ':');
	if (next_tag == NULL)
		return XY_ERR;
	*next_tag = '\0';
	wall_time->tm_min = (int)strtol(tag,NULL,10);
	if (wall_time->tm_min >= 60)
		return XY_ERR;

	tag = next_tag + 1;
	//+zone
	if (((next_tag = strchr(tag, '+')) != NULL) || ((next_tag = strchr(tag, '-')) != NULL))
	{
		char zone[4] = {0};

		memcpy(zone, next_tag, 3);

		if (next_tag[0] == '+')
		{
            if ((int)strtol(next_tag + 1, NULL, 10) > 96)
                return XY_ERR;

            *zone_sec = ((int)strtol(next_tag + 1, NULL, 10)) * 15 * 60;
        }
        else if (next_tag[0] == '-')
        {
            if ((int)strtol(next_tag + 1, NULL, 10) > 96)
                return XY_ERR;

            *zone_sec = 0 - ((int)strtol(next_tag + 1, NULL, 10)) * 15 * 60;

        }

        *next_tag = '\0';
        wall_time->tm_sec = (int)strtol(tag, NULL, 10);
        if (wall_time->tm_sec >= 60)
            return XY_ERR;

        g_softap_var_nv->g_zone = (int)strtol(zone, NULL, 10);
    }
	//not +zone
	else
	{
		return XY_ERR;
	}

	return XY_OK;
}




//AT+CCLK=<yy/MM/dd,hh:mm:ss>[<±zz>]    as 19/03/30,09:28:56+32
//AT+CCLK?   +CCLK:[<yy/MM/dd,hh:mm:ss>[<±zz>]]   +CCLK: "19/01/13,11:41:23+32"
int at_CCLK_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_REQ)
	{
		char date[16] = {0};
		char time[16] = {0};
		int zone_sec = 0;
		xy_walltime_t wall_time = {0};
		if(*at_buf == '"')
		{
			char *end_quot = NULL;
			at_buf++;
			if((end_quot = strchr(at_buf, '"')) == NULL)
			{
				*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
				return AT_END;
			}
			else
				end_quot = 0;
		}
		if (at_parse_param("%s,%s", at_buf, date, time) == XY_OK)
		{
            if (date[0] == 0 || time[0] == 0)
            {
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
                return AT_END;
            }

            if (convert_wall_time(date, time, &wall_time, &zone_sec) == XY_OK)
                xy_set_GMT_time(&wall_time);
            else
            {
                *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            }
        }
		else
        {
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        }
	}
	else if (g_req_type == AT_CMD_QUERY) /*若想获取北京时间，通过“AT+CTZU=3”设置，或者将g_app_basic_cfg.nitz设为3*/
	{
		int time_mode;
		time_mode = (g_app_basic_cfg.nitz == 3) ? 2 : 1;

		/*当前本地时间+CCLK: "23/06/13,03:41:23+32"	*/
		if (!get_walltime_str(0, time_mode, prsp_cmd))
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_NotAllowed);
	}
    else if (g_req_type == AT_CMD_ACTIVE)
    {
		*prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
	return AT_END;
}

#if VER_QUEC

//AT+QLTS=<mode>
int at_QLTS_req(char *at_buf, char **prsp_cmd)
{
	if (g_req_type == AT_CMD_REQ)
	{
		int ret = 0;
		int mode = 0;

		if (at_parse_param("%d(0-2)", at_buf, &mode) != XY_OK)
		{
            *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
            return AT_END;
        }

		//当前最新网络时间+QLTS: "2019/01/13,03:40:48+32,0"		
		if (mode == 0) 
			ret = get_walltime_str(1, 0, prsp_cmd);
		//当前 GMT 时间+QLTS: "2019/01/13,03:41:22+32,0"		
		else if (mode == 1) 
			ret = get_walltime_str(1, 1, prsp_cmd);
		//当前北京时间+QLTS: "2019/01/13,11:41:23+32,0"		
		else if (mode == 2) 
			ret = get_walltime_str(1, 2, prsp_cmd);
		// 若时间未通过网络同步，执行该命令后返回不带时间的字符串+QLTS: ""
		if (!ret)
		{
			*prsp_cmd = xy_malloc(25);
			snprintf(*prsp_cmd, 25, "\"\"");
		}
	}
	//当前 GMT 时间+QLTS: "2019/01/13,03:40:48+32,0"	
	else if (g_req_type == AT_CMD_ACTIVE)
	{
		if (!get_walltime_str(1, 0, prsp_cmd))
		{
			*prsp_cmd = xy_malloc(25);
			snprintf(*prsp_cmd, 25, "\"\"");
		}
	}
#if (!AT_TEST_OFF)
	else if (g_req_type == AT_CMD_TEST)
	{
		*prsp_cmd = xy_malloc(32);
		snprintf(*prsp_cmd, 32, "(0-2)");
	}
#endif
	else
	{
         *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
	
	return AT_END;
}

/* AT+QNTP==<contextID>,<server>[,<port>[,<autosettime>]] */
int at_QNTP_req(char *at_buf, char **prsp_cmd)
{
    int ret = XY_OK;
    if (g_req_type == AT_CMD_REQ)
	{
		ntp_query_param_t param = {0};

		param.atHandle = get_current_ttyFd();
		param.update = 1;
		param.retry_cnt = g_app_basic_cfg.ntp_cnt;
		param.retry_timeout = g_app_basic_cfg.ntp_interval;

		if (at_parse_param("%1d,%p(),%2d[1-65535],%1d[0-1]", at_buf, &param.contextID, &param.host, &param.port, &param.update) != XY_OK)
        {
            *prsp_cmd = AT_TCPIP_ERR_BUILD(XY_Err_Parameter);
            return AT_END;
        }

		if (!is_Ps_Cid_Vaild(param.contextID))
		{
			*prsp_cmd = AT_TCPIP_ERR_BUILD(XY_Err_Parameter);
			return AT_END;
		}

		ret = at_query_ntp_async(&param);
        *prsp_cmd = AT_TCPIP_ERR_BUILD(ret);
	}
	else if (g_req_type == AT_CMD_QUERY)
	{
		uint16_t sntp_port = 0;
		char *sntp_server = xy_malloc(DNS_MAX_NAME_LENGTH);
		// 若在同步时间过程中，则返回：+QNTP: <server>,<port>
		if (has_sntp_in_use(sntp_server, &sntp_port))
		{
			*prsp_cmd = xy_malloc(128);	
        	snprintf(*prsp_cmd, 128, "\"%s\",%d", sntp_server, sntp_port);
		}
		xy_free(sntp_server);
		set_at_tcpip_err(XY_OK);
	}
#if (!AT_TEST_OFF)
	else if (g_req_type == AT_CMD_TEST)
	{
        *prsp_cmd = xy_malloc(60);
        snprintf(*prsp_cmd, 60, "(%d-%d),<server>,(1-65535),(0,1)",CID_MIN_VAL, CID_MAX_VAL);
		set_at_tcpip_err(XY_OK);
	}
#endif
	else
	{
        *prsp_cmd = AT_TCPIP_ERR_BUILD(XY_Err_Parameter);
    }

	return AT_END;
}
#endif
  