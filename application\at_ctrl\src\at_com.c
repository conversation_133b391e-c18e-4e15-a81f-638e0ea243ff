/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "at_com.h"
#include "at_ctl.h"
#include "at_utils.h"
#include "oss_nv.h"
#include "xy_utils.h"
#include "xy_ftl.h"
#include "usb_api.h"
#include "softap_nv.h"
#include "xy_system.h"
#include "xy_fs.h"

/*******************************************************************************
 *                             Type definitions                                *
 ******************************************************************************/
typedef enum
{
	FMT_INVALID = 0,	//非法解析，%l、%h、%s的个数不合理
	FMT_NORMAL,			//不含%l格式
	FMT_POINT,		//解析的数据为明文字符串，解析格式为%l+%s,需判断input_len == data_len
	FMT_HEX,		//解析的数据为16进制hex码，解析格式为%l+%h,需判断input_len * 2 == data_len
}fmt_type_t;

/*该结构体仅仅是为了解析字符串类型参数后，进行长度合法性检错而设计的，没有功能上的价值*/
typedef struct
{
	int input_len;			//AT输入的数据长度参数解析后的赋值，即%l所对应的那个参数值；
	int data_len;			//AT命令中字符串参数最终的有效内容长度，即对应%s或%h解析后字符串有效长度值
	uint8_t point_flag;		//用于判断解析的参数类型是否为%p，即地址指针
	fmt_type_t data_type;	//用于识别%l的捆绑关系 参考@fmt_type_t
}parse_data_t;

/*整形变量取值范围 - 或 | 的解析结果结构体*/
typedef struct
{	uint8_t range_type;		//0：未定义范围；1：定义了上下限；2：定义了有效值枚举
	uint32_t min_limit;
	uint32_t max_limit;
	char *valid_value_start;
}param_range_t;

/*******************************************************************************
 *						   Global variable definitions						   *
 ******************************************************************************/
at_config_t g_at_config = {0};

/*******************************************************************************
 *						Local function implementations 					       *
 ******************************************************************************/
/*获取特殊字符一共有多少个*/
uint8_t get_chr_num_in_str(char *str, char chr)
{
	uint8_t chr_num = 0;
	char *chr_addr = NULL;
	while(*str != '\0')
	{
		if((chr_addr = strchr(str, chr)) != NULL)
		{
			chr_num++;
			str = chr_addr + 1;
		}
		else
			break;
	}
	return chr_num;
}

static bool is_param_in_valid_range(uint32_t  val_trans, param_range_t *param_range)
{
	bool ret = true;

	if((param_range->range_type == 1 && (int)val_trans < 0) || val_trans < param_range->min_limit || val_trans > param_range->max_limit)
		ret = false;
	else if(param_range->range_type ==2) 
	{
		uint32_t valid_value = 0;
		char *value_head = param_range->valid_value_start;
		char *value_end = strchr(value_head, '|');
		while(1)
		{
			valid_value = (int)strtol(value_head,NULL,0);
			if(val_trans == valid_value)
				break;
			else if(value_end != NULL)
			{
				value_head = value_end + 1; 
				value_end = strchr(value_head, '|');
			}
			else
			{
				ret = false;
				break;
			}
		}
	}
	return ret;
}

/* 获取%d后面() []的int型参数范围 */
static void get_val_valid_range(char *fmt, char *required_flag, char *type, int *size, param_range_t *range)
{
	char len_str[6] = {0};
	char *left_bracket = NULL;
	char *right_bracket = NULL;
	char *left_square_bracket = NULL;
	char *right_square_bracket = NULL;
	char *mid_line = NULL;
	uint8_t break_num = 0;		//括号内参数合法值分隔符'|'的个数

	if (strlen(fmt) == 4 && (strstr(fmt, "lld") || strstr(fmt, "llu")))
	{
		*type = 'm';
		return;
	}
	else if(strlen(fmt) > 2)
	{
		left_bracket = strchr(fmt, '(');
		right_bracket = strchr(fmt, ')');
		left_square_bracket = strchr(fmt, '[');
		right_square_bracket = strchr(fmt, ']');
		if((left_bracket == NULL && right_bracket == NULL) && (left_square_bracket == NULL && right_square_bracket== NULL))
		{
			*type = *(fmt + strlen(fmt) - 1);
			strncpy(len_str, fmt + 1, strlen(fmt) - 2);
			*size = (int)strtol(len_str,NULL,10);
		}
		else if(((left_bracket != NULL && right_bracket != NULL) && (left_square_bracket == NULL && right_square_bracket== NULL)) || \
			((left_bracket == NULL && right_bracket == NULL) && (left_square_bracket != NULL && right_square_bracket != NULL)))
		{
			if(left_bracket !=NULL)
			{
				*required_flag = 1;
			}
			else
			{
				left_bracket = left_square_bracket;
				right_bracket = right_square_bracket;
			}
			*left_bracket = '\0';
			*type = *(fmt + strlen(fmt) - 1);
			mid_line = strchr(left_bracket + 1, '-');
			break_num = get_chr_num_in_str(left_bracket + 1, '|');

			xy_assert(left_bracket < right_bracket);
			if(*type != 'd' && *type != 'D' && *type != 'c' && *type != 'C' && *type != 'l' && *type != 'L')	//对于%s、%h、%p类型的参数只能输()或[]表示必选和可选，括号中间不能有其他字符
				xy_assert((left_bracket + 1) == right_bracket);	

			if(mid_line != NULL && break_num == 0)			//定义了上下限,例如(0-100),[2-10]
			{
				range->range_type = 1;
				xy_assert(mid_line < right_bracket);
				*right_bracket = '\0';
				range->min_limit = (int)strtol(left_bracket + 1,NULL,0);		//min_limit至少为0
				if(right_bracket - mid_line > 1)
					range->max_limit = (int)strtol(mid_line + 1,NULL,0);		//不输上限时，max_limit为0x0fffffff,-)间必须输有数字，否则max_limit解析为0

				if(range->min_limit > range->max_limit)
				{
					xy_assert(0);
				}
			}
			else if(mid_line == NULL && break_num != 0)		//定义了合法值枚举，例如（0|1|2|4）,[3|5|7]
			{
				range->range_type = 2;
				range->valid_value_start = left_bracket + 1;
			}
			else
			{
				xy_assert((left_bracket + 1) == right_bracket);			//%d()表示必选参数没有范围限制，括号之间不能有任何字符
			}

			if(strlen(fmt) > 2)
			{
				strncpy(len_str, fmt + 1, strlen(fmt) - 2);
				*size = (int)strtol(len_str,NULL,10);
			}
		}
		else
		{
			xy_assert(0);
		}
	}
	else
	{
		*type = *(fmt + strlen(fmt) - 1);
	}
}

/*str:某参数字符串; fmt:参数格式字符串; nullstring:输入参数为带双引号的空字符；at_data:记录%p%l特殊参数解析结果，以便检查报错，没有特殊功能用途; arg:无意义;flag:无意义;ap:解析后参数值的空间数组链表*/
static int parse_type_val(char *str, char *fmt, int nullstring, parse_data_t *at_data, int* arg, int flag, va_list *ap)
{
	int ret = XY_OK;
	int size = 0;
	char type = 0;
	char required_flag = 0;
	param_range_t param_range = {0};
	param_range.max_limit = 0xffffffff;
	uint32_t val_trans = 0;

    /*fmt为空，跳过某参数解析，ap指针无需变更*/
    if (strlen(fmt)==0)
    {
		return ret;
	}
	else
	{
		get_val_valid_range(fmt, &required_flag, &type, &size, &param_range);

		/*源AT命令字符串中未携带该参数具体值，则识别是否为必选参数*/
		if (strlen(str) == 0)
		{
			if (nullstring == 0) /*源AT命令没有输入相应参数,所有类型都要判断是否为必选参数*/
			{
				if (required_flag == 1)				/*必选参数报错*/
					return XY_Err_Parameter;

				va_arg(*ap, uintptr_t); 			/*可选参数跳过，参数解析目标空间链表，跳过该参数空间*/
				return ret;
			}
			else /*源AT命令输入的参数为""，但两边的""已被去除，此时传入的str为'\0'，对于%s类型该输入有效，无需判断是否为必选*/
			{
				if (type != 's' && type != 'S') /*解析类型非%s，根据是否为必选参数报错或跳过*/
				{
					if (required_flag == 1) /*必选参数报错*/
						return XY_Err_Parameter;
					va_arg(*ap, uintptr_t);
				}
				else/*%s参数，输入""时直接赋'\0'*/
				{
					*(char *)(va_arg(*ap, uintptr_t)) = '\0';
				}
				return ret;
			}
		}
	}

	at_data->point_flag = 0;

	if(type == 'd' || type == 'D' || type == 'c' || type == 'C' || type == 'l' || type == 'L')
	{
		/*char type uint8_t*/
		if(type == 'c' || type == 'C')
			size = 1;
		
        if (!is_digit_str(str) && !is_hex_str(str, 1))
        {
			ret = XY_Err_Parameter;
		}
		else if (size == 0 || size == 4)
		{
            *((uint32_t *)(va_arg(*ap, uintptr_t))) = (uint32_t)strtoul(str, NULL, 0);
            val_trans = (uint32_t)strtoul(str, NULL, 0);
		}
		else if (size == 1)
		{
            *((uint8_t *)(va_arg(*ap, uintptr_t))) = (uint8_t)strtoul(str, NULL, 0);
            val_trans = (uint32_t)strtoul(str,NULL,0);

			if(param_range.range_type == 1 && param_range.max_limit > (unsigned int)0xff)
			{
				param_range.max_limit = 0xff;
			}
		}
		else if (size == 2)
		{
            *((uint16_t *)(va_arg(*ap, uintptr_t))) = (uint16_t)strtoul(str, NULL, 0);
            val_trans = (uint32_t)strtoul(str, NULL, 0);

			if(param_range.range_type == 1 && param_range.max_limit> (unsigned int)0xffff)
			{
				param_range.max_limit = 0xffff;
			}
		}
		else
		{
			xy_assert(0);
		}

        if (type == 'l' || type == 'L')
        {
			at_data->input_len = val_trans;
		}
        if (is_param_in_valid_range(val_trans, &param_range) == false)
            ret = XY_Err_Parameter;
	}
	/* %m : 64位整形解析*/
	else if (type == 'm' || type == 'M')
	{
		/* 64位数值解析 */
		*((uint64_t *)(va_arg(*ap, uintptr_t))) = (uint64_t)strtoull(str, NULL, 0);
	}
	/* %s : 可打印字符串解析*/
	else if (type == 's' || type == 'S')
	{
		if (size == 0)
		{
			if (flag == AT_PARAM_PARSE_ESC)
			{
				parase_esc_string(str);
			}
			strcpy((char *)(va_arg(*ap, uintptr_t)), str);
		}
		else
		{
			if (size <= (int)(strlen(str)))
			{
				ret = XY_Err_Parameter;
			}
			else
			{
				if (flag == AT_PARAM_PARSE_ESC)
				{
					parase_esc_string(str);
				}
				int cpy_len = (int)(strlen(str));
				char *var_dst = (char *)(va_arg(*ap, uintptr_t));
				strncpy(var_dst, str, cpy_len);
				*(char *)(var_dst + cpy_len) = '\0';
			}
		}
	}
	/* %p : 可打印的长字符串地址获取，少一次内存拷贝*/
	else if ((type == 'p' || type == 'P'))
	{
		at_data->point_flag = 1;
		if(at_data->data_type == FMT_POINT)
		{
			at_data->data_len = strlen(str);
		}

		if(size != 0 && size <= (int)strlen(str))
		{
			ret = XY_Err_Parameter;
			return ret;
		}

        *((uintptr_t *)(va_arg(*ap, uintptr_t))) = (uintptr_t)str;
	}
	/* %h: 码流的16进制字符串形式，转换成二进制码流；一般搭配%l使用，需要检查长度合法性*/
	else if(type == 'h' || type == 'H')
	{
		at_data->data_len = strlen(str);
		if(at_data->data_len % 2 != 0)
		{
			ret = XY_Err_Parameter;
			return ret;
		}

		if(at_data->data_type != FMT_HEX)	//没有%l时，必须为类似%32h带长度限制的格式，不带长度则断言，此时入参长度必须为固定值32，否则报错
		{
			xy_assert(size != 0);
			if(at_data->data_len != size)
			{
				ret = XY_Err_Parameter;
				return ret;
			}
		}

		if(hexstr2bytes(str, at_data->data_len, (char *)(va_arg(*ap, uintptr_t)), at_data->data_len/2) == -1)
			ret = XY_Err_Parameter;
	}
	/* %f : 浮点型参数解析*/
	else if(type == 'f' || type == 'F')
	{
		*((double *)(va_arg(*ap, uintptr_t))) = strtod(str, NULL);
	}
	else
	{
		xy_assert(0);
		return ret;
	}

	if (ret == XY_OK && flag == AT_PARAM_PARSE_ESC && arg != NULL)
	{
		*arg += 1;
	}

	return ret;
}

void Reset_AT_Config_Exclude()
{
    /* 仅用于AT&V显示，不参与任何NV的保存和重置，目前代码写死，可根据需求调整 */
    g_at_config.ats[6] = 2;
    g_at_config.ats[7] = 0;
    g_at_config.ats[8] = 2;
    g_at_config.ats[10] = 15;
    g_at_config.at_f = 0;
    g_at_config.atz = 0;
}

void Restore_AT_Config_From_WorkingNV()
{
    g_at_config.ate = g_softap_fac_nv->ate;
    g_at_config.ats[0] = g_softap_fac_nv->ats0;
    g_at_config.ats[3] = g_softap_fac_nv->ats3;
    g_at_config.ats[4] = g_softap_fac_nv->ats4;
    g_at_config.ats[5] = g_softap_fac_nv->ats5;
    g_at_config.atq = g_softap_fac_nv->atq;
    g_at_config.atv = g_softap_fac_nv->atv;
    g_at_config.atx = g_softap_fac_nv->atx;

    g_at_config.at_c = ((g_softap_fac_nv->lpuart_dcd_pin & 0x3F) <= 56) ? (g_softap_fac_nv->lpuart_dcd_pin >> 6) : 2;
    g_at_config.at_d = ((g_softap_fac_nv->lpuart_dtr_pin & 0x3F) <= 56) ? (g_softap_fac_nv->lpuart_dtr_pin >> 6) : 0;
    g_at_config.dcebydte = ((g_softap_fac_nv->lpuart_rts_pin & 0x7F) <= 56) ? (g_softap_fac_nv->lpuart_rts_pin >> 7) : 0;
    g_at_config.dtebydce = ((g_softap_fac_nv->lpuart_cts_pin & 0x7F) <= 56) ? (g_softap_fac_nv->lpuart_cts_pin >> 7) : 0;

    Reset_AT_Config_Exclude();
}

void Restore_AT_Config_From_FacNV()
{
    /* 获取原始出厂NV数值，不会影响工作态NV AT&F命令不会重置当前波特率 */
    #define ORIFAC_NV_VAL(param, type)  (type)(*(type *)(NV_MAIN_SOFTAP_FACTORY_BASE + 4 + OFFSET_FAC_PARAM(param)))

    g_at_config.ate = ORIFAC_NV_VAL(ate, uint8_t);
    g_at_config.atq = ORIFAC_NV_VAL(atq, uint8_t);
    g_at_config.atv = ORIFAC_NV_VAL(atv, uint8_t);
    g_at_config.atx = ORIFAC_NV_VAL(atx, uint8_t);
    g_at_config.ats[0] = ORIFAC_NV_VAL(ats0, uint8_t);
    g_at_config.ats[3] = ORIFAC_NV_VAL(ats3, uint8_t);
    g_at_config.ats[4] = ORIFAC_NV_VAL(ats4, uint8_t);
    g_at_config.ats[5] = ORIFAC_NV_VAL(ats5, uint8_t);

    uint8_t lpuart_dcd_pin = ORIFAC_NV_VAL(lpuart_dcd_pin, uint8_t);
    g_at_config.at_c = ((lpuart_dcd_pin & 0x3F) <= 56) ? (lpuart_dcd_pin >> 6) : 2;

    uint8_t lpuart_dtr_pin = ORIFAC_NV_VAL(lpuart_dtr_pin, uint8_t);
    g_at_config.at_d = ((lpuart_dtr_pin & 0x3F) <= 56) ? (lpuart_dtr_pin >> 6) : 0;

    uint8_t lpuart_rts_pin = ORIFAC_NV_VAL(lpuart_rts_pin, uint8_t);
    g_at_config.dcebydte = ((lpuart_rts_pin & 0x7F) <= 56) ? (lpuart_rts_pin >> 7) : 0;

    uint8_t lpuart_cts_pin = ORIFAC_NV_VAL(lpuart_cts_pin, uint8_t);
    g_at_config.dtebydce = ((lpuart_cts_pin & 0x7F) <= 56) ? (lpuart_cts_pin >> 7) : 0;

    Reset_AT_Config_Exclude();
}

/*******************************************************************************
 *                       Global function implementations	                   *
 ******************************************************************************/
int set_ate_mode(uint8_t mode)
{
    if (mode != 0 && mode != 1)
        return XY_Err_Parameter;
    g_at_config.ate = mode;
    return XY_OK;
}

uint8_t get_ate_mode()
{
	return g_at_config.ate;
}

int set_atq_mode(uint8_t mode)
{
    if (mode != 0 && mode != 1)
        return XY_Err_Parameter;
    g_at_config.atq = mode;
    return XY_OK;    
}

uint8_t get_atq_mode()
{
	return g_at_config.atq;
}

int set_atv_mode(uint8_t mode)
{
    if (mode != 0 && mode != 1)
        return XY_Err_Parameter;
    g_at_config.atv = mode;
    return XY_OK;    
}

uint8_t get_atv_mode()
{
	return g_at_config.atv;
}

int set_ats_val(uint8_t regId, uint8_t val)
{
    if (regId >= ATS_ID_MAX)
        return XY_Err_Parameter;

    g_at_config.ats[regId] = val;
    return XY_OK;
}

uint8_t get_ats_val(uint8_t regId)
{
    if (regId >= ATS_ID_MAX)
    {
        xy_assert(0);
    }

    return g_at_config.ats[regId];
}

int do_at_and_w(void)
{
    /* AT&W仅保留部分参数 */
    g_softap_fac_nv->ats0 = g_at_config.ats[0];
    g_softap_fac_nv->ate = g_at_config.ate;
    g_softap_fac_nv->atq = g_at_config.atq;
    g_softap_fac_nv->atv = g_at_config.atv;
    g_softap_fac_nv->atx = g_at_config.atx;
    g_softap_fac_nv->lpuart_dcd_pin = (g_at_config.at_c << 6) | (g_softap_fac_nv->lpuart_dcd_pin & 0x3F);
    g_softap_fac_nv->lpuart_dtr_pin = (g_at_config.at_d << 6) | (g_softap_fac_nv->lpuart_dtr_pin & 0x3F);
    g_softap_fac_nv->lpuart_rts_pin = (g_at_config.dcebydte << 7) | (g_softap_fac_nv->lpuart_rts_pin & 0x7F);
    g_softap_fac_nv->lpuart_cts_pin = (g_at_config.dtebydce << 7) | (g_softap_fac_nv->lpuart_cts_pin & 0x7F);
  
#if USR_CUSTOM8 || USR_CUSTOM14
	g_softap_fac_nv->at_uart_rate = 48; /*115200*/
#else /* 波特率可通过AT&W进行保存，但不能通过AT&F/ATZ进行恢复 */
	g_softap_fac_nv->at_uart_rate = g_softap_var_nv->at_ipr & 0x8000 ? 0 : g_softap_var_nv->at_ipr;
#endif
	xy_fremove(BSP_VAR_NV_FS);
    /* 保存到工作态NV */
    SAVE_SOFTAP_FAC();   
    return XY_OK;
}

int do_at_and_f(void)
{
    /* AT&F 恢复出厂设置 */
    Restore_AT_Config_From_FacNV();
    return XY_OK;
}

int do_at_z(void)
{
    /* ATZ仅从工作态NV中恢复部分配置 */
    g_at_config.ate = g_softap_fac_nv->ate;
    g_at_config.atq = g_softap_fac_nv->atq;
    g_at_config.atv = g_softap_fac_nv->atv;
    g_at_config.atx = g_softap_fac_nv->atx;
    g_at_config.at_c = ((g_softap_fac_nv->lpuart_dcd_pin & 0x3F) <= 56) ? (g_softap_fac_nv->lpuart_dcd_pin >> 6) : 2;
    g_at_config.at_d = ((g_softap_fac_nv->lpuart_dtr_pin & 0x3F) <= 56) ? (g_softap_fac_nv->lpuart_dtr_pin >> 6) : 0;
    g_at_config.dcebydte = ((g_softap_fac_nv->lpuart_rts_pin & 0x7F) <= 56) ? (g_softap_fac_nv->lpuart_rts_pin >> 7) : 0;
    g_at_config.dtebydce = ((g_softap_fac_nv->lpuart_cts_pin & 0x7F) <= 56) ? (g_softap_fac_nv->lpuart_cts_pin >> 7) : 0;
    g_at_config.ats[0] = g_softap_fac_nv->ats0;
    return XY_OK;
}

int do_at_and_v(char* prsp)
{
    if (prsp == NULL)
        return XY_Err_Parameter;

    sprintf(prsp, "\r\n&C: %d\r\n&D: %d\r\n&F: %d\r\n&W: %d\r\nE: %d\r\nQ: %d\r\nV: %d\r\nX: %d\r\nZ: %d\r\nS0: %d\r\nS3: %d\r\nS4: %d\r\nS5: %d\r\nS6: %d\r\nS7: %d\r\nS8: %d\r\nS10: %d",
            g_at_config.at_c, g_at_config.at_d, g_at_config.at_f, g_at_config.at_w,
            g_at_config.ate, g_at_config.atq, g_at_config.atv, g_at_config.atx, 
            g_at_config.atz, g_at_config.ats[0], g_at_config.ats[3], g_at_config.ats[4], 
            g_at_config.ats[5], g_at_config.ats[6], g_at_config.ats[7], g_at_config.ats[8], 
            g_at_config.ats[10]);

    return XY_OK;
}

//获取的前缀at_prefix里不包含头尾标识，即'+''*''=''?';最终结果为“NRB”"WORKLOCK""ATI""AT"等
char *at_get_prefix_and_param(char *at_cmd, char *at_prefix, uint8_t *type)
{
    char *head;
	char *end;
	char *param = NULL;
	uint8_t atcmd_type = AT_CMD_INVALID;
	int at_prefix_len = 0; 

	memset(at_prefix, 0, AT_CMD_PREFIX);
	while (*at_cmd == '\r' || *at_cmd == '\n')
		at_cmd++;
	
	/* 必须AT打头 */
    if (at_strncasecmp(at_cmd, "AT"))
    {
		char *temp = at_cmd + 2;

		/* eg: AT+ AT& AT* AT# ... */
		if (IS_HEAD_TAG(*temp))
		{
			head = temp + 1;
			if ((end = strchr(head, '=')) != NULL && *(end+1) == '?' && *(end+2) == '\r')
			{
				atcmd_type = AT_CMD_TEST;
				param = end + 1;
			}
			else if((end = strchr(head, '?')) != NULL && *(end+1) == '\r')
			{
				atcmd_type = AT_CMD_QUERY;
				param = end + 1;
			}
			else if((end = strchr(head, '=')) != NULL)
			{
				atcmd_type = AT_CMD_REQ;
				param = end + 1;		
			}
			else if ((end = strchr(head, '\r')) != NULL)
			{
				atcmd_type = AT_CMD_ACTIVE;
				if (*temp == '&' && isalpha((int)(*head)) && isdigit((int)(*(head + 1))))
				{
					/* AT&<commamd><param>，例如AT&W0， command字符长度必须为1且param必须为数字字符 */
					param = head + 1;
					end = end - 1;
				}
				else
				{
					param = end;
				}
			}
			else
			{
				xy_assert(0);
			}
                
            at_prefix_len = end - head;
			if(at_prefix_len < AT_CMD_PREFIX)
			{
				strncpy(at_prefix, head, at_prefix_len);
			}
			else
			{
				param = NULL;
                xy_printf(0, PLATFORM_AP, WARN_LOG, "[at_get_prefix_and_param]error in line:%d!!!", __LINE__);
            }
		}
        else if (isalpha((int)(*temp)))
        {
        	/* eg: ATS24=<val> */
            if ((*temp == 'S' || *temp == 's') && isdigit((int)(*(temp + 1))))
            {
                char *tmp = NULL;
                if ((tmp = strchr(temp, '?')) != NULL)
                {
                    at_prefix_len = tmp - at_cmd;
                    if (at_prefix_len >= AT_CMD_PREFIX)
                    {
                        xy_printf(0, PLATFORM_AP, WARN_LOG, "[at_get_prefix_and_param]error in line:%d!!!", __LINE__);
                        goto prefixParseEnd;
                    }
                    strncpy(at_prefix, at_cmd, at_prefix_len);
                    param = tmp + 1;
                    atcmd_type = AT_CMD_QUERY;
                }
                else if ((tmp = strchr(temp, '=')) != NULL)
                {
                    at_prefix_len = tmp - at_cmd;
                    if (at_prefix_len >= AT_CMD_PREFIX)
                    {
                        xy_printf(0, PLATFORM_AP, WARN_LOG, "[at_get_prefix_and_param]error in line:%d!!!", __LINE__);
                        goto prefixParseEnd;
                    }
                    strncpy(at_prefix, at_cmd, at_prefix_len);
                    param = tmp + 1;
                    atcmd_type = AT_CMD_REQ;
                }
				else
				{
					/* eg: ATS2K ATSDEF */
					atcmd_type = AT_CMD_ACTIVE;
					at_prefix_len = 3;
					strncpy(at_prefix, at_cmd, at_prefix_len);
					param = at_cmd + at_prefix_len;
				}
            }
            else  /* eg: ATD*98  ATE1 */
            {
				atcmd_type = AT_CMD_ACTIVE;
                at_prefix_len = 3;
                strncpy(at_prefix, at_cmd, at_prefix_len);
                param = at_cmd + at_prefix_len;
            }
		}
		else if (*temp == '\r')  /*eg: AT\r */
		{
            atcmd_type = AT_CMD_ACTIVE;
            at_prefix_len = 2;
			strncpy(at_prefix, at_cmd, at_prefix_len);
			param = at_cmd + at_prefix_len;
		}
		else if (isdigit((int)(*temp)))	/* eg: AT2 AT2ZZ */
		{		
			atcmd_type = AT_CMD_ACTIVE;
			at_prefix_len = strlen(temp) - 1;
			if (at_prefix_len < AT_CMD_PREFIX)
			{
				strncpy(at_prefix, temp, at_prefix_len);
			}
			param = NULL;
		}
		else 
		{
			param = NULL;
		}

		if (type != NULL)
			*type = atcmd_type;
	}
	else
	{
		param = NULL;
	}

prefixParseEnd:    
	return param;
}

/*用于对携带%l的格式化字符串进行类别判断*/
fmt_type_t get_fmt_type(char *fmt_param)
{
	uint8_t lenfmt_num = get_chr_num_in_str(fmt_param, 'l') + get_chr_num_in_str(fmt_param, 'L');
	uint8_t hexfmt_num = get_chr_num_in_str(fmt_param, 'h') + get_chr_num_in_str(fmt_param, 'H');
	uint8_t pointfmt_num = get_chr_num_in_str(fmt_param, 'p') + get_chr_num_in_str(fmt_param, 'P');

	if(lenfmt_num == 0)
	{
		return FMT_NORMAL;
	}
	else if(lenfmt_num == 1)
	{
		if(hexfmt_num == 1)
		{
			return FMT_HEX;
		}
		else if(hexfmt_num == 0 && pointfmt_num == 1)
		{
			return FMT_POINT;
		}
	}

	return FMT_INVALID;
}

//参数被“”包围的时候，从'“'的下一个字符开始，找与之匹配的'”'
char *get_quote_end(char *quote_next)
{
	int quote_num = 1;
	char *data = quote_next;
	char *tail = quote_next;

	while ((tail = strchr(data, '"')) != NULL)
	{
		quote_num++;
		if (quote_num % 2 == 0 && (*(tail + 1) == ',' || *(tail + 1) == '\0'))
			return tail;
		data = tail + 1;
	}
	return NULL;
}

/*该接口仅内部函数调用，参数解析以可变入参方式提供，类似scanf.目前next_param参数未使用，填NULL*/
int parse_param(char *fmt_parm, char *buf,char **next_param,int* arg, int flag, va_list *ap)
{
	xy_assert(fmt_parm != NULL && buf != NULL);
	int ret = XY_OK;
	char *param_comma = NULL;
	char *param_quotes_end = NULL;
	char *fmt_comma = NULL;
	char *param_end = NULL;
	int fmt_len = strlen(fmt_parm);
	char *fmt_original = xy_malloc(fmt_len + 1);
	*(fmt_original + fmt_len) = '\0';
	char *fmt = fmt_original; 
	strncpy(fmt, fmt_parm, fmt_len);
	parse_data_t at_data = {0};
	int null_string = 0;

	at_data.data_type = get_fmt_type(fmt);
	if(at_data.data_type == FMT_INVALID)
	{
		ret = XY_Err_Parameter;
		goto END;
	}

	while (*buf == ' ')
		buf++;

	param_end = strchr(buf, '\r');
	if (param_end != NULL)
		*param_end = '\0';

	while (*buf != '\0' || *fmt != '\0')
	{
		/*跳过参数头部空格，%s字符串类型若头部有空格，需用""圈定*/
		while(*fmt == ' ')
		{
			fmt++;
		}
		while(*buf == ' ')
		{
			buf++;
		}
	
		param_comma = strchr(buf, ',');
		fmt_comma = strchr(fmt, ',');
		param_quotes_end = NULL;
		null_string = 0;
		//如果参数带引号，则去掉引号
		if (*buf == '"')
		{
			buf++;
			if (flag == AT_PARAM_PARSE_ESC)
				param_quotes_end = find_next_double_quato(buf);
			//解析所有参数，且该参数为待解析的最后一个参数，且为%p或%s格式，则该参数头尾应为""，中间可以是任意字符，包括'"'和','
			else if (flag == AT_PARAM_PARSE_ALL && fmt_comma == NULL && (strstr(fmt, "%p") != NULL || strchr(fmt, 's') != NULL))
			{
				if (*(buf + strlen(buf) - 1) == '"')
					param_quotes_end = buf + strlen(buf) - 1;
				else
				{
					ret = XY_Err_Parameter;
					break;
				}
			}
			else
				param_quotes_end = get_quote_end(buf);
			//只有左引号，没有右引号
			if (param_quotes_end == NULL)
			{
				ret = XY_Err_Parameter;
				break;
			}
			//输入参数为""时，at_parse_param_all接口会正常解析
			if(param_quotes_end  == buf)
				null_string = 1;
			param_comma = strchr(param_quotes_end + 1, ',');
			*param_quotes_end = '\0';
		}

		/* AT命令参数字符串和fmt格式字符串后续都有逗号，则强行将逗号改为'\0'，待处理完当前参数后再恢复 */
		if (param_comma && fmt_comma)
		{
			*param_comma = '\0';
			*fmt_comma = '\0';

			if ((ret = parse_type_val(buf, fmt, null_string, &at_data, arg, flag, ap)) != XY_OK)
				break;
			*fmt_comma = ',';
			/*若为%p，则外部用户会直接访问对应字符串，进而不能把尾部的'\0'恢复正常；该行为强行修改了入参AT字符串，考虑到该字符串仅在对应的AT命令解析函数中有生命，问题不大*/
			if(at_data.point_flag == 0)
			{
				*param_comma = ',';
				if (param_quotes_end)
					*param_quotes_end = '"';
			}
			buf = param_comma + 1;
			fmt = fmt_comma + 1;
		}
		/* AT命令参数字符串后续有逗号，而fmt格式字符串已是最后参数时*/
		else if (param_comma)
		{
			//解析所有参数时，如果参数已解析完，而buf后续还有逗号及数据，则报错
			if (flag == AT_PARAM_PARSE_ALL)
				ret = XY_Err_Parameter;
			else
			{
				*param_comma = '\0';
				ret = parse_type_val(buf, fmt, null_string, &at_data, arg, flag, ap);
			}
			break;
		}
		/* fmt格式字符串后续有逗号，而AT命令参数字符串已是最后参数时*/
		else if (fmt_comma)
		{
			*fmt_comma = '\0';
			ret = parse_type_val(buf, fmt, null_string, &at_data, arg, flag, ap);
			if(ret == XY_OK)
			{
				buf = buf + strlen(buf);
				if(param_quotes_end)
				{
					buf++;
					if(at_data.point_flag == 0)
						*param_quotes_end = '"';
				}
				*fmt_comma = ',';
				fmt = fmt_comma + 1;
			}
			else
				break;
		}
		/* AT命令参数字符串和fmt格式字符串皆已解析到最后一个参数时*/
		else
		{
			ret = parse_type_val(buf, fmt, null_string, &at_data, arg, flag, ap);
			break;
		}
	}

	//AT字符串携带的长度值与对应的字符串实际传输长度不一致，报错；该类型错误往往是测试人员故意制造的错误AT命令
	if((at_data.data_type == FMT_POINT && at_data.input_len != at_data.data_len && at_data.input_len * 2 != at_data.data_len) || (at_data.data_type == FMT_HEX && at_data.input_len * 2 != at_data.data_len))
	{
		ret = XY_Err_Parameter;
	}

END:
	if(at_data.point_flag == 0)
	{
		if (param_end)
			*param_end = '\r';
		if (param_comma)
			*param_comma = ',';
			
		if (param_quotes_end)
			*param_quotes_end = '"';
	}

	/*赋值下一个待解析参数首地址*/
	if(next_param != NULL)
	{
		if (param_comma)
			*next_param = param_comma+1;
		else
			*next_param = NULL;
	}
		
	xy_free(fmt_original);
	return ret;
}

void at_config_init(void)
{
    Restore_AT_Config_From_WorkingNV();
}

bool is_usb_at_support()
{
	if (g_softap_fac_nv->usb_mode & USB_CLASS_TYPE_CDC_ACM_AT)
		return true;
	return false;
}

bool is_usb_modem_support()
{
#if USB_MODEM
	if (g_softap_fac_nv->usb_mode & USB_CLASS_TYPE_CDC_ACM_MODEM)
		return true;
#endif
	return false;
}


/**
 * @brief 使能AT的SLEEP的睡眠唤醒能力，通常与DTR能力捆绑使用。
 * @note  目前仅AT+QSCLK=1时，会关闭LPUART的AT命令唤醒睡眠能力。
 */
extern void EnLpuartWakupSleep(uint8_t enable);
void Enable_AT_Wakup(uint8_t enable)
{
#if LPUART_AT
	EnLpuartWakupSleep(enable);
#endif
}

