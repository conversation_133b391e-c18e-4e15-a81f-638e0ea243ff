/** 
* @file         xy_ps_api.h
* @brief        3GPP相关AT命令处理接口，包括两个部分，一个是主动上报类消息的处理机制xy_atc_registerPSEventCallback；\n
***  另一个是用户触发的AT请求类消息的处理xy_atc_interface_call。对于客户常用的AT命令，芯翼提供了相应的API接口，如xy_get_IMEI。
* @attention    3GPP细节较多，用户二次开发时请务必确认清楚后再执行相关AT的改造
*
*/

#pragma once
#include <stdint.h>
#include <xy_atc_interface.h>
/*******************************************************************************
*                             Macro definitions                               *
******************************************************************************/
/**
* @brief           Corresponding "AT+CFUN" operation
* @warning 
*/
#define NET_CFUN0       0 ///< will  AT+CFUN=0
#define NET_CFUN1       1 ///< will  AT+CFUN=1
#define NET_CFUN5       5 ///< will  AT+CFUN=5

#define IMEI_LEN        (15 + 1)
#define IMSI_LEN        (15 + 1)
#define APN_LEN         100
#define UCCID_LEN       (20 + 1)
#define SN_LEN          (64)
#define PDPTYPE_LEN     7
#define EDRX_BASE_TIME  10.24
#define PTW_BASE_TIME   2.56

/* User registrable PS eventId */
#ifndef PS_REG_EVENT
#define PS_REG_EVENT
#define D_XY_PS_REG_EVENT_SIMST              0x00000001 //ATC_MSG_SIMST_IND_STRU
#define D_XY_PS_REG_EVENT_XYIPDNS            0x00000002 //ATC_MSG_XYIPDNS_IND_STRU
#define D_XY_PS_REG_EVENT_CRTDCP             0x00000004 //ATC_MSG_CRTDCP_IND_STRU
#define D_XY_PS_REG_EVENT_CGAPNRC            0x00000008 //ATC_MSG_CGAPNRC_IND_STRU
#define D_XY_PS_REG_EVENT_CGEV               0x00000010 //ATC_MSG_CGEV_IND_STRU
#define D_XY_PS_REG_EVENT_CEREG              0x00000020 //ATC_MSG_CEREG_IND_STRU
#define D_XY_PS_REG_EVENT_CSCON              0x00000040 //ATC_MSG_CSCON_IND_STRU
#define D_XY_PS_REG_EVENT_NPTWEDRXP          0x00000080 //ATC_MSG_NPTWEDRXP_IND_STRU
#define D_XY_PS_REG_EVENT_CEDRXP             0x00000100 //ATC_MSG_CEDRXP_IND_STRU
#define D_XY_PS_REG_EVENT_CCIOTOPTI          0x00000200 //ATC_MSG_CCIOTOPTI_IND_STRU
#define D_XY_PS_REG_EVENT_CESQ_Ind           0x00000400 //ATC_MSG_CESQ_IND_STRU, Report RSRP/SINR every 10 seconds
#define D_XY_PS_REG_EVENT_LOCALTIMEINFO      0x00000800 //ATC_MSG_LOCALTIMEINFO_IND_STRU
#define D_XY_PS_REG_EVENT_PDNIPADDR          0x00001000 //ATC_MSG_PdnIPAddr_IND_STRU
#define D_XY_PS_REG_EVENT_NGACTR             0x00002000 //ATC_MSG_NGACTR_IND_STRU
#define D_XY_PS_REG_EVENT_CMT                0x00004000 //ATC_MSG_CMT_IND_STRU
#define D_XY_PS_REG_EVENT_CMOLRE             0x00008000 //ATC_MSG_CMOLRE_IND_STRU
#define D_XY_PS_REG_EVENT_CMOLRG             0x00010000 //ATC_MSG_CMOLRG_IND_STRU
#define D_XY_PS_REG_EVENT_CMTI               0x04000000 //ATC_MSG_CMTI_IND_STRU
#define D_XY_PS_REG_EVENT_CDS                0x08000000 //ATC_MSG_CDS_IND_STRU
#define D_XY_PS_REG_EVENT_CDSI               0x20000000 //ATC_MSG_CDSI_IND_STRU
#endif

/*******************************************************************************
*                             Type definitions                                *
******************************************************************************/
typedef struct
{
    unsigned char                  ucIsUsed;
    unsigned char                  ucCid;
#define    D_CID_STATE_DEACTIVE 0
#define    D_CID_STATE_ACTIVE   1
    unsigned char                  ucCidState;
#define    D_CID_TYPE_DEFAULT   0
#define    D_CID_TYPE_DEDICATE  1
    unsigned char                  ucCidType;
    unsigned char                  ucMipcallFlg;
} ST_ATC_AP_CID_INFO;

typedef struct ril_serving_cell_info
{
    short Signalpower;
    short Totalpower;
    short TXpower;
    int CellID;
    int ECL;
    short SNR;
    int EARFCN;
    int PCI;
    short RSRQ;
    char tac[6];
    int sband;
    unsigned int plmn;
} ril_serving_cell_info_t;

typedef struct neighbor_cell_info
{
    unsigned int nc_earfcn;
    unsigned short nc_pci;
    short nc_rsrp;

    unsigned int               nc_CellId;
    unsigned short             nc_Tac;
    short                      nc_signal;
    unsigned char              auc_mcc[3];
    unsigned char              auc_mnc[3];
} neighbor_cell_info_t;

typedef struct ril_phy_info
{
    //BLER
    int RLC_UL_BLER;
    int RLC_DL_BLER;
    int MAC_UL_BLER;
    int MAC_DL_BLER;
    int MAC_UL_total_bytes;
    int MAC_DL_total_bytes;
    int MAC_UL_total_HARQ_TX;
    int MAC_DL_total_HARQ_TX;
    int MAC_UL_HARQ_re_TX;
    int MAC_DL_HARQ_re_TX;
    //THP
    int RLC_UL_tput;
    int RLC_DL_tput;
    int MAC_UL_tput;
    int MAC_DL_tput;
} ril_phy_info_t;

typedef struct ril_neighbor_cell_info
{
    int nc_num;
    neighbor_cell_info_t neighbor_cell_info[5];
} ril_neighbor_cell_info_t;

typedef struct 
{
    unsigned int               nc_CellId;
    unsigned short             nc_Tac;
    short                      nc_signal;
    unsigned char              auc_mcc[3];
    unsigned char              auc_mnc[3];
} neighbor_cell_info_t_qcell;

typedef struct 
{
    int nc_num;
    neighbor_cell_info_t_qcell neighbor_cell_info[6];
} ril_neighbor_cell_info_t_qcell;

typedef struct ril_radio_info
{
    ril_serving_cell_info_t serving_cell_info;
    ril_neighbor_cell_info_t neighbor_cell_info;
    ril_phy_info_t phy_info;
} ril_radio_info_t;

typedef void (*xy_psEventCallback_t)(unsigned long eventId, void *param, int paramLen);

/**
* @brief           回调解析函数
* @param1 void*    EventId对应的CNF结构体
* @param2 itn      结构体大小
* @param3 void*    pResult返回回调函数解析结果
* @return          Y_ERR失败, XY_OK成功
*/
typedef int (*func_AppInterfaceCallback)(void*, int, void*);

/*******************************************************************************
*                       Global function declarations                          *
******************************************************************************/
/**
* @brief           PS主动上报消息的回调注册函数，当PS状态发生变化后，会触发eventId的上报，并执行callback回调函数
* @param eventId   用户注册事件ID，见PS_REG_EVENT
* @param callback  回调函数
* @return          void
*/
void xy_atc_registerPSEventCallback(unsigned long eventId, xy_psEventCallback_t callback);

/**
* @brief           供用户发送3GPP相关的AT请求，并同步接收处理返回的AT应答
* @param pCmd      AT命令字符串，e.g."AT+CGSN=1\r\n"
* @param callback  AT命令回调函数, 可为NULL。
*                      为NULL时，pResult保存EventId对应的CNF结构体，AT命令与返回结果结构体对应关系示例如下:
*                                1) 设置/执行命令：AT+XXXX[=<value>] -> ATC_MSG_XXXX_CNF_STRU
*                                2) 查询命令         : AT+XXXX?          -> ATC_MSG_XXXX_R_CNF_STRU
*                                3) 测试命令         : AT+XXXX=?         -> ATC_MSG_XXXX_T_CNF_STRU
*                                (详细参见xy_atc_interface.h)
*                      不为NULL时，pResult返回回调函数解析结果。
* @param pResult   执行返回结果, 可为NULL；仅返回OK的命令，如设置命令，该参数为NULL
* @return          XY_ERR失败, XY_OK成功
* @attention       如果客户需要解析对应的中间结果AT命令，需要针对每条AT请求使用正确的中间结构结构体，并将地址强制为pResult入参；具体对应关系请咨询协议栈开发人员
*/
int xy_atc_interface_call(char* pCmd, func_AppInterfaceCallback callback, void* pResult);

/**
* @brief           供用户发送请求消息，同步处理，阻塞等待处理结果
* @param pMsgq     请求消息结构体
* @param usMsgLen  请求消息结构体长度
* @param pCnf      CNF消息指针的指针,可为空
* @param pusCnfLen CNF消息长度指针, 可为空
* @return          XY_ERR失败, XY_OK成功
*/
int xy_msg_interface_call(void* pMsg, unsigned short usMsgLen, void** pMsgCnf, unsigned short *pusCnfLen);

/**
* @brief 			供用户发送3GPP相关的SMS内容请求，并同步接收处理返回的结果
* @param ucTextFlg 文本或PDU格式，0：pdu， 1：文本
* @param pSmsData  SMS PDU or text e.g. 0x08,0x91...(仅数据，不携带0x1A)
* @param pDataLen  数据长度
* @param callback  AT命令回调函数, 可为NULL。
*                      为NULL时，pResult保存ST_ATC_AP_CMD_RST结构体.
*                      不为NULL时，pResult返回回调函数解析结果。
* @param pResult   执行返回结果, 可为NULL；仅返回OK的命令，如设置命令，该参数为NULL
* @return 			XY_ERR失败, XY_OK成功
* @attention 		如果客户需要解析对应的中间结果AT命令，需要针对每条AT请求使用正确的中间结构结构体，并将地址强制为pResult入参；具体对应关系请咨询协议栈开发人员
*                  示例：xy_atc_interface_call_sms_data(1, "xxx", 3, NULL, NULL)
*/
int xy_atc_interface_call_sms_data(unsigned char ucTextFlg, unsigned char* pSmsData, unsigned short usDataLen, func_AppInterfaceCallback callback, void* pResult);

/**
* @brief           Corresponding "AT+CFUN" operation
* @param status    取值范围(0,1,5)
*                   0：软关机
*                   1：软开机
*                  5：快速关机
* @return          XY_OK is success,other value is fail
* @warning         AT+CFUN=1成功返回OK后，并不代表此时PDP已激活，需要调用xy_wait_tcpip_ok接口等待PDP激活后，才能进行网络通信业务
*/
int xy_cfun_excute(int status);

/**
* @brief            This is funtion user Read the value of CFUN
* @param cfun       存储cfun的int指针,取值范围(0,1)
*                   0：关机态
*                   1：开机态
* @return           XY_ERR获取失败, XY_OK获取成功
*/
int xy_cfun_read(int *cfun);

/**
* @brief            查询当前4G网络是否连通，AT+CGATT?
* @return           -1: 出现异常错误； 0：网络未连接； 1：网络已连接
*/
char xy_get_CGATT_state();

/**
* @brief            This is funtion user Read check whether the network is registered
* @param cgact      存储cid0的状态，0未激活，1激活
* @return           XY_ERR获取失败, XY_OK获取成功
*/
int xy_get_CGACT(int *cgact);


/**
* @brief            查询cid的激活状态（取值范围0，1）
* @param cid        想要查询的cid(1-15)（如cid未定义过，会返回XY_ERR获取失败）
* @param cid_state  存储cid的状态，0未激活，1激活
* @return           XY_ERR获取失败, XY_OK获取成功
*/
int xy_get_cid_state(char cid ,char *cid_state);


/**
* @brief            查询CEREG配置的n值
* @param            存储cereg的int指针，取值范围(0-10)
*                   0：未驻网，没有在搜网
*                   1：驻网成功
*                   2：未驻网，正在搜网
*                   3：驻网被拒
*                   4：未知
*                   5：已驻网，漫游
*                   8：仅附着紧急承载业务
*                   6,7,9,10:不支持
* @return           XY_ERR获取失败, XY_OK获取成功
*/
int xy_cereg_read(int *cereg);


/**
 * @brief           This is funtion user Read the SN
 * @param sn        由调用者申请内存，接口内部赋值
 * @param len       入参sn指针的长度，由调用者赋值，但不能小于@ref SN_LEN 
 * @return          XY_ERR获取失败, XY_OK获取成功
 * @warning         注意入参内存申请长度，否则会造成内存越界
 */
int xy_get_SN(char *sn, int len);

/**
* @brief            获取信号强度
* @param rssi       存储rssi的int指针,
*                   取值(0-31,99),对应实际值(-113~-51,无效值)
* @return           XY_ERR获取失败, XY_OK获取成功
*/
int xy_get_RSSI(int *rssi);

/**
* @brief           获取卡状态
* @return          0:ready; 1:not ready(no sim); 2:unknow
*/
char xy_get_sim_state();

/**
* @brief           This is funtion user set IMEI
* @param imei      IMEI字符串,长度15个字符
* @return          XY_ERR获取失败, XY_OK获取成功
*/
int xy_set_IMEI(char *imei);

/**
* @brief           This is funtion user get IMEI
* @param imei      由调用者申请内存，接口内部赋值
* @param len       入参imei指针的长度，由调用者赋值，但不能小于16
* @return          XY_ERR获取失败, XY_OK获取成功
* @warning         注意入参内存申请长度，否则会造成内存越界
*/
int xy_get_IMEI(char *imei, int len);

/**
 * @brief           This is funtion user get IMSI
 * @param imsi      由调用者申请内存，接口内部赋值
 * @param len       入参imsi指针的长度，由调用者赋值，但不能小于16
 * @return          XY_ERR is fail, XY_OK is success
 * @warning         注意入参内存申请长度，否则会造成内存越界
 *                  若尚未检测到SIM卡，imsi指针未被赋值，需要客户自行识别是否已经识别到SIM卡
 */
int xy_get_IMSI(char *imsi, int len);

/**
 * @brief           This is funtion user get cellid
 * @param           存储cellid的int指针
 * @return          XY_ERR获取失败, XY_OK获取成功
 * @warning         小区未驻留之前，无法获取小区ID
 */
int xy_get_CELLID(int *cell_id);

/**
 * @brief           Corresponding get NCCID operation
 * @param ccid      由调用者申请内存，接口内部赋值
 * @param len       入参ccid指针的长度，由调用者赋值，但不能小于23 
 * @return          XY_ERR is fail, XY_OK is success
 * @warning         注意入参内存申请长度，否则会造成内存越界
 *                  若尚未检测到SIM卡，imsi指针未被赋值，需要客户自行识别是否已经识别到SIM卡
 */
int xy_get_NCCID(char *ccid, int len);

/**
 * @brief           This is funtion user get APN
 * @param apn_buf   由调用者申请内存，接口内部赋值
 * @param len       入参apn_buf指针的长度，由调用者赋值，但不能小于100 
 * @param query_cid PDP激活对应的哪一路cid，调用者若不关心则填-1，接口内部会自动赋值，即当前网络激活承载的cid路数，一般为1
 * @return          XY_ERR is fail,XY_OK is success.
 * @warning         注意入参内存申请长度，否则会造成内存越界
 *                  若尚未配置APN，apn_buf不会被赋值
                    若query_cid要在驻网之后才能填-1，否则返回空
 */
int xy_get_PDP_APN(char *apn_buf, int len, int query_cid);

/**
 * @brief           This is funtion user get 3412 tau_time
 * @param tau       存储tau的int指针,单位秒
 * @return          XY_ERR获取失败, XY_OK获取成功
 */
int xy_get_T_TAU(int *tau);

/**
 * @brief           This is funtion user get 3324 act_time
 * @param           存储t3324的int指针,单位秒
 * @return          XY_ERR获取失败, XY_OK获取成功
 */
int xy_get_T_ACT(int *t3324);


/**
 * @brief   This is funtion user get uicc type
 * @param   uicc_type  存储uicc_type的int 指针
 *          1:china telecom; 2:china mobile; 3:china unicom; 4:china broadnet; 5:TestCard; other value:unknown
 * @return  XY_ERR is fail,return XY_OK is success.
*/
int xy_get_UICC_TYPE(int *uicc_type);

/**
 * @brief   设置eDRX的模式,寻呼周期大小
 * @param   modeVal     eDRX模式(0-5)
 * 0:禁用，1-5:启用edrx。
 * @param   actType     接入技术类型(7:EUTRAN)
 * @param   ulDrxValue  eDRX的寻呼周期，单位毫秒
 * 取值范围(20480,40960,81920,163840,327680,655360,1310720,2621440,5242880,10485760)
 * @return  XY_ERR is fail,return XY_OK is success.
*/
int xy_set_eDRX_value(unsigned char modeVal, unsigned char actType, unsigned long ulDrxValue);

/**
 * @brief   用以分别获取eDRX的模式,寻呼周期和寻呼窗口周期大小
 * @param   pucActType      接入技术类型(7:EUTRAN)
 * @param   pulEDRXValue    eDRX的寻呼周期，单位毫秒
 * @param   pulPtwValue     寻呼窗口周期，单位毫秒
 * @return  XY_ERR is fail,return XY_OK is success.
*/
int xy_get_eDRX_value_MS(unsigned char* pucActType, unsigned long* pulEDRXValue, unsigned long* pulPtwValue);

/**
 * @brief   用以分别获取eDRX的寻呼周期和寻呼窗口周期大小
 * @param   eDRX_value  eDRX的寻呼周期，单位秒
 * @param   ptw_value   寻呼窗口周期，单位秒
 * @return  XY_ERR is fail,return XY_OK is success.
*/
int xy_get_eDRX_value(float *eDRX_value, float *ptw_value);

/**
 * @brief   获取服务小区信息(对应AT命令为AT+NUESTATS=RADIO)
 * @param rcv_servingcell_info 用户主动malloc，并将地址传递进来，不可为NULL
 * 结构体中获取内容如下：
 *    int Signalpower : rsrp
 *    int Totalpower : rssi
 *    int TXpower : 发射功率
 *    int CellID: cellid
 *    int ECL: 覆盖等级
 *    int SNR: sinr
 *    int EARFCN: 频点
 *    int PCI: 物理cell ID
 *    int RSRQ: rsrq
 *    char tac[5]: 跟踪区
 *    int sband: 服务小区频点对应的BAND
 * @return  XY_ERR is fail,return XY_OK is success. 
 */
int xy_get_servingcell_info(ril_serving_cell_info_t *rcv_servingcell_info);

/**
 * @brief   获取邻小区信息(对应AT命令为AT+NUESTATS=CELL)
 * @param neighbor_cell_info 用户主动malloc，并将地址传递进来，不可为NULL
 *   int nc_earfcn : 频点
 *   short nc_pci : 物理cell ID
 *   nc_rsrp : rsrp
 *   nc_CellId : cellid
 *   nc_Tac : 跟踪区
 *   nc_signal : RSSI，[-113,0]
 *   auc_mcc[3] : plmn中MCC
 *   auc_mnc[3] : plmn中MNC
 * @return XY_ERR is fail,return XY_OK is success.
 * @note 获取邻小区的earfcn，pci，rsrp与邻小区的数量，最多只返回5个邻小区的信息。
 */
int xy_get_neighborcell_info(ril_neighbor_cell_info_t *neighbor_cell_info);

/**
 * @brief   获取PHY信息 (对应AT命令为AT+NUESTATS=BLER和AT+NUESTATS=THP)
 * @param  rcv_phy_info 用户主动malloc，并将地址传递进来，不可为NULL
 *     //BLER
 *    int RLC_UL_BLER;RLC层上行误块率
 *    int RLC_DL_BLER;RLC层下行误块率
 *    int MAC_UL_BLER;物理层上行误块率
 *    int MAC_DL_BLER;物理层下行误块率
 *    int MAC_UL_total_bytes;传输的总字节数
 *    int MAC_DL_total_bytes;接收的总字节数
 *    int MAC_UL_total_HARQ_TX;发送的传输块
 *    int MAC_DL_total_HARQ_TX;接收的传输块
 *    int MAC_UL_HARQ_re_TX;重传的传输块
 *    int MAC_DL_HARQ_re_TX;接收的总ACK/NACK消息数
 *    //THP
 *    int RLC_UL_tput;RLC层上行吞吐量
 *    int RLC_DL_tput;RLC层下行吞吐量
 *    int MAC_UL_tput;物理层上行吞吐量
 *    int MAC_DL_tput;物理层下行吞吐量
 * @return XY_ERR is fail,return XY_OK is success.
 * @note        
 */
int xy_get_phy_info(ril_phy_info_t *rcv_phy_info);

/**
 * @brief   获取NUESTATS中3GPP相关信息的API接口
 * @param rcv_radio_info 用户主动malloc，并将地址传递进来，不可为NULL
 * @return XY_ERR is fail,return XY_OK is success.
 * @note  该接口内部分为几次发送不同的AT命令给PS，由于这条AT命令非标准，存在变化的可能，客户自行参考该DEMO实现即可      
 */
int xy_get_radio_info(ril_radio_info_t *rcv_radio_info);

/**
* @brief            接口将冒号间隔BAND的字符串转为逗号间隔
* @param            入参,BAND字符串，格式"BAND:BAND:BAND……:BAND"
* @return           -1: 出现异常错误； 0: 成功
*/
char xy_creat_bandstr(char* pBandBuf);

/**
* @brief            设置band(调用AT+MBAND)，
                    注：此接口会自动关机（AT+CFUN=0）
* @param            入参
                         cBandNum:band个数,最大支持设置19个band;
                         pBandstr:BAND字符串，格式"BAND,BAND,BAND……,BAND";
* @return           -1: 出现异常错误； 0: 成功
*/
char xy_set_band(char cBandNum, char*   pBandstr);

/**
* @brief            获取band信息(调用AT+MBAND?)
* @param            入参
                        pBandNum:band个数储存地址，整型,取值范围(0-19); 
                        pBandBuf:band数组储存地址,空间大小需要大于19字节,避免空间不够保存band信息;
                        cSize:pBandBuf的空间大小，防止数据越界使用;
* @return           -1: 出现异常错误； 0: 成功
*/
char xy_get_band(char*     pBandNum, char* pBandBuf, char cSize);

/**
* @brief            设置使用的卡槽(调用AT+QSIMSWITCH=)
* @param            入参,卡槽取值范围(0,1)
* @return           -1: 出现异常错误； 0: 成功
*/
char xy_switch_sim(char card_slot);

/**
* @brief            获取目前使用的卡槽(调用AT+QSIMSWITCH?)
* @param            入参,卡槽信息存储地址，整型,(0,1)
* @return           -1: 出现异常错误； 0: 成功
*/
char xy_get_sim_slot(char* pcurrent_slot);


/**
* @brief            获取邻区信息
* @param            ril_neighbor_cell_info
* nc_CellId; cellid
* nc_Tac;    跟踪区
* nc_signal; rssi
* auc_mcc[3];plmn中MCC
* auc_mnc[3];plmn中MNC
* @return           -1: 出现异常错误； 0: 成功
*/
int xy_get_neighborcell_info_qcell(ril_neighbor_cell_info_t_qcell *ril_neighbor_cell_info);

/**
* @brief            获取卡状态
* @param            
* @return           0	none sim||unknow
	1	sim exist	卡存在
	2	searching	搜网
	5	注册成功，状态同(cereg:1,5)
	
	7	未注册，状态同(cereg:0)
*/
char xy_get_net_state();

/**
 * @brief   获取卡状态相关信息的API接口
 * @return  0：有卡；
 *          1：无卡；
 *          2：卡无效；
 *          3：接口获取异常
 * @note  调用多条AT，执行需要一定时间
 */
int xy_simst_read();

/**
* @brief            配置WiFiscan的相关信息,调用"AT+MWIFISCANCFG=<function>,<value>"
* @param            <fuction>：'Priority', 优先级, <value>：0：数据优先; 1：wifi优先。
                    <fuction>：'max', WiFi最大扫描个数, <value>:[4-10]。
* @return           -1: 出现异常错误； 0: 成功
*/
int xy_wifiscan_cfg(char* fuction , char value);

/**
* @brief            开始WiFiscan,Timeout = 0 调用"AT+MWIFISCANSTART=<Mround>[,<Mtimeout>]"
                                Timeout != 0 调用"AT+QWIFISCAN[=<time>[,<round>[,<max_bssid_num>[,<scan_timeout>[,<priority>]]]]] "
* @param            <Timeout>：扫描超时时间，[4000-255000]，单位ms
                    <ScanRound>(<Mround>)：扫描轮次,[1-3]
                    <WifiNum>：最大扫描数量,[4-10]
                    <ScanTime>(<Mtimeout>)：单轮扫描超时时间,[0-255].(mTimeout取值范围10-60)单位s
                    <Priority>：优先级设置[0-1]
* @return           -1: 出现异常错误； 0: 成功
* @note             wifi搜索是异步的，wifi搜索的结果请从注册回调中获取
*/
int xy_wifiscan_start(int Timeout,char ScanRound, char WifiNum, char ScanTime, char Priority);

/**
* @brief            停止WiFiscan
* @return           -1: 出现异常错误； 0: 成功
*/
int xy_wifiscan_stop();

/**
* @brief            查询上次的wifiscan的结果
* @param            pWifiScanInfo：用于存储wifi的信息，rssi，channel，mac
                    cSort：是否按照rssi进行排序(0:不排序，1：排序)
* @return           -1: 出现异常错误； 0: 成功
*/
int xy_wifiscan_que(ATC_MSG_MWIFISCANQUERT_CNF_STRU* pWifiScanInfo,char cSort);

/**
* @brief            设置rfmode
* @param            value: (0,1)
* @return           -1: 出现异常错误； 0: 成功
*/
int xy_set_rfmode(int value);

/**
* @brief            获取当前rfmode
* @param            value:存储地址 取值为0，1
* @return           -1: 出现异常错误； 0: 成功
*/
int xy_get_rfmode(int* value);

/**
* @brief            获取cid的ipv4的mtu
* @param            cid：整型，取值范围（1-15）
                    IPV4_MTU：存储mtu的地址
* @return           0: 出现异常错误； 1: 成功
*/
int xy_get_ipv4_mtu(unsigned char cid, unsigned short* IPV4_MTU);


/**
* @brief            激活cid
* @param cid        想要查询的cid(1-15)
* @return           XY_ERR激活失败, XY_OK激活成功
//非600N客户请勿使用此接口！！
*/
int xy_cid_active_qiact(char cid);

/**
* @brief            获取此时是否最后一路cid，是否有业务限制
* @return           1：受限, 0不受限
*/
unsigned char xy_get_cid_limit_flg();

/**
* @brief            设置是否业务限制
* @ucFlg           1：受限, 0不受限
*/
void xy_set_cid_limit_flg( unsigned char ucFlg);

/**
* @brief            异步激活
* @param            cid：整型，取值范围（1-15)
* @return           0: 出现异常错误； 1: 成功
*/
int xy_activate_cid(char cid, char mipcallflg);

/**
* @brief            异步激活,注cid的会增加qiact激活的标志，此时如果是非QIDEACT去激活时会多出+QIURC主动上报
* @param            cid：整型，取值范围（1-15）
                    ViewMode：整型，主动上报是否显示IP地址
* @return           0: 出现异常错误； 1: 成功
//注非800k用户请勿使用此接口！！
*/
int xy_activate_cid_QIACTEX(char cid ,char ViewMode);
/**
* @brief            异步去激活
* @param            cid：整型，取值范围（1-15）
* @return           0: 出现异常错误； 1: 成功
*/
int xy_deactivate_cid(char cid);

void xy_get_cid_state_info(unsigned char ucCid, ST_ATC_AP_CID_INFO* stCidInfo);
void xy_set_cid_state_mipcallflg( unsigned char ucCid, unsigned char ucMipcallFlg );
void xy_set_cid_state_info( unsigned char ucCid, unsigned char ucstate,unsigned char ucType );
void xy_clean_cid_state_info(  );

/**
* @brief            开关psm
* @param            cValue：0: 关闭； 1: 打开
* @return           0: 出现异常错误； 1: 成功
*/
int xy_set_PSM_Enable(char cValue);
/**
* @brief            异步去激活
* @param            cValue：0: 关闭； 1: 打开
* @return           0: 出现异常错误； 1: 成功
*/
int xy_get_PSM_Enable(char* cValue);

/**
 * @brief           设置PPP的CID参数
 * @param cid       入参，指定cid
 * @param ip_type   入参，IP类型，IP/IPV6/IPV4V6/Non-IP，不能为空
 * @param apn       入参, APN，最大30字节字符串，不能为NULL(可为空字符串)
 * @return          XY_ERR is fail, XY_OK is success
 */
int xy_set_ppp_param(char cid, char* ip_type, char *apn);

/**
 * @brief           +CGAUTH鉴权参数设置接口
 * @param cid       入参，指定cid
 * @param auth_prot 入参，鉴权协议。0:不使用鉴权, 1:PAP；2:CHAP
 * @param username  入参, 用户名，16字节字符串，不能为NULL(可为空字符串)
 * @param passwd    入参，密码，16字节字符串，不能为NULL(可为空字符串)
 * @return          XY_ERR is fail, XY_OK is success
 */
int xy_set_ppp_auth_param(char cid, char auth_prot, char *username, char* passwd);
