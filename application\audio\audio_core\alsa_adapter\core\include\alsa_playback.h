#ifndef __ALSA_PLAYBACK_H__
#define __ALSA_PLAYBACK_H__

#include <stdint.h>
#include "alsa.h"
#include "alsa_extra.h"

typedef struct alsa_playback alsa_playback_t;

typedef enum alsa_playback_stop_type
{
    ALSA_PLAYBACK_STOP_NORMAL = 0,
    ALSA_PLAYBACK_STOP_FADEOUT,
    ALSA_PLAYBACK_STOP_IMMEDIATE,
} alsa_playback_stop_type_t;

void alsa_playback_set_volume(uint8_t volume);
void alsa_playback_get_volume(uint8_t *volume);
alsa_playback_t *alsa_playback_open(uint32_t sample_rate,
                                    uint8_t channels,
                                    uint8_t bits);

int alsa_playbakc_set_data_push_callback(alsa_playback_t *playback, alsa_data_pull_push_cb_t cb);

int alsa_playback_start(alsa_playback_t *playback);

int alsa_playback_pause(alsa_playback_t *playback);

int alsa_playback_puase_with_fadeout(alsa_playback_t *playback);

int alsa_playback_stop(alsa_playback_t *playback, alsa_playback_stop_type_t stop_type);

int alsa_playback_flush(alsa_playback_t *playback);

int alsa_playback_close(alsa_playback_t *playback);

int alsa_playback_write(alsa_playback_t *playback, uint8_t *buf, uint32_t length);

uint32_t alsa_playback_data_from_user(uint8_t *data, uint32_t length);

int alsa_playback_register_pcm_state_callback(alsa_playback_t *playback,
                                              alsa_pcm_state_callback_t cb,
                                              void *arg);

int alsa_playback_set_user_type(alsa_playback_t *playback, alsa_user_type_t type);

int alsa_playback_get_user_type(alsa_playback_t *playback, alsa_user_type_t *type);

int alsa_playback_set_maxdelaytime(alsa_playback_t *playback, int delay_ms);

int alsa_playback_sw_gain_bypass(alsa_playback_t *playback, uint8_t bypass);

int alsa_playback_sw_gain_fade_inout_bypass(alsa_playback_t *playback, uint8_t bypass);

int alsa_playback_sw_gain_fade_inout_coef(alsa_playback_t *playback, float coef);

int alsa_playback_sw_gain_pre_compensation_enable(alsa_playback_t *playback, uint8_t enable, float compensation_db);

int alsa_playback_set_trigger_level(alsa_playback_t *playback, alsa_trigger_level_t level);

int alsa_playback_set_threshold(alsa_playback_t *playback, uint32_t threshold);

int alsa_playback_internal_buffer_get_total_length(alsa_playback_t *playback, uint32_t *length);

int alsa_playback_internal_buffer_get_readable_length(alsa_playback_t *playback, uint32_t *length);

int alsa_playback_internal_buffer_get_writeable_length(alsa_playback_t *playback, uint32_t *length);

int alsa_playback_pmc_state_changed(alsa_playback_t *playback, alsa_pcm_state_t pcm_state);

#endif
