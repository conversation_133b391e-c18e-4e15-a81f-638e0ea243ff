#ifndef __AUDIO_PLAY_H__
#define __AUDIO_PLAY_H__

#include <stdint.h>
#include "cmsis_os2.h"
#include "audio_record.h"
#include "xy_system.h"
#include "xy_fs.h"
#include "heap_api.h"
#include "kfifo.h"
#include "xy_audio.h"

// 流式播放缓冲区大小
#define AUDIO_PLAY_STREAM_BUFFER_SIZE         (32*1024)  // 128kbps的音频流可以缓存2s的音频数据

typedef enum  {
    AUDIO_ERR_OK          = 0,   // No error
    AUDIO_ERR_FILE_OPEN   = -1,  //打开文件失败
    AUDIO_ERR_FILE_CLOSE  = -2,  //关闭文件失败
    AUDIO_ERR_READ        = -3,  //读取数据时出错
    AUDIO_ERR_FORMAT      = -4,  //音频格式错误
    AUDIO_ERR_BITS        = -5,  //音频位宽错误（只支持16位）
    AUDIO_ERR_MEMORY      = -6,  //内存分配失败
    AUDIO_ERR_SINK_OPEN   = -7,  //sink打开失败
    AUDIO_ERR_STOP        = -8,  //用户主动停止播放

}audio_error_t;

typedef struct _audio_play audio_play_t;
typedef struct _audio_sink audio_sink_t;
typedef struct _audio_source audio_source_t;

typedef enum
{
    SINK_MODE_UNKNOWN = -1,

    /* PLAYBACK */
    SINK_MODE_OUT = (1 << 0),

    /* CAPTURE */
    SINK_MODE_IN = (1 << 1),

} sink_mode_t;

typedef enum
{
    AUDIO_SOURCE_TYPE_INVALID = 0,

    AUDIO_SOURCE_TYPE_BUFFER = 1,
    AUDIO_SOURCE_TYPE_FILE = 2,
    AUDIO_SOURCE_TYPE_STREAM = 3,

    AUDIO_SOURCE_MAX,
} audio_source_type_t;

typedef struct kfifo play_stream_t;

typedef struct _source_arg{
    audio_source_type_t type;
    uint32_t source_len;
    uint32_t read_len;
    union
    {
        XY_FILE * file;
        uint8_t * buf;
        play_stream_t * stream;
    } u;
}source_arg_t;

typedef size_t (*audio_source_read_t)(void *buf, size_t size, source_arg_t *source_arg);
typedef int (*audio_source_seek_t)(uint64_t position, source_arg_t *source_arg);

struct _audio_source{
    audio_source_format_t format;
    audio_source_read_t read;
    audio_source_seek_t seek;
    source_arg_t * source_arg;
};

typedef int (*audio_decoder_process_t)(audio_play_t *audio_play);
typedef struct _audio_decode{
    audio_decoder_process_t process;
}audio_decode_t;

typedef enum
{
    AUDIO_PLAY_STATE_ERROR = -1,
    AUDIO_PLAY_STATE_CLOSE = 0,
    AUDIO_PLAY_STATE_OPEN,
    AUDIO_PLAY_STATE_START,
    AUDIO_PLAY_STATE_STOP,
    AUDIO_PLAY_STATE_PAUSE,
} audio_play_state_t;

typedef void audio_sink_handle_t;
typedef audio_sink_handle_t *(*audio_sink_open_t)(sink_mode_t mode, uint32_t sample_rate, uint8_t channels, uint8_t bits);
typedef int (*audio_sink_start_t)(audio_sink_handle_t *sink_handle);
typedef int (*audio_sink_write_t)(audio_sink_handle_t *sink_handle, uint8_t *data, uint32_t length);
typedef int (*audio_sink_stop_t)(audio_sink_handle_t *sink_handle);
typedef int (*audio_sink_close_t)(audio_sink_handle_t *sink_handle);
typedef int (*audio_sink_pause_t)(audio_sink_handle_t *sink_handle);

struct _audio_sink{
    audio_sink_open_t open;
    audio_sink_start_t start;
    audio_sink_write_t write;
    audio_sink_stop_t stop;
    audio_sink_close_t close;
};

typedef audio_play_state_t (*play_state_get_t)(void);

struct _audio_play{
    audio_source_t *source;
    play_state_get_t state_get;
    audio_sink_t *sink;
    osSemaphoreId_t *sem;
};

typedef enum
{
    AUDIO_REQ_INVALID = 0,

    AUDIO_REQ_FILE_PLAY,
    AUDIO_REQ_BUFFER_PLAY,
    AUDIO_REQ_STREAM_PLAY,
    AUDIO_REQ_TTS_PLAY,

    AUDIO_REQ_MAX,
} audio_play_req_type_t;

typedef struct _audio_play_req{
    audio_play_req_type_t type;
    audio_source_format_t format;

    union
    {
        char * file;
        uint8_t * buf;
        play_stream_t * stream;
    } u;

    uint32_t len;
    void * cfg; // TTS config
    play_complete_cb_t complete_cb;
} audio_play_req_t;

void audio_play_init(void);
void audio_play_req(audio_play_req_t * req);
uint32_t audio_play_stream_put(uint8_t * buf, uint32_t len);
void audio_play_stop(void);
void audio_play_pause(void);
void audio_play_resume(void);

#endif //__AUDIO_PLAY_H__