/**********************************************************************/
/**
 * @file yx_utils_md5.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-06-08
 * @version V1.0
 * @brief MD5工具封装接口
 **********************************************************************/
#ifndef YX_UTILS_MD5_H
#define YX_UTILS_MD5_H

#include "yx_type.h"

typedef struct
{
    INT32U a;
    INT32U b;
    INT32U c;
    INT32U d;
} md5_val_t;

/**
 * @brief 计算MD5值
 * 
 * @param str 输入数据
 * @param size 输入长度
 * @return md5_val_t 
 */
md5_val_t yx_utils_get_md5(INT8U* str, INT32U size);

#endif
