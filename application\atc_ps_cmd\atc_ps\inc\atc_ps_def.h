#ifndef _ATC_AP_DEFINE_H_
#define _ATC_AP_DEFINE_H_

#include "xy_atc_interface.h"
#if !ATC_DSP
#include "xy_utils.h"
#endif

#ifndef NULL
#define NULL                         ((void*)0)
#endif

#if ATC_DSP
#define D_THEARD_ATC_AP_PRIO         THREAD_PRI_NAS - 1
#define D_THEARD_ATC_AP_STACK        2048
#define D_THEARD_ATC_AP_CB_STACK     1536
#else //M3
#define D_THEARD_ATC_AP_PRIO         osPriorityAboveNormal
#define D_THEARD_ATC_AP_STACK        0x800
#define D_THEARD_ATC_AP_CB_STACK     0xC00
#endif

#define ATC_AP_TRUE                  1
#define ATC_AP_FALSE                 0
#define ATC_AP_PARAM_OVER            2

#ifndef ATC_OK
#define ATC_OK                          0
#define ATC_NG                          1
#endif

#ifndef ATC_FALSE
#define ATC_FALSE                       0
#define ATC_TRUE                        1
#endif

#define D_ATC_AP_CME_UNKNOWN                    100
#define D_ATC_AP_CME_INCORRECT_PARAMETERS       50
#define D_ATC_AP_CME_OPER_NOT_SUPPORED          4

#ifndef D_ATC_DEFAULT_CR
/* &F and init default value */
#define D_ATC_DEFAULT_CR                13
#define D_ATC_DEFAULT_LF                10
#define D_ATC_DEFAULT_BS                8
#define D_ATC_DEFAULT_ECHO              0
#define D_ATC_DEFAULT_RSP_FMT           1
#define D_ATC_DEFAULT_ERR_MOD           1
#endif

#define D_ATC_EMM_CAUSE_TBL_SIZE             28
#define D_ATC_ESM_CAUSE_TBL_SIZE             40

#define D_ATC_USER_REG_EVENT_TBL_SIZE        32   //26->29

#define D_ATC_FAC_NUM                        1//22
#define D_ATC_TFT_TEST_MSG_MAX_LENGTH        1563
#define D_ATC_CGCONRRDP_MSG_MAX_LENGTH       1278
#define D_ATC_READ_TFT_MAX_VALUE             339                  
#define D_ATC_NQPODCP_IND_MSG_MAX_LENGTH     930

/* PARAMETER ANALYSIS RESULT */
#ifndef D_ATC_PARAM_OK
#define D_ATC_PARAM_OK                  0
#define D_ATC_PARAM_EMPTY               1
#define D_ATC_PARAM_ERROR               2
#define D_ATC_PARAM_SYNTAX_ERROR        3
#endif

#ifndef D_ATC_STOP_CHAR_CR
#define D_ATC_STOP_CHAR_CR                            0
#define D_ATC_STOP_CHAR_KANMA                         1
#define D_ATC_STOP_CHAR_SEMICOLON                     2
#define D_ATC_STOP_CHAR_STR                           3
#endif

/* ERR MODE */
#define D_ATC_ERRMOD_NOT_CMEE           0                                               /*lint !e755*/
#define D_ATC_ERRMOD_DIG_CMEE           1
#define D_ATC_ERRMOD_STR_CMEE           2

#ifndef D_ATC_COMMAND_OK
/* Command analysis result */
#define D_ATC_COMMAND_OK                0                                               /* Command normal                       */
#define D_ATC_COMMAND_ERROR             1                                               /* Not define command                   */
#define D_ATC_COMMAND_PARAMETER_ERROR   2                                               /* Parameter error                      */
#define D_ATC_COMMAND_END               3                                               /* Command end                          */
#define D_ATC_COMMAND_SYNTAX_ERROR      4                                               /* Command error                        */
#define D_ATC_COMMAND_DELIMITOR         5
#define D_ATC_COMMAND_SINGLE_OK         6
#define D_ATC_COMMAND_PDU_CANCEL        7
#define D_ATC_COMMAND_TOO_MANY_PARAMETERS     8                                         /* Parameter too many error             */
#define D_ATC_COMMAND_MODE_ERROR        9                                               /* Workmode error */
#endif

#define D_ATC_P_COPS_OPER_SIZE_MAX              6                                       /* +COPS:<per>                          */

#ifndef D_ATC_N_CR
#define D_ATC_N_CR                                    D_ATC_DEFAULT_CR                  /* <cr> define                          */
#define D_ATC_N_LF                                    D_ATC_DEFAULT_LF                  /* <lf> define                          */
#define D_ATC_N_COMMA                                 44                                /* ','                                  */
#define D_ATC_N_SEMICOLON                             59                                /* ';'                                  */
#define D_ATC_N_QUOTATION                             34                                /* '"'                                  */
#define D_ATC_N_CTRL_Z                                26                                /*  ctrl-Z                              */
#define D_ATC_N_ESC                                   27                                /*  ESC                                 */
#define D_ATC_N_SUB                                   45                                /* '-'                                  */
#define D_ATC_N_DOT                                   46                                /* '.'                                  */
#endif

/* LTEATC */
#define D_ATC_PARAM_MAX_P_CGDCONT               16                                      /* +CGDCONT PARAMETER NUMBER            */
#define D_ATC_PARAM_MAX_P_CGDSCONT              4                                       /* +CGDCONT PARAMETER NUMBER            */
#define D_ATC_PARAM_MAX_P_CGEQOS                6                                       /* +CGEQOS  PARAMETER NUMBER            */
#define D_ATC_PARAM_MAX_P_CGTFT                 12                                      /* +CGTFT  PARAMETER NUMBER             */
#define D_ATC_CGTFT_PARAM_SPI_LEN               8                                       /* +CGTFT  ipsec security parameter index */
#define D_ATC_CGTFT_PARAM_FLOW_LABEL_LEN        5                                       /* +CGTFT  flow label (ipv6)            */
#define D_ATC_PARAM_MAX_P_CFUN                  2                                       /* +CFUN PARAMETER NUMBER               */
#define D_ATC_PARAM_MAX_P_CSODCP                6                                       /* +CSODCP PARAMETER NUMBER             */
#define D_ATC_P_CSODCP_CPDATA_SIZE_MAX          65535                                   /* +CSODCP:<cpdata>                     */
#define D_ATC_PARAM_MAX_P_CEDRXS                4                                       /* +CEDRXS PARAMETER NUMBER             */
#define D_ATC_P_CEDRXS_EDRX_VALUE_MAX           4                                       /* +CEDRXS:<eDRX value>                 */
#define D_ATC_P_CPSMS_ReqPeriTAU_VALUE_MAX      8                                       /* +CPSMS:<Requested_Periodic-TAU value>*/
#define D_ATC_P_CPSMS_ReqActTime_VALUE_MAX      8                                       /* +CPSMS:<Requested_Active-Time value> */
#define D_ATC_PARAM_MAX_P_CPSMS                 5//3                                       /* +CPSMS PARAMETER NUMBER              */
#define D_ATC_PARAM_MAX_P_CNMI                  5                                       /* +CNMI PARAMETER NUMBER               */
#define D_ATC_PARAM_MAX_P_CMUX                  9
#define D_ATC_PARAM_MAX_P_CNMA                  3                                       /* +CNMA PARAMETER NUMBER               */
#define D_ATC_PARAM_MAX_P_COPS                  4                                       /* +COPS PARAMETER NUMBER               */
#define D_ATC_PARAM_MAX_P_NEARFCN               3                                       /* +NEARFCN PARAMETER NUMBER            */
#define D_ATC_PARAM_MAX_P_NBAND                 8
#define D_ATC_PARAM_MAX_P_QLWULDATAEX           3
#define D_ATC_PARAM_MAX_P_NATSPEED              5
#define D_ATC_PARAM_MAX_P_NSET                  3                                       /* +NSET PARAMETER NUMBER               */
#define D_ATC_PARAM_MAX_P_SIMUUICC              4 
#define D_ATC_PARAM_MAX_P_CCIOTOPT              3
//shao add for USAT
#define D_ATC_PARAM_MAX_P_CSIM                  2
#define D_ATC_PARAM_MAX_P_CCHC                  2
#define D_ATC_PARAM_MAX_P_CCHO                  1
#define D_ATC_PARAM_MAX_P_CGLA                  3
#define D_ATC_PARAM_MAX_P_CRSM                  7
#define D_ATC_PARAM_MAX_P_CPWD                  3                                       /* +CPWD PARAMETER NUMBER                   */
#define D_ATC_PARAM_MAX_P_CLCK                  3//4                                    /* +CLCK PARAMETER NUMBER                   */
#define D_ATC_PARAM_MAX_P_CPIN                  2                                       /* +CPIN PARAMETER NUMBER                   */

#define D_ATC_PARAM_MAX_P_CMOLR                 15

/* Parameter size */
#define D_ATC_P_CGDCONT_APN_SIZE_MAX                           100                      /* <APN> MAX SIZE                       */
#define D_ATC_P_CGTFT_REMOTEADDRESSANDSUBNETMASK_SIZE_MAX      130                      /* <remote address and subnet mask> MAX
                                                                                            SIZE                                */ 
#define D_ATC_P_CGTFT_PORT_RANG_SIZE_MAX                       15                       /* <local/remote port range> MAX SIZE          */
#define D_ATC_P_CGTFT_TOSANDMASK_TRACFFICCLASSANDMAK_SIZE_MAX  10                       /* <type of service (tos) (ipv4) and mask/
                                                                                        traffic class (ipv6) and mask> MAX SIZE */
#define D_ATC_P_CGTFT_LOCALADDRESSANDSUBNETMASK_SIZE_MAX       130                     /* <remote address and subnet mask> MAX 
                                                                                                                           SIZE */

//for CRSM 
#define D_ATC_CRSM_COMMAND_READ_BINARY               176
#define D_ATC_CRSM_COMMAND_READ_RECORD               178
#define D_ATC_CRSM_COMMAND_GET_RESPONSE              192
#define D_ATC_CRSM_COMMAND_UPDATE_BINARY             214
#define D_ATC_CRSM_COMMAND_UPDATE_RECORD             220
#define D_ATC_CRSM_COMMAND_STATUS                    242
#define D_ATC_CRSM_COMMAND_RETRIEVE_DATA             203
#define D_ATC_CRSM_COMMAND_SET_DATA                  219

#define D_ATC_NSET_AT_HEADER_SPACE               "AT_HEADER_SPACE"
#define D_ATC_NSET_SIM_STATE_RPTFLG              "SIM_RPT_FLG"

#ifdef LCS_MOLR_ENABLE
#define D_ATC_CMOLRG_MAX_SIZE                4000

//+CMOLG XML element
#define   LCS_XML_ELEMENT_HEAD                   "<?xml version=\"1.0\" ?>"
#define   LCS_XML_ELEMEN_LCATION_PARAM           "location_parameters"
#define   LCS_XML_ELEMEN_TIME                    "time"
#define   LCS_XML_ELEMEN_DIRECTION               "direction"
#define   LCS_XML_ELEMEN_SHAPE_DATA              "shape_data"
#define   LCS_XML_ELEMEN_ELLIP_POINT             "ellipsoid_point"
#define   LCS_XML_ELEMEN_ELLIP_UNCERT_CIRCLE     "ellipsoid_point_uncert_circle"
#define   LCS_XML_ELEMEN_ELLIP_UNCERT_ELLIP      "ellipsoid_point_uncert_ellipse"
#define   LCS_XML_ELEMEN_POLYGON                 "polygon"
#define   LCS_XML_ELEMEN_ELLIP_POINT_ALT         "ellipsoid_point_alt"
#define   LCS_XML_ELEMEN_ELLIP_POINT_ALT_UNCET   "ellipsoid_point_alt_uncertellipse"
#define   LCS_XML_ELEMEN_ELLIP_POINT_ARC         "ellips_arc"
#define   LCS_XML_ELEMEN_COORDINATE              "coordinate"
#define   LCS_XML_ELEMEN_LATITUDE                "latitude"
#define   LCS_XML_ELEMEN_NORTH                   "north"
#define   LCS_XML_ELEMEN_DEGRESS                 "degrees"
#define   LCS_XML_ELEMEN_LONGITUDE               "longitude"
#define   LCS_XML_ELEMEN_UNCERT_CIRCLE           "uncert_circle"
#define   LCS_XML_ELEMEN_UNCERT_ELLIP            "uncert_ellipse"
#define   LCS_XML_ELEMEN_UNCERT_SEMI_MAJOR       "uncert_semi_major"
#define   LCS_XML_ELEMEN_UNCERT_SEMI_MIJOR       "uncert_semi_minor"
#define   LCS_XML_ELEMEN_ORIENT_MIJOR            "orient_major"
#define   LCS_XML_ELEMEN_CONFIDENCE              "confidence"
#define   LCS_XML_ELEMEN_ALT                     "altitude"
#define   LCS_XML_ELEMEN_HEIGHT_ABOVE_SURFACE    "height_above_surface"
#define   LCS_XML_ELEMEN_HEIGHT                  "height"
#define   LCS_XML_ELEMEN_UNCERT_ALT              "uncert_alt"
#define   LCS_XML_ELEMEN_INNER_RAD               "inner_rad"
#define   LCS_XML_ELEMEN_UNCERT_RAD              "uncert_rad"
#define   LCS_XML_ELEMEN_OFFSET_ANGLE            "offset_angle"
#define   LCS_XML_ELEMEN_INC_ANGLE               "included_angle"

//velocity_data
#define   LCS_XML_ELEMEN_VEL_DATA                "velocity_data"
#define   LCS_XML_ELEMEN_HOR_VEL                 "hor_velocity"
#define   LCS_XML_ELEMEN_VERT_VEL                "vert_velocity"
#define   LCS_XML_ELEMEN_VERT_VEL_DIRECT         "vert_velocity_direction"
#define   LCS_XML_ELEMEN_HOR_UNCERT              "hor_uncert"
#define   LCS_XML_ELEMEN_VERT_UNCERT             "vert_uncert"
#endif

#define ATC_CFUN_COMMAND_PREFIX                     "CFUN"
#define ATC_CGATT_COMMAND_PREFIX                    "CGATT"
#define ATC_CGACT_COMMAND_PREFIX                    "CGACT"
#define ATC_CGDCONT_COMMAND_PREFIX                  "CGDCONT"
#define ATC_CGCONTRDP_COMMAND_PREFIX                "CGCONTRDP"
#define ATC_CREG_COMMAND_PREFIX                     "CREG"
#define ATC_CGREG_COMMAND_PREFIX                    "CGREG"
#define ATC_CEREG_COMMAND_PREFIX                    "CEREG"
#define ATC_CSCON_COMMAND_PREFIX                    "CSCON"
#define ATC_CMEE_COMMAND_PREFIX                     "CMEE"
#define ATC_CGSN_COMMAND_PREFIX                     "CGSN"
#define ATC_CESQ_COMMAND_PREFIX                     "CESQ"
#define ATC_CSQ_COMMAND_PREFIX                      "CSQ"
#define ATC_CGPADDR_COMMAND_PREFIX                  "CGPADDR"

#define ATC_CEDRXRDP_COMMAND_PREFIX                 "CEDRXRDP"
#define ATC_CPSMS_COMMAND_PREFIX                    "CPSMS"
#define ATC_CNEC_COMMAND_PREFIX                     "CNEC"
#define ATC_CLAC_COMMAND_PREFIX                     "CLAC"
#define ATC_CGEREP_COMMAND_PREFIX                   "CGEREP"
#define ATC_COPS_COMMAND_PREFIX                     "COPS"
#define ATC_CTZR_COMMAND_PREFIX                     "CTZR"
#define ATC_CTZU_COMMAND_PREFIX                     "CTZU"
#define ATC_CGPIAF_COMMAND_PREFIX                   "CGPIAF"
#define ATC_CPOL_COMMAND_PREFIX                     "CPOL"
#define ATC_COPN_COMMAND_PREFIX                     "COPN"
#define ATC_CSCS_COMMAND_PREFIX                     "CSCS"
#define ATC_CGMI_COMMAND_PREFIX                     "CGMI"
#define ATC_CGMM_COMMAND_PREFIX                     "CGMM"
#define ATC_CGMR_COMMAND_PREFIX                     "CGMR"

#ifdef ESM_DEDICATED_EPS_BEARER
#define ATC_CGDSCONT_COMMAND_PREFIX                 "CGDSCONT"
#define ATC_CGSCONTRDP_COMMAND_PREFIX               "CGSCONTRDP"
#define ATC_CGEQOS_COMMAND_PREFIX                   "CGEQOS"
#define ATC_CGEQOSRDP_COMMAND_PREFIX                "CGEQOSRDP"
#define ATC_CGTFT_COMMAND_PREFIX                    "CGTFT"
#define ATC_CGTFTRDP_COMMAND_PREFIX                 "CGTFTRDP"
#define ATC_CGCMOD_COMMAND_PREFIX                   "CGCMOD"
#endif

#define ATC_CIPCA_COMMAND_PREFIX                    "CIPCA"
#define ATC_CGAUTH_COMMAND_PREFIX                   "CGAUTH"
#define ATC_CEER_COMMAND_PREFIX                     "CEER"
#define ATC_CNUM_COMMAND_PREFIX                     "CNUM"
#define ATC_CEDRXS_COMMAND_PREFIX                   "CEDRXS"
#ifndef _FLASH_OPTIMIZE_
#define ATC_CSODCP_COMMAND_PREFIX                   "CSODCP"
#define ATC_CRTDCP_COMMAND_PREFIX                   "CRTDCP"
#define ATC_CCIOTOPT_COMMAND_PREFIX                 "CCIOTOPT"
#define ATC_CNMPSD_COMMAND_PREFIX                   "CNMPSD"
#define ATC_CACDC_COMMAND_PREFIX                    "CACDC"
#endif
#define ATC_CGAPNRC_COMMAND_PREFIX                  "CGAPNRC"

#define ATC_CIMI_COMMAND_PREFIX                     "CIMI"
#define ATC_CSIM_COMMAND_PREFIX                     "CSIM"
#define ATC_CCHO_COMMAND_PREFIX                     "CCHO"
#define ATC_CCHC_COMMAND_PREFIX                     "CCHC"
#define ATC_CGLA_COMMAND_PREFIX                     "CGLA"
#define ATC_CRSM_COMMAND_PREFIX                     "CRSM"
#define ATC_CPWD_COMMAND_PREFIX                     "CPWD"
#define ATC_CPIN_COMMAND_PREFIX                     "CPIN"
#define ATC_CLCK_COMMAND_PREFIX                     "CLCK"
#define ATC_CPINR_COMMAND_PREFIX                    "CPINR"
#ifdef LTE_SMS_FEATURE
#define ATC_CSMS_COMMAND_PREFIX                     "CSMS"
#define ATC_CMGF_COMMAND_PREFIX                     "CMGF"
#define ATC_CPMS_COMMAND_PREFIX                     "CPMS"
#define ATC_CSCA_COMMAND_PREFIX                     "CSCA"
#define ATC_CSMP_COMMAND_PREFIX                     "CSMP"
#define ATC_CNMI_COMMAND_PREFIX                     "CNMI"
#define ATC_CMGW_COMMAND_PREFIX                     "CMGW"
#define ATC_CMGR_COMMAND_PREFIX                     "CMGR"
#define ATC_CMGD_COMMAND_PREFIX                     "CMGD"
#define ATC_CMGL_COMMAND_PREFIX                     "CMGL"
#define ATC_CMSS_COMMAND_PREFIX                     "CMSS"
#define ATC_CMGS_COMMAND_PREFIX                     "CMGS"
#define ATC_CNMA_COMMAND_PREFIX                     "CNMA"
#define ATC_CMGC_COMMAND_PREFIX                     "CMGC"
#define ATC_CMMS_COMMAND_PREFIX                     "CMMS"
#define ATC_CSCB_COMMAND_PREFIX                     "CSCB"
#define ATC_CSDH_COMMAND_PREFIX                     "CSDH"
#define ATC_QCMGS_COMMAND_PREFIX                    "QCMGS"
#define ATC_QCMGR_COMMAND_PREFIX                    "QCMGR"
#define ATC_CGSMS_COMMAND_PREFIX                    "CGSMS"
#endif

#define ATC_CMOLR_COMMAND_PREFIX                    "CMOLR"
#define ATC_GMI_COMMAND_PREFIX                      "GMI"
#define ATC_GMM_COMMAND_PREFIX                      "GMM"
#define ATC_GMR_COMMAND_PREFIX                      "GMR"
#define ATC_GSN_COMMAND_PREFIX                      "GSN"
#define ATC_RAI_COMMAND_PREFIX                      "RAI"
#ifdef CSG_FEATURE

#define ATC_QCSG_COMMAND_PREFIX                     "QCSG"
#endif
#define ATC_QICSGP_COMMAND_PREFIX                   "QICSGP"
#define ATC_QIACT_COMMAND_PREFIX                    "QIACT"
#define ATC_QIACTEX_COMMAND_PREFIX                  "QIACTEX"
#define ATC_QIDEACT_COMMAND_PREFIX                  "QIDEACT"
#define ATC_QIDEACTEX_COMMAND_PREFIX                "QIDEACTEX"
#define ATC_QGSN_COMMAND_PREFIX                     "QGSN"
#define ATC_QCCID_COMMAND_PREFIX                    "QCCID"
#define ATC_QSPN_COMMAND_PREFIX                     "QSPN"
#define ATC_QENG_COMMAND_PREFIX                     "QENG"
#define ATC_QINISTAT_COMMAND_PREFIX                 "QINISTAT"
#define ATC_QSIMDET_COMMAND_PREFIX                  "QSIMDET"
#define ATC_QSIMSTAT_COMMAND_PREFIX                 "QSIMSTAT"
#define ATC_QNWINFO_COMMAND_PREFIX                  "QNWINFO"
#define ATC_QCSQ_COMMAND_PREFIX                     "QCSQ"
#define ATC_QGDCNT_COMMAND_PREFIX                   "QGDCNT"
#define ATC_QAUGDCNT_COMMAND_PREFIX                 "QAUGDCNT"
#define ATC_QCELL_COMMAND_PREFIX                    "QCELL"
#define ATC_QCELLEX_COMMAND_PREFIX                  "QCELLEX"
#define ATC_QPINC_COMMAND_PREFIX                    "QPINC"
#define ATC_QBLACKCELL_COMMAND_PREFIX               "QBLACKCELL"
#define ATC_QBLACKCELLCFG_COMMAND_PREFIX            "QBLACKCELLCFG"
#define ATC_QSIMSWITCH_COMMAND_PREFIX               "QSIMSWITCH"
#define ATC_QCFG_COMMAND_PREFIX                     "QCFG"
#if USR_CUSTOM9
#define ATC_QSCLKEX_COMMAND_PREFIX                  "ECSCLKEX"
#else
#define ATC_QSCLKEX_COMMAND_PREFIX                  "QSCLKEX"
#endif
#define ATC_NEARFCN_COMMAND_PREFIX                  "NEARFCN"
#define ATC_NCONFIG_COMMAND_PREFIX                  "NCONFIG"
#ifndef _FLASH_OPTIMIZE_
#define ATC_MPTWEDRXS_COMMAND_PREFIX                "MPTWEDRXS"
#define ATC_NPOPB_COMMAND_PREFIX                    "NPOPB"
#define ATC_NQPODCP_COMMAND_PREFIX                  "NQPODCP"
#define ATC_NSNPD_COMMAND_PREFIX                    "NSNPD"
#define ATC_NQPNPD_COMMAND_PREFIX                   "NQPNPD"
#define ATC_NRNPDM_COMMAND_PREFIX                   "NRNPDM"
#endif
#if !defined(_FLASH_OPTIMIZE_) || (USR_CUSTOM2)
#define ATC_NCIDSTATUS_COMMAND_PREFIX               "NCIDSTATUS"
#endif
#define ATC_NCPCDPR_COMMAND_PREFIX                  "NCPCDPR"
#define ATC_NGACTR_COMMAND_PREFIX                   "NGACTR"
#define ATC_NCSEARFCN_COMMAND_PREFIX                "NCSEARFCN"
#define ATC_NFPLMN_COMMAND_PREFIX                   "NFPLMN"
#define ATC_NL2THP_COMMAND_PREFIX                   "NL2THP"
#define ATC_NSET_COMMAND_PREFIX                     "NSET"
#define ATC_NPOWERCLASS_COMMAND_PREFIX              "NPOWERCLASS"
//#define ATC_NPIN_COMMAND_PREFIX                     "NPIN"
#define ATC_NTSETID_COMMAND_PREFIX                  "NTSETID"
//#define ATC_NIPINFO_COMMAND_PREFIX                  "NIPINFO"
#define ATC_NLOCKF_COMMAND_PREFIX                   "NLOCKF"
#define ATC_NITZ_COMMAND_PREFIX                     "NITZ"
#define ATC_NPREEARFCN_COMMAND_PREFIX               "NPREEARFCN"

#if USR_CUSTOM2
#define ATC_NUESTATS_COMMAND_PREFIX                 "NUESTATS"
#define ATC_MBAND_COMMAND_PREFIX                    "NBAND"
#define ATC_MCCID_COMMAND_PREFIX                    "NCCID"
#define ATC_MEID_COMMAND_PREFIX                     "CEID"
#else
#define ATC_MBAND_COMMAND_PREFIX                    "MBAND"
#define ATC_MCCID_COMMAND_PREFIX                    "MCCID"
#define ATC_MEID_COMMAND_PREFIX                     "MEID"
#endif
#define ATC_MNBIOTEVENT_COMMAND_PREFIX              "MNBIOTEVENT"
#define ATC_MLOCKFREQ_COMMAND_PREFIX                "MLOCKFREQ"
#define ATC_MCSEARFCN_COMMAND_PREFIX                "MCSEARFCN"
#define ATC_MUESTATS_COMMAND_PREFIX                 "MUESTATS"
#define ATC_MPSRAT_COMMAND_PREFIX                   "MPSRAT"
#define ATC_MEMMTIMER_COMMAND_PREFIX                "MEMMTIMER"
#define ATC_MUECONFIG_COMMAND_PREFIX                "MUECONFIG"

#define ATC_MWIFISCANCFG_COMMAND_PREFIX             "MWIFISCANCFG"
#define ATC_MWIFISCANSTART_COMMAND_PREFIX           "MWIFISCANSTART"
#define ATC_MWIFISCANSTOP_COMMAND_PREFIX            "MWIFISCANSTOP"
#define ATC_MWIFISCANQUERY_COMMAND_PREFIX           "MWIFISCANQUERY"
#define ATC_QWIFISCAN_COMMAND_PREFIX                "QWIFISCAN"
#define ATC_PSTEST_COMMAND_PREFIX                   "PSTEST"
#if PS_TEST_MODE
#define ATC_PSTESTMODE_COMMAND_PREFIX               "PSTESTMODE"
#endif

#define ATC_QTCNI_COMMAND_PREFIX                    "QTCNI"
#define ATC_NWDRX_COMMAND_PREFIX                    "NWDRX"
#ifdef SIMULATOR_UICC
#define ATC_SIMUUICC_COMMAND_PREFIX                 "SIMUUICC"
#endif
#define ATC_NWSDVOLT_COMMAND_PREFIX                 "NWSDVOLT"
#define ATC_SIMST_COMMAND_PREFIX                    "SIMST"
#define ATC_CCED_COMMAND_PREFIX                     "CCED"
#define ATC_CEMODE_COMMAND_PREFIX                   "CEMODE"
#define ATC_ICCID_COMMAND_PREFIX                    "ICCID"
#define ATC_BAND_COMMAND_PREFIX                     "BAND"
#define ATC_BANDIND_COMMAND_PREFIX                  "BANDIND"
#define ATC_QCELLINFO_COMMAND_PREFIX                "QCELLINFO"
#define ATC_QINDCFG_COMMAND_PREFIX                  "QINDCFG"
#define ATC_CIDACT_COMMAND_PREFIX                   "CIDACT"
#define ATC_QDSIM_COMMAND_PREFIX                    "QDSIM"
#define ATC_CUSD_COMMAND_PREFIX                     "CUSD"
#define ATC_QEHPLMN_COMMAND_PREFIX                  "QEHPLMN"
#if USR_CUSTOM12
#define ATC_CCID_COMMAND_PREFIX                     "CCID"
#define ATC_QMUXCFG_COMMAND_PREFIX                  "QMUXCFG"
#define ATC_QIFGCNT_COMMAND_PREFIX                  "QIFGCNT"
#define ATC_QPDPTIMER_COMMAND_PREFIX                "QPDPTIMER"
#define ATC_QPPPTIMER_COMMAND_PREFIX                "QPPPTIMER"
#define ATC_CGATTEX_COMMAND_PREFIX                  "CGATTEX"
#define ATC_QIREGAPP_COMMAND_PREFIX                 "QIREGAPP"
#endif
#define ATC_ECSIMCFG_COMMAND_PREFIX                 "ECSIMCFG"
#ifdef SIB16_FEATURE
#define ATC_QUTCTIME_COMMAND_PREFIX                 "QUTCTIME"
#endif

#define str2(x)                                 #x
#define str(x)                                  str2(x)

#define D_ATC_CID_RANGE                         str(D_ATC_MIN_CID)"-"str(D_ATC_CID_MAX_INDEX)
#define D_ATC_OK_RSP                            "OK"
//cmd handle format
#define D_ATC_CMD_CID_RANGE                     "%1d["D_ATC_CID_RANGE"]"
#define D_ATC_CMD_CID_ONCE_RANGE                "%1d("D_ATC_CID_RANGE")"
#define D_ATC_CMD_EARFCN_RANGE                  "%4d(-262143)"
#if USR_CUSTOM2
#define D_ATC_CMD_CFUN_FORMAT                   "%1d(0|1|5)"
#define D_ATC_CMD_CSCON_FORMAT                  "%1d(-1)"
#define D_ATC_CMD_CGEREP_FORMAT                 "%1d(-1)"
#define D_ATC_CMD_NUESTATS_FORMAT               "%12p"
#define D_ATC_CMD_CGDCONT_FORMAT                "%1d("D_ATC_CID_RANGE"),%7c,%101c,,%1d[-0],%1d[-0],,,,,%1d[-1]"
#define D_ATC_CMD_CGTFT_FORMAT                  "%1d("D_ATC_CID_RANGE"),%1d[1-6],%1d[],%131p,%2d[],%16p,%16p,%8h,%11p,%5h,%1d[-3],%131p"
#define D_ATC_CMD_CMGF_FORMAT                   "%1d(-0)"
#else
#define D_ATC_CMD_CFUN_FORMAT                   "%1d(0|1|4|5),%1d[-1]"
#define D_ATC_CMD_CSCON_FORMAT                  "%1d(-3)"
#define D_ATC_CMD_CGEREP_FORMAT                 "%1d(-2),%1d[-1]"
#define D_ATC_CMD_CGDCONT_FORMAT                "%1d("D_ATC_CID_RANGE"),%7c,%101c,,%1d[-0],%1d[-0],,,,,%1d[-1],%1d[-1]"
#define D_ATC_CMD_CGTFT_FORMAT                  "%1d("D_ATC_CID_RANGE"),%1d[1-16],%1d[],%131p,%2d[],%16p,%16p,%8h,%11p,%5h,%1d[-3],%131p"
#define D_ATC_CMD_CMGF_FORMAT                   "%1d(-1)"
#endif
#define D_ATC_CMD_CGATT_FORMAT                  "%1d(-1)"
#define D_ATC_CMD_CGACT_FORMAT                  "%1d(-1)"
#define D_ATC_CMD_CREG_FORMAT                   "%1d(-3)"
#define D_ATC_CMD_CGREG_FORMAT                  "%1d(-2)"
#define D_ATC_CMD_CEREG_FORMAT                  "%1d(-5)"
#define D_ATC_CMD_CMEE_FORMAT                   "%1d(-2)"
#define D_ATC_CMD_CGSN_FORMAT                   "%1d(-3)"
#define D_ATC_CMD_CEDRXS_FORMAT                 "%1d(-3),%1d[4-4],%5c,%5c"
#define D_ATC_CMD_CPSMS_FORMAT                  "%1d(-2),,,%9c,%9c"
#define D_ATC_CMD_CNEC_FORMAT                   "%1d(0|8|16|24)"
#define D_ATC_CMD_COPS_FORMAT                   "%1d(-4),%1d[-2],%21c,%1d[7-7]"
#define D_ATC_CMD_CTZR_FORMAT                   "%1d(-3)"
#if VER_CM
#define D_ATC_CMD_CTZU_FORMAT                   "%1d(-2)"
#else
#define D_ATC_CMD_CTZU_FORMAT                   "%1d(0|1|3)"
#endif
#define D_ATC_CMD_CGPIAF_FORMAT                 "%1d[-1],%1d[-1],%1d[-1],%1d[-1]"
#define D_ATC_CMD_CPOL_FORMAT                   "%1d[1-],%1d[-2],%21p,%1d[-1],%1d[-1],%1d[-1],%1d[-1]"
#define D_ATC_CMD_CSCS_FORMAT                   "%21p"
#define D_ATC_CMD_CGAPNRC_FORMAT                "%1d("D_ATC_CID_RANGE")"
#define D_ATC_CMD_CIPCA_FORMAT                  "%1d(3-3),%1d[-1]"
#define D_ATC_CMD_CGAUTH_FORMAT                 "%1d("D_ATC_CID_RANGE"),%1d[-2],%17c,%17c"
#define D_ATC_CMD_CCIOTOPT_FORMAT               "%1d(-3),%1d[0-3],%1d[0-2]"
#ifdef ESM_DEDICATED_EPS_BEARER
#define D_ATC_CMD_CGDSCONT_FORMAT               "%1d("D_ATC_CID_RANGE"),%1d["D_ATC_CID_RANGE"],%1d[-0],%1d[-0]"
#define D_ATC_CMD_CGEQOS_FORMAT                 "%1d("D_ATC_CID_RANGE"),%1d[-254],%4d[-10000000],%4d[-10000000],%4d[-10000000],%4d[-10000000]"
#endif
#define D_ATC_CMD_CSODCP_FORMAT                 "%1d("D_ATC_CID_RANGE"),%2d(-1600),%3200c,%1d[-2],%1d[-1],%1d[1-]"
#define D_ATC_CMD_CRTDCP_FORMAT                 "%1d(-1)"
#define D_ATC_CMD_CACDC_FORMAT                  "%33p(),%65p(),%1d(-1)"
#define D_ATC_CMD_CSIM_FORMAT                   "%2d(8-522),%523p()"
#define D_ATC_CMD_CCHO_FORMAT                   "%33p()"
#define D_ATC_CMD_CCHC_FORMAT                   "%1d(1-19)"
#define D_ATC_CMD_CGLA_FORMAT                   "%1d(1-19),%2d(8-522),%523p()"
#define D_ATC_CMD_CRSM_FORMAT                   "%1d(),%4d[1-65535],%1d[],%1d[],%1d[],%523p,%13p"
#define D_ATC_CMD_CPWD_FORMAT                   "%3p(),%9p(),%9p()"
#define D_ATC_CMD_CPIN_FORMAT                   "%9c(),%9c"
#define D_ATC_CMD_CLCK_FORMAT                   "%3p(),%1d(-2),%9p"
#define D_ATC_CMD_CPINR_FORMAT                  "%17c()"

#define D_ATC_CMD_GSN_FORMAT                    "%1d(-1)"
#define D_ATC_CMD_RAI_FORMAT                    "%1d()"
#define D_ATC_CMD_NCSG_FORMAT                   "%1d(-2),%4d[-0xEFFFFFF],%7p"
#define D_ATC_CMD_QICSGP_FORMAT                 "%1d("D_ATC_CID_RANGE"),%1d[1-3],%101c,%17c,%17c,%1d[-3]"
#define D_ATC_CMD_QENG_FORMAT                   "%21p()"
#define D_ATC_CMD_QSIMDET_FORMAT                "%1d(-1),%1d(-1)"
#define D_ATC_CMD_QSIMSTAT_FORMAT               "%1d(-1)"
#define D_ATC_CMD_QCSQ_FORMAT                   "%1d(-1)"
#define D_ATC_CMD_QGDCNT_FORMAT                 "%1d(-1)"
#define D_ATC_CMD_QAUGDCNT_FORMAT               "%2d()"
#define D_ATC_CMD_QCELLEX_FORMAT                "%1d(-1)"
#define D_ATC_CMD_QPINC_FORMAT                  "%4p()"
#define D_ATC_CMD_QBLACKCELL_FORMAT             "%1d(-1),%1d(2-2),%151p()"
#define D_ATC_CMD_QBLACKCELLCFG_FORMAT          "%1d(-1)"
#define D_ATC_CMD_QSIMSWITCH_FORMAT             "%1d(-1)"
#define D_ATC_CMD_QCFG_FORMAT                   "%31p(),%5c,%19c,%23c,%2c"
#define D_ATC_CMD_QCFG_BAND_FORMAT              "%5p,%9p"
#define D_ATC_CMD_QCFG_PARAM1_FORMAT            "%1d()"
#define D_ATC_CMD_QSCLKEX_FORMAT                "%1d(-1),%1d[1-50],%2d[-600]"
#define D_ATC_CMD_NEARFCN_FORMAT                "%1d(-0),%2d(),%4h[-0x1F7]"
#define D_ATC_CMD_NCONFIG_FORMAT                "%39p(),%10p()"
#define D_ATC_CMD_NCONFIG_PARAM2_FORMAT         "%2d()"
#define D_ATC_CMD_NL2THP_FORMAT                 "%1d(-1),%1d[]"
#define D_ATC_CMD_NSET_FORMAT                   "%20p(),%11c,%9c"
#define D_ATC_CMD_NSET_PARAM2_FORMAT            "%4d()"
#define D_ATC_CMD_NSET_PARAM3_FORMAT            "%4d()"
#define D_ATC_CMD_NPOWERCLASS_FORMAT            "%2d(1-256),%1d()"
#define D_ATC_CMD_NPTWEDRXS_FORMAT              "%1d(-3),%1d[4-4],%5c,%5c"
//#define D_ATC_CMD_NPIN_FORMAT                   "%1d(-4),%9p,%9p"
#define D_ATC_CMD_NTSETID_FORMAT                "%1d(1-1),%16p()"
#define D_ATC_CMD_NGACTR_FORMAT                 "%1d(-1)"
#define D_ATC_CMD_NPOPB_FORMAT                  "%1d(-2),%7p,%1d[],%4d[-262143],%2d[]"
// #define D_ATC_CMD_NIPINFO_FORMAT                "%1d(-1)"
#define D_ATC_CMD_NSNPD_FORMAT                  "%1d("D_ATC_CID_RANGE"),%2d(-1358),%2717c,%1d[-2],%1d[-1],%1d[1-]"
#define D_ATC_CMD_NRNPDM_FORMAT                 "%1d(-1)"
#define D_ATC_CMD_NCPCDPR_FORMAT                "%1d(-1),%1d(-1)"
#define D_ATC_CMD_NLOCKF_FORMAT                 "%1d(-2)"
#define D_ATC_CMD_NITZ_FORMAT                   "%1d(0|1|3),%1d[-1]"
#define D_ATC_CMD_NPREEARFCN_FORMAT             "%1d(-4),%4d[-262143]"
#define D_ATC_CMD_NBAND_FORMAT                  "%1d()"
#define D_ATC_CMD_MNBIOTEVENT_FORMAT            "%1d(-1),%1d(1-1)"
#define D_ATC_CMD_MLOCKFREQ_FORMAT              "%1d(-2)"
#define D_ATC_CMD_MLOCKFREQ_PARAM1_FORMAT       "%2d[-503]"
#define D_ATC_CMD_MCSEARFCN_FORMAT              "%1d(-0)"
#define D_ATC_CMD_MUESTATS_FORMAT               "%10p()"
#define D_ATC_CMD_MPSRAT_FORMAT                 "%4p()"
#define D_ATC_CMD_MEMMTIMER_FORMAT              "%1d(-1)"
#define D_ATC_CMD_MUECONFIG_FORMAT              "%21p(),%11c,%100c"
#define D_ATC_CMD_MWIFISCANCFG_FORMAT           "%21p(),%1d"
#define D_ATC_CMD_MWIFISCANSTART_FORMAT         "%1d(1-3),%1d[10-60],%33p,%1d[]"
#define D_ATC_CMD_MWIFISCANQUERY_FORMAT         "%1d(-1)"
#define D_ATC_CMD_QWIFISCAN_FORMAT              "%4d[4000-255000],%1d[1-3],%1d[4-10],%1d[],%1d[-1]"
#define D_ATC_CMD_PSTEST_FORMAT                 "%1d(),%2d[-1600],%3201p"
#define D_ATC_CMD_PSTESTMODE_FORMAT             "%1d(-1),%2d[],%1d[-1]"
#define D_ATC_CMD_PCTTESTINFO_FORMAT            "%16c()"
#define D_ATC_CMD_SIMUUICC_FORMAT               "%20p(),%33c,%33c,%65c"
#define D_ATC_CMD_NWSDVOLT_FORMAT               "%2d()"
#define D_ATC_CMD_SMS_FORMAT                    "%1d(-3)"
#define D_ATC_CMD_CSMS_FORMAT                   "%1d(-1)"
#define D_ATC_CMD_CPMS_FORMAT                   "%3p,%3p,%3p"
#define D_ATC_CMD_CNMA_FORMAT                   "%1d(-2),%1d[]"
#define D_ATC_CMD_CSCA_FORMAT                   "%85c(),%1d[]"
#define D_ATC_CMD_CSMP_FORMAT                   "%1d(),%21c,%1d[],%1d[]"
#define D_ATC_CMD_CNMI_FORMAT                   "%1d(-3),%1d[-3],%1d[-3],%1d[-2],%1d[-1]"
#define D_ATC_CMD_CMGW_PDU_FORMAT               "%1d(7-164),%1d[-3]"
#define D_ATC_CMD_CMGW_TEXT_FORMAT              "%85c(),%1d[],%11c"
#define D_ATC_CMD_CMGR_FORMAT                   "%1d()"
#define D_ATC_CMD_CMGD_FORMAT                   "%1d(1-),%1d[-4]"
#if VER_CM
    #define D_ATC_CMD_CMGL_FORMAT               "%1d(-8)"
#else
    #define D_ATC_CMD_CMGL_FORMAT               "%1d(-4)"
#endif
#define D_ATC_CMD_CMGL_STATE_FORMAT             "%11p"
#define D_ATC_CMD_CMSS_FORMAT                   "%1d(1-),%85p,%1d[]"
#define D_ATC_CMD_CMGS_PDU_FORMAT               "%1d(7-164)"
#define D_ATC_CMD_CMGS_TEXT_FORMAT              "%85p,%1d[]"
#define D_ATC_CMD_CMGC_PDU_FORMAT               "%1d(8-175)"
#define D_ATC_CMD_CMGC_TEXT_FORMAT              "%1d(),%1d(),%1d[],%1d[],%85p,%1d[]"
#define D_ATC_CMD_CMMS_FORMAT                   "%1d(-2)"
#define D_ATC_CMD_CSCB_FORMAT                   "%1d(-1)"
#define D_ATC_CMD_CSDH_FORMAT                   "%1d(-1)"
#define D_ATC_CMD_QCMGS_FORMAT                  "%88p,%1d[],%1d[],%1d[],%1d[]"
#define D_ATC_CMD_QCMGR_FORMAT                  "%1d(1-)"
#define D_ATC_CMD_CMOLR_FORMAT                  "%1d(-3),%1d[-6],%1d[-1],%1d[-127],%1d[-1],%1d[-1],%1d[-127],%1d[-4],%1d[-1],%2d[1-],%2d[1-],%1d[1-127],%1d[-1],%101p,%101p"
#define D_ATC_CMD_CCED_FORMAT                   "%1d(-0),%1d(1|2|8)"
#define D_ATC_CMD_CEMODE_FORMAT                 "%1d(-3)"
#define D_ATC_CMD_QIACTEX_FORMAT                "%1d("D_ATC_CID_RANGE"),%1d[-1]"
#define D_ATC_CMD_QINDCFG_FORMAT                "%10p(),%1d[-1],%1d[-1]"
#define D_ATC_CMD_CIDACT_FORMAT                 "%1d("D_ATC_CID_RANGE"),%1d(-1)"
#define D_ATC_CMD_QDSIM_FORMAT                  "%1d(-1)"
#define D_ATC_CMD_CUSD_FORMAT                   "%1d(-2),%85p,%1d[]"
#define D_ATC_CMD_QCELLINFO_FORMAT              "%1d()"
#define D_ATC_CMD_QEHPLMN_FORMAT                "%1d(-1),%1d(-3),%105c"
#define D_ATC_CMD_ECSIMCFG_FORMAT               "%10p(),%1d[-1]"
#define D_ATC_CMD_CESQ_FORMAT                   "%d()"

#if (!AT_TEST_OFF)
//test cmd rsp
#define D_ATC_ADDR_AND_MASK_IP                      "(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255)"
#define D_ATC_ADDR_AND_MASK_IPV6                    "(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).\
(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).\
(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255).(0-255)"

#if USR_CUSTOM2
#define D_ATC_TEST_CMD_CFUN_RSP                     "(0,1)"
#define D_ATC_TEST_CMD_CEREG_RSP                    "(0,1,2,3,4,5)"
#define D_ATC_TEST_CMD_CSCON_RSP                    "(0-1)"
#define D_ATC_TEST_CMD_CESQ_RSP                     "(99),(99),(255),(255),(0-34,255),(0-97,255)"
#define D_ATC_TEST_CMD_CSQ_RSP                      "(0-31,99),(99)"
#define D_ATC_TEST_CMD_CGEREP_RSP                   "(0,1)"
#define D_ATC_TEST_CMD_CTZR_RSP                     "(0,1,2,3)"
#define D_ATC_TEST_CMD_CGEQOS_RSP                   "("D_ATC_CID_RANGE"),(0,1-4,75,5-9,79,128-254),(0-"str(D_ATC_DL_GBR_MAX)"),(0-"str(D_ATC_UL_GBR_MAX)"),(0-"str(D_ATC_DL_MBR_MAX)"),(0-"str(D_ATC_DL_MBR_MAX)")"
#define D_ATC_TEST_CMD_CGTFT_RSP                    "\"IP\",(1-6),(0-255),("D_ATC_ADDR_AND_MASK_IP"),(0-255),\
((0-65535).(0-65535)),((0-65535).(0-65535)),(0-4294967295),((0-255).(0-255)),,(0-3),("D_ATC_ADDR_AND_MASK_IP")\r\n\
+CGTFT:\"IPV6\",(1-6),(0-255),("D_ATC_ADDR_AND_MASK_IPV6"),(0-255),((0-65535).(0-65535)),((0-65535).(0-65535)),\
(0-4294967295),((0-255).(0-255)),(0-1048575),(0-3),("D_ATC_ADDR_AND_MASK_IPV6")\r\n\
+CGTFT:\"IPV4V6\",(1-6),(0-255),("D_ATC_ADDR_AND_MASK_IPV6"),(0-255),((0-65535).(0-65535)),((0-65535).(0-65535)),\
(0-4294967295),((0-255).(0-255)),(0-1048575),(0-3),("D_ATC_ADDR_AND_MASK_IPV6")"
#define D_ATC_TEST_CMD_CMGF_RSP                     "(0)"
#define D_ATC_TEST_CMD_CMMS_RSP                     "0-2"
#define D_ATC_TEST_CMD_NUESTATS_RSP                 "RADIO,CELL,BLER,THP,APPSMEM,SBAND,ALL"
#define D_ATC_TEST_CMD_NGACTR_RSP                   "(0-1)"
#define D_ATC_TEST_CMD_CGAUTH_RSP                   "("D_ATC_CID_RANGE"),(0-2),\"\",\"\""
#define D_ATC_TEST_CMD_CPINR_RSP                    "SIM PIN,SIM PUK,SIM PIN2,SIM PUK2"
#define D_ATC_TEST_CMD_CIPCA_RSP                    "(3),(0-1)"
#else
#define D_ATC_TEST_CMD_CFUN_RSP                     "(0,1,4,5),(0,1)"
#define D_ATC_TEST_CMD_CEREG_RSP                    "(0-5)"
#define D_ATC_TEST_CMD_CSCON_RSP                    "(0-3)"
#define D_ATC_TEST_CMD_CESQ_RSP                     "(0-63,99),(0-7,99),(255),(255),(0-34,255),(0-97,255)"
#define D_ATC_TEST_CMD_CSQ_RSP                      "(0-31,99),(0-7,99)"
#define D_ATC_TEST_CMD_CGEREP_RSP                   "(0-2),(0,1)"
#define D_ATC_TEST_CMD_CTZR_RSP                     "(0-3)"
#define D_ATC_TEST_CMD_CGEQOS_RSP                   "("D_ATC_CID_RANGE"),(0,1-4,5-9,75,79,128-254),(0-"str(D_ATC_DL_GBR_MAX)"),(0-"str(D_ATC_UL_GBR_MAX)"),(0-"str(D_ATC_DL_MBR_MAX)"),(0-"str(D_ATC_DL_MBR_MAX)")"
#define D_ATC_TEST_CMD_CGTFT_RSP                    "\"IP\",(1-16),(0-255),("D_ATC_ADDR_AND_MASK_IP"),(0-255),\
((0-65535).(0-65535)),((0-65535).(0-65535)),(0-4294967295),((0-255).(0-255)),,(0-3),("D_ATC_ADDR_AND_MASK_IP")\r\n\
+CGTFT:\"IPV6\",(1-16),(0-255),("D_ATC_ADDR_AND_MASK_IPV6"),(0-255),\
((0-65535).(0-65535)),((0-65535).(0-65535)),(0-4294967295),((0-255).(0-255)),(0-1048575),(0-3),("D_ATC_ADDR_AND_MASK_IPV6")\r\n\
+CGTFT:\"IPV4V6\",(1-16),(0-255),("D_ATC_ADDR_AND_MASK_IPV6"),(0-255),\
((0-65535).(0-65535)),((0-65535).(0-65535)),(0-4294967295),((0-255).(0-255)),(0-1048575),(0-3),("D_ATC_ADDR_AND_MASK_IPV6")"
#define D_ATC_TEST_CMD_CMGF_RSP                     "(0,1)"
#define D_ATC_TEST_CMD_CMMS_RSP                     "(0-2)"
#define D_ATC_TEST_CMD_NGACTR_RSP                   "(0,1)"
#define D_ATC_TEST_CMD_CGAUTH_RSP                   "("D_ATC_CID_RANGE"),(0-2),(0-16),(0-16)"
#define D_ATC_TEST_CMD_CPINR_RSP                    "(SIM PIN,SIM PUK,SIM PIN2,SIM PUK2)"
#define D_ATC_TEST_CMD_CIPCA_RSP                    "(3),(0,1)"
#endif
#define D_ATC_TEST_CMD_CGATT_RSP                    "(0,1)"
#if VER_QUEC && !USR_CUSTOM2
#define D_ATC_TEST_CMD_CGACT_RSP                    "(0,1),("D_ATC_CID_RANGE")"
#else
#define D_ATC_TEST_CMD_CGACT_RSP                    "(0,1)"
#endif
#if USR_CUSTOM2
#define D_ATC_TEST_CMD_CGDCONT_RSP                  "("D_ATC_CID_RANGE"),(\"IP\",\"IPV6\",\"IPV4V6\",\"Non-IP\"),,,(0),(0),,,,,(0,1)"
#elif VER_CM
#define D_ATC_TEST_CMD_CGDCONT_RSP                  "("D_ATC_CID_RANGE"),(\"IP\",\"IPV6\",\"IPV4V6\"),,,(0),(0),,,,,(0,1),(0,1)"
#else
#define D_ATC_TEST_CMD_CGDCONT_RSP                  "("D_ATC_CID_RANGE"),(\"IP\",\"IPV6\",\"IPV4V6\",\"Non-IP\"),,,(0),(0),,,,,(0,1),(0,1)"
#endif
#define D_ATC_TEST_CMD_CREG_RSP                     "(0-3)"
#define D_ATC_TEST_CMD_CGREG_RSP                    "(0-2)"
#define D_ATC_TEST_CMD_CMEE_RSP                     "(0-2)"
#define D_ATC_TEST_CMD_CGSN_RSP                     "(0-3)"
#define D_ATC_TEST_CMD_CPSMS_RSP                    "(0-2),,,(00000000-11111111),(00000000-11111111)"
#define D_ATC_TEST_CMD_CNEC_RSP                     "(0,8,16,24)"
#define D_ATC_TEST_CMD_CGPIAF_RSP                   "(0,1),(0,1),(0,1),(0,1)"
#define D_ATC_TEST_CMD_CSCS_RSP                     "(\"GSM\",\"IRA\",\"UCS2\",\"HEX\")"
#define D_ATC_TEST_CMD_CSODCP_RSP                   "("D_ATC_CID_RANGE"),("str(EPS_CSODCP_CPDATA_LENGTH_MAX)"),(0-2),(0,1),(1-255)"
#define D_ATC_TEST_CMD_CRTDCP_RSP                   "(0,1),("D_ATC_CID_RANGE"),(65535)"
#define D_ATC_TEST_CMD_CEDRXS_RSP                   "(0-3),(4),(\"0000-1101\"),(\"0000-1111\")"
#define D_ATC_TEST_CMD_MPTWEDRXS_RSP                "(0-3),(4),(\"0000-1111\"),(\"0000-1101\")"
#define D_ATC_TEST_CMD_CSMS_RSP                     "(0,1)"
#define D_ATC_TEST_CMD_CNMI_RSP                     "(0-3),(0-3),(0-3),(0-2),(0-1)"
#define D_ATC_TEST_CMD_CMGD_RSP                     "(1-255),(0-4)"
#if VER_CM
#define D_ATC_TEST_CMD_CMGL_RSP                     "(0-4,8)"
#define D_ATC_TEST_CMD_CMGL_STRRSP                  "(\"REC UNREAD\",\"REC READ\",\"STO UNSENT\",\"STO SENT\",\"ALL\",\"RESEND\")"
#define D_ATC_TEST_CMD_CTZU_RSP                     "(0-2)"
#else
#define D_ATC_TEST_CMD_CMGL_RSP                     "(0-4)"
#define D_ATC_TEST_CMD_CMGL_STRRSP                  "(\"REC UNREAD\",\"REC READ\",\"STO UNSENT\",\"STO SENT\",\"ALL\")"
#define D_ATC_TEST_CMD_CTZU_RSP                     "(0,1,3)"
#endif
#define D_ATC_TEST_CMD_CNMA_RSP                     "(0-2)"
#define D_ATC_TEST_CMD_CSCB_RSP                     "(0,1)"
#define D_ATC_TEST_CMD_CSDH_RSP                     "(0,1)"
#if VER_QUEC
#define D_ATC_TEST_CMD_CCHO_RSP                     "<dfname>"
#define D_ATC_TEST_CMD_CRSM_RSP                     "(176,178,192,214,220,242),(12037-28599),(0-255),(0-255),(0-255),<data>,<pathid>"
#endif
#define D_ATC_TEST_CMD_CCIOTOPT_RSP                 "(0,1,3),(0,2,3),(0-2)"
#define D_ATC_TEST_CMD_CPIN_RSP                     "(READY,SIM PIN,SIM PUK)"
#define D_ATC_TEST_CMD_CLCK_RSP                     "(\"SC\")"
#define D_ATC_TEST_CMD_CPWD_RSP                     "(\"SC\",4-8)"
#define D_ATC_TEST_CMD_NL2THP_RSP                   "(0,1),(0-255)"
#define D_ATC_TEST_CMD_CMOLR_RSP                    "(0-3),(0-6),(0,1),(0-127),(0,1),(0,1),(0-127),(0-4),(0,1),(1-65535),(1-65535),(1,2,4,8,16,32,64),(0,1),(),()"
#if !defined(_FLASH_OPTIMIZE_) || (USR_CUSTOM2)
#define D_ATC_TEST_CMD_NCIDSTATUS_RSP               "("D_ATC_CID_RANGE")"
#endif

//#define D_ATC_TEST_CMD_NIPINFO_RSP                  "(0,1)"
#define D_ATC_TEST_CMD_NQPODCP_RSP                  "("D_ATC_CID_RANGE")"
#define D_ATC_TEST_CMD_NSNPD_RSP                    "("D_ATC_CID_RANGE"),(1358),(0-2),(0,1),(1-255)"
#define D_ATC_TEST_CMD_NQPNPD_RSP                   "("D_ATC_CID_RANGE")"
#define D_ATC_TEST_CMD_CNEC_RSP                     "(0,8,16,24)"
#define D_ATC_TEST_CMD_NRNPDM_RSP                   "(0,1),("D_ATC_CID_RANGE"),(1358)"
#define D_ATC_TEST_CMD_NCPCDPR_RSP                  "(0,1),(0,1)"
#define D_ATC_TEST_CMD_CGPIAF_RSP                   "(0,1),(0,1),(0,1),(0,1)"
#define D_ATC_TEST_CMD_NLOCKF_RSP                   "(0-2)"
#define D_ATC_TEST_CMD_QICSGP_RSP                   "("D_ATC_CID_RANGE"),(1-3),<APN>,<username>,<password>,(0-3)"
#define D_ATC_TEST_CMD_QIACT_RSP                    "("D_ATC_CID_RANGE")"
#define D_ATC_TEST_CMD_QIACTEX_RSP                  "("D_ATC_CID_RANGE"),(0,1)"
#define D_ATC_TEST_CMD_QIDEACT_RSP                  "("D_ATC_CID_RANGE")"
#define D_ATC_TEST_CMD_QIDEACTEX_RSP                "("D_ATC_CID_RANGE")"
#define D_ATC_TEST_CMD_QENG_RSP                     "(\"servingcell\",\"neighbourcell\")"
#define D_ATC_TEST_CMD_GSN_RSP                      "(0,1)"
#define D_ATC_TEST_CMD_QINISTAT_RSP                 "(0-7)"
#define D_ATC_TEST_CMD_QSIMDET_RSP                  "(0,1),(0,1)"
#define D_ATC_TEST_CMD_QSIMSTAT_RSP                 "(0,1)"
#define D_ATC_TEST_CMD_QCSQ_RSP                     "(\"LTE\")"
#define D_ATC_TEST_CMD_CSCB_RSP                     "(0,1)"
#define D_ATC_TEST_CMD_CSDH_RSP                     "(0,1)"
#define D_ATC_TEST_CMD_QGDCNT_RSP                   "(0,1)"
#define D_ATC_TEST_CMD_QAUGDCNT_RSP                 "(0,30-65535)"
#define D_ATC_TEST_CMD_CSCS_RSP                     "(\"GSM\",\"IRA\",\"UCS2\",\"HEX\")"
#define D_ATC_TEST_CMD_CGSMS_RSP                    "(0,2,3)"
#define D_ATC_TEST_CMD_QCELLEX_RSP                  "(0,1)"
#define D_ATC_TEST_CMD_QPINC_RSP                    "(\"SC\",\"P2\")"
#define D_ATC_TEST_CMD_MCSEARFCN_RSP                "(0)"
#define D_ATC_TEST_CMD_MUESTATS_RSP                 "(radio,cell,bler,thp,sband,all)"
#define D_ATC_TEST_CMD_MPSRAT_RSP                   "(\"LTE\")"
#define D_ATC_TEST_CMD_MEMMTIMER_RSP                "(0,1)"
#define D_ATC_TEST_CMD_MWIFISCANCFG_RSP             "(\"hidden\",(0,1))\r\n+MWIFISCANCFG:(\"priority\",(0,1))\r\n+MWIFISCANCFG:(\"max\",(4-10))"
#define D_ATC_TEST_CMD_QWIFISCAN_RSP                "(4000-255000),(1-3),(4-10),(0-255),(0,1)"
#define D_ATC_TEST_CMD_QBLACKCELLCFG_RSP            "(0,1)"
#define D_ATC_TEST_CMD_QSIMSWICH_RSP                "(0,1)"
#define D_ATC_TEST_CMD_QSCLKEX_RSP                  "(0,1),(1-50),(0-600)"
#define D_ATC_TEST_CMD_NWSDVOLT_RSP                 "(0-5000,65535)"
#define D_ATC_TEST_CMD_CCED_RSP                     "(0),(1,2,8)"
#define D_ATC_TEST_CMD_MUECONFIG_RSP                "\"defapn\",,\r\n+MUECONFIG:\"autoconn\",(0,1)\r\n+MUECONFIG:\"relver\",(13,14)\r\n+MUECONFIG:\"emulticar\",(0,1)"
#define D_ATC_TEST_CMD_CEMODE_RSP                   "(0-3)"
#define D_ATC_TEST_CMD_QINDCFG_RSP                  "\"all\",(0,1),(0,1)"
#define D_ATC_TEST_CMD_QDSIM_RSP                    "(0,1)"
#define D_ATC_TEST_CMD_CUSD_RSP                     "(0-2)"
#define D_ATC_TEST_CMD_ECSIMCFG_RSP                 "\"SimSlot\",(0,1)"

#endif

#endif
