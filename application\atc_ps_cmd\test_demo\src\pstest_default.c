#if PS_TEST_MODE
#include "pstest.h"
#include "pstest_api.h"
#include "atc_ps.h"

UCHAR ps_test_AT_init()
{

    //ps_test_send_at_wait_urc("AT+NSET=BANDSCAN,0\r\n","\r\nOK",NULL,10);
    //ps_test_send_at_wait_urc("AT+NL2THP=0\r\n","\r\nOK",NULL,10);
    ps_test_send_at_wait_urc("AT+CGDCONT=2\r\n","\r\nOK",NULL,10);
    ps_test_send_at_wait_urc("AT+CGDCONT=3\r\n","\r\nOK",NULL,10);
    ps_test_send_at_wait_urc("AT+CGDCONT=4\r\n","\r\nOK",NULL,10);
    ps_test_send_at_wait_urc("AT+CGDSCONT=2\r\n","\r\nOK",NULL,10);
    ps_test_send_at_wait_urc("AT+CESQ=15\r\n","\r\nOK",NULL,10);
    ps_test_send_at_wait_urc("AT+NSET=PSRPT,1\r\n","\r\nOK",NULL,10);
    ps_test_send_at_wait_urc("AT+CIMI\r\n","\r\nOK",NULL,10);
    ps_test_send_at_wait_urc("AT+MCCID\r\n","\r\nOK",NULL,10);
    ps_test_send_at_wait_urc("AT+CGSN=1\r\n","\r\nOK",NULL,10);
    ps_test_send_at_wait_urc("AT+NSET=SWVER\r\n","\r\nOK",NULL,10);
    ps_test_send_at_wait_urc("AT+NSET=SECALG,15\r\n","\r\nOK",NULL,60);
    ps_test_send_at_wait_urc("AT+MLED=0,1\r\n","\r\nOK",NULL,60);
    ps_test_cfun0_api();
    ps_test_send_at_wait_urc("AT+DEBUG=GS_POWERSW,1\r\n","OK",NULL,10);
    return Test_True;
}

UCHAR (*const   ps_test_mode_list[])()=
{
    ps_test_AT_init,
    ps_test_idel_cfun01,
    ps_test_cops_plmn_search,
    // ps_test_freq_scan,
    // ps_test_up_perf,
    // ps_test_down_perf,
    ps_test_paging,
    ps_test_mutil_default_cid,
    ps_test_secondary_cid,
    ps_test_security_algorithm,
    ps_test_idle_ping_200,
    ps_test_connect_ping_200,
    // ps_test_idle_keep,
    NULL,
};

void ps_test_case_num_upd()
{
    g_softap_fac_nv->ucPSTestNum = g_PSTestDemoInfo.ucPSTestNum;
    //g_softap_fac_nv->ucPSTestTimers = g_PSTestDemoInfo.ucPSTestFailTimers; 
    SAVE_FAC_PARAM(ucPSTestNum);
    //SAVE_FAC_PARAM(ucPSTestTimers);
}

void ps_test_case_num_rest()
{
    /*获取历史用例信息，注：一般不会出现重启（人为干涉，死机）。*/

    g_PSTestDemoInfo.ucPSTestNum = g_softap_fac_nv->ucPSTestNum;
    //g_PSTestDemoInfo.ucPSTestFailTimers = g_softap_fac_nv->ucPSTestTimers;

    /*跳过上次执行的用例（正式测试用例从用例1开始，用例0为初始化）,
    避免用例过程中的死机导致测试状态未更新导致，用例死循环*/
    
    g_PSTestDemoInfo.ucPSTestNum++;
    //g_PSTestDemoInfo.ucPSTestFailTimers = 0;

    /*超过最后一条，重新开始用例*/
    
    if(ps_test_mode_list[g_PSTestDemoInfo.ucPSTestNum] == NULL )
    {
        g_PSTestDemoInfo.ucPSTestNum = 1;
        //g_PSTestDemoInfo.ucPSTestFailTimers = 0;
    }
    ps_test_case_num_upd();
}

void Ps_Test_Start(void* pMsg)
{
    unsigned char ucCaseResult = 0;
    unsigned char i = 0;
    unsigned char ucCaseTimers = 0;
    
    ps_test_wait_time(10);
    ps_test_mode_list[0]();
    ps_test_wait_time(10);

    // case running addr init 
    ps_test_case_num_rest();
    //ucCaseTimers = g_PSTestDemoInfo.ucPSTestFailTimers;
    i = g_PSTestDemoInfo.ucPSTestNum;
    xy_printf(0,ATC_AP_T,WARN_LOG,"[Ps_Test_Start] from %d",i);

    for(;ps_test_mode_list[i] != NULL ; )
    {
        osTimerStart(g_PSTestDemoInfo.tTestCaseTime, 3 * 3600 * 1000);
        xy_printf(0,ATC_AP_T,WARN_LOG,"Ps_TestCase_start:%d", i);

        ucCaseResult = ps_test_mode_list[i]();

        xy_printf(0,ATC_AP_T,WARN_LOG,"Ps_TestCase_end:%d, result:%d,fail timers:%d",
                        i, ucCaseResult, ucCaseTimers);
        if(osTimerIsRunning(g_PSTestDemoInfo.tTestCaseTime))
        {
            osTimerStop(g_PSTestDemoInfo.tTestCaseTime);
        }
        ps_test_cfun0_api();

        if(Test_True == ucCaseResult)
        {
            i++;
            ucCaseTimers = 0;
        }
        else
        {
            ucCaseTimers++;
            if(ucCaseTimers >= 3)
            {
                i++;
                ucCaseTimers = 0;
            }
        }

        if(ps_test_mode_list[i] == NULL)
        {
            i = 0;
            ucCaseTimers = 0;
            g_PSTestDemoInfo.ucPSTestNum = i;
            g_PSTestDemoInfo.ucPSTestFailTimers = ucCaseTimers;
            ps_test_case_num_upd();
            ps_test_wait_time(180);
            ps_test_send_at_wait_urc("AT+DEBUG=GS_POWERSW,0\r\n","OK",NULL,10);
            ps_test_send_at_wait_urc("AT+DEBUG=GS_POWERSW,1\r\n","OK",NULL,10);
            ps_test_send_at_wait_urc("AT+MLED=0,0\r\n","\r\nOK",NULL,60);
            ps_test_wait_time(3600 * 2);
        }
        else
        {
            g_PSTestDemoInfo.ucPSTestNum = i;
            g_PSTestDemoInfo.ucPSTestFailTimers = ucCaseTimers;
            ps_test_case_num_upd();
        }
    }
}


/********************task2*************
check urc
************************************************/
void Ps_Test_Msg_Check(unsigned char* pMsg)
{
    ST_TEST_AT_URC_INFO* pAT_urc;
    unsigned char       ucResult = Test_False;
    UCHAR  ucShareInfo = 0;

    pAT_urc = (ST_TEST_AT_URC_INFO*)pMsg;
    osMutexAcquire(g_PSTestDemoInfo.stTestShareInfo.Mutex, osWaitForever);
    ucShareInfo = g_PSTestDemoInfo.stTestShareInfo.ucShareInfo;
    //test not start,wait for sim ready
    if((ucShareInfo & PS_TEST_START_VALUE) == Test_False)
    {
        if((0 == strncmp("\r\n+CPIN: READY", pAT_urc->aucAtRspBuf, strlen("\r\n+CPIN: READY"))))
        {
            ucShareInfo = (ucShareInfo & ~PS_TEST_NEED_WAIT_AT) | (PS_TEST_START_VALUE);
            g_PSTestDemoInfo.stTestShareInfo.ucShareInfo = ucShareInfo;
            ps_test_sim_state_callback();
        }
        xy_printf(0,ATC_AP_T,WARN_LOG,"[ps_test:test_start]%s ,%d",pAT_urc->aucAtRspBuf, ucShareInfo);
        osMutexRelease(g_PSTestDemoInfo.stTestShareInfo.Mutex);
        AtcAp_Free(pAT_urc->aucAtRspBuf);
        return;
    }
    //test check at regular respond，if not get these respond， prove at port block
    if(ucShareInfo & PS_TEST_NEED_WAIT_OK)
    {
        if(((NULL != strstr(pAT_urc->aucAtRspBuf,"OK"))
            || (NULL != strstr(pAT_urc->aucAtRspBuf,"ERROR"))
            || (NULL != strstr(pAT_urc->aucAtRspBuf,"QPING")))
            && (ucShareInfo & PS_TEST_NEED_WAIT_OK))
        {
            ucShareInfo = ucShareInfo &(~ PS_TEST_NEED_WAIT_OK);
            g_PSTestDemoInfo.stTestShareInfo.ucShareInfo = ucShareInfo;
            if((NULL != strstr(pAT_urc->aucAtRspBuf,"ERROR")))
            {
                Ps_test_at_task_rls();
                xy_printf(0,ATC_AP_T,WARN_LOG,"[Ps_Test:error]%s ,%d",pAT_urc->aucAtRspBuf, ucShareInfo);
                osMutexRelease(g_PSTestDemoInfo.stTestShareInfo.Mutex);
                AtcAp_Free(pAT_urc->aucAtRspBuf);
                return;
            }
        }
    }
    //check ATcommand wait for respond
    if(((ucShareInfo & PS_TEST_NEED_WAIT_AT) != Test_False)
        && (NULL != g_PSTestDemoInfo.stTestWaitAtKey.aucAtRspBuf)
        && (NULL != strstr(pAT_urc->aucAtRspBuf, g_PSTestDemoInfo.stTestWaitAtKey.aucAtRspBuf )))
    {
        ucShareInfo = (ucShareInfo & (~PS_TEST_NEED_WAIT_AT)) | PS_TEST_GET_RIGHT_RCV;
        g_PSTestDemoInfo.stTestShareInfo.ucShareInfo = ucShareInfo;
        osMutexAcquire(g_PSTestDemoInfo.stTestMSGRcv.mutex, osWaitForever);
        if(g_PSTestDemoInfo.stTestMSGRcv.pRspBuf != NULL)
        {
            AtcAp_Free(g_PSTestDemoInfo.stTestMSGRcv.pRspBuf);
        }

        g_PSTestDemoInfo.stTestMSGRcv.pRspBuf = AtcAp_Malloc(pAT_urc->usRspLen + 1);
        AtcAp_MemCpy(g_PSTestDemoInfo.stTestMSGRcv.pRspBuf, pAT_urc->aucAtRspBuf, pAT_urc->usRspLen);

        osMutexRelease(g_PSTestDemoInfo.stTestMSGRcv.mutex);
        Ps_test_at_task_rls();
    }
    if((ucShareInfo & PS_TEST_NEED_WAIT_OK) == Test_False)
    {
        g_PSTestDemoInfo.ucATNoAnswtime = 0;
    }
    xy_printf(0,ATC_AP_T,WARN_LOG,"[Ps_Test:Msg]%s ,%d",pAT_urc->aucAtRspBuf, ucShareInfo);
    osMutexRelease(g_PSTestDemoInfo.stTestShareInfo.Mutex);
    AtcAp_Free(pAT_urc->aucAtRspBuf);
    return;
    
}

void Ps_Test_AT_Time_Out(unsigned char* pMsg)
{
    UCHAR  ucShareInfo = 0;
    
    ucShareInfo = Ps_test_get_share_info();

    if(ucShareInfo & PS_TEST_NEED_WAIT_OK)
    {
        g_PSTestDemoInfo.ucATNoAnswtime++;
        xy_printf(0,ATC_AP_T,WARN_LOG,"[Ps_Test_AT_Time_Out] wait OK flg = %d",g_PSTestDemoInfo.ucATNoAnswtime);
        if(g_PSTestDemoInfo.ucATNoAnswtime < 5)
        {
            Ps_test_at_task_rls();
        }
        else
        {
            xy_assert(0);
        }

    }
    else
    {
        Ps_test_at_task_rls();
    }
    return;
}

void Ps_Test_CASE_Time_Out(unsigned char* pMsg)
{
    xy_printf(0,ATC_AP_T,WARN_LOG,"[Ps_Test_CASE_Time_Out]");
    xy_Soft_Reset(SOFT_RB_BY_NRB);
    return;
}

#endif
