#pragma once

#include <stdint.h>
#include <stdbool.h>

/*******************************************************************************
 *                             Macro definitions                               *
 ******************************************************************************/
#if FLASH_LIMIT
/**
 * @brief 基础平台CME ERROR， 受CMEE命令控制
 */
#define AT_PLAT_CME_ERR(a)          at_plat_err_build(a, true, NULL, __LINE__)

/**
 * @brief 基础ERROR 不受AT+CMEE命令控制，仅返回ERROR
 */
#define AT_PLAT_ERR(a)              at_plat_err_build(a, false, NULL, __LINE__)

/**
 * @brief TCPIP命令ERROR 不受AT+CMEE命令控制，仅返回ERROR，同时记录当前错误码，用于QIGETERROR命令查询
 */
#define AT_TCPIP_ERR_BUILD(err)      atTcpipErrorBuild(err, NULL, __LINE__)

/**
* @brief 直接发送AT错误信息到对应的外设，该接口不会执行reset context操作
* @param err_no  错误码，8007、8003 @see AT_ERRNO_E
* @param srcFd  at_context上下文
* @note 一般用于farps中的处理，不执行reset context可以避免因多线程操作全局context导致返回的error信息不正确
*/
#define SEND_ERR_RSP_TO_TTY(err_no, srcFd) send_err_rsp_2_tty(err_no, srcFd, NULL, __LINE__)

#else /* FLASH_LIMIT */
/**
 * @brief 基础平台CME ERROR， 受CMEE命令控制
 */
#define AT_PLAT_CME_ERR(a)          at_plat_err_build(a, true, __FILE__, __LINE__)

/**
 * @brief 基础ERROR 不受AT+CMEE命令控制，仅返回ERROR
 */
#define AT_PLAT_ERR(a)              at_plat_err_build(a, false, __FILE__, __LINE__)

/**
 * @brief TCPIP命令ERROR 不受AT+CMEE命令控制，仅返回ERROR，同时记录当前错误码，用于QIGETERROR命令查询
 */
#define AT_TCPIP_ERR_BUILD(err)      atTcpipErrorBuild(err, __FILE__, __LINE__)

/**
* @brief 直接发送AT错误信息到对应的外设，该接口不会执行reset context操作
* @param err_no  错误码，8007、8003 @see AT_ERRNO_E
* @param srcFd  at_context上下文
* @note 一般用于farps中的处理，不执行reset context可以避免因多线程操作全局context导致返回的error信息不正确
*/
#define SEND_ERR_RSP_TO_TTY(err_no, srcFd) send_err_rsp_2_tty(err_no, srcFd, __FILE__, __LINE__)

#endif /* FLASH_LIMIT */

#if NET_LOG
#include "net_log.h"
#define ATCTRL_NET_LOG(format, ...)    net_log_record("ATCTL", NETLOG_WARN, format, ##__VA_ARGS__)
#define ATRECV_NET_LOG(format, ...)    net_log_record("ATRECV", NETLOG_WARN, format, ##__VA_ARGS__)
#else
#define ATCTRL_NET_LOG(format, ...)
#define ATRECV_NET_LOG(format, ...)
#endif

/*******************************************************************************
 *                       Global function declarations                          *
 ******************************************************************************/
/**
 * @brief 根据芯翼基础错误结果码获取厂家自定义结果码描述信息
 * @param  errno  @see @ref xy_ret_Status_t
 * @return 结果码描述信息，-1表示获取失败 
 */
char *get_plat_err_info(int err_no);

/**
 * @brief  根据错误码构造返回错误描述字符串，仅基础命令使用
 * @param  err_no  错误码 @see @ref xy_ret_Status_t
 * @param  bCme true:CME 错误码， false: 非CME 错误码, 仅返回ERROR
 * @param  file 文件名
 * @param  line 行号
 * @return 错误描述字符串
 */
char *at_plat_err_build(int err_no, bool bCme, char *file, int line);

/**
 * @brief tcpip命令错误码构造
 * @param err_no 错误码 @see @ref xy_ret_Status_t
 * @param file 文件名
 * @param line 行号
 * @return 错误描述字符串
 */
char *atTcpipErrorBuild(int err_no, char *file, uint32_t line);

 /**
 * @brief 发送错误信息到外设，该接口不会执行reset context操作
 * @param  err_no [IN] 错误码 @see @ref AT_ERRNO_E
 * @param  srcFd [IN] at source fd @see @ref AT_TTY_FD
 * @param  file [IN] 文件名, 一般使用__FILE__
 * @param  line [IN] 行号, 一般使用__LINE__
 */
 void send_err_rsp_2_tty(int err_no, int srcFd, char *file, int line);
