/**********************************************************************/
/**
 * @file yx_ia_fs.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-18
 * @version V1.0
 * @brief fs接口适配
 **********************************************************************/
#ifndef YX_IA_FS_H
#define YX_IA_FS_H

#include "yx_type.h"


/**
 * @brief 雅迅文件类型枚举
 * @ingroup ia_fs
 */
typedef enum{
    FILE_TYPE_REG        = 0, /**< regular file*/
    FILE_TYPE_DIR        = 1, /**< sub_directory*/
}YX_FILE_TYPE_E;

typedef VOID    *yx_fs_t;     /**< 雅迅文件指针 */
typedef VOID    *yx_dir_t;     /**< 雅迅目录指针 */

#define YX_MAX_FULL_NAME_LENGTH 255
/**
 * @brief 雅迅文件/目录信息结构体
 * @note 完全拷贝simcom的struct dirent_t, 需要进行人工核对
 */
typedef struct
{
    unsigned char type;
    long size;
    char name[YX_MAX_FULL_NAME_LENGTH];
} yx_dirent_t;

/**
 * @brief 打开文件
 * @ingroup ia_fs
 * @param[in] fullname 文件名
 * @param[in] mode 文件打开模式: rb wb ab rb+ wb+ ab+
 * @return yx_fs_t 雅迅文件指针
 * @retval 非NULL, 成功，返回雅迅文件指针
 * @retval NULL, 失败
 * @note 此调用获取的雅迅文件指针，需要正确使用'yx_ia_fs_fclose'进行资源回收
 */
yx_fs_t yx_ia_fs_fopen(const CHAR *fullname, const CHAR *mode);

/**
 * @brief 关闭文件
 * @ingroup ia_fs
 * @param[in] yx_handler 雅迅文件指针
 * @return INT32S
 * @retval RTN_OK   成功
 * @retval RTN_ERR  失败
 * @note 'yx_ia_fs_fopen'获取的雅迅文件指针，需要正确使用此调用进行资源回收
 */
INT32S yx_ia_fs_fclose(yx_fs_t yx_handler);

/**
 * @brief 读取文件
 * @ingroup ia_fs
 * @param[in] yx_handler 雅迅文件指针
 * @param[in] buf 缓冲区指针
 * @param[in] size 缓冲区大小
 * @return INT32S
 * @retval 0或正数 已读取的字节数
 * @retval 负数 发送错误
 */
INT32S yx_ia_fs_fread(yx_fs_t yx_handler, VOID *buf, INT32U size);

/**
 * @brief 写入文件
 * @ingroup ia_fs
 * @param[in] yx_handler 雅迅文件指针
 * @param[in] buf 缓冲区指针
 * @param[in] length 需要写入的数据长度
 * @return INT32S
 * @retval 0或正数 已发送的字节数
 * @retval 负数 发送错误
 */
INT32S yx_ia_fs_fwrite(yx_fs_t yx_handler, VOID *buf, INT32U length);

/**
 * @brief 由文件中读取一字符串
 * @ingroup ia_fs
 * @param[in] yx_handler 雅迅文件指针
 * @param[in] buf 缓冲区指针
 * @param[in] length 需要读取的数据长度
 * @return CHAR
 * @retval 成功返回buf
 * @retval 错误/文件结束且无内容返回NULL
 */
CHAR *yx_ia_fs_fgets(yx_fs_t yx_handler, CHAR *buf, INT32U length);

/**
 * @brief 同步文件
 * @ingroup ia_fs
 * @param[in] yx_handler 雅迅文件指针
 * @return INT32S
 * @retval RTN_OK   成功
 * @retval RTN_ERR  失败
 */
INT32S yx_ia_fs_fsync(yx_fs_t yx_handler);

/**
 * @brief 操作文件位置指针
 * @ingroup ia_fs
 * @param[in] yx_handler 雅迅文件指针
 * @param[in] offset 偏移量
 * @param[in] whence 偏移量的起始位置 0-文件头，1-文件当前位置, 2-文件尾
 * @return INT32S
 * @retval RTN_OK   成功
 * @retval RTN_ERR  失败
 */
INT32S yx_ia_fs_fseek(yx_fs_t yx_handler, INT32S offset, INT32S whence);

/**
 * @brief 文件更名
 * @ingroup ia_fs
 * @param[in] old_name 旧文件名
 * @param[in] new_name 新文件名
 * @return INT32S
 * @retval RTN_OK   成功
 * @retval RTN_ERR  失败
 */
INT32S yx_ia_fs_rename(const CHAR *old_name, const CHAR *new_name);

/**
 * @brief 移除文件
 * @ingroup ia_fs
 * @param[in] fullname 文件名
 * @return INT32S
 * @retval RTN_OK   成功
 * @retval RTN_ERR  失败
 */
INT32S yx_ia_fs_remove(const CHAR *fullname);

/**
 * @brief 读取文件信息
 * @ingroup ia_fs
 * @param[in] fullname 文件名
 * @param[out] dirent 返回雅迅文件/文件信息结构体
 * @return INT32S
 * @retval RTN_OK   成功
 * @retval RTN_ERR  失败
 */
INT32S yx_ia_fs_stat(const CHAR *fullname, yx_dirent_t *dirent);

/**
 * @brief 获取当前文件当前读写位置
 * @ingroup ia_fs
 * @param[in] yx_handler 雅迅文件指针
 * @return INT32S
 * @retval >= 0 当前读写位置
 * @retval RTN_ERR  失败
 */
INT32S yx_ia_fs_ftell(yx_fs_t yx_handler);

/**
 * @brief 打开目录
 * @ingroup ia_fs
 * @param[in] fullpath 目录名
 * @return yx_dir_t 雅迅目录指针
 * @retval 非NULL, 成功，返回雅迅目录指针
 * @retval NULL, 失败
 * @note 此调用获取的雅迅目录指针，需要正确使用'yx_ia_fs_closedir'进行资源回收
 */
yx_dir_t yx_ia_fs_opendir(const CHAR *fullpath);

/**
 * @brief 读取目录
 * @ingroup ia_fs
 * @param[in] dir 雅迅目录指针
 * @return yx_dirent_t 雅迅文件/文件信息结构体
 * @retval 非NULL, 成功，返回雅迅文件/文件信息结构体，不可修改
 * @retval NULL, 失败
 */
yx_dirent_t *yx_ia_fs_readdir(yx_dir_t dir);

/**
 * @brief 关闭目录
 * @ingroup ia_fs
 * @param[in] dir 雅迅目录指针
 * @return INT32S
 * @retval RTN_OK   成功
 * @retval RTN_ERR  失败
 */
INT32S yx_ia_fs_closedir(yx_dir_t dir);

/**
 * @brief 创建目录
 * @ingroup ia_fs
 * @param[in] fullpath 目录名
 * @return INT32S
 * @retval RTN_OK   成功
 * @retval RTN_ERR  失败
 * @note 内部文件夹权限固定为0755
 */
INT32S yx_ia_fs_createdir(const CHAR *fullpath);

/**
 * @brief 移除目录
 * @ingroup ia_fs
 * @param[in] fullpath 目录名
 * @return INT32S
 * @retval RTN_OK   成功
 * @retval RTN_ERR  失败
 */
INT32S yx_ia_fs_removedir(const CHAR *fullpath);


/**
 * @brief 获取指定盘的剩余空间
 * @ingroup ia_fs
 * @param[in] disk 目录名：("C:/" or "D:/)
 * @return long long
 * @retval >=0 剩余空间大小，单位字节
 */
long long yx_ia_fs_disk_free(CHAR *disk);

/**
 * @brief 获取指定盘的已使用空间
 * @ingroup ia_fs
 * @param[in] disk 目录名：("C:/" or "D:/)
 * @return long long
 * @retval >=0 已使用空间大小，单位字节
 */
long long yx_ia_fs_disk_used(CHAR *disk);

/**
 * @brief 获取指定盘的空间大小
 * @ingroup ia_fs
 * @param[in] disk 目录名：("C:/" or "D:/)
 * @return long long
 * @retval >=0 空间大小，单位字节
 */
long long yx_ia_fs_disk_size(CHAR *disk);

/**
 * @brief 文件系统恢复默认配置
 * @ingroup ia_fs
 * @return INT32S
 * @retval RTN_OK   成功
 * @retval RTN_ERR  失败
 * @note 恢复默认配置，删除所有系统无关文件和配置
 */
INT32S yx_ia_fs_restore_default_cfg();
#endif /* YX_IA_FS_H */