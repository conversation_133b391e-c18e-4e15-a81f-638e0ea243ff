# Makefile.in generated by automake 1.16.5 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@



VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
@GCC_ARMV5_TRUE@am__append_1 = -DPV_CPU_ARCH_VERSION=5 -DPV_COMPILER=1
@COMPILE_AS_C_TRUE@am__append_2 = -x c -std=c99
subdir = amrwb
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/libtool.m4 \
	$(top_srcdir)/m4/ltoptions.m4 $(top_srcdir)/m4/ltsugar.m4 \
	$(top_srcdir)/m4/ltversion.m4 $(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(amrwbinclude_HEADERS) \
	$(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_CLEAN_FILES = opencore-amrwb.pc
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libdir)" "$(DESTDIR)$(pkgconfigdir)" \
	"$(DESTDIR)$(amrwbincludedir)"
LTLIBRARIES = $(lib_LTLIBRARIES)
libopencore_amrwb_la_LIBADD =
am_libopencore_amrwb_la_OBJECTS = wrapper.lo agc2_amr_wb.lo \
	band_pass_6k_7k.lo dec_acelp_2p_in_64.lo dec_acelp_4p_in_64.lo \
	dec_alg_codebook.lo dec_gain2_amr_wb.lo deemphasis_32.lo \
	dtx_decoder_amr_wb.lo get_amr_wb_bits.lo \
	highpass_400hz_at_12k8.lo highpass_50hz_at_12k8.lo \
	homing_amr_wb_dec.lo interpolate_isp.lo isf_extrapolation.lo \
	isp_az.lo isp_isf.lo lagconceal.lo low_pass_filt_7k.lo \
	median5.lo mime_io.lo noise_gen_amrwb.lo normalize_amr_wb.lo \
	oversamp_12k8_to_16k.lo phase_dispersion.lo pit_shrp.lo \
	pred_lt4.lo preemph_amrwb_dec.lo pvamrwbdecoder.lo \
	pvamrwb_math_op.lo q_gain2_tab.lo qisf_ns.lo qisf_ns_tab.lo \
	qpisf_2s.lo qpisf_2s_tab.lo scale_signal.lo \
	synthesis_amr_wb.lo voice_factor.lo wb_syn_filt.lo \
	weight_amrwb_lpc.lo
libopencore_amrwb_la_OBJECTS = $(am_libopencore_amrwb_la_OBJECTS)
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/agc2_amr_wb.Plo \
	./$(DEPDIR)/band_pass_6k_7k.Plo \
	./$(DEPDIR)/dec_acelp_2p_in_64.Plo \
	./$(DEPDIR)/dec_acelp_4p_in_64.Plo \
	./$(DEPDIR)/dec_alg_codebook.Plo \
	./$(DEPDIR)/dec_gain2_amr_wb.Plo ./$(DEPDIR)/deemphasis_32.Plo \
	./$(DEPDIR)/dtx_decoder_amr_wb.Plo ./$(DEPDIR)/dummy.Plo \
	./$(DEPDIR)/get_amr_wb_bits.Plo \
	./$(DEPDIR)/highpass_400hz_at_12k8.Plo \
	./$(DEPDIR)/highpass_50hz_at_12k8.Plo \
	./$(DEPDIR)/homing_amr_wb_dec.Plo \
	./$(DEPDIR)/interpolate_isp.Plo \
	./$(DEPDIR)/isf_extrapolation.Plo ./$(DEPDIR)/isp_az.Plo \
	./$(DEPDIR)/isp_isf.Plo ./$(DEPDIR)/lagconceal.Plo \
	./$(DEPDIR)/low_pass_filt_7k.Plo ./$(DEPDIR)/median5.Plo \
	./$(DEPDIR)/mime_io.Plo ./$(DEPDIR)/noise_gen_amrwb.Plo \
	./$(DEPDIR)/normalize_amr_wb.Plo \
	./$(DEPDIR)/oversamp_12k8_to_16k.Plo \
	./$(DEPDIR)/phase_dispersion.Plo ./$(DEPDIR)/pit_shrp.Plo \
	./$(DEPDIR)/pred_lt4.Plo ./$(DEPDIR)/preemph_amrwb_dec.Plo \
	./$(DEPDIR)/pvamrwb_math_op.Plo ./$(DEPDIR)/pvamrwbdecoder.Plo \
	./$(DEPDIR)/q_gain2_tab.Plo ./$(DEPDIR)/qisf_ns.Plo \
	./$(DEPDIR)/qisf_ns_tab.Plo ./$(DEPDIR)/qpisf_2s.Plo \
	./$(DEPDIR)/qpisf_2s_tab.Plo ./$(DEPDIR)/scale_signal.Plo \
	./$(DEPDIR)/synthesis_amr_wb.Plo ./$(DEPDIR)/voice_factor.Plo \
	./$(DEPDIR)/wb_syn_filt.Plo ./$(DEPDIR)/weight_amrwb_lpc.Plo \
	./$(DEPDIR)/wrapper.Plo
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
CXXCOMPILE = $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS)
LTCXXCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CXXFLAGS) $(CXXFLAGS)
AM_V_CXX = $(am__v_CXX_@AM_V@)
am__v_CXX_ = $(am__v_CXX_@AM_DEFAULT_V@)
am__v_CXX_0 = @echo "  CXX     " $@;
am__v_CXX_1 = 
CXXLD = $(CXX)
CXXLINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CXXLD) $(AM_CXXFLAGS) \
	$(CXXFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CXXLD = $(am__v_CXXLD_@AM_V@)
am__v_CXXLD_ = $(am__v_CXXLD_@AM_DEFAULT_V@)
am__v_CXXLD_0 = @echo "  CXXLD   " $@;
am__v_CXXLD_1 = 
SOURCES = $(libopencore_amrwb_la_SOURCES) \
	$(nodist_EXTRA_libopencore_amrwb_la_SOURCES)
DIST_SOURCES = $(libopencore_amrwb_la_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
DATA = $(pkgconfig_DATA)
HEADERS = $(amrwbinclude_HEADERS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
am__DIST_COMMON = $(srcdir)/Makefile.in $(srcdir)/opencore-amrwb.pc.in \
	$(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPPFLAGS = @CPPFLAGS@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
ETAGS = @ETAGS@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAINT = @MAINT@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OPENCORE_AMRNB_VERSION = @OPENCORE_AMRNB_VERSION@
OPENCORE_AMRWB_VERSION = @OPENCORE_AMRWB_VERSION@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@

# Just set OC_BASE to the opencore root, or set AMR_BASE directly to
# a detached gsm_amr directory
OC_BASE = $(top_srcdir)/opencore
AMR_BASE = $(OC_BASE)/codecs_v2/audio/gsm_amr
DEC_DIR = $(AMR_BASE)/amr_wb/dec
DEC_SRC_DIR = $(DEC_DIR)/src
OSCL = $(top_srcdir)/oscl
AM_CFLAGS = -I$(OSCL) -I$(DEC_SRC_DIR) -I$(DEC_DIR)/include \
	-I$(AMR_BASE)/common/dec/include $(am__append_1) \
	$(am__append_2)
@COMPILE_AS_C_FALSE@libopencore_amrwb_la_LINK = $(CXXLINK) $(libopencore_amrwb_la_LDFLAGS)
@COMPILE_AS_C_TRUE@libopencore_amrwb_la_LINK = $(LINK) $(libopencore_amrwb_la_LDFLAGS)
@COMPILE_AS_C_TRUE@nodist_EXTRA_libopencore_amrwb_la_SOURCES = dummy.c
AM_CXXFLAGS = $(AM_CFLAGS)
amrwbincludedir = $(includedir)/opencore-amrwb
amrwbinclude_HEADERS = dec_if.h if_rom.h
pkgconfigdir = $(libdir)/pkgconfig
pkgconfig_DATA = opencore-amrwb.pc
lib_LTLIBRARIES = libopencore-amrwb.la
libopencore_amrwb_la_LDFLAGS = -version-info @OPENCORE_AMRWB_VERSION@ -no-undefined -export-symbols $(top_srcdir)/amrwb/opencore-amrwb.sym
EXTRA_DIST = $(top_srcdir)/amrwb/opencore-amrwb.sym

# Our sources to include. There are certain sources we exclude and they are
# $(DEC_SRC_DIR)/decoder_amr_wb.cpp
libopencore_amrwb_la_SOURCES = \
    wrapper.cpp \
    $(DEC_SRC_DIR)/agc2_amr_wb.cpp \
        $(DEC_SRC_DIR)/band_pass_6k_7k.cpp \
        $(DEC_SRC_DIR)/dec_acelp_2p_in_64.cpp \
        $(DEC_SRC_DIR)/dec_acelp_4p_in_64.cpp \
        $(DEC_SRC_DIR)/dec_alg_codebook.cpp \
        $(DEC_SRC_DIR)/dec_gain2_amr_wb.cpp \
        $(DEC_SRC_DIR)/deemphasis_32.cpp \
        $(DEC_SRC_DIR)/dtx_decoder_amr_wb.cpp \
        $(DEC_SRC_DIR)/get_amr_wb_bits.cpp \
        $(DEC_SRC_DIR)/highpass_400hz_at_12k8.cpp \
        $(DEC_SRC_DIR)/highpass_50hz_at_12k8.cpp \
        $(DEC_SRC_DIR)/homing_amr_wb_dec.cpp \
        $(DEC_SRC_DIR)/interpolate_isp.cpp \
        $(DEC_SRC_DIR)/isf_extrapolation.cpp \
        $(DEC_SRC_DIR)/isp_az.cpp \
        $(DEC_SRC_DIR)/isp_isf.cpp \
        $(DEC_SRC_DIR)/lagconceal.cpp \
        $(DEC_SRC_DIR)/low_pass_filt_7k.cpp \
        $(DEC_SRC_DIR)/median5.cpp \
        $(DEC_SRC_DIR)/mime_io.cpp \
        $(DEC_SRC_DIR)/noise_gen_amrwb.cpp \
        $(DEC_SRC_DIR)/normalize_amr_wb.cpp \
        $(DEC_SRC_DIR)/oversamp_12k8_to_16k.cpp \
        $(DEC_SRC_DIR)/phase_dispersion.cpp \
        $(DEC_SRC_DIR)/pit_shrp.cpp \
        $(DEC_SRC_DIR)/pred_lt4.cpp \
        $(DEC_SRC_DIR)/preemph_amrwb_dec.cpp \
        $(DEC_SRC_DIR)/pvamrwbdecoder.cpp \
        $(DEC_SRC_DIR)/pvamrwb_math_op.cpp \
        $(DEC_SRC_DIR)/q_gain2_tab.cpp \
        $(DEC_SRC_DIR)/qisf_ns.cpp \
        $(DEC_SRC_DIR)/qisf_ns_tab.cpp \
        $(DEC_SRC_DIR)/qpisf_2s.cpp \
        $(DEC_SRC_DIR)/qpisf_2s_tab.cpp \
        $(DEC_SRC_DIR)/scale_signal.cpp \
        $(DEC_SRC_DIR)/synthesis_amr_wb.cpp \
        $(DEC_SRC_DIR)/voice_factor.cpp \
        $(DEC_SRC_DIR)/wb_syn_filt.cpp \
        $(DEC_SRC_DIR)/weight_amrwb_lpc.cpp

all: all-am

.SUFFIXES:
.SUFFIXES: .c .cpp .lo .o .obj
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign amrwb/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign amrwb/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
opencore-amrwb.pc: $(top_builddir)/config.status $(srcdir)/opencore-amrwb.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}

libopencore-amrwb.la: $(libopencore_amrwb_la_OBJECTS) $(libopencore_amrwb_la_DEPENDENCIES) $(EXTRA_libopencore_amrwb_la_DEPENDENCIES) 
	$(AM_V_GEN)$(libopencore_amrwb_la_LINK) -rpath $(libdir) $(libopencore_amrwb_la_OBJECTS) $(libopencore_amrwb_la_LIBADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/agc2_amr_wb.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/band_pass_6k_7k.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/dec_acelp_2p_in_64.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/dec_acelp_4p_in_64.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/dec_alg_codebook.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/dec_gain2_amr_wb.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/deemphasis_32.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/dtx_decoder_amr_wb.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/dummy.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/get_amr_wb_bits.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/highpass_400hz_at_12k8.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/highpass_50hz_at_12k8.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/homing_amr_wb_dec.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/interpolate_isp.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/isf_extrapolation.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/isp_az.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/isp_isf.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lagconceal.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/low_pass_filt_7k.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/median5.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mime_io.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/noise_gen_amrwb.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/normalize_amr_wb.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/oversamp_12k8_to_16k.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/phase_dispersion.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pit_shrp.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pred_lt4.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/preemph_amrwb_dec.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pvamrwb_math_op.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pvamrwbdecoder.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/q_gain2_tab.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/qisf_ns.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/qisf_ns_tab.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/qpisf_2s.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/qpisf_2s_tab.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/scale_signal.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/synthesis_amr_wb.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/voice_factor.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/wb_syn_filt.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/weight_amrwb_lpc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/wrapper.Plo@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LTCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

.cpp.o:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXXCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ $<

.cpp.obj:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXXCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.cpp.lo:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LTCXXCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LTCXXCOMPILE) -c -o $@ $<

agc2_amr_wb.lo: $(DEC_SRC_DIR)/agc2_amr_wb.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT agc2_amr_wb.lo -MD -MP -MF $(DEPDIR)/agc2_amr_wb.Tpo -c -o agc2_amr_wb.lo `test -f '$(DEC_SRC_DIR)/agc2_amr_wb.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/agc2_amr_wb.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/agc2_amr_wb.Tpo $(DEPDIR)/agc2_amr_wb.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/agc2_amr_wb.cpp' object='agc2_amr_wb.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o agc2_amr_wb.lo `test -f '$(DEC_SRC_DIR)/agc2_amr_wb.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/agc2_amr_wb.cpp

band_pass_6k_7k.lo: $(DEC_SRC_DIR)/band_pass_6k_7k.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT band_pass_6k_7k.lo -MD -MP -MF $(DEPDIR)/band_pass_6k_7k.Tpo -c -o band_pass_6k_7k.lo `test -f '$(DEC_SRC_DIR)/band_pass_6k_7k.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/band_pass_6k_7k.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/band_pass_6k_7k.Tpo $(DEPDIR)/band_pass_6k_7k.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/band_pass_6k_7k.cpp' object='band_pass_6k_7k.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o band_pass_6k_7k.lo `test -f '$(DEC_SRC_DIR)/band_pass_6k_7k.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/band_pass_6k_7k.cpp

dec_acelp_2p_in_64.lo: $(DEC_SRC_DIR)/dec_acelp_2p_in_64.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT dec_acelp_2p_in_64.lo -MD -MP -MF $(DEPDIR)/dec_acelp_2p_in_64.Tpo -c -o dec_acelp_2p_in_64.lo `test -f '$(DEC_SRC_DIR)/dec_acelp_2p_in_64.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_acelp_2p_in_64.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/dec_acelp_2p_in_64.Tpo $(DEPDIR)/dec_acelp_2p_in_64.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/dec_acelp_2p_in_64.cpp' object='dec_acelp_2p_in_64.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o dec_acelp_2p_in_64.lo `test -f '$(DEC_SRC_DIR)/dec_acelp_2p_in_64.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_acelp_2p_in_64.cpp

dec_acelp_4p_in_64.lo: $(DEC_SRC_DIR)/dec_acelp_4p_in_64.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT dec_acelp_4p_in_64.lo -MD -MP -MF $(DEPDIR)/dec_acelp_4p_in_64.Tpo -c -o dec_acelp_4p_in_64.lo `test -f '$(DEC_SRC_DIR)/dec_acelp_4p_in_64.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_acelp_4p_in_64.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/dec_acelp_4p_in_64.Tpo $(DEPDIR)/dec_acelp_4p_in_64.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/dec_acelp_4p_in_64.cpp' object='dec_acelp_4p_in_64.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o dec_acelp_4p_in_64.lo `test -f '$(DEC_SRC_DIR)/dec_acelp_4p_in_64.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_acelp_4p_in_64.cpp

dec_alg_codebook.lo: $(DEC_SRC_DIR)/dec_alg_codebook.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT dec_alg_codebook.lo -MD -MP -MF $(DEPDIR)/dec_alg_codebook.Tpo -c -o dec_alg_codebook.lo `test -f '$(DEC_SRC_DIR)/dec_alg_codebook.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_alg_codebook.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/dec_alg_codebook.Tpo $(DEPDIR)/dec_alg_codebook.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/dec_alg_codebook.cpp' object='dec_alg_codebook.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o dec_alg_codebook.lo `test -f '$(DEC_SRC_DIR)/dec_alg_codebook.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_alg_codebook.cpp

dec_gain2_amr_wb.lo: $(DEC_SRC_DIR)/dec_gain2_amr_wb.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT dec_gain2_amr_wb.lo -MD -MP -MF $(DEPDIR)/dec_gain2_amr_wb.Tpo -c -o dec_gain2_amr_wb.lo `test -f '$(DEC_SRC_DIR)/dec_gain2_amr_wb.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_gain2_amr_wb.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/dec_gain2_amr_wb.Tpo $(DEPDIR)/dec_gain2_amr_wb.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/dec_gain2_amr_wb.cpp' object='dec_gain2_amr_wb.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o dec_gain2_amr_wb.lo `test -f '$(DEC_SRC_DIR)/dec_gain2_amr_wb.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dec_gain2_amr_wb.cpp

deemphasis_32.lo: $(DEC_SRC_DIR)/deemphasis_32.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT deemphasis_32.lo -MD -MP -MF $(DEPDIR)/deemphasis_32.Tpo -c -o deemphasis_32.lo `test -f '$(DEC_SRC_DIR)/deemphasis_32.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/deemphasis_32.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/deemphasis_32.Tpo $(DEPDIR)/deemphasis_32.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/deemphasis_32.cpp' object='deemphasis_32.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o deemphasis_32.lo `test -f '$(DEC_SRC_DIR)/deemphasis_32.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/deemphasis_32.cpp

dtx_decoder_amr_wb.lo: $(DEC_SRC_DIR)/dtx_decoder_amr_wb.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT dtx_decoder_amr_wb.lo -MD -MP -MF $(DEPDIR)/dtx_decoder_amr_wb.Tpo -c -o dtx_decoder_amr_wb.lo `test -f '$(DEC_SRC_DIR)/dtx_decoder_amr_wb.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dtx_decoder_amr_wb.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/dtx_decoder_amr_wb.Tpo $(DEPDIR)/dtx_decoder_amr_wb.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/dtx_decoder_amr_wb.cpp' object='dtx_decoder_amr_wb.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o dtx_decoder_amr_wb.lo `test -f '$(DEC_SRC_DIR)/dtx_decoder_amr_wb.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/dtx_decoder_amr_wb.cpp

get_amr_wb_bits.lo: $(DEC_SRC_DIR)/get_amr_wb_bits.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT get_amr_wb_bits.lo -MD -MP -MF $(DEPDIR)/get_amr_wb_bits.Tpo -c -o get_amr_wb_bits.lo `test -f '$(DEC_SRC_DIR)/get_amr_wb_bits.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/get_amr_wb_bits.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/get_amr_wb_bits.Tpo $(DEPDIR)/get_amr_wb_bits.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/get_amr_wb_bits.cpp' object='get_amr_wb_bits.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o get_amr_wb_bits.lo `test -f '$(DEC_SRC_DIR)/get_amr_wb_bits.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/get_amr_wb_bits.cpp

highpass_400hz_at_12k8.lo: $(DEC_SRC_DIR)/highpass_400hz_at_12k8.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT highpass_400hz_at_12k8.lo -MD -MP -MF $(DEPDIR)/highpass_400hz_at_12k8.Tpo -c -o highpass_400hz_at_12k8.lo `test -f '$(DEC_SRC_DIR)/highpass_400hz_at_12k8.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/highpass_400hz_at_12k8.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/highpass_400hz_at_12k8.Tpo $(DEPDIR)/highpass_400hz_at_12k8.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/highpass_400hz_at_12k8.cpp' object='highpass_400hz_at_12k8.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o highpass_400hz_at_12k8.lo `test -f '$(DEC_SRC_DIR)/highpass_400hz_at_12k8.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/highpass_400hz_at_12k8.cpp

highpass_50hz_at_12k8.lo: $(DEC_SRC_DIR)/highpass_50hz_at_12k8.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT highpass_50hz_at_12k8.lo -MD -MP -MF $(DEPDIR)/highpass_50hz_at_12k8.Tpo -c -o highpass_50hz_at_12k8.lo `test -f '$(DEC_SRC_DIR)/highpass_50hz_at_12k8.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/highpass_50hz_at_12k8.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/highpass_50hz_at_12k8.Tpo $(DEPDIR)/highpass_50hz_at_12k8.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/highpass_50hz_at_12k8.cpp' object='highpass_50hz_at_12k8.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o highpass_50hz_at_12k8.lo `test -f '$(DEC_SRC_DIR)/highpass_50hz_at_12k8.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/highpass_50hz_at_12k8.cpp

homing_amr_wb_dec.lo: $(DEC_SRC_DIR)/homing_amr_wb_dec.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT homing_amr_wb_dec.lo -MD -MP -MF $(DEPDIR)/homing_amr_wb_dec.Tpo -c -o homing_amr_wb_dec.lo `test -f '$(DEC_SRC_DIR)/homing_amr_wb_dec.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/homing_amr_wb_dec.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/homing_amr_wb_dec.Tpo $(DEPDIR)/homing_amr_wb_dec.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/homing_amr_wb_dec.cpp' object='homing_amr_wb_dec.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o homing_amr_wb_dec.lo `test -f '$(DEC_SRC_DIR)/homing_amr_wb_dec.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/homing_amr_wb_dec.cpp

interpolate_isp.lo: $(DEC_SRC_DIR)/interpolate_isp.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT interpolate_isp.lo -MD -MP -MF $(DEPDIR)/interpolate_isp.Tpo -c -o interpolate_isp.lo `test -f '$(DEC_SRC_DIR)/interpolate_isp.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/interpolate_isp.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/interpolate_isp.Tpo $(DEPDIR)/interpolate_isp.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/interpolate_isp.cpp' object='interpolate_isp.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o interpolate_isp.lo `test -f '$(DEC_SRC_DIR)/interpolate_isp.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/interpolate_isp.cpp

isf_extrapolation.lo: $(DEC_SRC_DIR)/isf_extrapolation.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT isf_extrapolation.lo -MD -MP -MF $(DEPDIR)/isf_extrapolation.Tpo -c -o isf_extrapolation.lo `test -f '$(DEC_SRC_DIR)/isf_extrapolation.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/isf_extrapolation.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/isf_extrapolation.Tpo $(DEPDIR)/isf_extrapolation.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/isf_extrapolation.cpp' object='isf_extrapolation.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o isf_extrapolation.lo `test -f '$(DEC_SRC_DIR)/isf_extrapolation.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/isf_extrapolation.cpp

isp_az.lo: $(DEC_SRC_DIR)/isp_az.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT isp_az.lo -MD -MP -MF $(DEPDIR)/isp_az.Tpo -c -o isp_az.lo `test -f '$(DEC_SRC_DIR)/isp_az.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/isp_az.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/isp_az.Tpo $(DEPDIR)/isp_az.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/isp_az.cpp' object='isp_az.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o isp_az.lo `test -f '$(DEC_SRC_DIR)/isp_az.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/isp_az.cpp

isp_isf.lo: $(DEC_SRC_DIR)/isp_isf.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT isp_isf.lo -MD -MP -MF $(DEPDIR)/isp_isf.Tpo -c -o isp_isf.lo `test -f '$(DEC_SRC_DIR)/isp_isf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/isp_isf.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/isp_isf.Tpo $(DEPDIR)/isp_isf.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/isp_isf.cpp' object='isp_isf.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o isp_isf.lo `test -f '$(DEC_SRC_DIR)/isp_isf.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/isp_isf.cpp

lagconceal.lo: $(DEC_SRC_DIR)/lagconceal.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT lagconceal.lo -MD -MP -MF $(DEPDIR)/lagconceal.Tpo -c -o lagconceal.lo `test -f '$(DEC_SRC_DIR)/lagconceal.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/lagconceal.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/lagconceal.Tpo $(DEPDIR)/lagconceal.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/lagconceal.cpp' object='lagconceal.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o lagconceal.lo `test -f '$(DEC_SRC_DIR)/lagconceal.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/lagconceal.cpp

low_pass_filt_7k.lo: $(DEC_SRC_DIR)/low_pass_filt_7k.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT low_pass_filt_7k.lo -MD -MP -MF $(DEPDIR)/low_pass_filt_7k.Tpo -c -o low_pass_filt_7k.lo `test -f '$(DEC_SRC_DIR)/low_pass_filt_7k.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/low_pass_filt_7k.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/low_pass_filt_7k.Tpo $(DEPDIR)/low_pass_filt_7k.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/low_pass_filt_7k.cpp' object='low_pass_filt_7k.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o low_pass_filt_7k.lo `test -f '$(DEC_SRC_DIR)/low_pass_filt_7k.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/low_pass_filt_7k.cpp

median5.lo: $(DEC_SRC_DIR)/median5.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT median5.lo -MD -MP -MF $(DEPDIR)/median5.Tpo -c -o median5.lo `test -f '$(DEC_SRC_DIR)/median5.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/median5.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/median5.Tpo $(DEPDIR)/median5.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/median5.cpp' object='median5.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o median5.lo `test -f '$(DEC_SRC_DIR)/median5.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/median5.cpp

mime_io.lo: $(DEC_SRC_DIR)/mime_io.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT mime_io.lo -MD -MP -MF $(DEPDIR)/mime_io.Tpo -c -o mime_io.lo `test -f '$(DEC_SRC_DIR)/mime_io.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/mime_io.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/mime_io.Tpo $(DEPDIR)/mime_io.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/mime_io.cpp' object='mime_io.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o mime_io.lo `test -f '$(DEC_SRC_DIR)/mime_io.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/mime_io.cpp

noise_gen_amrwb.lo: $(DEC_SRC_DIR)/noise_gen_amrwb.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT noise_gen_amrwb.lo -MD -MP -MF $(DEPDIR)/noise_gen_amrwb.Tpo -c -o noise_gen_amrwb.lo `test -f '$(DEC_SRC_DIR)/noise_gen_amrwb.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/noise_gen_amrwb.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/noise_gen_amrwb.Tpo $(DEPDIR)/noise_gen_amrwb.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/noise_gen_amrwb.cpp' object='noise_gen_amrwb.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o noise_gen_amrwb.lo `test -f '$(DEC_SRC_DIR)/noise_gen_amrwb.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/noise_gen_amrwb.cpp

normalize_amr_wb.lo: $(DEC_SRC_DIR)/normalize_amr_wb.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT normalize_amr_wb.lo -MD -MP -MF $(DEPDIR)/normalize_amr_wb.Tpo -c -o normalize_amr_wb.lo `test -f '$(DEC_SRC_DIR)/normalize_amr_wb.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/normalize_amr_wb.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/normalize_amr_wb.Tpo $(DEPDIR)/normalize_amr_wb.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/normalize_amr_wb.cpp' object='normalize_amr_wb.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o normalize_amr_wb.lo `test -f '$(DEC_SRC_DIR)/normalize_amr_wb.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/normalize_amr_wb.cpp

oversamp_12k8_to_16k.lo: $(DEC_SRC_DIR)/oversamp_12k8_to_16k.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT oversamp_12k8_to_16k.lo -MD -MP -MF $(DEPDIR)/oversamp_12k8_to_16k.Tpo -c -o oversamp_12k8_to_16k.lo `test -f '$(DEC_SRC_DIR)/oversamp_12k8_to_16k.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/oversamp_12k8_to_16k.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/oversamp_12k8_to_16k.Tpo $(DEPDIR)/oversamp_12k8_to_16k.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/oversamp_12k8_to_16k.cpp' object='oversamp_12k8_to_16k.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o oversamp_12k8_to_16k.lo `test -f '$(DEC_SRC_DIR)/oversamp_12k8_to_16k.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/oversamp_12k8_to_16k.cpp

phase_dispersion.lo: $(DEC_SRC_DIR)/phase_dispersion.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT phase_dispersion.lo -MD -MP -MF $(DEPDIR)/phase_dispersion.Tpo -c -o phase_dispersion.lo `test -f '$(DEC_SRC_DIR)/phase_dispersion.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/phase_dispersion.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/phase_dispersion.Tpo $(DEPDIR)/phase_dispersion.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/phase_dispersion.cpp' object='phase_dispersion.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o phase_dispersion.lo `test -f '$(DEC_SRC_DIR)/phase_dispersion.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/phase_dispersion.cpp

pit_shrp.lo: $(DEC_SRC_DIR)/pit_shrp.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT pit_shrp.lo -MD -MP -MF $(DEPDIR)/pit_shrp.Tpo -c -o pit_shrp.lo `test -f '$(DEC_SRC_DIR)/pit_shrp.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/pit_shrp.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pit_shrp.Tpo $(DEPDIR)/pit_shrp.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/pit_shrp.cpp' object='pit_shrp.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o pit_shrp.lo `test -f '$(DEC_SRC_DIR)/pit_shrp.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/pit_shrp.cpp

pred_lt4.lo: $(DEC_SRC_DIR)/pred_lt4.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT pred_lt4.lo -MD -MP -MF $(DEPDIR)/pred_lt4.Tpo -c -o pred_lt4.lo `test -f '$(DEC_SRC_DIR)/pred_lt4.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/pred_lt4.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pred_lt4.Tpo $(DEPDIR)/pred_lt4.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/pred_lt4.cpp' object='pred_lt4.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o pred_lt4.lo `test -f '$(DEC_SRC_DIR)/pred_lt4.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/pred_lt4.cpp

preemph_amrwb_dec.lo: $(DEC_SRC_DIR)/preemph_amrwb_dec.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT preemph_amrwb_dec.lo -MD -MP -MF $(DEPDIR)/preemph_amrwb_dec.Tpo -c -o preemph_amrwb_dec.lo `test -f '$(DEC_SRC_DIR)/preemph_amrwb_dec.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/preemph_amrwb_dec.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/preemph_amrwb_dec.Tpo $(DEPDIR)/preemph_amrwb_dec.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/preemph_amrwb_dec.cpp' object='preemph_amrwb_dec.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o preemph_amrwb_dec.lo `test -f '$(DEC_SRC_DIR)/preemph_amrwb_dec.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/preemph_amrwb_dec.cpp

pvamrwbdecoder.lo: $(DEC_SRC_DIR)/pvamrwbdecoder.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT pvamrwbdecoder.lo -MD -MP -MF $(DEPDIR)/pvamrwbdecoder.Tpo -c -o pvamrwbdecoder.lo `test -f '$(DEC_SRC_DIR)/pvamrwbdecoder.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/pvamrwbdecoder.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pvamrwbdecoder.Tpo $(DEPDIR)/pvamrwbdecoder.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/pvamrwbdecoder.cpp' object='pvamrwbdecoder.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o pvamrwbdecoder.lo `test -f '$(DEC_SRC_DIR)/pvamrwbdecoder.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/pvamrwbdecoder.cpp

pvamrwb_math_op.lo: $(DEC_SRC_DIR)/pvamrwb_math_op.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT pvamrwb_math_op.lo -MD -MP -MF $(DEPDIR)/pvamrwb_math_op.Tpo -c -o pvamrwb_math_op.lo `test -f '$(DEC_SRC_DIR)/pvamrwb_math_op.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/pvamrwb_math_op.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/pvamrwb_math_op.Tpo $(DEPDIR)/pvamrwb_math_op.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/pvamrwb_math_op.cpp' object='pvamrwb_math_op.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o pvamrwb_math_op.lo `test -f '$(DEC_SRC_DIR)/pvamrwb_math_op.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/pvamrwb_math_op.cpp

q_gain2_tab.lo: $(DEC_SRC_DIR)/q_gain2_tab.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT q_gain2_tab.lo -MD -MP -MF $(DEPDIR)/q_gain2_tab.Tpo -c -o q_gain2_tab.lo `test -f '$(DEC_SRC_DIR)/q_gain2_tab.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/q_gain2_tab.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/q_gain2_tab.Tpo $(DEPDIR)/q_gain2_tab.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/q_gain2_tab.cpp' object='q_gain2_tab.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o q_gain2_tab.lo `test -f '$(DEC_SRC_DIR)/q_gain2_tab.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/q_gain2_tab.cpp

qisf_ns.lo: $(DEC_SRC_DIR)/qisf_ns.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT qisf_ns.lo -MD -MP -MF $(DEPDIR)/qisf_ns.Tpo -c -o qisf_ns.lo `test -f '$(DEC_SRC_DIR)/qisf_ns.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/qisf_ns.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/qisf_ns.Tpo $(DEPDIR)/qisf_ns.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/qisf_ns.cpp' object='qisf_ns.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o qisf_ns.lo `test -f '$(DEC_SRC_DIR)/qisf_ns.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/qisf_ns.cpp

qisf_ns_tab.lo: $(DEC_SRC_DIR)/qisf_ns_tab.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT qisf_ns_tab.lo -MD -MP -MF $(DEPDIR)/qisf_ns_tab.Tpo -c -o qisf_ns_tab.lo `test -f '$(DEC_SRC_DIR)/qisf_ns_tab.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/qisf_ns_tab.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/qisf_ns_tab.Tpo $(DEPDIR)/qisf_ns_tab.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/qisf_ns_tab.cpp' object='qisf_ns_tab.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o qisf_ns_tab.lo `test -f '$(DEC_SRC_DIR)/qisf_ns_tab.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/qisf_ns_tab.cpp

qpisf_2s.lo: $(DEC_SRC_DIR)/qpisf_2s.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT qpisf_2s.lo -MD -MP -MF $(DEPDIR)/qpisf_2s.Tpo -c -o qpisf_2s.lo `test -f '$(DEC_SRC_DIR)/qpisf_2s.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/qpisf_2s.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/qpisf_2s.Tpo $(DEPDIR)/qpisf_2s.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/qpisf_2s.cpp' object='qpisf_2s.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o qpisf_2s.lo `test -f '$(DEC_SRC_DIR)/qpisf_2s.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/qpisf_2s.cpp

qpisf_2s_tab.lo: $(DEC_SRC_DIR)/qpisf_2s_tab.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT qpisf_2s_tab.lo -MD -MP -MF $(DEPDIR)/qpisf_2s_tab.Tpo -c -o qpisf_2s_tab.lo `test -f '$(DEC_SRC_DIR)/qpisf_2s_tab.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/qpisf_2s_tab.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/qpisf_2s_tab.Tpo $(DEPDIR)/qpisf_2s_tab.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/qpisf_2s_tab.cpp' object='qpisf_2s_tab.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o qpisf_2s_tab.lo `test -f '$(DEC_SRC_DIR)/qpisf_2s_tab.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/qpisf_2s_tab.cpp

scale_signal.lo: $(DEC_SRC_DIR)/scale_signal.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT scale_signal.lo -MD -MP -MF $(DEPDIR)/scale_signal.Tpo -c -o scale_signal.lo `test -f '$(DEC_SRC_DIR)/scale_signal.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/scale_signal.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/scale_signal.Tpo $(DEPDIR)/scale_signal.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/scale_signal.cpp' object='scale_signal.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o scale_signal.lo `test -f '$(DEC_SRC_DIR)/scale_signal.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/scale_signal.cpp

synthesis_amr_wb.lo: $(DEC_SRC_DIR)/synthesis_amr_wb.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT synthesis_amr_wb.lo -MD -MP -MF $(DEPDIR)/synthesis_amr_wb.Tpo -c -o synthesis_amr_wb.lo `test -f '$(DEC_SRC_DIR)/synthesis_amr_wb.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/synthesis_amr_wb.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/synthesis_amr_wb.Tpo $(DEPDIR)/synthesis_amr_wb.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/synthesis_amr_wb.cpp' object='synthesis_amr_wb.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o synthesis_amr_wb.lo `test -f '$(DEC_SRC_DIR)/synthesis_amr_wb.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/synthesis_amr_wb.cpp

voice_factor.lo: $(DEC_SRC_DIR)/voice_factor.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT voice_factor.lo -MD -MP -MF $(DEPDIR)/voice_factor.Tpo -c -o voice_factor.lo `test -f '$(DEC_SRC_DIR)/voice_factor.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/voice_factor.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/voice_factor.Tpo $(DEPDIR)/voice_factor.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/voice_factor.cpp' object='voice_factor.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o voice_factor.lo `test -f '$(DEC_SRC_DIR)/voice_factor.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/voice_factor.cpp

wb_syn_filt.lo: $(DEC_SRC_DIR)/wb_syn_filt.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT wb_syn_filt.lo -MD -MP -MF $(DEPDIR)/wb_syn_filt.Tpo -c -o wb_syn_filt.lo `test -f '$(DEC_SRC_DIR)/wb_syn_filt.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/wb_syn_filt.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/wb_syn_filt.Tpo $(DEPDIR)/wb_syn_filt.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/wb_syn_filt.cpp' object='wb_syn_filt.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o wb_syn_filt.lo `test -f '$(DEC_SRC_DIR)/wb_syn_filt.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/wb_syn_filt.cpp

weight_amrwb_lpc.lo: $(DEC_SRC_DIR)/weight_amrwb_lpc.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT weight_amrwb_lpc.lo -MD -MP -MF $(DEPDIR)/weight_amrwb_lpc.Tpo -c -o weight_amrwb_lpc.lo `test -f '$(DEC_SRC_DIR)/weight_amrwb_lpc.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/weight_amrwb_lpc.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/weight_amrwb_lpc.Tpo $(DEPDIR)/weight_amrwb_lpc.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$(DEC_SRC_DIR)/weight_amrwb_lpc.cpp' object='weight_amrwb_lpc.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o weight_amrwb_lpc.lo `test -f '$(DEC_SRC_DIR)/weight_amrwb_lpc.cpp' || echo '$(srcdir)/'`$(DEC_SRC_DIR)/weight_amrwb_lpc.cpp

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-pkgconfigDATA: $(pkgconfig_DATA)
	@$(NORMAL_INSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgconfigdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pkgconfigdir)" || exit $$?; \
	done

uninstall-pkgconfigDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgconfigdir)'; $(am__uninstall_files_from_dir)
install-amrwbincludeHEADERS: $(amrwbinclude_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(amrwbinclude_HEADERS)'; test -n "$(amrwbincludedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(amrwbincludedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(amrwbincludedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(amrwbincludedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(amrwbincludedir)" || exit $$?; \
	done

uninstall-amrwbincludeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(amrwbinclude_HEADERS)'; test -n "$(amrwbincludedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(amrwbincludedir)'; $(am__uninstall_files_from_dir)

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(LTLIBRARIES) $(DATA) $(HEADERS)
installdirs:
	for dir in "$(DESTDIR)$(libdir)" "$(DESTDIR)$(pkgconfigdir)" "$(DESTDIR)$(amrwbincludedir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libLTLIBRARIES clean-libtool \
	mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/agc2_amr_wb.Plo
	-rm -f ./$(DEPDIR)/band_pass_6k_7k.Plo
	-rm -f ./$(DEPDIR)/dec_acelp_2p_in_64.Plo
	-rm -f ./$(DEPDIR)/dec_acelp_4p_in_64.Plo
	-rm -f ./$(DEPDIR)/dec_alg_codebook.Plo
	-rm -f ./$(DEPDIR)/dec_gain2_amr_wb.Plo
	-rm -f ./$(DEPDIR)/deemphasis_32.Plo
	-rm -f ./$(DEPDIR)/dtx_decoder_amr_wb.Plo
	-rm -f ./$(DEPDIR)/dummy.Plo
	-rm -f ./$(DEPDIR)/get_amr_wb_bits.Plo
	-rm -f ./$(DEPDIR)/highpass_400hz_at_12k8.Plo
	-rm -f ./$(DEPDIR)/highpass_50hz_at_12k8.Plo
	-rm -f ./$(DEPDIR)/homing_amr_wb_dec.Plo
	-rm -f ./$(DEPDIR)/interpolate_isp.Plo
	-rm -f ./$(DEPDIR)/isf_extrapolation.Plo
	-rm -f ./$(DEPDIR)/isp_az.Plo
	-rm -f ./$(DEPDIR)/isp_isf.Plo
	-rm -f ./$(DEPDIR)/lagconceal.Plo
	-rm -f ./$(DEPDIR)/low_pass_filt_7k.Plo
	-rm -f ./$(DEPDIR)/median5.Plo
	-rm -f ./$(DEPDIR)/mime_io.Plo
	-rm -f ./$(DEPDIR)/noise_gen_amrwb.Plo
	-rm -f ./$(DEPDIR)/normalize_amr_wb.Plo
	-rm -f ./$(DEPDIR)/oversamp_12k8_to_16k.Plo
	-rm -f ./$(DEPDIR)/phase_dispersion.Plo
	-rm -f ./$(DEPDIR)/pit_shrp.Plo
	-rm -f ./$(DEPDIR)/pred_lt4.Plo
	-rm -f ./$(DEPDIR)/preemph_amrwb_dec.Plo
	-rm -f ./$(DEPDIR)/pvamrwb_math_op.Plo
	-rm -f ./$(DEPDIR)/pvamrwbdecoder.Plo
	-rm -f ./$(DEPDIR)/q_gain2_tab.Plo
	-rm -f ./$(DEPDIR)/qisf_ns.Plo
	-rm -f ./$(DEPDIR)/qisf_ns_tab.Plo
	-rm -f ./$(DEPDIR)/qpisf_2s.Plo
	-rm -f ./$(DEPDIR)/qpisf_2s_tab.Plo
	-rm -f ./$(DEPDIR)/scale_signal.Plo
	-rm -f ./$(DEPDIR)/synthesis_amr_wb.Plo
	-rm -f ./$(DEPDIR)/voice_factor.Plo
	-rm -f ./$(DEPDIR)/wb_syn_filt.Plo
	-rm -f ./$(DEPDIR)/weight_amrwb_lpc.Plo
	-rm -f ./$(DEPDIR)/wrapper.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-amrwbincludeHEADERS install-pkgconfigDATA

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-libLTLIBRARIES

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/agc2_amr_wb.Plo
	-rm -f ./$(DEPDIR)/band_pass_6k_7k.Plo
	-rm -f ./$(DEPDIR)/dec_acelp_2p_in_64.Plo
	-rm -f ./$(DEPDIR)/dec_acelp_4p_in_64.Plo
	-rm -f ./$(DEPDIR)/dec_alg_codebook.Plo
	-rm -f ./$(DEPDIR)/dec_gain2_amr_wb.Plo
	-rm -f ./$(DEPDIR)/deemphasis_32.Plo
	-rm -f ./$(DEPDIR)/dtx_decoder_amr_wb.Plo
	-rm -f ./$(DEPDIR)/dummy.Plo
	-rm -f ./$(DEPDIR)/get_amr_wb_bits.Plo
	-rm -f ./$(DEPDIR)/highpass_400hz_at_12k8.Plo
	-rm -f ./$(DEPDIR)/highpass_50hz_at_12k8.Plo
	-rm -f ./$(DEPDIR)/homing_amr_wb_dec.Plo
	-rm -f ./$(DEPDIR)/interpolate_isp.Plo
	-rm -f ./$(DEPDIR)/isf_extrapolation.Plo
	-rm -f ./$(DEPDIR)/isp_az.Plo
	-rm -f ./$(DEPDIR)/isp_isf.Plo
	-rm -f ./$(DEPDIR)/lagconceal.Plo
	-rm -f ./$(DEPDIR)/low_pass_filt_7k.Plo
	-rm -f ./$(DEPDIR)/median5.Plo
	-rm -f ./$(DEPDIR)/mime_io.Plo
	-rm -f ./$(DEPDIR)/noise_gen_amrwb.Plo
	-rm -f ./$(DEPDIR)/normalize_amr_wb.Plo
	-rm -f ./$(DEPDIR)/oversamp_12k8_to_16k.Plo
	-rm -f ./$(DEPDIR)/phase_dispersion.Plo
	-rm -f ./$(DEPDIR)/pit_shrp.Plo
	-rm -f ./$(DEPDIR)/pred_lt4.Plo
	-rm -f ./$(DEPDIR)/preemph_amrwb_dec.Plo
	-rm -f ./$(DEPDIR)/pvamrwb_math_op.Plo
	-rm -f ./$(DEPDIR)/pvamrwbdecoder.Plo
	-rm -f ./$(DEPDIR)/q_gain2_tab.Plo
	-rm -f ./$(DEPDIR)/qisf_ns.Plo
	-rm -f ./$(DEPDIR)/qisf_ns_tab.Plo
	-rm -f ./$(DEPDIR)/qpisf_2s.Plo
	-rm -f ./$(DEPDIR)/qpisf_2s_tab.Plo
	-rm -f ./$(DEPDIR)/scale_signal.Plo
	-rm -f ./$(DEPDIR)/synthesis_amr_wb.Plo
	-rm -f ./$(DEPDIR)/voice_factor.Plo
	-rm -f ./$(DEPDIR)/wb_syn_filt.Plo
	-rm -f ./$(DEPDIR)/weight_amrwb_lpc.Plo
	-rm -f ./$(DEPDIR)/wrapper.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-amrwbincludeHEADERS uninstall-libLTLIBRARIES \
	uninstall-pkgconfigDATA

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-generic clean-libLTLIBRARIES clean-libtool cscopelist-am \
	ctags ctags-am distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am \
	install-amrwbincludeHEADERS install-data install-data-am \
	install-dvi install-dvi-am install-exec install-exec-am \
	install-html install-html-am install-info install-info-am \
	install-libLTLIBRARIES install-man install-pdf install-pdf-am \
	install-pkgconfigDATA install-ps install-ps-am install-strip \
	installcheck installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags tags-am uninstall uninstall-am \
	uninstall-amrwbincludeHEADERS uninstall-libLTLIBRARIES \
	uninstall-pkgconfigDATA

.PRECIOUS: Makefile

@COMPILE_AS_C_TRUE@    # Mention a dummy pure C file to trigger generation of the $(LINK) variable

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
