#include "xy_atc_interface.h"
#include "xy_ps_api.h"
#include "atc_ps.h"
#include "xy_at_api.h"

#ifdef HW_LM_SUPPORT
#include "xy_hw_lm_api.h"
void xy_api_test_hwIms_CamStartInd(ImsCamStartInd* pInd)
{
    char data[30] = { 0 };

    AtcAp_HexToAsc(MAX_IMSI_LEN, data, pInd->imsi);    
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]CamStartInd:imStatus=%d,reason=%d,cardType=%d,isTestCard=%d,MncLen=%d",
          pInd->usimStatus, pInd->reason, pInd->cardType, pInd->isTestCard, pInd->isThreeDigitalMnc ? 3 : 2);   
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]CamStartInd:IsimFlg=%d,Isim=%s,FileChgEfid=%d", pInd->ucIsimFileFlg, data, pInd->usFileChgEfid);
}

void xy_api_test_hwIms_CamServiceChangeInd(CamServiceChangeInd* pInd)
{
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]ServiceChangeInd:voPsInd=%d,emsInd=%d,servStatus=%d,isRoaming=%d,changeType=%d",
          pInd->voPsInd, pInd->emsInd, pInd->serviceStatus, pInd->isRoaming, pInd->changeType);
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]ServiceChangeInd:cellId=%d,mcc=%d,mnc=%d,tac=%d",
          pInd->cellInfo.cellId, pInd->cellInfo.mcc, pInd->cellInfo.mnc,pInd->cellInfo.tac);
}

void xy_api_test_hwIms_EmergencyNumListInd(EmergencyNumListInd* pInd)
{
    int   i,j;
    char  data[30];
    
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]EmergencyNumListInd:EmergNum=%d,sourcetype=%d,mcc=%d,mnc=%d",
          pInd->emcNumCount, pInd->sourcetype, pInd->plmn.mcc, pInd->plmn.mnc);

    for(i = 0; i < pInd->emcNumCount; i++)
    {
        memset(data, 0, 30);
        for(j = 0; j < pInd->emcNumList[i].emcNumLen; j++)
        {
            data[j] = pInd->emcNumList[i].emcNum[j] + 0x30;
        }
        xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]EmergencyNumListInd:[%d]category=%d,len=%d,%s",
              i,
              pInd->emcNumList[i].category, pInd->emcNumList[i].emcNumLen, data);
    }
}

void xy_api_test_hwIms_PdpActInd(PdpActInd* pInd)
{
    int i;
    
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]PdpActInd:cid[0]=%d,cid[1]=%d,bid[0]=%d,bid[1]=%d,dCid=%d,bearType=%d",
          pInd->cid[0], pInd->cid[1], pInd->bid[0], pInd->bid[1],pInd->dCid, pInd->bearType);
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]PdpActInd:apnStr=%s,isMeInitial=%d,mtu=%d", pInd->apnStr, pInd->isMeInitial, pInd->mtu);

    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]PdpActInd:ue[0]=%d.%d.%d.%d", 
        pInd->ue[0].addr[0],pInd->ue[0].addr[1],pInd->ue[0].addr[2],pInd->ue[0].addr[3]);

    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]PdpActInd:ue[1]=%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d",
        pInd->ue[1].addr[0],pInd->ue[1].addr[1],pInd->ue[1].addr[2],pInd->ue[1].addr[3],
        pInd->ue[1].addr[4],pInd->ue[1].addr[5],pInd->ue[1].addr[6],pInd->ue[1].addr[7],
        pInd->ue[1].addr[8],pInd->ue[1].addr[9],pInd->ue[1].addr[10],pInd->ue[1].addr[11],
        pInd->ue[1].addr[12],pInd->ue[1].addr[13],pInd->ue[1].addr[14],pInd->ue[1].addr[15]);

    for(i= 0; i < pInd->pCscfAddrNum; i++)
    {
       xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]PdpActInd:pcscf[0][%d]=%d.%d.%d.%d", i,
            pInd->pcscf[0][i].addr[0],pInd->pcscf[0][i].addr[1],pInd->pcscf[0][i].addr[2],pInd->pcscf[0][i].addr[3]);
        
       xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]PdpActInd:pcscf[1][%d]=%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d",
            i,
            pInd->pcscf[1][i].addr[0],pInd->pcscf[1][i].addr[1],pInd->pcscf[1][i].addr[2],pInd->pcscf[1][i].addr[3],
            pInd->pcscf[1][i].addr[4],pInd->pcscf[1][i].addr[5],pInd->pcscf[1][i].addr[6],pInd->pcscf[1][i].addr[7],
            pInd->pcscf[1][i].addr[8],pInd->pcscf[1][i].addr[9],pInd->pcscf[1][i].addr[10],pInd->pcscf[1][i].addr[11],
            pInd->pcscf[1][i].addr[12],pInd->pcscf[1][i].addr[13],pInd->pcscf[1][i].addr[14],pInd->pcscf[1][i].addr[15]);
    }
}

void xy_api_test_hwIms_PdpModifyInd(PdpModifyInd* pInd)
{
    int i;
    
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]PdpModifyInd:cid[0]=%d,cid[1]=%d,bid[0]=%d,bid[1]=%d,dCid=%d,bearType=%d",
          pInd->cid[0], pInd->cid[1], pInd->bid[0], pInd->bid[1],pInd->dCid, pInd->bearType);
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]PdpModifyInd:apnStr=%s,isMeInitial=%d,mtu=%d", pInd->apnStr, pInd->isMeInitial, pInd->mtu);

    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]PdpModifyInd:ue[0]=%d.%d.%d.%d", 
        pInd->ue[0].addr[0],pInd->ue[0].addr[1],pInd->ue[0].addr[2],pInd->ue[0].addr[3]);

    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]PdpModifyInd:ue[1]=%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d",
        pInd->ue[1].addr[0],pInd->ue[1].addr[1],pInd->ue[1].addr[2],pInd->ue[1].addr[3],
        pInd->ue[1].addr[4],pInd->ue[1].addr[5],pInd->ue[1].addr[6],pInd->ue[1].addr[7],
        pInd->ue[1].addr[8],pInd->ue[1].addr[9],pInd->ue[1].addr[10],pInd->ue[1].addr[11],
        pInd->ue[1].addr[12],pInd->ue[1].addr[13],pInd->ue[1].addr[14],pInd->ue[1].addr[15]);

    for(i= 0; i < pInd->pCscfAddrNum; i++)
    {
       xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]PdpModifyInd:pcscf[0][%d]=%d.%d.%d.%d", i,
            pInd->pcscf[0][i].addr[0],pInd->pcscf[0][i].addr[1],pInd->pcscf[0][i].addr[2],pInd->pcscf[0][i].addr[3]);
        
       xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]PdpModifyInd:pcscf[1][%d]=%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d.%d",
            i,
            pInd->pcscf[1][i].addr[0],pInd->pcscf[1][i].addr[1],pInd->pcscf[1][i].addr[2],pInd->pcscf[1][i].addr[3],
            pInd->pcscf[1][i].addr[4],pInd->pcscf[1][i].addr[5],pInd->pcscf[1][i].addr[6],pInd->pcscf[1][i].addr[7],
            pInd->pcscf[1][i].addr[8],pInd->pcscf[1][i].addr[9],pInd->pcscf[1][i].addr[10],pInd->pcscf[1][i].addr[11],
            pInd->pcscf[1][i].addr[12],pInd->pcscf[1][i].addr[13],pInd->pcscf[1][i].addr[14],pInd->pcscf[1][i].addr[15]);
    }


}

void xy_api_test_hwIms_PdpDeactInd(PdpDeactInd* pInd)
{
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]PdpDeactInd:cid=%d,dCid=%d,isMeInitial=%d,cause=%d", pInd->cid, pInd->dCid, pInd->isMeInitial, pInd->cause);
}

void xy_api_test_hwIms_RrcHimsSsacInd(RrcHimsSsacInd* pInd)
{
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]RrcHimsSsacInd:%d,%d,%d,%d", pInd->barFactorForVoice, pInd->barTimeForVoice, pInd->barFactorForVideo, pInd->barTimeForVideo);
}

void xy_api_test_hwIms_RrcStateInd(RrcStateInd* pInd)
{
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]RrcStateInd:state=%d,reason=%d", pInd->state, pInd->reason);
}

void xy_api_test_hwIms_CDrxCfgInd(CDrxCfgInd* pInd)
{
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]CDrxCfgInd:%d,%d,%d,%d", pInd->cdrxEnableFlg, pInd->shortCdrxEnableFlg, pInd->longCdrxCycle, pInd->shortCdrxCycle);
}

void xy_api_test_hwIms_L2CongestReportInd(L2CongestReportInd* pInd)
{
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]L2CongestReportInd:type=%d,increaseFlg=%d", pInd->type, pInd->increaseFlg);
}

void xy_api_test_hwIms_RohcStateInd(RohcStateInd* pInd)
{
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]RohcStateInd:bearId=%d,enbalbe=%d", pInd->bearId, pInd->enbalbe);
}

void xy_api_test_hwIms_BerInfoInd(BerInfoInd* pInd)
{
    xy_printf(0,USER_LOG, DEBUG_LOG, "[HW_API]BerInfoInd:ulBer=%d,increaseFlgUl=%d,dlBer=%d,increaseFlgDl=%d",
              pInd->ulBer, pInd->increaseFlgUl,pInd->dlBer, pInd->increaseFlgDl);
}

void xy_api_test_hwIms_interface_ind(unsigned int eventId, void *param, int paramLen)
{
    LM_INTERFACE_DATA_STRU *pInterfaceInd = (LM_INTERFACE_DATA_STRU*)param;
    
    switch(pInterfaceInd->eMsgId)
    {
        case IMS_CAM_START_IND:
            xy_api_test_hwIms_CamStartInd((ImsCamStartInd*)pInterfaceInd->aucData);
            break;
        case IMS_CAM_SERVICE_CHANGE_IND:
            xy_api_test_hwIms_CamServiceChangeInd((CamServiceChangeInd*)pInterfaceInd->aucData);
            break;
        case NW_EMERGENCY_NUM_IND:
            xy_api_test_hwIms_EmergencyNumListInd((EmergencyNumListInd*)pInterfaceInd->aucData);
            break;
        case PDP_ACT_IND:
            xy_api_test_hwIms_PdpActInd((PdpActInd*)pInterfaceInd->aucData);
            break;
        case XXX_PDP_MODIFY_IND:
            xy_api_test_hwIms_PdpModifyInd((PdpModifyInd*)pInterfaceInd->aucData);
            break;
        case PDP_DEACT_IND:
            xy_api_test_hwIms_PdpDeactInd((PdpDeactInd*)pInterfaceInd->aucData);
            break;
        case RRC_HIMS_SSAC_CHANGE_NTF:
            xy_api_test_hwIms_RrcHimsSsacInd((RrcHimsSsacInd*)pInterfaceInd->aucData);
            break;
        case RRC_STATE_IND:
            xy_api_test_hwIms_RrcStateInd((RrcStateInd*)pInterfaceInd->aucData);
            break;
        case CDRX_CFG_IND:
            xy_api_test_hwIms_CDrxCfgInd((CDrxCfgInd*)pInterfaceInd->aucData);
            break;
        case L2_CONGEST_RT_IND:
            xy_api_test_hwIms_L2CongestReportInd((L2CongestReportInd*)pInterfaceInd->aucData);
            break;
        case ROHC_STATE_IND:
            xy_api_test_hwIms_RohcStateInd((RohcStateInd*)pInterfaceInd->aucData);
            break;
        case BER_INFO_IND:
            xy_api_test_hwIms_BerInfoInd((BerInfoInd*)pInterfaceInd->aucData);
            break;
        default:
            break;
    }
}

void xy_api_test_hwIms_SimAuthReq(char* at_buf, char **prsp_cmd)
{
    int             ret;
    SimAuthReq      tReq = { 0 };
    SimAuthCnf     *pCnf = NULL;
    char            aucIk[33] = { 0 };
    char            aucCk[33] = { 0 };
    char            aucRes[35] = { 0 };

    tReq.cardType = 0;
    
    ret = xy_lm_SimAuthReq(&tReq, &pCnf);
    if(XY_OK != ret)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    AtcAp_HexToAsc(16, aucIk, pCnf->ik);
    AtcAp_HexToAsc(16, aucCk, pCnf->ck);
    AtcAp_HexToAsc(16, aucRes, pCnf->res);

    *prsp_cmd = (char*)xy_malloc(200);
    snprintf(*prsp_cmd, 200, "\r\n+SIM_AUTH:rc=%d,reslen=%d,autslen=%d,IK=%s,CK=%s,ResOrAuts=%s\r\n\r\nOK\r\n",
             pCnf->rc, pCnf->resLen, pCnf->autsLen, aucIk, aucCk, aucRes);
    
    xy_free(pCnf);
}

void xy_api_test_hwIms_SetApnReq(char* at_buf, char **prsp_cmd)
{
    int       pdpType, ucDiscType, ucImsFlg, ucIsEmc;
    char      aucApn[20+1] = { 0 };
    SetApnReq tReq         = { 0 };
    SetApnCnf *pCnf        = NULL;
    
    
    if(at_parse_param(",%d,%20s,%d,%d,%d", at_buf, &pdpType, aucApn, &ucDiscType, &ucImsFlg, &ucIsEmc) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    tReq.ipType = pdpType;
    tReq.apnLength = strlen(aucApn);
    if(0 != tReq.apnLength)
    {
        AtcAp_MemCpy(tReq.apnStr, aucApn, tReq.apnLength);
    }
    tReq.pcscfDisType = ucDiscType;
    tReq.imCnSigFlag = ucImsFlg;
    tReq.isEmc = ucIsEmc;

    if(XY_OK != xy_lm_SetApnReq(&tReq, &pCnf))
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }
    
    *prsp_cmd = (char*)xy_malloc(100);
    snprintf(*prsp_cmd, 200, "\r\n+SET_APN:cid=%d, rc=%d\r\n\r\nOK\r\n", pCnf->cid,pCnf->rc);
    xy_free(pCnf);
}

void xy_api_test_hwIms_ActPdnReq(char* at_buf, char **prsp_cmd)
{
    int       cid,state;
    ActPdnReq tReq         = { 0 };
    ActPdnCnf *pCnf        = NULL;

    if(at_parse_param(",%d,%d", at_buf, &cid, &state) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    tReq.cid = cid;
    tReq.state = state;

    if(XY_OK != xy_lm_ActPdnReq(&tReq, &pCnf))
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    *prsp_cmd = (char*)xy_malloc(100);
    snprintf(*prsp_cmd, 100, "\r\n+ACT_PDN:rc=%d\r\n\r\nOK\r\n", pCnf->rc);
    xy_free(pCnf);
}

void xy_api_test_hwIms_SimReadReq(char* at_buf, char **prsp_cmd)
{
    int       cmd,efId,cardType;
    SimReadReq tReq         = { 0 };
    SimReadCnf *pCnf        = NULL;
    char      *pData;

    if(at_parse_param(",%d,%d,%d", at_buf, &cmd,&efId, &cardType) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    tReq.cmd = cmd;
    tReq.efId = efId;
    tReq.cardType = cardType;

    if(XY_OK != xy_lm_SimReadReq(&tReq, &pCnf))
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    *prsp_cmd = (char*)xy_malloc(pCnf->len * 2 + 100);

    pData = (char*)AtcAp_Malloc(pCnf->len * 2 + 1);    
    AtcAp_HexToAsc(pCnf->len, pData, pCnf->data);    
    snprintf(*prsp_cmd, pCnf->len * 2 + 100, "\r\n+SIM_READ:rc=%d,len=%d,data=%s\r\n\r\nOK\r\n", pCnf->rc, pCnf->len, pData);
    xy_free(pData);
    if(pCnf->data != NULL)
    {
        xy_free(pCnf->data);
    }
    xy_free(pCnf);

}

void xy_api_test_hwIms_StartPsReq(char* at_buf, char **prsp_cmd)
{
    PsStartReq tReq         = { 0 };
    int  epsAttachType, voiceSetting,voiceDomainPrefer,imsSupport,roamingImsSupport,rohc,ttiBunding,sps;

    if(at_parse_param(",%d,%d,%d,%d,%d,%d,%d,%d", at_buf,
        &epsAttachType,&voiceSetting, &voiceDomainPrefer,&imsSupport,
        &roamingImsSupport,&rohc, &ttiBunding,&sps) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    tReq.epsAttachType = epsAttachType;
    tReq.voiceSetting = voiceSetting;
    tReq.voiceDomainPrefer = voiceDomainPrefer;
    tReq.imsSupport = imsSupport;
    tReq.roamingImsSupport = roamingImsSupport;
    tReq.rohc = rohc;
    tReq.ttiBunding = ttiBunding;
    tReq.sps = sps;

    if(XY_OK != xy_lm_PsStartReq(&tReq))
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    *prsp_cmd = (char*)xy_malloc(100);  
    snprintf(*prsp_cmd, 100, "\r\nOK\r\n");
}

void xy_api_test_hwIms_GetTimerLenReq(char* at_buf, char **prsp_cmd)
{
    TIME_LEN_CNF *pCnf;

    if(XY_OK != xy_lm_GetEmmTimerLenReq(&pCnf))
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    *prsp_cmd = (char*)xy_malloc(100);  
    snprintf(*prsp_cmd, 100, "\r\n+GET_EMM_TIMER_LEN:len=%d\r\n\r\nOK\r\n", pCnf->timeLen);
    xy_free(pCnf);
}

void xy_api_test_hwIms_EmergencyAttReq(char* at_buf, char **prsp_cmd)
{
    EmcAttachReq tReq         = { 0 };
    EmcAttachCnf *pCnf;
    int action;

    if(at_parse_param(",%d", at_buf, &action) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    tReq.action = action;
    if(XY_OK != xy_lm_EmcAttachReq(&tReq, &pCnf))
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    *prsp_cmd = (char*)xy_malloc(100);  
    snprintf(*prsp_cmd, 100, "\r\n+EMERGENCY_ATTACH:rc=%d\r\n\r\nOK\r\n", pCnf->rc);
    xy_free(pCnf);
}

void xy_api_test_hwIms_Ignor3411TimerReq(char* at_buf, char **prsp_cmd)
{
    HimsMmIgnor3411TimerInd tReq         = { 0 };
    int ignorTimer;

    if(at_parse_param(",%d", at_buf, &ignorTimer) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    tReq.ignorTimer = ignorTimer;
    if(XY_OK != xy_lm_MMIgnorl3411TimerInd(&tReq))
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    *prsp_cmd = (char*)xy_malloc(100);  
    snprintf(*prsp_cmd, 100, "\r\nOK\r\n");
}

void xy_api_test_hwIms_CellThreshReq(char* at_buf, char **prsp_cmd)
{
    CellThreshAdjReq tReq         = { 0 };
    int rsrpThreshAdjust, rsrqThreshAdjust;

    if(at_parse_param(",%d,%d", at_buf, &rsrpThreshAdjust, &rsrqThreshAdjust) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    tReq.rsrpThreshAdjust = rsrpThreshAdjust;
    tReq.rsrqThreshAdjust = rsrqThreshAdjust;

    if(XY_OK != xy_lm_CellThreshAdjReq(&tReq))
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    *prsp_cmd = (char*)xy_malloc(100);  
    snprintf(*prsp_cmd, 100, "\r\nOK\r\n");
}

void xy_api_test_hwIms_MeasRptAdjReq(char* at_buf, char **prsp_cmd)
{
    MeasRptAdjReq tReq         = { 0 };
    int rsrpRptAdjust, rsrqRptAdjust;

    if(at_parse_param(",%d,%d", at_buf, &rsrpRptAdjust, &rsrqRptAdjust) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    tReq.rsrpRptAdjust = rsrpRptAdjust;
    tReq.rsrqRptAdjust = rsrqRptAdjust;

    if(XY_OK != xy_lm_MeasRptAdjReq(&tReq))
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    *prsp_cmd = (char*)xy_malloc(100);  
    snprintf(*prsp_cmd, 100, "\r\nOK\r\n");

}

void xy_api_test_hwIms_CellThreshAdjReq(char* at_buf, char **prsp_cmd)
{
    MeasTriggerThreshAdjReq tReq         = { 0 };
    int rsrpMeasAdjust, rsrqMeasAdjust;

    if(at_parse_param(",%d,%d", at_buf, &rsrpMeasAdjust, &rsrqMeasAdjust) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    tReq.rsrpMeasAdjust = rsrpMeasAdjust;
    tReq.rsrqMeasAdjust = rsrqMeasAdjust;
    if(XY_OK != xy_lm_MeasTriggerThreshAdjReq(&tReq))
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    *prsp_cmd = (char*)xy_malloc(100);  
    snprintf(*prsp_cmd, 100, "\r\nOK\r\n");
}

void xy_api_test_hwIms_MeasTriggerThreshAdjReq(char* at_buf, char **prsp_cmd)
{
    L2CongestReportSetReq tReq         = { 0 };
    L2CongestReportSetCnf *pCnf;   
    int enable, bearId, normal, warning, error, discard1, discard2;

    if(at_parse_param(",%d,%d,%d,%d,%d,%d,%d", at_buf,
        &enable, &bearId, &normal, &warning, &error, &discard1, &discard2) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    tReq.enable = enable;
    tReq.bearId = bearId;
    tReq.normal = normal;
    tReq.warning = warning;
    tReq.error = error;
    tReq.discard1 = discard1;
    tReq.discard2 = discard2;
    if(XY_OK != xy_lm_L2CongestReportSetReq(&tReq, &pCnf))
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    *prsp_cmd = (char*)xy_malloc(100);  
    snprintf(*prsp_cmd, 100, "\r\n+L2_CONGEST_RT_SET:rc=%d\r\\r\nOK\r\n", pCnf->rc);
    xy_free(pCnf);
}

void xy_api_test_hwIms_BlerSetReq(char* at_buf, char **prsp_cmd)
{
    BerSetReq tReq         = { 0 };
    BerSetCnf *pCnf;
    int level1, level2;

    if(at_parse_param(",%d,%d", at_buf, &level1, &level2) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    tReq.level1 = level1;
    tReq.level2 = level2;
    if(XY_OK != xy_lm_BerSetReq(&tReq, &pCnf))
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }

    *prsp_cmd = (char*)xy_malloc(100);  
    snprintf(*prsp_cmd, 100, "\r\n+BER_SET:rc=%d\r\n\r\nOK\r\n", pCnf->rc);
}

void xy_api_test_hwIms(char* at_buf, char **prsp_cmd)
{
    char subcmd[30] = {0};

    xy_printf(0,USER_LOG, DEBUG_LOG, "%s", at_buf);
    if(at_parse_param(",%30s", at_buf, subcmd) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return;
    }
    xy_printf(0,USER_LOG, DEBUG_LOG, "%s", subcmd);
    
    at_buf += strlen(subcmd) + 1;
    if(0 == strcmp(subcmd, "OPEN_IND"))
    {
        *prsp_cmd = (char*)xy_malloc(20);
        snprintf(*prsp_cmd, 20, "\r\nOK\r\n");
        
        xy_atc_registerPSEventCallback(D_XY_PS_REG_EVENT_HW_LM_INTERFACE, xy_api_test_hwIms_interface_ind);
    }
    else if(0 == strcmp(subcmd, "SIM_AUTH"))
    {
        xy_api_test_hwIms_SimAuthReq(at_buf, prsp_cmd);
    }
    else if(0 == strcmp(subcmd, "SET_APN"))
    {
        xy_api_test_hwIms_SetApnReq(at_buf, prsp_cmd);
    }
    else if(0 == strcmp(subcmd, "ACT_PDN"))
    {
        xy_api_test_hwIms_ActPdnReq(at_buf, prsp_cmd);
    }
    else if(0 == strcmp(subcmd, "SIM_READ"))
    {
        xy_api_test_hwIms_SimReadReq(at_buf, prsp_cmd);
    }
    else if(0 == strcmp(subcmd, "START_PS"))
    {
        xy_api_test_hwIms_StartPsReq(at_buf, prsp_cmd);
    }
     else if(0 == strcmp(subcmd, "GET_EMM_TIMER_LEN"))
    {
        xy_api_test_hwIms_GetTimerLenReq(at_buf, prsp_cmd);
    }
    else if(0 == strcmp(subcmd, "EMERGENCY_ATTACH"))
    {
        xy_api_test_hwIms_EmergencyAttReq(at_buf, prsp_cmd);
    }
    else if(0 == strcmp(subcmd, "IGNOR_3411_TIMER"))
    {
        xy_api_test_hwIms_Ignor3411TimerReq(at_buf, prsp_cmd);
    }
    else if(0 == strcmp(subcmd, "CELL_THRESH_ADJ"))
    {
        xy_api_test_hwIms_CellThreshReq(at_buf, prsp_cmd);
    }
    else if(0 == strcmp(subcmd, "MEAS_RPT_ADJ"))
    {
        xy_api_test_hwIms_MeasRptAdjReq(at_buf, prsp_cmd);
    }
    else if(0 == strcmp(subcmd, "CELL_THRESH_ADJ"))
    {
        xy_api_test_hwIms_CellThreshAdjReq(at_buf, prsp_cmd);
    }
    else if(0 == strcmp(subcmd, "MEAS_TRIGGER_THRESH_ADJ"))
    {
        xy_api_test_hwIms_MeasTriggerThreshAdjReq(at_buf, prsp_cmd);
    }
    else if(0 == strcmp(subcmd, "BER_SET"))
    {
        xy_api_test_hwIms_BlerSetReq(at_buf, prsp_cmd);
    }
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
    }
}

#endif

unsigned char xy_APITEST(char* at_buf, char **prsp_cmd)
{
    char subcmd[10] = {0};
    
    if(at_parse_param(",%10s", at_buf, subcmd) != XY_OK)
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return AT_END;
    }
#ifdef HW_LM_SUPPORT
    if(0 == strcmp(subcmd, "HW"))
    {
        xy_api_test_hwIms(strstr(at_buf, subcmd) + strlen(subcmd), prsp_cmd);
    }
#endif
    else
    {
        *prsp_cmd = AT_PLAT_CME_ERR(XY_Err_Parameter);
        return AT_END;
    }
    
    return AT_END;
}



