/**********************************************************************/
/**
 * @file yx_ia_http.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-18
 * @version V1.0
 * @brief http模块接口适配
 **********************************************************************/
#ifndef YX_IA_HTTP_H
#define YX_IA_HTTP_H

#include "yx_type.h"
#include "module_id_def.h"
#define IA_HTTP_ERRNO            (0x80 + YX_FUNC_MODULE_ID_HTTP)

/**
 * @brief 错误码枚举
 * @ingroup ia_http
 */
typedef enum {
    IA_HTTP_SUCCESS = RTN_OK,                                   /**< 成功 */
    IA_HTTP_INVALID_PARAM_ERR = (IA_HTTP_ERRNO << 24) | 0x01,   /**< 参数错误 */
    IA_HTTP_EXECUTE_FAILED    = (IA_HTTP_ERRNO << 24) | 0x02,   /**< 执行失败 */
    IA_HTTP_MEMORY_ERR        = (IA_HTTP_ERRNO << 24) | 0x03,   /**< 内存分配失败 */
    IA_HTTP_INIT_ERR          = (IA_HTTP_ERRNO << 24) | 0x04,   /**< 初始化失败 */
    IA_HTTP_CONFIG_ERR        = (IA_HTTP_ERRNO << 24) | 0x05,   /**< 配置错误 */
    IA_HTTP_REQUEST_ERR       = (IA_HTTP_ERRNO << 24) | 0x06,   /**< 请求失败 */ 
    IA_HTTP_RESPONSE_ERR      = (IA_HTTP_ERRNO << 24) | 0x07,   /**< 响应错误 */
    IA_HTTP_SSL_ERR           = (IA_HTTP_ERRNO << 24) | 0x08,   /**< SSL错误 */
    IA_HTTP_NETWORK_ERR       = (IA_HTTP_ERRNO << 24) | 0x09,   /**< 网络错误 */
    IA_HTTP_TIMEOUT_ERR       = (IA_HTTP_ERRNO << 24) | 0x0A,   /**< 超时错误 */
    IA_HTTP_UNKNOWN_ERR       = (IA_HTTP_ERRNO << 24) | 0xFF,   /**< 未知错误 */
} ia_http_errno_e;

/**
 * @brief http客户端句柄类型定义
 * @ingroup ia_http
 */
typedef struct yx_ia_http_client_t yx_ia_http_client_t;     /**< http客户端句柄 */

/**
 * @brief http请求类型枚举定义
 * @ingroup ia_http
 */
typedef enum {
    HTTP_CLIENT_REQUEST_NONE = 0,
    HTTP_CLIENT_REQUEST_GET,                /**< GET请求 */
    HTTP_CLIENT_REQUEST_POST,               /**< POST请求 */
    HTTP_CLIENT_REQUEST_PUT,                /**< PUT请求 */
    HTTP_CLIENT_REQUEST_DELETE,             /**< DELETE请求 */
    HTTP_CLIENT_REQUEST_HEAD,               /**< HEAD请求 */
    HTTP_CLIENT_REQUEST_MAX
} yx_ia_http_request_type_e;

/**
 * @brief http请求回调事件枚举定义
 * @ingroup ia_http
 */
typedef enum{
    HTTP_CALLBACK_EVENT_REQ_START = 1,      /**< 请求启动成功事件 */
    HTTP_CALLBACK_EVENT_RSP_HEADER,         /**< 接收到报头事件 */
    HTTP_CALLBACK_EVENT_RSP_CONTENT,        /**< 接收到消息体事件 */
    HTTP_CALLBACK_EVENT_RSP_END,            /**< 请求响应结束事件 */
    HTTP_CALLBACK_EVENT_UPLOAD_PROGRESS,    /**< 上传进度事件 */
    HTTP_CALLBACK_EVENT_UPLOAD_END,         /**< 上传结束事件*/
    HTTP_CALLBACK_EVENT_ERROR,              /**< 请求失败事件 */
}yx_ia_http_client_callback_event_e;

/**
 * @brief ssl验证级别枚举定义
 * @ingroup ia_http
 */
typedef enum {
    HTTP_SSL_NONE = 0,                      /**< 不使用SSL */
    HTTP_SSL_VERIFY_NONE,                   /**< 不验证服务器证书 */
    HTTP_SSL_VERIFY_SERVER,                 /**< 验证服务器证书 */
    HTTP_SSL_VERIFY_SERVER_CLIENT           /**< 双向验证 */
} yx_ia_http_ssl_verify_level_e;

/**
 * @brief ssl证书类型定义
 * @ingroup ia_http
 */
typedef enum {
    HTTP_SSL_CERT_TYPE_NONE = 0,            /**< 不使用证书 */
    HTTP_SSL_CERT_TYPE_FILE,                /**< 文件证书 */
    HTTP_SSL_CERT_TYPE_BUFFER,              /**< 缓存证书 */
    HTTP_SSL_CERT_TYPE_MAX                  /**< 最大证书类型 */
} yx_ia_http_ssl_cert_type_e;

/**
 * @brief 请求配置结构体定义
 * @ingroup ia_http
 */
typedef struct {
    const INT8U *url;                       /**< 请求URL */
    const INT8U *headers;                   /**< 请求头 */
    const INT8U *postdata;                  /**< POST数据 */
    INT32U postdata_len;                    /**< POST数据长度 */
    const INT8U *post_file_path;            /**< 文件路径（用于POST文件） */
    INT32U connect_timeout;                 /**< 连接超时时间（秒） */
    INT32U response_timeout;                /**< 响应超时时间（秒） */
    yx_ia_http_request_type_e request_type; /**< 请求类型，见yx_ia_http_request_type_e */
} yx_ia_http_cli_config_t;

/**
 * @brief ssl配置结构体定义
 * @ingroup ia_http
 */
typedef struct {
    yx_ia_http_ssl_verify_level_e verify_level; /**< SSL验证级别,见yx_ia_http_ssl_verify_level_e */
    yx_ia_http_ssl_cert_type_e cert_type;       /**< 证书类型,见yx_ia_http_ssl_cert_type_e */
    INT8U ssl_contxtid;                         /**< SSL上下文ID（范围0-9,0为默认,不可用于mqtts） */

    const INT8U *root_crt_path;                 /**< CA证书路径 */
    const INT8U *client_crt_path;               /**< 客户端证书路径 */
    const INT8U *client_key_path;               /**< 客户端私钥路径 */

    CHAR* root_crt_buffer;                      /**< CA 证书buffer指针 */
    CHAR* client_crt_buffer;                    /**< 本地证书buffer指针 */
    CHAR* client_key_buffer;                    /**< 客户端私钥buffer指针 */

    CHAR* client_key_pwd;                       /**< 客户端私钥密码 */
} yx_ia_http_ssl_config_t;

/**
 * @brief HTTP客户端回调函数结构体定义
 * @ingroup ia_http
 */
typedef struct {
    VOID (*http_callback)(INT32S event, CHAR *buf, INT32S buf_len, INT32S total_len, INT32S resp_code);  /**< HTTP回调函数指针 */
} yx_ia_http_callback_t;

/**
 * @brief 初始化HTTP客户端
 * @ingroup ia_http
 * @param[in] callback 回调函数指针，接收HTTP请求结果
 * @return yx_ia_http_client_t*
 * @retval 非NULL，成功，返回客户端句柄
 * @retval NULL，失败
 */
yx_ia_http_client_t* yx_ia_http_init(yx_ia_http_callback_t *callback);

/**
 * @brief 终止HTTP客户端并释放资源
 * @ingroup ia_http
 * @param[in] client 客户端句柄指针的地址
 * @note 该函数会释放客户端句柄指向的内存，并将指针置为NULL
 * @return VOID
 */
VOID yx_ia_http_term(yx_ia_http_client_t **client);

/**
 * @brief 配置HTTP的请求参数
 * @ingroup ia_http
 * @param[in] client 客户端句柄
 * @param[in] config 请求参数, @see yx_ia_http_cli_config_t
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval 其他值 失败
 */
INT32S yx_ia_http_set_cli_cfg(yx_ia_http_client_t *client, const yx_ia_http_cli_config_t *config);

/**
 * @brief 配置HTTP的SSL参数
 * @ingroup ia_http
 * @param[in] client 客户端句柄
 * @param[in] config SSL参数, @see yx_ia_http_ssl_config_t
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval 其他值 失败
 */
INT32S yx_ia_http_set_ssl_cfg(yx_ia_http_client_t *client, const yx_ia_http_ssl_config_t *config);

/**
 * @brief 发起HTTP请求
 * @ingroup ia_http
 * @param[in] client 客户端句柄
 * @return INT32S
 * @retval RTN_OK(0) 成功
 * @retval 其他值 失败
 * @warning 注意该接口阻塞等待响应
 */
INT32S yx_ia_http_request(yx_ia_http_client_t *client);

#endif /* YX_IA_HTTP_H */
