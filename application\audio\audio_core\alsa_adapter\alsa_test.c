#include "alsa.h"
#include "alsa_extra.h"
#include "hal_trace.h"

// #define ALSA_SINGLE_INSTANCE_TEST
// #define ALSA_MULTI_INSTANCE_TEST
// #define ALSA_CAPTURE_LOOP_PLAYBACK_TEST

#ifdef ALSA_SINGLE_INSTANCE_TEST
int alsa_test_single_instance_playback(void);
#endif

#ifdef ALSA_MULTI_INSTANCE_TEST
int alsa_test_multi_instance_playback(void);
#endif

#ifdef ALSA_CAPTURE_LOOP_PLAYBACK_TEST
int alsa_test_capture_loop_playback(bool onoff);
#endif

#ifdef ALSA_SINGLE_INSTANCE_TEST
static const uint32_t test_pcm_1[] = {
#include "1000_16k_2ch_16bit.pcm"
};

int alsa_test_single_instance_playback(void)
{
    alsa_handle_t *h = NULL;

    h = alsa_open(ALSA_MODE_OUT, 16000, 2, 16);
    if (h)
    {
        alsa_start(h);
    }
    else
    {
        return -1;
    }
    alsa_write(h, (uint8_t *)test_pcm_1, sizeof(test_pcm_1));
    alsa_stop(h);
    alsa_close(h);

    h = alsa_open(ALSA_MODE_OUT, 16000, 2, 16);
    if (h)
    {
        alsa_start(h);
    }
    else
    {
        return -1;
    }
    alsa_write(h, (uint8_t *)test_pcm_2, sizeof(test_pcm_2));
    alsa_stop(h);
    alsa_close(h);

    return 0;
}
#endif // ALSA_SINGLE_INSTANCE_TEST

#ifdef ALSA_MULTI_INSTANCE_TEST

static const uint32_t test_pcm_1[] = {
#include "1000_16k_1ch_16bit-0db.pcm"
// #include "1000_16k_1ch_16bit-20db.pcm"
};

static const uint32_t test_pcm_2[] = {
#include "500_16k_1ch_16bit-0db.pcm"
// #include "500_16k_1ch_16bit-20db.pcm"
};

typedef struct
{
    alsa_handle_t *h;
    uint8_t *ptr;
    uint32_t length;
} alsa_multi_instance_test_t;

static alsa_multi_instance_test_t test_arg_1;
static alsa_multi_instance_test_t test_arg_2;

static int alsa_multi_instance_test_finish_flag = 0;

static void alsa_multi_instance_test_task(const void *argument)
{
    alsa_multi_instance_test_t *test_arg = (alsa_multi_instance_test_t *)argument;
    uint32_t remain_len = test_arg->length;
    uint8_t *ptr = test_arg->ptr;
    alsa_handle_t *h = test_arg->h;
    uint32_t write_len;

    while (remain_len)
    {
        write_len = remain_len > 4096 ? 4096 : remain_len;
        alsa_write(h, ptr, write_len);
        ptr += write_len;
        remain_len -= write_len;
    }
    alsa_stop(h);
    alsa_close(h);
    alsa_multi_instance_test_finish_flag++;
    osThreadExit();
}
osThreadDef(alsa_multi_instance_test_task, osPriorityNormal, 2, 8 * 1024, "alsa_test");

int alsa_test_multi_instance_playback(void)
{
    alsa_handle_t *h1 = alsa_open(ALSA_MODE_OUT, 16000, 1, 16);
    if (h1)
    {
        alsa_start(h1);
    }
    else
    {
        return -1;
    }

    alsa_handle_t *h2 = alsa_open(ALSA_MODE_OUT, 16000, 1, 16);
    if (h2)
    {
        alsa_start(h2);
    }
    else
    {
        return -1;
    }

    memset(&test_arg_1, 0, sizeof(test_arg_1));
    test_arg_1.h = h1;
    test_arg_1.ptr = (uint8_t *)test_pcm_1;
    test_arg_1.length = sizeof(test_pcm_1);
    osThreadCreate(osThread(alsa_multi_instance_test_task), &test_arg_1);

    test_arg_2.h = h2;
    test_arg_2.ptr = (uint8_t *)test_pcm_2;
    test_arg_2.length = sizeof(test_pcm_2);
    osThreadCreate(osThread(alsa_multi_instance_test_task), &test_arg_2);

    while (1)
    {
        if (alsa_multi_instance_test_finish_flag == 2)
        {
            alsa_multi_instance_test_finish_flag = 0;
            break;
        }
        else
        {
            osDelay(1000);
        }
    }

    return 0;
}
#endif // ALSA_MULTI_INSTANCE_TEST

#ifdef ALSA_CAPTURE_LOOP_PLAYBACK_TEST
static alsa_handle_t *alsa_capture = NULL;
static alsa_handle_t *alsa_playback = NULL;
static osThreadId alsa_capture_loop_playback_thread_id = NULL;
static bool alsa_capture_loop_playback_thread_exit = false;

#define ALSA_CAPTURE_LOOP_PLAYBACK_BUFFER_SIZE (1024 * 16)
static uint8_t
    alsa_capture_loop_playback_buffer[ALSA_CAPTURE_LOOP_PLAYBACK_BUFFER_SIZE] = {0};

static void alsa_capture_loop_playback_thread(void const *arg)
{
    uint32_t length;

    if (alsa_capture)
        alsa_start(alsa_capture);

    if (alsa_playback)
        alsa_start(alsa_playback);

    while (1)
    {
        if (alsa_capture_loop_playback_thread_exit)
        {
            TRACE(0, "%s exit", __func__);
            break;
        }

        length = 0;
        if (alsa_capture)
        {
            length = alsa_read(alsa_capture,
                               alsa_capture_loop_playback_buffer,
                               ALSA_CAPTURE_LOOP_PLAYBACK_BUFFER_SIZE);
            // TRACE(0, "alsa_read length %d", length);
        }
        if (length && alsa_playback)
        {
            length = alsa_write(alsa_playback,
                                alsa_capture_loop_playback_buffer,
                                length);
            // TRACE(0, "alsa_write length %d", length);
        }
        else
        {
            osDelay(2);
        }
    }

    alsa_capture_loop_playback_thread_exit = false;

    osThreadExit();
}

osThreadDef(
    alsa_capture_loop_playback_thread,
    osPriorityHigh,
    1, (1024 * 8), "alsa_loop_test");

int alsa_test_capture_loop_playback(bool onoff)
{
    if (onoff)
    {
        if (!alsa_capture)
        {
            alsa_capture = alsa_open(ALSA_MODE_IN, 16000, 2, 16);
            if (!alsa_capture)
            {
                TRACE(0, "%s alsa_capture open error", __func__);
                goto error_exit;
            }
        }

        if (!alsa_playback)
        {
            alsa_playback = alsa_open(ALSA_MODE_OUT, 16000, 2, 16);
            if (!alsa_playback)
            {
                TRACE(0, "%s alsa_playback open error", __func__);
                goto error_exit;
            }
        }

        if (!alsa_capture_loop_playback_thread_id)
        {
            alsa_capture_loop_playback_thread_exit = false;
            alsa_capture_loop_playback_thread_id = osThreadCreate(
                osThread(alsa_capture_loop_playback_thread),
                NULL);
            if (!alsa_capture_loop_playback_thread_id)
            {
                TRACE(0, "%s alsa_capture_loop_playback_thread open error", __func__);
                goto error_exit;
            }
        }

        return 0;
    }
    else
    {
        if (alsa_capture_loop_playback_thread_id)
        {
            alsa_capture_loop_playback_thread_exit = true;
            do
            {
                osDelay(10);
            } while (alsa_capture_loop_playback_thread_exit);
            alsa_capture_loop_playback_thread_id = NULL;
        }
        if (alsa_capture)
        {
            alsa_stop(alsa_capture);
            alsa_close(alsa_capture);
            alsa_capture = NULL;
        }
        if (alsa_playback)
        {
            alsa_stop(alsa_playback);
            alsa_close(alsa_playback);
            alsa_playback = NULL;
        }

        return 0;
    }

error_exit:

    if (alsa_capture)
    {
        alsa_close(alsa_capture);
        alsa_capture = NULL;
    }
    if (alsa_playback)
    {
        alsa_close(alsa_playback);
        alsa_playback = NULL;
    }
    if (alsa_capture_loop_playback_thread_id)
    {
        osThreadTerminate(alsa_capture_loop_playback_thread_id);
        alsa_capture_loop_playback_thread_id = NULL;
    }

    return -1;
}
#endif // ALSA_CAPTURE_LOOP_PLAYBACK_TEST