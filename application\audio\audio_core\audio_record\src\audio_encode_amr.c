#include "audio_record.h"
#include "interf_enc.h"
#include "xy_system.h"

static const char *amrStr[4] = {
    "#!AMR\n",         // 6 bytes
    "#!AMR-WB\n",      // 9 bytes
    "#!AMR_MC1.0\n",   // 12 bytes
    "#!AMR-WB_MC1.0\n" // 15 bytes
};

static uint8_t amr_buffer[62] = {0};
static void * amr_encoder = NULL;

int32_t audio_encode_amr_process(uint8_t * data, uint32_t len, audio_rec_t *audio_rec)
{
    int32_t ret = REC_ERR_OK;
    int32_t write_len;
    int size;

    if(audio_rec->state_get() == REC_PAUSE)
    {
        return REC_ERR_OK;
    }

    if(audio_rec->dest->dest_arg->write_len == 0)
    {
        if(audio_rec->enc == REC_ENC_AMRDTX)
        {
            amr_encoder = Encoder_Interface_init(1);
        }
        else
        {
            amr_encoder = Encoder_Interface_init(0);
        }
        audio_rec->dest->write((void *)amrStr[0], strlen(amrStr[0]), audio_rec->dest->dest_arg);
    }

    xy_assert(len % 320 == 0);
    for(uint32_t i = 0; i < len; i += 320)
    {
        if(audio_rec->state_get() == REC_STOP)
        {
            ret = REC_ERR_STOP;
            goto exit;
        }
        else
        {
            size = Encoder_Interface_Encode(amr_encoder, audio_rec->enc - REC_ENC_AMR475, (uint16_t *)(data + i), amr_buffer, 0);
            write_len = audio_rec->dest->write(amr_buffer, (uint32_t)size, audio_rec->dest->dest_arg);
            if(write_len < size)
            {
                ret = REC_ERR_DEST_FULL;
                goto exit;
            }
            audio_rec->dest->dest_arg->real_duration_ms += 20; // 20ms per frame for AMR-NB
            if(audio_rec->dest->dest_arg->real_duration_ms >= audio_rec->dest->dest_arg->duration_ms)
            {
                ret = REC_ERR_DEST_FULL;
                goto exit;
            }
        }
    }

    return REC_ERR_OK;

exit:
    if(amr_encoder)
    {
        Encoder_Interface_exit(amr_encoder);
        amr_encoder = NULL;
    }

    return ret;
}
