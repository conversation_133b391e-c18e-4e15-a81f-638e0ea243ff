#include "alsa_config.h"
#include "alsa_extra.h"
#include "hal_trace.h"
#include <stdlib.h>
#include <stddef.h>
#include <string.h>
#include "factory_nv.h"
#include "heap_api.h"

/* stereo => mono */

static int16_t sample_stereo2momo_16bit(int16_t sample_1, int16_t sample_2)
{
    int32_t sample = (int32_t)sample_1 + (int32_t)sample_2;
    return (int16_t)(sample / 2);
}

int alsa_stereo_to_mono_16bit(uint8_t *buf, uint32_t *len)
{
    if (buf == NULL)
    {
        *len = 0;
        return -1;
    }

    uint32_t remain_len = *len & (~0x03);
    uint32_t sample_count = remain_len / sizeof(int16_t) / 2;
    int16_t sample_1;
    int16_t sample_2;
    int16_t sample;
    uint8_t *p_in = buf;
    uint8_t *p_out = buf;
    uint32_t out_size = 0;
    while (sample_count--)
    {
        sample_1 = *(int16_t *)p_in;
        p_in += sizeof(int16_t);
        sample_2 = *(int16_t *)p_in;
        p_in += sizeof(int16_t);
        sample = sample_stereo2momo_16bit(sample_1, sample_2);
        *(int16_t *)p_out = sample;
        p_out += sizeof(int16_t);
        out_size += sizeof(int16_t);
    }
    *len = out_size;
    return 0;
}

static int32_t sample_stereo2momo_24bit(int32_t sample_1, int32_t sample_2)
{

    return sample_1;
}

int alsa_stereo_to_mono_24bit(uint8_t *buf, uint32_t *len)
{
    if (buf == NULL)
    {
        *len = 0;
        return -1;
    }

    uint32_t remain_len = *len & (~0x07);
    uint32_t sample_count = remain_len / sizeof(int32_t) / 2;
    int32_t sample_1;
    int32_t sample_2;
    int32_t sample;
    uint8_t *p_in = buf;
    uint8_t *p_out = buf;
    uint32_t out_size = 0;
    while (sample_count--)
    {
        sample_1 = *(int32_t *)p_in;
        p_in += sizeof(int32_t);
        sample_2 = *(int32_t *)p_in;
        p_in += sizeof(int32_t);
        sample = sample_stereo2momo_24bit(sample_1, sample_2);
        *(int32_t *)p_out = sample;
        p_out += sizeof(int32_t);
        out_size += sizeof(int32_t);
    }
    *len = out_size;
    return 0;
}

static int32_t sample_stereo2momo_32bit(int32_t sample_1, int32_t sample_2)
{

    return sample_1;
}

int alsa_stereo_to_mono_32bit(uint8_t *buf, uint32_t *len)
{
    if (buf == NULL)
    {
        *len = 0;
        return -1;
    }

    uint32_t remain_len = *len & (~0x07);
    uint32_t sample_count = remain_len / sizeof(int32_t) / 2;
    int32_t sample_1;
    int32_t sample_2;
    int32_t sample;
    uint8_t *p_in = buf;
    uint8_t *p_out = buf;
    uint32_t out_size = 0;
    while (sample_count--)
    {
        sample_1 = *(int32_t *)p_in;
        p_in += sizeof(int32_t);
        sample_2 = *(int32_t *)p_in;
        p_in += sizeof(int32_t);
        sample = sample_stereo2momo_32bit(sample_1, sample_2);
        *(int32_t *)p_out = sample;
        p_out += sizeof(int32_t);
        out_size += sizeof(int32_t);
    }
    *len = out_size;
    return 0;
}

/* mono => stereo */

static uint8_t alsa_mono_to_stereo_new_buf[ALSA_PLAYBACK_INSTANCE_COUNT * ALSA_WRITE_DATA_MAX_SIZE * 2] NO_INIT;
int alsa_mono_to_stereo_16bit(uint8_t id, uint8_t **buf, uint32_t *len)
{
    if (*buf == NULL)
    {
        *len = 0;
        return -1;
    }

    if (*len == 0)
    {
        return -1;
    }

    if (*len > ALSA_WRITE_DATA_MAX_SIZE)
    {
        ASSERT(0, "%s input len %d, larger than ALSA_WRITE_DATA_MAX_SIZE %d", __func__, *len, ALSA_WRITE_DATA_MAX_SIZE);
    }

    uint32_t length = *len;
    int16_t *origin_buf = (int16_t *)*buf;
    uint32_t offset = id * ALSA_WRITE_DATA_MAX_SIZE * 2;
    int16_t *new_buf = (int16_t *)&alsa_mono_to_stereo_new_buf[offset];
    // memset((uint8_t *)new_buf, 0, ALSA_WRITE_DATA_MAX_SIZE * 2);
    uint32_t new_buf_offset = 0;
    uint32_t origin_buf_offset = 0;
    while (1)
    {
        new_buf[new_buf_offset] = origin_buf[origin_buf_offset];
        new_buf_offset += 1;
        #if(AUDIO_CODEC_TYPE == 3) // TM8211左右声道差分输出（软件差分）
        new_buf[new_buf_offset] = origin_buf[origin_buf_offset] * (-1);
        #else
        new_buf[new_buf_offset] = origin_buf[origin_buf_offset];
        #endif
        new_buf_offset += 1;

        origin_buf_offset += 1;
        if (origin_buf_offset == length / sizeof(int16_t))
        {
            break;
        }
    }

    *len = new_buf_offset * sizeof(int16_t);
    *buf = (uint8_t *)new_buf;

    return 0;
}

uint8_t *alsa_mono_to_stereo_16bit_with_new_buffer(uint8_t **in_out_buf, uint32_t *in_out_len)
{
    if (in_out_buf == NULL)
    {
        TRACE(0, "%s in_out_buf NULL error", __func__);
        return NULL;
    }

    if (in_out_len == NULL)
    {
        TRACE(0, "%s in_out_len NULL error", __func__);
        return NULL;
    }

    uint32_t total_len = *in_out_len;
    uint8_t *origin_buf = *in_out_buf;
    uint8_t *new_buf = (uint8_t *)audio_calloc(total_len * 2, sizeof(uint8_t));
    if (!new_buf)
    {
        TRACE(0, "%s alloc new_buf error. alloc size %d", __func__, total_len * 2);
        return NULL;
    }

    uint32_t total_sample_count = total_len / sizeof(int16_t);
    int16_t *mono_buf = (int16_t *)origin_buf;
    int16_t *stereo_buf = (int16_t *)new_buf;
    uint32_t mono_iter = 0;
    uint32_t stereo_iter = 0;

    while (total_sample_count)
    {
        stereo_buf[stereo_iter] = mono_buf[mono_iter];
        stereo_buf[stereo_iter + 1] = mono_buf[mono_iter];
        total_sample_count--;
        stereo_iter += 2;
        mono_iter += 1;
    }
    *in_out_buf = new_buf;
    *in_out_len = stereo_iter * sizeof(int16_t);
    return new_buf;
}

uint8_t *alsa_mono_to_stereo_32bit_with_new_buffer(uint8_t **in_out_buf, uint32_t *in_out_len)
{
    if (in_out_buf == NULL)
    {
        TRACE(0, "%s in_out_buf NULL error", __func__);
        return NULL;
    }

    if (in_out_len == NULL)
    {
        TRACE(0, "%s in_out_len NULL error", __func__);
        return NULL;
    }

    uint32_t total_len = *in_out_len;
    uint8_t *origin_buf = *in_out_buf;
    uint8_t *new_buf = (uint8_t *)audio_calloc(total_len * 2, sizeof(uint8_t));
    if (!new_buf)
    {
        TRACE(0, "%s alloc new_buf error. alloc size %d", __func__, total_len * 2);
        return NULL;
    }

    uint32_t total_sample_count = total_len / sizeof(int32_t);
    int32_t *mono_buf = (int32_t *)origin_buf;
    int32_t *stereo_buf = (int32_t *)new_buf;
    uint32_t mono_iter = 0;
    uint32_t stereo_iter = 0;

    while (total_sample_count)
    {
        stereo_buf[stereo_iter] = mono_buf[mono_iter];
        stereo_buf[stereo_iter + 1] = mono_buf[mono_iter];
        total_sample_count--;
        stereo_iter += 2;
        mono_iter += 1;
    }
    *in_out_buf = new_buf;
    *in_out_len = stereo_iter * sizeof(int32_t);
    return new_buf;
}

int alsa_mono_to_stereo_free_new_buffer(uint8_t *new_buffer_ptr)
{
    if (new_buffer_ptr)
    {
        audio_free(new_buffer_ptr);
        return 0;
    }
    return -1;
}

void alsa_16bit_volume_ctrl(int16_t *out, int16_t *in, uint32_t *in_out_len, uint8_t volume)
{
    uint32_t i;
    uint32_t len = (*in_out_len) / sizeof(int16_t);

    if(volume >= 15)
    {
        return;
    }

    for(i = 0; i < len; i ++)
    {
        out[i] = in[i] >> (15 - volume);
    }
}

/* 16bit => 10bit */
// for pwm audio，数值归一化到[0, 1024]
void alsa_convert_16bit_to_10bit(int16_t *out, int16_t *in, uint32_t *in_out_len)
{
    uint32_t i;
    uint32_t len = (*in_out_len) / sizeof(int16_t);
    uint16_t temp;

    for(i = 0; i < len; i ++)
    {
        temp = (uint16_t)in[i] + 0x7fff;
        out[i] = temp >> 6;
    }
}

/* 16bit => 32bit */

void alsa_convert_16bit_to_32bit(int32_t *out, int16_t *in, uint32_t *in_out_len)
{
    uint32_t len = (*in_out_len) / sizeof(int16_t);
    if (len == 0)
    {
        *in_out_len = 0;
        return;
    }

    for (int i = len - 1; i >= 0; i--)
    {
        out[i] = ((int32_t)in[i] << 16);
    }
    *in_out_len = len * sizeof(int16_t) * (sizeof(int32_t) / sizeof(int16_t));
}

/* 16bit => 24bit */

void alsa_convert_16bit_to_24bit(uint8_t *out, int16_t *in, uint32_t *in_out_len)
{
    uint32_t i;
    uint32_t len = (*in_out_len) / sizeof(int16_t);
    if (len == 0)
    {
        *in_out_len = 0;
        return;
    }

    union{
        uint32_t u32;
        int8_t u8[4];
    }temp_value;

    if(g_softap_fac_nv->audio_cfg & 1)  //单端输出
     {
        for (i = 0; i < len; i++)
        {
            temp_value.u32 = ((int32_t)in[i] << 8);
            out[i * 3] = temp_value.u8[0];
            out[i * 3 + 1] = temp_value.u8[1];
            out[i * 3 + 2] = temp_value.u8[2];
        }
    }
    else  //差分输出
    {
        for (i = 0; i < len; i++)
        {
            temp_value.u32 = ((int32_t)in[i] << 8);
            out[i * 3] = temp_value.u8[0];
            out[i * 3 + 1] = temp_value.u8[1];
            out[i * 3 + 2] = temp_value.u8[2];
            i ++;
            temp_value.u32 *= -1;
            out[i * 3] = temp_value.u8[0];
            out[i * 3 + 1] = temp_value.u8[1];
            out[i * 3 + 2] = temp_value.u8[2];
        }
    }

    *in_out_len = len * 3;
}

void alsa_convert_stereo_24bit_to_mono_16bit(int16_t *out, uint8_t *in, uint32_t *in_out_len)
{
    uint32_t i;
    uint32_t len = (*in_out_len);
    uint32_t k = 0;

    if (len == 0)
    {
        *in_out_len = 0;
        return;
    }

    for(i = 3; i < len; i = i + 6)
    {
        out[k] = (((uint16_t)in[i + 2] << 8) | (uint16_t)in[i + 1]);
        k++;
    }

    *in_out_len = k * sizeof(int16_t);
}

uint8_t *alsa_convert_16bit_to_24bit_with_new_buffer(uint8_t **in_out_buf, uint32_t *in_out_len)
{
    if (in_out_len == NULL)
    {
        return NULL;
    }

    if (in_out_buf == NULL)
    {
        *in_out_len = 0;
        return NULL;
    }

    int16_t *data_16 = NULL;
    int32_t *data_32 = NULL;
    uint32_t len = *in_out_len;
    uint8_t *origin_buf = *in_out_buf;
    uint8_t *new_buf = (uint8_t *)audio_calloc(len * 2, sizeof(uint8_t));
    if (!new_buf)
    {
        TRACE(0, "%s new_buf alloc error. alloc_size %d", __func__, len * 2);
        return NULL;
    }
    data_16 = (int16_t *)origin_buf;
    data_32 = (int32_t *)new_buf;
    for (int i = 0; i < (int)(len / 2); i++)
    {
        *data_32 = data_16[i];
        *data_32 = (int32_t)(*data_32 << 8);
        data_32++;
    }
    *in_out_len = len * 2;
    *in_out_buf = new_buf;
    return new_buf;
}

uint8_t *alsa_convert_16bit_to_32bit_with_new_buffer(uint8_t **in_out_buf, uint32_t *in_out_len)
{
    if (in_out_len == NULL)
    {
        return NULL;
    }

    if (in_out_buf == NULL)
    {
        *in_out_len = 0;
        return NULL;
    }

    int16_t *data_16 = NULL;
    int32_t *data_32 = NULL;
    uint32_t len = *in_out_len;
    uint8_t *origin_buf = *in_out_buf;
    uint8_t *new_buf = (uint8_t *)audio_calloc(len * 2, sizeof(uint8_t));
    if (!new_buf)
    {
        TRACE(0, "%s new_buf alloc error. alloc_size %d", __func__, len * 2);
        return NULL;
    }
    data_16 = (int16_t *)origin_buf;
    data_32 = (int32_t *)new_buf;
    for (int i = 0; i < (int)(len / 2); i++)
    {
        *data_32 = data_16[i];
        *data_32 = (int32_t)(*data_32 << 16);
        data_32++;
    }
    *in_out_len = len * 2;
    *in_out_buf = new_buf;
    return new_buf;
}

int alsa_convert_16bit_free_new_buffer(uint8_t *new_buffer_ptr)
{
    if (new_buffer_ptr)
    {
        audio_free(new_buffer_ptr);
        return 0;
    }
    return -1;
}

/* 24bit => 32bit */

int alsa_convert_24bit_to_32bit(uint8_t *buf, uint32_t len)
{
    if (buf == NULL)
    {
        return -1;
    }

    int32_t *data_32 = (int32_t *)buf;
    for (int i = 0; i < (int)(len / sizeof(int32_t)); i++)
    {
        data_32[i] = data_32[i] << 8;
    }

    return 0;
}

/* 32bit => 24bit */

int alsa_convert_32bit_to_24bit(uint8_t *buf, uint32_t len)
{
    if (buf == NULL)
    {
        return -1;
    }

    int32_t *data_32 = (int32_t *)buf;
    for (int i = 0; i < (int)(len / sizeof(int32_t)); i++)
    {
        data_32[i] = data_32[i] >> 8;
    }

    return 0;
}

/* 32bit => 16bit */

int alsa_convert_32bit_to_16bit(uint8_t *buf, uint32_t *in_out_len)
{
    if (in_out_len == NULL)
    {
        return -1;
    }

    if (buf == NULL)
    {
        *in_out_len = 0;
        return -1;
    }

    uint32_t sample_count = *in_out_len / sizeof(int32_t);
    int32_t *data_32_ptr = (int32_t *)buf;
    int16_t *data_16_ptr = (int16_t *)buf;
    uint32_t offset_32 = 0;
    uint32_t offset_16 = 0;

    while (sample_count)
    {
        data_16_ptr[offset_16] = data_32_ptr[offset_32] >> 16;
        offset_32++;
        offset_16++;
        sample_count--;
    }
    *in_out_len = offset_16 * sizeof(int16_t);
    return 0;
}

/* 24bit => 16bit */

int alsa_convert_24bit_to_16bit(uint8_t *buf, uint32_t *in_out_len)
{
    if (in_out_len == NULL)
    {
        return -1;
    }

    if (buf == NULL)
    {
        *in_out_len = 0;
        return -1;
    }

    uint32_t sample_count = *in_out_len / sizeof(int32_t);
    int32_t *data_24_ptr = (int32_t *)buf;
    int16_t *data_16_ptr = (int16_t *)buf;
    uint32_t offset_24 = 0;
    uint32_t offset_16 = 0;

    while (sample_count)
    {
        data_16_ptr[offset_16] = (int16_t)(data_24_ptr[offset_24] >> 8);
        offset_24++;
        offset_16++;
        sample_count--;
    }
    *in_out_len = offset_16 * sizeof(int16_t);
    return 0;
}