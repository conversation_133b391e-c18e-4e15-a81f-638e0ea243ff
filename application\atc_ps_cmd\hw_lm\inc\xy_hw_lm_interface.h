#ifndef _XY_HW_LM_INTERFACE_H_
#define _XY_HW_LM_INTERFACE_H_

#include "xy_hw_lm_type.h"

#define  D_HIMS_FALSE  0
#define  D_HIMS_TRUE   1

#define D_CNF_MSG_RC_SUCC 0
#define D_CNF_MSG_RC_FAIL 1

/*****************************************************************************************
* 功能 1： PS 开机上报，携带 SIM 卡信息
* Type: IND
* SIG ID: IMS_CAM_START_IND
* Scenarios :
*****************************************************************************************/
#define MAX_IMSI_LEN 9
typedef struct {
#define D_HIMS_USIM_STATUS_VALID       0
#define D_HIMS_USIM_STATUS_NO_CARD     1
#define D_HIMS_USIM_STATUS_INVALID     2
#define D_HIMS_USIM_STATUS_NEED_PIN    3
#define D_HIMS_USIM_STATUS_NEED_PUK    4
#define D_HIMS_USIM_STATUS_CARD_BLOCK  5
    UINT8 usimStatus; //0:有效卡;1:无卡;2:无效卡;3:需要PIN:4: 需要PUK; 5:卡被锁
#define D_HIMS_CARD_STATE_NO_CHG    0
#define D_HIMS_CARD_STATE_CHG       1
#define D_HIMS_CARD_FILE_CHG        2
    UINT8 reason; // 0:卡状态没有改变 1:卡状态变化 2:卡文件变化
#define D_SIM_CARD_TYPE_ISIM  0
#define D_SIM_CARD_TYPE_USIM  1    
    UINT8 cardType; // 0:ISIM 卡 1:USIM 卡
    UINT8 isTestCard; // 是否是协议测试卡, 0:否  1:是
    UINT8 isThreeDigitalMnc; // 是否是三位 MNC，0:否  1:是
    UINT8 imsi[MAX_IMSI_LEN]; // SIM 的 IMSI
    UINT8 ucIsimFileFlg;  //reason=2时，指示是否是ISIM卡文件变化。0:USIM卡文件变化; 1:ISIM卡文件变化
    UINT16 usFileChgEfid; //reason=2时，对应的卡文件EFID
} ImsCamStartInd;

/*****************************************************************************************
* 功能 2： PS 驻网服务状态改变上报
* Type: IND
* SIG ID: IMS_CAM_SERVICE_CHANGE_IND
* Scenarios :
*****************************************************************************************/
typedef struct {
    UINT32 cellId;
    UINT16 mcc; //如46012: mcc=0x0460, mnc=0x012F; 如460123: mcc=0x0460, mnc=0x0123
    UINT16 mnc;
    UINT16 tac;
} CampedCell;

typedef struct {
    UINT8 voPsInd; // 网络是否支持 IMS 普通呼叫, 0: 不支持; 1: 支持
    UINT8 emsInd; // 网络是否支持 IMS 紧急呼叫, 0: 不支持; 1: 支持
#define D_SERVICE_STATIS_NORMAL_SERVICE   0  
#define D_SERVICE_STATIS_LIMITED_SERVICE  1
#define D_SERVICE_STATIS_NO_SERVICE       2
    UINT8 serviceStatus; // 0: NORMAL_SERVICE; 1: LIMITED_SERVICE; 2: NO_SERVICE
    UINT8 isRoaming; // 0: 非漫游; 1: 漫游
#define D_SERVICE_CHANGE_IND_ATT             0
#define D_SERVICE_CHANGE_IND_TAU             1
#define D_SERVICE_CHANGE_IND_TA_CHG_NO_TAU   2
#define D_SERVICE_CHANGE_IND_RESELECT        3
#define D_SERVICE_CHANGE_IND_DETACH          4
#define D_SERVICE_CHANGE_IND_OTHER           255
    UINT8 changeType; // 移动性管理原因, 0: ATTACH 1: TAU; 2: 小区切换未发生 TAU; 3:重选/handover; 4:DETACH;  0xFF: 其它补充
    CampedCell cellInfo;
} CamServiceChangeInd;

/*****************************************************************************************
* 功能 3： SIM 卡鉴权请求
* Type: REQ/CNF
* SIG ID: SIM_AUTH_REQ/CNF
* Scenarios :
*****************************************************************************************/
#define AUTH_MAX_LEN 16
typedef struct {
    UINT8 cardType; // 0: USIM, 1: ISIM
    UINT8 rand[AUTH_MAX_LEN];
    UINT8 autn[AUTH_MAX_LEN];
} SimAuthReq;

typedef struct {
    UINT8 rc; //0: success/ 1:sync fail/2:mac fail/3:other fail
    UINT8 resLen;
    UINT8 autsLen;
    UINT8 ik[AUTH_MAX_LEN];
    UINT8 ck[AUTH_MAX_LEN];
    UINT8 res[AUTH_MAX_LEN+1]; //auts 或 res
} SimAuthCnf;

/*****************************************************************************************
* 功能 4： 网络紧急呼叫号码上报
* Type: IND
* SIG ID: NW_EMERGENCY_NUM_IND
* Scenarios :
*****************************************************************************************/
#define IMS_EMC_NUM_LIST_MAX_RECORDS 10
#define IMS_EMC_NUM_MAX_LEN 12
    typedef struct {
    UINT8 category;
    UINT8 emcNumLen; // 紧急呼号码长度
    UINT8 emcNum[IMS_EMC_NUM_MAX_LEN];
} ImsEmergencyNum;

typedef struct {
    UINT16 mcc; //如46012: mcc=0x0460, mnc=0x012F; 如460123: mcc=0x0460, mnc=0x0123
    UINT16 mnc; 
} Plmn;
    
typedef struct {
    UINT8 emcNumCount;
#define D_EMER_NUM_SOURCE_TYPE_SIM        0
#define D_EMER_NUM_SOURCE_TYPE_NETWORK    1
    UINT8 sourcetype; // 紧急呼号码来源, 0: SIM 卡; 1: 网络下发
    UINT8 reserved[2];
    ImsEmergencyNum emcNumList[IMS_EMC_NUM_LIST_MAX_RECORDS];
    Plmn plmn;
} EmergencyNumListInd;

/***************************************************************************************
* Type: REQ/CNF
* SIG ID: SET_APN_REQ/SET_APN_CNF
* Scenarios :
****************************************************************************************/
#define APN_LEN_MAX 48
typedef struct {
#define D_IMS_IP_TYPE_IPV4     0
#define D_IMS_IP_TYPE_IPV6     1
#define D_IMS_IP_TYPE_IPV4V6   2
    UINT8 ipType; //0:IPv4/1:IPv6/2:IPv4v6
    UINT8 apnLength;
    UINT8 apnStr[APN_LEN_MAX];
#define D_PCSCF_ADDR_DIS_NOT_SET              0
#define D_PCSCF_ADDR_DIS_THROUGH_NAS_SIGNALL  1
    UINT8 pcscfDisType; /* 0:not influenced by +CGDCONT; 1:through NAS signalling;*/
    UINT8 imCnSigFlag; /* 1: IM CN subsystem-related signalling only */
    UINT8 isEmc;  /* 0:normal; 1: Emc */
}SetApnReq;

typedef struct {
    UINT8 cid; //对应的 cid
    UINT16 rc;// result code, 0:succ,1:fail
}SetApnCnf;

/**************************************************************************************
* Type: REQ/CNF
* SIG ID: ACT_PDN_REQ/CNF
* Scenarios :
**************************************************************************************/
typedef struct
{
    UINT8 cid;
#define D_ACT_PDN_STATE_ACTIVE    1
#define D_ACT_PDN_STATE_DEACTIVE  0
    UINT8 state; //1：激活; 0：去激活
}ActPdnReq;
typedef struct
{
    UINT16 rc; //激活/去激活结果。result code, 0:succ,1:fail
}ActPdnCnf;

/**************************************************************************************
* Type: IND
* SIG ID: PDP_ACT_IND
* Scenarios :
**************************************************************************************/
#define PCSCF_MAX_NUM 4

typedef struct
{
    UINT8 addr[16]; //IPv4:addr[0]-addr[3]; IPv6:addr[0]-addr[15]
} IpAddr;

typedef struct
{
    UINT8 cid[2]; // 0: IPV4 1: IPV6
    UINT8 bid[2]; // 0: IPV4 1: IPV6
    UINT8 apnStr[APN_LEN_MAX];
    UINT8 dCid; // ded bear cid
#define D_HIMS_BEARER_TYPE_DEFAULT    0
#define D_HIMS_BEARER_TYPE_DEDICATE   1
    UINT8 bearType; // 0: default, 1: dedicated
    UINT8 isMeInitial; //网络触发还是 ME 触发，0:否 1:是
    UINT8 pCscfAddrNum;
    IpAddr pcscf[2][PCSCF_MAX_NUM]; // 0: IPV4 1: IPV6
    IpAddr ue[2]; // 0: IPV4 1: IPV6
    UINT16 mtu; //mut size，单位：字节
} PdpActInd;

/**************************************************************************************
* 功能 9： PDN 修改通知
* Type: IND
* SIG ID: SIG ID: XXX_PDP_MODIFY_IND
* Scenarios :
**************************************************************************************/
typedef struct
{
    UINT8 cid[2]; // 0: IPV4 1: IPV6
    UINT8 bid[2]; // 0: IPV4 1: IPV6
    UINT8 apnStr[APN_LEN_MAX];
    UINT8 dCid; // ded bear cid
    UINT8 bearType; // 0: default, 1: dedicated
    UINT8 isMeInitial; //网络触发还是 ME 触发
    UINT8 pCscfAddrNum;
    IpAddr pcscf[2][PCSCF_MAX_NUM]; // 0: IPV4 1: IPV6
    IpAddr ue[2]; // 0: IPV4 1: IPV6
    UINT16 mtu; //mut size，单位：字节
} PdpModifyInd;

/**************************************************************************************
* 功能 10： PDN 去激活上报
* Type: IND
* SIG ID: SIG ID: PDP_DEACT_IND
* Scenarios :
**************************************************************************************/
typedef struct
{
    UINT8 cid;
    UINT8 dCid;
    UINT8 isMeInitial;  //0:否 1:是
    UINT32 cause;       //3GPP原因值，0:表示不带原因值。
} PdpDeactInd;

/**************************************************************************************
* 功能 12： SIM 卡文件读写接口
* Type: REQ/CNF
* SIG ID: SIG ID: SIM_READ_REQ/CNF
* Scenarios :
**************************************************************************************/
typedef struct {
    UINT8 cmd; // 0: read 1:write
    UINT16 efId;    //2bytes, 如:0x6F07
    UINT8 cardType; // 0: ISIM 卡/1:USIM 卡
    UINT16 len;
    UINT16 *data; //used by write
} SimReadReq;

typedef struct {
    UINT16 rc; // 返回结果         0:succ 1:fail
    UINT16 len; // 数据区长度
    UINT16 *data;// 数据区
} SimReadCnf;

/**************************************************************************************
* 功能 13： PS 驻网请求，并携带 IMS 能力参数
* Type: REQ
* SIG ID: SIG ID: START_PS_REQ
* Scenarios :
**************************************************************************************/
typedef struct {
#define D_EPS_ATTACH_TYPE_EPS_ONLY      0
#define D_EPS_ATTACH_TYPE_COMBINED_ATT  1
#define D_EPS_ATTACH_TYPE_EPS_EMERG     2
    UINT8 epsAttachType; // 0: eps only, 1: combined_attach, 2: eps emergency
#define D_VOICE_SETTING_VOICE_CENTRIC     0
#define D_VOICE_SETTING_DATA_CENTRIC      1
    UINT8 voiceSetting; // 0: voice-centric, 1: data-centric
#define D_VOICE_DOMAIN_IMS_PS_VOICE_ONLY  1    
    UINT8 voiceDomainPrefer; // 1: ims-ps-voice-only
    UINT8 resv0;
    UINT32 imsSupport : 1; //是否支持 IMS 普通呼叫, 0: 不支持; 1: 支持
    UINT32 roamingImsSupport : 1; //是否支持 IMS ROAMING, 0:不支持；1：支持
    UINT32 rohc : 1; //是否开启ROHC，0：关闭 1：开启
    UINT32 ttiBunding : 1; //是否支持TTI Bunding, 0:不支持；1：支持
    UINT32 sps : 1; //是否支持sps, 0:不支持；1：支持
    UINT32 resv: 27;
} PsStartReq_Stru;

/**************************************************************************************
* 功能 15： 获取 T3402 定时器时长
* Type: REQ/CNF
* SIG ID: SIG ID: GET_EMM_TIMER_LEN_REQ/CNF
* Scenarios :
**************************************************************************************/
typedef struct
{
    UINT32 timeLen;//seconds
} TIME_LEN_CNF;

typedef struct
{
    UINT8 action; //0:attach;1:detach
} EmcAttachReq;

/**************************************************************************************
* 功能 16： 限制服务/无卡场景紧急注册请求
* Type: REQ/CNF
* SIG ID: SIG ID: EMERGENCY_ATTACH_REQ/CNF
* Scenarios :
**************************************************************************************/
typedef struct
{
    UINT16 rc; //激活/去激活结果 0:succ; 1:fail
} EmcAttachCnf;

/**************************************************************************************
* 功能 17： 通知 MM 停止紧急注册定时器
* Type: IND
* SIG ID: SIG ID: M_REG_TIMER_CTL_IND
* Scenarios :
**************************************************************************************/
typedef struct {
#define D_EC_REG_TIMER_STOP     0
#define D_EC_REG_TIMER_START    1
    UINT8 start; // 1: 启动紧急注册定时器， 0：停止紧急注册定时器
} MmEcRegTimerCtlReq;

/**************************************************************************************
* 功能 18： SAC 变更时需要通知 IMS
* Type: IND
* SIG ID: SIG ID: RRC_HIMS_SSAC_CHANGE_NTF
* Scenarios :
**************************************************************************************/
typedef struct {
    UINT8 barFactorForVoice; /* 取值范围:0~15 枚举值: p00, p05, p10, p15, p20, p25, p30, p40, p50, p60, p70, p75, p80, p85, p90, p95 */
    UINT8 barTimeForVoice;   /* 取值范围:0~7 枚举值,单位:s, 枚举值: s4, s8, s16, s32, s64, s128, s256, s512 */
    UINT8 barFactorForVideo; /* 取值范围:0~15 枚举值: p00, p05, p10, p15, p20, p25, p30, p40, p50, p60, p70, p75, p80, p85, p90, p95 */
    UINT8 barTimeForVideo;   /* 取值范围:0~7 枚举值,单位:s, 枚举值: s4, s8, s16, s32, s64, s128, s256, s512 */
} RrcHimsSsacInd;

/**************************************************************************************
* 功能 19： 通知 MM 是否忽略 3411 定时器
* Type: IND
* SIG ID: SIG ID: MM_IGNOR_3411_TIMER_IND
* Scenarios :
**************************************************************************************/
typedef struct {
    UINT8 ignorTimer; // 1: 忽略， 0：不忽略
} HimsMmIgnor3411TimerInd;

/**************************************************************************************
* 功能 20： AP 配置小区重选抑制的门限值
* Type: REQ
* SIG ID: SIG ID: CELL_THRESH_ADJ
* Scenarios :
**************************************************************************************/
typedef struct {
    INT16 rsrpThreshAdjust; //有符号数，单位 1/4dB
    INT16 rsrqThreshAdjust; //有符号数，单位 1/4dB
} CellThreshAdjReq;

/**************************************************************************************
* 功能 21： AP 配置邻区测量上报 RSRP/RSRQ
* Type: REQ
* SIG ID: SIG ID: XXX_MEAS_RPT_ADJ
* Scenarios :
**************************************************************************************/
typedef struct {
    INT16 rsrpRptAdjust; //有符号数，单位 1/4dB
    INT16 rsrqRptAdjust; //有符号数，单位 1/4dB
} MeasRptAdjReq;

/**************************************************************************************
* 功能 22： AP 配置邻区测量启动门限调整量
* Type: REQ
* SIG ID: SIG ID: XXX_MEAS_TRIGGER_THRESH_ADJ
* Scenarios :
**************************************************************************************/
typedef struct {
    INT16 rsrpMeasAdjust; //有符号数，单位 1/4dB
    INT16 rsrqMeasAdjust; //有符号数，单位 1/4dB
} MeasTriggerThreshAdjReq;

/**************************************************************************************
* 功能 23： RRC 建链请求
* Type: REQ/CNF
* SIG ID: SIG ID: RRC_EST_REQ/CNF
* Scenarios :
**************************************************************************************/
    
/**************************************************************************************
* 功能 24： RRC 状态通知（conn/deactive 等）
* Type: IND
* SIG ID: SIG ID: RRC_STATE_IND
* Scenarios :
**************************************************************************************/
typedef struct{
    UINT8 state;  //0:空闲态; 1:连接态
#define D_RRC_STATE_CONN_REASON_HIMS_EST_REQ       0  //IMS触发进入连接态
#define D_RRC_STATE_CONN_REASON_SYS_EST_REQ        1  //非IMS MO 业务触发进入连接态
#define D_RRC_STATE_CONN_REASON_MT_EST_REQ         2  //MT业务触发进入连接态
#define D_RRC_STATE_DEACT_NO_REASON                4  //进入空闲态(不区分原因)
    UINT8 reason; //变更原因，如0:HIMS_EST_REQ/1:SYS_MO_EST_REQ/2:NW_MT_EST_REQ
} RrcStateInd;

/**************************************************************************************
* 功能 25： CDRX 配置信息通知
* Type: IND
* SIG ID: SIG ID: CDRX_CFG_IND
* Scenarios :
**************************************************************************************/
typedef struct {
    UINT8 cdrxEnableFlg; //1:开启 0:关闭
    UINT16 shortCdrxEnableFlg; //1:开启 0:关闭
    UINT16 longCdrxCycle; //ms
    UINT16 shortCdrxCycle; //ms
} CDrxCfgInd;

/**************************************************************************************
* 功能 26： PDCP/RLC 拥塞状态上报门限配置
* Type: REQ/CNF
* SIG ID: SIG ID: L2_CONGEST_RT_SET_REQ/CNF
* Scenarios :
**************************************************************************************/
typedef struct{
    UINT8 enable; //是否启动上报， 1：启动， 0：关闭
    UINT8 bearId;//以下下门限对应的 eps bear id
    UINT8 normal; //pdcp/rlc 缓存安全门限， 取值 0~100，除以 100 即百分比
    UINT8 warning; //警告门限， 取值 0~100，除以 100 即百分比
    UINT8 error; //错误门限， 取值 0~100，除以 100 即百分比
    UINT8 discard1; //丢包率 1 上报门限， 取值 0~100，除以 100 即百分比
    UINT8 discard2; //丢包率 2 上报门限， 取值 0~100，除以 100 即百分比
} L2CongestReportSetReq;

typedef struct{
    UINT16 rc;  //0:succ; 1:fail
} L2CongestReportSetCnf;

/**************************************************************************************
* 功能 27： PDCP/RLC 拥塞状态上报
* Type: IND
* SIG ID: SIG ID: L2_CONGEST_RT_IND
* Scenarios :
**************************************************************************************/
typedef struct{
#define D_L2_CONGEST_IND_TYPE_NORMAL    0
#define D_L2_CONGEST_IND_TYPE_WARNING   1
#define D_L2_CONGEST_IND_TYPE_ERROR     2
#define D_L2_CONGEST_IND_TYPE_DISCARD1  3
#define D_L2_CONGEST_IND_TYPE_DISCARD2  4
    UINT8 type; //0:normal/1:warning/2:error/3:discard1/4:discard2
    UINT8 increaseFlg; //1:pdcp/rlc 缓存增加， 0:减少
} L2CongestReportInd;

/**************************************************************************************
* 功能 28： ROHC实际生效状态上报
* Type: IND
* SIG ID: SIG ID: ROHC_STATE_IND
* Scenarios :
**************************************************************************************/
typedef struct{
    UINT8 bearId; //eps bear id
    UINT8 enbalbe; //是否启用ROHC，1:开启 0:未开启
} RohcStateInd;

/**************************************************************************************
* 功能 29： 空口误码上报门限配置请求
* Type: REQ/CNF
* SIG ID: SIG ID: BER_SET_REQ/CNF
* Scenarios :
**************************************************************************************/
typedef struct{
    UINT8 level1;// 误码率上报门限1: 0-100，除以 100 即百分比
    UINT8 level2;// 误码率上报门限2: 0-100，除以 100 即百分比
} BerSetReq;

typedef struct{
    UINT16 rc; //0:succ; 1:fail
} BerSetCnf;

/**************************************************************************************
* 功能 30： 空口误码状态上报
* Type: IND
* SIG ID: SIG ID: BER_INFO_IND
* Scenarios :
**************************************************************************************/
typedef struct{
    UINT8 ulBer;// 上行误码率， 0-100，除以 100 即百分比
    UINT8 increaseFlgUl;//1: 上升， 0：下降
    UINT8 dlBer;// 下行误码率， 0-100，除以 100 即百分比
    UINT8 increaseFlgDl;//1: 上升， 0：下降
} BerInfoInd;

typedef UINT8 LM_MSG_ID_ENUM;
typedef enum
{
    IMS_CAM_START_IND,
    IMS_CAM_SERVICE_CHANGE_IND,
    SIM_AUTH_REQ,
    SIM_AUTH_CNF,
    NW_EMERGENCY_NUM_IND,
    SET_APN_REQ,
    SET_APN_CNF,
    ACT_PDN_REQ,
    ACT_PDN_CNF,
    PDP_ACT_IND,
    XXX_PDP_MODIFY_IND,
    PDP_DEACT_IND,
    SIM_READ_REQ,
    SIM_READ_CNF,
    START_PS_REQ,
    GET_EMM_TIMER_LEN_REQ,
    GET_EMM_TIMER_LEN_CNF,
    EMERGENCY_ATTACH_REQ,
    EMERGENCY_ATTACH_CNF,
    MM_REG_TIMER_CTL_IND,
    RRC_HIMS_SSAC_CHANGE_NTF,
    MM_IGNOR_3411_TIMER_IND,
    CELL_THRESH_ADJ,
    XXX_MEAS_RPT_ADJ,
    XXX_MEAS_TRIGGER_THRESH_ADJ,
    RRC_STATE_IND,
    CDRX_CFG_IND,
    L2_CONGEST_RT_SET_REQ,
    L2_CONGEST_RT_SET_CNF,
    L2_CONGEST_RT_IND,
    ROHC_STATE_IND,
    BER_SET_REQ,
    BER_SET_CNF,
    BER_INFO_IND
} LM_MSG_ID_VALUE;

typedef struct {
    UINT16 rc;
    UINT16 len; 
    UINT8  data[4];
} SimReadCnfData; //xy use only

typedef struct
{
    unsigned short                        usEvent;
    LM_MSG_ID_ENUM                        eMsgId;
    unsigned char                         aucPadding;
    unsigned int                          usDataLen;
    unsigned char                         aucData[4];
} LM_INTERFACE_DATA_STRU;

#endif

