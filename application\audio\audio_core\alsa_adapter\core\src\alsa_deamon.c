#include "alsa_config.h"
#include "alsa_deamon.h"
#include "alsa.h"
#include "alsa_extra.h"
#include "alsa_os.h"
#include "audioflinger.h"
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "hal_aud.h"
#include "hal_trace.h"
#include "heap_api.h"

#define ALSA_SILENCE_TRIGGER_STOP_COUNT (2)
#if (ALSA_SILENCE_TRIGGER_STOP_COUNT < 2)
#error "ALSA_SILENCE_TRIGGER_STOP_COUNT must not less than 2"
#endif

#define ALSA_DEAMON_MSG_COUNT_MAX (16)
#define ALSA_DEAMON_DEFAULT_VOL ALSA_PLAYBACK_DEFAULT_VOL
#define ALSA_DEAMON_DEFAULT_GAIN_DB (-100)
#define ALSA_DEAMON_DEFAULT_PERCENT_VOL (-1)
#define ALSA_DEAMON_DEFAULT_FLOAT_VOL (-1.0f)

#define ALSA_USER_COUNT ALSA_INSTANCE_COUNT
#define ALSA_PLAYBACK_USER_COUNT ALSA_PLAYBACK_INSTANCE_COUNT
#define ALSA_CAPTURE_USER_COUNT ALSA_CAPTURE_INSTANCE_COUNT
#if ALSA_PLAYBACK_USER_COUNT >= ALSA_CAPTURE_USER_COUNT
#define STOP_SEM_MAX_COUNT ALSA_PLAYBACK_USER_COUNT
#else
#define STOP_SEM_MAX_COUNT ALSA_CAPTURE_USER_COUNT
#endif

typedef enum alsa_deamon_msg_type
{
    ALSA_DEAMON_MSG_PLAYBACK_OPEN = 0,
    ALSA_DEAMON_MSG_PLAYBACK_START,
    ALSA_DEAMON_MSG_PLAYBACK_STOP,
    ALSA_DEAMON_MSG_PLAYBACK_CLOSE,
#ifdef ALSA_SUPPORT_CAPTURE
    ALSA_DEAMON_MSG_CAPTURE_OPEN,
    ALSA_DEAMON_MSG_CAPTURE_START,
    ALSA_DEAMON_MSG_CAPTURE_STOP,
    ALSA_DEAMON_MSG_CAPTURE_CLOSE,
#endif
    ALSA_DEAMON_MSG_PCM_STATE,
    ALSA_DEAMON_MSG_MAX,
} alsa_deamon_msg_type_t;

typedef struct alsa_deamon_msg
{
    alsa_deamon_msg_type_t type;
    void *arg;
} alsa_deamon_msg_t;

typedef struct alsa_pcm_state_msg
{
    alsa_stream_t stream;
    alsa_pcm_state_t state;
    uint8_t id;
} alsa_pcm_state_msg_t;

typedef struct alsa_pcm_arg
{
    enum AUD_SAMPRATE_T sample_rate;
    enum AUD_BITS_T bits;
    enum AUD_CHANNEL_NUM_T ch_num;
} alsa_pcm_arg_t;

typedef enum alsa_mute_type
{
    ALSA_REAL_UN_MUTE = 0,
    ALSA_NEED_MUTE,
    ALSA_REAL_MUTE,
    ALSA_NEED_UN_MUTE,
} alsa_mute_t;

#ifdef ALSA_SUPPORT_PA_CTRL
typedef enum pa_ctrl
{
    ALSA_PA_OFF = 0,
    ALSA_PA_ON,
} alsa_pa_ctrl_t;
#endif

typedef struct alsa_deamon_ctrl
{
    alsa_os_mutex_t interface_lock;
    alsa_os_mutex_t state_lock;
    alsa_state_t state;
    alsa_os_semaphore_t open_sem;
    alsa_os_semaphore_t start_sem;
    alsa_os_semaphore_t stop_sem;
    alsa_os_semaphore_t close_sem;
} alsa_deamon_ctrl_t;

typedef struct alsa_deamon_vol
{
    alsa_os_mutex_t vol_lock;
    uint8_t vol;
    int8_t gain_db;

    alsa_mute_t mute;
    int8_t percent_vol;
    float float_vol;
} alsa_deamon_vol_t;

typedef struct alsa_deamon_playback
{
    alsa_deamon_ctrl_t ctrl;
    alsa_deamon_vol_t vol;
    alsa_playback_frome_user_callback_t playback_from_user_cb;

#ifdef ALSA_SUPPORT_PA_CTRL
    alsa_pa_ctrl_t pa_ctrl;
#endif
    alsa_os_mutex_t playback_cb_lock;
} alsa_deamon_playback_t;

#ifdef ALSA_SUPPORT_CAPTURE
typedef struct alsa_deamon_capture
{
    alsa_deamon_ctrl_t ctrl;
    alsa_deamon_vol_t vol;
    alsa_capture_to_user_callback_t capture_to_user_cb;
} alsa_deamon_capture_t;
#endif

typedef struct alsa_deamon
{
    /* common */
    uint8_t inited;
    alsa_os_thread_t thread;
    alsa_os_mq_t mq;
    alsa_os_mutex_t user_lock;
    alsa_state_t user_state[ALSA_USER_COUNT];
    alsa_stream_t user_stream[ALSA_USER_COUNT];
    alsa_pcm_state_changed_callback_t pcm_state_changed_cb;
    alsa_deamon_playback_t playback;
#ifdef ALSA_SUPPORT_CAPTURE
    alsa_deamon_capture_t capture;
#endif
} alsa_deamon_t;

/***** Variable declarations *****/

static alsa_deamon_t g_alsa_deamon;

static const char *alsa_deamon_msg_str[ALSA_DEAMON_MSG_MAX] = {
    "PLAYBACK_OPEN",
    "PLAYBACK_START",
    "PLAYBACK_STOP",
    "PLAYBACK_CLOSE",
#ifdef ALSA_SUPPORT_CAPTURE
    "CAPTURE_OPEN",
    "CAPTURE_START",
    "CAPTURE_STOP",
    "CAPTURE_CLOSE",
#endif
    "PCM_STATE",
};


static uint8_t * alsa_playback_dma_buffer = NULL;

#ifdef ALSA_SUPPORT_CAPTURE
static uint8_t * alsa_capture_dma_buffer = NULL;
#endif

#ifdef ALSA_QUEUE_STEREO_PLAYBACK_MONO_EN
static uint8_t alsa_deamon_stereo_tmp_buffer[ALSA_PLAYBACK_DMA_BUFFER_SIZE] __attribute__((aligned(4)));
#endif

static uint32_t alsa_playback_dma_buffer_size;

static alsa_pcm_arg_t playback_pcm_arg = {
    .sample_rate = ALSA_SUPPORTED_SAMPLE_RATE_48000,
    .bits = ALSA_SUPPORTED_BITS_16,
    .ch_num = ALSA_SUPPORTED_CHANNEL_NUM_1,
};

#ifdef ALSA_PLAYBACK_START_DELAY_EN
static uint8_t alsa_start_delayed = 0;
#endif

#ifdef ALSA_SUPPORT_CAPTURE
static alsa_pcm_arg_t capture_pcm_arg = {
    .sample_rate = ALSA_SUPPORTED_SAMPLE_RATE_16000,
    .bits = ALSA_SUPPORTED_BITS_16,
    .ch_num = ALSA_SUPPORTED_CHANNEL_NUM_1,
};
#endif

#ifdef ALSA_MIXER_PRIORITY_EN
static uint8_t alsa_playback_mixer_priority = 0;
#endif

/***** Internal functions declarations *****/

static int alsa_deamon_ctrl_init(alsa_deamon_ctrl_t *ctrl);
static int alsa_deamon_vol_init(alsa_deamon_vol_t *vol);
static void alsa_deamon_task(void *arg);
static int alsa_deamon_send_msg(alsa_deamon_msg_type_t type, void *arg);
static int set_user_state(uint8_t user_id, alsa_state_t state);
static alsa_state_t get_user_state(uint8_t user_id);
static int set_user_stream(uint8_t user_id, alsa_stream_t stream);
static alsa_stream_t get_user_stream(uint8_t user_id);
static int alsa_open_internal(enum AUD_STREAM_T stream_type,
                              enum AUD_SAMPRATE_T sample_rate,
                              enum AUD_BITS_T bits,
                              enum AUD_CHANNEL_NUM_T ch_num);
static int alsa_close_internal(enum AUD_STREAM_T stream_type);
static int alsa_start_internal(enum AUD_STREAM_T stream_type);
static int alsa_stop_internal(enum AUD_STREAM_T stream_type);
static int pcm_state_msg_handler(alsa_pcm_state_msg_t *pcm_state_msg);

static int alsa_deamon_playback_init(alsa_deamon_playback_t *playback,
                                     alsa_playback_frome_user_callback_t cb);
static int set_playback_internal_state(alsa_state_t state);
static alsa_state_t get_playback_internal_state(void);
static void playback_pcm_arg_set(alsa_pcm_arg_t *pcm_arg);
static int playback_open_msg_handler(uint32_t sample_rate, uint8_t bits, uint8_t ch_num);
static int playback_start_msg_handler(void);
static int playback_stop_msg_handler(void);
static int playback_close_msg_handler(alsa_state_t current_state);
static uint32_t alsa_deamon_pcm_process(uint8_t *buf, uint32_t len);
#ifdef AUDIO_OUTPUT_INVERT_ALL_CHANNEL
static uint32_t alsa_invert_process(uint8_t *buf, uint32_t len);
#endif
static uint32_t alsa_fill_data_on_start(void);
static uint32_t alsa_playback_callback(uint8_t *buf, uint32_t len);
static int playback_vol_update_internal(uint8_t vol);
#ifdef ALSA_SUPPORT_PA_CTRL
static void alsa_pa_ctrl(alsa_pa_ctrl_t ctrl);
#endif
static uint32_t playback_float_volume_mapping(uint32_t vol);

#ifdef ALSA_SUPPORT_CAPTURE
static int alsa_deamon_capture_init(alsa_deamon_capture_t *capture,
                                    alsa_capture_to_user_callback_t cb);
static int set_capture_internal_state(alsa_state_t state);
alsa_state_t get_capture_internal_state(void);
static void capture_pcm_arg_set(alsa_pcm_arg_t *pcm_arg);
static int capture_open_msg_handler(uint32_t sample_rate,
                                    uint8_t bits,
                                    uint8_t ch_num);
static int capture_start_msg_handler(void);
static int capture_stop_msg_handler(void);
static int capture_close_msg_handler(alsa_state_t current_state);
static uint32_t alsa_capture_callback(uint8_t *buf, uint32_t len);
#endif

#ifdef ALSA_STATE_HOOK_EN
int alsa_state_open_hook_ivk(int sample_rate, int channels, int bits);
int alsa_state_start_hook_ivk(void);
int alsa_state_process_hook_ivk(const uint8_t *input, uint32_t len);
int alsa_state_stop_hook_ivk(void);
int alsa_state_close_hook_ivk(void);
#endif

void analog_aud_codec_speaker_enable(bool en)
{
}

void analog_aud_codec_dac_enable(bool en)
{

}

/***** Extern Interface *****/


void alsa_deamon_init(alsa_playback_frome_user_callback_t playback_from_user_cb,
                      alsa_capture_to_user_callback_t capture_to_user_cb,
                      alsa_pcm_state_changed_callback_t pcm_state_changed_cb)
{

    ASSERT(pcm_state_changed_cb, "%s %d error", __func__, __LINE__);
    ASSERT(playback_from_user_cb, "%s %d error", __func__, __LINE__);
#ifdef ALSA_CAPTURE_SUPPORT
    ASSERT(capture_to_user_cb, "%s %d error", __func__, __LINE__);
#endif

    memset(&g_alsa_deamon, 0, sizeof(alsa_deamon_t));

    g_alsa_deamon.pcm_state_changed_cb = pcm_state_changed_cb;

    g_alsa_deamon.user_lock = alsa_os_mutex_create();
    ASSERT(g_alsa_deamon.user_lock, "%s %d error", __func__, __LINE__);

    g_alsa_deamon.mq = alsa_os_mq_create(ALSA_DEAMON_MSG_COUNT_MAX, sizeof(void *));
    ASSERT(g_alsa_deamon.mq, "%s %d error", __func__, __LINE__);

    ASSERT(!alsa_deamon_playback_init(&g_alsa_deamon.playback, playback_from_user_cb), "%s %d error", __func__, __LINE__);

#ifdef ALSA_SUPPORT_CAPTURE
    ASSERT(!alsa_deamon_capture_init(&g_alsa_deamon.capture, capture_to_user_cb), "%s %d error", __func__, __LINE__);
#endif

    g_alsa_deamon.thread = alsa_os_thread_create(alsa_deamon_task, "alsa_deamon", (2 * 1024), osPriorityAboveNormal, NULL);
    ASSERT(g_alsa_deamon.thread, "%s %d error", __func__, __LINE__);

    g_alsa_deamon.inited = 1;
}

int alsa_deamon_playback_open(uint8_t user_id,
                              enum AUD_SAMPRATE_T sample_rate,
                              enum AUD_BITS_T bits,
                              enum AUD_CHANNEL_NUM_T ch_num)
{
    int ret = 0;
    alsa_os_status_t os_status;
    alsa_os_mutex_wait(g_alsa_deamon.playback.ctrl.interface_lock, osWaitForever);
    alsa_state_t playback_state = get_playback_internal_state();
    if (playback_state == ALSA_STATE_CLOSE)
    {
        alsa_pcm_arg_t *pcm_arg = (alsa_pcm_arg_t *)audio_calloc(1, sizeof(alsa_pcm_arg_t));
        if (pcm_arg)
        {
            pcm_arg->sample_rate = sample_rate;
            pcm_arg->bits = bits;
            pcm_arg->ch_num = ch_num;
            if (0 == alsa_deamon_send_msg(ALSA_DEAMON_MSG_PLAYBACK_OPEN, pcm_arg))
            {
                do
                {
                    os_status = alsa_os_semaphore_acquire(g_alsa_deamon.playback.ctrl.open_sem, 100);
                    if (os_status != osOK)
                    {
                        TRACE(0, "%s sem error %d", __func__, os_status);
                    }
                } while (os_status != osOK);
                playback_state = get_playback_internal_state();
                if (playback_state != ALSA_STATE_OPEN)
                {
                    ret = -1;
                }
            }
            else
            {
                TRACE(0, "%s OPEN msg send error", __func__);
                audio_free(pcm_arg);
                ret = -1;
            }
        }
        else
        {
            TRACE(0, "alsa alloc pcm arg error");
            ret = -1;
        }
    }
    else
    {
        TRACE(0, "playback_state %d, not need open", playback_state);
    }
    if (ret == 0)
    {
        set_user_state(user_id, ALSA_STATE_OPEN);
        set_user_stream(user_id, ALSA_STREAM_PLAYBACK);
    }
    alsa_os_mutex_release(g_alsa_deamon.playback.ctrl.interface_lock);
    return ret;
}

int alsa_deamon_playback_start(uint8_t user_id)
{
    int ret = 0;
    alsa_os_status_t os_status;
    alsa_os_mutex_wait(g_alsa_deamon.playback.ctrl.interface_lock, osWaitForever);
    alsa_state_t playback_state = get_playback_internal_state();
    if (playback_state == ALSA_STATE_CLOSE)
    {
        TRACE(0, "alsa_deamon not support CLOSE -> START");
        alsa_os_mutex_release(g_alsa_deamon.playback.ctrl.interface_lock);
        return -1;
    }
    if (playback_state == ALSA_STATE_OPEN || playback_state == ALSA_STATE_STOP)
    {
        if (0 == alsa_deamon_send_msg(ALSA_DEAMON_MSG_PLAYBACK_START, NULL))
        {
            do
            {
                os_status = alsa_os_semaphore_acquire(g_alsa_deamon.playback.ctrl.start_sem, 100);
                if (os_status != osOK)
                {
                    TRACE(0, "%s sem error %d", __func__, os_status);
                }
            } while (os_status != osOK);
            playback_state = get_playback_internal_state();
            if (playback_state != ALSA_STATE_START)
            {
                ret = -1;
            }
        }
        else
        {
            TRACE(0, "%s ALSA_DEAMON_MSG_PLAYBACK_START send error", __func__);
            ret = -1;
        }
    }
    else
    {
        TRACE(0, "playback_state %d, not need start", playback_state);
    }
    if (ret == 0)
    {
        set_user_state(user_id, ALSA_STATE_START);
    }
    alsa_os_mutex_release(g_alsa_deamon.playback.ctrl.interface_lock);
    return ret;
}

int alsa_deamon_playback_stop(uint8_t user_id, uint8_t immediate)
{
    int ret = 0;
    alsa_os_status_t os_status;
    alsa_os_mutex_wait(g_alsa_deamon.playback.ctrl.interface_lock, osWaitForever);
    uint8_t need_stop_codec = 1;
    for (int i = 0; i < ALSA_USER_COUNT; i++)
    {
        alsa_state_t user_state = get_user_state(i);
        alsa_stream_t user_stream = get_user_stream(i);
        if (user_stream == ALSA_STREAM_PLAYBACK &&
            user_state == ALSA_STATE_START &&
            i != user_id)
        {
            TRACE(0, "%s not need stop codec. user_state = %d", __func__, user_state);
            need_stop_codec = 0;
            break;
        }
    }
    if (need_stop_codec)
    {
        alsa_state_t playback_state = get_playback_internal_state();
        if (playback_state != ALSA_STATE_START)
        {
            TRACE(0, "%s not need stop codec. playback_state = %d", __func__, playback_state);
            need_stop_codec = 0;
        }
    }
    if (need_stop_codec)
    {
        TRACE(0, "alsa_deamon wait stop");
        if (immediate)
        {
            TRACE(0, "alsa_deamon_playback_stop immdiate");
            if (0 != alsa_deamon_send_msg(ALSA_DEAMON_MSG_PLAYBACK_STOP, NULL))
            {
                ASSERT(0, "%s ALSA_DEAMON_MSG_PLAYBACK_STOP send error", __func__);
                ret = -1;
            }
        }
        else
        {
            set_user_state(user_id, ALSA_STATE_STOP);
        }
        do
        {
            os_status = alsa_os_semaphore_acquire(g_alsa_deamon.playback.ctrl.stop_sem, 100);
            if (os_status != osOK)
            {
                TRACE(0, "%s sem error %d", __func__, os_status);
            }
        } while (os_status != osOK);
        if (immediate)
        {
            set_user_state(user_id, ALSA_STATE_STOP);
        }
    }
    else
    {
        set_user_state(user_id, ALSA_STATE_STOP);
    }
    alsa_os_mutex_release(g_alsa_deamon.playback.ctrl.interface_lock);
    return ret;
}

int alsa_deamon_playback_close(uint8_t user_id)
{
    TRACE(0, "%s ENTER", __func__);
    int ret = 0;
    alsa_os_status_t os_status;
    alsa_os_mutex_wait(g_alsa_deamon.playback.ctrl.interface_lock, osWaitForever);
    uint8_t close_codec = 1;
    set_user_state(user_id, ALSA_STATE_CLOSE);
    for (int i = 0; i < ALSA_USER_COUNT; i++)
    {
        alsa_state_t user_state = get_user_state(i);
        alsa_stream_t user_stream = get_user_stream(i);
        if (user_stream == ALSA_STREAM_PLAYBACK)
        {
            if (!(user_state == ALSA_STATE_CLOSE ||
                  user_state == ALSA_STATE_STOP))
            {
                TRACE(0, "user %d state %d, so alsa_deamon cannot close", i, user_state);
                close_codec = 0;
                break;
            }
        }
    }
    if (close_codec)
    {
        if (0 == alsa_deamon_send_msg(ALSA_DEAMON_MSG_PLAYBACK_CLOSE, NULL))
        {
            do
            {
                os_status = alsa_os_semaphore_acquire(g_alsa_deamon.playback.ctrl.close_sem, 100);
                if (os_status != osOK)
                {
                    TRACE(0, "%s sem error %d", __func__, os_status);
                }
            } while (os_status != osOK);
        }
        else
        {
            ASSERT(0, "%s ALSA_DEAMON_MSG_PLAYBACK_CLOSE send error", __func__);
            ret = -1;
        }
    }
    if (ret == 0)
    {
        set_user_state(user_id, ALSA_STATE_CLOSE);
        set_user_stream(user_id, ALSA_STREAM_UNKONWN);
    }
    alsa_os_mutex_release(g_alsa_deamon.playback.ctrl.interface_lock);
    TRACE(0, "%s LEAVE", __func__);
    return 0;
}

alsa_state_t alsa_deamon_get_playback_state(void)
{
    return get_playback_internal_state();
}

alsa_state_t alsa_deamon_get_playback_user_state(uint8_t user_id)
{
    alsa_stream_t stream = get_user_stream(user_id);
    if (stream == ALSA_STREAM_PLAYBACK)
    {
        return get_user_state(user_id);
    }
    return ALSA_STATE_ERROR;
}

uint32_t alsa_deamon_get_playback_dma_buffer_size(void)
{
    return alsa_playback_dma_buffer_size;
}

int alsa_deamon_playback_cb_lock(void)
{
    if (g_alsa_deamon.playback.playback_cb_lock)
        return alsa_os_mutex_wait(g_alsa_deamon.playback.playback_cb_lock, osWaitForever);

    return -1;
}

int alsa_deamon_playback_cb_unlock(void)
{
    if (g_alsa_deamon.playback.playback_cb_lock)
        return alsa_os_mutex_release(g_alsa_deamon.playback.playback_cb_lock);

    return -1;
}

void alsa_mute_set(void)
{
#ifdef ALSA_SW_GAIN_PROCESS_EN
#else
    alsa_os_mutex_wait(g_alsa_deamon.playback.vol.vol_lock, osWaitForever);
    if (ALSA_REAL_MUTE != g_alsa_deamon.playback.vol.mute)
    {
        g_alsa_deamon.playback.vol.mute = ALSA_NEED_MUTE;
        if (0 == af_stream_mute(AUD_STREAM_PLAYBACK, 1))
        {
            g_alsa_deamon.playback.vol.mute = ALSA_REAL_MUTE;
        }
    }
    TRACE(0, "alsa_mute_set %d", g_alsa_deamon.playback.vol.mute);
    alsa_os_mutex_release(g_alsa_deamon.playback.vol.vol_lock);
#endif
}

void alsa_mute_cancel(void)
{
#ifdef ALSA_SW_GAIN_PROCESS_EN
#else
    alsa_os_mutex_wait(g_alsa_deamon.playback.vol.vol_lock, osWaitForever);
    if (ALSA_REAL_UN_MUTE != g_alsa_deamon.playback.vol.mute)
    {
        g_alsa_deamon.playback.vol.mute = ALSA_NEED_UN_MUTE;
        if (0 == af_stream_mute(AUD_STREAM_PLAYBACK, 0))
        {
            g_alsa_deamon.playback.vol.mute = ALSA_REAL_UN_MUTE;
        }
    }
    TRACE(0, "alsa_mute_cancel %d", g_alsa_deamon.playback.vol.mute);
    alsa_os_mutex_release(g_alsa_deamon.playback.vol.vol_lock);
#endif
}

int alsa_volume_update(void)
{
    uint8_t vol = alsa_volume_get();

    return playback_vol_update_internal(vol);
}


void alsa_volume_set(uint8_t vol_dac)
{
    alsa_os_mutex_wait(g_alsa_deamon.playback.vol.vol_lock, osWaitForever);
    g_alsa_deamon.playback.vol.vol = vol_dac;
    alsa_os_mutex_release(g_alsa_deamon.playback.vol.vol_lock);
    alsa_volume_update();
}

uint8_t alsa_volume_get(void)
{
    alsa_os_mutex_wait(g_alsa_deamon.playback.vol.vol_lock, osWaitForever);
    uint8_t vol = g_alsa_deamon.playback.vol.vol;
    alsa_os_mutex_release(g_alsa_deamon.playback.vol.vol_lock);
    return vol;
}

int alsa_volume_float_set(float volume)
{
    if (volume > 1.0f)
    {
        volume = 1.0f;
    }

    if (volume < 0.0f)
    {
        volume = 0.0f;
    }

    double volume_double = (double)(volume * 100.0f);
    // TRACE(0, "volume_double %d --- %f", (uint32_t)volume_double, volume_double);
    double floor_volume = floor(volume_double);
    // TRACE(0, "floor %d --- %f", (uint32_t)floor_volume, floor_volume);
    double ceil_volume = ceil(volume_double);
    // TRACE(0, "ceil %d --- %f", (uint32_t)ceil_volume, ceil_volume);

    uint32_t u32vol = (uint32_t)floor_volume;
    volume = (float)floor_volume;

    if ((volume_double - floor_volume) > (double)0.5)
    {
        u32vol += 1;
        volume = (float)ceil_volume;
    }

    alsa_os_mutex_wait(g_alsa_deamon.playback.vol.vol_lock, osWaitForever);
    g_alsa_deamon.playback.vol.float_vol = volume;
#ifdef ALSA_AUDIO_PROCESS_RENDER_EN
    alsa_audio_process_render_set_vol(volume);
#endif
    alsa_os_mutex_release(g_alsa_deamon.playback.vol.vol_lock);
    //uint32_t dac_vol = playback_float_volume_mapping(u32vol);
    TRACE(0, "%s u32vol = %d, dac_vol = %d", __func__, u32vol, dac_vol);
    alsa_volume_set(u32vol);
    return 0;
}

int alsa_volume_float_get(float *volume)
{
    if (!volume)
    {
        TRACE(0, "%s volume NULL error", __func__);
        return -1;
    }

    float vol_f = 0.0f;
    alsa_os_mutex_wait(g_alsa_deamon.playback.vol.vol_lock, osWaitForever);
    vol_f = g_alsa_deamon.playback.vol.float_vol;
    alsa_os_mutex_release(g_alsa_deamon.playback.vol.vol_lock);
    *volume = vol_f;
    return 0;
}

int alsa_volume_factory_set(uint8_t vol_dac_factory)
{
#if defined(TGT_VOLUME_LEVEL_USER_MAX) && defined(TGT_VOLUME_LEVEL_FACTORY_MAX) && defined(TGT_VOLUME_LEVEL_DEBUG_MAX)
    uint8_t factory_volume_step = tgt_factory_volume_step_get();
    uint8_t factory_volume_base = tgt_factory_volume_base_get();
    if (vol_dac_factory == 0 || vol_dac_factory > factory_volume_step)
    {
        TRACE(0, "%s vol %d just support [1 - %d]", __func__, vol_dac_factory, factory_volume_step);
        return -1;
    }
    uint8_t vol_dac = vol_dac_factory + factory_volume_base;
    alsa_volume_set(vol_dac);
    return 0;
#else
    TRACE(0, "%s current not support", __func__);
    return -1;
#endif
}

int alsa_volume_debug_set(uint8_t vol_dac_debug)
{
#if defined(TGT_VOLUME_LEVEL_USER_MAX) && defined(TGT_VOLUME_LEVEL_FACTORY_MAX) && defined(TGT_VOLUME_LEVEL_DEBUG_MAX)
    uint8_t debug_volume_step = tgt_debug_volume_step_get();
    uint8_t debug_volume_base = tgt_debug_volume_base_get();
    if (vol_dac_debug == 0 || vol_dac_debug > debug_volume_step)
    {
        TRACE(0, "%s vol %d just support [1 - %d]", __func__, vol_dac_debug, debug_volume_step);
        return -1;
    }
    uint8_t vol_dac = vol_dac_debug + debug_volume_base;
    alsa_volume_set(vol_dac);
    return 0;
#else
    TRACE(0, "%s current not support", __func__);
    return -1;
#endif
}

#ifdef ALSA_SUPPORT_CAPTURE
int alsa_deamon_capture_open(uint8_t user_id,
                             enum AUD_SAMPRATE_T sample_rate,
                             enum AUD_BITS_T bits,
                             enum AUD_CHANNEL_NUM_T ch_num)
{
    int ret = 0;
    alsa_os_status_t os_status;
    alsa_os_mutex_wait(g_alsa_deamon.capture.ctrl.interface_lock, osWaitForever);
    alsa_state_t capture_state = get_capture_internal_state();
    if (capture_state == ALSA_STATE_CLOSE)
    {
        alsa_pcm_arg_t *pcm_arg = (alsa_pcm_arg_t *)audio_calloc(1, sizeof(alsa_pcm_arg_t));
        if (pcm_arg)
        {
            pcm_arg->sample_rate = sample_rate;
            pcm_arg->bits = bits;
            pcm_arg->ch_num = ch_num;
            if (0 == alsa_deamon_send_msg(ALSA_DEAMON_MSG_CAPTURE_OPEN, pcm_arg))
            {
                do
                {
                    os_status = alsa_os_semaphore_acquire(g_alsa_deamon.capture.ctrl.open_sem, 100);
                } while (os_status != osOK);
                capture_state = get_capture_internal_state();
                if (capture_state != ALSA_STATE_OPEN)
                {
                    ret = -1;
                }
            }
            else
            {
                TRACE(0, "%s ALSA_DEAMON_MSG_CAPTURE_OPEN send error", __func__);
                audio_free(pcm_arg);
                ret = -1;
            }
        }
        else
        {
            TRACE(0, "alsa alloc pcm arg error");
            ret = -1;
        }
    }
    else
    {
        TRACE(0, "capture_state %d, not need open", capture_state);
    }
    if (ret == 0)
    {
        set_user_state(user_id, ALSA_STATE_OPEN);
        set_user_stream(user_id, ALSA_STREAM_CAPTURE);
    }
    alsa_os_mutex_release(g_alsa_deamon.capture.ctrl.interface_lock);
    return ret;
}

int alsa_deamon_capture_start(uint8_t user_id)
{
    int ret = 0;
    alsa_os_status_t os_status;
    alsa_os_mutex_wait(g_alsa_deamon.capture.ctrl.interface_lock, osWaitForever);
    alsa_state_t capture_state = get_capture_internal_state();
    if (capture_state == ALSA_STATE_CLOSE)
    {
        TRACE(0, "alsa_deamon not support CLOSE -> START");
        alsa_os_mutex_release(g_alsa_deamon.capture.ctrl.interface_lock);
        return -1;
    }
    if (capture_state == ALSA_STATE_OPEN || capture_state == ALSA_STATE_STOP)
    {
        if (0 == alsa_deamon_send_msg(ALSA_DEAMON_MSG_CAPTURE_START, NULL))
        {
            do
            {
                os_status = alsa_os_semaphore_acquire(g_alsa_deamon.capture.ctrl.start_sem, 100);
            } while (os_status != osOK);
            capture_state = get_capture_internal_state();
            if (capture_state != ALSA_STATE_START)
            {
                ret = -1;
            }
        }
        else
        {
            TRACE(0, "%s ALSA_DEAMON_MSG_CAPTURE_START send error", __func__);
            ret = -1;
        }
    }
    else
    {
        TRACE(0, "capture_state %d, not need start", capture_state);
    }
    if (ret == 0)
    {
        set_user_state(user_id, ALSA_STATE_START);
    }
    alsa_os_mutex_release(g_alsa_deamon.capture.ctrl.interface_lock);
    return ret;
}

int alsa_deamon_capture_stop(uint8_t user_id)
{
    int ret = 0;
    alsa_os_status_t os_status;
    alsa_os_mutex_wait(g_alsa_deamon.capture.ctrl.interface_lock, osWaitForever);
    uint8_t need_stop_codec = 1;

    alsa_state_t user_state = get_user_state(user_id);
    alsa_stream_t user_stream = get_user_stream(user_id);
    if (user_stream == ALSA_STREAM_CAPTURE &&
        ((user_state == ALSA_STATE_START) ||
            user_state == ALSA_STATE_OPEN))
    {
        TRACE(0, "%s not need stop codec. user_state = %d", __func__, user_state);
        need_stop_codec = 0;
    }

    if (need_stop_codec)
    {
        alsa_state_t capture_state = get_capture_internal_state();
        if (capture_state != ALSA_STATE_START)
        {
            TRACE(0, "%s not need stop codec. capture_state = %d", __func__, capture_state);
            need_stop_codec = 0;
        }
    }
    if (need_stop_codec)
    {
        if (0 == alsa_deamon_send_msg(ALSA_DEAMON_MSG_CAPTURE_STOP, NULL))
        {
            do
            {
                os_status = alsa_os_semaphore_acquire(g_alsa_deamon.capture.ctrl.stop_sem, 100);
            } while (os_status != osOK);
        }
        else
        {
            ASSERT(0, "%s ALSA_DEAMON_MSG_CAPTURE_STOP send error", __func__);
            ret = -1;
        }
    }
    else
    {
        set_user_state(user_id, ALSA_STATE_STOP);
    }
    alsa_os_mutex_release(g_alsa_deamon.capture.ctrl.interface_lock);
    return ret;
}

int alsa_deamon_capture_close(uint8_t user_id)
{
    TRACE(0, "%s ENTER", __func__);
    int ret = 0;
    alsa_os_status_t os_status;
    alsa_os_mutex_wait(g_alsa_deamon.capture.ctrl.interface_lock, osWaitForever);
    uint8_t close_codec = 1;

    alsa_state_t user_state = get_user_state(user_id);
    alsa_stream_t user_stream = get_user_stream(user_id);
    if (user_stream == ALSA_STREAM_CAPTURE)
    {
        if (!(user_state == ALSA_STATE_CLOSE ||
                user_state == ALSA_STATE_STOP))
        {
            TRACE(0, "%s user %d state %d, so alsa_deamon cannot close", __func__, i, user_state);
            close_codec = 0;
        }
    }

    if (close_codec)
    {
        if (0 == alsa_deamon_send_msg(ALSA_DEAMON_MSG_CAPTURE_CLOSE, NULL))
        {
            do
            {
                os_status = alsa_os_semaphore_acquire(g_alsa_deamon.capture.ctrl.close_sem, 100);
            } while (os_status != osOK);
        }
        else
        {
            ASSERT(0, "%s ALSA_DEAMON_MSG_CAPTURE_CLOSE send error", __func__);
            ret = -1;
        }
    }

    if (ret == 0)
    {
        set_user_state(user_id, ALSA_STATE_CLOSE);
        set_user_stream(user_id, ALSA_STREAM_UNKONWN);
    }
    alsa_os_mutex_release(g_alsa_deamon.capture.ctrl.interface_lock);
    TRACE(0, "%s ENTER", __func__);
    return ret;
}

alsa_state_t alsa_deamon_get_capture_state(void)
{
    return get_capture_internal_state();
}

uint8_t alsa_capture_volume_get(void)
{
    return 1;
    // return g_alsa_deamon.capture.vol.vol;
}

void alsa_capture_volume_update(void)
{
}
#endif

#ifdef ALSA_PLAYBACK_START_DELAY_EN
static alsa_user_type_t alsa_user_type = ALSA_USER_TYPE_UNKNOWN;
static uint8_t alsa_delay_time_max = 20; // 20 * 100ms
void alsa_delay_start(uint8_t enable)
{
    alsa_start_delayed = enable;
}

void alsa_deamon_set_playback_user_type(alsa_user_type_t type)
{
    alsa_user_type = type;
}

void alsa_deamon_set_playback_maxdelaytime(int delay_ms)
{
    alsa_delay_time_max = (uint8_t)(delay_ms / 100);
}
#endif

/***** Internal Interface *****/


static int alsa_deamon_ctrl_init(alsa_deamon_ctrl_t *ctrl)
{

    if (!ctrl)
    {
        TRACE(0, "%s ctrl NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_t interface_lock = NULL;
    alsa_os_mutex_t state_lock = NULL;
    alsa_os_semaphore_t open_sem = NULL;
    alsa_os_semaphore_t start_sem = NULL;
    alsa_os_semaphore_t stop_sem = NULL;
    alsa_os_semaphore_t close_sem = NULL;

    interface_lock = alsa_os_mutex_create();
    if (!interface_lock)
    {
        TRACE(0, "%s interface_lock create error", __func__);
        goto error_exit;
    }

    state_lock = alsa_os_mutex_create();
    if (!state_lock)
    {
        TRACE(0, "%s state_lock create error", __func__);
        goto error_exit;
    }

    open_sem = alsa_os_semaphore_create(1, 0);
    if (!open_sem)
    {
        TRACE(0, "%s open_sem create error", __func__);
        goto error_exit;
    }

    start_sem = alsa_os_semaphore_create(1, 0);
    if (!start_sem)
    {
        TRACE(0, "%s start_sem create error", __func__);
        goto error_exit;
    }

    stop_sem = alsa_os_semaphore_create(1, 0);
    if (!stop_sem)
    {
        TRACE(0, "%s stop_sem create error", __func__);
        goto error_exit;
    }

    close_sem = alsa_os_semaphore_create(1, 0);
    if (!close_sem)
    {
        TRACE(0, "%s close_sem create error", __func__);
        goto error_exit;
    }

    ctrl->interface_lock = interface_lock;
    ctrl->state_lock = state_lock;
    ctrl->open_sem = open_sem;
    ctrl->start_sem = start_sem;
    ctrl->stop_sem = stop_sem;
    ctrl->close_sem = close_sem;

    return 0;

error_exit:

    if (interface_lock)
    {
        alsa_os_mutex_delete(interface_lock);
    }
    if (state_lock)
    {
        alsa_os_mutex_delete(state_lock);
    }
    if (open_sem)
    {
        alsa_os_semaphore_delete(open_sem);
    }
    if (start_sem)
    {
        alsa_os_semaphore_delete(start_sem);
    }
    if (stop_sem)
    {
        alsa_os_semaphore_delete(stop_sem);
    }
    if (close_sem)
    {
        alsa_os_semaphore_delete(close_sem);
    }

    return -1;
}

static int alsa_deamon_vol_init(alsa_deamon_vol_t *vol)
{
    if (!vol)
    {
        TRACE(0, "%s vol NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_t vol_lock = alsa_os_mutex_create();
    if (!vol_lock)
    {
        TRACE(0, "%s vol_lock create error", __func__);
        return -1;
    }

    vol->vol_lock = vol_lock;
    vol->vol = ALSA_DEAMON_DEFAULT_VOL;
    vol->gain_db = ALSA_DEAMON_DEFAULT_GAIN_DB;
    vol->mute = ALSA_REAL_UN_MUTE;
    vol->percent_vol = ALSA_DEAMON_DEFAULT_PERCENT_VOL;
    vol->float_vol = ALSA_DEAMON_DEFAULT_FLOAT_VOL;

    return 0;
}

static void alsa_deamon_task(void *arg)
{
    alsa_deamon_msg_t *msg = NULL;
    alsa_state_t playback_state;
#ifdef ALSA_SUPPORT_CAPTURE
    alsa_state_t capture_state;
#endif

    while (1)
    {
        if (0 == alsa_os_mq_get(g_alsa_deamon.mq, &msg, osWaitForever))
        {
            if (!msg)
            {
                TRACE(0, "%s get msg NULL", __func__);
                continue;
            }
            playback_state = get_playback_internal_state();
#ifdef ALSA_SUPPORT_CAPTURE
            capture_state = get_capture_internal_state();
#endif
            TRACE(0, "alsa_deamon: recv msg %s", alsa_deamon_msg_str[msg->type]);
            TRACE(0, "alsa_deamon: playback_state %d", playback_state);
#ifdef ALSA_SUPPORT_CAPTURE
            TRACE(0, "alsa_deamon: capture_state  %d", capture_state);
#endif
            switch (msg->type)
            {
            case ALSA_DEAMON_MSG_PLAYBACK_OPEN:
                if (msg->arg && playback_state == ALSA_STATE_CLOSE)
                {
                    alsa_pcm_arg_t *pcm_arg = (alsa_pcm_arg_t *)msg->arg;
                    playback_pcm_arg_set(pcm_arg);
                    playback_open_msg_handler(pcm_arg->sample_rate, pcm_arg->bits, pcm_arg->ch_num);
                    audio_free(msg->arg);
                }
                if (0 != alsa_os_semaphore_release(g_alsa_deamon.playback.ctrl.open_sem))
                {
                    ASSERT(0, "%s release playback_open_sem error", __func__);
                }
                break;
            case ALSA_DEAMON_MSG_PLAYBACK_START:
                if (playback_state == ALSA_STATE_OPEN || playback_state == ALSA_STATE_STOP)
                {
                    playback_start_msg_handler();
                }
                if (0 != alsa_os_semaphore_release(g_alsa_deamon.playback.ctrl.start_sem))
                {
                    ASSERT(0, "%s release playback_start_sem error", __func__);
                }
                break;
            case ALSA_DEAMON_MSG_PLAYBACK_STOP:
                if (playback_state == ALSA_STATE_START)
                {
                    playback_stop_msg_handler();
                }
                if (0 != alsa_os_semaphore_release(g_alsa_deamon.playback.ctrl.stop_sem))
                {
                    ASSERT(0, "%s release playback_stop_sem error", __func__);
                }
                break;
            case ALSA_DEAMON_MSG_PLAYBACK_CLOSE:
                if (playback_state >= ALSA_STATE_OPEN)
                {
                    playback_close_msg_handler(playback_state);
                }
                if (0 != alsa_os_semaphore_release(g_alsa_deamon.playback.ctrl.close_sem))
                {
                    ASSERT(0, "%s release playback_close_sem error", __func__);
                }
                break;
#ifdef ALSA_SUPPORT_CAPTURE
            case ALSA_DEAMON_MSG_CAPTURE_OPEN:
                if (msg->arg && capture_state == ALSA_STATE_CLOSE)
                {
                    alsa_pcm_arg_t *pcm_arg = (alsa_pcm_arg_t *)msg->arg;
                    capture_pcm_arg_set(pcm_arg);
                    capture_open_msg_handler(pcm_arg->sample_rate, pcm_arg->bits, pcm_arg->ch_num);
                    audio_free(msg->arg);
                }
                if (0 != alsa_os_semaphore_release(g_alsa_deamon.capture.ctrl.open_sem))
                {
                    ASSERT(0, "%s release capture_open_sem error", __func__);
                }
                break;
            case ALSA_DEAMON_MSG_CAPTURE_START:
                if (capture_state == ALSA_STATE_OPEN || capture_state == ALSA_STATE_STOP)
                {
                    capture_start_msg_handler();
                }
                if (0 != alsa_os_semaphore_release(g_alsa_deamon.capture.ctrl.start_sem))
                {
                    ASSERT(0, "%s release capture_start_sem error", __func__);
                }
                break;
            case ALSA_DEAMON_MSG_CAPTURE_STOP:
                if (capture_state == ALSA_STATE_START)
                {
                    capture_stop_msg_handler();
                }
                if (0 != alsa_os_semaphore_release(g_alsa_deamon.capture.ctrl.stop_sem))
                {
                    ASSERT(0, "%s release capture_stop_sem error", __func__);
                }
                break;
            case ALSA_DEAMON_MSG_CAPTURE_CLOSE:
                if (capture_state >= ALSA_STATE_OPEN)
                {
                    capture_close_msg_handler(capture_state);
                }
                if (0 != alsa_os_semaphore_release(g_alsa_deamon.capture.ctrl.close_sem))
                {
                    ASSERT(0, "%s release capture_close_sem error", __func__);
                }
                break;
#endif
            case ALSA_DEAMON_MSG_PCM_STATE:
                if (msg->arg)
                {
                    alsa_pcm_state_msg_t *pcm_state_msg = (alsa_pcm_state_msg_t *)msg->arg;
                    pcm_state_msg_handler(pcm_state_msg);
                    audio_free(msg->arg);
                }
                break;
            default:
                TRACE(0, "not handled case %d", msg->type);
                break;
            }
            audio_free(msg);
        }
    }
}

static int alsa_deamon_send_msg(alsa_deamon_msg_type_t type, void *arg)
{
    alsa_deamon_msg_t *msg = (alsa_deamon_msg_t *)audio_calloc(1, sizeof(alsa_deamon_msg_t));
    if (!msg)
    {
        TRACE(0, "ALSA_DEAMON_ERR alloc msg error");
        return -1;
    }

    msg->type = type;
    msg->arg = arg;

    osStatus_t status = alsa_os_mq_put(g_alsa_deamon.mq, &msg);
    if (status != osOK)
    {
        TRACE(0, "%s %d mq put error", __func__, __LINE__);
        audio_free(msg);
        return -1;
    }
    return 0;
}

static alsa_state_t get_playback_internal_state(void)
{
    alsa_state_t state = ALSA_STATE_ERROR;
    alsa_os_mutex_wait(g_alsa_deamon.playback.ctrl.state_lock, osWaitForever);
    state = g_alsa_deamon.playback.ctrl.state;
    alsa_os_mutex_release(g_alsa_deamon.playback.ctrl.state_lock);
    return state;
}

static int set_user_state(uint8_t user_id, alsa_state_t state)
{
    ASSERT((user_id < ALSA_USER_COUNT), "%s user_id %d error", __func__, user_id);
    ASSERT((state >= ALSA_STATE_ERROR && state <= ALSA_STATE_STOP), "%s state %d error", __func__, state);
    alsa_os_mutex_wait(g_alsa_deamon.user_lock, osWaitForever);
#ifdef ALSA_MIXER_PRIORITY_EN
    alsa_state_t current_state = alsa_deamon_get_playback_user_state(user_id);
    if ((current_state == ALSA_STATE_OPEN || current_state == ALSA_STATE_STOP) &&
        state == ALSA_STATE_START)
    {
        alsa_mixer_set_priority(user_id, ++alsa_playback_mixer_priority);
    }
    if (current_state == ALSA_STATE_START &&
        state == ALSA_STATE_STOP)
    {
        alsa_mixer_set_priority(user_id, --alsa_playback_mixer_priority);
    }
#endif
    g_alsa_deamon.user_state[user_id] = state;
    alsa_os_mutex_release(g_alsa_deamon.user_lock);
    return 0;
}

static alsa_state_t get_user_state(uint8_t user_id)
{
    alsa_state_t state = ALSA_STATE_ERROR;
    ASSERT((user_id < ALSA_USER_COUNT), "%s user_id %d error", __func__, user_id);
    ASSERT((state >= ALSA_STATE_ERROR && state <= ALSA_STATE_STOP), "%s state %d error", __func__, state);
    alsa_os_mutex_wait(g_alsa_deamon.user_lock, osWaitForever);
    state = g_alsa_deamon.user_state[user_id];
    alsa_os_mutex_release(g_alsa_deamon.user_lock);
    return state;
}

static int set_user_stream(uint8_t user_id, alsa_stream_t stream)
{
    ASSERT((user_id < ALSA_USER_COUNT), "%s user_id %d error", __func__, user_id);
    ASSERT((stream >= ALSA_STREAM_UNKONWN && stream < ALSA_STREAM_MAX), "%s stream %d error", __func__, stream);
    alsa_os_mutex_wait(g_alsa_deamon.user_lock, osWaitForever);
    g_alsa_deamon.user_stream[user_id] = stream;
    alsa_os_mutex_release(g_alsa_deamon.user_lock);
    return 0;
}

static alsa_stream_t get_user_stream(uint8_t user_id)
{
    alsa_stream_t stream = ALSA_STREAM_UNKONWN;
    ASSERT((user_id < ALSA_USER_COUNT), "%s user_id %d error", __func__, user_id);
    ASSERT((stream >= ALSA_STREAM_UNKONWN && stream < ALSA_STREAM_MAX), "%s stream %d error", __func__, stream);
    alsa_os_mutex_wait(g_alsa_deamon.user_lock, osWaitForever);
    stream = g_alsa_deamon.user_stream[user_id];
    alsa_os_mutex_release(g_alsa_deamon.user_lock);
    return stream;
}

static int alsa_open_internal(enum AUD_STREAM_T stream_type,
                              enum AUD_SAMPRATE_T sample_rate,
                              enum AUD_BITS_T bits,
                              enum AUD_CHANNEL_NUM_T ch_num)
{
    if (!(stream_type == AUD_STREAM_PLAYBACK || stream_type == AUD_STREAM_CAPTURE))
    {
        TRACE(0, "%s error stream_type %d", __func__, stream_type);
        return -1;
    }

    uint32_t dma_buffer_size;
    struct AF_STREAM_CONFIG_T stream_cfg;
    memset(&stream_cfg, 0, sizeof(stream_cfg));

    if (stream_type == AUD_STREAM_PLAYBACK)
    {
        set_playback_internal_state(ALSA_STATE_CLOSE);

        // 采样点字节数*每ms采样点数*通道数*ms数*2，底层I2S传输时都是2声道
        dma_buffer_size = 2 * (sample_rate / 1000) * ALSA_PLAYBACK_CHANNEL_NUM * ALSA_PLAYBACK_DMA_SLOT_MS * 2;
        alsa_playback_dma_buffer_size = dma_buffer_size;

        if(alsa_playback_dma_buffer == NULL)
        {
            alsa_playback_dma_buffer = xy_malloc_align(dma_buffer_size);
        }
        else
        {
            xy_assert(0);
        }

        stream_cfg.channel_num = ch_num;
        stream_cfg.sample_rate = sample_rate;
        stream_cfg.bits = bits;
        stream_cfg.handler = alsa_playback_callback;
        stream_cfg.data_ptr = alsa_playback_dma_buffer;
        stream_cfg.data_size = dma_buffer_size;
        stream_cfg.vol = alsa_volume_get();

        if (0 == af_stream_open(AUD_STREAM_PLAYBACK, &stream_cfg))
        {
            set_playback_internal_state(ALSA_STATE_OPEN);
            return 0;
        }
    }

#ifdef ALSA_SUPPORT_CAPTURE
    if (stream_type == AUD_STREAM_CAPTURE)
    {
        set_capture_internal_state(ALSA_STATE_CLOSE);

        // 采样点字节数*每ms采样点数*通道数*ms数*2，底层I2S传输时都是2声道
        dma_buffer_size = 2 * (sample_rate / 1000) * ALSA_CAPTURE_CHANNEL_NUM * ALSA_CAPTURE_DMA_SLOT_MS * 2;

        if(alsa_capture_dma_buffer == NULL)
        {
            alsa_capture_dma_buffer = xy_malloc_align(dma_buffer_size);
        }
        else
        {
            xy_assert(0);
        }

        stream_cfg.channel_num = ch_num;
        stream_cfg.sample_rate = sample_rate;
        stream_cfg.bits = bits;
        stream_cfg.handler = alsa_capture_callback;
        stream_cfg.data_ptr = alsa_capture_dma_buffer;
        stream_cfg.data_size = dma_buffer_size;
        stream_cfg.vol = alsa_capture_volume_get();

        if (0 == af_stream_open(AUD_STREAM_CAPTURE, &stream_cfg))
        {
            set_capture_internal_state(ALSA_STATE_OPEN);
            return 0;
        }
    }
#endif

    TRACE(0, "%s af_stream_open error. stream_type %d", __func__, stream_type);
    return -1;
}

static int alsa_close_internal(enum AUD_STREAM_T stream_type)
{
    int ret = -1;
    if (!(stream_type == AUD_STREAM_PLAYBACK || stream_type == AUD_STREAM_CAPTURE))
    {
        TRACE(0, "%s error stream_type %d", __func__, stream_type);
        return -1;
    }

    if (stream_type == AUD_STREAM_PLAYBACK)
    {
        if (0 == af_stream_close(AUD_STREAM_PLAYBACK))
        {
            set_playback_internal_state(ALSA_STATE_CLOSE);
            ret = 0;
        }
        if(alsa_playback_dma_buffer)
        {
            xy_free(alsa_playback_dma_buffer);
            alsa_playback_dma_buffer = NULL;
        }
    }

#ifdef ALSA_SUPPORT_CAPTURE
    else if (stream_type == AUD_STREAM_CAPTURE)
    {
        if (0 == af_stream_close(AUD_STREAM_CAPTURE))
        {
            set_capture_internal_state(ALSA_STATE_CLOSE);
            ret = 0;
        }
        if(alsa_capture_dma_buffer)
        {
            xy_free(alsa_capture_dma_buffer);
            alsa_capture_dma_buffer = NULL;
        }
    }
#endif

    return ret;
}

static int alsa_start_internal(enum AUD_STREAM_T stream_type)
{
    if (!(stream_type == AUD_STREAM_PLAYBACK || stream_type == AUD_STREAM_CAPTURE))
    {
        TRACE(0, "%s error stream_type %d", __func__, stream_type);
        return -1;
    }

    if (stream_type == AUD_STREAM_PLAYBACK)
    {
        if (0 == af_stream_start(AUD_STREAM_PLAYBACK))
        {
            set_playback_internal_state(ALSA_STATE_START);
            return 0;
        }
    }

#ifdef ALSA_SUPPORT_CAPTURE
    if (stream_type == AUD_STREAM_CAPTURE)
    {
        if (0 == af_stream_start(AUD_STREAM_CAPTURE))
        {
            set_capture_internal_state(ALSA_STATE_START);
            return 0;
        }
    }
#endif

    TRACE(0, "%s af_stream_start error", __func__);
    return -1;
}

static int alsa_stop_internal(enum AUD_STREAM_T stream_type)
{
    if (!(stream_type == AUD_STREAM_PLAYBACK || stream_type == AUD_STREAM_CAPTURE))
    {
        TRACE(0, "%s error stream_type %d", __func__, stream_type);
        return -1;
    }

    if (stream_type == AUD_STREAM_PLAYBACK)
    {
        if (0 == af_stream_stop(AUD_STREAM_PLAYBACK))
        {
            set_playback_internal_state(ALSA_STATE_STOP);
            return 0;
        }
    }

#ifdef ALSA_SUPPORT_CAPTURE
    if (stream_type == AUD_STREAM_CAPTURE)
    {
        if (0 == af_stream_stop(AUD_STREAM_CAPTURE))
        {
            set_capture_internal_state(ALSA_STATE_STOP);
            return 0;
        }
    }
#endif

    TRACE(0, "%s af_stream_stop error", __func__);
    return -1;
}

int alsa_deamon_pcm_state_notify(alsa_stream_t stream, alsa_pcm_state_t state, uint8_t user_id)
{
    alsa_pcm_state_msg_t *pcm_state = (alsa_pcm_state_msg_t *)audio_calloc(1, sizeof(alsa_pcm_state_msg_t));
    if (pcm_state)
    {
        pcm_state->stream = stream;
        pcm_state->id = user_id;
        pcm_state->state = state;
        if (0 != alsa_deamon_send_msg(ALSA_DEAMON_MSG_PCM_STATE, pcm_state))
        {
            audio_free(pcm_state);
        }
    }
    return 0;
}

static int pcm_state_msg_handler(alsa_pcm_state_msg_t *pcm_state_msg)
{
    if (g_alsa_deamon.pcm_state_changed_cb)
        g_alsa_deamon.pcm_state_changed_cb(pcm_state_msg->stream, pcm_state_msg->state, pcm_state_msg->id);
    return 0;
}


static int alsa_deamon_playback_init(alsa_deamon_playback_t *playback,
                                     alsa_playback_frome_user_callback_t cb)
{
    if (!playback)
    {
        TRACE(0, "%s playback NULL error", __func__);
        return -1;
    }

    alsa_os_mutex_t playback_cb_lock = alsa_os_mutex_create();
    if (!playback_cb_lock)
    {
        TRACE(0, "%s playback_cb_lock create error", __func__);
        return -1;
    }

    ASSERT(!alsa_deamon_ctrl_init(&playback->ctrl), "%s alsa_deamon_ctrl_init error", __func__);
    ASSERT(!alsa_deamon_vol_init(&playback->vol), "%s alsa_deamon_vol_init error", __func__);
    playback->playback_from_user_cb = cb;
#ifdef ALSA_SUPPORT_PA_CTRL
    playback->pa_ctrl = ALSA_PA_OFF;
#endif
    playback->playback_cb_lock = playback_cb_lock;

    return 0;
}

static int set_playback_internal_state(alsa_state_t state)
{
    ASSERT((state >= ALSA_STATE_ERROR && state <= ALSA_STATE_STOP), "%s state %d error", __func__, state);
    alsa_os_mutex_wait(g_alsa_deamon.playback.ctrl.state_lock, osWaitForever);
    g_alsa_deamon.playback.ctrl.state = state;
    alsa_os_mutex_release(g_alsa_deamon.playback.ctrl.state_lock);
    return 0;
}

static void playback_pcm_arg_set(alsa_pcm_arg_t *pcm_arg)
{
    if (pcm_arg)
    {
        playback_pcm_arg.sample_rate = pcm_arg->sample_rate;
        playback_pcm_arg.bits = pcm_arg->bits;
        playback_pcm_arg.ch_num = pcm_arg->ch_num;
    }
}

static int playback_open_msg_handler(uint32_t sample_rate,
                                     uint8_t bits,
                                     uint8_t ch_num)
{
#ifdef ALSA_AUDIO_PROCESS_RENDER_EN
    if (0 != alsa_audio_process_render_open(
                 ALSA_AUDIO_PROCESS_RENDER_USER_WIFI,
                 ALSA_AUDIO_PROCESS_RENDER_SAMPLE_RATE,
                 ALSA_AUDIO_PROCESS_RENDER_INPUT_CHANNEL_NUM,
                 ALSA_AUDIO_PROCESS_RENDER_OUTPUT_CHANNEL_NUM,
                 ALSA_AUDIO_PROCESS_RENDER_INPUT_BITS,
                 ALSA_AUDIO_PROCESS_RENDER_OUTPUT_BITS,
                 ALSA_PLAYBACK_DMA_SLOT_MS / 2))
    {
        TRACE(0, "%s open render handle failed", __func__);
        return -1;
    }
#endif

    if (0 != alsa_open_internal(AUD_STREAM_PLAYBACK, sample_rate, bits, ch_num))
    {
        TRACE(0, "%s alsa_open_internal error", __func__);
        return -1;
    }

    alsa_volume_update();

#ifdef ALSA_SUPPORT_PA_CTRL
    alsa_pa_ctrl(ALSA_PA_ON);
#endif

#ifdef ALSA_STATE_HOOK_EN
    alsa_state_open_hook_ivk(sample_rate, ch_num, bits);
#endif

    return 0;
}

static int playback_start_msg_handler(void)
{
    alsa_fill_data_on_start();

#ifdef ALSA_PLAYBACK_START_DELAY_EN
    /* delay to start */
    uint8_t alsa_delay_time = 0;
    while ((alsa_user_type != ALSA_USER_TYPE_LOCAL) && alsa_start_delayed && alsa_delay_time++ < alsa_delay_time_max)
        osDelay(100);
#endif

#ifdef ALSA_STATE_HOOK_EN
    alsa_state_start_hook_ivk();
#endif

    alsa_start_internal(AUD_STREAM_PLAYBACK);

    return 0;
}

static int playback_stop_msg_handler(void)
{
    alsa_stop_internal(AUD_STREAM_PLAYBACK);

#ifdef ALSA_STATE_HOOK_EN
    alsa_state_stop_hook_ivk();
#endif

    return 0;
}

static int playback_close_msg_handler(alsa_state_t current_state)
{
    if (current_state == ALSA_STATE_START)
    {
        alsa_stop_internal(AUD_STREAM_PLAYBACK);

#ifdef ALSA_STATE_HOOK_EN
        alsa_state_stop_hook_ivk();
#endif
    }

#ifdef ALSA_MIXER_PROCESS_EN
    alsa_mixer_process_reset();
#endif

    alsa_close_internal(AUD_STREAM_PLAYBACK);

#ifdef ALSA_SUPPORT_PA_CTRL
    alsa_pa_ctrl(ALSA_PA_OFF);
#endif

#ifdef ALSA_STATE_HOOK_EN
    alsa_state_close_hook_ivk();
#endif

#ifdef ALSA_AUDIO_PROCESS_EN
    alsa_audio_process_close();
#endif

#ifdef ALSA_AUDIO_PROCESS_RENDER_EN
    alsa_audio_process_render_close(ALSA_AUDIO_PROCESS_RENDER_USER_WIFI);
#endif

    return 0;
}

static uint32_t alsa_deamon_pcm_process(uint8_t *buf, uint32_t len)
{
    uint32_t op_len;
    uint8_t *op_buf;
    uint32_t user_data_len = 0;

    //memset(buf, 0, len); //alsa_playback_data_from_user中有memset

    op_buf = buf;
    op_len = len;

#ifdef ALSA_QUEUE_STEREO_PLAYBACK_MONO_EN
    op_buf = &alsa_deamon_stereo_tmp_buffer[0];
    op_len = 2 * len;
#endif

    if (g_alsa_deamon.playback.playback_from_user_cb)
        user_data_len = g_alsa_deamon.playback.playback_from_user_cb(op_buf, op_len);

    if (user_data_len == 0)
    {
#ifdef ALSA_DUMP_EN
        alsa_dump_point(ALSA_DUMP_POINT_BEFORE_AUDIO_PROCESS, buf, len);
        alsa_dump_point(ALSA_DUMP_POINT_AFTER_AUDIO_PROCESS, buf, len);
#endif
        return 0;
    }

#ifdef ALSA_QUEUE_STEREO_PLAYBACK_MONO_EN
#if (ALSA_PLAYBACK_QUEUE_BITS == ALSA_SUPPORTED_BITS_16)
    alsa_stereo_to_mono_16bit(op_buf, &op_len);
#elif (ALSA_PLAYBACK_QUEUE_BITS == ALSA_SUPPORTED_BITS_24)
    alsa_stereo_to_mono_24bit(op_buf, &op_len);
#elif (ALSA_PLAYBACK_QUEUE_BITS == ALSA_SUPPORTED_BITS_32)
    alsa_stereo_to_mono_32bit(op_buf, &op_len);
#else
#error "ALSA unsupported bits format for stereo to mono"
#endif
#endif

#ifdef ALSA_AUDIO_PROCESS_RENDER_EN
    alsa_audio_process_render(op_buf, &op_len);
#endif

#ifdef ALSA_AUDIO_PROCESS_EN
    alsa_audio_process(op_buf, op_len);
#endif

#ifdef ALSA_QUEUE_STEREO_PLAYBACK_MONO_EN
    if (!(op_len == len))
    {
        TRACE(op_len == len, "%s %d error op_len = %d, len = %d", __func__, __LINE__, op_len, len);
    }
    memcpy(buf, op_buf, len);
#endif

#ifdef ALSA_QUEUE_STEREO_PLAYBACK_MONO_EN
    user_data_len /= 2;
#endif

#ifdef ALSA_DUMP_EN
#if defined(ALSA_AUDIO_PROCESS_RENDER_EN) && defined(ALSA_AUDIO_PROCESS_EN)
#else
    alsa_dump_point(ALSA_DUMP_POINT_AFTER_AUDIO_PROCESS, buf, len);
#endif
#endif

    return user_data_len;
}

#ifdef AUDIO_OUTPUT_INVERT_ALL_CHANNEL
static uint32_t alsa_invert_process(uint8_t *buf, uint32_t len)
{
    enum AUD_BITS_T bits = playback_pcm_arg.bits;
    if (bits == AUD_BITS_16)
    {
        int16_t *buf16 = (int16_t *)buf;
        for (int i = 0; i < len / sizeof(int16_t); i++)
        {
            int32_t tmp = -buf16[i];
            buf16[i] = __SSAT(tmp, 16);
        }
    }
    else if (bits == AUD_BITS_24)
    {
        int32_t *buf32 = (int32_t *)buf;
        for (int i = 0; i < len / sizeof(int32_t); i++)
        {
            int32_t tmp = -buf32[i];
            buf32[i] = __SSAT(tmp, 24);
        }
    }
    else
    {
        int32_t *buf32 = (int32_t *)buf;
        for (int i = 0; i < len / sizeof(int32_t); i++)
        {
            if ((uint32_t)buf[i] == 0x80000000)
                buf32[i] = 0x7fffffff;
            else
                buf32[i] = -buf32[i];
        }
    }
    return 0;
}
#endif

static uint32_t alsa_fill_data_on_start(void)
{
    uint8_t *buf = NULL;
    uint32_t dma_buffer_size = alsa_playback_dma_buffer_size;
    uint32_t len = dma_buffer_size / 2;

    /* fill DMA PING buffer */
    buf = alsa_playback_dma_buffer;

    alsa_deamon_pcm_process(buf, len);

#ifdef AUDIO_OUTPUT_INVERT_ALL_CHANNEL
    alsa_invert_process(buf, len);
#endif

#ifdef ALSA_STATE_HOOK_EN
    alsa_state_process_hook_ivk(buf, len);
#endif

    /* fill DMA PANG buffer */
    buf = alsa_playback_dma_buffer + len;

    alsa_deamon_pcm_process(buf, len);

#ifdef AUDIO_OUTPUT_INVERT_ALL_CHANNEL
    alsa_invert_process(buf, len);
#endif

#ifdef ALSA_STATE_HOOK_EN
    alsa_state_process_hook_ivk(buf, len);
#endif

    return 0;
}

static uint32_t alsa_playback_callback(uint8_t *buf, uint32_t len)
{
    uint32_t user_data_len = 0;
    int i;
    uint8_t user_could_stop_count = 0;
    static uint32_t alsa_silence_count = 0;
    alsa_state_t state_of_user;
    alsa_stream_t stream_of_user;

    alsa_deamon_playback_cb_lock();

    user_data_len = alsa_deamon_pcm_process(buf, len);

#ifdef ALSA_STATE_HOOK_EN
    alsa_state_process_hook_ivk(buf, len);
#endif

    if (user_data_len == len)
    {
        alsa_silence_count = 0;
    }
    else if (user_data_len < len && user_data_len != 0)
    {
        alsa_silence_count = ALSA_SILENCE_TRIGGER_STOP_COUNT - 2;
        TRACE(0, "alsa fadeout imediately stop. user_data_len = %d, silence_count %d", user_data_len, alsa_silence_count);
    }
    else
    {
        alsa_silence_count++;
        bool playback_stop = true;
        TRACE(0, "alsa_silence_count = %d", alsa_silence_count);
        if (alsa_silence_count >= ALSA_SILENCE_TRIGGER_STOP_COUNT)
        {
            for (i = 0; i < ALSA_USER_COUNT; i++)
            {
                state_of_user = get_user_state(i);
                stream_of_user = get_user_stream(i);
                if (stream_of_user == ALSA_STREAM_PLAYBACK &&
                    state_of_user == ALSA_STATE_START)
                {
                    playback_stop = false;
                    break;
                }
            }
            if (playback_stop)
            {
                TRACE(0, "alsa normal stop. user_data_len = %d, silence_count %d", user_data_len, alsa_silence_count);
                alsa_deamon_send_msg(ALSA_DEAMON_MSG_PLAYBACK_STOP, NULL);
                alsa_silence_count = 0;
            }
        }
    }
    alsa_deamon_playback_cb_unlock();

    return user_data_len;
}

static int playback_vol_update_internal(uint8_t vol)
{
    struct AF_STREAM_CONFIG_T *stream_cfg = NULL;
    if (0 != af_stream_get_cfg(AUD_STREAM_PLAYBACK, &stream_cfg))
    {
        TRACE(0, "%s af_stream_get_cfg NULL", __func__);

        return -1;
    }

    if (stream_cfg->vol == vol)
    {
        TRACE(0, "%s vol same with last config", __func__);

        return 0;
    }

    if (stream_cfg && stream_cfg->vol != vol)
    {
        stream_cfg->vol = vol;

        return 0;
    }

    return -1;
}

#ifdef ALSA_SUPPORT_PA_CTRL
static void alsa_pa_ctrl(alsa_pa_ctrl_t ctrl)
{
    TRACE(0, "%s ctrl = %d", __func__, ctrl);

    if (ctrl == ALSA_PA_OFF)
    {
        if (g_alsa_deamon.playback.pa_ctrl == ALSA_PA_ON)
        {
            g_alsa_deamon.playback.pa_ctrl = ALSA_PA_OFF;

            analog_aud_codec_speaker_enable(false);

            /* codec mute has bad effect to other player not use alsa, so delete it*/
            // analog_aud_codec_mute();

            /* Disable DAC after stopping stream (specifically after disabling PA) */
            analog_aud_codec_dac_enable(false);
        }
    }
    else if (ctrl == ALSA_PA_ON)
    {
        if (g_alsa_deamon.playback.pa_ctrl == ALSA_PA_OFF)
        {
            g_alsa_deamon.playback.pa_ctrl = ALSA_PA_ON;

            /* Enable DAC before starting stream (specifically before enabling PA) */
            analog_aud_codec_dac_enable(true);

            analog_aud_codec_speaker_enable(true);

            // analog_aud_codec_nomute();

#ifdef ALSA_PA_OPEN_WAIT_TIME_MS
            TRACE(0, "wait PA open %d ms...", ALSA_PA_OPEN_WAIT_TIME_MS);
            osDelay(ALSA_PA_OPEN_WAIT_TIME_MS);
#endif
        }
    }
    else
    {
    }
}
#endif

static uint32_t playback_float_volume_mapping(uint32_t vol)
{
    if (vol > 100)
    {
        vol = 100;
    }

    uint32_t vol_dac = 0;

#ifdef TGT_VOLUME_LEVEL_USER_MAX
    if (vol == 0)
    {
        vol_dac = 0;
        TRACE(0, "%s vol = 0, mute vol_dac = 0", __func__);
    }
    else
    {
        uint32_t step = tgt_user_volume_step_get();
        uint32_t step_index = 0;
        for (step_index = 1; step_index < step; step_index++)
        {
            if (vol >= alsa_float_volume_mapping_array[step_index - 1] && vol < alsa_float_volume_mapping_array[step_index])
            {
                break;
            }
        }
        if (vol == alsa_float_volume_mapping_array[step - 1])
        {
            step_index = step;
        }
        vol_dac = step_index;
        TRACE(0, "%s step %d , vol_32 = %d vol_dac(step_index) = %d", __func__, step, vol, vol_dac);
    }
#else
    vol_dac = vol / 6;
    TRACE(0, "%s default_step: vol_32 = %d vol_dac = %d", __func__, vol, vol_dac);
#endif

    return vol_dac;
}

#ifdef ALSA_SUPPORT_CAPTURE
static int alsa_deamon_capture_init(alsa_deamon_capture_t *capture,
                                    alsa_capture_to_user_callback_t cb)
{
    if (!capture)
    {
        TRACE(0, "%s capture NULL error", __func__);
        return -1;
    }

    ASSERT(!alsa_deamon_ctrl_init(&capture->ctrl), "%s alsa_deamon_ctrl_init error", __func__);
    ASSERT(!alsa_deamon_vol_init(&capture->vol), "%s alsa_deamon_vol_init error", __func__);
    capture->capture_to_user_cb = cb;

    return 0;
}

static int set_capture_internal_state(alsa_state_t state)
{
    ASSERT((state >= ALSA_STATE_ERROR && state <= ALSA_STATE_STOP), "%s state %d error", __func__, state);
    alsa_os_mutex_wait(g_alsa_deamon.capture.ctrl.state_lock, osWaitForever);
    g_alsa_deamon.capture.ctrl.state = state;
    alsa_os_mutex_release(g_alsa_deamon.capture.ctrl.state_lock);
    return 0;
}

alsa_state_t get_capture_internal_state(void)
{
    alsa_state_t state = ALSA_STATE_ERROR;
    alsa_os_mutex_wait(g_alsa_deamon.capture.ctrl.state_lock, osWaitForever);
    state = g_alsa_deamon.capture.ctrl.state;
    alsa_os_mutex_release(g_alsa_deamon.capture.ctrl.state_lock);
    return state;
}

static void capture_pcm_arg_set(alsa_pcm_arg_t *pcm_arg)
{
    if (pcm_arg)
    {
        capture_pcm_arg.sample_rate = pcm_arg->sample_rate;
        capture_pcm_arg.bits = pcm_arg->bits;
        capture_pcm_arg.ch_num = pcm_arg->ch_num;
    }
}

static int capture_open_msg_handler(uint32_t sample_rate,
                                    uint8_t bits,
                                    uint8_t ch_num)
{
    if (0 != alsa_open_internal(AUD_STREAM_CAPTURE, sample_rate, bits, ch_num))
    {
        TRACE(0, "%s alsa_open_internal error", __func__);
        return -1;
    }

    alsa_capture_volume_update();

    return 0;
}

static int capture_start_msg_handler(void)
{
    return alsa_start_internal(AUD_STREAM_CAPTURE);
}

static int capture_stop_msg_handler(void)
{
    return alsa_stop_internal(AUD_STREAM_CAPTURE);
}

static int capture_close_msg_handler(alsa_state_t current_state)
{
    if (current_state == ALSA_STATE_START)
    {
        alsa_stop_internal(AUD_STREAM_CAPTURE);
    }

    return alsa_close_internal(AUD_STREAM_CAPTURE);
}

static uint32_t alsa_capture_callback(uint8_t *buf, uint32_t len)
{
    if (g_alsa_deamon.capture.capture_to_user_cb)
        return g_alsa_deamon.capture.capture_to_user_cb(buf, len);

    return 0;
}

#endif