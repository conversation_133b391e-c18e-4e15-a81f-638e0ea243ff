#include "atc_ps.h"
#ifdef LTE_SMS_FEATURE

void AtcAp_SmsPdu_Analysis(ATC_AP_MSG_DATA_REQ_STRU* pAtcDataReq)
{
    ST_ATC_AP_SMS_PDU_PARAMETER* pSmsPduParam;
    if(ATC_AP_FALSE == AtcAp_CurrEventChk_IsWaitSmsPdu())
    {
        return;
    }
    if(NULL != g_AtcApInfo.pCurrEvent)
    {
        AtcAp_FreeEventBuffer((unsigned char*)g_AtcApInfo.pCurrEvent);
    }
    g_AtcApInfo.pCurrEvent = (ST_ATC_CMD_COM_EVENT*)AtcAp_Malloc(sizeof(UN_ATC_CMD_EVENT));

    pSmsPduParam = (ST_ATC_AP_SMS_PDU_PARAMETER*)g_AtcApInfo.pCurrEvent;
    pSmsPduParam->usEvent = D_ATC_AP_SMS_PDU_REQ;
    pSmsPduParam->usCmdEvent = g_AtcApInfo.usCurrEvent;
    pSmsPduParam->usPduLength = pAtcDataReq->usMsgLen;
    pSmsPduParam->pucPduData = pAtcDataReq->aucMsgData;

    if(AtcAp_SendCmdEventToPs() != ATC_AP_TRUE)
    {
        AtcAp_SendErrorRsp();
    }

}

void AtcAp_SmsQcmgsText_Analysis(ATC_AP_MSG_DATA_REQ_STRU* pDataReq, ST_ATC_QCMGS_PARAMETER* pEventParam)
{
    ST_ATC_QCMGS_PARAMETER*       pQcmgsParam;
    static unsigned char     ucWaitPduFlg = ATC_AP_FALSE;
    static unsigned char    *pParamBuff = NULL;

    if(g_SmsFormatMode == 0)
    {
        AtcAp_SendErrorRsp();
        return;
    }
    xy_printf(0,ATC_AP_T,INFO_LOG, "[AtcAp_SmsQcmgsText_Analysis] %d",ucWaitPduFlg);

    if(ATC_AP_FALSE == ucWaitPduFlg)
    {
        xy_assert(pEventParam != NULL);

        ucWaitPduFlg = ATC_AP_TRUE;
        pQcmgsParam = pEventParam ;
        pQcmgsParam->usEvent = D_ATC_AP_SMS_PDU_IND;
        pParamBuff = (unsigned char *)AtcAp_Malloc(sizeof(ST_ATC_QCMGS_PARAMETER));
        AtcAp_MemCpy(pParamBuff, pEventParam, sizeof(ST_ATC_QCMGS_PARAMETER));
        AtcAp_MsgProc_SMS_PDU_Ind((unsigned char*)(pQcmgsParam));
        return;
    }
    else
    {
        xy_assert(pDataReq != NULL);
        xy_assert(pParamBuff != NULL);
        
        ucWaitPduFlg = ATC_AP_FALSE;
        pQcmgsParam = (ST_ATC_QCMGS_PARAMETER*)g_AtcApInfo.pCurrEvent;
        AtcAp_MemCpy(pQcmgsParam, pParamBuff, offsetof(ST_ATC_QCMGS_PARAMETER, usTextLength));
        pQcmgsParam->usEvent = D_ATC_EVENT_QCMGS;
        pQcmgsParam->usTextLength = pDataReq->usMsgLen;
        pQcmgsParam->pucTextData = (unsigned long)pDataReq->aucMsgData;

        AtcAp_Free(pParamBuff);

        if(AtcAp_SendCmdEventToPs() != ATC_AP_TRUE)
        {
            AtcAp_SendErrorRsp();
        }
    }
}

void AtcAp_SmsText_Analysis(ATC_AP_MSG_DATA_REQ_STRU* pAtcDataReq)
{
    ST_ATC_AP_SMS_TEXT_PARAMETER* pSmsTextParam;

    if(ATC_AP_FALSE == AtcAp_CurrEventChk_IsWaitSmsPdu())
    {
        return;
    }
    if(D_ATC_EVENT_QCMGS == g_AtcApInfo.usCurrEvent)
    {
        AtcAp_SmsQcmgsText_Analysis(pAtcDataReq,NULL);
        return;
    }
    
    if(NULL != g_AtcApInfo.pCurrEvent)
    {
        AtcAp_FreeEventBuffer((unsigned char*)g_AtcApInfo.pCurrEvent);
    }
    g_AtcApInfo.pCurrEvent = (ST_ATC_CMD_COM_EVENT*)AtcAp_Malloc(sizeof(UN_ATC_CMD_EVENT));

    pSmsTextParam = (ST_ATC_AP_SMS_TEXT_PARAMETER*)g_AtcApInfo.pCurrEvent;
    pSmsTextParam->usEvent = D_ATC_AP_SMS_TEXT_REQ;
    pSmsTextParam->usCmdEvent = g_AtcApInfo.usCurrEvent;
    pSmsTextParam->usTextLength = pAtcDataReq->usMsgLen;
    pSmsTextParam->pucTextData = pAtcDataReq->aucMsgData;
    
    if(AtcAp_SendCmdEventToPs() != ATC_AP_TRUE)
    {
        AtcAp_SendErrorRsp();
    }

}
#endif

