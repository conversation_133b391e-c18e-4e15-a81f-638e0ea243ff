/**********************************************************************/
/**
 * @file yx_ia_uart.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-14
 * @version V1.0
 * @brief uart接口适配
 **********************************************************************/
#ifndef YX_IA_UART_H
#define YX_IA_UART_H

#include "yx_type.h"

/**
 * @brief 雅迅uart总线定义
 */
typedef enum {
    YX_IA_UART_0,
    YX_IA_UART_1,
    YX_IA_UART_2,
    YX_IA_UART_3,
    YX_IA_UART_4,
    YX_IA_UART_5,
    YX_IA_UART_MAX
} yx_ia_uart_bus_e;

/**
 * @brief 雅迅uart事件定义
 */
typedef enum {
    YX_IA_UART_READY_READ = 1,
    YX_IA_UART_READY_WRITE,
    YX_IA_UART_READ_DONE,
    YX_IA_UART_WRITE_DONE,
    YX_IA_UART_FIFO_ERROR,
    YX_IA_UART_RX_BREAK,
    YX_IA_UART_TRANSMIT_ERROR,
    YX_IA_UART_EVENT_MAX
} yx_ia_uart_event_e;

/**
 * @brief 雅迅uart配置结构体
 */
typedef struct {
    INT32U      baud_rate;      ///<波特率: 115200等
    INT8U       parity;         ///<校验方式：0-none, 1-odd, 2-even
    INT8U       data_bits;      ///<数据位大小: 8-bit, 7-bit, 6-bit, 5-bit
    INT8U       stop_bits;      ///<停止位大小: 1-bit 2-bit
} yx_ia_uart_config_t;

/**
 * @brief 雅迅uart句柄
 */
typedef struct {
    yx_ia_uart_bus_e        bus_id;
    void                    *sdk_handler;
    yx_ia_uart_config_t     config;
} yx_ia_uart_handler_t;

/**
 * @brief 打开uart总线
 * @ingroup ia_uart
 * @param[in] bus_id 雅迅uart总线ID
 * @param[in] yx_config 雅迅uart配置
 * @return yx_ia_uart_handler_t * 雅迅uart句柄
 * @retval 非NULL, 成功，返回雅迅uart句柄指针
 * @retval NULL, 失败
 * @note 此调用获取的雅迅uart句柄，需要正确使用'yx_ia_uart_close'进行资源回收
 */
yx_ia_uart_handler_t *yx_ia_uart_open(yx_ia_uart_bus_e bus_id,
    const yx_ia_uart_config_t *yx_config);

/**
 * @brief 关闭uart总线
 * @ingroup ia_uart
 * @param[in] yx_handler 雅迅uart句柄
 * @return INT32S
 * @retval RTN_OK   成功
 * @retval RTN_ERR  失败
 * @note 'yx_ia_uart_open'获取的雅迅uart句柄，需要正确使用此调用进行资源回收
 */
INT32S yx_ia_uart_close(yx_ia_uart_handler_t *yx_handler);

/**
 * @brief 向uart总线写入数据
 * @ingroup ia_uart
 * @param[in] yx_handler 雅迅uart句柄
 * @param[in] buf 发送数据缓冲区
 * @param[in] length 需要传输数据长度
 * @return INT32S
 * @retval 0或正数  已发送的字节数
 * @retval RTN_ERR  发送错误
 */
INT32S yx_ia_uart_write(yx_ia_uart_handler_t *yx_handler, INT8U *buf,
    INT32U length);

/**
 * @brief 从uart总线读取数据
 * @ingroup ia_uart
 * @param[in] yx_handler 雅迅uart句柄
 * @param[out] buf 接收数据缓冲区
 * @param[in] size 缓冲区大小
 * @return INT32S
 * @retval 0或正数  已读取的字节数
 * @retval RTN_ERR  发送错误
 */
INT32S yx_ia_uart_read(yx_ia_uart_handler_t *yx_handler, INT8U *buf,
    INT32U size);

/**
 * @brief simcom注册回调函数
 * @ingroup ia_uart
 * @param[in] yx_handler 雅迅uart句柄
 * @param[in] cb 回调函数
 * @param[in] data 回调函数的data参数
 * @return INT32S
 * @retval 0或正数  已读取的字节数
 * @retval RTN_ERR  发送错误
 */
INT32S ya_ia_uart_register_cb(yx_ia_uart_handler_t *yx_handler,
    VOID (*cb)(INT32S port, INT32S len, VOID *data), VOID *data);

/**
 * @brief simcom注册串口事件回调函数
 * @ingroup ia_uart
 * @param[in] cb 回调函数
 * @return INT32S
 * @retval 0或正数  已读取的字节数
 * @retval RTN_ERR  发送错误
 */
INT32S ya_ia_uart_reg_event_cb(VOID (*cb)(INT32S port, yx_ia_uart_event_e event));

#endif /* YX_IA_UART_H */