#ifndef __ALSA_SUPPORTED_H__
#define __ALSA_SUPPORTED_H__

#define ALSA_SUPPORTED_SAMPLE_RATE_48000    48000
#define ALSA_SUPPORTED_SAMPLE_RATE_16000    16000
#define ALSA_SUPPORTED_SAMPLE_RATE_44100    44100
#define ALSA_SUPPORTED_SAMPLE_RATE_8000     8000

#define ALSA_SUPPORTED_CHANNEL_NUM_1        1
#define ALSA_SUPPORTED_CHANNEL_NUM_2        2
#define ALSA_SUPPORTED_CHANNEL_NUM_3        3

#define ALSA_SUPPORTED_BITS_16              16
#define ALSA_SUPPORTED_BITS_24              24
#define ALSA_SUPPORTED_BITS_32              32

#define ALSA_SUPPORTED_CHMAP_CH0            (1 << 0)
#define ALSA_SUPPORTED_CHMAP_CH1            (1 << 1)
#define ALSA_SUPPORTED_CHMAP_CH2            (1 << 2)

#endif