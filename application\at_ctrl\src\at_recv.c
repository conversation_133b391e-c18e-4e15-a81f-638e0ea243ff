#include "at_context.h"
#include "at_ctl.h"
#include "at_error.h"
#if XY_CMUX
#include "cmux_report.h"
#endif /* XY_CMUX */
#include "posix_device.h"
#include "posix_io.h"
#include "xy_system.h"
#include <errno.h>

/*******************************************************************************
 *                             Macro definitions                               *
 ******************************************************************************/
#if UART_AT
#define AT_RECV_BUF_LEN     3200
#else /* UART_AT */
#define AT_RECV_BUF_LEN     1600
#endif /* UART_AT */

/*******************************************************************************
 *                       Global variable declarations                          *
 ******************************************************************************/
osThreadId_t g_at_recv_handle = NULL;
osMutexId_t g_at_recv_mux = NULL;
char s_recvBuf[AT_RECV_BUF_LEN] = {0};

/*******************************************************************************
 *                       Global function declarations                          *
 ******************************************************************************/
/*AT物理串口热插拔能力，最终体现为context上下文是否有效*/
int update_at_tty(const char* dev_path, bool on)
{
    uint32_t i;
    
    if (dev_path == NULL)
        return XY_ERR;

    for (i = 0; i < sizeof(g_at_tty_ctx) / sizeof(at_context_t); i++)
    {
        if (g_at_tty_ctx[i].devPath == NULL)
        {
            continue;
        }

        if (strcmp(dev_path, g_at_tty_ctx[i].devPath) == 0)
        {
            if (!on)
            {
                int ret = posix_close(g_at_tty_ctx[i].devFd);
                if (ret)
                {
			        ATRECV_NET_LOG("close dev:%s fail,err:%d", dev_path, errno);                    
                    xy_printf(0, PLATFORM_AP, WARN_LOG, "close dev:%s fail, err:%d", dev_path, errno);
                    return XY_ERR;
                }
                else
                    g_at_tty_ctx[i].devFd = -1;
            }
            else
            {
                if (g_at_tty_ctx[i].input == NULL)
                {
                    xy_printf(0, PLATFORM_AP, WARN_LOG, "open dev:%s fail, at ctrl input null", dev_path);
                    return XY_ERR;
                }
                int devFd = posix_open(dev_path, O_RDWR);
                if (devFd == -1)
                {
			        ATRECV_NET_LOG("open dev:%s fail,err:%d", dev_path, errno);                      
                    xy_printf(0, PLATFORM_AP, WARN_LOG, "open dev:%s fail, err:%d", dev_path, errno);
                    // 重复打开时，保留之前的devFd
                    if (errno == EBUSY)
                        return XY_ERR;
                }
                g_at_tty_ctx[i].devFd = devFd;
            }
            break;
        }
    }

    if (i == sizeof(g_at_tty_ctx) / sizeof(at_context_t))
        return XY_ERR;
    
    return XY_OK;
}

static void open_all_at_ttys(void)
{
	uint32_t i = 0;
	for (i = 0; i < sizeof(g_at_tty_ctx) / sizeof(at_context_t); i++)
	{
		/* CMUX外设初始化时不打开，只能在CMUX UP事件回调中处理 */  
        if (g_at_tty_ctx[i].fd != AT_CMUX1_FD && g_at_tty_ctx[i].fd != AT_CMUX2_FD)
        {
            update_at_tty(g_at_tty_ctx[i].devPath, true);
            /* 透传信息初始化 */
            atPassthInfoClear(&g_at_tty_ctx[i].passthInfo);
        }
	}
}

#if XY_CMUX
/* cmux设备事件回调 */
void cmux_dev_event_cb(CMUX_EVENT_TYPE eventId)
{
    osMutexAcquire(g_at_recv_mux, osWaitForever);
    if (eventId == CMUX_UP)
    {
        /* cmux up */
#if UART_AT
        update_at_tty(DEV_UART_AT, false);
#else /* UART_AT */
        update_at_tty(DEV_LPUART_AT, false);
#endif /* UART_AT */
        update_at_tty(DEV_CMUX1, true);
        update_at_tty(DEV_CMUX2, true);

    }
    else if (eventId == CMUX_DOWN)
    {
        /* cmux down */
        update_at_tty(DEV_CMUX1, false);
        update_at_tty(DEV_CMUX2, false);
#if UART_AT
        update_at_tty(DEV_UART_AT, true);
#else /* UART_AT */
        update_at_tty(DEV_LPUART_AT, true);
#endif /* UART_AT */
    }
    osMutexRelease(g_at_recv_mux);
}
#endif /* XY_CMUX */

void at_read_tty_task(void* arg)
{
    (void)arg;
    uint32_t i = 0;
    int ret = 0;
    int max_fd;
    fd_set readfds;
    fd_set exceptfds;

    /* 注册at外设事件callback */
#if XY_CMUX
    cmux_reg_event_callback(cmux_dev_event_cb);
#endif /* XY_CMUX */

    while (1)
    {
        max_fd = -1;
        FD_ZERO(&readfds);
        FD_ZERO(&exceptfds);

        osMutexAcquire(g_at_recv_mux, osWaitForever);
        for (i = 0; i < sizeof(g_at_tty_ctx) / sizeof(at_context_t); i++)
        {
            if (g_at_tty_ctx[i].devFd != -1)
            {
                FD_SET(g_at_tty_ctx[i].devFd, &readfds);
                FD_SET(g_at_tty_ctx[i].devFd, &exceptfds);
                max_fd = (g_at_tty_ctx[i].devFd > max_fd) ? g_at_tty_ctx[i].devFd : max_fd;
            }
        }
        osMutexRelease(g_at_recv_mux);
        
        if (max_fd == -1)
        {
            osDelay(200);
            continue;
        }

        ret = posix_select(max_fd + 1, &readfds, NULL, &exceptfds, NULL);
        if (ret < 0)
        {
            if (errno == EBADF)
            {
                xy_assert(0);
            }
            else
            {
                continue;
            }
        }
        else if (ret == 0)
        {
            /* nothing happened */
            continue;
        }

        ssize_t readLen;
        
        osMutexAcquire(g_at_recv_mux, osWaitForever);
        for (i = 0; i < sizeof(g_at_tty_ctx) / sizeof(at_context_t); i++)
        {
            if (g_at_tty_ctx[i].devFd != -1 && g_at_tty_ctx[i].input != NULL && FD_ISSET(g_at_tty_ctx[i].devFd, &readfds))
            {
                readLen = posix_read(g_at_tty_ctx[i].devFd, s_recvBuf, sizeof(s_recvBuf));
                if (readLen > 0)
                {
                	/*AT+NV=SET,SPECIAL,32,从AT通道接收到的数据全打印,定位脏数据*/
                    if((g_softap_fac_nv->special & (1<<5)))
                    {
                        static uint32_t s_debug_at_recv_num = 0;
                        s_debug_at_recv_num += readLen;
                        s_recvBuf[readLen] = '\0';
                        xy_printf(0, PLATFORM_AP, INFO_LOG,"[at_read_tty_task]devFd:%d,dLen:%d,tLen:%d,data:%s",g_at_tty_ctx[i].devFd,
                        readLen, s_debug_at_recv_num, s_recvBuf);
                        
                    }
                    g_at_tty_ctx[i].input(s_recvBuf, readLen);
                }
            }
        }
        osMutexRelease(g_at_recv_mux);
    }

    g_at_recv_handle = NULL;
    osThreadExit();
}

void at_recv_task_init(void)
{	
    g_at_recv_mux = osMutexNew(NULL);

	open_all_at_ttys();

    /* 透传资源初始化 */
    atPassthInit();

	osThreadAttr_t thread_attr = {0};
	thread_attr.name        = AT_RECV_THREAD_NAME;
	thread_attr.stack_size	= AT_RECV_THREAD_STACKSIZE;
	thread_attr.priority    = AT_RECV_THREAD_PRIO;
	g_at_recv_handle = osThreadNew((osThreadFunc_t)(at_read_tty_task), NULL, &thread_attr); 
}
