#include "xy_atc_interface.h"

#ifdef HW_LM_SUPPORT
#include "xy_hw_lm_interface.h"
#include "xy_ps_api.h"
#include "xy_hw_lm_api.h"
#include "atc_ps.h"

static int xy_lm_interface_call(unsigned char ucReqMsgId, void* pReqMsg, unsigned short usMsgLen, void* pMsgCnf)
{
    LM_INTERFACE_DATA_STRU *pInterfaceReq;
    unsigned short          usReqDataLen;
    LM_INTERFACE_DATA_STRU *pInterfaceCnf = NULL;
    SimReadCnfData         *pSimReadCnfData;
    SimReadCnf             *pSimReadCnf;
   
    if(usMsgLen < 4)
    {
        usReqDataLen = sizeof(LM_INTERFACE_DATA_STRU);
    }
    else
    {
        usReqDataLen = sizeof(LM_INTERFACE_DATA_STRU) + usMsgLen - 4;
    }
    
    pInterfaceReq = (LM_INTERFACE_DATA_STRU*)AtcAp_Malloc(usReqDataLen);
    pInterfaceReq->usEvent = D_HW_LM_INTERFACE_REQ;
    pInterfaceReq->eMsgId = ucReqMsgId;
    pInterfaceReq->usDataLen = usMsgLen;
    if(pReqMsg != NULL)
    {
        AtcAp_MemCpy(pInterfaceReq->aucData, pReqMsg, usMsgLen);
    }

    if(XY_OK != xy_msg_interface_call(pInterfaceReq, usReqDataLen, &pInterfaceCnf, NULL))
    {
        AtcAp_Free(pInterfaceReq);
        if(NULL != pMsgCnf)
        {
            AtcAp_Free(pMsgCnf);
        }
        return XY_ERR;
    }
    AtcAp_Free(pInterfaceReq);

    if(NULL != pMsgCnf)
    {
        if(pInterfaceCnf->eMsgId != ucReqMsgId + 1)
        {
            AtcAp_Free(pMsgCnf);
            AtcAp_Free(pInterfaceCnf);
            return XY_ERR;
        }

        if(SIM_READ_CNF == pInterfaceCnf->eMsgId)
        {
           pSimReadCnfData = (SimReadCnfData*)pInterfaceCnf->aucData;
           pSimReadCnf = (SimReadCnf*)pMsgCnf;
           pSimReadCnf->rc = pSimReadCnfData->rc;
           pSimReadCnf->len = pSimReadCnfData->len;
           if(0 != pSimReadCnf->len)
           {
                pSimReadCnf->data = (UINT16*)AtcAp_Malloc(pSimReadCnf->len);
                AtcAp_MemCpy((unsigned char*)pSimReadCnf->data, (unsigned char*)pSimReadCnfData->data, pSimReadCnf->len);   
           }
        }
        else
        {
            AtcAp_MemCpy((unsigned char*)pMsgCnf, (unsigned char*)pInterfaceCnf->aucData, pInterfaceCnf->usDataLen);
        }
    }

    AtcAp_Free(pInterfaceCnf);
    
    return XY_OK;
}

/**
 * @brief           由需求方实现
 * @param pInd      ImsCamStartInd上报, 消息指针由芯翼释放
 * @return          void
 */
void xy_lm_ImsCamStartInd(ImsCamStartInd* pInd)
{
    //调用需求方接口
}

/**
 * @brief           由需求方实现
 * @param pInd      CamServiceChangeInd上报，消息指针由芯翼释放
 * @return          void
 */
void xy_lm_CamServiceChangeInd(CamServiceChangeInd* pInd)
{
    //调用需求方接口
}

/**
 * @brief           由需求方实现
 * @param pInd      EmergencyNumListInd上报，消息指针由芯翼释放
 * @return          void
 */
void xy_lm_EmergencyNumListInd(EmergencyNumListInd* pInd)
{
    //调用需求方接口
}

/**
 * @brief           由需求方实现
 * @param pInd      PdpActInd上报，消息指针由芯翼释放
 * @return          void
 */
void xy_lm_PdpActInd(PdpActInd* pInd)
{
    //调用需求方接口
}

/**
 * @brief           由需求方实现
 * @param pInd      PdpModifyInd上报，消息指针由芯翼释放
 * @return          void
 */
void xy_lm_PdpModifyInd(PdpModifyInd* pInd)
{
    //调用需求方接口
}

/**
 * @brief           由需求方实现
 * @param pInd      PdpDeactInd上报，消息指针由芯翼释放
 * @return          void
 */
void xy_lm_PdpDeactInd(PdpDeactInd* pInd)
{
    //调用需求方接口
}

/**
 * @brief           由需求方实现
 * @param pInd      RrcHimsSsacInd上报，消息指针由芯翼释放
 * @return          void
 */
void xy_lm_RrcHimsSsacInd(RrcHimsSsacInd* pInd)
{
    //调用需求方接口
}

/**
 * @brief           由需求方实现
 * @param pInd      RrcStateInd上报，消息指针由芯翼释放
 * @return          void
 */
void xy_lm_RrcStateInd(RrcStateInd* pInd)
{
    //调用需求方接口
}

/**
 * @brief           由需求方实现
 * @param pInd      CDrxCfgInd上报，消息指针由芯翼释放
 * @return          void
 */
void xy_lm_CDrxCfgInd(CDrxCfgInd* pInd)
{
    //调用需求方接口
}

/**
 * @brief           由需求方实现
 * @param pInd      L2CongestReportInd上报，消息指针由芯翼释放
 * @return          void
 */
void xy_lm_L2CongestReportInd(L2CongestReportInd* pInd)
{
    //调用需求方接口
}

/**
 * @brief           由需求方实现
 * @param pInd      RohcStateInd上报，消息指针由芯翼释放
 * @return          void
 */
void xy_lm_RohcStateInd(RohcStateInd* pInd)
{
    //调用需求方接口
}

/**
 * @brief           由需求方实现
 * @param pInd      BerInfoInd 上报，消息指针由芯翼释放
 * @return          void
 */
void xy_lm_BerInfoInd(BerInfoInd* pInd)
{
    //调用需求方接口
}

/**
 * @brief           主动上报处理函数
 * @param           主动上报消息
 * @paramLen        主动上报消息长度
 * @return          void
 */
static void xy_lm_interface_ind(unsigned int eventId, void *param, int paramLen)
{
    LM_INTERFACE_DATA_STRU *pInterfaceInd = (LM_INTERFACE_DATA_STRU*)param;

    switch(pInterfaceInd->eMsgId)
    {
        case IMS_CAM_START_IND:
            xy_lm_ImsCamStartInd((ImsCamStartInd*)pInterfaceInd->aucData);
            break;
        case IMS_CAM_SERVICE_CHANGE_IND:
            xy_lm_CamServiceChangeInd((CamServiceChangeInd*)pInterfaceInd->aucData);
            break;
        case NW_EMERGENCY_NUM_IND:
            xy_lm_EmergencyNumListInd((EmergencyNumListInd*)pInterfaceInd->aucData);
            break;
        case PDP_ACT_IND:
            xy_lm_PdpActInd((PdpActInd*)pInterfaceInd->aucData);
            break;
        case XXX_PDP_MODIFY_IND:
            xy_lm_PdpModifyInd((PdpModifyInd*)pInterfaceInd->aucData);
            break;
        case PDP_DEACT_IND:
            xy_lm_PdpDeactInd((PdpDeactInd*)pInterfaceInd->aucData);
            break;
        case RRC_HIMS_SSAC_CHANGE_NTF:
            xy_lm_RrcHimsSsacInd((RrcHimsSsacInd*)pInterfaceInd->aucData);
            break;
        case RRC_STATE_IND:
            xy_lm_RrcStateInd((RrcStateInd*)pInterfaceInd->aucData);
            break;
        case CDRX_CFG_IND:
            xy_lm_CDrxCfgInd((CDrxCfgInd*)pInterfaceInd->aucData);
            break;
        case L2_CONGEST_RT_IND:
            xy_lm_L2CongestReportInd((L2CongestReportInd*)pInterfaceInd->aucData);
            break;
        case ROHC_STATE_IND:
            xy_lm_RohcStateInd((RohcStateInd*)pInterfaceInd->aucData);
            break;
        case BER_INFO_IND:
            xy_lm_BerInfoInd((BerInfoInd*)pInterfaceInd->aucData);
            break;
        default:
            break;
    }
}

/**
 * @brief           主动上报处理函数注册
 * @return          void
 */
void xy_lm_interface_register()
{
    xy_atc_registerPSEventCallback(D_XY_PS_REG_EVENT_HW_LM_INTERFACE, xy_lm_interface_ind);
}

UINT8 xy_lm_SimAuthReq(SimAuthReq* pReq, SimAuthCnf **ppCnf)
{
    *ppCnf = (SimAuthCnf*)AtcAp_Malloc(sizeof(SimAuthCnf));
    
    return xy_lm_interface_call(SIM_AUTH_REQ, pReq, sizeof(SimAuthReq), *ppCnf);
}

UINT8 xy_lm_SetApnReq(SetApnReq* pReq, SetApnCnf **ppCnf)
{
    *ppCnf = (SetApnCnf*)AtcAp_Malloc(sizeof(SetApnCnf));
    
    return xy_lm_interface_call(SET_APN_REQ, pReq, sizeof(SetApnReq), *ppCnf);
}

UINT8 xy_lm_ActPdnReq(ActPdnReq* pReq, ActPdnCnf **ppCnf)
{
    *ppCnf = (ActPdnCnf*)AtcAp_Malloc(sizeof(ActPdnCnf));
    
    return xy_lm_interface_call(ACT_PDN_REQ, pReq, sizeof(ActPdnReq), *ppCnf);
}

UINT8 xy_lm_SimReadReq(SimReadReq* pReq, SimReadCnf **ppCnf)
{
    *ppCnf = (SimReadCnf*)AtcAp_Malloc(sizeof(SimReadCnf));
    
    return xy_lm_interface_call(SIM_READ_REQ, pReq, sizeof(SimReadReq), *ppCnf);
}

UINT8 xy_lm_PsStartReq(PsStartReq* pReq)
{
    return xy_lm_interface_call(START_PS_REQ, pReq, sizeof(PsStartReq), NULL);  
}

UINT8 xy_lm_GetEmmTimerLenReq(TIME_LEN_CNF **ppCnf)
{
    *ppCnf = (TIME_LEN_CNF*)AtcAp_Malloc(sizeof(TIME_LEN_CNF));
    
    return xy_lm_interface_call(GET_EMM_TIMER_LEN_REQ, NULL, 0, *ppCnf);
}

UINT8 xy_lm_EmcAttachReq(EmcAttachReq* pReq, EmcAttachCnf **ppCnf)
{
    *ppCnf = (EmcAttachCnf*)AtcAp_Malloc(sizeof(EmcAttachCnf));
    
    return xy_lm_interface_call(EMERGENCY_ATTACH_REQ, pReq, sizeof(PsStartReq), *ppCnf);
}

UINT8 xy_lm_MMIgnorl3411TimerInd(HimsMmIgnor3411TimerInd* pInd)
{
    return xy_lm_interface_call(MM_IGNOR_3411_TIMER_IND, pInd, sizeof(HimsMmIgnor3411TimerInd), NULL);
}

UINT8 xy_lm_CellThreshAdjReq(CellThreshAdjReq* pReq)
{
    return xy_lm_interface_call(CELL_THRESH_ADJ, pReq, sizeof(CellThreshAdjReq), NULL); 
}

UINT8 xy_lm_MeasRptAdjReq(MeasRptAdjReq* pReq)
{
    return xy_lm_interface_call(XXX_MEAS_RPT_ADJ, pReq, sizeof(MeasRptAdjReq), NULL); 
}

UINT8 xy_lm_MeasTriggerThreshAdjReq(MeasTriggerThreshAdjReq* pReq)
{
    return xy_lm_interface_call(XXX_MEAS_TRIGGER_THRESH_ADJ, pReq, sizeof(MeasTriggerThreshAdjReq), NULL); 
}

UINT8 xy_lm_L2CongestReportSetReq(L2CongestReportSetReq* pReq, L2CongestReportSetCnf **ppCnf)
{
    *ppCnf = (L2CongestReportSetCnf*)AtcAp_Malloc(sizeof(L2CongestReportSetCnf));
    
    return xy_lm_interface_call(L2_CONGEST_RT_SET_REQ, pReq, sizeof(L2CongestReportSetReq), *ppCnf);
}

UINT8 xy_lm_BerSetReq(BerSetReq* pReq, BerSetCnf **ppCnf)
{
    *ppCnf = (BerSetCnf*)AtcAp_Malloc(sizeof(BerSetCnf));
    
    return xy_lm_interface_call(BER_SET_REQ, pReq, sizeof(BerSetReq), *ppCnf);
}

#endif
