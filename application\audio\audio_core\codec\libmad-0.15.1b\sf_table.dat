/*
 * libmad - MPEG audio decoder library
 * Copyright (C) 2000-2004 Underbit Technologies, Inc.
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * $Id: sf_table.dat,v 1.7 2004/01/23 09:41:33 rob Exp $
 */

/*
 * These are the scalefactor values for Layer I and Layer II.
 * The values are from Table B.1 of ISO/IEC 11172-3.
 *
 * There is some error introduced by the 32-bit fixed-point representation;
 * the amount of error is shown. For 16-bit PCM output, this shouldn't be
 * too much of a problem.
 *
 * Strictly speaking, Table B.1 has only 63 entries (0-62), thus a strict
 * interpretation of ISO/IEC 11172-3 would suggest that a scalefactor index of
 * 63 is invalid. However, for better compatibility with current practices, we
 * add a 64th entry.
 */

  MAD_F(0x20000000),  /* 2.000000000000 => 2.000000000000, e  0.000000000000 */
  MAD_F(0x1965fea5),  /* 1.587401051968 => 1.587401051074, e  0.000000000894 */
  MAD_F(0x1428a2fa),  /* 1.259921049895 => 1.259921051562, e -0.000000001667 */
  MAD_F(0x10000000),  /* 1.000000000000 => 1.000000000000, e  0.000000000000 */
  MAD_F(0x0cb2ff53),  /* 0.793700525984 => 0.793700527400, e -0.000000001416 */
  MAD_F(0x0a14517d),  /* 0.629960524947 => 0.629960525781, e -0.000000000833 */
  MAD_F(0x08000000),  /* 0.500000000000 => 0.500000000000, e  0.000000000000 */
  MAD_F(0x06597fa9),  /* 0.396850262992 => 0.396850261837, e  0.000000001155 */

  MAD_F(0x050a28be),  /* 0.314980262474 => 0.314980261028, e  0.000000001446 */
  MAD_F(0x04000000),  /* 0.250000000000 => 0.250000000000, e  0.000000000000 */
  MAD_F(0x032cbfd5),  /* 0.198425131496 => 0.198425132781, e -0.000000001285 */
  MAD_F(0x0285145f),  /* 0.157490131237 => 0.157490130514, e  0.000000000723 */
  MAD_F(0x02000000),  /* 0.125000000000 => 0.125000000000, e  0.000000000000 */
  MAD_F(0x01965fea),  /* 0.099212565748 => 0.099212564528, e  0.000000001220 */
  MAD_F(0x01428a30),  /* 0.078745065618 => 0.078745067120, e -0.000000001501 */
  MAD_F(0x01000000),  /* 0.062500000000 => 0.062500000000, e  0.000000000000 */

  MAD_F(0x00cb2ff5),  /* 0.049606282874 => 0.049606282264, e  0.000000000610 */
  MAD_F(0x00a14518),  /* 0.039372532809 => 0.039372533560, e -0.000000000751 */
  MAD_F(0x00800000),  /* 0.031250000000 => 0.031250000000, e  0.000000000000 */
  MAD_F(0x006597fb),  /* 0.024803141437 => 0.024803142995, e -0.000000001558 */
  MAD_F(0x0050a28c),  /* 0.019686266405 => 0.019686266780, e -0.000000000375 */
  MAD_F(0x00400000),  /* 0.015625000000 => 0.015625000000, e  0.000000000000 */
  MAD_F(0x0032cbfd),  /* 0.012401570719 => 0.012401569635, e  0.000000001084 */
  MAD_F(0x00285146),  /* 0.009843133202 => 0.009843133390, e -0.000000000188 */

  MAD_F(0x00200000),  /* 0.007812500000 => 0.007812500000, e  0.000000000000 */
  MAD_F(0x001965ff),  /* 0.006200785359 => 0.006200786680, e -0.000000001321 */
  MAD_F(0x001428a3),  /* 0.004921566601 => 0.004921566695, e -0.000000000094 */
  MAD_F(0x00100000),  /* 0.003906250000 => 0.003906250000, e  0.000000000000 */
  MAD_F(0x000cb2ff),  /* 0.003100392680 => 0.003100391477, e  0.000000001202 */
  MAD_F(0x000a1451),  /* 0.002460783301 => 0.002460781485, e  0.000000001816 */
  MAD_F(0x00080000),  /* 0.001953125000 => 0.001953125000, e  0.000000000000 */
  MAD_F(0x00065980),  /* 0.001550196340 => 0.001550197601, e -0.000000001262 */

  MAD_F(0x00050a29),  /* 0.001230391650 => 0.001230392605, e -0.000000000955 */
  MAD_F(0x00040000),  /* 0.000976562500 => 0.000976562500, e  0.000000000000 */
  MAD_F(0x00032cc0),  /* 0.000775098170 => 0.000775098801, e -0.000000000631 */
  MAD_F(0x00028514),  /* 0.000615195825 => 0.000615194440, e  0.000000001385 */
  MAD_F(0x00020000),  /* 0.000488281250 => 0.000488281250, e  0.000000000000 */
  MAD_F(0x00019660),  /* 0.000387549085 => 0.000387549400, e -0.000000000315 */
  MAD_F(0x0001428a),  /* 0.000307597913 => 0.000307597220, e  0.000000000693 */
  MAD_F(0x00010000),  /* 0.000244140625 => 0.000244140625, e  0.000000000000 */

  MAD_F(0x0000cb30),  /* 0.000193774542 => 0.000193774700, e -0.000000000158 */
  MAD_F(0x0000a145),  /* 0.000153798956 => 0.000153798610, e  0.000000000346 */
  MAD_F(0x00008000),  /* 0.000122070313 => 0.000122070313, e  0.000000000000 */
  MAD_F(0x00006598),  /* 0.000096887271 => 0.000096887350, e -0.000000000079 */
  MAD_F(0x000050a3),  /* 0.000076899478 => 0.000076901168, e -0.000000001689 */
  MAD_F(0x00004000),  /* 0.000061035156 => 0.000061035156, e  0.000000000000 */
  MAD_F(0x000032cc),  /* 0.000048443636 => 0.000048443675, e -0.000000000039 */
  MAD_F(0x00002851),  /* 0.000038449739 => 0.000038448721, e  0.000000001018 */

  MAD_F(0x00002000),  /* 0.000030517578 => 0.000030517578, e  0.000000000000 */
  MAD_F(0x00001966),  /* 0.000024221818 => 0.000024221838, e -0.000000000020 */
  MAD_F(0x00001429),  /* 0.000019224870 => 0.000019226223, e -0.000000001354 */
  MAD_F(0x00001000),  /* 0.000015258789 => 0.000015258789, e -0.000000000000 */
  MAD_F(0x00000cb3),  /* 0.000012110909 => 0.000012110919, e -0.000000000010 */
  MAD_F(0x00000a14),  /* 0.000009612435 => 0.000009611249, e  0.000000001186 */
  MAD_F(0x00000800),  /* 0.000007629395 => 0.000007629395, e -0.000000000000 */
  MAD_F(0x00000659),  /* 0.000006055454 => 0.000006053597, e  0.000000001858 */

  MAD_F(0x0000050a),  /* 0.000004806217 => 0.000004805624, e  0.000000000593 */
  MAD_F(0x00000400),  /* 0.000003814697 => 0.000003814697, e  0.000000000000 */
  MAD_F(0x0000032d),  /* 0.000003027727 => 0.000003028661, e -0.000000000934 */
  MAD_F(0x00000285),  /* 0.000002403109 => 0.000002402812, e  0.000000000296 */
  MAD_F(0x00000200),  /* 0.000001907349 => 0.000001907349, e -0.000000000000 */
  MAD_F(0x00000196),  /* 0.000001513864 => 0.000001512468, e  0.000000001396 */
  MAD_F(0x00000143),  /* 0.000001201554 => 0.000001203269, e -0.000000001714 */
  MAD_F(0x00000000)   /* this compatibility entry is not part of Table B.1 */
