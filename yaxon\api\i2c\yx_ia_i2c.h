/**********************************************************************/
/**
 * @file yx_ia_i2c.h
 * @copyright Copyright (c) 2025-2025 厦门雅迅智联科技股份有限公司
 * <AUTHOR>
 * @date 2025-03-10
 * @version V1.0
 * @brief i2c接口适配
 **********************************************************************/
#ifndef YX_IA_I2C_H
#define YX_IA_I2C_H

#include "yx_type.h"

/**
 * @brief 雅迅i2c总线定义
 */
typedef enum {
    YX_IA_I2C_0,
    YX_IA_I2C_1,
    YX_IA_I2C_MAX
} yx_ia_i2c_bus_e;

/**
 * @brief 雅迅i2c适配句柄，master模式
 */
typedef struct {
    void                *sdk_handler;   /**< sdk原生handler */
    yx_ia_i2c_bus_e     bus_id;         /**< 总线id定义 */
    INT32U              clock;          /**< 时钟 */
} yx_ia_i2c_handler_t;

/**
 * @brief 打开i2c总线
 * @ingroup ia_i2c
 * @param[in] bus_id 雅迅i2c总线ID
 * @param[in] clock i2c总线时钟, 快速模式一般为400000bit/s
 * @return yx_ia_i2c_handler_t * 雅迅i2c适配句柄
 * @retval 非NULL, 成功，返回雅迅i2c适配句柄指针
 * @retval NULL, 失败
 * @note 此调用获取的i2c适配句柄，需要正确使用'yx_ia_i2c_close'进行资源回收
 */
yx_ia_i2c_handler_t *yx_ia_i2c_open(yx_ia_i2c_bus_e bus_id, INT32U clock);

/**
 * @brief 关闭i2c总线
 * @ingroup ia_i2c
 * @param[in] yx_handler 雅迅i2c适配句柄
 * @return INT32S
 * @retval 0 成功
 * @retval 负数 失败
 * @note 'yx_ia_i2c_open'获取的i2c适配句柄，需要正确使用此调用进行资源回收
 */
INT32S yx_ia_i2c_close(yx_ia_i2c_handler_t *yx_handler);

/**
 * @brief i2c总线写入数据
 * @ingroup ia_i2c
 * @param[in] yx_handler 雅迅i2c适配句柄
 * @param[in] slave i2c从设备地址, 7bit地址(无需移位，无需设置读写位)
 * @param[in] buf 数据缓冲区
 * @param[in] length 需要传输数据长度
 * @return INT32S
 * @retval 0或正数 已发送的字节数
 * @retval 负数 发送错误
 */
INT32S yx_ia_i2c_write(yx_ia_i2c_handler_t *yx_handler, INT8U slave,
    INT8U *buf, INT32U length);

/**
 * @brief i2c总线读取数据
 * @ingroup ia_i2c
 * @param[in] yx_handler 雅迅i2c适配句柄
 * @param[in] slave i2c从设备地址, 7bit地址(无需移位，无需设置读写位)
 * @param[out] buf 数据缓冲区
 * @param[in] size 缓冲区大小
 * @return INT32S
 * @retval 0或正数 已读取的字节数
 * @retval 负数 发送错误
 */
INT32S yx_ia_i2c_read(yx_ia_i2c_handler_t *yx_handler, INT8U slave,
    INT8U *buf, INT32U size);

/**
 * @brief i2c总线写入数据至寄存器
 * @ingroup ia_i2c
 * @param[in] yx_handler 雅迅i2c适配句柄
 * @param[in] slave i2c从设备地址, 7bit地址(无需移位，无需设置读写位)
 * @param[in] reg 寄存器地址, 8bit
 * @param[in] buf 数据缓冲区
 * @param[in] length 需要传输数据长度
 * @return INT32S
 * @retval 0或正数 已发送的字节数
 * @retval 负数 发送错误
 */
INT32S yx_ia_i2c_write_reg(yx_ia_i2c_handler_t *yx_handler, INT8U slave,
    INT8U reg, INT8U *buf, INT32U length);

/**
 * @brief i2c总线从寄存器读取数据
 * @ingroup ia_i2c
 * @param[in] yx_handler 雅迅i2c适配句柄
 * @param[in] slave i2c从设备地址, 7bit地址(无需移位，无需设置读写位)
 * @param[in] reg 寄存器地址, 8bit
 * @param[out] buf 数据缓冲区
 * @param[in] size 需要传输数据长度
 * @return INT32S
 * @retval 0或正数 已读取的字节数
 * @retval 负数 发送错误
 */
INT32S yx_ia_i2c_read_reg(yx_ia_i2c_handler_t *yx_handler, INT8U slave,
    INT8U reg, INT8U *buf, INT32U size);

#endif /* YX_IA_I2C_H */