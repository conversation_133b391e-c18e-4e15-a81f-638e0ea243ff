{
    "version": "0.1.0",
    "configurations": [
        {
            "name": "riscv-debug",
            "type": "cppdbg",
            "request": "launch",
            "miDebuggerPath": "C:\\C-Sky\\CDS\\MinGW\\riscv64-elf-tools\\bin\\riscv64-unknown-elf-gdb.exe",
            "miDebuggerServerAddress": "**********:1025",
             "program": "${workspaceRoot}/build/boot_mini/elf/boot_mini.elf",
            //"program": "${workspaceRoot}/build/elf/ap.elf",
            "cwd": "${workspaceFolder}"
        }
    ]
}