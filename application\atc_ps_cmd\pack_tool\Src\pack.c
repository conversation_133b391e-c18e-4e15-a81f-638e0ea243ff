#include "atc_ps.h"

static unsigned short pack_node_data(unsigned char* p_code_stream, unsigned short* p_offset, unsigned char* p_parent_node_code_stream_addr,
                                         unsigned char* parent_node_addr, pack_node_stru* node, unsigned char only_get_size_flg);


static unsigned short get_node_data_size(unsigned char* basic_addr, pack_node_stru* node)
{
    unsigned short size = 0;

    switch(node->param_type)
    {
        case PACK_PARAM_TYPE_DATA_SIZE:
            size = node->param;
            break;
        case PACK_PARAM_TYPE_PTR_U8:
            size = *((unsigned char*)(basic_addr + node->param)) * node->ext_param;
            break;
        case PACK_PARAM_TYPE_PTR_U16:
            size = *((unsigned short*)(basic_addr + node->param)) * node->ext_param;
            break;
        case PACK_PARAM_TYPE_PTR_U32:
            size = (unsigned short)(*(unsigned long*)(basic_addr + node->param)) * node->ext_param;
            break;
        case PACK_PARAM_TYPE_PTR_ARRAY:
        case PACK_PARAM_TYPE_PTR64_ARRAY:
            size = node->param;
            break;
        default:
            break;
    }
    
    return size;
}

static unsigned short get_node_data_num(unsigned char* basic_addr, pack_node_stru* node)
{
    unsigned short num = 0;

    switch(node->param_type)
    {
        case PACK_PARAM_TYPE_DATA_SIZE:
            num = 1;
            break;
        case PACK_PARAM_TYPE_PTR_U8:
            num = *((unsigned char*)(basic_addr + node->param));
            break;
        case PACK_PARAM_TYPE_PTR_U16:
            num = *((unsigned short*)(basic_addr + node->param));
            break;
        case PACK_PARAM_TYPE_PTR_U32:
            num = (unsigned short)(*(unsigned long*)(basic_addr + node->param));
            break;
        default:
            break;
    }

    return num;
}

static unsigned char* pack_curr_node_data(unsigned char* p_code_stream, unsigned short* p_offset, unsigned char* p_parent_node_code_stream_addr, 
                                               unsigned char* node_addr, pack_node_stru* node, unsigned char offset_in_node, unsigned char only_get_size_flg, 
                                               unsigned short* p_size, unsigned char** new_node_addr)
{
    unsigned short      data_size;
    unsigned char*      basic_addr;
    
    if(0 == node->level)
    {
        basic_addr = (node_addr + node->offset + offset_in_node);
    }
    else
    {
        basic_addr = *((unsigned char**)(node_addr + node->offset + offset_in_node));
    }

    data_size = get_node_data_size(node_addr + node->stru_offset, node);
    
    if(NULL == basic_addr || 0 == data_size)
    {
        *p_size = 0;
        return NULL;
    }
    
    if(1 == only_get_size_flg)
    {
        *p_size = data_size + 2;
    }
    else
    {
        if(0 == node->level) //root_data
        {
          /*#################################################################################
             |   16bit      |    size       |
             |   root_size  |   root_data   |...
            ##################################################################################*/
            p_code_stream[*p_offset] = (data_size & 0xFF00) >> 8;
            p_code_stream[*p_offset + 1] = (data_size & 0x00FF);
            AtcAp_MemCpy(p_code_stream + *p_offset + 2, basic_addr, data_size);
        }
        else //child data
        {
          /*#################################################################################
             | ptr_addr_offset of parent_data | 4bit | 4bit |   8bit   |     size     |
             |          size(16bit)           | level|       offset    |     data     |...
            ##################################################################################*/
            p_code_stream[*p_offset] = node->level << 4;
            p_code_stream[*p_offset] |= (((node->offset + offset_in_node) & 0x0F00) >> 8);
            p_code_stream[*p_offset + 1] = ((node->offset + offset_in_node) & 0x00FF);
            AtcAp_MemCpy(p_code_stream + *p_offset + 2, basic_addr, data_size);
            
            p_parent_node_code_stream_addr[node->offset + offset_in_node] = (data_size & 0xFF00) >> 8;
            p_parent_node_code_stream_addr[node->offset + offset_in_node + 1] = (data_size & 0x00FF);
            p_parent_node_code_stream_addr[node->offset + offset_in_node + 2] = 0;
            p_parent_node_code_stream_addr[node->offset + offset_in_node + 3] = 0;
        }

        *new_node_addr = p_code_stream + *p_offset + 2;
        *p_offset += (data_size + 2);
    }

    return basic_addr;
}

static unsigned short pack_child_node_data(unsigned char* p_code_stream, unsigned short* p_offset, unsigned char* p_parent_node_code_stream_addr, 
                                                 unsigned char* parent_node_addr, pack_node_stru* node, unsigned char only_get_size_flg)
{
    unsigned short      node_size        = 0;
    unsigned short      total_size       = 0;
    unsigned short      node_num,i;
    pack_node_stru      temp_child_node  = { 0 };
    unsigned char*      peer_head_addr;
    unsigned char*      curr_node_code_addr;
    unsigned char*      basic_addr;

    basic_addr = *((unsigned char**)(parent_node_addr + node->offset));
    if(NULL != basic_addr)
    {
        peer_head_addr = pack_curr_node_data(p_code_stream, p_offset, p_parent_node_code_stream_addr, 
                                             parent_node_addr, node, 0, only_get_size_flg, &node_size, &curr_node_code_addr);
        total_size += node_size;

        if(NULL != node->child)
        {
            node_num = get_node_data_num(parent_node_addr, node);
            temp_child_node = *(node->child);
            for(i = 0; i < node_num; i++)
            {
                if(i == 0)
                {
                    total_size += pack_node_data(p_code_stream, p_offset, curr_node_code_addr, basic_addr, &temp_child_node, only_get_size_flg);
                }
                else
                {
                    temp_child_node.stru_offset += node->ext_param;
                    temp_child_node.offset += node->ext_param;
                    total_size += pack_node_data(p_code_stream, p_offset, curr_node_code_addr, peer_head_addr, &temp_child_node, only_get_size_flg);
                }
            }
        }
    }

    //next
    if(NULL != node->next)
    {
        total_size += pack_node_data(p_code_stream, p_offset, p_parent_node_code_stream_addr, 
                                     parent_node_addr, node->next, only_get_size_flg);
    }
    
    return total_size;
}

static unsigned short pack_node_data(unsigned char* p_code_stream, unsigned short* p_offset, unsigned char* p_parent_node_code_stream_addr,
                                         unsigned char* parent_node_addr, pack_node_stru* node, unsigned char only_get_size_flg)
{
    unsigned char*      basic_addr;
    unsigned short      total_size       = 0;
    unsigned short      node_size        = 0;
    pack_node_stru*     next_node;
    unsigned char*      curr_node_code_addr;
    unsigned short      node_num;
    unsigned char       i;

    if(PACK_PARAM_TYPE_PTR_ARRAY == node->param_type || PACK_PARAM_TYPE_PTR64_ARRAY == node->param_type)
    {
        node_num = node->ext_param;
        for(i = 0; i < node_num; i++)
        {
            basic_addr = pack_curr_node_data(p_code_stream, p_offset, p_parent_node_code_stream_addr, 
                                             parent_node_addr, node,
                                             PACK_PARAM_TYPE_PTR_ARRAY == node->param_type ? i * 4 : i * 8,
                                             only_get_size_flg, &node_size, &curr_node_code_addr);
            total_size += node_size;
            
            //child
            if(NULL != basic_addr && NULL != node->child)
            {
                total_size += pack_node_data(p_code_stream, p_offset, curr_node_code_addr, basic_addr, node->child, only_get_size_flg);
            }
        }
    }
    else
    {
        basic_addr = pack_curr_node_data(p_code_stream, p_offset, p_parent_node_code_stream_addr, 
                                         parent_node_addr, node, 0, only_get_size_flg, &node_size, &curr_node_code_addr);
        total_size += node_size;

        //child
        if(NULL != basic_addr && NULL != node->child)
        {
            total_size += pack_node_data(p_code_stream, p_offset, curr_node_code_addr, basic_addr, node->child, only_get_size_flg);
        }
    }

    //next
    next_node = node->next;
    while(NULL != next_node)
    {
        basic_addr = pack_curr_node_data(p_code_stream, p_offset, p_parent_node_code_stream_addr, 
                                         parent_node_addr, next_node, 0, only_get_size_flg, &node_size, &curr_node_code_addr);
        total_size += node_size;

        //child
        if(NULL != basic_addr && NULL != next_node->child)
        {
            total_size += pack_child_node_data(p_code_stream, p_offset, curr_node_code_addr, basic_addr, next_node->child, only_get_size_flg);
        }

        next_node = next_node->next;
    }

    return total_size;
}

unsigned short get_code_stram_len(pack_node_stru* root_node, unsigned char* src_data)
{
    unsigned short pack_size;

   /*##################################################
     |   16bit   |   size    |
     |   size    |   data    |...
    ##################################################*/
    pack_size = 2;
    pack_size += pack_node_data(NULL, NULL, NULL, src_data, root_node, 1);
    
    return pack_size;
}

static void unpack_child_data(unsigned char level, unsigned char* des, unsigned char* data, unsigned short len, unsigned short* p_offset)
{
    unsigned short      offset;
    unsigned short      node_data_len;
    unsigned short      node_offset;
    unsigned char**     p_node_addr  = NULL;
    unsigned char       node_level;

    offset = *p_offset;
    while(offset < len)
    {
        node_level = data[offset] >> 4;
        if(node_level == level)
        {
            if(offset + 2 > len)
            {
                offset = len;
                break;
            }
            
            node_offset = (unsigned short)((data[offset] & 0x0F) << 8) + data[offset + 1];
            node_data_len = (unsigned short)(des[node_offset] << 8) + des[node_offset + 1];
            offset += 2;

            if(offset + node_data_len > len)
            {
                offset = len;
                break;
            }
        
            p_node_addr = (unsigned char**)((unsigned long)des + node_offset);
            *p_node_addr = NULL;
            if(0 != node_data_len)
            {
                *p_node_addr = (unsigned char*)AtcAp_MallocWithClean(node_data_len);
                AtcAp_MemCpy(*p_node_addr, data + offset, node_data_len);
                offset += node_data_len;
            }
        }
        else if(node_level > level)
        {
            unpack_child_data(node_level, *p_node_addr, data, len, &offset);
        }
        else //upper level
        {
            break;
        }
    }

    *p_offset = offset;
}

void clear_pack_node(pack_node_stru* node)
{
    pack_node_stru* temp_next_node, *next_node;

    if(NULL != node->child)
    {
        clear_pack_node(node->child);
    }

    next_node = node->next;
    AtcAp_Free(node);
    
    while(NULL != next_node)
    {
        if(NULL != next_node->child)
        {
            clear_pack_node(next_node->child);
        }
        
        temp_next_node = next_node->next;
        AtcAp_Free(next_node);
  
        next_node = temp_next_node;
    }
}

pack_node_stru* pack_node_register(pack_node_stru** peer_node, unsigned char level, unsigned short offset, unsigned char param_type, unsigned short param, unsigned short ext_param)
{
    pack_node_stru *new_node;

    new_node = (pack_node_stru*)AtcAp_MallocWithClean(sizeof(pack_node_stru));
    new_node->level = level;
    new_node->offset = offset;
    new_node->param_type = param_type;
    new_node->param = param;
    new_node->ext_param = ext_param;
    if(NULL == *peer_node)
    {
        *peer_node = new_node;
    }
    else
    {
        (*peer_node)->next = new_node;
    }
    
    return new_node;
}

unsigned char pack(unsigned char** des, unsigned short* p_len, unsigned char* src_data, unsigned short size, pack_node_stru* root_node)
{
    unsigned short  offset;
    unsigned short  code_stream_len;
    unsigned char*  p_code_stream;

    if(NULL == root_node || PACK_PARAM_TYPE_DATA_SIZE != root_node->param_type || size != root_node->param)
    {
        return PACK_FAIL;
    }

    code_stream_len = get_code_stram_len(root_node, src_data);
    p_code_stream = (unsigned char*)AtcAp_MallocWithClean(code_stream_len);
    //root node
  /*##################################################
     |   16bit   |   size       |    
     |   size    |   data       | ...
    ##################################################*/
    p_code_stream[0] = ((code_stream_len - 2) & 0xFF00) >> 8;
    p_code_stream[1] = ((code_stream_len - 2) & 0x00FF);
    
    offset = 2;
    pack_node_data(p_code_stream, &offset, p_code_stream + 2, src_data, root_node, 0);

    *des = p_code_stream;
    *p_len = code_stream_len;
    
    return PACK_SUCC;
}

unsigned char unpack(unsigned char* des, unsigned short des_len, unsigned char* data, unsigned short len)
{
    unsigned short      offset        = 0;
    unsigned short      data_len;
    unsigned short      root_node_len;

    if(NULL == data || len < 4)
    {
        return PACK_FAIL;
    }
    
    data_len = (((unsigned short)data[0]) << 8) + data[1];
    root_node_len = (((unsigned short)data[2]) << 8) + data[3];
    offset = 4;
    
    if(data_len + 2 != len || 0 == root_node_len || des_len < root_node_len)
    {
        return PACK_FAIL;
    }
    
    AtcAp_MemCpy(des, data + offset, root_node_len);
    offset += root_node_len;

    unpack_child_data(1, des, data, len, &offset);
    
    return PACK_SUCC;
}

