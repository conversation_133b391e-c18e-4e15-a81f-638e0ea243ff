
#include "alsa.h"
#include "alsa_config_5.h"
#include "audio_play.h"
#include "cmsis_os2.h"
#include "ivTTS_al.h"
#include "xy_system.h"

#define AUDIO_PLAY_THREAD_NAME        "audio_play"
#if (MP3_DECODER_TYPE == 1)
#define AUDIO_PLAY_THREAD_STACKSIZE 	(1024 * 24)
#elif (MP3_DECODER_TYPE == 2)
#define AUDIO_PLAY_THREAD_STACKSIZE 	(1024 * 14)
#endif
#define AUDIO_PLAY_THREAD_PRIO 		osPriorityNormal3
#define AUDIO_PLAY_QUEUE_SIZE         1

static osThreadId_t audio_play_thread_id = NULL; 
static osMessageQueueId_t audio_play_req_msg_id = NULL; //request msg
static osMutexId_t audio_play_mutex_lock = NULL;
static osSemaphoreId_t audio_play_pause_sem = NULL;

static audio_sink_t audio_sink_alsa = {
    .open = (audio_sink_open_t)alsa_open,
    .start = (audio_sink_start_t)alsa_start,
    .close = (audio_sink_close_t)alsa_close,
    .write = (audio_sink_write_t)alsa_write,
    .stop = (audio_sink_stop_t)alsa_stop,
};

extern int audio_decode_mp3_process(audio_play_t *audio_play);
extern int audio_decode_amr_process(audio_play_t *audio_play);
extern int audio_decode_wav_process(audio_play_t *audio_play);
static audio_decode_t audio_decode[AUDIO_SOURCE_FORMAT_MAX] = {
#if DECODER_SUPPORT_WAV
    [AUDIO_SOURCE_FORMAT_WAV] = {.process = audio_decode_wav_process},
#endif
#if DECODER_SUPPORT_MP3
    [AUDIO_SOURCE_FORMAT_MP3] = {.process = audio_decode_mp3_process},
#endif
#if DECODER_SUPPORT_AMR
    [AUDIO_SOURCE_FORMAT_AMR] = {.process = audio_decode_amr_process},
#endif
};

static source_arg_t audio_source_arg;
static audio_source_t audio_source;
static audio_play_state_t audio_play_state;
static play_stream_t * play_stream = NULL;

static size_t read_source(void *buf, size_t size, source_arg_t *source_arg);
static int seek_source(uint64_t position, source_arg_t *source_arg);
static void audio_play_state_set(audio_play_state_t state);
static audio_play_state_t audio_play_state_get(void);

static audio_play_t audio_play = {
    .source = &audio_source,
    .state_get = audio_play_state_get,
    .sink = &audio_sink_alsa,
    .sem = &audio_play_pause_sem,
};

static int8_t audio_play_sleep_lock = -1;

static void audio_play_thread(void* arg)
{
	audio_play_req_t req;
    audio_error_t ret = AUDIO_ERR_OK;
    XY_FILE* pfile = NULL;

	while(1)
	{
		osMessageQueueGet(audio_play_req_msg_id, &req, 0, osWaitForever);

        if(audio_play_sleep_lock == -1)
        {
            audio_play_sleep_lock = create_sleep_lock("audio_play");
        }
        
        if(audio_play_sleep_lock != -1)
        {
            sleep_lock(audio_play_sleep_lock, WL_ALL|WL_WFI);
        }
        else
        {
            xy_assert(0);
        }

		switch (req.type)
		{
            case AUDIO_REQ_FILE_PLAY:
            case AUDIO_REQ_BUFFER_PLAY:
            case AUDIO_REQ_STREAM_PLAY:
                if(req.type == AUDIO_REQ_FILE_PLAY)
                {
                    pfile = xy_fopen(req.u.file, "rb");
                    if(pfile == NULL)
                    {
                        ret = AUDIO_ERR_FILE_OPEN;
                        goto exit_play;
                    }

                    audio_source_arg.u.file = pfile;
                    audio_source_arg.type = AUDIO_SOURCE_TYPE_FILE;
                }
                else if(req.type == AUDIO_REQ_BUFFER_PLAY)
                {
                    audio_source_arg.u.buf = req.u.buf;
                    audio_source_arg.source_len = req.len;
                    audio_source_arg.read_len = 0;
                    audio_source_arg.type = AUDIO_SOURCE_TYPE_BUFFER;
                }
                else
                {
                    audio_source_arg.u.stream = req.u.stream;
                    audio_source_arg.source_len = 0;
                    audio_source_arg.read_len = 0;
                    audio_source_arg.type = AUDIO_SOURCE_TYPE_STREAM;
                }
                audio_source.read = read_source;
                audio_source.seek = seek_source;
                audio_source.source_arg = &audio_source_arg;
                audio_source.format = req.format;

                audio_play_state_set(AUDIO_PLAY_STATE_START);

                if(audio_source.format != AUDIO_SOURCE_FORMAT_INVALID)
                {
                    ret = audio_decode[audio_source.format].process(&audio_play);
                }
                else
                {
                    for(uint32_t i = 1; i < AUDIO_SOURCE_FORMAT_MAX; i++)
                    {
                        if(audio_decode[i].process != NULL)
                        {
                            ret = audio_decode[i].process(&audio_play);
                            if(ret != AUDIO_ERR_FORMAT)
                            {
                                break;
                            }
                        }
                    }
                }

            exit_play:
                if(pfile)
                {
                    xy_fclose(pfile);
                    pfile = NULL;
                }

                if(req.type == AUDIO_REQ_STREAM_PLAY)
                {
                    if((req.u.stream)->buffer)
                    {
                        xy_free((req.u.stream)->buffer);
                    }
                    play_stream = NULL;
                }

                xy_free(req.u.buf);

                break;
            case AUDIO_REQ_TTS_PLAY:

                audio_play_state_set(AUDIO_PLAY_STATE_START);
                #if (XY_TTS == 1)
                ret = tts_build_and_play(req.u.buf, req.len, req.cfg);
                #endif
                xy_free(req.u.buf);

                break;

            default:
                break;
		}

        audio_play_state_set(AUDIO_PLAY_STATE_CLOSE);

        if(audio_play_sleep_lock != -1)
        {
            sleep_unlock(audio_play_sleep_lock, WL_ALL|WL_WFI);
        }

        if(req.complete_cb)
        {
            req.complete_cb(ret);
        }
	}
}

void audio_play_init(void)
{
    audio_play_mutex_lock = osMutexNew(NULL);
	xy_assert(audio_play_mutex_lock != NULL);
	audio_play_req_msg_id = osMessageQueueNew(AUDIO_PLAY_QUEUE_SIZE, sizeof(audio_play_req_t), NULL);
	xy_assert(audio_play_req_msg_id != NULL);
    audio_play_pause_sem = osSemaphoreNew(1, 0, NULL);
	
	osThreadAttr_t thread_attr = {0};
	thread_attr.name        = AUDIO_PLAY_THREAD_NAME;
	thread_attr.priority    = AUDIO_PLAY_THREAD_PRIO;
	thread_attr.stack_size  = AUDIO_PLAY_THREAD_STACKSIZE;
	audio_play_thread_id = osThreadNew((osThreadFunc_t)(audio_play_thread), NULL, &thread_attr);
}

static size_t read_source(void *buf, size_t size, source_arg_t *source_arg)
{
    int32_t len = 0;
    static uint8_t stream_discard_flg = 0;

    switch (source_arg->type)
    {
        case AUDIO_SOURCE_TYPE_FILE:
            len = xy_fread(buf, size, 1, source_arg->u.file);
            xy_assert(len >= 0);
            break;
        case AUDIO_SOURCE_TYPE_BUFFER:
            len = size > source_arg->source_len - source_arg->read_len ? source_arg->source_len - source_arg->read_len : size;
            if(len > 0)
            {
                memcpy(buf, source_arg->u.buf + source_arg->read_len, len);
                source_arg->read_len += len;
            }
            break;
        case AUDIO_SOURCE_TYPE_STREAM:
            // stream 会多线程操作，纯内存操作执行很快，用临界区保护
            osCoreEnterCritical ();
            if(source_arg->read_len >= AUDIO_PLAY_STREAM_BUFFER_SIZE/2)
            {
                if(stream_discard_flg == 0)
                {
                    stream_discard_flg = 1;
                    // 丢弃前面旧数据
                    kfifo_discard(source_arg->u.stream, source_arg->read_len);
                }
                len = kfifo_get(source_arg->u.stream, buf, size);
            }
            else
            {
                stream_discard_flg = 0;
                len = kfifo_peek_to_buf_skip(source_arg->u.stream, buf, size, source_arg->read_len);
            }
            osCoreExitCritical ();
            
            source_arg->read_len += len;

            break;
        default:
            xy_assert(0);
            break;
    }

    return (size_t)len;
}

static int seek_source(uint64_t position, source_arg_t *source_arg)
{
    int32_t ret;

    switch (source_arg->type)
    {
        case AUDIO_SOURCE_TYPE_FILE:
            ret = xy_fseek(source_arg->u.file, (int32_t) position, 0);
            xy_assert(ret >= 0);
            break;
        case AUDIO_SOURCE_TYPE_BUFFER:
            xy_assert(source_arg->source_len >= position);
            source_arg->read_len = (uint32_t)position;
            break;
        case AUDIO_SOURCE_TYPE_STREAM:
            // 数据流的前AUDIO_PLAY_STREAM_BUFFER_SIZE/2才支持seek，
            // 所以AUDIO_PLAY_STREAM_BUFFER_SIZE要设置的足够大，保证解码时seek有效
            xy_assert((AUDIO_PLAY_STREAM_BUFFER_SIZE/2 >= position) && (AUDIO_PLAY_STREAM_BUFFER_SIZE/2 >= source_arg->read_len));  
            source_arg->read_len = (uint32_t)position;
            break;
        default:
            xy_assert(0);
            break;
    }

    return 0;
}

void audio_play_req(audio_play_req_t * req)
{
    uint32_t len;
    audio_play_req_t req_copy;
    memcpy(&req_copy, req, sizeof(audio_play_req_t));
    switch (req->type)
    {
        case AUDIO_REQ_FILE_PLAY:
            len = strlen(req->u.file) + 1;
            req_copy.u.file = xy_malloc(len);
            memcpy(req_copy.u.file, req->u.file, len);
            req_copy.u.file[len - 1] = '\0';
            break;
        case AUDIO_REQ_BUFFER_PLAY:
        case AUDIO_REQ_TTS_PLAY:
            req_copy.u.buf = xy_malloc(req->len);
            memcpy(req_copy.u.buf, req->u.buf, req->len);
            break;
        case AUDIO_REQ_STREAM_PLAY:
            xy_assert(play_stream == NULL);
            play_stream =  xy_malloc(sizeof(play_stream_t));
            // 此处开始建立stream，无多线程问题，无需保护
            kfifo_init(play_stream, xy_malloc(AUDIO_PLAY_STREAM_BUFFER_SIZE), AUDIO_PLAY_STREAM_BUFFER_SIZE);
            len = kfifo_put(play_stream, req_copy.u.buf, req->len);
            xy_assert(len == req->len);
            req_copy.u.stream = play_stream;

            break;
        default:
            xy_assert(0);
            break;
    }

	osMessageQueuePut(audio_play_req_msg_id, &req_copy, 0, osWaitForever);
}

uint32_t audio_play_stream_put(uint8_t * buf, uint32_t len)
{
    uint32_t ret = 0;

    if(play_stream == NULL)
    {
        return 0;
    }
    // stream 会多线程操作，纯内存操作执行很快，用临界区保护
    osCoreEnterCritical ();
    ret = kfifo_put(play_stream, buf, len);
    osCoreExitCritical ();

    return ret;
}

static void audio_play_state_set(audio_play_state_t state)
{
    osMutexAcquire(audio_play_mutex_lock, osWaitForever);
    audio_play_state = state;
    osMutexRelease(audio_play_mutex_lock);
}

static audio_play_state_t audio_play_state_get(void)
{
    audio_play_state_t state;
    osMutexAcquire(audio_play_mutex_lock, osWaitForever);
    state = audio_play_state;
    osMutexRelease(audio_play_mutex_lock);

    return state;
}

void audio_play_stop(void)
{
    osMutexAcquire(audio_play_mutex_lock, osWaitForever);
    if(audio_play_state == AUDIO_PLAY_STATE_START)
    {
        audio_play_state = AUDIO_PLAY_STATE_STOP;
    }
    osMutexRelease(audio_play_mutex_lock);
}

void audio_play_pause(void)
{
    osMutexAcquire(audio_play_mutex_lock, osWaitForever);
    if(audio_play_state == AUDIO_PLAY_STATE_START)
    {
        audio_play_state = AUDIO_PLAY_STATE_PAUSE;
    }
    osMutexRelease(audio_play_mutex_lock);
}

void audio_play_resume(void)
{
    osMutexAcquire(audio_play_mutex_lock, osWaitForever);
    if(audio_play_state == AUDIO_PLAY_STATE_PAUSE)
    {
        audio_play_state = AUDIO_PLAY_STATE_START;
        osSemaphoreRelease (audio_play_pause_sem);
    }
    osMutexRelease(audio_play_mutex_lock);
}
