#ifndef __ALSA_CAPTURE_CONFIG_H__
#define __ALSA_CAPTURE_CONFIG_H__

#include "alsa_config_5.h"

// #ifdef ALSA_SUPPORT_CAPTURE

#include "alsa_supported.h"

//#define ALSA_CAPTURE_SUPPORT_MULTI_INSTANCE
#ifdef ALSA_CAPTURE_SUPPORT_MULTI_INSTANCE
#define ALSA_CAPTURE_INSTANCE_COUNT              3
#else
#define ALSA_CAPTURE_INSTANCE_COUNT              1
#endif

//#define ALSA_CAPTURE_PARAMETER_USER_SPECIFIED
#ifndef ALSA_CAPTURE_PARAMETER_USER_SPECIFIED
#define ALSA_CAPTURE_PARAMETER_FIXED
#endif

#ifdef ALSA_CAPTURE_PARAMETER_FIXED
#define ALSA_CAPTURE_SAMPLE_RATE                 ALSA_SUPPORTED_SAMPLE_RATE_8000
#define ALSA_CAPTURE_CHANNEL_NUM                 ALSA_SUPPORTED_CHANNEL_NUM_2
#define ALSA_CAPTURE_BITS                        ALSA_SUPPORTED_BITS_16
#define ALSA_CAPTURE_MAP                         (ALSA_SUPPORTED_CHMAP_CH0 | ALSA_SUPPORTED_CHMAP_CH1)
#define ALSA_CAPTURE_QUEUE_SAMPLE_RATE           ALSA_CAPTURE_SAMPLE_RATE
#define ALSA_CAPTURE_QUEUE_CHANNEL_NUM           ALSA_CAPTURE_CHANNEL_NUM
#define ALSA_CAPTURE_QUEUE_BITS                  ALSA_CAPTURE_BITS
#else
#if defined(ALSA_CAPTURE_SUPPORT_MULTI_INSTANCE)
#error "ALSA_CAPTURE_PARAMETER_USER_SPECIFIED conflict with ALSA_CAPTURE_SUPPORT_MULTI_INSTANCE"
#endif
#endif

#define ALSA_CAPTURE_DMA_SLOT_MS                 100
#ifdef ALSA_CAPTURE_PARAMETER_FIXED
#define ALSA_CAPTURE_DMA_ONE_SAMPLE_SIZE         (ALSA_CAPTURE_BITS / 8)
#define ALSA_CAPTURE_DMA_ONE_MS_PCM_SIZE         (ALSA_CAPTURE_SAMPLE_RATE / 1000 *   \
                                                  ALSA_CAPTURE_DMA_ONE_SAMPLE_SIZE * \
                                                  ALSA_CAPTURE_CHANNEL_NUM)
#define ALSA_CAPTURE_DMA_BUFFER_SIZE             (ALSA_CAPTURE_DMA_ONE_MS_PCM_SIZE * ALSA_CAPTURE_DMA_SLOT_MS * 2)
#else
#define ALSA_CAPTURE_DMA_BUFFER_SIZE             24576
#endif

#ifdef ALSA_CAPTURE_PARAMETER_FIXED
#define ALSA_CAPTURE_QUEUE_TIME_MS               (ALSA_CAPTURE_DMA_SLOT_MS * 2 * 2)
#define ALSA_CAPTURE_QUEUE_ONE_SAMPLE_SIZE       (ALSA_CAPTURE_QUEUE_BITS / 8)
#define ALSA_CAPTURE_QUEUE_ONE_MS_PCM_SIZE       (ALSA_CAPTURE_QUEUE_SAMPLE_RATE / 1000 * \
                                                  ALSA_CAPTURE_QUEUE_ONE_SAMPLE_SIZE *    \
                                                  ALSA_CAPTURE_QUEUE_CHANNEL_NUM)
#define ALSA_CAPTURE_QUEUE_SIZE                  (ALSA_CAPTURE_QUEUE_ONE_MS_PCM_SIZE * ALSA_CAPTURE_QUEUE_TIME_MS)
#else
#define ALSA_CAPTURE_QUEUE_SIZE                  (ALSA_CAPTURE_DMA_BUFFER_SIZE * 4)
#endif

// #endif
#endif