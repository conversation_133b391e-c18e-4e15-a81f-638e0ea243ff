/** 
* @file     at_passthrough.h
* @brief    Interfaces related to the passthrough mode of AT channels
* @warning  Supports parallel processing of multiple AT channel passthroughs.
            Supports multiple passthrough-related services on the same AT physical channel, but the passthrough framework only retains the latest passthrough service information. 
            Special commands like ATO/+++ only apply to the latest passthrough service. Other services calling atEnterCommandState or atEnterOnlineCommandState will return XY_Err_InProgress.
            In this case, passthrough configuration/restoration/suspension/exit operations should not be performed. You may call atEnterPassthroughState again to reoccupy the AT passthrough channel by specifying the passthrough service state.
* @note     Pay attention to the return values of passthrough interfaces. If a passthrough operation fails, it is recommended to call atEnterPassthroughState again to re-execute the passthrough entry process.
* @example  Below are some common passthrough workflows:

  Example 1: PPP dial-up internet:
  1. Enter passthrough, register callbacks for input data, +++, ATO, and DTR events, atEnterPassthroughState --> 
  2. Process passthrough data --> 
  3. Upon receiving a DTR1 pin signal or +++ string, switch the AT channel state from online data state to online command state, atEnterOnlineCommandState --> 
  4. Restore passthrough with the ATO command. The background executes the passthEventCb callback to resume the online data state for continued passthrough data processing, atEnterPassthroughState -->
  5. Upon receiving a DTR2 pin signal, exit passthrough by calling atEnterCommandState to disable passthrough functionality. Cannot be restored; must call atEnterPassthroughState again to re-enter passthrough.

  Example 2: FTP file upload
  1. Enter passthrough, register callbacks for input data, +++, and DTR events, atEnterPassthroughState -->
  3. Process passthrough data --> 
  4. Upon receiving a DTR1 pin signal or +++ string, exit passthrough by calling atEnterCommandState to disable passthrough functionality. Cannot be restored; must call atEnterPassthroughState again to re-enter passthrough.

  Example 3: Socket fixed-length data passthrough
  1. Enter passthrough. Fixed-length mode does not require setting passthrough switching mode, atEnterPassthroughState --> 
  2. Process passthrough data --> 
  3. Exit passthrough by calling atEnterCommandState to leave the passthrough state. Cannot be restored; must call atEnterPassthroughState again to re-enter passthrough.
*/
#pragma once

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include "cmsis_os2.h"


/*******************************************************************************
 *                             Macro definitions                               *
 ******************************************************************************/
#define PASSTH_THD_PROI            osPriorityNormal3
#define PASSTH_THD_STACKSIZE       0x1000
#define PASSTH_THD_NAME            "psthd"
#define PASSTH_QUEUE_SIZE          50
#define PASSTH_TIMER_DURATION      500
#define PASSTH_TIMER_NAME          "psthm"

#define PASSTH_CTRLZ               0x1A
#define PASSTH_ESC                 0x1B
#define PASSTH_QUIT_SYMBOL         "+++"

/* Passthrough prompts */
#define PASSTH_RSP_OK              "\r\nOK\r\n"
#define PASSTH_RSP_ERR             "\r\nERROR\r\n"
#define PASSTH_RSP_CONN            "\r\nCONNECT\r\n"
#define PASSTH_RSP_NOCARR          "\r\nNO CARRIER\r\n"
#define PASSTH_RSP_SEND_OK         "\r\nSEND OK\r\n"
#define PASSTH_RSP_SEND_FAIL       "\r\nSEND FAIL\r\n"
#define PASSTH_RSP_GRETHAN_PROM    "\r\n>\r\n"
#define PASSTH_RSP_GRETHAN_PROM1   "\r\n> "


/*******************************************************************************
 *                             Type definitions                                *
 ******************************************************************************/
#define  AT_STATE_IS_COMMAND(passthInfo)                     ((passthInfo)->channState == AT_STATE_COMMAND)
#define  AT_STATE_IS_PASSTHROUGH(passthInfo)                 ((passthInfo)->channState > AT_STATE_ONLINE_DATA && (passthInfo)->channState < AT_STATE_STATE_MAX)
#define  AT_STATE_IS_ONLINE_COMMAND(passthInfo)              ((passthInfo)->channState == AT_STATE_ONLINE_COMMAND)
#define  PASSTH_STATE_IS_CHANGED(passthInfo, passthState)    ((passthInfo)->PrevPassthState != AT_STATE_STATE_MAX && (passthInfo)->PrevPassthState != (passthState))

/**
 * @brief  Declaration of the user-implemented passthrough preprocessing callback function. Blocking operations (e.g., network data interaction) are not allowed within this function, as they may affect passthrough data reception and processing.
 * @param   atHandle     [IN] AT channel handle in passthrough state
 * @param   channCurState[IN] Current AT channel state in passthrough, @see @ref atchannState_e
 * @param   eventId      [IN] Event type requiring business processing, @see @ref PASSTH_EVENTID_E
 * @param   arg          [IN] Event-related parameters requiring business processing, e.g., parameter information carried by PASSTH_EVENT_DATA_INPUT event, @see @ref atPassthDataArg_t
 * @return XY_OK: Preprocessing successful. XY_ERR: Preprocessing failed, exit passthrough.
 * @note Users can perform preprocessing operations in this interface, such as pre-opening files.
 */
typedef int (*passthEventCb)(int atHandle, uint8_t channCurState, int eventId, void *arg);


/**
  * @brief  AT channel states in passthrough mode
  */
typedef enum
{
    /* Default state of the AT channel in passthrough. AT commands can be normally received and processed in this state. Passthrough data state cannot be restored. Passthrough-related configurations must be cleared. Calling atEnterCommandState enters this state. */
    AT_STATE_COMMAND = 0,
    /* AT channel online command state in passthrough. AT commands can be normally received and processed in this state. Passthrough data state can be restored. Passthrough-related configurations must be backed up. Calling atEnterOnlineCommandState enters this state. */
    AT_STATE_ONLINE_COMMAND,
    /* Starting state of AT channel online data state in passthrough. This state is not allowed for business use. */
    AT_STATE_ONLINE_DATA,

    /**********************************************************************************************************************************************************************
     *  The following states are all AT channel data passthrough states. New businesses should add a member with a unique business identifier in the corresponding module to ensure each business's passthrough data state is unique. Call atEnterPassthroughState to enter the specified business passthrough state.  *
     **********************************************************************************************************************************************************************/
    /* Socket business passthrough state */
    DEF_SOCKET_PASSTH_STATE,
    CMCC_SOCKET_PASSTH_STATE,
    MN316_SOCKET_PASSTH_STATE,

    /* PPP dial-up passthrough state */
    DEF_PPP_PASSTH_STATE,

    /* FS business passthrough state */
    DEF_FS_PASSTH_STATE,
    CMCC_FS_PASSTH_STATE,

    /* SSL business passthrough state */
    DEF_SSL_PASSTH_STATE,
    CMCC_SSL_PASSTH_STATE,

    /* FTP business passthrough state */
    DEF_FTP_PASSTH_STATE,
    CMCC_FTP_PASSTH_STATE,

    /* MQTT business passthrough state */
    DEF_MQTT_PASSTH_STATE,
    CMCC_MQTT_PASSTH_STATE,
    CUCC_MQTT_PASSTH_STATE,
    CTCC_MQTT_PASSTH_STATE,

    /* HTTP business passthrough state */
    DEF_HTTP_PASSTH_STATE,
    CMCC_HTTP_PASSTH_STATE,

    /* FOTA business passthrough state  */
    DEF_FOTA_PASSTH_STATE,
    CMCC_FOTA_PASSTH_STATE,

    /* SMS business passthrough state */
    DEF_SMS_PASSTH_STATE,


    GNSS_DL_PASSTH_STATE,      /*Externally injected GNSS version stream or ephemeris stream*/

    AT_STATE_STATE_MAX = 0xFF
} atchannState_e;

typedef enum
{
    PASSTH_EVENT_UNKNOWN       = 0,          /* All operations below are unsupported; default value */
    PASSTH_EVENT_DATA_INPUT    = 1 << 0,     /* AT channel input data processing in passthrough. eventCb carries parameter information, @see @ref atPassthDataArg_t */
    PASSTH_EVENT_TRIPLE_PLUS   = 1 << 1,     /* Supports recognition of special characters "+++". eventCb does not carry parameter information. */
    PASSTH_EVENT_ATO           = 1 << 2,     /* Supports ATO command. eventCb does not carry parameter information. */
    PASSTH_EVENT_DTR1          = 1 << 3,     /* Supports AT&D1 command. DTR level changes from low to high. eventCb does not carry parameter information. */
    PASSTH_EVENT_DTR2          = 1 << 4,     /* Supports AT&D2 command. DTR level changes from low to high. eventCb does not carry parameter information. */
    PASSTH_EVENT_ALL           = PASSTH_EVENT_DATA_INPUT | PASSTH_EVENT_TRIPLE_PLUS | PASSTH_EVENT_ATO | PASSTH_EVENT_DTR1 | PASSTH_EVENT_DTR2,
} atPassthEventId_e;

typedef struct
{
#if PASSTH_TIMER_DURATION
    osTimerId_t               timerId;          /* Soft timer ID, used to detect whether a complete special character (e.g., "+++") is matched within one second in data mode */
    uint32_t                  recvTime;         /* Records the most recent data reception time point of the current AT channel in passthrough */
#endif /* PASSTH_TIMER_DURATION */
    char                      *matchChar;       /* Special string to be matched (e.g., '+++') */
    uint8_t                   matchNum;         /* Records the number of matched special characters (e.g., '+') */
    uint8_t                   padding;
} atPassthMatchChar_t;

/* For multiple passthrough services on the same AT channel, only the latest passthrough service information is recorded. */
typedef struct
{
    uint8_t                   channState;       /* Indicates the current state of the AT channel in passthrough, @see @ref atchannState_e */
    uint8_t                   PrevPassthState;
    uint16_t                  events;           /* Indicates whether the current passthrough service supports special actions like DTR level/"ATO" to switch between passthrough data mode and command mode, @see @ref atPassthEventId_e */
    passthEventCb             eventCb;          /* Event handling callback */
    atPassthMatchChar_t       *matchPlus;       /* Records matching information for the special character '+'. When a complete "+++" is matched, the service will be notified to handle the PASSTH_EVENT_TRIPLE_PLUS event. */
} atPassthInfo_t;

/* Parameter information carried by the PASSTH_EVENT_DATA_INPUT event */
typedef struct
{
    void                      *pData;
    uint32_t                  dataSize;
} atPassthDataArg_t;


/*******************************************************************************
 *                       Global function declarations                          *
 ******************************************************************************/
/**
 * @brief   Passthrough resource initialization
 * @warning This interface is temporarily not open for business use and is implemented internally by XY.
 */
void atPassthInit(void);

/**
 * @brief   Clear AT channel passthrough information
 * @warning This interface is temporarily not open for business use and is implemented internally by XY.
 */
void atPassthInfoClear(atPassthInfo_t *info);

/**
 * @brief   ATO restores the latest recorded passthrough service of the AT channel
 * @param   atHandle   [IN] AT channel handle in passthrough state
 * @warning This interface is temporarily not open for business use and is implemented internally by XY.
 */
int atPassthStateResumeByATO(int atHandle);

/**
 * @brief   Process passthrough data stream received from the serial port
 * @param   atHandle   [IN] AT channel handle in passthrough state
 * @param   pData      [IN] Passthrough data stream
 * @param   datasize   [IN] Length of passthrough data stream
 * @note    Since the UART receiving thread has a small stack and cannot be blocked for long, passthrough data must be dispatched to a business-defined data processing thread after reception.
 * @warning This interface is temporarily not open for business use and is implemented internally by XY.
 */
int atPassthStateDataInput(int atHandle, char *pData, uint32_t datasize);

/**
 * @brief  Clear passthrough-related context, exit passthrough mode, and switch back to AT command mode. This interface is called after completing passthrough data transmission or upon reception timeout.
 * @param  atHandle   [IN] AT channel handle in passthrough state
 * @param  passthState[IN] Unique passthrough business state identifier for the user
 * @return Operation result code, @see @ref XY_PLAT_ERR_E 
 * @note   Users can define exit conditions and call this interface when matched in their business processes.
 * @note   If multiple passthrough-related services are running on the same AT channel, the passthrough framework only retains the latest passthrough service information. Special commands like ATO/+++ only apply to the latest passthrough service.
 *         Other services calling this interface will return XY_Err_InProgress. In this case, other services should not perform passthrough restoration/suspension operations and may call atEnterPassthroughState again to reoccupy the AT passthrough channel.
 */
int atEnterCommandState(int atHandle, atchannState_e passthState);

/**
 * @brief   Enter passthrough state, allowing special services to register passthrough event callbacks. Related callback functions are executed by the XY platform in the background.
 * @param   atHandle   [IN] AT channel handle in passthrough state
 * @param   passthState[IN] Unique passthrough business state identifier for the user
 * @param   events     [IN] Set of event types the user wants to monitor, @see @ref PASSTH_EVENTID_E
 * @param   eventCb    [IN] User-implemented passthrough event handling callback function for receiving and processing passthrough events. Blocking operations (e.g., network data interaction) are not allowed in eventCb, as they may affect passthrough data reception and processing.
 * @return  Operation result code, @see @ref XY_PLAT_ERR_E
 * @note    Supports parallel processing of passthrough modes on multiple AT channels!
 *          For multiple services entering passthrough, only the latest passthrough service information is recorded per AT channel. Historical services cannot be automatically restored and must call this interface again!
 * @warning This function creates a new passthrough thread to handle TTY-transmitted passthrough data, used for scenarios where passthrough data processing is time-consuming or has deep stack calls, such as socket passthrough.
 */
int atEnterPassthroughState(int atHandle, atchannState_e passthState, int events, passthEventCb eventCb);

/**
 * @brief   Save passthrough-related context, switch from passthrough mode to AT command mode (corresponding to AT_STATE_ONLINE_COMMAND state). This interface is called upon recognizing special characters (e.g., "+++"). Commands like "ATO" can be used to restore the latest passthrough service state.
 * @param   atHandle   [IN] AT channel handle in passthrough state
 * @param   passthState[IN] Unique passthrough business state identifier for the user
 * @param   events     [IN] Set of event types the user wants to monitor, @see @ref PASSTH_EVENTID_E
 * @param   eventCb    [IN] User-implemented passthrough event handling callback function for receiving and processing passthrough events. Blocking operations (e.g., network data interaction) are not allowed in eventCb, as they may affect passthrough data reception and processing.
 * @return  Operation result code, @see @ref XY_PLAT_ERR_E
 * @note    Generally used in conjunction with the "ATO" command!
 * @warning This function creates a new passthrough thread to handle TTY-transmitted passthrough data, used for scenarios where passthrough data processing is time-consuming or has deep stack calls, such as socket passthrough.
 */
int atEnterOnlineCommandState(int atHandle, atchannState_e passthState, int events, passthEventCb eventCb);