OpenCORE Adaptive Multi Rate (AMR) speech codec library implementation.

This library contains an implementation of the 3GPP TS 26.073 specification for
the Adaptive Multi Rate (AMR) speech codec and an implementation for the
3GPP TS 26.173 specification for the Adaptive Multi-Rate - Wideband (AMR-WB)
speech decoder. The implementation is derived from the OpenCORE framework, part
of the Google Android project.

This library is Licensed under the Apache License, Version 2.0. A copy me be
found in the file 'LICENSE' and at
http://www.apache.org/licenses/LICENSE-2.0.html
