#include "atc_ps.h"
#include "oss_nv.h"
#include "xy_lpm.h"

extern void SendAtInd2User(char *pAt, unsigned int ulAtLen, int ttyFd);

#define D_ATC_STAT_UNREAD         0
#define D_ATC_STAT_READ           1
#define D_ATC_STAT_UNSENT         2
#define D_ATC_STAT_SENT           3

#define TP_MTI_SMS_DELIVER               0
#define TP_MTI_SMS_SUBMIT                1
#define TP_MTI_SMS_STATUS_REPORT         2
#define TP_MTI_SMS_COMMMAND              2

static void parse_Hex2Str(unsigned char* pDes, unsigned char* pSrc, uint16_t usSrcLen)
{
    int  i;
    char h1,h2;
 
    for(i = 0; i < usSrcLen; i++)
    {
        h1 = pSrc[i] / 16 + '0';
        h2 = pSrc[i] % 16 + '0';

        if(h1 > '9') { h1 += 7; }
        if(h2 > '9') { h2 += 7; }

        pDes[2*i] = h1;
        pDes[2*i + 1] = h2;
    }
}

unsigned char AtcAp_ConvertGSM2UCS2(unsigned char* pUcs2,unsigned char* pGsm,unsigned short usGsmLen)
{
    unsigned short i=0;
    unsigned char ucRet = ATC_AP_TRUE;
    unsigned char ucHex[3] = {0};

    for (i = 0; i < usGsmLen; i++)
    {
        sprintf(ucHex,"%02X",pGsm[i]);
        pUcs2[4*i+0] = 0x30;
        pUcs2[4*i+1] = 0x30;
        pUcs2[4*i+2] = ucHex[0];
        pUcs2[4*i+3] = ucHex[1];
    }
    return ucRet;
}

unsigned char AtcAp_ConvertGSM2HEX(unsigned char* pHex,unsigned char* pGsm,unsigned short usGsmLen)
{
    unsigned short i=0;
    unsigned char ucRet = ATC_AP_TRUE;
    unsigned char ucHex[3] = {0};

    for (i = 0; i < usGsmLen; i++)
    {
        sprintf(ucHex,"%02X",pGsm[i]);
        pHex[2*i+0] = ucHex[0];
        pHex[2*i+1] = ucHex[1];
    }
    return ucRet;
}

void AtcAp_Tpdu_DecodeScts(unsigned char* pInput,unsigned char InputLen,char* pOutput)
{
    unsigned char ucRaLen = 0;

    if((pInput[0] & 0x03) == 1)//submit report
    {
        if(InputLen >= 9)
        {
            AtcAp_TpduToScts(&pInput[2],pOutput);
        }
     }
    else if((pInput[0] & 0x03) == 2 )//status report
    {
        if(InputLen >= 3)
        {
            ucRaLen = (pInput[2]+1)/2;
        }
        if(InputLen >= 3+1+ucRaLen+7)
        {
            AtcAp_TpduToScts(&pInput[3+1+ucRaLen],pOutput);
        }
    }
    else
    {
        pOutput = NULL;
    }   
}
const ST_ATC_AP_STR_TABLE ATC_NConfig_Table[D_ATC_NCONFIG_MAX] = 
{
    {   D_ATC_NCONFIG_AUTOCONNECT,                      "AUTOCONNECT"                   },
    {   D_ATC_NCONFIG_CELL_RESELECTION,                 "CELL_RESELECTION"              },
    {   D_ATC_NCONFIG_ENABLE_BIP,                       "ENABLE_BIP"                    },
    {   D_ATC_NCONFIG_BARRING_RELEASE_DELAY,            "BARRING_RELEASE_DELAY"         },
    {   D_ATC_NCONFIG_RELEASE_VERSION,                  "RELEASE_VERSION"               },
    {   D_ATC_NCONFIG_SYNC_TIME_PERIOD,                 "SYNC_TIME_PERIOD"              },
    {   D_ATC_NCONFIG_PCO_IE_TYPE,                      "PCO_IE_TYPE"                   },
    {   D_ATC_NCONFIG_NON_IP_NO_SMS_ENABLE,             "NON_IP_NO_SMS_ENABLE"          },
    {   D_ATC_NCONFIG_T3324_T3412_EXT_CHANGE_REPORT,    "T3324_T3412_EXT_CHANGE_REPORT" },
};

const ST_ATC_AP_STR_TABLE ATC_UeConfig_Table[D_ATC_UECONFIG_MAX] = 
{
    {   D_ATC_UECONFIG_DEFAPN,                          "defapn"              },
    {   D_ATC_UECONFIG_AUTOCONN,                        "autoconn"            },
    {   D_ATC_UECONFIG_RELVER,                          "relver"              },
    {   D_ATC_UECONFIG_REESTABLISH,                     "reestablish"         },
    {   D_ATC_UECONFIG_EMULTICAR,                       "emulticar"           },
};

const ST_ATC_AP_STR_TABLE ATC_Qcfg_Table[D_ATC_QCFG_MAX] = 
{
    {   D_ATC_QCFG_GPRSATTACH,                          "gprsattach"            },
    {   D_ATC_QCFG_NWSCANMODE,                          "nwscanmode"            },
    {   D_ATC_QCFG_NWSCANSEQ,                           "nwscanseq"             },
    {   D_ATC_QCFG_ROAMSERVICE,                         "roamservice"           },
    {   D_ATC_QCFG_SERVICEDOMAIN,                       "servicedomain"         },
    {   D_ATC_QCFG_BAND,                                "band"                  },
    {   D_ATC_QCFG_IOTOPMODE,                           "iotopmode"             },//for BG95
    {   D_ATC_QCFG_CREG_EMERGENCY,                      "creg/emergency"        },
    {   D_ATC_QCFG_NW_QPTMZ,                            "nw/optmz"              },
    {   D_ATC_QCFG_PDP_RETRYTIMES,                      "pdp/retrytimes"        },
};

void AtcAp_MsgProc_AT_CMD_RST(unsigned char* pRecvMsg)
{
    ST_ATC_AP_CMD_RST* pCmdRst;
    ST_ATC_AP_CMD_RST tCmsRst = { 0 };
    
    pCmdRst = &tCmsRst;
    AtcAp_MemCpy((unsigned char*)pCmdRst, pRecvMsg, sizeof(ST_ATC_AP_CMD_RST));

    if(ATC_OK == pCmdRst->ucResult)
    {
        AtcAp_SendOkRsp();
    }
    else
    {
        if(ATC_CMD_TYPE_CMEE == pCmdRst->ucCmdType)
        {
            AtcAp_SendCmeeErr(pCmdRst->usErrCode);
        }
        else
        {
            AtcAp_SendCmsErr(pCmdRst->usErrCode);
        }
    }
}

void AtcAp_MsgProc_CGSN_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CGSN_CNF_STRU* pCgsnCnf;
    unsigned char*         pucData;

    pCgsnCnf = (ATC_MSG_CGSN_CNF_STRU*)pRecvMsg;

    if(0 == pCgsnCnf->ucLen)
    {
        return;
    }

    pucData = AtcAp_Malloc(pCgsnCnf->ucLen + 1);
    AtcAp_MemCpy(pucData, pCgsnCnf->aucData, pCgsnCnf->ucLen);
    
    if (0 == pCgsnCnf->snt)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s\r\n", pucData);
    }
    else if (1 == pCgsnCnf->snt)
    {
#if ((VER_QUEC) && (!USR_CUSTOM2)) || (USR_CUSTOM6) || (USR_CUSTOM12)
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s\r\n", pucData);
#else
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CGSN:%s\r\n", pucData);
#endif
    }
    else 
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CGSN:%s\r\n", pucData);
    }
    AtcAp_SendDataInd(pRecvMsg);
    AtcAp_Free(pucData);
}

void AtcAp_MsgProc_CEREG_R_Cnf(unsigned char* pRecvMsg)
{
    unsigned char             i;
    unsigned char             ucTemp    = 0;
    unsigned char             aucStr[9] = {0};
    ATC_MSG_CEREG_R_CNF_STRU* pCeregRCnf;

    pCeregRCnf = (ATC_MSG_CEREG_R_CNF_STRU*)pRecvMsg;

    //<n>,<stat>
    if(D_ATC_EVENT_CEREG_R == pCeregRCnf->usEvent)
    {
#if USR_CUSTOM6
        if(4 == pCeregRCnf->tRegisterState.ucEPSRegStatus)
        {
            pCeregRCnf->tRegisterState.ucEPSRegStatus = 0;
        }
#endif
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CEREG:%d,%d", pCeregRCnf->tRegisterState.ucIndPara, pCeregRCnf->tRegisterState.ucEPSRegStatus);
#if USR_CUSTOM9
        if(pCeregRCnf->tRegisterState.ucEPSRegStatus == 0 || pCeregRCnf->tRegisterState.ucEPSRegStatus == 2)
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), (const unsigned char *)"\r\n");
            AtcAp_SendDataInd(pRecvMsg);
            return;
        }
#endif
#if USR_CUSTOM6
        if(pCeregRCnf->tRegisterState.ucEPSRegStatus == 0 || pCeregRCnf->tRegisterState.ucEPSRegStatus == 3)
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), (const unsigned char *)"\r\n");
            AtcAp_SendDataInd(pRecvMsg);
            return;
        }
#endif
    }
    else if(D_ATC_EVENT_CGREG_R == pCeregRCnf->usEvent)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CGREG:%d,%d", pCeregRCnf->tRegisterState.ucIndPara, pCeregRCnf->tRegisterState.ucEPSRegStatus);
    }
    else //CREG_R
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CREG:%d,%d", pCeregRCnf->tRegisterState.ucIndPara, pCeregRCnf->tRegisterState.ucEPSRegStatus);
    }
    
    if (1 < pCeregRCnf->tRegisterState.ucIndPara)
    {
        //[<tac>]two byte tracking area code in hexadecimal format 
        if (pCeregRCnf->tRegisterState.OP_TacLac)
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = 4;
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",\"%X\"", pCeregRCnf->tRegisterState.usTacOrLac);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",");
        }
        //[<ci>] four byte E-UTRAN cell ID in hexadecimal format
        if (pCeregRCnf->tRegisterState.OP_CellId)
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = 8;
#if VER_CM || USR_CUSTOM9 || USR_CUSTOM11
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",\"%08X\"", pCeregRCnf->tRegisterState.ulCellId);
#else
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",\"%07X\"", pCeregRCnf->tRegisterState.ulCellId);
#endif
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",");
        }

        //[<Act>]
        AtcAp_WriteIntPara_M(pCeregRCnf->tRegisterState.OP_Act, pCeregRCnf->tRegisterState.ucAct, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    }
    if ((3 == pCeregRCnf->tRegisterState.ucIndPara)
        &&pCeregRCnf->tRegisterState.OP_Act)
    {
        //[<cause_type>]
        AtcAp_WriteIntPara(pCeregRCnf->tRegisterState.OP_CauseType, pCeregRCnf->tRegisterState.ucCauseType );
        //[<reject_cause>]
        AtcAp_WriteIntPara(pCeregRCnf->tRegisterState.OP_RejectCause, pCeregRCnf->tRegisterState.ucRejectCause );
    }

    if (3 < pCeregRCnf->tRegisterState.ucIndPara)
    {    
        //[<cause_type>]
        AtcAp_WriteIntPara_M(pCeregRCnf->tRegisterState.OP_CauseType, pCeregRCnf->tRegisterState.ucCauseType, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
        //[<reject_cause>]
        AtcAp_WriteIntPara_M(pCeregRCnf->tRegisterState.OP_RejectCause, pCeregRCnf->tRegisterState.ucRejectCause, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);

        //<Active-Time>
        if (pCeregRCnf->tRegisterState.OP_ActiveTime)
        {
            for (i  = 0; i < 8; i++)
            {
                ucTemp = (0x80 >> i)& pCeregRCnf->tRegisterState.ucActiveTime;
                aucStr[i] = (ucTemp >> (7-i)) + 0x30;
            }
        }
        AtcAp_WriteStrPara_M(pCeregRCnf->tRegisterState.OP_ActiveTime, aucStr );


        //<Periodic-TAU>
        if (pCeregRCnf->tRegisterState.OP_PeriodicTAU)
        {
            for (i  = 0; i < 8; i++)
            {
                ucTemp = (0x80 >> i)& pCeregRCnf->tRegisterState.ucPeriodicTAU;
                aucStr[i] = (ucTemp >> (7-i)) + 0x30;
            }
        }
        AtcAp_WriteStrPara_M(pCeregRCnf->tRegisterState.OP_PeriodicTAU, aucStr );
    }
    AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CREG_R_Cnf(unsigned char* pRecvMsg)
{
    AtcAp_MsgProc_CEREG_R_Cnf(pRecvMsg);
}

void AtcAp_MsgProc_CGATT_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CGATT_R_CNF_STRU* ptCgattRCnf;

    ptCgattRCnf = (ATC_MSG_CGATT_R_CNF_STRU*)pRecvMsg;
    
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, 
        (const unsigned char *)"\r\n+CGATT:%d\r\n", ptCgattRCnf->ucState);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CIMI_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CIMI_CNF_STRU*    ptCimiCnf;

    ptCimiCnf = (ATC_MSG_CIMI_CNF_STRU*)pRecvMsg;
#if USR_CUSTOM2
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
     (const unsigned char *)"\r\n+CIMI:%s\r\n", ptCimiCnf->stImsi.aucImsi);
#else
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
     (const unsigned char *)"\r\n%s\r\n", ptCimiCnf->stImsi.aucImsi);
#endif
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGDCONT_R_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                  i;
#if VER_CM
    unsigned char                  aucPdpType[][7] = {"RESERV","IP","IPV6","IPV4V6","RESERV"};
#else
    unsigned char                  aucPdpType[][7] = {"RESERV","IP","IPV6","IPV4V6","RESERV","Non-IP"};
#endif
    ATC_MSG_CGDCONT_R_CNF_STRU*    ptCgdcontRCnf;

    ptCgdcontRCnf = (ATC_MSG_CGDCONT_R_CNF_STRU*)pRecvMsg;
#if USR_CUSTOM2
    for (i = 0; i < ptCgdcontRCnf->ucNum; i++)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+CGDCONT:%d,\"%s\",\"%s\",,0,%d,,,,,%d",
                                ptCgdcontRCnf->stPdpContext[i].ucAtCid, 
                                aucPdpType[ptCgdcontRCnf->stPdpContext[i].ucPdpType],
                                ptCgdcontRCnf->stPdpContext[i].aucApnValue,
                                ptCgdcontRCnf->stPdpContext[i].ucH_comp,
                                ptCgdcontRCnf->stPdpContext[i].ucNSLPI);
    }
#else
    for (i = 0; i < ptCgdcontRCnf->ucNum; i++)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+CGDCONT:%d,\"%s\",\"%s\",,0,%d,,,,,%d,%d",
                                ptCgdcontRCnf->stPdpContext[i].ucAtCid, 
                                aucPdpType[ptCgdcontRCnf->stPdpContext[i].ucPdpType],
                                ptCgdcontRCnf->stPdpContext[i].aucApnValue,
                                ptCgdcontRCnf->stPdpContext[i].ucH_comp,
                                ptCgdcontRCnf->stPdpContext[i].ucNSLPI,
                                ptCgdcontRCnf->stPdpContext[i].ucSecurePco);
    }
#endif
    if(0 != g_AtcApInfo.stAtRspInfo.usRspLen)
    {   
        AtcAp_StrPrintf_AtcRspBuf("\r\n");
        AtcAp_SendDataInd(pRecvMsg);
    }
}

void AtcAp_MsgProc_CFUN_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CFUN_R_CNF_STRU*       ptCfunRCnf;

    ptCfunRCnf = (ATC_MSG_CFUN_R_CNF_STRU*)pRecvMsg;
    
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CFUN:%d\r\n", ptCfunRCnf->ucFunMode);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CESQ_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CESQ_CNF_STRU*       pCesqCnf;

    pCesqCnf = (ATC_MSG_CESQ_CNF_STRU*)pRecvMsg;
#if USR_CUSTOM2
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
       (const unsigned char *)"\r\n+CESQ:99,99,%d,%d,%d,%d\r\n", 
       pCesqCnf->ucRscp, 
       pCesqCnf->ucEcno, pCesqCnf->ucRsrq, pCesqCnf->ucRsrp);
#else
     g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CESQ:%d,%d,%d,%d,%d,%d\r\n", 
        pCesqCnf->ucRxlev, pCesqCnf->ucBer, pCesqCnf->ucRscp, 
        pCesqCnf->ucEcno, pCesqCnf->ucRsrq, pCesqCnf->ucRsrp);
#endif
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CSQ_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CSQ_CNF_STRU*       pCsqCnf;

    pCsqCnf = (ATC_MSG_CSQ_CNF_STRU*)pRecvMsg;
#if USR_CUSTOM2
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CSQ:%d,99\r\n", pCsqCnf->ucRxlev);
#else
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CSQ:%d,%d\r\n", pCsqCnf->ucRxlev, pCsqCnf->ucBer);
#endif
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGPADDR_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                       i;
    ATC_MSG_CGPADDR_CNF_STRU*           pCgpaddrCnf;
    unsigned char             ucIpv6addr_all[16] = { 0 };

    pCgpaddrCnf = (ATC_MSG_CGPADDR_CNF_STRU*)pRecvMsg;

    if(0 == pCgpaddrCnf->stPara.ucCidNum)
    {
        return;
    }

    for (i = 0; i < pCgpaddrCnf->stPara.ucCidNum; i++)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
            (const unsigned char *)"\r\n+CGPADDR:%d", (pCgpaddrCnf->stPara.aPdpAddr[i].ucCid));
#if VER_QUEC && !USR_CUSTOM2
        if((D_PDP_TYPE_IPV4 == pCgpaddrCnf->stPara.aPdpAddr[i].ucPdpType) || (D_PDP_TYPE_IPV4V6 == pCgpaddrCnf->stPara.aPdpAddr[i].ucPdpType))
        {
            AtcAp_OutputAddr(4, pCgpaddrCnf->stPara.aPdpAddr[i].Pdpaddr1.aucIpv4Addrs, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
        }
        
        if((D_PDP_TYPE_IPV6 == pCgpaddrCnf->stPara.aPdpAddr[i].ucPdpType) || (D_PDP_TYPE_IPV4V6 == pCgpaddrCnf->stPara.aPdpAddr[i].ucPdpType))
        {
            if((D_ATC_FLAG_TRUE != pCgpaddrCnf->stPara.aPdpAddr[i].ucPdpaddr2Flg))
            {
                AtcAp_OutputAddr(16, pCgpaddrCnf->stPara.aPdpAddr[i].PdpAddr2.aucIpv6Addrs, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
            }
            else
            {
                if(ATC_GET_IPV6ADDR_ALL((pCgpaddrCnf->stPara.aPdpAddr[i].ucCid),ucIpv6addr_all) == ATC_AP_TRUE)
                {
                    AtcAp_OutputAddr(16, ucIpv6addr_all, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                }
                else
                {
                    AtcAp_OutputAddr(16, pCgpaddrCnf->stPara.aPdpAddr[i].PdpAddr2.aucIpv6Addrs, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                }
            }
        }
#else
        if(D_ATC_FLAG_TRUE == pCgpaddrCnf->stPara.aPdpAddr[i].ucPdpaddr1Flg || D_ATC_FLAG_TRUE == pCgpaddrCnf->stPara.aPdpAddr[i].ucPdpaddr2Flg)
        {
            if (D_ATC_FLAG_TRUE == pCgpaddrCnf->stPara.aPdpAddr[i].ucPdpaddr1Flg)
            {
                AtcAp_OutputAddr(4, pCgpaddrCnf->stPara.aPdpAddr[i].Pdpaddr1.aucIpv4Addrs, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
            }
            
            if (D_ATC_FLAG_TRUE == pCgpaddrCnf->stPara.aPdpAddr[i].ucPdpaddr2Flg)
            {
                if(ATC_GET_IPV6ADDR_ALL((pCgpaddrCnf->stPara.aPdpAddr[i].ucCid),ucIpv6addr_all) == ATC_AP_TRUE)
                {
                    AtcAp_OutputAddr(16, ucIpv6addr_all, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                }
                else
                {
                    AtcAp_OutputAddr(16, pCgpaddrCnf->stPara.aPdpAddr[i].PdpAddr2.aucIpv6Addrs, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                }
            }
        }
        else
        {
#if VER_CM
            if(D_PDP_TYPE_IPV4V6 == pCgpaddrCnf->stPara.aPdpAddr[i].ucPdpType)
            {
                AtcAp_StrPrintf_AtcRspBuf(",\"\",\"\"");
            }
            else
            {
                AtcAp_StrPrintf_AtcRspBuf(",\"\"");
            }
#endif
        }
#endif
    }

    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),(const unsigned char *)"\r\n");
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGPADDR_T_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CGPADDR_T_CNF_STRU*    pCgpaddrTCnf;
    unsigned char                  i;

    pCgpaddrTCnf = (ATC_MSG_CGPADDR_T_CNF_STRU*)pRecvMsg;
#if USR_CUSTOM2
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, (const unsigned char *)"\r\n+CGPADDR:");
    for (i = 0; i < pCgpaddrTCnf->stPara.ucCidNum; i++)
    {
        if (i == (pCgpaddrTCnf->stPara.ucCidNum - 1))
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
                (const unsigned char *)"%d",
                (pCgpaddrTCnf->stPara.aucCid[i]));
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
                (const unsigned char *)"%d,",
                (pCgpaddrTCnf->stPara.aucCid[i]));
        }
    }
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
        (const unsigned char *)"\r\n");
#else
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, (const unsigned char *)"\r\n+CGPADDR:(");
    for (i = 0; i < pCgpaddrTCnf->stPara.ucCidNum; i++)
    {
        if (i == (pCgpaddrTCnf->stPara.ucCidNum - 1))
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
                (const unsigned char *)"%d",
                (pCgpaddrTCnf->stPara.aucCid[i]));
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
                (const unsigned char *)"%d,",
                (pCgpaddrTCnf->stPara.aucCid[i]));
        }
    }
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
        (const unsigned char *)")\r\n");
#endif
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGACT_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CGACT_R_CNF_STRU*    pCgactRCnf;
    unsigned char                i;

    pCgactRCnf = (ATC_MSG_CGACT_R_CNF_STRU*)pRecvMsg;

    if (0 != pCgactRCnf->stState.ucValidNum)
    {
        for (i = 0; i < pCgactRCnf->stState.ucValidNum; i++)
        {
            AtcAp_StrPrintf_AtcRspBuf("\r\n+CGACT:%d,%d", pCgactRCnf->stState.aCidSta[i].ucCid, pCgactRCnf->stState.aCidSta[i].ucState);
        }

        AtcAp_StrPrintf_AtcRspBuf("\r\n");
        AtcAp_SendDataInd(pRecvMsg);
    }
}

#ifndef _FLASH_OPTIMIZE_
void AtcAp_MsgProc_CRTDCP_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CRTDCP_R_CNF_STRU* pCrtdcpRCnf = (ATC_MSG_CRTDCP_R_CNF_STRU*)pRecvMsg;

    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CRTDCP:%d\r\n", pCrtdcpRCnf->ucCrtdcpRepValue);
    
    AtcAp_SendDataInd(pRecvMsg);
}
#endif
void AtcAp_MsgProc_CEDRXS_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CEDRXS_R_CNF_STRU* pEdrxsRCnf;
    unsigned char              aucStr[5]  = {0};
    unsigned char              aucStr1[5] = {0};
    unsigned char              i;
    
    pEdrxsRCnf = (ATC_MSG_CEDRXS_R_CNF_STRU*)pRecvMsg;
    for (i  = 0; i < 4; i++)
    {
        aucStr[i] = ((pEdrxsRCnf->stPara.ucReqDRXValue >> (3-i))&0x01) + 0x30;
    }
    for (i  = 0; i < 4; i++)
    {
        aucStr1[i] = ((pEdrxsRCnf->stPara.ucReqPagingTimeWin >> (3-i))&0x01) + 0x30;
    }

    if(D_ATC_EVENT_CEDRXS_R == pEdrxsRCnf->usEvent)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
            (const unsigned char *)"\r\n+CEDRXS:%d,\"%s\",\"%s\"\r\n", pEdrxsRCnf->stPara.ucActType,aucStr,aucStr1);
    }
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
            (const unsigned char *)"\r\n+MPTWEDRXS:%d,\"%s\",\"%s\"", pEdrxsRCnf->stPara.ucActType,
            aucStr1,aucStr);
        
        if(pEdrxsRCnf->stPara.ucNWeDRXValueFlg)
        {
            AtcAp_ConvertByte2BitStr(pEdrxsRCnf->stPara.ucNWeDRXValue, 4, aucStr);
            AtcAp_StrPrintf_AtcRspBuf(",\"%s\"", aucStr);
        }
        else
        {
            if(pEdrxsRCnf->stPara.ucPagingTimeWinFlg)
            {
                AtcAp_StrPrintf_AtcRspBuf(",\"\"");
            }
        }

        if(pEdrxsRCnf->stPara.ucPagingTimeWinFlg)
        {
            AtcAp_ConvertByte2BitStr(pEdrxsRCnf->stPara.ucPagingTimeWin, 4, aucStr);
            AtcAp_StrPrintf_AtcRspBuf(",\"%s\"", aucStr);
        }
        
        AtcAp_StrPrintf_AtcRspBuf("\r\n");
    }
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_SIMST_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_SIMST_IND_STRU*    pSimstInd = (ATC_MSG_SIMST_IND_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+SIMST:%d\r\n",pSimstInd->ucSimStatus);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CPSMS_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CPSMS_R_CNF_STRU*       pCpsmsCnf;
    unsigned char                   i = 0;
    unsigned char                   aucTau[9]     = {0};
    unsigned char                   aucActTime[9] = {0};

    pCpsmsCnf = (ATC_MSG_CPSMS_R_CNF_STRU*)pRecvMsg;
    for (i = 0;i < 8;i++)
    {
        aucActTime[i] = ((pCpsmsCnf->ucReqActTime >> (7 - i))&0x01) + 0x30;
        aucTau[i] = ((pCpsmsCnf->ucReqPeriTAU >> (7 - i))&0x01) + 0x30;
    }

    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, 
        (const unsigned char *)"\r\n+CPSMS:%d,,,%s,%s\r\n",
        pCpsmsCnf->ucMode, aucTau, aucActTime);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGAPNRC_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CGAPNRC_CNF_STRU*      pCgapnrcCnf;
    unsigned char                  i;

    pCgapnrcCnf = (ATC_MSG_CGAPNRC_CNF_STRU*)pRecvMsg;
    
    for(i = 0; i < pCgapnrcCnf->ucNum; i++)
    {
#if USR_CUSTOM2
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
                        (const unsigned char *)"\r\n+CGAPNRC:%d,%d,%d,%d",
                        pCgapnrcCnf->stEpsApnRateCtl[i].ucCid, 
                        pCgapnrcCnf->stEpsApnRateCtl[i].ucAdditionExcepReportFlg,
                        pCgapnrcCnf->stEpsApnRateCtl[i].ulUplinkTimeUnit,
                        pCgapnrcCnf->stEpsApnRateCtl[i].ulMaxUplinkRate);
#else
        if(pCgapnrcCnf->stEpsApnRateCtl[i].ulUplinkTimeUnit == 0)
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
                            (const unsigned char *)"\r\n+CGAPNRC:%d,%d,%d",
                            pCgapnrcCnf->stEpsApnRateCtl[i].ucCid, 
                            pCgapnrcCnf->stEpsApnRateCtl[i].ucAdditionExcepReportFlg,
                            pCgapnrcCnf->stEpsApnRateCtl[i].ulUplinkTimeUnit);
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
                            (const unsigned char *)"\r\n+CGAPNRC:%d,%d,%d,%d",
                            pCgapnrcCnf->stEpsApnRateCtl[i].ucCid, 
                            pCgapnrcCnf->stEpsApnRateCtl[i].ucAdditionExcepReportFlg,
                            pCgapnrcCnf->stEpsApnRateCtl[i].ulUplinkTimeUnit,
                            pCgapnrcCnf->stEpsApnRateCtl[i].ulMaxUplinkRate);
        }
#endif
    }

    if(0 != g_AtcApInfo.stAtRspInfo.usRspLen)
    {
       AtcAp_StrPrintf_AtcRspBuf("\r\n");
       AtcAp_SendDataInd(pRecvMsg);
    } 
}

void AtcAp_MsgProc_CGAPNRC_T_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CGAPNRC_T_CNF_STRU*      pCgapnrcTCnf;
    unsigned char i;

    pCgapnrcTCnf = (ATC_MSG_CGAPNRC_T_CNF_STRU*)pRecvMsg;

    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, (const unsigned char *)"\r\n+CGAPNRC:(");
    for (i = 0; i < pCgapnrcTCnf->stPara.ucCidNum; i++)
    {
        if (i == ( pCgapnrcTCnf->stPara.ucCidNum - 1))
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
                (const unsigned char *)"%d",
                ( pCgapnrcTCnf->stPara.aucCid[i]));
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
                (const unsigned char *)"%d,",
                ( pCgapnrcTCnf->stPara.aucCid[i]));
        }
    }
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
        (const unsigned char *)")\r\n");

    AtcAp_SendDataInd(pRecvMsg);
    return;
}


void AtcAp_MsgProc_CSCON_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CSCON_R_CNF_STRU*    pCsconCnf;

    pCsconCnf = (ATC_MSG_CSCON_R_CNF_STRU*)pRecvMsg;
    
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CSCON:%d,%d",
        pCsconCnf->stPara.ucIndPara,pCsconCnf->stPara.ucMode);//TBD

    if(1 < pCsconCnf->stPara.ucIndPara)
    {
        AtcAp_WriteIntPara(pCsconCnf->stPara.OP_State, pCsconCnf->stPara.ucState );//when n >= 2 and EMM_CONNECTED mode this param is exist
        if(2 < pCsconCnf->stPara.ucIndPara)
        {
            AtcAp_WriteIntPara(pCsconCnf->stPara.OP_Access, pCsconCnf->stPara.ucAccess );//when n >= 3 and EMM_CONNECTED mode this param is exist
        }
    }
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
        (const unsigned char *)"\r\n");
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NL2THP_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_NL2THP_R_CNF_STRU* pNl2thpRCnf;

    pNl2thpRCnf = (ATC_MSG_NL2THP_R_CNF_STRU*)pRecvMsg;

    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, 
        (const unsigned char *)"\r\n+NL2THP:%d,%d\r\n", pNl2thpRCnf->ucL2THPFlag, pNl2thpRCnf->ucTimerLen);
    
    AtcAp_SendDataInd(pRecvMsg);
}
#if USR_CUSTOM2
void AtcAp_MsgProc_NUESTATS_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_NUESTATS_CNF_STRU* pNueStatsCnf;
    unsigned char              aucSelectPlmn[7] = { 0 };

    pNueStatsCnf = (ATC_MSG_NUESTATS_CNF_STRU*)pRecvMsg;
    if(ATC_NUESTATS_TYPE_NOPARAMETER == pNueStatsCnf->type)
    {
        if(0xFFFFFFFF != pNueStatsCnf->stRadio.current_plmn)
        {
            AtcAp_IntegerToPlmn(pNueStatsCnf->stRadio.current_plmn, aucSelectPlmn);
        }

        AtcAp_StrPrintf_AtcRspBuf("\r\n%s%d\r\n", "+Signal power:", pNueStatsCnf->stRadio.rsrp);
        AtcAp_StrPrintf_AtcRspBuf("%s%d\r\n", "+Total power:", pNueStatsCnf->stRadio.rssi);
        AtcAp_StrPrintf_AtcRspBuf("%s%d\r\n", "+TX power:", pNueStatsCnf->stRadio.current_tx_power_level);
        AtcAp_StrPrintf_AtcRspBuf("%s%u\r\n", "+TX time:", pNueStatsCnf->stRadio.total_tx_time);
        AtcAp_StrPrintf_AtcRspBuf("%s%u\r\n", "+RX time:", pNueStatsCnf->stRadio.total_rx_time);
        AtcAp_StrPrintf_AtcRspBuf("%s%u\r\n", "+Cell ID:", pNueStatsCnf->stRadio.last_cell_ID);
        AtcAp_StrPrintf_AtcRspBuf("%s%u\r\n", "+ECL:", pNueStatsCnf->stRadio.last_ECL_value);
        AtcAp_StrPrintf_AtcRspBuf("%s%d\r\n", "+SNR:", pNueStatsCnf->stRadio.last_snr_value);
        AtcAp_StrPrintf_AtcRspBuf("%s%u\r\n", "+EARFCN:", pNueStatsCnf->stRadio.last_earfcn_value);
        AtcAp_StrPrintf_AtcRspBuf("%s%u\r\n", "+PCI:", pNueStatsCnf->stRadio.last_pci_value);
        AtcAp_StrPrintf_AtcRspBuf("%s%d\r\n", "+RSRQ:", pNueStatsCnf->stRadio.rsrq);
        /* AtcAp_StrPrintf_AtcRspBuf("%s%s\r\n", "+PLMN:", aucSelectPlmn);
        AtcAp_StrPrintf_AtcRspBuf("%s%04X\r\n", "+TAC:", pNueStatsCnf->stRadio.current_tac);
        AtcAp_StrPrintf_AtcRspBuf("%s%d\r\n", "+SBAND:", pNueStatsCnf->stRadio.band); */
        AtcAp_SendDataInd(pRecvMsg);
    }

    if(ATC_NUESTATS_TYPE_RADIO == pNueStatsCnf->type || ATC_NUESTATS_TYPE_ALL == pNueStatsCnf->type)
    {
        if(0xFFFFFFFF != pNueStatsCnf->stRadio.current_plmn)
        {
            AtcAp_IntegerToPlmn(pNueStatsCnf->stRadio.current_plmn, aucSelectPlmn);
        }

        AtcAp_StrPrintf_AtcRspBuf("\r\n%s%d\r\n", "+Signal power:", pNueStatsCnf->stRadio.rsrp);
        AtcAp_StrPrintf_AtcRspBuf("%s%d\r\n", "+Total power:", pNueStatsCnf->stRadio.rssi);
        AtcAp_StrPrintf_AtcRspBuf("%s%d\r\n", "+TX power:", pNueStatsCnf->stRadio.current_tx_power_level);
        AtcAp_StrPrintf_AtcRspBuf("%s%u\r\n", "+TX time:", pNueStatsCnf->stRadio.total_tx_time);
        AtcAp_StrPrintf_AtcRspBuf("%s%u\r\n", "+RX time:", pNueStatsCnf->stRadio.total_rx_time);
        AtcAp_StrPrintf_AtcRspBuf("%s%u\r\n", "+Cell ID:", pNueStatsCnf->stRadio.last_cell_ID);
        AtcAp_StrPrintf_AtcRspBuf("%s%u\r\n", "+ECL:", pNueStatsCnf->stRadio.last_ECL_value);
        AtcAp_StrPrintf_AtcRspBuf("%s%d\r\n", "+SNR:", pNueStatsCnf->stRadio.last_snr_value);
        AtcAp_StrPrintf_AtcRspBuf("%s%u\r\n", "+EARFCN:", pNueStatsCnf->stRadio.last_earfcn_value);
        AtcAp_StrPrintf_AtcRspBuf("%s%u\r\n", "+PCI:", pNueStatsCnf->stRadio.last_pci_value);
        AtcAp_StrPrintf_AtcRspBuf("%s%d\r\n", "+RSRQ:", pNueStatsCnf->stRadio.rsrq);
        /* AtcAp_StrPrintf_AtcRspBuf("%s%s\r\n", "+PLMN:", aucSelectPlmn);
        AtcAp_StrPrintf_AtcRspBuf("%s%04X\r\n", "+TAC:", pNueStatsCnf->stRadio.current_tac);
        AtcAp_StrPrintf_AtcRspBuf("%s%d\r\n", "+SBAND:", pNueStatsCnf->stRadio.band); */
        AtcAp_SendDataInd(pRecvMsg);
    }

    if(ATC_NUESTATS_TYPE_BLER == pNueStatsCnf->type || ATC_NUESTATS_TYPE_ALL == pNueStatsCnf->type)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, 
            (const unsigned char *)"\r\n%s%d\r\n%s%d\r\n%s%d\r\n%s%d\r\n%s%d\r\n%s%d\r\n", 
            "+NUESTATS:BLER,RLC UL BLER,", pNueStatsCnf->stBler.rlc_ul_bler, 
            "+NUESTATS:BLER,RLC DL BLER,", pNueStatsCnf->stBler.rlc_dl_bler, 
            "+NUESTATS:BLER,MAC UL BLER,", pNueStatsCnf->stBler.mac_ul_bler, 
            "+NUESTATS:BLER,MAC DL BLER,", pNueStatsCnf->stBler.mac_dl_bler, 
            "+NUESTATS:BLER,Total TX bytes,", pNueStatsCnf->stBler.total_bytes_transmit, 
            "+NUESTATS:BLER,Total RX bytes,", pNueStatsCnf->stBler.total_bytes_receive);
        AtcAp_SendDataInd(pRecvMsg);

        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, 
            (const unsigned char *)"%s%d\r\n%s%d\r\n%s%d\r\n%s%d\r\n", 
            "+NUESTATS:BLER,Total TX blocks,", pNueStatsCnf->stBler.transport_blocks_send, 
            "+NUESTATS:BLER,Total RX blocks,", pNueStatsCnf->stBler.transport_blocks_receive, 
            "+NUESTATS:BLER,Total RTX blocks,", pNueStatsCnf->stBler.transport_blocks_retrans, 
            "+NUESTATS:BLER,Total ACK/NACK RX,", pNueStatsCnf->stBler.total_ackOrNack_msg_receive);
        AtcAp_SendDataInd(pRecvMsg);
    }

    if(ATC_NUESTATS_TYPE_THP == pNueStatsCnf->type || ATC_NUESTATS_TYPE_ALL == pNueStatsCnf->type)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, 
            (const unsigned char *)"\r\n%s%d\r\n%s%d\r\n%s%d\r\n%s%d\r\n", 
            "+NUESTATS:THP,RLC UL,", pNueStatsCnf->stThp.rlc_ul, 
            "+NUESTATS:THP,RLC DL,", pNueStatsCnf->stThp.rlc_dl, 
            "+NUESTATS:THP,MAC UL,", pNueStatsCnf->stThp.mac_ul, 
            "+NUESTATS:THP,MAC DL,", pNueStatsCnf->stThp.mac_dl);

        AtcAp_SendDataInd(pRecvMsg);
    }

    if(ATC_NUESTATS_TYPE_CELL == pNueStatsCnf->type || ATC_NUESTATS_TYPE_ALL == pNueStatsCnf->type)
    {
        if(0 != pNueStatsCnf->stCell.stCellList.ucCellNum)
        {
            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, 
            (const unsigned char *)"\r\n%s%d%c%d%s", "+NUESTATS:CELL,", 
            pNueStatsCnf->stCell.stCellList.aNCell[0].ulDlEarfcn, ',', pNueStatsCnf->stCell.stCellList.aNCell[0].usPhyCellId, ",1,");
            AtcAp_StrPrintf_AtcRspBuf("%d,", pNueStatsCnf->stCell.stCellList.aNCell[0].sRsrp);
            AtcAp_StrPrintf_AtcRspBuf("%d,", pNueStatsCnf->stCell.stCellList.aNCell[0].sRsrq);
            AtcAp_StrPrintf_AtcRspBuf("%d,", pNueStatsCnf->stCell.stCellList.aNCell[0].sRssi);
            AtcAp_StrPrintf_AtcRspBuf("%d\r\n", pNueStatsCnf->stCell.stCellList.aNCell[0].sSinr);

            AtcAp_Build_NCell(&pNueStatsCnf->stCell.stCellList);
            
            AtcAp_SendDataInd(pRecvMsg);
        }
    }

    if(ATC_NUESTATS_SBAND == pNueStatsCnf->type)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
            (const unsigned char *)"\r\n+NUESTATS:SBAND,%d\r\n", pNueStatsCnf->stBand.band);
        AtcAp_SendDataInd(pRecvMsg);
    }
}
#endif
void AtcAp_MsgProc_NEARFCN_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_NEARFCN_R_CNF_STRU* pNearfcnRCnf;

    pNearfcnRCnf = (ATC_MSG_NEARFCN_R_CNF_STRU*)pRecvMsg;

    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+NEARFCN:%d",pNearfcnRCnf->search_mode);
    if(1 == pNearfcnRCnf->search_mode)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
            (const unsigned char *)",%d", pNearfcnRCnf->earfcn);
    }
    else if(2 == pNearfcnRCnf->search_mode)
    {
        if (pNearfcnRCnf->pci > 255)
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = 3;
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = 2;
        }
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
            (const unsigned char *)",%d,%h", pNearfcnRCnf->earfcn, pNearfcnRCnf->pci);
    }
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"\r\n");
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NBAND_R_Cnf(unsigned char* pRecvMsg)
{
    unsigned char             ucBandCount;
    unsigned char             i;
    unsigned char             aucBandInfo[G_LRRC_SUPPORT_BAND_MAX_NUM];
    unsigned short            usOffset;

    ATC_MSG_NBAND_R_CNF_STRU* pNbandRCnf;

    pNbandRCnf = (ATC_MSG_NBAND_R_CNF_STRU*)pRecvMsg;
    
    ucBandCount = pNbandRCnf->stSupportBandList.ucSupportBandNum;
    for (i = 0; i < ucBandCount; i++ )
    {
        aucBandInfo[i] = pNbandRCnf->stSupportBandList.aucSuppBand[i];
    }
    
#if USR_CUSTOM2
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, (const unsigned char *)"\r\n+NBAND:");
#else
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, (const unsigned char *)"\r\n+MBAND:\"LTE\",");
#endif
    usOffset    = g_AtcApInfo.stAtRspInfo.usRspLen;
    if (ucBandCount > 0)
    {
        for( i = 0; i < (ucBandCount-1); i++ )
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + usOffset, (const unsigned char *)"%d,", aucBandInfo[i]);
            usOffset    = g_AtcApInfo.stAtRspInfo.usRspLen;
        }
    
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + usOffset, (const unsigned char *)"%d\r\n", aucBandInfo[i]);
    }
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + usOffset, (const unsigned char *)"\r\n");
    }

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NBAND_T_Cnf(unsigned char* pRecvMsg)
{
    unsigned char             ucBandCount;
    unsigned char             i;
    unsigned char             aucBandInfo[G_LRRC_SUPPORT_BAND_MAX_NUM];
    unsigned short            usOffset;
    
    ATC_MSG_NBAND_T_CNF_STRU* pNbandTCnf;

    pNbandTCnf = (ATC_MSG_NBAND_T_CNF_STRU*)pRecvMsg;
    
    ucBandCount = pNbandTCnf->stSupportBandList.ucSupportBandNum;
    for (i = 0; i < ucBandCount; i++ )
    {
        aucBandInfo[i] = pNbandTCnf->stSupportBandList.aucSuppBand[i];
    }

#if USR_CUSTOM2
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, (const unsigned char *)"\r\n%s","+NBAND:");
    
    usOffset    = g_AtcApInfo.stAtRspInfo.usRspLen;
    if (ucBandCount > 0)
    {
        for( i = 0; i < (ucBandCount-1); i++ )
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf+usOffset, (const unsigned char *)"%d,", aucBandInfo[i]);
            usOffset    = g_AtcApInfo.stAtRspInfo.usRspLen;
        }
    
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf+usOffset, (const unsigned char *)"%d\r\n", aucBandInfo[i]);
    }
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf+usOffset, (const unsigned char *)"\r\n");
    }
#else
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, (const unsigned char *)"\r\n%s","+MBAND:(");

    usOffset    = g_AtcApInfo.stAtRspInfo.usRspLen;
    if (ucBandCount > 0)
    {
        for( i = 0; i < (ucBandCount-1); i++ )
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf+usOffset, (const unsigned char *)"%d,", aucBandInfo[i]);
            usOffset    = g_AtcApInfo.stAtRspInfo.usRspLen;
        }
    
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf+usOffset, (const unsigned char *)"%d)\r\n", aucBandInfo[i]);
    }
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf+usOffset, (const unsigned char *)")\r\n");
    }
#endif
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NCONFIG_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_NCONFIG_R_CNF_STRU* pNconfigRCnf;
    char*                       format          = "+NCONFIG:%s,%d\r\n";
    char*                       format_str      = "+NCONFIG:%s,%s\r\n";

    pNconfigRCnf = (ATC_MSG_NCONFIG_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n");
#if USR_CUSTOM2
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_AUTOCONNECT].pucStr, pNconfigRCnf->autoconnect);
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_CELL_RESELECTION].pucStr, pNconfigRCnf->cell_reselection);
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_ENABLE_BIP].pucStr, pNconfigRCnf->enable_bip);
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_BARRING_RELEASE_DELAY].pucStr, pNconfigRCnf->barring_release_delay);
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_RELEASE_VERSION].pucStr, pNconfigRCnf->release_version);
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_SYNC_TIME_PERIOD].pucStr, pNconfigRCnf->sync_time_period);
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_PCO_IE_TYPE].pucStr, pNconfigRCnf->pco_ie_type);
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_NON_IP_NO_SMS_ENABLE].pucStr, pNconfigRCnf->non_ip_no_sms_enable);
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_T3324_T3412_EXT_CHANGE_REPORT].pucStr, pNconfigRCnf->t3324_t3412_ext_chg_report);

#else
    AtcAp_StrPrintf_AtcRspBuf(format_str, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_AUTOCONNECT].pucStr, pNconfigRCnf->autoconnect == 1 ? "TRUE" : "FALSE");
    AtcAp_StrPrintf_AtcRspBuf(format_str, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_CELL_RESELECTION].pucStr, pNconfigRCnf->cell_reselection == 1 ? "TRUE" : "FALSE");
    AtcAp_StrPrintf_AtcRspBuf(format_str, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_ENABLE_BIP].pucStr, pNconfigRCnf->enable_bip == 1 ? "TRUE" : "FALSE");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_BARRING_RELEASE_DELAY].pucStr, pNconfigRCnf->barring_release_delay);
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_RELEASE_VERSION].pucStr, pNconfigRCnf->release_version);
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_SYNC_TIME_PERIOD].pucStr, pNconfigRCnf->sync_time_period);
    AtcAp_StrPrintf_AtcRspBuf(format_str, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_PCO_IE_TYPE].pucStr, pNconfigRCnf->pco_ie_type == 1 ? "EPCO" : "PCO");
    AtcAp_StrPrintf_AtcRspBuf(format_str, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_NON_IP_NO_SMS_ENABLE].pucStr, pNconfigRCnf->non_ip_no_sms_enable == 1 ? "TRUE" : "FALSE");
    AtcAp_StrPrintf_AtcRspBuf(format_str, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_T3324_T3412_EXT_CHANGE_REPORT].pucStr, pNconfigRCnf->t3324_t3412_ext_chg_report == 1 ? "TRUE" : "FALSE");
#endif
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NCONFIG_T_Cnf(unsigned char *pRecvMsg)
{
    ATC_MSG_NCONFIG_T_CNF_STRU* pNconfigTCnf = (ATC_MSG_NCONFIG_T_CNF_STRU*)pRecvMsg;
    char*                       format       = "+NCONFIG:(%s,(%s))\r\n";

    AtcAp_StrPrintf_AtcRspBuf("\r\n");
    
#if USR_CUSTOM2
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_AUTOCONNECT].pucStr, "0,1");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_CELL_RESELECTION].pucStr, "0,1");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_ENABLE_BIP].pucStr, "0,1");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_BARRING_RELEASE_DELAY].pucStr, "0-1800");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_RELEASE_VERSION].pucStr, "8-14");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_SYNC_TIME_PERIOD].pucStr, "0-65535");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_PCO_IE_TYPE].pucStr, "0,1");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_NON_IP_NO_SMS_ENABLE].pucStr, "0,1");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_T3324_T3412_EXT_CHANGE_REPORT].pucStr, "0,1");

#else
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_AUTOCONNECT].pucStr, "FALSE,TRUE");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_CELL_RESELECTION].pucStr, "FALSE,TRUE");
    if(pNconfigTCnf->ucBipSupportType == D_ATC_NCONFIG_T_BIP_TYPE_SINGLE)
    {
        AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_ENABLE_BIP].pucStr,  
            pNconfigTCnf->enable_bip == 1 ? "TRUE" : "FALSE");
    }
    else
    {
        AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_ENABLE_BIP].pucStr, "FALSE,TRUE");
    }
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_BARRING_RELEASE_DELAY].pucStr, "0-1800");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_RELEASE_VERSION].pucStr, "8-14");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_SYNC_TIME_PERIOD].pucStr, "0-65535");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_PCO_IE_TYPE].pucStr, "PCO,EPCO");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_NON_IP_NO_SMS_ENABLE].pucStr, "FALSE,TRUE");
    AtcAp_StrPrintf_AtcRspBuf(format, (char*)ATC_NConfig_Table[D_ATC_NCONFIG_T3324_T3412_EXT_CHANGE_REPORT].pucStr, "TRUE");
#endif
    AtcAp_SendDataInd(pRecvMsg);

    return;
}

void AtcAp_MsgProc_NSET_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_NSET_R_CNF_STRU* pNsetRCnf;

    pNsetRCnf = (ATC_MSG_NSET_R_CNF_STRU*)pRecvMsg;

    if(0 == strcmp((char*)pNsetRCnf->aucInsValue, "SWVER"))
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+NSET:\"Software Version\",%s\r\n", (unsigned char*)pNsetRCnf->u.aucValue);
    }
    else if(0 == strcmp((char*)pNsetRCnf->aucInsValue, "DIRTY_DATA_CHECK"))
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+NSET:\"DIRTY_DATA_CHECK\",%d,%02X\r\n", (char)pNsetRCnf->u.ulValue, pNsetRCnf->ulValue2);
    }
    else if(0 == strcmp((char*)pNsetRCnf->aucInsValue, "PPM"))
    {
    	AtcAp_StrPrintf_AtcRspBuf("\r\n+NSET:\"%s\",%d\r\n", (unsigned char*)pNsetRCnf->aucInsValue, (char)pNsetRCnf->u.ulValue);
    }
    else if(0 == strcmp((char*)pNsetRCnf->aucInsValue, "NV_TEST"))
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+NSET:\"%s\",%d,%d\r\n", (unsigned char*)pNsetRCnf->aucInsValue, pNsetRCnf->u.ulValue, pNsetRCnf->ulValue2);
    }
    else
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+NSET:\"%s\",%d\r\n", (unsigned char*)pNsetRCnf->aucInsValue, pNsetRCnf->u.ulValue);
    }
    AtcAp_SendDataInd(pRecvMsg);
}

#ifdef LCS_MOLR_ENABLE
void AtcAp_MsgProc_CMOLR_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CMOLR_R_CNF_STRU* pCmolrRCnf;

    pCmolrRCnf = (ATC_MSG_CMOLR_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+CMOLR:%d", pCmolrRCnf->enable);

    AtcAp_WriteIntPara_M(pCmolrRCnf->method_flag, pCmolrRCnf->method, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);

    AtcAp_StrPrintf_AtcRspBuf(",%d", pCmolrRCnf->hor_acc_set);

    AtcAp_WriteIntPara_M(pCmolrRCnf->hor_acc_flag, pCmolrRCnf->hor_acc, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);

    AtcAp_StrPrintf_AtcRspBuf(",%d,%d", pCmolrRCnf->ver_req, pCmolrRCnf->ver_acc_set);

    AtcAp_WriteIntPara_M(pCmolrRCnf->ver_acc_flag, pCmolrRCnf->ver_acc, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);

    AtcAp_StrPrintf_AtcRspBuf(",%d,%d", pCmolrRCnf->vel_req, pCmolrRCnf->rep_mode);

    AtcAp_WriteIntPara_M(pCmolrRCnf->timeout, pCmolrRCnf->timeout, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    AtcAp_WriteIntPara_M(pCmolrRCnf->interval, pCmolrRCnf->interval, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    AtcAp_WriteIntPara_M(pCmolrRCnf->shape_rep, pCmolrRCnf->shape_rep, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    AtcAp_StrPrintf_AtcRspBuf(",%d", pCmolrRCnf->plane);
    
    AtcAp_SendDataInd(pRecvMsg);
}
#endif

#ifdef ESM_DEDICATED_EPS_BEARER
void AtcAp_MsgProc_CGDSCONT_R_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                i;
    ATC_MSG_CGDSCONT_R_CNF_STRU* pCgdscontRCnf;

    pCgdscontRCnf = (ATC_MSG_CGDSCONT_R_CNF_STRU*)pRecvMsg;

    if(0 == pCgdscontRCnf->stPara.ucValidNum)
    {
        return;
    }
    
    for(i = 0; i < pCgdscontRCnf->stPara.ucValidNum; i++)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+CGDSCONT:%d,%d,0,0", 
            pCgdscontRCnf->stPara.aSPdpCtxtPara[i].ucCid, pCgdscontRCnf->stPara.aSPdpCtxtPara[i].ucP_cid);
    }
    AtcAp_StrPrintf_AtcRspBuf("\r\n");
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGDSCONT_T_Cnf(unsigned char *pRecvMsg)
{
    unsigned char i = 0;

    ATC_MSG_CGDSCONT_T_CNF_STRU* pCgdscontTCnf;

    pCgdscontTCnf = (ATC_MSG_CGDSCONT_T_CNF_STRU*)pRecvMsg;

    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CGDSCONT:(");

    for (i = 0; i < pCgdscontTCnf->ucNum; i++)
    {
        if (pCgdscontTCnf->ucNum - 1 == i)
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                (const unsigned char *)"%d", pCgdscontTCnf->aucCid[i]);
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                (const unsigned char *)"%d,",pCgdscontTCnf->aucCid[i]);
        }
    }

    if (0 == pCgdscontTCnf->ucP_CidNum)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
            (const unsigned char *)"),(");
    }
    else
    {
        for (i = 0;i < pCgdscontTCnf->ucP_CidNum; i++)
        {
            if (0 == i)
            {
                g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                    (const unsigned char *)"),(%d",pCgdscontTCnf->aucP_Cid[i]);
            }
            else
            {
                g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                    (const unsigned char *)",%d",pCgdscontTCnf->aucP_Cid[i]);
            }
        }
    }
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
        (const unsigned char *)"),(0),(0)\r\n");

    AtcAp_SendDataInd(pRecvMsg);

    return;
}

void AtcAp_MsgProc_CGSCONTRDP_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                    i;
    ATC_MSG_CGSCONTRDP_CNF_STRU*    pCgscontrdpCnf = (ATC_MSG_CGSCONTRDP_CNF_STRU*)pRecvMsg;

    for (i = 0; i < pCgscontrdpCnf->stPara.ucValidNum ;i++)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                            (const unsigned char *)"\r\n+CGSCONTRDP:%d,%d,%d",pCgscontrdpCnf->stPara.aucPdpDynamicInfo[i].ucCid,
                            pCgscontrdpCnf->stPara.aucPdpDynamicInfo[i].ucPcid, pCgscontrdpCnf->stPara.aucPdpDynamicInfo[i].ucBearerId);

    }
    if(pCgscontrdpCnf->stPara.ucValidNum != 0)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
            (const unsigned char *)"\r\n");
        AtcAp_SendDataInd(pRecvMsg);
    }
}

void AtcAp_MsgProc_CGSCONTRDP_T_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                    i = 0;
    ATC_MSG_CGSCONTRDP_T_CNF_STRU*    pCgscontrdpTCnf = (ATC_MSG_CGSCONTRDP_T_CNF_STRU*)pRecvMsg;

    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                    (const unsigned char *)"\r\n+CGSCONTRDP:(");

    for (i = 0; i < pCgscontrdpTCnf->stPara.ucCidNum ;i++)
    {
        if (0 == i)
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                (const unsigned char *)"%d", pCgscontrdpTCnf->stPara.aucCid[i]);
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                (const unsigned char *)",%d", pCgscontrdpTCnf->stPara.aucCid[i]);
        }
    }
    
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
        (const unsigned char *)")\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGTFT_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CGTFT_R_CNF_STRU* pCgtftRCnf      = (ATC_MSG_CGTFT_R_CNF_STRU*)pRecvMsg;
    unsigned char             i;
    API_EPS_SET_PF_INFO      *ptPacketFilter;

    if(0 != pCgtftRCnf->ucPacketFilterNum)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
    }
    for(i = 0; i < pCgtftRCnf->ucPacketFilterNum; i++)
    {
        ptPacketFilter = (API_EPS_SET_PF_INFO*)(&pCgtftRCnf->atPacketFilter[i]);
     
        AtcAp_StrPrintf_AtcRspBuf((const char *)"+CGTFT:%d,%d,%d",
                                  pCgtftRCnf->ucAtCid,
                                  ptPacketFilter->ucPacketFilterId, 
                                  ptPacketFilter->ucEvaluationPrecedenceIndex);

        AtcAp_OutputAddr(ptPacketFilter->ucRemoteAddrAndSubMaskLen,
                         (unsigned char*)ptPacketFilter->aucRemoteAddrAndSubMask, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);

        AtcAp_WriteIntPara_M(ptPacketFilter->ucProtocolNum_NextHeaderFlg,
                             ptPacketFilter->ucProtocolNum_NextHeader, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);

        AtcAp_OutputPortRange(ptPacketFilter->ucLocalPortRangeLen, 
                              ptPacketFilter->ausLocalPortRange, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
        AtcAp_OutputPortRange(ptPacketFilter->ucRemotePortRangeLen,
                              ptPacketFilter->ausRemotePortRange, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
        
        AtcAp_WriteHexPara_M(ptPacketFilter->ucIpsecSPIFlg,
                             ptPacketFilter->ulIpsecSPI, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, D_ATC_CGTFT_PARAM_SPI_LEN);

        if (0 != ptPacketFilter->ucTypeOfServiceAndMaskLen)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)",\"%d.%d\"",
                                      ptPacketFilter->aucTypeOfServiceAndMask[0], 
                                      ptPacketFilter->aucTypeOfServiceAndMask[1]);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)",\"\"");
        }

        AtcAp_WriteHexPara_M(ptPacketFilter->ucFlowLabelFlg, 
                             ptPacketFilter->ulFlowLabel, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, D_ATC_CGTFT_PARAM_FLOW_LABEL_LEN);

        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", ptPacketFilter->ucDirection);
        AtcAp_OutputAddr(ptPacketFilter->ucLocalAddrAndSubMaskLen, 
                         (unsigned char*)ptPacketFilter->aucLocalAddrAndSubMask, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
        
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
                
        AtcAp_SendDataInd(pRecvMsg);
    }
}


void AtcAp_MsgProc_CGTFTRDP_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CGTFTRDP_CNF_STRU*    pCgtftrdpCnf;
    unsigned char             i = 0,j = 0;
    unsigned char             ucPfIdx; 

    pCgtftrdpCnf = (ATC_MSG_CGTFTRDP_CNF_STRU*)pRecvMsg;

    if(pCgtftrdpCnf->ucPacketNum == 0)
    {
        return;
    }

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
    for(i = 0; i < pCgtftrdpCnf->ucCidNum; i++)
    {
        for(j = 0; j < D_ATC_MAX_PACKET_FILTER; j++)
        {
            ucPfIdx = pCgtftrdpCnf->atBearerTft[i].aucPacketFilterIdMapInfo[j];
            if(0xFF == ucPfIdx)
            {
                continue;
            }
            
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                                                                (const unsigned char *)"+CGTFTRDP:%d,%d,%d",
                                                                pCgtftrdpCnf->atBearerTft[i].ucAtCid,
                                                                pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ucPacketFilterId, 
                                                                pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ucEvaluationPrecedenceIndex);

            AtcAp_OutputAddr(pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ucRemoteAddrAndSubMaskLen,
                             pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].aucRemoteAddrAndSubMask, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);

            AtcAp_WriteIntPara_M(pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ucProtocolNum_NextHeaderFlg,
                                 pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ucProtocolNum_NextHeader, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);

            AtcAp_OutputPortRange(pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ucLocalPortRangeLen, 
                                  pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ausLocalPortRange, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
            AtcAp_OutputPortRange(pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ucRemotePortRangeLen,
                                  pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ausRemotePortRange, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
            
            AtcAp_WriteHexPara_M(pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ucIpsecSPIFlg,
                                 pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ulIpsecSPI, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, D_ATC_CGTFT_PARAM_SPI_LEN);

            if (0 != pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ucTypeOfServiceAndMaskLen)
            {
                g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), (const unsigned char *)",\"%d.%d\"",
                                                                    pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].aucTypeOfServiceAndMask[0], 
                                                                    pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].aucTypeOfServiceAndMask[1]);
            }
            else
            {
                g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), (const unsigned char *)",\"\"");
            }

            AtcAp_WriteHexPara_M(pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ucFlowLabelFlg, 
                                 pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ulFlowLabel, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, D_ATC_CGTFT_PARAM_FLOW_LABEL_LEN);

            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                (const unsigned char *)",%d", pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ucDirection);
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                (const unsigned char *)",%d", ucPfIdx);

            AtcAp_OutputAddr(pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].ucLocalAddrAndSubMaskLen, 
                             pCgtftrdpCnf->atBearerTft[i].atPacketFilter[ucPfIdx].aucLocalAddrAndSubMask, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
            
            AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
            AtcAp_SendDataInd(pRecvMsg);
        }
    }
}

void AtcAp_MsgProc_CGTFTRDP_T_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                    i = 0;
    ATC_MSG_CGTFTRDP_T_CNF_STRU*    pCgtftrdpTCnf = (ATC_MSG_CGTFTRDP_T_CNF_STRU*)pRecvMsg;

    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                    (const unsigned char *)"\r\n+CGTFTRDP:(");

    for (i = 0; i < pCgtftrdpTCnf->stPara.ucCidNum ;i++)
    {
        if (0 == i)
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                (const unsigned char *)"%d", pCgtftrdpTCnf->stPara.aucCid[i]);
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                (const unsigned char *)",%d", pCgtftrdpTCnf->stPara.aucCid[i]);
        }
    }
    
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
        (const unsigned char *)")\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGEQOS_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CGEQOS_R_CNF_STRU*  pCgeqosRCnf;
    unsigned char               i;

    pCgeqosRCnf = (ATC_MSG_CGEQOS_R_CNF_STRU*)pRecvMsg;

    for (i = 0; i < pCgeqosRCnf->stPara.ucValidNum; i++)
    {
       AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CGEQOS:%d,%d,%d,%d,%d,%d",
            pCgeqosRCnf->stPara.aQosInfo[i].ucCid, pCgeqosRCnf->stPara.aQosInfo[i].stQosInfo.ucQci,
            pCgeqosRCnf->stPara.aQosInfo[i].stQosInfo.ulDlGbr,pCgeqosRCnf->stPara.aQosInfo[i].stQosInfo.ulUlGbr,
            pCgeqosRCnf->stPara.aQosInfo[i].stQosInfo.ulDlMbr,pCgeqosRCnf->stPara.aQosInfo[i].stQosInfo.ulUlMbr);
    }
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGCMOD_T_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CGCMOD_T_CNF_STRU*  pCgcModTCnf;
    unsigned char               i;

    pCgcModTCnf = (ATC_MSG_CGCMOD_T_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CGCMOD:(");
    
    for (i = 0; i < pCgcModTCnf->stPara.ucCidNum; i++)
    {
        if (i == (pCgcModTCnf->stPara.ucCidNum - 1))
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"%d", pCgcModTCnf->stPara.aucCid[i]);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"%d,", pCgcModTCnf->stPara.aucCid[i]);
        }
    }
    AtcAp_StrPrintf_AtcRspBuf((const char *)")\r\n");

    AtcAp_SendDataInd(pRecvMsg);
}
#endif

void AtcAp_MsgProc_COPS_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_COPS_R_CNF_STRU* pCopsRCnf;
    char                     acPlmnName[20] = { 0 };

    pCopsRCnf = (ATC_MSG_COPS_R_CNF_STRU*)pRecvMsg;
#if VER_CM || USR_CUSTOM12
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+COPS:%d,%d", pCopsRCnf->stPara.ucMode, pCopsRCnf->stPara.ucFormat);
#else
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+COPS:%d", pCopsRCnf->stPara.ucMode);
#endif
    if(D_ATC_FLAG_TRUE == pCopsRCnf->stPara.ucPsAttachFlg || 1 == pCopsRCnf->stPara.ucMode || 4 == pCopsRCnf->stPara.ucMode)
    {
#if !VER_CM && !USR_CUSTOM12
        AtcAp_WriteIntPara(pCopsRCnf->stPara.ucPlmnSelFlg, pCopsRCnf->stPara.ucFormat);
#endif
        if (pCopsRCnf->stPara.ucPlmnSelFlg)
        {
            AtcAp_MemCpy(acPlmnName, pCopsRCnf->stPara.aucNetworkName, sizeof(pCopsRCnf->stPara.aucNetworkName));
            if(0 == strlen(acPlmnName))
            {
                AtcAp_ConvertPlmn2NameStr(pCopsRCnf->stPara.ulPlmnNum, pCopsRCnf->stPara.ucFormat, acPlmnName);
            }
            AtcAp_StrPrintf_AtcRspBuf((const char *)",\"%s\"", acPlmnName);

            AtcAp_WriteIntPara(pCopsRCnf->stPara.ucPlmnSelFlg, pCopsRCnf->stPara.ucAct); 
        }
    }

    AtcAp_StrPrintf_AtcRspBuf("\r\n");
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_COPS_T_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                   i;
    unsigned char                   aucPlmnNum[7] = {0};
    char                            acLongPlmnName[20]  = { 0 };
    char                            acShortPlmnName[20] = { 0 };
    ATC_MSG_COPS_T_CNF_STRU*        pSrchCnf      = (ATC_MSG_COPS_T_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"\r\n+COPS:(");

    for (i = 0; i < pSrchCnf->stPara.ucNum; i++)
    {
        AtcAp_IntegerToPlmn(pSrchCnf->stPara.aOpeInf[i].ulPlmn, aucPlmnNum);

        AtcAp_MemSet(acLongPlmnName, 0, sizeof(acLongPlmnName));
        AtcAp_MemSet(acShortPlmnName, 0, sizeof(acShortPlmnName));
        AtcAp_MemCpy(acLongPlmnName, pSrchCnf->stPara.aOpeInf[i].aucFullNetworkName, sizeof(pSrchCnf->stPara.aOpeInf[i].aucFullNetworkName));
        AtcAp_MemCpy(acShortPlmnName, pSrchCnf->stPara.aOpeInf[i].aucShortNetworkName, sizeof(pSrchCnf->stPara.aOpeInf[i].aucShortNetworkName));
        if(0 == strlen(acLongPlmnName))
        {
            AtcAp_ConvertPlmn2NameStr(pSrchCnf->stPara.aOpeInf[i].ulPlmn, PLMN_FORMAT_LONG_ALPHA, acLongPlmnName);
        }
        if(0 == strlen(acShortPlmnName))
        {
            AtcAp_ConvertPlmn2NameStr(pSrchCnf->stPara.aOpeInf[i].ulPlmn, PLMN_FORMAT_SHORT_ALPHA, acShortPlmnName);
        }

        AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"(%d,\"%s\",\"%s\",\"%s\",%d)", pSrchCnf->stPara.aOpeInf[i].ucState, 
                                  acLongPlmnName, acShortPlmnName, aucPlmnNum, 7);
        
        if (i < (pSrchCnf->stPara.ucNum - 1))
        {
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",");
        }
    }

    AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"),,(0-4),(0-2)\r\n");

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGEREP_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CGEREP_R_CNF_STRU* pCgerepRCnf;

    pCgerepRCnf = (ATC_MSG_CGEREP_R_CNF_STRU*)pRecvMsg;
#if USR_CUSTOM2
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CGEREP:%d\r\n", pCgerepRCnf->ucCgerepMode);
#else
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CGEREP:%d,%d\r\n", pCgerepRCnf->ucCgerepMode, pCgerepRCnf->ucCgerepBfr);
#endif
    AtcAp_SendDataInd(pRecvMsg);
}
#ifndef _FLASH_OPTIMIZE_

void AtcAp_MsgProc_CCIOTOPT_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CCIOTOPT_R_CNF_STRU* pCciotoptRCnf = (ATC_MSG_CCIOTOPT_R_CNF_STRU*)pRecvMsg;
    
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CCIOTOPT:%d,%d,%d\r\n", 
        pCciotoptRCnf->stPara.ucCciotoptN, pCciotoptRCnf->stPara.ucSupptUeOpt, pCciotoptRCnf->stPara.ucPreferOpt);
    AtcAp_SendDataInd(pRecvMsg);
}
#endif

void AtcAp_MsgProc_CEDRXRDP_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CEDRXRDP_CNF_STRU* pCciotoptRCnf = (ATC_MSG_CEDRXRDP_CNF_STRU*)pRecvMsg;

    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CEDRXRDP:%d", pCciotoptRCnf->stPara.ucActType);

    if(pCciotoptRCnf->stPara.ucActType != 0)
    {
        if (EMM_EDRX_INVALID_VALUE != pCciotoptRCnf->stPara.ucReqDRXValue)
        {
            AtcAp_Write4BitData(pCciotoptRCnf->stPara.ucReqDRXValue);

            if ((EMM_EDRX_INVALID_VALUE != pCciotoptRCnf->stPara.ucNWeDRXValue)
                && (EMM_PTW_INVALID_VALUE != pCciotoptRCnf->stPara.ucPagingTimeWin))
            {
                AtcAp_Write4BitData(pCciotoptRCnf->stPara.ucNWeDRXValue);
                AtcAp_Write4BitData(pCciotoptRCnf->stPara.ucPagingTimeWin);
            }
        }
    }
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), (const unsigned char *)"\r\n");

    AtcAp_SendDataInd(pRecvMsg);
}

#ifdef ESM_DEDICATED_EPS_BEARER
void AtcAp_MsgProc_CGEQOSRDP_Cnf(unsigned char* pRecvMsg)
{
    unsigned                    i;
    ATC_MSG_CGEQOSRDP_CNF_STRU* pCgeqosCnf = (ATC_MSG_CGEQOSRDP_CNF_STRU*)pRecvMsg;

    for (i = 0; i < pCgeqosCnf->stPara.ucValidNum; i++)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
            (const unsigned char *)"\r\n+CGEQOSRDP:%d,%d",
            pCgeqosCnf->stPara.aQosApnAmbrInfo[i].ucCid, 
            pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stQosInfo.ucQci);

        if ((((1 <= pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stQosInfo.ucQci) 
            && (4 >= pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stQosInfo.ucQci)) 
            || (65 == pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stQosInfo.ucQci)
            || (66 == pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stQosInfo.ucQci)
            || (75 == pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stQosInfo.ucQci)))
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf  + g_AtcApInfo.stAtRspInfo.usRspLen), 
                (const unsigned char *)",%d,%d,%d,%d",
                pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stQosInfo.ulDlGbr, 
                pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stQosInfo.ulUlGbr, 
                pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stQosInfo.ulDlMbr, 
                pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stQosInfo.ulUlMbr);

            if (ATC_AP_TRUE == pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stApnAmbrInfo.ucApnAmbrFlag)
            {
                g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf  + g_AtcApInfo.stAtRspInfo.usRspLen), 
                    (const unsigned char *)",%d,%d",
                    pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stApnAmbrInfo.ulDlApnAmbr, 
                    pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stApnAmbrInfo.ulUlApnAmbr);
            }
        }
        else
        {
            if (ATC_AP_TRUE == pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stApnAmbrInfo.ucApnAmbrFlag)
            {
                g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
                    (const unsigned char *)",,,,,%d,%d",
                    pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stApnAmbrInfo.ulDlApnAmbr, 
                    pCgeqosCnf->stPara.aQosApnAmbrInfo[i].stApnAmbrInfo.ulUlApnAmbr);
            }
        }
    }

    if (0 != g_AtcApInfo.stAtRspInfo.usRspLen)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), (const unsigned char *)"\r\n");
        AtcAp_SendDataInd(pRecvMsg);
    }
}

void AtcAp_MsgProc_CGEQOSRDP_T_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                 i;
    ATC_MSG_CGEQOSRDP_T_CNF_STRU* pCgeqosrdpTCnf = (ATC_MSG_CGEQOSRDP_T_CNF_STRU*)pRecvMsg;
#if USR_CUSTOM2
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CGEQOSRDP:");
    for (i = 0; i < pCgeqosrdpTCnf->stPara.ucCidNum; i++)
    {
        if (i == (pCgeqosrdpTCnf->stPara.ucCidNum - 1))
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
                (const unsigned char *)"%d", (pCgeqosrdpTCnf->stPara.aucCid[i]));
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
                (const unsigned char *)"%d,",
                (pCgeqosrdpTCnf->stPara.aucCid[i]));
        }
    }
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
        (const unsigned char *)"\r\n");
#else
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CGEQOSRDP:(");
    for (i = 0; i < pCgeqosrdpTCnf->stPara.ucCidNum; i++)
    {
        if (i == (pCgeqosrdpTCnf->stPara.ucCidNum - 1))
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
                (const unsigned char *)"%d", (pCgeqosrdpTCnf->stPara.aucCid[i]));
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen,
                (const unsigned char *)"%d,",
                (pCgeqosrdpTCnf->stPara.aucCid[i]));
        }
    }
    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
        (const unsigned char *)")\r\n");
#endif
    AtcAp_SendDataInd(pRecvMsg);
}
#endif

void AtcAp_MsgProc_CTZR_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CTZR_R_CNF_STRU*      pCtzrTCnf = (ATC_MSG_CTZR_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+CTZR:%d\r\n", pCtzrTCnf->ucCtzrReport);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGCONTRDP_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                    i;
    ATC_MSG_CGCONTRDP_CNF_STRU*      pCgcongrdpCnf = (ATC_MSG_CGCONTRDP_CNF_STRU*)pRecvMsg;

    if(0 == pCgcongrdpCnf->stPara.ucValidNum)
    {
        return;
    }
    
    for(i = 0; i < pCgcongrdpCnf->stPara.ucValidNum; i++)
    {
        switch(pCgcongrdpCnf->stPara.aucPdpDynamicInfo[i].ucPdpType)
        {
            case D_PDP_TYPE_IPV4:
            case D_PDP_TYPE_IPV6:
            case D_PDP_TYPE_NonIP:
                AtcAp_CGCONTRDP_Print(&pCgcongrdpCnf->stPara.aucPdpDynamicInfo[i], pCgcongrdpCnf->stPara.aucPdpDynamicInfo[i].ucPdpType, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                break;
            default: //D_PDP_TYPE_IPV4V6   
                AtcAp_CGCONTRDP_Print(&pCgcongrdpCnf->stPara.aucPdpDynamicInfo[i], D_PDP_TYPE_IPV4, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                AtcAp_CGCONTRDP_Print(&pCgcongrdpCnf->stPara.aucPdpDynamicInfo[i], D_PDP_TYPE_IPV6, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                break;
        }
        if((pCgcongrdpCnf->stPara.ucValidNum - 1) == i)
        {
            AtcAp_StrPrintf_AtcRspBuf("\r\n");
        }
        AtcAp_SendDataInd(pRecvMsg);
    }

}

void AtcAp_MsgProc_CGCONTRDP_T_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                    i;
    ATC_MSG_CGCONTRDP_T_CNF_STRU*    pCgcongrdpTCnf = (ATC_MSG_CGCONTRDP_T_CNF_STRU*)pRecvMsg;

#if VER_QUEC
    if(0 == pCgcongrdpTCnf->stPara.ucCidNum)
    {
        return;
    }
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
            (const unsigned char *)"\r\n+CGCONTRDP:");

        for (i = 0; i < pCgcongrdpTCnf->stPara.ucCidNum ;i++)
        {
            if (0 == i)
            {
                g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                    (const unsigned char *)"%d", pCgcongrdpTCnf->stPara.aucCid[i]);
            }
            else
            {
                g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                    (const unsigned char *)",%d", pCgcongrdpTCnf->stPara.aucCid[i]);
            }
        }
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
            (const unsigned char *)"\r\n");
        AtcAp_SendDataInd(pRecvMsg);
    }
    return;
#else
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                    (const unsigned char *)"\r\n+CGCONTRDP:(");

    for (i = 0; i < pCgcongrdpTCnf->stPara.ucCidNum ;i++)
    {
        if (0 == i)
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                (const unsigned char *)"%d", pCgcongrdpTCnf->stPara.aucCid[i]);
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                (const unsigned char *)",%d", pCgcongrdpTCnf->stPara.aucCid[i]);
        }
    }

        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), 
            (const unsigned char *)")\r\n");
        AtcAp_SendDataInd(pRecvMsg);
#endif
}

void AtcAp_MsgProc_CEER_Cnf(unsigned char* pRecvMsg)
{
    const char*               errText;
    ATC_MSG_CEER_IND_STRU*    pCeerCnf = (ATC_MSG_CEER_IND_STRU*)pRecvMsg;
    
    if(D_FAIL_CAUSE_TYPE_NULL != pCeerCnf->ucType)
    {
        if(D_FAIL_CAUSE_TYPE_EMM == pCeerCnf->ucType)
        {
            errText = ATC_ConvertErrCode2Str(PEmmCauseTextTbl, D_ATC_EMM_CAUSE_TBL_SIZE, pCeerCnf->ucCause);
        }
        else
        {
            errText = ATC_ConvertErrCode2Str(PEsmCauseTextTbl, D_ATC_ESM_CAUSE_TBL_SIZE, pCeerCnf->ucCause);
        }
        AtcAp_StrPrintf_AtcRspBuf("\r\n+CEER:%s\r\n", errText);
        AtcAp_SendDataInd(pRecvMsg);
    }
}

void AtcAp_MsgProc_CIPCA_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CIPCA_R_CNF_STRU*    pCipcaRCnf = (ATC_MSG_CIPCA_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+CIPCA:%d,%d\r\n", pCipcaRCnf->n, pCipcaRCnf->ucAttachWithOutPdn);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGAUTH_R_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                 aucUserName[D_PCO_AUTH_MAX_LEN + 1];
    unsigned char                 aucPassword[D_PCO_AUTH_MAX_LEN + 1];
    unsigned char                 index;
    ATC_MSG_CGAUTH_R_CNF_STRU*    pCgauthRCnf = (ATC_MSG_CGAUTH_R_CNF_STRU*)pRecvMsg;

    for(index = 0; index < pCgauthRCnf->ucNum; index++)
    {
        AtcAp_MemSet(aucUserName, 0, D_PCO_AUTH_MAX_LEN + 1);
        AtcAp_MemSet(aucPassword, 0, D_PCO_AUTH_MAX_LEN + 1);
        AtcAp_MemCpy(aucUserName, pCgauthRCnf->stCgauth[index].aucUserName, D_PCO_AUTH_MAX_LEN);
        AtcAp_MemCpy(aucPassword, pCgauthRCnf->stCgauth[index].aucPassword, D_PCO_AUTH_MAX_LEN);

        AtcAp_StrPrintf_AtcRspBuf("\r\n+CGAUTH:%d,%d,%s,%s", pCgauthRCnf->stCgauth[index].ucAtCid, pCgauthRCnf->stCgauth[index].ucAuthProt, aucUserName, aucPassword);
    }

    if(0 != pCgauthRCnf->ucNum)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n");
        AtcAp_SendDataInd(pRecvMsg);
    }
}

void AtcAp_MsgProc_NPOWERCLASS_R_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                      index;
    ATC_MSG_NPOWERCLASS_R_CNF_STRU*    pNpowerClassRCnf = (ATC_MSG_NPOWERCLASS_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n");
    for(index = 0; index < pNpowerClassRCnf->unNum; index++)
    {
        AtcAp_StrPrintf_AtcRspBuf("+NPOWERCLASS:%d,%d\r\n", pNpowerClassRCnf->stSupportBandInfo[index].usBand, pNpowerClassRCnf->stSupportBandInfo[index].ucPowerClass);
    }

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NPOWERCLASS_T_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                      index;
    ATC_MSG_NPOWERCLASS_T_CNF_STRU*    pNpowerClassTCnf = (ATC_MSG_NPOWERCLASS_T_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+NPOWERCLASS:(");
    for(index = 0; index < pNpowerClassTCnf->unNum; index++)
    {
        if(index != 0)
        {
            AtcAp_StrPrintf_AtcRspBuf(",");
        }
        AtcAp_StrPrintf_AtcRspBuf("%d", pNpowerClassTCnf->ausBand[index]);
    }
    AtcAp_StrPrintf_AtcRspBuf("),(1,2,3)\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}
#ifndef _FLASH_OPTIMIZE_
void AtcAp_MsgProc_NPTWEDRXS_R_Cnf(unsigned char* pRecvMsg)
{
    AtcAp_MsgProc_CEDRXS_R_Cnf(pRecvMsg);
}

void AtcAp_MsgProc_NPOPB_R_Cnf(unsigned char* pRecvMsg)
{
    unsigned char             ucIdx = 0, ucOperatorIndex = 0;
    unsigned char             aucPlmnNum[7] = {0};
    ATC_NPOB_PRE_BAND_STRU *  ptBand;
    ATC_MSG_NPOPB_R_CNF_STRU* pNpopbRCnf    = (ATC_MSG_NPOPB_R_CNF_STRU*)pRecvMsg;

    for (ucOperatorIndex = 0; ucOperatorIndex < NVM_MAX_OPERATOR_NUM; ucOperatorIndex++)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+NPOPB:%d,(", ucOperatorIndex);
        
        for(ucIdx = 0; ucIdx < NVM_MAX_OPERATOR_PLMN_NUM; ucIdx++)
        {
            if(0 == pNpopbRCnf->stNpobList[ucOperatorIndex].aulPlmn[ucIdx])
            {
                break;
            }
            
            if (0 != ucIdx)
            {
                AtcAp_StrPrintf_AtcRspBuf((const char *)",");
            }
            
            AtcAp_IntegerToPlmn(pNpopbRCnf->stNpobList[ucOperatorIndex].aulPlmn[ucIdx], aucPlmnNum);
            AtcAp_StrPrintf_AtcRspBuf((const char *)"%s", aucPlmnNum);
        }
        
        AtcAp_StrPrintf_AtcRspBuf((const char *)"),(");
        
        for (ucIdx = 0; ucIdx < NVM_MAX_PRE_BAND_NUM; ucIdx++)
        {
            ptBand = &(pNpopbRCnf->stNpobList[ucOperatorIndex].aPreBand[ucIdx]);
            if(0 == ptBand->ucSuppBand)
            {
                break;
            }
 
            if (0 != ucIdx)
            {
               AtcAp_StrPrintf_AtcRspBuf((const char *)",");
            }
            
            AtcAp_StrPrintf_AtcRspBuf((const char *)"(%d, %d, %d)", ptBand->ucSuppBand, ptBand->ulStartFreq, ptBand->usOffset);
        }
        AtcAp_StrPrintf_AtcRspBuf((const char *)")");
        if((NVM_MAX_OPERATOR_NUM - 1) == ucOperatorIndex)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
        }
        AtcAp_SendDataInd(pRecvMsg);
    }
}
void AtcAp_MsgProc_NQPODCP_Cnf(unsigned char* pRecvMsg)
{
    unsigned char             i;
    ATC_MSG_NQPODCP_CNF_STRU* pNqpodcpCnf;

    pNqpodcpCnf      = (ATC_MSG_NQPODCP_CNF_STRU*)pRecvMsg;    
    if (pNqpodcpCnf->stSequence.ucSequenceNum == 0)
    {
        return;
    }

    if(pNqpodcpCnf->stSequence.ucSequenceNum > (D_ATC_RSP_MAX_BUF_SIZE - 4 - 9) / 4) //"\r\n+NQPODCP:xxx,xxx\r\n"
    {
        AtcAp_Free(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
        g_AtcApInfo.stAtRspInfo.aucAtcRspBuf = (unsigned char*)AtcAp_Malloc(D_ATC_NQPODCP_IND_MSG_MAX_LENGTH);
    }

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s","+NQPODCP:");
    for( i = 0; i < (pNqpodcpCnf->stSequence.ucSequenceNum); i++ )
    {  
        if(i != pNqpodcpCnf->stSequence.ucSequenceNum-1)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"%d,", pNqpodcpCnf->stSequence.Sequence[i]);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"%d\r\n", pNqpodcpCnf->stSequence.Sequence[i]);
        }
    }

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NQPNPD_Cnf(unsigned char* pRecvMsg)
{
    unsigned char             i;
    ATC_MSG_NQPNPD_CNF_STRU* pNqpnpdCnf;

    pNqpnpdCnf      = (ATC_MSG_NQPNPD_CNF_STRU*)pRecvMsg;    
    if (pNqpnpdCnf->stSequence.ucSequenceNum == 0)
    {
        return;
    }

    if(pNqpnpdCnf->stSequence.ucSequenceNum > (D_ATC_RSP_MAX_BUF_SIZE - 4 - 9) / 4) //"\r\n+NQPODCP:xxx,xxx\r\n"
    {
        AtcAp_Free(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
        g_AtcApInfo.stAtRspInfo.aucAtcRspBuf = (unsigned char*)AtcAp_Malloc(D_ATC_NQPODCP_IND_MSG_MAX_LENGTH);
    }

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s","+NQPNPD:");
    for( i = 0; i < (pNqpnpdCnf->stSequence.ucSequenceNum); i++ )
    {  
        if(i != pNqpnpdCnf->stSequence.ucSequenceNum-1)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"%d,", pNqpnpdCnf->stSequence.Sequence[i]);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"%d\r\n", pNqpnpdCnf->stSequence.Sequence[i]);
        }
    }

    AtcAp_SendDataInd(pRecvMsg);
}
#endif
#if !defined(_FLASH_OPTIMIZE_) || (USR_CUSTOM2)
void AtcAp_MsgProc_NCIDSTATUS_Cnf(unsigned char* pRecvMsg)
{
    unsigned char                 index;
    ATC_MSG_NCIDSTATUS_CNF_STRU*  pNcidStatusCnf = (ATC_MSG_NCIDSTATUS_CNF_STRU*)pRecvMsg;

    if(0 == pNcidStatusCnf->stCidStatus.ucNum)
    {
        return;
    }
    
    for(index = 0; index < pNcidStatusCnf->stCidStatus.ucNum; index++)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+NCIDSTATUS:%d,%d", pNcidStatusCnf->stCidStatus.aCidStaus[index].ucCid, pNcidStatusCnf->stCidStatus.aCidStaus[index].ucStatus);
        if(D_STATUS_BACKOFF == pNcidStatusCnf->stCidStatus.aCidStaus[index].ucStatus)
        {
            AtcAp_StrPrintf_AtcRspBuf(",%d", pNcidStatusCnf->stCidStatus.aCidStaus[index].ulBackOffVal);
        }
    }
    AtcAp_StrPrintf_AtcRspBuf("\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NCIDSTATUS_R_Cnf(unsigned char* pRecvMsg)
{
    AtcAp_MsgProc_NCIDSTATUS_Cnf(pRecvMsg);
}
#endif
void AtcAp_MsgProc_NGACTR_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_NGACTR_R_CNF_STRU*    pNgactrRCnf = (ATC_MSG_NGACTR_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+NGACTR:%d\r\n", pNgactrRCnf->ucNgactrN);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CNEC_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CNEC_R_CNF_STRU* pCnecRCnf = (ATC_MSG_CNEC_R_CNF_STRU*)pRecvMsg;
    
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CNEC:%d\r\n", pCnecRCnf->ucN);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NCPCDPR_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_NCPCDPR_R_CNF_STRU* pNcpcdprRCnf = (ATC_MSG_NCPCDPR_R_CNF_STRU*)pRecvMsg;
    unsigned char               i;

    if(pNcpcdprRCnf->ucNum == 0)
    {
        return;
    }

    for(i = 0; i < pNcpcdprRCnf->ucNum; i++)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+NCPCDPR:%d,%d", 
            pNcpcdprRCnf->stPdpCtxDnsSet[i].ucParam, pNcpcdprRCnf->stPdpCtxDnsSet[i].ucState);
    }
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CEID_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CEID_CNF_STRU* pCeidCnf  = (ATC_MSG_CEID_CNF_STRU*)pRecvMsg;
    unsigned char*         pDataBuff;
    
    if(0 == pCeidCnf->ucLen)
    {
        return;
    }

    pDataBuff = (unsigned char*)AtcAp_Malloc(pCeidCnf->ucLen * 2 + 1);
    AtcAp_HexToAsc(pCeidCnf->ucLen, pDataBuff, pCeidCnf->aucEid);
#if USR_CUSTOM2
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s\r\n", pDataBuff);
#else
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+MEID:%s\r\n", pDataBuff);
#endif
    AtcAp_Free(pDataBuff);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_CharSet_Val2Str(unsigned char val,unsigned char* str)
{
    unsigned char i;
    for(i = 0; i < sizeof(ATC_CharSet_Table) / sizeof(ST_ATC_STR_TABLE); i++)
    {
        if(ATC_CharSet_Table[i].ucStrVal == val)
        {
            AtcAp_MemCpy(str,ATC_CharSet_Table[i].pucStr,strlen(ATC_CharSet_Table[i].pucStr));
            break;
        }
    }
}

#ifdef LTE_SMS_FEATURE

void AtcAp_MsgProc_CSMS_Cnf(unsigned char* pRecvMsg)
{
    Unused_para(pRecvMsg);
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CSMS:1,1,0\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CSMS_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CSMS_R_CNF_STRU*    pCsmsRCnf = (ATC_MSG_CSMS_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CSMS:%d,1,1,0\r\n", pCsmsRCnf->ucMsgService);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CMGC_Cnf(unsigned char* pRecvMsg)
{
    char aucScts[32] = {0};
    unsigned char ucRaLen = 0;
    ATC_MSG_CMGC_CNF_STRU*    pCmgcInd = (ATC_MSG_CMGC_CNF_STRU*)pRecvMsg;
    
    if (0 == pCmgcInd->ucTpduLen)
    {
        AtcAp_StrPrintf_AtcRspBuf( (const char *)"\r\n+CMGC:%d\r\n", pCmgcInd->ucTpmr);
    }
    else
    {
        if(g_SmsFormatMode == 0)
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmgcInd->ucTpduLen;
            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
            (const unsigned char *)"\r\n+CMGC:%d,%x\r\n", pCmgcInd->ucTpmr, pCmgcInd->aucTpdu);
        }
        else
        {
           AtcAp_Tpdu_DecodeScts(pCmgcInd->aucTpdu,pCmgcInd->ucTpduLen,aucScts);
           if(aucScts != NULL && pCmgcInd->usMsgService == 1)
           {

                AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CMGC:%d,\"%s\"\r\n", pCmgcInd->ucTpmr,aucScts);
           }
           else
           {
                AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CMGC:%d\r\n", pCmgcInd->ucTpmr);
           }   
        }
    }
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CMGW_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CMGW_IND_STRU*    pCpmsInd = (ATC_MSG_CMGW_IND_STRU*)pRecvMsg;

    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
    (const unsigned char *)"\r\n+CMGW:%d\r\n",pCpmsInd->ucIndex);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CMTI_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_CMTI_IND_STRU*    pCmtiInd = (ATC_MSG_CMTI_IND_STRU*)pRecvMsg;

    if(pCmtiInd->ucNotURC == ATC_TRUE)
    {
        return;
    }
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
    (const unsigned char *)"\r\n+CMTI:\"%c%c\",%d\r\n",pCmtiInd->ucaMem[0],pCmtiInd->ucaMem[1], pCmtiInd->usIndex);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CMT_Ind(unsigned char* pRecvMsg)
{
    unsigned char aucAlpha[16] = {0};
    ATC_MSG_CMT_IND_STRU*    pCmtInd = (ATC_MSG_CMT_IND_STRU*)pRecvMsg;
    unsigned char  *pOa = NULL;
    unsigned char  *pSca = NULL;
    unsigned char  *pData = NULL;
    unsigned short usDataLen=0;

    if(pCmtInd->ucNotURC == ATC_TRUE)
    {
        return;
    }

    if(pCmtInd->ucShowMode == 0)//SMS_SHOW_PDU
    {
        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmtInd->u.stPduCmd.ucDataLen;
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CMT:,%d\r\n%x\r\n",pCmtInd->u.stPduCmd.ucPduLen, pCmtInd->u.stPduCmd.aucData);  
    }
    else
    {
        if(pCmtInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_HEX)
        {
            pOa = (unsigned char*)AtcAp_Malloc(2*strlen(pCmtInd->u.stTextCmd.aucOa)+1);
            AtcAp_ConvertGSM2HEX(pOa,pCmtInd->u.stTextCmd.aucOa,strlen(pCmtInd->u.stTextCmd.aucOa));

            pSca = (unsigned char*)AtcAp_Malloc(2*strlen(pCmtInd->u.stTextCmd.aucSca)+1);
            AtcAp_ConvertGSM2HEX(pSca,pCmtInd->u.stTextCmd.aucSca,strlen(pCmtInd->u.stTextCmd.aucSca));
        }
        else if(pCmtInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_UCS2)
        {
            pOa = (unsigned char*)AtcAp_Malloc(4*strlen(pCmtInd->u.stTextCmd.aucOa)+1);
            AtcAp_ConvertGSM2UCS2(pOa,pCmtInd->u.stTextCmd.aucOa,strlen(pCmtInd->u.stTextCmd.aucOa));

            pSca = (unsigned char*)AtcAp_Malloc(4*strlen(pCmtInd->u.stTextCmd.aucSca)+1);
            AtcAp_ConvertGSM2UCS2(pSca,pCmtInd->u.stTextCmd.aucSca,strlen(pCmtInd->u.stTextCmd.aucSca));
        }
        else //GSM or IRA
        {
            pOa = (unsigned char*)AtcAp_Malloc(strlen(pCmtInd->u.stTextCmd.aucOa)+1);
            AtcAp_MemCpy(pOa,pCmtInd->u.stTextCmd.aucOa,strlen(pCmtInd->u.stTextCmd.aucOa));

            pSca = (unsigned char*)AtcAp_Malloc(strlen(pCmtInd->u.stTextCmd.aucSca)+1);
            AtcAp_MemCpy(pSca,pCmtInd->u.stTextCmd.aucSca,strlen(pCmtInd->u.stTextCmd.aucSca));
            
            pCmtInd->u.stTextCmd.ucSelectCharSet = D_CHAR_SET_SELETE_IRA;            
        }
        
        if(pCmtInd->u.stTextCmd.ucSelectCharSet==D_CHAR_SET_SELETE_HEX
            || ((pCmtInd->u.stTextCmd.ucFo&0x40) == 0x40)  //TP-User-Data-Header-Indication is set
            || ((pCmtInd->u.stTextCmd.ucDcs & 0x0C) != 0)) //no GSM
        {
            usDataLen = pCmtInd->u.stTextCmd.ucDataLen;
            if(8 == pCmtInd->u.stTextCmd.ucDcs)
            {
                usDataLen = pCmtInd->u.stTextCmd.ucDataLen / 2;
            }
            if(1 == pCmtInd->u.stTextCmd.ucShowHeadFlg)
            {
                g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                                            (const unsigned char *)"\r\n+CMT:\"%s\",%s,\"%s\",%d,%d,%d,%d",
                                            pOa,
                                            aucAlpha,
                                            pCmtInd->u.stTextCmd.aucScts,
                                            pCmtInd->u.stTextCmd.ucTooa,
                                            pCmtInd->u.stTextCmd.ucFo,
                                            pCmtInd->u.stTextCmd.ucPid,
                                            pCmtInd->u.stTextCmd.ucDcs);
                
                g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf+g_AtcApInfo.stAtRspInfo.usRspLen,
                                            (const unsigned char *)",\"%s\",%d",
                                            pSca,
                                            pCmtInd->u.stTextCmd.ucToSca);
                g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmtInd->u.stTextCmd.ucDataLen;
                g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf+g_AtcApInfo.stAtRspInfo.usRspLen,
                                            (const unsigned char *)",%d\r\n%x\r\n",
                                            usDataLen,
                                            pCmtInd->u.stTextCmd.aucData);
            }
            else
            {
                g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                                            (const unsigned char *)"\r\n+CMT:\"%s\",%s,\"%s\"\r\n",
                                            pOa,
                                            aucAlpha,
                                            pCmtInd->u.stTextCmd.aucScts); 

                g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmtInd->u.stTextCmd.ucDataLen;
                g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf+g_AtcApInfo.stAtRspInfo.usRspLen,
                                            (const unsigned char *)"%x\r\n",
                                            pCmtInd->u.stTextCmd.aucData);            
            }            
        }
        else
        {
            //AtcAp_CharSet_Val2Str(pCmtInd->u.stTextCmd.ucSelectCharSet,ucAlpha);
            usDataLen=(unsigned short)pCmtInd->u.stTextCmd.ucDataLen;
            if(pCmtInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_GSM||pCmtInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_IRA)
            {
                pData = (unsigned char*)AtcAp_Malloc(pCmtInd->u.stTextCmd.ucDataLen+1);
                AtcAp_MemCpy(pData,pCmtInd->u.stTextCmd.aucData,pCmtInd->u.stTextCmd.ucDataLen);
            }
            else if(pCmtInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_UCS2)
            {
                pData = (unsigned char*)AtcAp_Malloc(4*pCmtInd->u.stTextCmd.ucDataLen+1);
                AtcAp_ConvertGSM2UCS2(pData,pCmtInd->u.stTextCmd.aucData,pCmtInd->u.stTextCmd.ucDataLen);
                usDataLen = pCmtInd->u.stTextCmd.ucDataLen;
            }

            if(1 == pCmtInd->u.stTextCmd.ucShowHeadFlg)
            {
                g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                                            (const unsigned char *)"\r\n+CMT:\"%s\",%s,\"%s\",%d,%d,%d,%d,\"%s\",%d,%d\r\n%s\r\n",
                                            pOa,
                                            aucAlpha,
                                            pCmtInd->u.stTextCmd.aucScts,
                                            pCmtInd->u.stTextCmd.ucTooa,
                                            pCmtInd->u.stTextCmd.ucFo,
                                            pCmtInd->u.stTextCmd.ucPid,
                                            pCmtInd->u.stTextCmd.ucDcs,
                                            pSca,
                                            pCmtInd->u.stTextCmd.ucToSca,
                                            usDataLen,
                                            pData);
            }
            else
            {
                g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                                            (const unsigned char *)"\r\n+CMT:\"%s\",%s,\"%s\"\r\n%s\r\n",
                                            pOa,
                                            aucAlpha,
                                            pCmtInd->u.stTextCmd.aucScts,
                                            pData);            
            }

            if(NULL!=pData)
            {
                AtcAp_Free(pData);
            }
        }
        
        if(NULL!=pOa)
        {
            AtcAp_Free(pOa);
        }
        if(NULL!=pSca)
        {
            AtcAp_Free(pSca);
        }        
    }
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CDS_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_CDS_IND_STRU*    pCdsInd = (ATC_MSG_CDS_IND_STRU*)pRecvMsg;
    unsigned char*           pRa=NULL;

    if(pCdsInd->ucNotURC == ATC_TRUE)
    {
        return;
    }

    if(pCdsInd->ucShowMode == 0)//SMS_SHOW_PDU
    {
        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCdsInd->u.stPduCmd.ucDataLen;
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CDS:%d\r\n%x\r\n",pCdsInd->u.stPduCmd.ucPduLen, pCdsInd->u.stPduCmd.aucData);  
    }
    else
    {
        if(pCdsInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_HEX)
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = strlen(pCdsInd->u.stTextCmd.aucRa);
            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
            (const unsigned char *)"\r\n+CDS:%d,%d,\"%x\",%d,\"%s\",\"%s\",%d\r\n",
            pCdsInd->u.stTextCmd.ucFo,
            pCdsInd->u.stTextCmd.ucMr,
            pCdsInd->u.stTextCmd.aucRa,
            pCdsInd->u.stTextCmd.ucTora,
            pCdsInd->u.stTextCmd.aucScts,
            pCdsInd->u.stTextCmd.aucDt,
            pCdsInd->u.stTextCmd.ucSt);   
        }
        else
        {
            if(pCdsInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_GSM||pCdsInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_IRA)
            {
                pRa = (unsigned char*)AtcAp_Malloc(strlen(pCdsInd->u.stTextCmd.aucRa)+1);
                AtcAp_MemCpy(pRa ,pCdsInd->u.stTextCmd.aucRa,strlen(pCdsInd->u.stTextCmd.aucRa));
            }
            else if(pCdsInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_UCS2)
            {
                pRa = (unsigned char*)AtcAp_Malloc(4*strlen(pCdsInd->u.stTextCmd.aucRa)+1);
                AtcAp_ConvertGSM2UCS2(pRa ,pCdsInd->u.stTextCmd.aucRa,strlen(pCdsInd->u.stTextCmd.aucRa));        
            }
            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
            (const unsigned char *)"\r\n+CDS:%d,%d,\"%s\",%d,\"%s\",\"%s\",%d\r\n",
            pCdsInd->u.stTextCmd.ucFo,
            pCdsInd->u.stTextCmd.ucMr,
            pRa,
            pCdsInd->u.stTextCmd.ucTora,
            pCdsInd->u.stTextCmd.aucScts,
            pCdsInd->u.stTextCmd.aucDt,
            pCdsInd->u.stTextCmd.ucSt); 
            if(NULL!=pRa)
            {
                AtcAp_Free(pRa);
            }
        }
    }
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CDSI_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_CDSI_IND_STRU*    pCdsiInd = (ATC_MSG_CDSI_IND_STRU*)pRecvMsg;

    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
    (const unsigned char *)"\r\n+CDSI:\"%c%c\",%d\r\n",pCdsiInd->ucaMem[0],pCdsiInd->ucaMem[1], pCdsiInd->usIndex);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CMGR_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CMGR_CNF_STRU*    pCmgrInd = (ATC_MSG_CMGR_CNF_STRU*)pRecvMsg;
    unsigned char aucState[11] = {0};
    unsigned char aucAlpha[16] = {0};
    unsigned char *pOa = NULL;
    unsigned char *pSca = NULL;
    unsigned char *pData = NULL;
    unsigned char* pRespBuff;
    unsigned short usMsgLen = 1024;
    unsigned short usDataLen = 0;

    pRespBuff = (unsigned char*)AtcAp_Malloc(usMsgLen);
    if(NULL == pRespBuff)
    {
        return;
    }

    if(pCmgrInd->ucShowMode == 0)//SMS_SHOW_PDU
    {
        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmgrInd->u.stPduCmd.ucDataLen;
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
        (const unsigned char *)"\r\n+CMGR:%d,,%d\r\n%x\r\n",pCmgrInd->ucState,pCmgrInd->u.stPduCmd.ucPduLen, pCmgrInd->u.stPduCmd.aucData);  
    }
    else
    {
        memset(aucState,0,sizeof(aucState));
        if(pCmgrInd->ucState == D_ATC_STAT_SENT)
        {
            AtcAp_MemCpy(aucState,"STO SENT",8);
        }
        else if(pCmgrInd->ucState == D_ATC_STAT_UNSENT)
        {
            AtcAp_MemCpy(aucState,"STO UNSENT",10);
        }
        else if(pCmgrInd->ucState == D_ATC_STAT_READ)
        {
            AtcAp_MemCpy(aucState,"REC READ",8);
        }
        else if(pCmgrInd->ucState == D_ATC_STAT_UNREAD)
        {
            AtcAp_MemCpy(aucState,"REC UNREAD",10);
        }

        if(pCmgrInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_HEX)
        {
            pOa = (unsigned char*)AtcAp_Malloc(2*strlen(pCmgrInd->u.stTextCmd.aucOa)+1);
            AtcAp_ConvertGSM2HEX(pOa,pCmgrInd->u.stTextCmd.aucOa,strlen(pCmgrInd->u.stTextCmd.aucOa));

            pSca = (unsigned char*)AtcAp_Malloc(2*strlen(pCmgrInd->u.stTextCmd.aucSca)+1);
            AtcAp_ConvertGSM2HEX(pSca,pCmgrInd->u.stTextCmd.aucSca,strlen(pCmgrInd->u.stTextCmd.aucSca));
        }
        else if(pCmgrInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_UCS2)
        {
            pOa = (unsigned char*)AtcAp_Malloc(4*strlen(pCmgrInd->u.stTextCmd.aucOa)+1);
            AtcAp_ConvertGSM2UCS2(pOa,pCmgrInd->u.stTextCmd.aucOa,strlen(pCmgrInd->u.stTextCmd.aucOa));

            pSca = (unsigned char*)AtcAp_Malloc(4*strlen(pCmgrInd->u.stTextCmd.aucSca)+1);
            AtcAp_ConvertGSM2UCS2(pSca,pCmgrInd->u.stTextCmd.aucSca,strlen(pCmgrInd->u.stTextCmd.aucSca));
        }
        else //GSM or IRA
        {
            pOa = (unsigned char*)AtcAp_Malloc(strlen(pCmgrInd->u.stTextCmd.aucOa)+1);
            AtcAp_MemCpy(pOa,pCmgrInd->u.stTextCmd.aucOa,strlen(pCmgrInd->u.stTextCmd.aucOa));

            pSca = (unsigned char*)AtcAp_Malloc(strlen(pCmgrInd->u.stTextCmd.aucSca)+1);
            AtcAp_MemCpy(pSca,pCmgrInd->u.stTextCmd.aucSca,strlen(pCmgrInd->u.stTextCmd.aucSca));
            
            pCmgrInd->u.stTextCmd.ucSelectCharSet = D_CHAR_SET_SELETE_IRA;            
        }

        if(pCmgrInd->u.stTextCmd.ucSelectCharSet==D_CHAR_SET_SELETE_HEX 
            || ((pCmgrInd->u.stTextCmd.ucFo&0x40) == 0x40) 
            || (((pCmgrInd->u.stTextCmd.ucDcs) & 0x0C) != 0 && (((pCmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_DELIVER) || ((pCmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_SUBMIT))))//UDHI or no gsm
        {
            usDataLen = pCmgrInd->u.stTextCmd.ucDataLen;
            if(8 == pCmgrInd->u.stTextCmd.ucDcs)
            {
                usDataLen = pCmgrInd->u.stTextCmd.ucDataLen / 2;
            }
            
            if((pCmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_DELIVER)
            {
                if(1 == pCmgrInd->u.stTextCmd.ucShowHeadFlg)
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                (const unsigned char *)"\r\n+CMGR:\"%s\",\"%s\",%s,\"%s\",%d,%d,%d,%d",
                                                aucState,
                                                pOa,
                                                aucAlpha,
                                                pCmgrInd->u.stTextCmd.aucScts,
                                                pCmgrInd->u.stTextCmd.ucTooa,
                                                pCmgrInd->u.stTextCmd.ucFo,
                                                pCmgrInd->u.stTextCmd.ucPid,
                                                pCmgrInd->u.stTextCmd.ucDcs);
                    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                (const unsigned char *)",\"%s\",%d",
                                                pSca,
                                                pCmgrInd->u.stTextCmd.ucToSca);
                    g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmgrInd->u.stTextCmd.ucDataLen;
                    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                (const unsigned char *)",%d\r\n%x\r\n",
                                                usDataLen,
                                                pCmgrInd->u.stTextCmd.aucData);    

                }
                else
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                (const unsigned char *)"\r\n+CMGR:\"%s\",\"%s\",%s,\"%s\"\r\n",
                                                aucState,
                                                pOa,
                                                aucAlpha,
                                                pCmgrInd->u.stTextCmd.aucScts);
                    g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmgrInd->u.stTextCmd.ucDataLen;
                    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                (const unsigned char *)"%x\r\n",
                                                pCmgrInd->u.stTextCmd.aucData);               
                }
            }
            else if((pCmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_SUBMIT)
            {
                if(1 == pCmgrInd->u.stTextCmd.ucShowHeadFlg)
                {
                    if(((pCmgrInd->u.stTextCmd.ucFo&0x18)>>3) == VP_ABSOLUTE_FORMAT)
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                    (const unsigned char *)"\r\n+CMGR:\"%s\",\"%s\",%s,%d,%d,%d,%d,\"%s\"",
                                                    aucState,
                                                    pOa,
                                                    aucAlpha,
                                                    pCmgrInd->u.stTextCmd.ucTooa,
                                                    pCmgrInd->u.stTextCmd.ucFo,
                                                    pCmgrInd->u.stTextCmd.ucPid,
                                                    pCmgrInd->u.stTextCmd.ucDcs,
                                                    pCmgrInd->u.stTextCmd.aucVp);
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",\"%s\",%d",
                                                    pSca,
                                                    pCmgrInd->u.stTextCmd.ucToSca);
                        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmgrInd->u.stTextCmd.ucDataLen;
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",%d\r\n%x\r\n",
                                                    usDataLen,
                                                    pCmgrInd->u.stTextCmd.aucData);
                    }
                    else if(((pCmgrInd->u.stTextCmd.ucFo&0x18)>>3) == VP_RELATIVE_FORMAT)
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                    (const unsigned char *)"\r\n+CMGR:\"%s\",\"%s\",%s,%d,%d,%d,%d,%d",
                                                    aucState,
                                                    pOa,
                                                    aucAlpha,
                                                    pCmgrInd->u.stTextCmd.ucTooa,
                                                    pCmgrInd->u.stTextCmd.ucFo,
                                                    pCmgrInd->u.stTextCmd.ucPid,
                                                    pCmgrInd->u.stTextCmd.ucDcs,
                                                    pCmgrInd->u.stTextCmd.aucVp[0]);
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",\"%s\",%d",
                                                    pSca,
                                                    pCmgrInd->u.stTextCmd.ucToSca);
                        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmgrInd->u.stTextCmd.ucDataLen;
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",%d\r\n%x\r\n",
                                                    usDataLen,
                                                    pCmgrInd->u.stTextCmd.aucData);
                    }
                    else
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                    (const unsigned char *)"\r\n+CMGR:\"%s\",\"%s\",%s,%d,%d,%d,%d,",
                                                    aucState,
                                                    pOa,
                                                    aucAlpha,
                                                    pCmgrInd->u.stTextCmd.ucTooa,
                                                    pCmgrInd->u.stTextCmd.ucFo,
                                                    pCmgrInd->u.stTextCmd.ucPid,
                                                    pCmgrInd->u.stTextCmd.ucDcs);
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",\"%s\",%d",
                                                    pSca,
                                                    pCmgrInd->u.stTextCmd.ucToSca);
                        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmgrInd->u.stTextCmd.ucDataLen;
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",%d\r\n%x\r\n",
                                                    usDataLen,
                                                    pCmgrInd->u.stTextCmd.aucData);
                    }
                }
                else
                {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                    (const unsigned char *)"\r\n+CMGR:\"%s\",\"%s\",%s\r\n",
                                                    aucState,
                                                    pOa,
                                                    aucAlpha);
                        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmgrInd->u.stTextCmd.ucDataLen;
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)"%x\r\n",
                                                    pCmgrInd->u.stTextCmd.aucData);  
                }
                
            }
            else if(((pCmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_COMMMAND) && (pCmgrInd->ucState==D_ATC_STAT_UNSENT || pCmgrInd->ucState==D_ATC_STAT_SENT))
            {
                if(1 == pCmgrInd->u.stTextCmd.ucShowHeadFlg)
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                (const unsigned char *)"\r\n+CMGR:\"%s\",%d,%d,%d,%d,\"%s\",%d",
                                                aucState,
                                                pCmgrInd->u.stTextCmd.ucFo,
                                                pCmgrInd->u.stTextCmd.ucCt,
                                                pCmgrInd->u.stTextCmd.ucPid,
                                                pCmgrInd->u.stTextCmd.ucMn,
                                                pOa,
                                                pCmgrInd->u.stTextCmd.ucTooa);
                    g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmgrInd->u.stTextCmd.ucDataLen;
                    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                (const unsigned char *)",%d\r\n%x\r\n",
                                                usDataLen,
                                                pCmgrInd->u.stTextCmd.aucData);
                }
                else
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                    (const unsigned char *)"\r\n+CMGR:\"%s\",%d,%d\r\n",
                    aucState,
                    pCmgrInd->u.stTextCmd.ucFo,
                    pCmgrInd->u.stTextCmd.ucCt);        
                }
            }
            else if(((pCmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_STATUS_REPORT) && (pCmgrInd->ucState==D_ATC_STAT_UNREAD || pCmgrInd->ucState==D_ATC_STAT_READ))
            {
                g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                (const unsigned char *)"\r\n+CMGR:\"%s\",%d,%d,\"%s\",%d,\"%s\",\"%s\",%d\r\n",
                aucState,
                pCmgrInd->u.stTextCmd.ucFo,
                pCmgrInd->u.stTextCmd.ucMr,
                pOa,
                pCmgrInd->u.stTextCmd.ucTooa,
                pCmgrInd->u.stTextCmd.aucScts,
                pCmgrInd->u.stTextCmd.aucDt,
                pCmgrInd->u.stTextCmd.ucSt); 
            }
        }
        else
        {
            //AtcAp_CharSet_Val2Str(pCmgrInd->u.stTextCmd.ucSelectCharSet,ucAlpha);
            usDataLen = (unsigned short)pCmgrInd->u.stTextCmd.ucDataLen;
            if(pCmgrInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_GSM||pCmgrInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_IRA)
            {
                pData = (unsigned char*)AtcAp_Malloc(pCmgrInd->u.stTextCmd.ucDataLen+1);
                AtcAp_MemCpy(pData,pCmgrInd->u.stTextCmd.aucData,pCmgrInd->u.stTextCmd.ucDataLen);
            }
            else if(pCmgrInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_UCS2)
            {
                pData = (unsigned char*)AtcAp_Malloc(4*pCmgrInd->u.stTextCmd.ucDataLen+1);
                AtcAp_ConvertGSM2UCS2(pData,pCmgrInd->u.stTextCmd.aucData,pCmgrInd->u.stTextCmd.ucDataLen);
                usDataLen = pCmgrInd->u.stTextCmd.ucDataLen;
            }    
            if((pCmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_DELIVER)
            {
                if(1 == pCmgrInd->u.stTextCmd.ucShowHeadFlg)
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                    (const unsigned char *)"\r\n+CMGR:\"%s\",\"%s\",%s,\"%s\",%d,%d,%d,%d,\"%s\",%d,%d\r\n%s\r\n",
                                                    aucState,
                                                    pOa,
                                                    aucAlpha,
                                                    pCmgrInd->u.stTextCmd.aucScts,
                                                    pCmgrInd->u.stTextCmd.ucTooa,
                                                    pCmgrInd->u.stTextCmd.ucFo,
                                                    pCmgrInd->u.stTextCmd.ucPid,
                                                    pCmgrInd->u.stTextCmd.ucDcs,
                                                    pSca,
                                                    pCmgrInd->u.stTextCmd.ucToSca,
                                                    usDataLen,
                                                    pData);
                }
                else
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                    (const unsigned char *)"\r\n+CMGR:\"%s\",\"%s\",%s,\"%s\"\r\n%s\r\n",
                                                    aucState,
                                                    pOa,
                                                    aucAlpha,
                                                    pCmgrInd->u.stTextCmd.aucScts,                    
                                                    pData);                
                }
            }
            else if((pCmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_SUBMIT)
            {
                if(1 == pCmgrInd->u.stTextCmd.ucShowHeadFlg)
                {
                    if(((pCmgrInd->u.stTextCmd.ucFo&0x18)>>3) == VP_ABSOLUTE_FORMAT)
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                        (const unsigned char *)"\r\n+CMGR:\"%s\",\"%s\",%s,%d,%d,%d,%d,\"%s\",\"%s\",%d,%d\r\n%s\r\n",
                        aucState,
                        pOa,
                        aucAlpha,
                        pCmgrInd->u.stTextCmd.ucTooa,
                        pCmgrInd->u.stTextCmd.ucFo,
                        pCmgrInd->u.stTextCmd.ucPid,
                        pCmgrInd->u.stTextCmd.ucDcs,
                        pCmgrInd->u.stTextCmd.aucVp,
                        pSca,
                        pCmgrInd->u.stTextCmd.ucToSca,
                        usDataLen,
                        pData); 
                    }
                    else if(((pCmgrInd->u.stTextCmd.ucFo&0x18)>>3) == VP_RELATIVE_FORMAT)
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                        (const unsigned char *)"\r\n+CMGR:\"%s\",\"%s\",%s,%d,%d,%d,%d,%d,\"%s\",%d,%d\r\n%s\r\n",
                        aucState,
                        pOa,
                        aucAlpha,
                        pCmgrInd->u.stTextCmd.ucTooa,
                        pCmgrInd->u.stTextCmd.ucFo,
                        pCmgrInd->u.stTextCmd.ucPid,
                        pCmgrInd->u.stTextCmd.ucDcs,
                        pCmgrInd->u.stTextCmd.aucVp[0],
                        pSca,
                        pCmgrInd->u.stTextCmd.ucToSca,
                        usDataLen,
                        pData); 
                    }
                    else
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                        (const unsigned char *)"\r\n+CMGR:\"%s\",\"%s\",%s,%d,%d,%d,%d,,\"%s\",%d,%d\r\n%s\r\n",
                        aucState,
                        pOa,
                        aucAlpha,
                        pCmgrInd->u.stTextCmd.ucTooa,
                        pCmgrInd->u.stTextCmd.ucFo,
                        pCmgrInd->u.stTextCmd.ucPid,
                        pCmgrInd->u.stTextCmd.ucDcs,
                        pSca,
                        pCmgrInd->u.stTextCmd.ucToSca,
                        usDataLen,
                        pData); 
                    }
                }
                else
                {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                        (const unsigned char *)"\r\n+CMGR:\"%s\",\"%s\",%s\r\n%s\r\n",
                        aucState,
                        pOa,
                        aucAlpha,
                        pData);   
                }
                
            }
            else if(((pCmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_COMMMAND) && (pCmgrInd->ucState==D_ATC_STAT_UNSENT || pCmgrInd->ucState==D_ATC_STAT_SENT))
            {
                if(1 == pCmgrInd->u.stTextCmd.ucShowHeadFlg)
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                    (const unsigned char *)"\r\n+CMGR:\"%s\",%d,%d,%d,%d,\"%s\",%d,%d\r\n%s\r\n",
                    aucState,
                    pCmgrInd->u.stTextCmd.ucFo,
                    pCmgrInd->u.stTextCmd.ucCt,
                    pCmgrInd->u.stTextCmd.ucPid,
                    pCmgrInd->u.stTextCmd.ucMn,
                    pOa,
                    pCmgrInd->u.stTextCmd.ucTooa,
                    usDataLen,
                    pData);
                }
                else
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                    (const unsigned char *)"\r\n+CMGR:\"%s\",%d,%d\r\n",
                    aucState,
                    pCmgrInd->u.stTextCmd.ucFo,
                    pCmgrInd->u.stTextCmd.ucCt);        
                }
            }
            else if(((pCmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_STATUS_REPORT) && (pCmgrInd->ucState==D_ATC_STAT_UNREAD || pCmgrInd->ucState==D_ATC_STAT_READ))
            {
                g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                (const unsigned char *)"\r\n+CMGR:\"%s\",%d,%d,\"%s\",%d,\"%s\",\"%s\",%d\r\n",
                aucState,
                pCmgrInd->u.stTextCmd.ucFo,
                pCmgrInd->u.stTextCmd.ucMr,
                pOa,
                pCmgrInd->u.stTextCmd.ucTooa,
                pCmgrInd->u.stTextCmd.aucScts,
                pCmgrInd->u.stTextCmd.aucDt,
                pCmgrInd->u.stTextCmd.ucSt); 
            }    
            if(NULL!=pData)
            {
                AtcAp_Free(pData);
            }
        }

        if(NULL!=pOa)
        {
            AtcAp_Free(pOa);
        }
        if(NULL!=pSca)
        {
            AtcAp_Free(pSca);
        }
    } 
    AtcAp_SendLongDataInd(pRecvMsg, &pRespBuff, usMsgLen);
    AtcAp_Free(pRespBuff);
    //AtcAp_SendDataInd(pEventBuffer);
}

void AtcAp_MsgProc_CMGL_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CMGL_CNF_STRU*    pCmglInd = (ATC_MSG_CMGL_CNF_STRU*)pRecvMsg;
    unsigned char aucState[11] = {0};
    unsigned char aucAlpha[16] = {0};
    unsigned char *pOa = NULL;
    unsigned char *pData = NULL;
    unsigned char* pRespBuff;
    unsigned short usMsgLen = 1024;
    unsigned short usDataLen = 0;

    pRespBuff = (unsigned char*)AtcAp_Malloc(usMsgLen);
    if(NULL == pRespBuff)
    {
        return;
    }
    if(pCmglInd->ucShowMode == 0)//SMS_SHOW_PDU
    {
        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmglInd->u.stPduCmd.ucDataLen;
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
        (const unsigned char *)"\r\n+CMGL:%d,%d,,%d\r\n%x\r\n",pCmglInd->ucIndex,pCmglInd->ucState,pCmglInd->u.stPduCmd.ucPduLen, pCmglInd->u.stPduCmd.aucData);  
    }
    else
    {
        memset(aucState,0,sizeof(aucState));
        if(pCmglInd->ucState == D_ATC_STAT_SENT)
        {
            AtcAp_MemCpy(aucState,"STO SENT",8);
        }
        else if(pCmglInd->ucState == D_ATC_STAT_UNSENT)
        {
            AtcAp_MemCpy(aucState,"STO UNSENT",10);
        }
        else if(pCmglInd->ucState == D_ATC_STAT_READ)
        {
            AtcAp_MemCpy(aucState,"REC READ",8);
        }
        else if(pCmglInd->ucState == D_ATC_STAT_UNREAD)
        {
            AtcAp_MemCpy(aucState,"REC UNREAD",10);
        }

        if(pCmglInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_HEX)
        {
            pOa = (unsigned char*)AtcAp_Malloc(2*strlen(pCmglInd->u.stTextCmd.aucOa)+1);
            AtcAp_ConvertGSM2HEX(pOa,pCmglInd->u.stTextCmd.aucOa,strlen(pCmglInd->u.stTextCmd.aucOa));
        }
        else if(pCmglInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_UCS2)
        {
            pOa = (unsigned char*)AtcAp_Malloc(4*strlen(pCmglInd->u.stTextCmd.aucOa)+1);
            AtcAp_ConvertGSM2UCS2(pOa,pCmglInd->u.stTextCmd.aucOa,strlen(pCmglInd->u.stTextCmd.aucOa));
        }
        else //GSM or IRA
        {
            pOa = (unsigned char*)AtcAp_Malloc(strlen(pCmglInd->u.stTextCmd.aucOa)+1);
            AtcAp_MemCpy(pOa,pCmglInd->u.stTextCmd.aucOa,strlen(pCmglInd->u.stTextCmd.aucOa));
            
            pCmglInd->u.stTextCmd.ucSelectCharSet = D_CHAR_SET_SELETE_IRA;            
        }

        if(pCmglInd->u.stTextCmd.ucSelectCharSet==D_CHAR_SET_SELETE_HEX 
            || ((pCmglInd->u.stTextCmd.ucFo&0x40) == 0x40) 
            || (((pCmglInd->u.stTextCmd.ucDcs) & 0x0C) != 0 && (((pCmglInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_DELIVER) || ((pCmglInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_SUBMIT))))
        {
            usDataLen = pCmglInd->u.stTextCmd.ucDataLen;
            if(8 == pCmglInd->u.stTextCmd.ucDcs)
            {
                usDataLen = pCmglInd->u.stTextCmd.ucDataLen / 2;
            }
            if((pCmglInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_DELIVER || (pCmglInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_SUBMIT)
            {
                if(1 == pCmglInd->u.stTextCmd.ucShowHeadFlg)
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+CMGL:%d,\"%s\",\"%s\"",
                                                        pCmglInd->ucIndex,
                                                        aucState,
                                                        pOa);
                    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",%s,\"%s\"",
                                                        aucAlpha,
                                                        pCmglInd->u.stTextCmd.aucScts);
                    
                    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",%d",
                                                        pCmglInd->u.stTextCmd.ucTooa);
                                    
                    g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmglInd->u.stTextCmd.ucDataLen;
                    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",%d\r\n%x\r\n",
                                                        usDataLen,
                                                        pCmglInd->u.stTextCmd.aucData); 
                }
                else
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+CMGL:%d,\"%s\",\"%s\",%s,\"%s\"\r\n",
                                                        pCmglInd->ucIndex,
                                                        aucState,
                                                        pOa,
                                                        aucAlpha,
                                                        pCmglInd->u.stTextCmd.aucScts);                                    
                    g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmglInd->u.stTextCmd.ucDataLen;
                    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)"%x\r\n",
                                                        pCmglInd->u.stTextCmd.aucData);
  
                }
            
            }
            else if(((pCmglInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_COMMMAND) && (pCmglInd->ucState==D_ATC_STAT_UNSENT || pCmglInd->ucState==D_ATC_STAT_SENT))
            {
                g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                (const unsigned char *)"\r\n+CMGL:%d,\"%s\",%d,%d\r\n",
                pCmglInd->ucIndex,
                aucState,
                pCmglInd->u.stTextCmd.ucFo,
                pCmglInd->u.stTextCmd.ucCt);
            }
            else if(((pCmglInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_STATUS_REPORT) && (pCmglInd->ucState==D_ATC_STAT_UNREAD || pCmglInd->ucState==D_ATC_STAT_READ))
            {
                g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                (const unsigned char *)"\r\n+CMGL:%d,\"%s\",%d,%d,\"%s\",%d,\"%s\",\"%s\",%d\r\n",
                pCmglInd->ucIndex,
                aucState,
                pCmglInd->u.stTextCmd.ucFo,
                pCmglInd->u.stTextCmd.ucMr,
                pOa,
                pCmglInd->u.stTextCmd.ucTooa,
                pCmglInd->u.stTextCmd.aucScts,
                pCmglInd->u.stTextCmd.aucDt,
                pCmglInd->u.stTextCmd.ucSt); 
            }
        }
        else
        {
            //AtcAp_CharSet_Val2Str(pCmglInd->u.stTextCmd.ucSelectCharSet,ucAlpha);
            usDataLen = (unsigned short)pCmglInd->u.stTextCmd.ucDataLen;
            if(pCmglInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_GSM||pCmglInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_IRA)
            {
                pData = (unsigned char*)AtcAp_Malloc(pCmglInd->u.stTextCmd.ucDataLen+1);
                AtcAp_MemCpy(pData,pCmglInd->u.stTextCmd.aucData,pCmglInd->u.stTextCmd.ucDataLen);
            }
            else if(pCmglInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_UCS2)
            {
                pData = (unsigned char*)AtcAp_Malloc(4*pCmglInd->u.stTextCmd.ucDataLen+1);
                AtcAp_ConvertGSM2UCS2(pData,pCmglInd->u.stTextCmd.aucData,pCmglInd->u.stTextCmd.ucDataLen);
                usDataLen = pCmglInd->u.stTextCmd.ucDataLen;
            }   
            if((pCmglInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_DELIVER || (pCmglInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_SUBMIT)
            {
                if(1 == pCmglInd->u.stTextCmd.ucShowHeadFlg)
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+CMGL:%d,\"%s\",\"%s\"",
                                                        pCmglInd->ucIndex,
                                                        aucState,
                                                        pOa);
                    
                    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",%s,\"%s\"",
                                                        aucAlpha,
                                                        pCmglInd->u.stTextCmd.aucScts);                                                  
                    
                    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",%d",
                                                        pCmglInd->u.stTextCmd.ucTooa);           
          
                    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",%d\r\n%s\r\n",
                                                        usDataLen,
                                                        pData); 
                }
                else
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+CMGL:%d,\"%s\",\"%s\",%s,\"%s\"\r\n%s\r\n",
                                                        pCmglInd->ucIndex,
                                                        aucState,
                                                        pOa,
                                                        aucAlpha,
                                                        pCmglInd->u.stTextCmd.aucScts,
                                                        pData);  
                }
            
            }
            else if(((pCmglInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_COMMMAND) && (pCmglInd->ucState==D_ATC_STAT_UNSENT || pCmglInd->ucState==D_ATC_STAT_SENT))
            {
                g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                (const unsigned char *)"\r\n+CMGL:%d,\"%s\",%d,%d\r\n",
                pCmglInd->ucIndex,
                aucState,
                pCmglInd->u.stTextCmd.ucFo,
                pCmglInd->u.stTextCmd.ucCt);
            }
            else if(((pCmglInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_STATUS_REPORT) && (pCmglInd->ucState==D_ATC_STAT_UNREAD || pCmglInd->ucState==D_ATC_STAT_READ))
            {
                g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                (const unsigned char *)"\r\n+CMGL:%d,\"%s\",%d,%d,\"%s\",%d,\"%s\",\"%s\",%d\r\n",
                pCmglInd->ucIndex,
                aucState,
                pCmglInd->u.stTextCmd.ucFo,
                pCmglInd->u.stTextCmd.ucMr,
                pOa,
                pCmglInd->u.stTextCmd.ucTooa,
                pCmglInd->u.stTextCmd.aucScts,
                pCmglInd->u.stTextCmd.aucDt,
                pCmglInd->u.stTextCmd.ucSt); 
            }  

            if(NULL!=pData)
            {
                AtcAp_Free(pData);
            }
        }

        if(NULL!=pOa)
        {
            AtcAp_Free(pOa);
        }
    }
    AtcAp_SendLongDataInd(pRecvMsg, &pRespBuff, usMsgLen);
    AtcAp_Free(pRespBuff);
    //AtcAp_SendDataInd(pEventBuffer);
}

void AtcAp_MsgProc_CMGF_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CMGF_CNF_STRU*    pCmgfCnf = (ATC_MSG_CMGF_CNF_STRU*)pRecvMsg;
    g_SmsFormatMode = pCmgfCnf->ucValue;
}

void AtcAp_MsgProc_CMGF_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CMGF_R_CNF_STRU*    pCmgfRCnf = (ATC_MSG_CMGF_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CMGF:%d\r\n", pCmgfRCnf->ucValue);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CPMS_Cnf(unsigned char* pRecvMsg)
{
    unsigned char str[100] = {0};
    unsigned char ucstrLen = 0;
    ATC_MSG_CPMS_CNF_STRU*    pCpmsCnf = (ATC_MSG_CPMS_CNF_STRU*)pRecvMsg;

    ucstrLen += sprintf(str+ucstrLen,"\r\n+CPMS:");
    if(pCpmsCnf->ucMem1 == 1)
    {
       ucstrLen += sprintf(str+ucstrLen,"%d,%d,",pCpmsCnf->ucSmMemUsed,pCpmsCnf->ucSmMemTotal);
    }
    else
    {
        ucstrLen += sprintf(str+ucstrLen,"%d,%d,",pCpmsCnf->ucMeMemUsed,pCpmsCnf->ucMeMemTotal);
    }

    if(pCpmsCnf->ucMem2 == 1)
    {
       ucstrLen += sprintf(str+ucstrLen,"%d,%d,",pCpmsCnf->ucSmMemUsed,pCpmsCnf->ucSmMemTotal);
    }
    else
    {
        ucstrLen += sprintf(str+ucstrLen,"%d,%d,",pCpmsCnf->ucMeMemUsed,pCpmsCnf->ucMeMemTotal);
    }

    if(pCpmsCnf->ucMem3 == 1)
    {
       ucstrLen += sprintf(str+ucstrLen,"%d,%d",pCpmsCnf->ucSmMemUsed,pCpmsCnf->ucSmMemTotal);
    }
    else
    {
        ucstrLen += sprintf(str+ucstrLen,"%d,%d",pCpmsCnf->ucMeMemUsed,pCpmsCnf->ucMeMemTotal);
    }
    ucstrLen += sprintf(str+ucstrLen,"\r\n");
    AtcAp_StrPrintf_AtcRspBuf((const char *)str);  
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CPMS_R_Cnf(unsigned char* pRecvMsg)
{
    unsigned char str[100] = {0};
    unsigned char ucstrLen = 0;
    ATC_MSG_CPMS_R_CNF_STRU*    pCpmsRCnf = (ATC_MSG_CPMS_R_CNF_STRU*)pRecvMsg;

    ucstrLen += sprintf(str+ucstrLen,"\r\n+CPMS:");
    if(pCpmsRCnf->ucMem1 == 1)
    {
       ucstrLen += sprintf(str+ucstrLen,"\"SM\",%d,%d,",pCpmsRCnf->ucSmMemUsed,pCpmsRCnf->ucSmMemTotal);
    }
    else
    {
        ucstrLen += sprintf(str+ucstrLen,"\"ME\",%d,%d,",pCpmsRCnf->ucMeMemUsed,pCpmsRCnf->ucMeMemTotal);
    }

    if(pCpmsRCnf->ucMem2 == 1)
    {
       ucstrLen += sprintf(str+ucstrLen,"\"SM\",%d,%d,",pCpmsRCnf->ucSmMemUsed,pCpmsRCnf->ucSmMemTotal);
    }
    else
    {
        ucstrLen += sprintf(str+ucstrLen,"\"ME\",%d,%d,",pCpmsRCnf->ucMeMemUsed,pCpmsRCnf->ucMeMemTotal);
    }

    if(pCpmsRCnf->ucMem3 == 1)
    {
       ucstrLen += sprintf(str+ucstrLen,"\"SM\",%d,%d",pCpmsRCnf->ucSmMemUsed,pCpmsRCnf->ucSmMemTotal);
    }
    else
    {
        ucstrLen += sprintf(str+ucstrLen,"\"ME\",%d,%d",pCpmsRCnf->ucMeMemUsed,pCpmsRCnf->ucMeMemTotal);
    }
    ucstrLen += sprintf(str+ucstrLen,"\r\n");
    AtcAp_StrPrintf_AtcRspBuf((const char *)str);  
    AtcAp_SendDataInd(pRecvMsg);
}


void AtcAp_MsgProc_CPMS_T_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CPMS_T_CNF_STRU*    pCpmsTCnf = (ATC_MSG_CPMS_T_CNF_STRU*)pRecvMsg;

    if(pCpmsTCnf->ucValue == 1)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CPMS:(\"SM\",\"ME\"),(\"SM\",\"ME\"),(\"SM\",\"ME\")\r\n");
    }
    else
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CPMS:(\"SM\"),(\"SM\"),(\"SM\")\r\n");  
    }
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CSCA_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CSCA_R_CNF_STRU*    pCscaRCnf      = (ATC_MSG_CSCA_R_CNF_STRU*)pRecvMsg;
    unsigned char               aucScaData[32] = { 0 };
    unsigned char               aucShowScaData[32] = { 0 };
    unsigned char               *pSca=NULL;


    AtcAp_CSCA_ConvertScaByte2Str(pCscaRCnf->aucSca, pCscaRCnf->ucScaLen, aucScaData);
    if(0x90 == (pCscaRCnf->ucToSca & 0xF0))
    {
        aucShowScaData[0]='+';
        AtcAp_MemCpy(&aucShowScaData[1],aucScaData,strlen(aucScaData));
    }
    else
    {
        AtcAp_MemCpy(aucShowScaData,aucScaData,strlen(aucScaData));
    }
    if(pCscaRCnf->ucSelectCharSet == D_CHAR_SET_SELETE_HEX)
    {
        g_AtcApInfo.stAtRspInfo.ucHexStrLen = strlen(aucShowScaData);
        if(0 == pCscaRCnf->ucToSca)
        {
            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                                        (const unsigned char *)"\r\n+CSCA:\"%x\"\r\n",
                                        aucShowScaData);
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                                        (const unsigned char *)"\r\n+CSCA:\"%x\",%d\r\n",
                                        aucShowScaData,
                                        pCscaRCnf->ucToSca);
        } 
    }
    else
    {
        if(pCscaRCnf->ucSelectCharSet == D_CHAR_SET_SELETE_GSM||pCscaRCnf->ucSelectCharSet == D_CHAR_SET_SELETE_IRA)
        {
            pSca = (unsigned char*)AtcAp_Malloc(strlen(aucShowScaData)+1);
            AtcAp_MemCpy(pSca,aucShowScaData,strlen(aucShowScaData));
        }
        else if(pCscaRCnf->ucSelectCharSet == D_CHAR_SET_SELETE_UCS2)
        {
            pSca = (unsigned char*)AtcAp_Malloc(4*strlen(aucShowScaData)+1);
            AtcAp_ConvertGSM2UCS2(pSca,aucShowScaData,strlen(aucShowScaData));
        }  
        if(0 == pCscaRCnf->ucToSca)
        {
            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                                        (const unsigned char *)"\r\n+CSCA:\"%s\"\r\n",
                                        pSca);
        }
        else
        {
            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
                                        (const unsigned char *)"\r\n+CSCA:\"%s\",%d\r\n",
                                        pSca,
                                        pCscaRCnf->ucToSca);
        } 
        if(NULL!=pSca)
        {
            AtcAp_Free(pSca);
        }
    }
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CSMP_R_Cnf(unsigned char* pRecvMsg)
{
    unsigned char ucVpStringFormat = 0;
    unsigned char ucVp[D_ATC_P_CSMP_IND_VP_SIZE_MAX+1] = {0};    
    ATC_MSG_CSMP_R_CNF_STRU*    pCsmpRCnf      = (ATC_MSG_CSMP_R_CNF_STRU*)pRecvMsg;
    if(pCsmpRCnf->ucVpLen == D_ATC_P_CSMP_IND_VP_SIZE_MAX)
    {
        ucVpStringFormat =1;  
    }
    if(ucVpStringFormat == 1)
    {
        AtcAp_MemCpy(ucVp,pCsmpRCnf->ucVp,D_ATC_P_CSMP_IND_VP_SIZE_MAX);
        ucVp[D_ATC_P_CSMP_IND_VP_SIZE_MAX] = 0;
        AtcAp_StrPrintf_AtcRspBuf("\r\n+CSMP:%d,\"%s\",%d,%d\r\n", pCsmpRCnf->ucFo,ucVp,pCsmpRCnf->ucPid,pCsmpRCnf->ucDcs);
    }
    else
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+CSMP:%d,%d,%d,%d\r\n", pCsmpRCnf->ucFo,pCsmpRCnf->ucVp[0],pCsmpRCnf->ucPid,pCsmpRCnf->ucDcs);
    }
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CNMI_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CNMI_R_CNF_STRU*    pCnmiRCnf      = (ATC_MSG_CNMI_R_CNF_STRU*)pRecvMsg;
    AtcAp_StrPrintf_AtcRspBuf("\r\n+CNMI:%d,%d,%d,%d,%d\r\n", pCnmiRCnf->ucMode,pCnmiRCnf->ucMt,pCnmiRCnf->ucBm,pCnmiRCnf->ucDs,pCnmiRCnf->ucBfr);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CMMS_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CMMS_R_CNF_STRU*    pCmmsRCnf = (ATC_MSG_CMMS_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+CMMS:%d\r\n", pCmmsRCnf->ucCmmsN);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CMGS_Cnf(unsigned char* pRecvMsg)
{
    char aucScts[32] = {0};
    unsigned char ucRaLen = 0;
    ATC_MSG_CMGS_CNF_STRU*    pCmgsInd = (ATC_MSG_CMGS_CNF_STRU*)pRecvMsg;

    if (0 == pCmgsInd->ucTpduLen)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CMGS:%d\r\n", pCmgsInd->ucTpmr);
    }
    else
    {
        if(g_SmsFormatMode == 0)
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmgsInd->ucTpduLen;
            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
            (const unsigned char *)"\r\n+CMGS:%d,%x\r\n", pCmgsInd->ucTpmr, pCmgsInd->aucTpdu);
        }
        else
        {
           AtcAp_Tpdu_DecodeScts(pCmgsInd->aucTpdu,pCmgsInd->ucTpduLen,aucScts);
           if(aucScts != NULL && pCmgsInd->usMsgService == 1)
           {

                AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CMGS:%d,\"%s\"\r\n", pCmgsInd->ucTpmr,aucScts);
           }
           else
           {
                AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CMGS:%d\r\n", pCmgsInd->ucTpmr);
           }   
        }
    }
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CMSS_Cnf(unsigned char* pRecvMsg)
{
    char aucScts[32] = {0};
    unsigned char ucRaLen = 0;
    ATC_MSG_CMSS_CNF_STRU*    pCmssInd = (ATC_MSG_CMSS_CNF_STRU*)pRecvMsg;

    if (0 == pCmssInd->ucTpduLen)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CMSS:%d\r\n", pCmssInd->ucTpmr);
    }
    else
    {
        if(g_SmsFormatMode == 0)
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = pCmssInd->ucTpduLen;
            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
            (const unsigned char *)"\r\n+CMSS:%d,%x\r\n", pCmssInd->ucTpmr, pCmssInd->aucTpdu);
        }
        else
        {
           AtcAp_Tpdu_DecodeScts(pCmssInd->aucTpdu,pCmssInd->ucTpduLen,aucScts);
           if(aucScts != NULL && pCmssInd->usMsgService == 1)
           {

                AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CMSS:%d,\"%s\"\r\n", pCmssInd->ucTpmr,aucScts);
           }
           else
           {
                AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CMSS:%d\r\n", pCmssInd->ucTpmr);
           }   
        }
    }
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_SMS_PDU_Ind(unsigned char* pRecvMsg)
{
    if(g_AtcApInfo.ucTempSeqNum != 0)
    {
        AtcAp_AppInterfaceInfo_CmdRstProc(g_AtcApInfo.ucTempSeqNum, D_APP_INTERFACE_RESULT_SUCC);
        g_AtcApInfo.ucTempSeqNum = 0;
    }
    else
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n> ");
        AtcAp_SendDataInd(pRecvMsg);
    }
}

void AtcAp_MsgProc_CSCB_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CSCB_R_CNF_STRU*    pCnf = (ATC_MSG_CSCB_R_CNF_STRU*)pRecvMsg;
    
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CSCB:%d\r\n", pCnf->ucMode);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CSDH_R_Cnf(unsigned char* pRecvMsg)
{
     ATC_MSG_CSDH_R_CNF_STRU*    pCnf = (ATC_MSG_CSDH_R_CNF_STRU*)pRecvMsg;
    
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CSDH:%d\r\n", pCnf->ucShow);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QCMGS_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QCMGS_CNF_STRU*    pCnf = (ATC_MSG_QCMGS_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QCMGS:%d\r\n", pCnf->ucTpmr);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QCMGR_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QCMGR_CNF_STRU*    pQcmgrInd = (ATC_MSG_QCMGR_CNF_STRU*)pRecvMsg;
    unsigned char aucState[11] = {0};
    //unsigned char ucAlpha[16] = {0};
    unsigned char *pOa = NULL;
    unsigned char *pSca = NULL;
    unsigned char *pData = NULL;
    unsigned char* pRespBuff;
    unsigned short usMsgLen = 1024;
    unsigned short usDataLen = 0;

    pRespBuff = (unsigned char*)AtcAp_Malloc(usMsgLen);
    if(NULL == pRespBuff)
    {
        return;
    }
    if(pQcmgrInd->ucShowMode == 0)//SMS_SHOW_PDU
    {
        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stPduCmd.ucDataLen;
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
        (const unsigned char *)"\r\n+QCMGR:%d,,%d\r\n%x\r\n",pQcmgrInd->ucState,pQcmgrInd->u.stPduCmd.ucPduLen, pQcmgrInd->u.stPduCmd.aucData);  
    }
    else
    {
        memset(aucState,0,sizeof(aucState));
        if(pQcmgrInd->ucState == D_ATC_STAT_SENT)
        {
            AtcAp_MemCpy(aucState,"STO SENT",8);
        }
        else if(pQcmgrInd->ucState == D_ATC_STAT_UNSENT)
        {
            AtcAp_MemCpy(aucState,"STO UNSENT",10);
        }
        else if(pQcmgrInd->ucState == D_ATC_STAT_READ)
        {
            AtcAp_MemCpy(aucState,"REC READ",8);
        }
        else if(pQcmgrInd->ucState == D_ATC_STAT_UNREAD)
        {
            AtcAp_MemCpy(aucState,"REC UNREAD",10);
        }

        if(pQcmgrInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_HEX)
        {
            pOa = (unsigned char*)AtcAp_Malloc(2*strlen(pQcmgrInd->u.stTextCmd.aucOa)+1);
            AtcAp_ConvertGSM2HEX(pOa,pQcmgrInd->u.stTextCmd.aucOa,strlen(pQcmgrInd->u.stTextCmd.aucOa));

            pSca = (unsigned char*)AtcAp_Malloc(2*strlen(pQcmgrInd->u.stTextCmd.aucSca)+1);
            AtcAp_ConvertGSM2HEX(pSca,pQcmgrInd->u.stTextCmd.aucSca,strlen(pQcmgrInd->u.stTextCmd.aucSca));
        }
        else if(pQcmgrInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_UCS2)
        {
            pOa = (unsigned char*)AtcAp_Malloc(4*strlen(pQcmgrInd->u.stTextCmd.aucOa)+1);
            AtcAp_ConvertGSM2UCS2(pOa,pQcmgrInd->u.stTextCmd.aucOa,strlen(pQcmgrInd->u.stTextCmd.aucOa));

            pSca = (unsigned char*)AtcAp_Malloc(4*strlen(pQcmgrInd->u.stTextCmd.aucSca)+1);
            AtcAp_ConvertGSM2UCS2(pSca,pQcmgrInd->u.stTextCmd.aucSca,strlen(pQcmgrInd->u.stTextCmd.aucSca));
        }
        else //GSM or IRA
        {
            pOa = (unsigned char*)AtcAp_Malloc(strlen(pQcmgrInd->u.stTextCmd.aucOa)+1);
            AtcAp_MemCpy(pOa,pQcmgrInd->u.stTextCmd.aucOa,strlen(pQcmgrInd->u.stTextCmd.aucOa));

            pSca = (unsigned char*)AtcAp_Malloc(strlen(pQcmgrInd->u.stTextCmd.aucSca)+1);
            AtcAp_MemCpy(pSca,pQcmgrInd->u.stTextCmd.aucSca,strlen(pQcmgrInd->u.stTextCmd.aucSca));
        }

        if(pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum == 0 || pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum==0)
        {
            if(pQcmgrInd->u.stTextCmd.ucSelectCharSet==D_CHAR_SET_SELETE_HEX 
                || ((pQcmgrInd->u.stTextCmd.ucFo&0x40) == 0x40) 
                || (((pQcmgrInd->u.stTextCmd.ucDcs) & 0x0C) != 0 && (((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_DELIVER) || ((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_SUBMIT))))
            {
                usDataLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                if(8 == pQcmgrInd->u.stTextCmd.ucDcs)
                {
                    usDataLen = pQcmgrInd->u.stTextCmd.ucDataLen / 2;
                }
                if((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_DELIVER)
                {
                    if(1 == pQcmgrInd->u.stTextCmd.ucShowHeadFlg)
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                    (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,\"%s\",%d,%d,%d,%d",
                                                    aucState,
                                                    pOa,
                                                    pQcmgrInd->u.stTextCmd.aucScts,
                                                    pQcmgrInd->u.stTextCmd.ucTooa,
                                                    pQcmgrInd->u.stTextCmd.ucFo,
                                                    pQcmgrInd->u.stTextCmd.ucPid,
                                                    pQcmgrInd->u.stTextCmd.ucDcs);
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",\"%s\",%d",
                                                    pSca,
                                                    pQcmgrInd->u.stTextCmd.ucToSca);
                        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",%d\r\n%x\r\n",
                                                    usDataLen,
                                                    pQcmgrInd->u.stTextCmd.aucData); 
                    }
                    else
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                    (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,\"%s\"\r\n",
                                                    aucState,
                                                    pOa,
                                                    pQcmgrInd->u.stTextCmd.aucScts);
                        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)"%x\r\n",
                                                    pQcmgrInd->u.stTextCmd.aucData);                 
                    }
                }
                else if((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_SUBMIT)
                {
                    if(1 == pQcmgrInd->u.stTextCmd.ucShowHeadFlg)
                    {
                        if(((pQcmgrInd->u.stTextCmd.ucFo&0x18)>>3) == VP_ABSOLUTE_FORMAT)
                        {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,%d,%d,%d,%d,\"%s\"",
                                                        aucState,
                                                        pOa,
                                                        pQcmgrInd->u.stTextCmd.ucTooa,
                                                        pQcmgrInd->u.stTextCmd.ucFo,
                                                        pQcmgrInd->u.stTextCmd.ucPid,
                                                        pQcmgrInd->u.stTextCmd.ucDcs,
                                                        pQcmgrInd->u.stTextCmd.aucVp);
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",\"%s\",%d",
                                                        pSca,
                                                        pQcmgrInd->u.stTextCmd.ucToSca);
                            g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",%d\r\n%x\r\n",
                                                        usDataLen,
                                                        pQcmgrInd->u.stTextCmd.aucData);
                        }
                        else if(((pQcmgrInd->u.stTextCmd.ucFo&0x18)>>3) == VP_RELATIVE_FORMAT)
                        {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,%d,%d,%d,%d,%d",
                                                        aucState,
                                                        pOa,
                                                        pQcmgrInd->u.stTextCmd.ucTooa,
                                                        pQcmgrInd->u.stTextCmd.ucFo,
                                                        pQcmgrInd->u.stTextCmd.ucPid,
                                                        pQcmgrInd->u.stTextCmd.ucDcs,
                                                        pQcmgrInd->u.stTextCmd.aucVp[0]);
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",\"%s\",%d",
                                                        pSca,
                                                        pQcmgrInd->u.stTextCmd.ucToSca);
                            g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",%d\r\n%x\r\n",
                                                        usDataLen,
                                                        pQcmgrInd->u.stTextCmd.aucData);
                        }
                        else
                        {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,%d,%d,%d,%d",
                                                        aucState,
                                                        pOa,
                                                        pQcmgrInd->u.stTextCmd.ucTooa,
                                                        pQcmgrInd->u.stTextCmd.ucFo,
                                                        pQcmgrInd->u.stTextCmd.ucPid,
                                                        pQcmgrInd->u.stTextCmd.ucDcs);
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",,\"%s\",%d",
                                                        pSca,
                                                        pQcmgrInd->u.stTextCmd.ucToSca);
                            g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",%d\r\n%x\r\n",
                                                        usDataLen,
                                                        pQcmgrInd->u.stTextCmd.aucData);
                        }
                    }
                    else
                    {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",\r\n",
                                                        aucState,
                                                        pOa);
                            g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)"%x\r\n",
                                                        pQcmgrInd->u.stTextCmd.aucData);  
                    }
                    
                }
                else if(((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_COMMMAND) && (pQcmgrInd->ucState==D_ATC_STAT_UNSENT || pQcmgrInd->ucState==D_ATC_STAT_SENT))
                {
                    if(1 == pQcmgrInd->u.stTextCmd.ucShowHeadFlg)
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                    (const unsigned char *)"\r\n+QCMGR:\"%s\",%d,%d,%d,%d,\"%s\",%d",
                                                    aucState,
                                                    pQcmgrInd->u.stTextCmd.ucFo,
                                                    pQcmgrInd->u.stTextCmd.ucCt,
                                                    pQcmgrInd->u.stTextCmd.ucPid,
                                                    pQcmgrInd->u.stTextCmd.ucMn,
                                                    pOa,
                                                    pQcmgrInd->u.stTextCmd.ucTooa);
                        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",%d\r\n%x\r\n",
                                                    usDataLen,
                                                    pQcmgrInd->u.stTextCmd.aucData);
                    }
                    else
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                        (const unsigned char *)"\r\n+QCMGR:\"%s\",%d,%d\r\n",
                        aucState,
                        pQcmgrInd->u.stTextCmd.ucFo,
                        pQcmgrInd->u.stTextCmd.ucCt);        
                    }
                }
                else if(((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_STATUS_REPORT) && (pQcmgrInd->ucState==D_ATC_STAT_UNREAD || pQcmgrInd->ucState==D_ATC_STAT_READ))
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                    (const unsigned char *)"\r\n+QCMGR:\"%s\",%d,%d,\"%s\",%d,\"%s\",\"%s\",%d\r\n",
                    aucState,
                    pQcmgrInd->u.stTextCmd.ucFo,
                    pQcmgrInd->u.stTextCmd.ucMr,
                    pOa,
                    pQcmgrInd->u.stTextCmd.ucTooa,
                    pQcmgrInd->u.stTextCmd.aucScts,
                    pQcmgrInd->u.stTextCmd.aucDt,
                    pQcmgrInd->u.stTextCmd.ucSt); 
                }
            }
            else
            {
                //AtcAp_CharSet_Val2Str(pQcmgrInd->u.stTextCmd.ucSelectCharSet,ucAlpha);
                usDataLen=(unsigned short)pQcmgrInd->u.stTextCmd.ucDataLen;
                if(pQcmgrInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_GSM||pQcmgrInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_IRA)
                {
                    pData = (unsigned char*)AtcAp_Malloc(pQcmgrInd->u.stTextCmd.ucDataLen+1);
                    AtcAp_MemCpy(pData,pQcmgrInd->u.stTextCmd.aucData,pQcmgrInd->u.stTextCmd.ucDataLen);
                }
                else if(pQcmgrInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_UCS2)
                {
                    pData = (unsigned char*)AtcAp_Malloc(4*pQcmgrInd->u.stTextCmd.ucDataLen+1);
                    AtcAp_ConvertGSM2UCS2(pData,pQcmgrInd->u.stTextCmd.aucData,pQcmgrInd->u.stTextCmd.ucDataLen);
                    usDataLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                }       
                if((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_DELIVER)
                {
                    if(1 == pQcmgrInd->u.stTextCmd.ucShowHeadFlg)
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,\"%s\",%d,%d,%d,%d,\"%s\",%d,%d\r\n%s\r\n",
                                                        aucState,
                                                        pOa,
                                                        //ucAlpha,
                                                        pQcmgrInd->u.stTextCmd.aucScts,
                                                        pQcmgrInd->u.stTextCmd.ucTooa,
                                                        pQcmgrInd->u.stTextCmd.ucFo,
                                                        pQcmgrInd->u.stTextCmd.ucPid,
                                                        pQcmgrInd->u.stTextCmd.ucDcs,
                                                        pSca,
                                                        pQcmgrInd->u.stTextCmd.ucToSca,
                                                        usDataLen,
                                                        pData);
                    }
                    else
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,\"%s\"\r\n%s\r\n",
                                                        aucState,
                                                        pOa,
                                                        //ucAlpha,
                                                        pQcmgrInd->u.stTextCmd.aucScts,                    
                                                        pData);                
                    }
                }
                else if((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_SUBMIT)
                {
                    if(1 == pQcmgrInd->u.stTextCmd.ucShowHeadFlg)
                    {
                        if(((pQcmgrInd->u.stTextCmd.ucFo&0x18)>>3) == VP_ABSOLUTE_FORMAT)
                        {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                            (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,%d,%d,%d,%d,\"%s\",\"%s\",%d,%d\r\n%s\r\n",
                            aucState,
                            pOa,
                            //ucAlpha,
                            pQcmgrInd->u.stTextCmd.ucTooa,
                            pQcmgrInd->u.stTextCmd.ucFo,
                            pQcmgrInd->u.stTextCmd.ucPid,
                            pQcmgrInd->u.stTextCmd.ucDcs,
                            pQcmgrInd->u.stTextCmd.aucVp,
                            pSca,
                            pQcmgrInd->u.stTextCmd.ucToSca,
                            usDataLen,
                            pData); 
                        }
                        else if(((pQcmgrInd->u.stTextCmd.ucFo&0x18)>>3) == VP_RELATIVE_FORMAT)
                        {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                            (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,%d,%d,%d,%d,%d,\"%s\",%d,%d\r\n%s\r\n",
                            aucState,
                            pOa,
                            //ucAlpha,
                            pQcmgrInd->u.stTextCmd.ucTooa,
                            pQcmgrInd->u.stTextCmd.ucFo,
                            pQcmgrInd->u.stTextCmd.ucPid,
                            pQcmgrInd->u.stTextCmd.ucDcs,
                            pQcmgrInd->u.stTextCmd.aucVp[0],
                            pSca,
                            pQcmgrInd->u.stTextCmd.ucToSca,
                            usDataLen,
                            pData); 
                        }
                        else
                        {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                            (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,%d,%d,%d,%d,,\"%s\",%d,%d\r\n%s\r\n",
                            aucState,
                            pOa,
                            //ucAlpha,
                            pQcmgrInd->u.stTextCmd.ucTooa,
                            pQcmgrInd->u.stTextCmd.ucFo,
                            pQcmgrInd->u.stTextCmd.ucPid,
                            pQcmgrInd->u.stTextCmd.ucDcs,
                            pSca,
                            pQcmgrInd->u.stTextCmd.ucToSca,
                            usDataLen,
                            pData); 
                        }
                    }
                    else
                    {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                            (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",\r\n%s\r\n",
                            aucState,
                            pOa,
                            //ucAlpha,
                            pData);   
                    }
                    
                }
                else if(((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_COMMMAND) && (pQcmgrInd->ucState==D_ATC_STAT_UNSENT || pQcmgrInd->ucState==D_ATC_STAT_SENT))
                {
                    if(1 == pQcmgrInd->u.stTextCmd.ucShowHeadFlg)
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                        (const unsigned char *)"\r\n+QCMGR:\"%s\",%d,%d,%d,%d,\"%s\",%d,%d\r\n%x\r\n",
                        aucState,
                        pQcmgrInd->u.stTextCmd.ucFo,
                        pQcmgrInd->u.stTextCmd.ucCt,
                        pQcmgrInd->u.stTextCmd.ucPid,
                        pQcmgrInd->u.stTextCmd.ucMn,
                        pOa,
                        pQcmgrInd->u.stTextCmd.ucTooa,
                        usDataLen,
                        pData);
                    }
                    else
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                        (const unsigned char *)"\r\n+QCMGR:\"%s\",%d,%d\r\n",
                        aucState,
                        pQcmgrInd->u.stTextCmd.ucFo,
                        pQcmgrInd->u.stTextCmd.ucCt);        
                    }
                }
                else if(((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_STATUS_REPORT) && (pQcmgrInd->ucState==D_ATC_STAT_UNREAD || pQcmgrInd->ucState==D_ATC_STAT_READ))
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                    (const unsigned char *)"\r\n+QCMGR:\"%s\",%d,%d,\"%s\",%d,\"%s\",\"%s\",%d\r\n",
                    aucState,
                    pQcmgrInd->u.stTextCmd.ucFo,
                    pQcmgrInd->u.stTextCmd.ucMr,
                    pOa,
                    pQcmgrInd->u.stTextCmd.ucTooa,
                    pQcmgrInd->u.stTextCmd.aucScts,
                    pQcmgrInd->u.stTextCmd.aucDt,
                    pQcmgrInd->u.stTextCmd.ucSt); 
                }    
                if(NULL!=pData)
                {
                    AtcAp_Free(pData);
                }
            }
        }
        else
        {
            if(pQcmgrInd->u.stTextCmd.ucSelectCharSet==D_CHAR_SET_SELETE_HEX 
                || (((pQcmgrInd->u.stTextCmd.ucDcs) & 0x0C) != 0 && (((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_DELIVER) || ((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_SUBMIT))))
            {
                usDataLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                if(8 == pQcmgrInd->u.stTextCmd.ucDcs)
                {
                    usDataLen = pQcmgrInd->u.stTextCmd.ucDataLen / 2;
                }
                if((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_DELIVER)
                {
                    if(1 == pQcmgrInd->u.stTextCmd.ucShowHeadFlg)
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                    (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,\"%s\",%d,%d,%d,%d",
                                                    aucState,
                                                    pOa,
                                                    pQcmgrInd->u.stTextCmd.aucScts,
                                                    pQcmgrInd->u.stTextCmd.ucTooa,
                                                    pQcmgrInd->u.stTextCmd.ucFo,
                                                    pQcmgrInd->u.stTextCmd.ucPid,
                                                    pQcmgrInd->u.stTextCmd.ucDcs);
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",\"%s\",%d",
                                                    pSca,
                                                    pQcmgrInd->u.stTextCmd.ucToSca);
                        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",%d,%d,%d,%d\r\n%x\r\n",
                                                    usDataLen,
                                                    pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                                                    pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                                                    pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                                                    pQcmgrInd->u.stTextCmd.aucData);
                    }
                    else
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                    (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,\"%s\"",
                                                    aucState,
                                                    pOa,
                                                    pQcmgrInd->u.stTextCmd.aucScts);
                        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",%d,%d,%d\r\n%x\r\n",
                                                    pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                                                    pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                                                    pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                                                    pQcmgrInd->u.stTextCmd.aucData);              
                    }
                }
                else if((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_SUBMIT)
                {
                    if(1 == pQcmgrInd->u.stTextCmd.ucShowHeadFlg)
                    {
                        if(((pQcmgrInd->u.stTextCmd.ucFo&0x18)>>3) == VP_ABSOLUTE_FORMAT)
                        {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,%d,%d,%d,%d,\"%s\"",
                                                        aucState,
                                                        pOa,
                                                        pQcmgrInd->u.stTextCmd.ucTooa,
                                                        pQcmgrInd->u.stTextCmd.ucFo,
                                                        pQcmgrInd->u.stTextCmd.ucPid,
                                                        pQcmgrInd->u.stTextCmd.ucDcs,
                                                        pQcmgrInd->u.stTextCmd.aucVp);
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",\"%s\",%d",
                                                        pSca,
                                                        pQcmgrInd->u.stTextCmd.ucToSca);
                            g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",%d,%d,%d,%d\r\n%x\r\n",
                                                        usDataLen,
                                                        pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                                                        pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                                                        pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                                                        pQcmgrInd->u.stTextCmd.aucData);  
                        }
                        else if(((pQcmgrInd->u.stTextCmd.ucFo&0x18)>>3) == VP_RELATIVE_FORMAT)
                        {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,%d,%d,%d,%d,%d",
                                                        aucState,
                                                        pOa,
                                                        pQcmgrInd->u.stTextCmd.ucTooa,
                                                        pQcmgrInd->u.stTextCmd.ucFo,
                                                        pQcmgrInd->u.stTextCmd.ucPid,
                                                        pQcmgrInd->u.stTextCmd.ucDcs,
                                                        pQcmgrInd->u.stTextCmd.aucVp[0]);
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",\"%s\",%d",
                                                        pSca,
                                                        pQcmgrInd->u.stTextCmd.ucToSca);
                            g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",%d,%d,%d,%d\r\n%x\r\n",
                                                        usDataLen,
                                                        pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                                                        pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                                                        pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                                                        pQcmgrInd->u.stTextCmd.aucData);
                        }
                        else
                        {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,%d,%d,%d,%d",
                                                        aucState,
                                                        pOa,
                                                        pQcmgrInd->u.stTextCmd.ucTooa,
                                                        pQcmgrInd->u.stTextCmd.ucFo,
                                                        pQcmgrInd->u.stTextCmd.ucPid,
                                                        pQcmgrInd->u.stTextCmd.ucDcs);
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",,\"%s\",%d",
                                                        pSca,
                                                        pQcmgrInd->u.stTextCmd.ucToSca);
                            g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                        (const unsigned char *)",%d,%d,%d,%d\r\n%x\r\n",
                                                        usDataLen,
                                                        pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                                                        pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                                                        pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                                                        pQcmgrInd->u.stTextCmd.aucData);
                        }
                    }
                    else
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                    (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",",
                                                    aucState,
                                                    pOa);
                        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",%d,%d,%d\r\n%x\r\n",
                                                    pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                                                    pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                                                    pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                                                    pQcmgrInd->u.stTextCmd.aucData); 
                    }
                    
                }
                else if(((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_COMMMAND) && (pQcmgrInd->ucState==D_ATC_STAT_UNSENT || pQcmgrInd->ucState==D_ATC_STAT_SENT))
                {
                    if(1 == pQcmgrInd->u.stTextCmd.ucShowHeadFlg)
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                    (const unsigned char *)"\r\n+QCMGR:\"%s\",%d,%d,%d,%d,\"%s\",%d",
                                                    aucState,
                                                    pQcmgrInd->u.stTextCmd.ucFo,
                                                    pQcmgrInd->u.stTextCmd.ucCt,
                                                    pQcmgrInd->u.stTextCmd.ucPid,
                                                    pQcmgrInd->u.stTextCmd.ucMn,
                                                    pOa,
                                                    pQcmgrInd->u.stTextCmd.ucTooa);
                        g_AtcApInfo.stAtRspInfo.ucHexStrLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf(pRespBuff+g_AtcApInfo.stAtRspInfo.usRspLen,
                                                    (const unsigned char *)",%d,%d,%d,%d\r\n%x\r\n",
                                                    usDataLen,
                                                    pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                                                    pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                                                    pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                                                    pQcmgrInd->u.stTextCmd.aucData);
                    }
                    else
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                        (const unsigned char *)"\r\n+QCMGR:\"%s\",%d,%d,%d,%d,%d\r\n",
                        aucState,
                        pQcmgrInd->u.stTextCmd.ucFo,
                        pQcmgrInd->u.stTextCmd.ucCt,
                        pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                        pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                        pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum);        
                    }
                }
                else if(((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_STATUS_REPORT) && (pQcmgrInd->ucState==D_ATC_STAT_UNREAD || pQcmgrInd->ucState==D_ATC_STAT_READ))
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                (const unsigned char *)"\r\n+QCMGR:\"%s\",%d,%d,\"%s\",%d,\"%s\",\"%s\",%d,%d,%d,%d\r\n",
                                                aucState,
                                                pQcmgrInd->u.stTextCmd.ucFo,
                                                pQcmgrInd->u.stTextCmd.ucMr,
                                                pOa,
                                                pQcmgrInd->u.stTextCmd.ucTooa,
                                                pQcmgrInd->u.stTextCmd.aucScts,
                                                pQcmgrInd->u.stTextCmd.aucDt,
                                                pQcmgrInd->u.stTextCmd.ucSt,
                                                pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                                                pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                                                pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum);
                }
            }
            else
            {
                //AtcAp_CharSet_Val2Str(pQcmgrInd->u.stTextCmd.ucSelectCharSet,ucAlpha);
                usDataLen=(unsigned short)pQcmgrInd->u.stTextCmd.ucDataLen;
                if(pQcmgrInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_GSM||pQcmgrInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_IRA)
                {
                    pData = (unsigned char*)AtcAp_Malloc(pQcmgrInd->u.stTextCmd.ucDataLen+1);
                    AtcAp_MemCpy(pData,pQcmgrInd->u.stTextCmd.aucData,pQcmgrInd->u.stTextCmd.ucDataLen);
                }
                else if(pQcmgrInd->u.stTextCmd.ucSelectCharSet == D_CHAR_SET_SELETE_UCS2)
                {
                    pData = (unsigned char*)AtcAp_Malloc(4*pQcmgrInd->u.stTextCmd.ucDataLen+1);
                    AtcAp_ConvertGSM2UCS2(pData,pQcmgrInd->u.stTextCmd.aucData,pQcmgrInd->u.stTextCmd.ucDataLen);
                    usDataLen = pQcmgrInd->u.stTextCmd.ucDataLen;
                } 
                if((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_DELIVER)
                {
                    if(1 == pQcmgrInd->u.stTextCmd.ucShowHeadFlg)
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,\"%s\",%d,%d,%d,%d,\"%s\",%d,%d,%d,%d,%d\r\n%s\r\n",
                                                        aucState,
                                                        pOa,
                                                        //ucAlpha,
                                                        pQcmgrInd->u.stTextCmd.aucScts,
                                                        pQcmgrInd->u.stTextCmd.ucTooa,
                                                        pQcmgrInd->u.stTextCmd.ucFo,
                                                        pQcmgrInd->u.stTextCmd.ucPid,
                                                        pQcmgrInd->u.stTextCmd.ucDcs,
                                                        pSca,
                                                        pQcmgrInd->u.stTextCmd.ucToSca,
                                                        usDataLen,
                                                        pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                                                        pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                                                        pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                                                        pData);
                    }
                    else
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                                                        (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,\"%s\",%d,%d,%d\r\n%s\r\n",
                                                        aucState,
                                                        pOa,
                                                        //ucAlpha,
                                                        pQcmgrInd->u.stTextCmd.aucScts, 
                                                        pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                                                        pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                                                        pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                                                        pData);                
                    }
                }
                else if((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_SUBMIT)
                {
                    if(1 == pQcmgrInd->u.stTextCmd.ucShowHeadFlg)
                    {
                        if(((pQcmgrInd->u.stTextCmd.ucFo&0x18)>>3) == VP_ABSOLUTE_FORMAT)
                        {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                            (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,%d,%d,%d,%d,\"%s\",\"%s\",%d,%d,%d,%d,%d\r\n%s\r\n",
                            aucState,
                            pOa,
                            //ucAlpha,
                            pQcmgrInd->u.stTextCmd.ucTooa,
                            pQcmgrInd->u.stTextCmd.ucFo,
                            pQcmgrInd->u.stTextCmd.ucPid,
                            pQcmgrInd->u.stTextCmd.ucDcs,
                            pQcmgrInd->u.stTextCmd.aucVp,
                            pSca,
                            pQcmgrInd->u.stTextCmd.ucToSca,
                            usDataLen,
                            pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                            pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                            pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                            pData); 
                        }
                        else if(((pQcmgrInd->u.stTextCmd.ucFo&0x18)>>3) == VP_RELATIVE_FORMAT)
                        {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                            (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,%d,%d,%d,%d,%d,\"%s\",%d,%d,%d,%d,%d\r\n%s\r\n",
                            aucState,
                            pOa,
                            //ucAlpha,
                            pQcmgrInd->u.stTextCmd.ucTooa,
                            pQcmgrInd->u.stTextCmd.ucFo,
                            pQcmgrInd->u.stTextCmd.ucPid,
                            pQcmgrInd->u.stTextCmd.ucDcs,
                            pQcmgrInd->u.stTextCmd.aucVp[0],
                            pSca,
                            pQcmgrInd->u.stTextCmd.ucToSca,
                            usDataLen,
                            pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                            pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                            pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                            pData); 
                        }
                        else
                        {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                            (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,%d,%d,%d,%d,,\"%s\",%d,%d,%d,%d,%d\r\n%s\r\n",
                            aucState,
                            pOa,
                            //ucAlpha,
                            pQcmgrInd->u.stTextCmd.ucTooa,
                            pQcmgrInd->u.stTextCmd.ucFo,
                            pQcmgrInd->u.stTextCmd.ucPid,
                            pQcmgrInd->u.stTextCmd.ucDcs,
                            pSca,
                            pQcmgrInd->u.stTextCmd.ucToSca,
                            usDataLen,
                            pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                            pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                            pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                            pData); 
                        }
                    }
                    else
                    {
                            g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                            (const unsigned char *)"\r\n+QCMGR:\"%s\",\"%s\",,%d,%d,%d\r\n%s\r\n",
                            aucState,
                            pOa,
                            //ucAlpha,
                            pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                            pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                            pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                            pData);   
                    }
                    
                }
                else if(((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_COMMMAND) && (pQcmgrInd->ucState==D_ATC_STAT_UNSENT || pQcmgrInd->ucState==D_ATC_STAT_SENT))
                {
                    if(1 == pQcmgrInd->u.stTextCmd.ucShowHeadFlg)
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                        (const unsigned char *)"\r\n+QCMGR:\"%s\",%d,%d,%d,%d,\"%s\",%d,%d,%d,%d,%d\r\n%x\r\n",
                        aucState,
                        pQcmgrInd->u.stTextCmd.ucFo,
                        pQcmgrInd->u.stTextCmd.ucCt,
                        pQcmgrInd->u.stTextCmd.ucPid,
                        pQcmgrInd->u.stTextCmd.ucMn,
                        pOa,
                        pQcmgrInd->u.stTextCmd.ucTooa,
                        usDataLen,
                        pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                        pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                        pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum,
                        pData);
                    }
                    else
                    {
                        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                        (const unsigned char *)"\r\n+QCMGR:\"%s\",%d,%d,%d,%d,%d\r\n",
                        aucState,
                        pQcmgrInd->u.stTextCmd.ucFo,
                        pQcmgrInd->u.stTextCmd.ucCt,
                        pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                        pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                        pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum);        
                    }
                }
                else if(((pQcmgrInd->u.stTextCmd.ucFo&0x03) == TP_MTI_SMS_STATUS_REPORT) && (pQcmgrInd->ucState==D_ATC_STAT_UNREAD || pQcmgrInd->ucState==D_ATC_STAT_READ))
                {
                    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf(pRespBuff,
                    (const unsigned char *)"\r\n+QCMGR:\"%s\",%d,%d,\"%s\",%d,\"%s\",\"%s\",%d,%d,%d,%d\r\n",
                    aucState,
                    pQcmgrInd->u.stTextCmd.ucFo,
                    pQcmgrInd->u.stTextCmd.ucMr,
                    pOa,
                    pQcmgrInd->u.stTextCmd.ucTooa,
                    pQcmgrInd->u.stTextCmd.aucScts,
                    pQcmgrInd->u.stTextCmd.aucDt,
                    pQcmgrInd->u.stTextCmd.ucSt,
                    pQcmgrInd->u.stTextCmd.usConcatenatedReferenceNum,
                    pQcmgrInd->u.stTextCmd.ucConcatenatedSequenceNum,
                    pQcmgrInd->u.stTextCmd.ucMaxConcatenatedNum); 
                }    
                if(NULL!=pData)
                {
                    AtcAp_Free(pData);
                }
            }
        }

        if(NULL!=pOa)
        {
            AtcAp_Free(pOa);
        }
        if(NULL!=pSca)
        {
            AtcAp_Free(pSca);
        }
    }
    AtcAp_SendLongDataInd(pRecvMsg, &pRespBuff, usMsgLen);
    AtcAp_Free(pRespBuff);
    //AtcAp_SendDataInd(pEventBuffer);
}
#endif

void AtcAp_MsgProc_SIMST_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_SIMST_IND_STRU*    pSimstInd = (ATC_MSG_SIMST_IND_STRU*)pRecvMsg;

    if(g_softap_fac_nv->usim_state_rpt == 0)
    {
        return;
    }
#if USR_CUSTOM2
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n^SIMST:%d\r\n",pSimstInd->ucSimStatus);
    AtcAp_SendDataInd(pRecvMsg);
#else
    if(D_ATC_SIMST_NOT_PRESENT == pSimstInd->ucSimStatus)
    {
    #if VER_CM
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CPIN:SIM REMOVED\r\n");
    #else
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CPIN:NOT READY\r\n");
    #endif
        AtcAp_SendDataInd(pRecvMsg);
    }
    else if(D_ATC_SIMST_SUCC_INIT == pSimstInd->ucSimStatus)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CPIN:READY\r\n");
        AtcAp_SendDataInd(pRecvMsg);
    }
#endif
}
void AtcAp_MsgProc_CPIN_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CPIN_R_CNF_STRU* pCpinRCnf = (ATC_MSG_CPIN_R_CNF_STRU*)pRecvMsg;

    if(pCpinRCnf->ucPinStatus >= (sizeof(ATC_PinCodeTbl) / sizeof(ATC_PinCodeTbl[0])))
    {
        return;
    }

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CPIN:%s\r\n", ATC_PinCodeTbl[pCpinRCnf->ucPinStatus]);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CLCK_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CLCK_CNF_STRU* pClckCnf = (ATC_MSG_CLCK_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CLCK:%d\r\n", pClckCnf->ucStatus);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_PINSTATUS_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_PIN_STATUS_IND_STRU* pPinStatusInd = (ATC_MSG_PIN_STATUS_IND_STRU*)pRecvMsg;
    const char*                  pPinStatusStr[3] = { "SIM PIN", "SIM PUK", "SIM BLOCKED" };

    if(pPinStatusInd->ucPinStatus > D_ATC_PIN_STATUS_PUK_BLOCK)
    {
        return;
    }

    AtcAp_StrPrintf_AtcRspBuf("\r\n+CPIN:%s\r\n", pPinStatusStr[pPinStatusInd->ucPinStatus]);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CPINR_Cnf(unsigned char* pRecvMsg)
{
    unsigned char             index;
    ATC_MSG_CPINR_CNF_STRU*   pPinRetriesCnf = (ATC_MSG_CPINR_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n");
    for(index = 0; index < pPinRetriesCnf->ucNum; index++)
    {
        if(pPinRetriesCnf->aPinRetires[index].ucPinType == D_ATC_CPINR_TYPE_PIN1)
        {
            AtcAp_StrPrintf_AtcRspBuf("+CPINR:%s,%d,3\r\n", "SIM PIN", pPinRetriesCnf->aPinRetires[index].ucRetriesNum);
        }
        if(pPinRetriesCnf->aPinRetires[index].ucPinType == D_ATC_CPINR_TYPE_PUK1)
        {
            AtcAp_StrPrintf_AtcRspBuf("+CPINR:%s,%d,10\r\n", "SIM PUK", pPinRetriesCnf->aPinRetires[index].ucRetriesNum);
        }
        if(pPinRetriesCnf->aPinRetires[index].ucPinType == D_ATC_CPINR_TYPE_PIN2)
        {
            AtcAp_StrPrintf_AtcRspBuf("+CPINR:%s,%d,3\r\n", "SIM PIN2", pPinRetriesCnf->aPinRetires[index].ucRetriesNum);
        }
        if(pPinRetriesCnf->aPinRetires[index].ucPinType == D_ATC_CPINR_TYPE_PUK2)
        {
            AtcAp_StrPrintf_AtcRspBuf("+CPINR:%s,%d,10\r\n", "SIM PUK2", pPinRetriesCnf->aPinRetires[index].ucRetriesNum);
        }
    }
    AtcAp_SendDataInd(pRecvMsg);
}

#ifndef _FLASH_OPTIMIZE_
void AtcAp_MsgProc_CRTDCP_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_CRTDCP_IND_STRU     tCrtdcpInd = { 0 };
    unsigned char*              pInd;

    AtcAp_MemCpy((unsigned char*)&tCrtdcpInd, pRecvMsg, offsetof(ATC_MSG_CRTDCP_IND_STRU, usLen) + 2);
    tCrtdcpInd.pucReportData = pRecvMsg + offsetof(ATC_MSG_CRTDCP_IND_STRU, usLen) + 2;
    if(1 == tCrtdcpInd.ucNonIpDataFlg) 
    {
        AtcAp_MsgProc_NRNPDM_Ind(pRecvMsg, tCrtdcpInd.ucNrnpdmRepValue, tCrtdcpInd.ucAtCid,tCrtdcpInd.usLen,tCrtdcpInd.pucReportData);
    }

    if(0 == tCrtdcpInd.ucCrtdcpRepValue)
    {
        return;
    }

    if (0 != tCrtdcpInd.usLen)
    {
        pInd = (unsigned char *)AtcAp_Malloc(D_ATC_CRTDCP_IND_LENGTH + tCrtdcpInd.usLen*2 + 1);
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)pInd, (const unsigned char *)"\r\n+CRTDCP:%d,%d,\"", tCrtdcpInd.ucAtCid, tCrtdcpInd.usLen);

        AtcAp_HexToAsc(tCrtdcpInd.usLen, pInd + g_AtcApInfo.stAtRspInfo.usRspLen, tCrtdcpInd.pucReportData);
        g_AtcApInfo.stAtRspInfo.usRspLen += tCrtdcpInd.usLen*2;

        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)pInd + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"\"\r\n");
        AtcAp_SendLongDataInd(pRecvMsg, &pInd, D_ATC_CRTDCP_IND_LENGTH + tCrtdcpInd.usLen*2);
        
        AtcAp_Free(pInd);
    } 
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
            (const unsigned char *)"\r\n+CRTDCP:%d,%d,\"\"\r\n",
            tCrtdcpInd.ucAtCid, 0);
        AtcAp_SendDataInd(pRecvMsg);
    }
}
#endif
void AtcAp_MsgProc_CGAPNRC_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_CGAPNRC_IND_STRU*  pCgapnrcInd = (ATC_MSG_CGAPNRC_IND_STRU*)pRecvMsg;
    
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, 
                    (const unsigned char *)"\r\n+CGAPNRC:%d,%d,%d,%d,%d\r\n",
                    pCgapnrcInd->stEpsApnRateCtl.ucCid, 
                    pCgapnrcInd->stEpsApnRateCtl.ucAdditionExcepReportFlg,
                    pCgapnrcInd->stEpsApnRateCtl.ulMaxUplinkRate,
                    pCgapnrcInd->stEpsApnRateCtl.ulUplinkTimeUnit,
                    pCgapnrcInd->stEpsApnRateCtl.usMaxUplinkMsgSize);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGEV_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_CGEV_IND_STRU*    pCgevInd = (ATC_MSG_CGEV_IND_STRU*)pRecvMsg;

    if(pCgevInd->ucCgerepMode == 0)
    {
        return;
    }
    if(pCgevInd->ucCgevEventId <= D_ATC_CGEV_IS)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, (const unsigned char *)"\r\n+CGEV:");
    }
    switch (pCgevInd->ucCgevEventId)
    {
    /* +CGEV: NW DETACH */
    case D_ATC_CGEV_NW_DETACH:
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"NW DETACH");
        break;
    /* +CGEV: ME DETACH */
    case D_ATC_CGEV_ME_DETACH:
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"ME DETACH");
        break; 
    /* +CGEV: ME PDN ACT <cid>[,<reason>[,<cid_other>]][,<WLAN_Offload>] */
    case D_ATC_CGEV_ME_PDN_ACT:
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"ME PDN ACT %d", pCgevInd->stCgevPara.ucCid );
        if (0xFF != pCgevInd->stCgevPara.ucReason)
        {
            g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen),
                (const unsigned char *)",%d",pCgevInd->stCgevPara.ucReason);
        }
        break;
    /* +CGEV: NW ACT <p_cid>, <cid>, <event_type>[,<WLAN_Offload>] */
    case D_ATC_CGEV_NW_ACT:
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"NW ACT %d,%d,%d",
            pCgevInd->stCgevPara.ucPcid, pCgevInd->stCgevPara.ucCid, pCgevInd->stCgevPara.ucEventType);
        break;
    /* +CGEV: ME ACT <p_cid>, <cid>, <event_type>[,<WLAN_Offload>] */
    case D_ATC_CGEV_ME_ACT:
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"ME ACT %d,%d,%d",
            pCgevInd->stCgevPara.ucPcid, pCgevInd->stCgevPara.ucCid, pCgevInd->stCgevPara.ucEventType);
        break;
   /* +CGEV: NW PDN DEACT <cid>[,<WLAN_Offload>] */
    case D_ATC_CGEV_NW_PDN_DEACT:
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"NW PDN DEACT %d", pCgevInd->stCgevPara.ucCid);
        break;
   /* +CGEV: ME PDN DEACT <cid>[,<WLAN_Offload>] */
    case D_ATC_CGEV_ME_PDN_DEACT:
       g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"ME PDN DEACT %d", pCgevInd->stCgevPara.ucCid);
        break;
    /* +CGEV: NW DEACT <p_cid>, <cid>, <event_type>[,<WLAN_Offload>] */
    case D_ATC_CGEV_NW_DEACT:
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"NW DEACT %d,%d,%d",
            pCgevInd->stCgevPara.ucPcid, pCgevInd->stCgevPara.ucCid, pCgevInd->stCgevPara.ucEventType);
        break;
    /* +CGEV: NW DEACT <p_cid>, <cid>, <event_type>[,<WLAN_Offload>] */
    case D_ATC_CGEV_ME_DEACT :
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"ME DEACT %d,%d,%d",
            pCgevInd->stCgevPara.ucPcid, pCgevInd->stCgevPara.ucCid, pCgevInd->stCgevPara.ucEventType);
        break;
    /* +CGEV: NW MODIFY <cid>, <change_reason>, <event_type>[,<WLAN_Offload>] */
    case D_ATC_CGEV_NW_MODIFY:
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"NW MODIFY %d,%d,%d",
            pCgevInd->stCgevPara.ucCid, pCgevInd->stCgevPara.ucChangeReason, pCgevInd->stCgevPara.ucEventType);
        break;
    /* +CGEV: ME MODIFY <cid>, <change_reason>, <event_type>[,<WLAN_Offload>] */
    case D_ATC_CGEV_ME_MODIFY:
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"ME MODIFY %d,%d,%d",
            pCgevInd->stCgevPara.ucCid, pCgevInd->stCgevPara.ucChangeReason, pCgevInd->stCgevPara.ucEventType);
        break;
    /* +CGEV: OOS */
    case D_ATC_CGEV_OOS:
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"OOS");
        break;
    /* +CGEV: IS */
    case D_ATC_CGEV_IS:
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"IS");
        break;
    default:
        break;
    }

    if (g_AtcApInfo.stAtRspInfo.usRspLen > 0)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), (const unsigned char *)"\r\n");
        AtcAp_SendDataInd(pRecvMsg);
    }
}

void AtcAp_MsgProc_CEREG_Ind(unsigned char* pRecvMsg)
{
    unsigned char           ucERegSta;
    unsigned char           ucERegMode;
    unsigned char           aucStr[9]   = {0};
    unsigned char           ucTemp      = 0;
    unsigned char           i;
    ATC_MSG_CEREG_IND_STRU* pCeregInd;

    pCeregInd = (ATC_MSG_CEREG_IND_STRU*)pRecvMsg;
    
    ucERegSta = pCeregInd->stPara.ucEPSRegStatus;
    ucERegMode = pCeregInd->stPara.ucIndPara;
    if(pCeregInd->stPara.ucOnlyUserRegEventIndFlg == 1)
    {
        return;
    }
#if USR_CUSTOM6
    if(4 == pCeregInd->stPara.ucEPSRegStatus)
    {
        pCeregInd->stPara.ucEPSRegStatus = 0;
    }
#endif
    //<n>,<stat>
    AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"\r\n+CEREG:%d", pCeregInd->stPara.ucEPSRegStatus);

#if USR_CUSTOM6
    if( 0 == pCeregInd->stPara.ucEPSRegStatus || 3 == pCeregInd->stPara.ucEPSRegStatus)
    {
        AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"\r\n");
        AtcAp_SendDataInd(pRecvMsg);
        return;
    }
#endif

#if VER_QUEC
    if( 0 == pCeregInd->stPara.ucEPSRegStatus)
    {
        AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"\r\n");
        AtcAp_SendDataInd(pRecvMsg);
        return;
    }
#endif

    //<tac>,<ci>,<Act>
    if (1 < ucERegMode)
    {
        //[<tac>]two byte tracking area code in hexadecimal format 
        if (pCeregInd->stPara.OP_TacLac)
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = 4;
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",\"%X\"", pCeregInd->stPara.usTacOrLac);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",");
        }
        //[<ci>] four byte E-UTRAN cell ID in hexadecimal format
        if (pCeregInd->stPara.OP_CellId)
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = 8;
#if VER_CM || USR_CUSTOM9 || USR_CUSTOM11
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",\"%08X\"",  pCeregInd->stPara.ulCellId);
#else

            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",\"%07X\"",  pCeregInd->stPara.ulCellId);
#endif
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",");
        }
        //[<Act>]
        AtcAp_WriteIntPara_M(pCeregInd->stPara.OP_Act, pCeregInd->stPara.ucAct, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    }
    
    //[<cause_type>][<reject_cause>]
    if(3 == ucERegMode)
    {
        AtcAp_WriteIntPara_M(pCeregInd->stPara.OP_CauseType, pCeregInd->stPara.ucCauseType, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
        AtcAp_WriteIntPara_M(pCeregInd->stPara.OP_RejectCause, pCeregInd->stPara.ucRejectCause, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    }
    if (5 == ucERegMode)
    {
        AtcAp_WriteIntPara_M(pCeregInd->stPara.OP_CauseType, pCeregInd->stPara.ucCauseType, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
        AtcAp_WriteIntPara_M(pCeregInd->stPara.OP_RejectCause, pCeregInd->stPara.ucRejectCause, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    }
    else if(4 == ucERegMode)
    {
        AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",,");
    }
    
    //<Active-Time><Periodic-TAU>
    if (3 < ucERegMode)
    {
        if(0 == ucERegSta || 2 == ucERegSta || 3 == ucERegSta || 4 == ucERegSta)
        {
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",,");
        }
        else
        {
            //<Active-Time>
            if (pCeregInd->stPara.OP_ActiveTime)
            {
                for (i  = 0; i < 8; i++)
                {
                    ucTemp = (0x80 >> i)& pCeregInd->stPara.ucActiveTime;
                    aucStr[i] = (ucTemp >> (7-i)) + 0x30;
                }
            }
            AtcAp_WriteStrPara_M(pCeregInd->stPara.OP_ActiveTime, aucStr );

            //<Periodic-TAU>
            if (pCeregInd->stPara.OP_PeriodicTAU)
            {
                for (i  = 0; i < 8; i++)
                {
                    ucTemp = (0x80 >> i)& pCeregInd->stPara.ucPeriodicTAU;
                    aucStr[i] = (ucTemp >> (7-i)) + 0x30;
                }
            }
            AtcAp_WriteStrPara_M(pCeregInd->stPara.OP_PeriodicTAU, aucStr );
        }
    }

    AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CREG_Ind(unsigned char* pRecvMsg)
{
    unsigned char           ucERegMode;
    ATC_MSG_CREG_IND_STRU* pCregInd;

    pCregInd = (ATC_MSG_CREG_IND_STRU*)pRecvMsg;

    ucERegMode = pCregInd->stPara.ucIndPara;

    //<stat>
    AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"\r\n+CREG:%d", pCregInd->stPara.ucEPSRegStatus);

#if VER_QUEC
    if( 0 == pCregInd->stPara.ucEPSRegStatus)
    {
        AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"\r\n");
        AtcAp_SendDataInd(pRecvMsg);
        return;
    }
#endif

    //<tac>,<ci>,<Act>
    if (1 < ucERegMode)
    {
        //[<tac>]two byte tracking area code in hexadecimal format 
        if (pCregInd->stPara.OP_TacLac)
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = 4;
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",\"%X\"", pCregInd->stPara.usTacOrLac);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",");
        }
        //[<ci>] four byte E-UTRAN cell ID in hexadecimal format
        if (pCregInd->stPara.OP_CellId)
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = 8;
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",\"%07X\"", pCregInd->stPara.ulCellId);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",");
        }
        //[<Act>]
        AtcAp_WriteIntPara_M(pCregInd->stPara.OP_Act, pCregInd->stPara.ucAct, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    }
    
    //[<cause_type>][<reject_cause>]
    if(3 == ucERegMode)
    {
        AtcAp_WriteIntPara_M(pCregInd->stPara.OP_CauseType, pCregInd->stPara.ucCauseType, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
        AtcAp_WriteIntPara_M(pCregInd->stPara.OP_RejectCause, pCregInd->stPara.ucRejectCause, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    }

    AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGREG_Ind(unsigned char* pRecvMsg)
{
    unsigned char           ucERegMode;
    ATC_MSG_CGREG_IND_STRU* pCregInd;

    pCregInd = (ATC_MSG_CGREG_IND_STRU*)pRecvMsg;

    ucERegMode = pCregInd->stPara.ucIndPara;

    //<stat>
    AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"\r\n+CGREG:%d", pCregInd->stPara.ucEPSRegStatus);

    //<tac>,<ci>,<Act>
    if (1 < ucERegMode)
    {
        //[<tac>]two byte tracking area code in hexadecimal format 
        if (pCregInd->stPara.OP_TacLac)
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = 4;
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",\"%X\"", pCregInd->stPara.usTacOrLac);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",");
        }
        //[<ci>] four byte E-UTRAN cell ID in hexadecimal format
        if (pCregInd->stPara.OP_CellId)
        {
            g_AtcApInfo.stAtRspInfo.ucHexStrLen = 8;
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",\"%07X\"", pCregInd->stPara.ulCellId);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",");
        }
        //[<Act>]
        AtcAp_WriteIntPara_M(pCregInd->stPara.OP_Act, pCregInd->stPara.ucAct, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    }
    
    //[<cause_type>][<reject_cause>]
    if(3 == ucERegMode)
    {
        AtcAp_WriteIntPara_M(pCregInd->stPara.OP_CauseType, pCregInd->stPara.ucCauseType, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
        AtcAp_WriteIntPara_M(pCregInd->stPara.OP_RejectCause, pCregInd->stPara.ucRejectCause, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    }

    AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)"\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CSCON_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_CSCON_IND_STRU* pCsconInd = (ATC_MSG_CSCON_IND_STRU*)pRecvMsg;

    if(0 == pCsconInd->ucCsconN)
    {
        return;
    }

    //<n>,<mode>g_AtcMng.ucCsconN 
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CSCON:%d", pCsconInd->stPara.ucMode);

    if (1 < pCsconInd->ucCsconN)
    {
        //[<state>]
        AtcAp_WriteIntPara(pCsconInd->stPara.OP_State, pCsconInd->stPara.ucState);
        if (2 < pCsconInd->ucCsconN)
        {
            //[<access>]
            AtcAp_WriteIntPara(pCsconInd->stPara.OP_Access, pCsconInd->stPara.ucAccess);
        }
    }

    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), (const unsigned char *)"\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CEDRXP_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_NPTWEDRXP_IND_STRU* pCedrxpInd = (ATC_MSG_NPTWEDRXP_IND_STRU*)pRecvMsg;
    unsigned char i = 0;
    unsigned char ucTemp =0;
    unsigned char aucStr[5] = {0};

    if(2 != pCedrxpInd->stPara.ucNptwEDrxMode)
    {
        return;
    }

    AtcAp_StrPrintf_AtcRspBuf("\r\n+CEDRXP:%d", pCedrxpInd->stPara.ucActType);
    
    AtcAp_ConvertByte2BitStr(pCedrxpInd->stPara.ucReqDRXValue, 4, aucStr);
    AtcAp_StrPrintf_AtcRspBuf(",\"%s\"", aucStr);
    
    if(0 != pCedrxpInd->stPara.ucActType)
    {
        AtcAp_ConvertByte2BitStr(pCedrxpInd->stPara.ucNWeDRXValue, 4, aucStr);
        AtcAp_StrPrintf_AtcRspBuf(",\"%s\"", aucStr);

        AtcAp_ConvertByte2BitStr(pCedrxpInd->stPara.ucPagingTimeWin, 4, aucStr);
        AtcAp_StrPrintf_AtcRspBuf(",\"%s\"", aucStr);      
    }    
    AtcAp_StrPrintf_AtcRspBuf("\r\n");

    AtcAp_SendDataInd(pRecvMsg);

    return ;
}

#ifndef _FLASH_OPTIMIZE_
void AtcAp_MsgProc_NPTWEDRXP_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_NPTWEDRXP_IND_STRU* pNptwedrxpInd = (ATC_MSG_NPTWEDRXP_IND_STRU*)pRecvMsg;
    unsigned char               aucStr[5]     = { 0 };

    if(2 != pNptwedrxpInd->stPara.ucNptwEDrxMode)
    {
        return;
    }
    
    AtcAp_StrPrintf_AtcRspBuf("\r\n+MPTWEDRXP:%d", pNptwedrxpInd->stPara.ucActType);
    AtcAp_ConvertByte2BitStr(pNptwedrxpInd->stPara.ucReqPagingTimeWin, 4, aucStr);
    AtcAp_StrPrintf_AtcRspBuf(",\"%s\"", aucStr);

    AtcAp_ConvertByte2BitStr(pNptwedrxpInd->stPara.ucReqDRXValue, 4, aucStr);
    AtcAp_StrPrintf_AtcRspBuf(",\"%s\"", aucStr);

    if(0 != pNptwedrxpInd->stPara.ucActType)
    {
        AtcAp_ConvertByte2BitStr(pNptwedrxpInd->stPara.ucNWeDRXValue, 4, aucStr);
        AtcAp_StrPrintf_AtcRspBuf(",\"%s\"", aucStr);

        AtcAp_ConvertByte2BitStr(pNptwedrxpInd->stPara.ucPagingTimeWin, 4, aucStr);
        AtcAp_StrPrintf_AtcRspBuf(",\"%s\"", aucStr);      
    }    
    AtcAp_StrPrintf_AtcRspBuf("\r\n");
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CCIOTOPTI_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_CCIOTOPTI_IND_STRU* pCciotopti = (ATC_MSG_CCIOTOPTI_IND_STRU*)pRecvMsg;

    if(pCciotopti->ucCciotoptN == 0 || pCciotopti->ucCciotoptN == 3)
    {
        return;
    }

    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
        (const unsigned char *)"\r\n+CCIOTOPTI:%d\r\n", pCciotopti->ucSupptNwOpt);
    AtcAp_SendDataInd(pRecvMsg);
}
#endif


void AtcAp_MsgProc_L2_THP_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_L2_THP_IND_STRU*    pL2ThpInd = (ATC_MSG_L2_THP_IND_STRU*)pRecvMsg;

    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, 
                                    (const unsigned char *)"\r\n%s%d\r\n%s%d\r\n%s%d\r\n%s%d\r\n", 
                                    "+L2_THP,RLC UL:", pL2ThpInd->ulRlcUl, 
                                    "+L2_THP,RLC DL:", pL2ThpInd->ulRlcDl, 
                                    "+L2_THP,MAC UL:", pL2ThpInd->ulMacUl, 
                                    "+L2_THP,MAC DL:", pL2ThpInd->ulMacDl);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NCCID_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_UICCID_CNF_STRU*    pUccidCnf = (ATC_MSG_UICCID_CNF_STRU*)pRecvMsg;
#if USR_CUSTOM2
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+NCCID:%s\r\n", pUccidCnf->aucICCIDstring);
#else
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+MCCID:%s\r\n", pUccidCnf->aucICCIDstring);
#endif
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_XYIPDNS_Ind(unsigned char* pRecvMsg)
{
}

void AtcAp_MsgProc_MALLOC_ADDR_Ind(unsigned char* pRecvMsg)
{
}

void AtcAp_MsgProc_IPSN_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_IPSN_IND_STRU*    pIpsnInd = (ATC_MSG_IPSN_IND_STRU*)pRecvMsg;

    if(ATC_AP_TRUE == pIpsnInd->ucFlowCtrlStatusFlg)
    {
        if(1 == pIpsnInd->ucFlowCtrlStatus)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+DBGINFO:PsFlowCtrlStart!\r\n");
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+DBGINFO:PsFlowCtrlEnd!\r\n");
        }
        AtcAp_SendDataInd(pRecvMsg);
    }
}

#ifdef LCS_MOLR_ENABLE
void AtcAp_MsgProc_CMOLRE_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_CMOLRE_IND_STRU*    pCmolrE = (ATC_MSG_CMOLRE_IND_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CMOLRE:%d\r\n", pCmolrE->ucErrCode);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CMOLRG_Ind(unsigned char* pRecvMsg)
{
    unsigned char*              pAtcRspBuf;
    unsigned char*              pData;
    unsigned short              usLen      = 0;
    ATC_MSG_CMOLRG_IND_STRU*    pCmolrG    = (ATC_MSG_CMOLRG_IND_STRU*)pRecvMsg;

    pAtcRspBuf = (unsigned char*)AtcAp_Malloc(D_ATC_CMOLRG_MAX_SIZE + 1);
    pData = pAtcRspBuf;

    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)pData,  (const unsigned char *)"\r\n+CMOLRG:");
    pData += g_AtcApInfo.stAtRspInfo.usRspLen;

    usLen = D_ATC_CMOLRG_MAX_SIZE - usLen - 10;
    g_AtcApInfo.stAtRspInfo.usRspLen += Lcs_MolrResult_OutputXML(pData, &usLen, &pCmolrG->stShapeData, &pCmolrG->stVelData);
    pData += g_AtcApInfo.stAtRspInfo.usRspLen;

    g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)pData,  (const unsigned char *)"\r\n");

    AtcAp_SendLongDataInd(pRecvMsg, &pAtcRspBuf, D_ATC_CMOLRG_MAX_SIZE);
    
    AtcAp_Free(pAtcRspBuf);
}
#endif

void AtcAp_MsgProc_PDNIPADDR_Ind(unsigned char* pRecvMsg)
{
}

void AtcAp_MsgProc_NGACTR_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_NGACTR_IND_STRU*    pNgactrInd = (ATC_MSG_NGACTR_IND_STRU*)pRecvMsg;

    if(1 != pNgactrInd->ucNgactrN)
    {
        return;
    }

    AtcAp_StrPrintf_AtcRspBuf("\r\n+NGACTR:%d,%d,%d\r\n", pNgactrInd->ucAtCid, pNgactrInd->state, pNgactrInd->result);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CELLSRCH_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_CELLSRCH_TEST_STRU* pNCSEARFCN = (ATC_MSG_CELLSRCH_TEST_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+CELLSRCH:%d,%d\r\n", pNCSEARFCN->ucFstSrchType, pNCSEARFCN->ucCampOnType);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CSIM_Cnf(unsigned char* pRecvMsg)
{
    unsigned char*                 pDataBuff;
    ATC_MSG_CSIM_CNF_STRU*         pCsimCnf = (ATC_MSG_CSIM_CNF_STRU*)pRecvMsg;

    pDataBuff = (unsigned char*)AtcAp_Malloc(pCsimCnf->usRspLen * 2 + 1);
    
    AtcAp_HexToAsc((unsigned short)pCsimCnf->usRspLen, pDataBuff, pCsimCnf->aucRsp);
#if USR_CUSTOM2
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CSIM:%d,%s\r\n", pCsimCnf->usRspLen * 2, pDataBuff);
#else
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CSIM:%d,\"%s\"\r\n", pCsimCnf->usRspLen * 2, pDataBuff);
#endif
    AtcAp_Free(pDataBuff);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGLA_Cnf(unsigned char* pRecvMsg)
{
    unsigned char*                 pDataBuff;
    ATC_MSG_CGLA_CNF_STRU*         pCglaCnf = (ATC_MSG_CGLA_CNF_STRU*)pRecvMsg;

    pDataBuff = (unsigned char*)AtcAp_Malloc(pCglaCnf->usRspLen * 2 + 1);
    
    AtcAp_HexToAsc((unsigned short)pCglaCnf->usRspLen, pDataBuff, pCglaCnf->aucRsp);
#if USR_CUSTOM2
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CGLA:%d,%s\r\n", pCglaCnf->usRspLen * 2, pDataBuff);
#else
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CGLA:%d,\"%s\"\r\n", pCglaCnf->usRspLen * 2, pDataBuff);
#endif
    AtcAp_Free(pDataBuff);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CCHO_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CCHO_CNF_STRU*  pCchoCnf = (ATC_MSG_CCHO_CNF_STRU*)pRecvMsg;
#if VER_QUEC
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CCHO:%d\r\n", pCchoCnf->ucChannelId);
#else
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%d\r\n", pCchoCnf->ucChannelId);
#endif
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CCHC_Cnf(unsigned char* pRecvMsg)
{
    Unused_para(pRecvMsg);
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CCHC\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CRSM_Cnf(unsigned char* pRecvMsg)
{
    unsigned char          *pStrData;
    ATC_MSG_CRSM_CNF_STRU*  pCrsmCnf = (ATC_MSG_CRSM_CNF_STRU*)pRecvMsg;

    if (D_ATC_CRSM_COMMAND_UPDATE_BINARY == pCrsmCnf->ucCommand
        || D_ATC_CRSM_COMMAND_UPDATE_RECORD == pCrsmCnf->ucCommand
        || D_ATC_CRSM_COMMAND_SET_DATA == pCrsmCnf->ucCommand
        || 0 == pCrsmCnf->usRspLen)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+CRSM:%d,%d\r\n", pCrsmCnf->sw1, pCrsmCnf->sw2);
    }
    else
    {
        pStrData = (unsigned char*)AtcAp_Malloc(pCrsmCnf->usRspLen * 2 + 1);
        AtcAp_HexToAsc(pCrsmCnf->usRspLen, pStrData, pCrsmCnf->aucResponse);
#if USR_CUSTOM2
        AtcAp_StrPrintf_AtcRspBuf("\r\n+CRSM:%d,%d,%s\r\n", pCrsmCnf->sw1, pCrsmCnf->sw2, pStrData);
#else
        AtcAp_StrPrintf_AtcRspBuf("\r\n+CRSM:%d,%d,\"%s\"\r\n", pCrsmCnf->sw1, pCrsmCnf->sw2, pStrData);
#endif
        AtcAp_Free(pStrData);
    }
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_LOCALTIMEINFO_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_LOCALTIMEINFO_IND_STRU* pLocalTimeInfo = (ATC_MSG_LOCALTIMEINFO_IND_STRU*)pRecvMsg;

    switch (pLocalTimeInfo->ucCtzrReport)
    {
    case 0:
        break;
    case 1:
        AtcAp_OutputTimeZone(pLocalTimeInfo->ucCtzrReport, &pLocalTimeInfo->stPara);
        break;
    case 2:
        AtcAp_OutputTimeZone(pLocalTimeInfo->ucCtzrReport, &pLocalTimeInfo->stPara);
        AtcAp_OutputLocalTime(&pLocalTimeInfo->stPara);
        break;
    case 3:
        AtcAp_OutputTimeZone(pLocalTimeInfo->ucCtzrReport, &pLocalTimeInfo->stPara);
        AtcAp_OutputUniversalTime(&pLocalTimeInfo->stPara);
        break;
    default:
        break;
    }

    if (0 != g_AtcApInfo.stAtRspInfo.usRspLen)
    {
        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf + g_AtcApInfo.stAtRspInfo.usRspLen), (const unsigned char *)"\r\n");
        AtcAp_SendDataInd(pRecvMsg);
    }

    AtcAp_OutputLocalTime(&pLocalTimeInfo->stPara);
    PrintUserLog(USER_LOG, DEBUG_LOG, "[LocalTime]%s", g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    g_AtcApInfo.stAtRspInfo.usRspLen = 0;
    AtcAp_MemSet(g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, 0 ,D_ATC_RSP_MAX_BUF_SIZE);    
}

void AtcAp_MsgProc_NoCarrier_Ind(unsigned char* pRecvMsg)
{
    Unused_para(pRecvMsg);
    g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf, (const unsigned char *)"\r\nNO CARRIER\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_PSINFO_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_PSINFO_IND_STRU* pPsInfo = (ATC_MSG_PSINFO_IND_STRU*)pRecvMsg;
    

    if(pPsInfo->ucType == D_MSG_PSINFO_TYPE_SOFT_RESET)
    {
        xy_Soft_Reset(SOFT_RB_BY_CP_USER);
    }
}

#ifndef _FLASH_OPTIMIZE_
void AtcAp_MsgProc_CSODCPR_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_CSODCPR_IND_STRU* pCsodcprInd = (ATC_MSG_CSODCPR_IND_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+CSODCPR:%d,%d,%d\r\n", pCsodcprInd->ucAtCid, pCsodcprInd->ucSequence, pCsodcprInd->ucStatus);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NSNPDR_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_NSNPDR_IND_STRU* pNsnpdrInd = (ATC_MSG_NSNPDR_IND_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+NSNPDR:%d,%d,%d\r\n", pNsnpdrInd->ucAtCid, pNsnpdrInd->ucSequence, pNsnpdrInd->ucStatus);
    AtcAp_SendDataInd(pRecvMsg);
}
#endif
void AtcAp_MsgProc_CNEC_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_CNEC_IND_STRU* pCnecInd      = (ATC_MSG_CNEC_IND_STRU*)pRecvMsg;

    if(pCnecInd->ucType == D_ATC_CNEC_TYPE_EMM)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+CNEC_EMM:%d", pCnecInd->ucErrCode);
    }
    else
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+CNEC_ESM:%d", pCnecInd->ucErrCode);
    }

    if(pCnecInd->ucAtCidFlg != 0)
    {
        AtcAp_StrPrintf_AtcRspBuf(",%d", pCnecInd->ucAtCid);
    }
    AtcAp_StrPrintf_AtcRspBuf("\r\n");
    
    AtcAp_SendDataInd(pRecvMsg);
}
#ifndef _FLASH_OPTIMIZE_
void AtcAp_MsgProc_NRNPDM_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_NRNPDM_R_CNF_STRU* pNrnpdmR=(ATC_MSG_NRNPDM_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+NRNPDM:%d\r\n", pNrnpdmR->ucReporting);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NRNPDM_Ind(unsigned char* pRecvMsg, unsigned char ucNrnpdmRepValue, unsigned char ucAtCid,unsigned short usLen,unsigned char* pucReportData)
{
    unsigned char*              pInd;

    if(0 == ucNrnpdmRepValue)
    {
        return;
    }
    
    if (0 != usLen)
    {
        pInd = (unsigned char *)AtcAp_Malloc(D_ATC_NRNPDM_IND_LENGTH + usLen*2 + 1);
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)pInd, (const unsigned char *)"\r\n+NRNPDM:%d,%d,\"", ucAtCid, usLen);

        AtcAp_HexToAsc(usLen, pInd + g_AtcApInfo.stAtRspInfo.usRspLen, pucReportData);
        g_AtcApInfo.stAtRspInfo.usRspLen += usLen*2;

        g_AtcApInfo.stAtRspInfo.usRspLen += AtcAp_StrPrintf((unsigned char *)pInd + g_AtcApInfo.stAtRspInfo.usRspLen, (const unsigned char *)"\"\r\n");
        AtcAp_SendLongDataInd(pRecvMsg, &pInd, D_ATC_NRNPDM_IND_LENGTH + usLen*2);
        
        AtcAp_Free(pInd);
    } 
    else
    {
        g_AtcApInfo.stAtRspInfo.usRspLen = AtcAp_StrPrintf((unsigned char *)g_AtcApInfo.stAtRspInfo.aucAtcRspBuf,
            (const unsigned char *)"\r\n+NRNPDM:%d,%d,\"\"\r\n",ucAtCid, 0);
        AtcAp_SendDataInd(pRecvMsg);
    }
}
#endif
void AtcAp_MsgProc_MNBIOTEVENT_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_MNBIOTEVENT_IND_STRU* pInd = (ATC_MSG_MNBIOTEVENT_IND_STRU*)pRecvMsg;
    char* pPsmState[2]                 = { "ENTER PSM", "EXIT PSM" };

    if(pInd->ucPsmEnable == 0)
    {
        return;
    }

    AtcAp_StrPrintf_AtcRspBuf("\r\n+MNBIOTEVENT:\"%s\"\r\n", pInd->ucPsmState == D_ATC_MNBIOTEVENT_ENTER_PSM ? pPsmState[0] : pPsmState[1]);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NLOCKF_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_NLOCKF_R_CNF_STRU*  pNlockfR =(ATC_MSG_NLOCKF_R_CNF_STRU*) pRecvMsg;
    unsigned char   ucEarfcnNum = 0;

    if(pNlockfR->ucEarLockFlg == 1)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+NLOCKF:1,%u",pNlockfR->ulLockEar);
        if(pNlockfR->ucPciLockFlg == 1)
        {
            AtcAp_StrPrintf_AtcRspBuf(",%u",pNlockfR->usLockPci);
        }
    }
    if(pNlockfR->ucEarfcnNum > 0)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+NLOCKF:2");
        for(ucEarfcnNum = 0; ucEarfcnNum <= (pNlockfR->ucEarfcnNum - 1); ucEarfcnNum++)
        {
            AtcAp_StrPrintf_AtcRspBuf(",%u",pNlockfR->aulEarfcnList[ucEarfcnNum]);
        }
    }
    if(pNlockfR->ucEarLockFlg == 1 || pNlockfR->ucEarfcnNum > 0)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n");
    }
    AtcAp_SendDataInd(pRecvMsg);
}

#ifdef CSG_FEATURE
void AtcAp_MsgProc_NCSG_R_Cnf(unsigned char* pRecvMsg)
{
    unsigned char            aucStrPlmn[7] = { 0 };
    ATC_MSG_NCSG_R_CNF_STRU *pNcsgR        = (ATC_MSG_NCSG_R_CNF_STRU*)pRecvMsg;
    
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QCSG:%d", pNcsgR->ucMode);     
    if (pNcsgR->ucPlmnSelFlg)
    {
        AtcAp_ConvertPlmn2NameStr(pNcsgR->ulPlmnNum, PLMN_FORMAT_NUMERIC, aucStrPlmn);      
        AtcAp_StrPrintf_AtcRspBuf((const char *)",\"%s\"", aucStrPlmn);
        AtcAp_WriteIntPara(pNcsgR->ucCsgIdFlg, pNcsgR->uiCsgId);  
    }
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NCSG_T_Cnf(unsigned char* pRecvMsg)
{
    unsigned char            i             = 0;
    unsigned char            aucStrPlmn[7] = { 0 };
    ATC_MSG_NCSG_T_CNF_STRU *pNcsgT        = (ATC_MSG_NCSG_T_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QCSG:(");
    for(i = 0; i < pNcsgT->unNum; i++)
    {
        AtcAp_IntegerToPlmn(pNcsgT->astCsgList[i].ulPlmnId , aucStrPlmn);
        AtcAp_StrPrintf_AtcRspBuf((const char *)"(%d,%d,%u,\"%s\")", pNcsgT->astCsgList[i].ucAllowFg, pNcsgT->ucAct,
                                pNcsgT->astCsgList[i].ulCsgId, aucStrPlmn);            
        if(i < pNcsgT->unNum - 1)
        {
            AtcAp_StrPrintf_AtcRspBuf((const unsigned char *)",");
        }
    }
    AtcAp_StrPrintf_AtcRspBuf((const char *)")\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}
#endif
#ifndef _FLASH_OPTIMIZE_
void AtcAp_MsgProc_CACDC_T_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CACDC_T_CNF_STRU *pCacdcT   = (ATC_MSG_CACDC_T_CNF_STRU*)pRecvMsg;
    unsigned char             i         = 0;
    unsigned short            usRspLen  = 0;
    unsigned char            *pRspBuf;


    pRspBuf = (unsigned char*)AtcAp_Malloc(pCacdcT->ucNum * 60 + 100);

    usRspLen += AtcAp_StrPrintf((unsigned char *)(pRspBuf + usRspLen),(const unsigned char *)"\r\n+CACDC:(");
    for(i = 0; i < pCacdcT->ucNum; i++)
    {
        g_AtcApInfo.stAtRspInfo.ucHexStrLen = 16;
        usRspLen += AtcAp_StrPrintf((unsigned char *)(pRspBuf + usRspLen),(const unsigned char *)"(%x,%s)", pCacdcT->astAcdcOsConfig[i].aucOsId, pCacdcT->astAcdcOsConfig[i].aucOsappId);
        if(i < pCacdcT->ucNum - 1)
        {
            usRspLen += AtcAp_StrPrintf((unsigned char *)(pRspBuf + usRspLen),(const unsigned char *)",");
        }
    }
    usRspLen += AtcAp_StrPrintf((unsigned char *)(pRspBuf + usRspLen),(const char *)"),(0,1)\r\n");

    g_AtcApInfo.stAtRspInfo.usRspLen = usRspLen;
    usRspLen = pCacdcT->ucNum * 60 + 100;
    AtcAp_SendLongDataInd(pRecvMsg, &pRspBuf, usRspLen - 1);

    AtcAp_Free(pRspBuf);
}
#endif
void AtcAp_MsgProc_QICSGP_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QICSGP_R_CNF_STRU *pCnf   = (ATC_MSG_QICSGP_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QICSGP:%d", pCnf->ucPdpType);    
    AtcAp_StrPrintf_AtcRspBuf((const char *)",\"%s\"", pCnf->aucApnValue);
    AtcAp_StrPrintf_AtcRspBuf((const char *)",\"%s\"", pCnf->aucUserName);
    AtcAp_StrPrintf_AtcRspBuf((const char *)",\"%s\"", pCnf->aucPassword);  
    AtcAp_StrPrintf_AtcRspBuf((const char *)",%d\r\n", pCnf->ucAuthProt);
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QIACT_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QIACT_R_CNF_STRU*    pQiactRCnf;
    unsigned char                i;
    unsigned char                aucIpLocalAddr[16] = { 0 };
    unsigned char                ucIpv6addr_all[16] = { 0 };

    pQiactRCnf = (ATC_MSG_QIACT_R_CNF_STRU*)pRecvMsg;

    if (0 != pQiactRCnf->stPara.ucValidNum)
    {
        for (i = 0; i < pQiactRCnf->stPara.ucValidNum; i++)
        {
            AtcAp_StrPrintf_AtcRspBuf("\r\n+QIACT:%d,1,%d", pQiactRCnf->stPara.aucPdpDynamicInfo[i].ucCid, pQiactRCnf->stPara.aucPdpDynamicInfo[i].ucPdpType);
            switch(pQiactRCnf->stPara.aucPdpDynamicInfo[i].ucPdpType)
            {
                case D_PDP_TYPE_IPV4:
                    AtcAp_OutputAddr(4, pQiactRCnf->stPara.aucPdpDynamicInfo[i].aucPdpAddrValue + 8, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    break;    
                case D_PDP_TYPE_IPV6:
                    if(ATC_GET_IPV6ADDR_ALL(pQiactRCnf->stPara.aucPdpDynamicInfo[i].ucCid, ucIpv6addr_all) == ATC_AP_TRUE)
                    {
                        AtcAp_OutputAddr(16, ucIpv6addr_all, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    }
                    else
                    {
                        aucIpLocalAddr[0] = 0xFE;
                        aucIpLocalAddr[1] = 0x80;
                        AtcAp_MemCpy(aucIpLocalAddr + 8, pQiactRCnf->stPara.aucPdpDynamicInfo[i].aucPdpAddrValue, 8);
                        
                        AtcAp_OutputAddr(16, aucIpLocalAddr, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    }
                    break;
                case D_PDP_TYPE_IPV4V6:
                    AtcAp_OutputAddr(4, pQiactRCnf->stPara.aucPdpDynamicInfo[i].aucPdpAddrValue + 8, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    
                    if(ATC_GET_IPV6ADDR_ALL(pQiactRCnf->stPara.aucPdpDynamicInfo[i].ucCid, ucIpv6addr_all) == ATC_AP_TRUE)
                    {
                        AtcAp_OutputAddr(16, ucIpv6addr_all, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    }
                    else
                    {
                        aucIpLocalAddr[0] = 0xFE;
                        aucIpLocalAddr[1] = 0x80;
                        AtcAp_MemCpy(aucIpLocalAddr + 8, pQiactRCnf->stPara.aucPdpDynamicInfo[i].aucPdpAddrValue, 8);
                        
                        AtcAp_OutputAddr(16, aucIpLocalAddr, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    }
                    break;
                default:
                    AtcAp_OutputAddr(0, NULL, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    break;
            }
            if(i == pQiactRCnf->stPara.ucValidNum - 1)
            {
                AtcAp_StrPrintf_AtcRspBuf("\r\n");
            }
            AtcAp_SendDataInd(pRecvMsg);
        }
    }
}

void AtcAp_MsgProc_QGSN_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QGSN_CNF_STRU* pQgsnCnf;
    unsigned char*         pucData;

    pQgsnCnf = (ATC_MSG_QGSN_CNF_STRU*)pRecvMsg;

    if(0 == pQgsnCnf->ucLen || D_ATC_CGSN_TYPE_IMEI != pQgsnCnf->snt)
    {
        return;
    }

    pucData = AtcAp_MallocWithClean(pQgsnCnf->ucLen + 1);
    AtcAp_MemCpy(pucData, pQgsnCnf->aucData, pQgsnCnf->ucLen);
    
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QGSN:%s\r\n", pucData);
    AtcAp_SendDataInd(pRecvMsg);

    AtcAp_Free(pucData);
}

void AtcAp_MsgProc_GSN_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_GSN_CNF_STRU* pQgsnCnf;
    unsigned char*         pucData;

    pQgsnCnf = (ATC_MSG_GSN_CNF_STRU*)pRecvMsg;

    if(0 == pQgsnCnf->ucLen)
    {
        return;
    }

    pucData = AtcAp_MallocWithClean(pQgsnCnf->ucLen + 1);
    AtcAp_MemCpy(pucData, pQgsnCnf->aucData, pQgsnCnf->ucLen);
#if USR_CUSTOM9 || USR_CUSTOM10
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s\r\n", pucData);
#elif VER_CM
    if(0 == pQgsnCnf->snt)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s\r\n", pucData);
    }
    else
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+GSN:%s\r\n", pucData);
    }
#else
    if(0 == pQgsnCnf->sntFlg)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n%s\r\n", pucData);
    }
    else
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+GSN:%s\r\n", pucData);
    }
#endif
    AtcAp_Free(pucData);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QCCID_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QCCID_CNF_STRU*    pUccidCnf = (ATC_MSG_QCCID_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QCCID:%s\r\n", pUccidCnf->aucICCIDstring);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CGREG_R_Cnf(unsigned char* pRecvMsg)
{
    AtcAp_MsgProc_CEREG_R_Cnf(pRecvMsg);
}

void AtcAp_MsgProc_QSPN_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QSPN_CNF_STRU* pCnf             = (ATC_MSG_QSPN_CNF_STRU*)pRecvMsg;
    unsigned char          aucSelectPlmn[7] = { 0 };
    unsigned char          i = 0;

    if(1 == pCnf->ucRegPlmnFlg)
    {
        AtcAp_IntegerToPlmn(pCnf->uiRegPlmn, aucSelectPlmn);
        if(0 == strlen(pCnf->aucFullNetworkName) && 0 == strlen(pCnf->aucShortNetworkName))
        {
            for(i = 0; i < 13; i++)
            {
                if(0 == strcmp(aucSelectPlmn, ATC_PlmnName_Table[i].pucOper))
                {
                    AtcAp_MemCpy(pCnf->aucFullNetworkName, ATC_PlmnName_Table[i].pucLongName, strlen(ATC_PlmnName_Table[i].pucLongName));
                    AtcAp_MemCpy(pCnf->aucShortNetworkName, ATC_PlmnName_Table[i].pucShortName, strlen(ATC_PlmnName_Table[i].pucShortName));
                    break;
                }
            }
        }
    }
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QSPN:\"%s\",\"%s\",\"%s\",0,\"%s\"\r\n", pCnf->aucFullNetworkName, pCnf->aucShortNetworkName, pCnf->aucServProvName, aucSelectPlmn);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QENG_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QENG_CNF_STRU*    pCnf = (ATC_MSG_QENG_CNF_STRU*)pRecvMsg;
    char*                     aType[] = { "servingcell", "neighbourcell intra", "neighbourcell inter" };
    char*                     aState[] = { "SEARCH", "LIMSRV", "NOCONN", "CONNECT" };
    char                      aucSelectPlmn[7] = { 0 };
    char                      aucMcc[4] = { 0 };
    char                      aucMnc[4] = { 0 };
    unsigned char             i;

    if(0 == pCnf->type)
    {
        if(D_ATC_QENG_CELL_TYPE_SEARCH == pCnf->stServCell.state)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QENG:\"%s\",\"%s\"\r\n", aType[0], aState[0]);
        }
        else
        {
            AtcAp_IntegerToPlmn(pCnf->stServCell.plmn, (unsigned char*)aucSelectPlmn);
            strncpy(aucMcc, aucSelectPlmn, 3);
            strncpy(aucMnc, aucSelectPlmn + 3, 3);
            AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QENG:\"%s\",\"%s\",\"LTE\"", aType[0], aState[pCnf->stServCell.state]);
            AtcAp_StrPrintf_AtcRspBuf((const char *)",\"%s\"", pCnf->stServCell.is_tdd == 1 ? "TDD":"FDD");
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%s,%s", aucMcc, aucMnc);
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%04X,%d,%u,%u", 
                pCnf->stServCell.cell_ID, pCnf->stServCell.pci_value,
                pCnf->stServCell.earfcn_value, pCnf->stServCell.freq_band_ind);
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%u,%u,%X", 
                pCnf->stServCell.ul_bandwidth, pCnf->stServCell.dl_bandwidth, pCnf->stServCell.tac);
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%d,%d,%d,%d", 
                pCnf->stServCell.rsrp, pCnf->stServCell.rsrq, pCnf->stServCell.rssi, pCnf->stServCell.sinr);
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%d\r\n", pCnf->stServCell.srxlev);
        }
    }
    else
    {
        for(i = 0; i < pCnf->ucNeighCellNum; i++)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QENG:\"%s\",\"LTE\"", aType[pCnf->aNeighbourCellList[i].intraOrInter + 1]);
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%u", pCnf->aNeighbourCellList[i].earfcn);
            
            if(0 == pCnf->aNeighbourCellList[i].intraOrInter)
            {
                AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->aNeighbourCellList[i].pci);
                AtcAp_StrPrintf_AtcRspBuf((const char *)",%d,%d,%d",
                    pCnf->aNeighbourCellList[i].rsrp,
                    pCnf->aNeighbourCellList[i].rsrq,
                    pCnf->aNeighbourCellList[i].rssi);
                if(0x8000 != pCnf->aNeighbourCellList[i].sinr)
                {
                    AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->aNeighbourCellList[i].sinr);
                }
                else
                {
                    AtcAp_StrPrintf_AtcRspBuf((const char *)",");
                }
                AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->aNeighbourCellList[i].srxlev);
                
                AtcAp_StrPrintf_AtcRspBuf((const char *)",%d,%d,%d,%d",
                    pCnf->aNeighbourCellList[i].u.intra.cell_resel_priority,
                    pCnf->aNeighbourCellList[i].u.intra.s_non_intra_search,
                    pCnf->aNeighbourCellList[i].u.intra.thresh_serving_low,
                    pCnf->aNeighbourCellList[i].u.intra.s_intra_search);
            }
            else
            {
                AtcAp_StrPrintf_AtcRspBuf((const char *)",-,-,-,-,-");
                AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->aNeighbourCellList[i].srxlev);
                AtcAp_StrPrintf_AtcRspBuf((const char *)",%d,%d,%d",
                    pCnf->aNeighbourCellList[i].u.inter.threshX_low, 
                    pCnf->aNeighbourCellList[i].u.inter.threshX_high,
                    pCnf->aNeighbourCellList[i].u.inter.cell_resel_priority);
            }
        }
        
        if(0 != pCnf->ucNeighCellNum)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
        }
    }
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QSIMDET_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QSIMDET_R_CNF_STRU*    pCnf = (ATC_MSG_QSIMDET_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QSIMDET:%d,%d\r\n", pCnf->ucEnable, pCnf->ucInsertLevl);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QSIMSTAT_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QSIMSTAT_R_CNF_STRU*    pCnf = (ATC_MSG_QSIMSTAT_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QSIMSTAT:%d,%d\r\n", pCnf->ucEnable, pCnf->ucState);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QSIMSTAT_Ind(unsigned char* pRecvMsg)
{
    AtcAp_MsgProc_QSIMSTAT_R_Cnf(pRecvMsg);
}

void AtcAp_MsgProc_QNWINFO_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QNWINFO_CNF_STRU*    pCnf             = (ATC_MSG_QNWINFO_CNF_STRU*)pRecvMsg;
    char                        *aucAct[3]        = { "NONE", "FDD LTE", "TDD LTE" };
    unsigned char                aucSelectPlmn[7] = { 0 };
    unsigned char                ucAct;

    if(0 != pCnf->ucBandType)
    {
        return;
    }

    ucAct = (pCnf->ucAct > D_ACT_TDD_LTE ? D_ACT_UNKNOWN : pCnf->ucAct);
    if(D_ACT_UNKNOWN == ucAct)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QNWINFO:\"%s\",,\"\",%d\r\n", aucAct[ucAct], 0xFFFFFFFF);    
    }
    else
    {
        if(0xFFFFFFFF != pCnf->ulOper)
        {
            AtcAp_IntegerToPlmn(pCnf->ulOper, aucSelectPlmn);
        }

        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QNWINFO:\"%s\",%s,\"LTE BAND %d\",%d\r\n", aucAct[ucAct], aucSelectPlmn, pCnf->usBand, pCnf->usChannel);
    }

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QCSQ_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QCSQ_R_CNF_STRU*    pCnf             = (ATC_MSG_QCSQ_R_CNF_STRU*)pRecvMsg;
    
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QCSQ:%d\r\n", pCnf->ucEnable);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QCSQ_E_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QCSQ_E_CNF_STRU*    pCnf             = (ATC_MSG_QCSQ_E_CNF_STRU*)pRecvMsg;

    if(0 == pCnf->ucSysMode)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QCSQ:\"NOSERVICE\"\r\n");
    }
    else
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QCSQ:\"LTE\",%d,%d,%d,%d\r\n", pCnf->sRssi, pCnf->sRsrp, pCnf->sSinr, pCnf->sRsrq);
    }

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QGDCNT_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QGDCNT_R_CNF_STRU*    pCnf             = (ATC_MSG_QGDCNT_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QGDCNT:%ld,%ld\r\n", pCnf->ullDataBytesSend, pCnf->ullDataBytesRecv);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QAUGDCNT_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QAUGDCNT_R_CNF_STRU*    pCnf           = (ATC_MSG_QAUGDCNT_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QAUGDCNT:%d\r\n", pCnf->usValue);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CPOL_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CPOL_R_CNF_STRU* pCnf             = (ATC_MSG_CPOL_R_CNF_STRU*)pRecvMsg;
    unsigned char            i;
    char                     acPlmnNameStr[20] = { 0 };

    if(0 == pCnf->ucNum)
    {
        return;
    }

    for(i = 0; i < pCnf->ucNum; i++)
    {
        if(0xFFFFFF != pCnf->atOperList[i].ulPlmn)
        {
            AtcAp_ConvertPlmn2NameStr(pCnf->atOperList[i].ulPlmn, pCnf->ucFormat, acPlmnNameStr);
            AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CPOL:%d,%d,\"%s\",%d,%d,%d,%d",
                                    i + 1,
                                    pCnf->ucFormat,
                                    acPlmnNameStr,
                                    pCnf->atOperList[i].ucGSMAcT,
                                    pCnf->atOperList[i].ucGSMCompactAcT, 
                                    pCnf->atOperList[i].ucUTRANAcT,
                                    pCnf->atOperList[i].ucEUTRANAcT);
        }
    }
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CPOL_T_Cnf(unsigned char *pRecvMsg)
{
    ATC_MSG_CPOL_T_CNF_STRU*    pCnf  = (ATC_MSG_CPOL_T_CNF_STRU*)pRecvMsg;

    if(0 == pCnf->ucMaxNum)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CPOL:(),(0-2)\r\n");
    }
    else if(1 == pCnf->ucMaxNum)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CPOL:(1),(0-2)\r\n");
    }
    else
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CPOL:(1-%d),(0-2)\r\n", pCnf->ucMaxNum);
    }

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QINISTAT_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QINISTAT_CNF_STRU*    pCnf  = (ATC_MSG_QINISTAT_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QINISTAT:%d\r\n", pCnf->ucStatus);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CSCS_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CSCS_R_CNF_STRU*    pCnf  = (ATC_MSG_CSCS_R_CNF_STRU*)pRecvMsg;

    if(pCnf->ucChset < (sizeof(ATC_CharSet_Table) / sizeof(ST_ATC_STR_TABLE)))
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CSCS:\"%s\"\r\n", (char*)ATC_CharSet_Table[pCnf->ucChset].pucStr);
        AtcAp_SendDataInd(pRecvMsg);
    }
}

void AtcAp_MsgProc_CGSMS_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CGSMS_R_CNF_STRU*    pCnf  = (ATC_MSG_CGSMS_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CGSMS:%d\r\n", pCnf->ucValue);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QCELL_R_Cnf(unsigned char* pRecvMsg)
{    
    ATC_MSG_QCELL_R_CNF_STRU*    pCnf  = (ATC_MSG_QCELL_R_CNF_STRU*)pRecvMsg;
    unsigned char                i;
    const char                  *aucStrCell[3]    = { "servingcell", "neighbourcell intra", "neighbourcell inter" };
    unsigned char                aucSelectPlmn[7] = { 0 };
    char                         aucMCC[4] = { 0 };
    char                         aucMNC[4] = { 0 };
    
    for(i = 0; i < pCnf->tCellInfo.ucNum; i++)
    {
        if(pCnf->tCellInfo.aCellInfo[i].ucCellType > 2)
        {
            continue;
        }
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QCELL:\"%s\",\"LTE\"", aucStrCell[pCnf->tCellInfo.aCellInfo[i].ucCellType]);
        if(0xFFFFFFFF != pCnf->tCellInfo.aCellInfo[i].ulPlmn)
        {
            AtcAp_MemSet(aucSelectPlmn, 0, 7);
            AtcAp_IntegerToPlmn(pCnf->tCellInfo.aCellInfo[i].ulPlmn, aucSelectPlmn);
            strncpy(aucMCC, (char*)aucSelectPlmn, 3);
            strncpy(aucMNC, (char*)aucSelectPlmn+3, 3);
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%s,%s", aucMCC, aucMNC);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)",,");
        }

        AtcAp_StrPrintf_AtcRspBuf((const char *)",%x", pCnf->tCellInfo.aCellInfo[i].usTac);
#if USR_CUSTOM11
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%08x", pCnf->tCellInfo.aCellInfo[i].ulCell_ID);
#else
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%x", pCnf->tCellInfo.aCellInfo[i].ulCell_ID);
#endif
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].usPhyCellId);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].ucRxLev);
    }

    if(pCnf->tCellInfo.ucNum != 0)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
    }
        
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QPINC_Cnf(unsigned char* pRecvMsg)
{
    unsigned char             index;
    ATC_MSG_CPINR_CNF_STRU*   pPinRetriesCnf = (ATC_MSG_CPINR_CNF_STRU*)pRecvMsg;
    unsigned char             ucScFlg = 0, ucPin2Flg = 0;
    unsigned char             ucPin1 = 0, ucPuk1 = 0, ucPin2 = 0, ucPuk2 = 0;

    for(index = 0; index < pPinRetriesCnf->ucNum; index++)
    {
        if(pPinRetriesCnf->aPinRetires[index].ucPinType == D_ATC_CPINR_TYPE_PIN1)
        {
            ucScFlg = 1;
            ucPin1 = pPinRetriesCnf->aPinRetires[index].ucRetriesNum;
        }
        else if(pPinRetriesCnf->aPinRetires[index].ucPinType == D_ATC_CPINR_TYPE_PUK1)
        {
            ucPuk1 = pPinRetriesCnf->aPinRetires[index].ucRetriesNum;
        }
        else if(pPinRetriesCnf->aPinRetires[index].ucPinType == D_ATC_CPINR_TYPE_PIN2)
        {
            ucPin2Flg = 1;
            ucPin2 = pPinRetriesCnf->aPinRetires[index].ucRetriesNum;
        }
        else if(pPinRetriesCnf->aPinRetires[index].ucPinType == D_ATC_CPINR_TYPE_PUK2)
        {
            ucPuk2 = pPinRetriesCnf->aPinRetires[index].ucRetriesNum;
        }
    }

    if(1 == ucScFlg)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+QPINC:\"SC\",%d,%d", ucPin1, ucPuk1);
    }

    if(1 == ucPin2Flg)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+QPINC:\"P2\",%d,%d", ucPin2, ucPuk2);
    }
    
    AtcAp_StrPrintf_AtcRspBuf("\r\n");
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_MLOCKFREQ_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_MLOCKFREQ_R_CNF_STRU*   pCnf   = (ATC_MSG_MLOCKFREQ_R_CNF_STRU*)pRecvMsg;
    unsigned char                   i;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+MLOCKFREQ:%d", pCnf->LockFreqList.ucMode);
    for(i = 0; i < pCnf->LockFreqList.ucNum; i++)
    {
        AtcAp_StrPrintf_AtcRspBuf(",%d", pCnf->LockFreqList.aEarfcnInfo[i].ulEarfcn);
        if(1 == pCnf->LockFreqList.aEarfcnInfo[i].ucPciFlg)
        {
            AtcAp_StrPrintf_AtcRspBuf(",%d", pCnf->LockFreqList.aEarfcnInfo[i].usPci);
        }
        else
        {
            if(i != pCnf->LockFreqList.ucNum - 1)
            {
                AtcAp_StrPrintf_AtcRspBuf(",");
            }
        }
    }
    AtcAp_StrPrintf_AtcRspBuf("\r\n");
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_MLOCKFREQ_T_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_MLOCKFREQ_T_CNF_STRU*   pCnf   = (ATC_MSG_MLOCKFREQ_T_CNF_STRU*)pRecvMsg;
    unsigned char                   i;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+MLOCKFREQ:(0-2),(");

    for(i = 0; i < pCnf->FreqList.nuNum; i++)
    {
        if(pCnf->FreqList.aFreq[i].ulStart != pCnf->FreqList.aFreq[i].ulEnd)
        {
            AtcAp_StrPrintf_AtcRspBuf("%d-%d", pCnf->FreqList.aFreq[i].ulStart, pCnf->FreqList.aFreq[i].ulEnd);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf("%d", pCnf->FreqList.aFreq[i].ulStart);
        }

        if(i != pCnf->FreqList.nuNum - 1)
        {
            AtcAp_StrPrintf_AtcRspBuf(",");
        }
    }
    
    AtcAp_StrPrintf_AtcRspBuf("),(0-503)\r\n");
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_MUESTATS_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_MUESTATS_CNF_STRU*   pCnf   = (ATC_MSG_MUESTATS_CNF_STRU*)pRecvMsg;
    unsigned char                i;
    unsigned char                ucRat  = 4; //LTE
    unsigned char                aucSelectPlmn[7];
    unsigned char                aucMCC[4]={0};
    unsigned char                aucMNC[4]={0};
    char                        *pCellType;

    if(ATC_NUESTATS_TYPE_NOPARAMETER == pCnf->type || ATC_NUESTATS_TYPE_RADIO == pCnf->type || ATC_NUESTATS_TYPE_ALL == pCnf->type)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+MUESTATS:\"radio\",%d,%d,%d,%d,%u,%u,%X,%u,%d,%d,%u,%d\r\n", ucRat,
                pCnf->stRadio.rsrp, pCnf->stRadio.rssi, pCnf->stRadio.current_tx_power_level,
                pCnf->stRadio.total_tx_time, pCnf->stRadio.total_rx_time,
                pCnf->stRadio.last_cell_ID, pCnf->stRadio.last_ECL_value,
                pCnf->stRadio.last_snr_value, pCnf->stRadio.last_earfcn_value, pCnf->stRadio.last_pci_value,pCnf->stRadio.rsrq);
        AtcAp_SendDataInd(pRecvMsg);
    }

    if(ATC_NUESTATS_TYPE_CELL == pCnf->type || ATC_NUESTATS_TYPE_ALL == pCnf->type)
    {
        for(i = 0; i < pCnf->stCell.stCellList.ucCellNum; i++)
        {
            AtcAp_MemSet(aucSelectPlmn, 0, 7);
            AtcAp_IntegerToPlmn(pCnf->stCell.stCellList.aNCell[i].ulPlmn, aucSelectPlmn);
            strncpy((char*)aucMCC, (char*)aucSelectPlmn, 3);
            strncpy((char*)aucMNC, (char*)aucSelectPlmn + 3, 3);

            pCellType = (i == 0 ? "scell" : "ncell");
            
            AtcAp_StrPrintf_AtcRspBuf("\r\n+MUESTATS:\"%s\",%d,%s,%s,%u,%d,%d,%d,%d,%d,%d,%d\r\n", 
                pCellType, ucRat, aucMCC, aucMNC,
                pCnf->stCell.stCellList.aNCell[i].ulDlEarfcn, pCnf->stCell.stCellList.aNCell[i].ucEarfcnOffset,
                pCnf->stCell.stCellList.aNCell[i].usPhyCellId, 
                pCnf->stCell.stCellList.aNCell[i].sRsrp, pCnf->stCell.stCellList.aNCell[i].sRsrq,
                pCnf->stCell.stCellList.aNCell[i].sRssi, pCnf->stCell.stCellList.aNCell[i].sSinr,
                pCnf->stCell.stCellList.aNCell[i].ucDlBandWidth);
        }
        AtcAp_SendDataInd(pRecvMsg);
    }

    if(ATC_NUESTATS_TYPE_BLER == pCnf->type || ATC_NUESTATS_TYPE_ALL == pCnf->type)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+MUESTATS:\"bler\",%d,%d,%d,%d,%d,%d,%d,%d,%d,%d\r\n", 
                   pCnf->stBler.rlc_ul_bler, pCnf->stBler.rlc_dl_bler, pCnf->stBler.mac_ul_bler, pCnf->stBler.mac_dl_bler,
                   pCnf->stBler.total_bytes_transmit, pCnf->stBler.total_bytes_receive,
                   pCnf->stBler.transport_blocks_send, pCnf->stBler.transport_blocks_receive, pCnf->stBler.transport_blocks_retrans,
                   pCnf->stBler.total_ackOrNack_msg_receive);
        AtcAp_SendDataInd(pRecvMsg);
    }

    if(ATC_NUESTATS_TYPE_THP == pCnf->type || ATC_NUESTATS_TYPE_ALL == pCnf->type)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+MUESTATS:\"thp\",%d,%d,%d,%d\r\n",
                   pCnf->stThp.rlc_ul, pCnf->stThp.rlc_dl, pCnf->stThp.mac_ul, pCnf->stThp.mac_dl);
        AtcAp_SendDataInd(pRecvMsg);
    }

    if(ATC_NUESTATS_SBAND == pCnf->type || ATC_NUESTATS_TYPE_ALL == pCnf->type)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+MUESTATS:\"sband\",%d\r\n", pCnf->stBand.band);
        AtcAp_SendDataInd(pRecvMsg);
    }
}

void AtcAp_MsgProc_MEMMTIMER_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_MEMMTIMER_R_CNF_STRU*   pCnf   = (ATC_MSG_MEMMTIMER_R_CNF_STRU*)pRecvMsg;
    
    AtcAp_StrPrintf_AtcRspBuf("\r\n+MEMMTIMER:%d\r\n", pCnf->ucValue);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_MEMMTIMER_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_MEMMTIMER_IND_STRU*   pInd   = (ATC_MSG_MEMMTIMER_IND_STRU*)pRecvMsg;
    
    AtcAp_StrPrintf_AtcRspBuf("\r\n+MEMMTIMER:%d,%d,%d,%d\r\n", 
        pInd->ucBackoffTimerId, pInd->ucEvent, pInd->ulTimerLen_s * 1000, pInd->ulTimerLeftLen_s * 1000);

    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_MUECONFIG_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_MUECONFIG_R_CNF_STRU*   pCnf       = (ATC_MSG_MUECONFIG_R_CNF_STRU*)pRecvMsg;
    char                           *pStrIpType = "";

    if(pCnf->ucFunc >= D_ATC_UECONFIG_MAX)
    {
        return;
    }

    if(D_ATC_UECONFIG_DEFAPN == pCnf->ucFunc)
    {
        switch(pCnf->ucValue1)
        {
            case D_PDP_TYPE_IPV4:
                pStrIpType = "IP";
                break;
            case D_PDP_TYPE_IPV6:
                pStrIpType = "IPV6";
                break;
            case D_PDP_TYPE_IPV4V6:
                pStrIpType = "IPV4V6";
                break;
            case D_PDP_TYPE_NonIP:
                pStrIpType = "Non-IP";
                break;
        }
        AtcAp_StrPrintf_AtcRspBuf("\r\n+MUECONFIG:\"%s\",\"%s\",\"%s\"\r\n", (char*)ATC_UeConfig_Table[D_ATC_UECONFIG_DEFAPN].pucStr, pStrIpType, pCnf->aucValue2);
    }
    else
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+MUECONFIG:\"%s\",%d\r\n", (char*)ATC_UeConfig_Table[pCnf->ucFunc].pucStr, pCnf->ucValue1);
    }
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_MWIFISCANCFG_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_MWIFISCANCFG_R_CNF_STRU*   pCnf         = (ATC_MSG_MWIFISCANCFG_R_CNF_STRU*)pRecvMsg;
    const char                        *aucOptStr[3] = { "hidden", "priority", "max" };

    if(pCnf->ucOpt >= 3)
    {
        return;
    }
    
    AtcAp_StrPrintf_AtcRspBuf("\r\n+MWIFISCANCFG:\"%s\",%d\r\n", (char*)aucOptStr[pCnf->ucOpt], pCnf->ucValue);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_MWIFISCANQUERY_Cnf(unsigned char* pRecvMsg)
{
    unsigned char i;
    ATC_MSG_MWIFISCANQUERT_CNF_STRU* pCnf   = (ATC_MSG_MWIFISCANQUERT_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+MWIFISCANQUERY:%d", pCnf->ucNum);
    for(i = 0; i < pCnf->ucNum; i++)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n%d,,\"%02X:%02X:%02X:%02X:%02X:%02X\",,%d,%d,", i + 1, 
            pCnf->aWifiInfo[i].aucMac[0], pCnf->aWifiInfo[i].aucMac[1], pCnf->aWifiInfo[i].aucMac[2], 
            pCnf->aWifiInfo[i].aucMac[3], pCnf->aWifiInfo[i].aucMac[4], pCnf->aWifiInfo[i].aucMac[5], pCnf->aWifiInfo[i].sRssi, pCnf->aWifiInfo[i].ucChannel);
    }
    AtcAp_StrPrintf_AtcRspBuf("\r\n");
    AtcAp_SendDataInd(pRecvMsg);

}


void AtcAp_MsgProc_QWIFISCAN_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QWIFISCAN_R_CNF_STRU* pCnf   = (ATC_MSG_QWIFISCAN_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+QWIFISCAN:%d,%d,%d,%d,%d\r\n",
                                pCnf->uiTimeout,
                                pCnf->ucRound,
                                pCnf->ucMasIDNum,
                                pCnf->ucScantimeout,
                                pCnf->ucPriority);

    AtcAp_SendDataInd(pRecvMsg);

}

void AtcAp_MsgProc_QWIFISCAN_IND(unsigned char* pRecvMsg)
{
    unsigned char       i = 0;
    ATC_MSG_QWIFISCAN_IND_STRU*   pCnf         = (ATC_MSG_QWIFISCAN_IND_STRU*)pRecvMsg;
    if(pCnf->ucWifiType == D_ATC_QWIFISCAN_CMD)
    {
        if(pCnf->ucNum == 0)
        {
            AtcAp_StrPrintf_AtcRspBuf("\r\n+QWIFISCAN:TIMEOUT\r\n");
        }
        else
        {
            for(i = 0; i < pCnf->ucNum; i++)
            {
                AtcAp_StrPrintf_AtcRspBuf("\r\n+QWIFISCAN:(-,-,%d,\"%02X:%02X:%02X:%02X:%02X:%02X\",%d)",pCnf->aWifiInfo[i].sRssi, 
                    pCnf->aWifiInfo[i].aucMac[0], pCnf->aWifiInfo[i].aucMac[1], pCnf->aWifiInfo[i].aucMac[2], 
                    pCnf->aWifiInfo[i].aucMac[3], pCnf->aWifiInfo[i].aucMac[4], pCnf->aWifiInfo[i].aucMac[5], pCnf->aWifiInfo[i].ucChannel);
            }
            AtcAp_StrPrintf_AtcRspBuf("\r\n\r\nOK\r\n");
        }
    }
    else
    {
        if(pCnf->ucNum != 0)
        {
            for(i = 0; i < pCnf->ucNum; i++)
            {
                AtcAp_StrPrintf_AtcRspBuf("\r\n+MWIFISCANINFO:%d,,\"%02X:%02X:%02X:%02X:%02X:%02X\",,%d,%d,", i + 1, 
                    pCnf->aWifiInfo[i].aucMac[0], pCnf->aWifiInfo[i].aucMac[1], pCnf->aWifiInfo[i].aucMac[2], 
                    pCnf->aWifiInfo[i].aucMac[3], pCnf->aWifiInfo[i].aucMac[4], pCnf->aWifiInfo[i].aucMac[5],
                    pCnf->aWifiInfo[i].sRssi, pCnf->aWifiInfo[i].ucChannel);
            }
        }
        AtcAp_StrPrintf_AtcRspBuf("\r\n+MWIFISCANINFO:0\r\n");
    }
    AtcAp_SendDataInd(pRecvMsg);
}


void AtcAp_MsgProc_CNUM_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CNUM_CNF_STRU*   pCnf         = (ATC_MSG_CNUM_CNF_STRU*)pRecvMsg;
    unsigned char            i;

    for(i = 0; i < pCnf->ucCnt; i++)
    {
        if(145 == pCnf->atSisdn[i].ucType)
        {
            AtcAp_StrPrintf_AtcRspBuf("\r\n+CNUM:\"\",\"+%s\",%d\r\n", (char*)pCnf->atSisdn[i].aucNum, pCnf->atSisdn[i].ucType);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf("\r\n+CNUM:\"\",\"%s\",%d\r\n", (char*)pCnf->atSisdn[i].aucNum, pCnf->atSisdn[i].ucType);
        }
    }
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QIURC_PdpDeact_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_QIURC_PDPDEACT_IND_STRU*   pInd         = (ATC_MSG_QIURC_PDPDEACT_IND_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+QIURC:\"pdpdeact\",%d\r\n", pInd->ucCid);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QBLACKCELL_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QBLACKCELL_R_CNF_STRU*   pCnf         = (ATC_MSG_QBLACKCELL_R_CNF_STRU*)pRecvMsg;
    int                              i;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+QBLACKCELL:2,\"");
    for(i = 0; i < pCnf->ucNum; i++)
    {
        AtcAp_StrPrintf_AtcRspBuf("%d:%d", pCnf->atEarfcnInfo[i].ulEarfcn, pCnf->atEarfcnInfo[i].usPci);
        if(i !=  pCnf->ucNum - 1)
        {
            AtcAp_StrPrintf_AtcRspBuf("&");
        }
    }
    AtcAp_StrPrintf_AtcRspBuf("\"\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QBLACKCELLCFG_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QBLACKCELLCFG_R_CNF_STRU*   pCnf     = (ATC_MSG_QBLACKCELLCFG_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+QBLACKCELLCFG:%d\r\n", pCnf->ucValue);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QSIMSWITCH_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_SIMSWICH_R_CNF_STRU*   pCnf     = (ATC_MSG_SIMSWICH_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+QSIMSWITCH:%d\r\n", pCnf->ucValue);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_PSTEST_Info_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_PSTEST_INFO_IND_STRU*   pInd = (ATC_MSG_PSTEST_INFO_IND_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("%s", pInd->aucInfo);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_PCTTESTINFO_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_PCTTESTINFO_R_CNF_STRU*   pCnf = (ATC_MSG_PCTTESTINFO_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n%s\r\n", pCnf->aucPCTTestSequence);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CIEV_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_CIEV_IND_STRU*   pInd = (ATC_MSG_CIEV_IND_STRU*)pRecvMsg;

    if(D_CIEV_MESSAGE == pInd->ucType)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+CIEV:\"%s\",%d\r\n", "MESSAGE", 1);
    }
    else if(D_CIEV_SMSFULL == pInd->ucType)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+CIEV:\"%s\",%d\r\n", "SMSFULL", pInd->ucValue);
    }
    
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_NPREEARFCN_R_Cnf(unsigned char* pRecvMsg)
{
    unsigned char i = 0;
    ATC_MSG_NPREEARFCN_R_CNF_STRU*   pCnf = (ATC_MSG_NPREEARFCN_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+NPREEARFCN=%d,%d", pCnf->ucOperatorIndex, pCnf->ucNum);
    if(0 != pCnf->ucNum)
    {
        for(; i < pCnf->ucNum ; i++)
        {
            AtcAp_StrPrintf_AtcRspBuf(",%d", pCnf->aulearfcn[i]);
        }
    }
    AtcAp_StrPrintf_AtcRspBuf("\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QSCLKEX_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QSCLKEX_R_CNF_STRU*   pCnf = (ATC_MSG_QSCLKEX_R_CNF_STRU*)pRecvMsg;
#if USR_CUSTOM9
    AtcAp_StrPrintf_AtcRspBuf("\r\n+ECSCLKEX:%d,%d,%d\r\n", pCnf->ucmode, pCnf->ucIdletime, pCnf->usRetrytime);
#else
    AtcAp_StrPrintf_AtcRspBuf("\r\n+QSCLKEX:%d,%d,%d\r\n", pCnf->ucmode, pCnf->ucIdletime, pCnf->usRetrytime);
#endif
    AtcAp_SendDataInd(pRecvMsg);

}

void AtcAp_MsgProc_NWDRX_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_NWDRX_CNF_STRU*   pCnf = (ATC_MSG_NWDRX_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+NWDRX:%d\r\n", pCnf->usNWDrxValue);
    
    AtcAp_SendDataInd(pRecvMsg);

}

#ifdef SIMULATOR_UICC
void AtcAp_MsgProc_SIMUUICC_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_SIMUUICC_R_CNF_STRU* pSimuuiccRCnf;
    unsigned char Imsi[D_ATC_SIMUUICC_IMSI_SIZE_MAX+1]={0};
    unsigned char AuthKey[NAS_AUTH_KEY_LTH*2+1]={0};
    unsigned char AuthOp[NAS_AUTH_OP_LTH*2+1]={0};
    unsigned char i;

    pSimuuiccRCnf = (ATC_MSG_SIMUUICC_R_CNF_STRU*)pRecvMsg;
    if(0 == strcmp((char*)pSimuuiccRCnf->aucInsValue, "IMSI"))
    {
        Imsi[0] = (((pSimuuiccRCnf->u.aucValue[1] & 0xF0) >> 4) + 0x30);
        for (i = 2; i <= pSimuuiccRCnf->u.aucValue[0] && i <= NAS_IMSI_LTH - 1; i++)
        {
            Imsi[2*i-3] = ((pSimuuiccRCnf->u.aucValue[i] & 0x0F) + 0x30);
            Imsi[2*i-2] = (((pSimuuiccRCnf->u.aucValue[i] & 0xF0) >> 4) + 0x30);
        }
        AtcAp_StrPrintf_AtcRspBuf("\r\n+SIMUUICC:\"%s\",\"%s\"\r\n",(unsigned char*)pSimuuiccRCnf->aucInsValue,Imsi);
    }
    else if(0 == strcmp((char*)pSimuuiccRCnf->aucInsValue, "CARDNUM"))
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+SIMUUICC:\"%s\",%d,%d\r\n",(unsigned char*)pSimuuiccRCnf->aucInsValue,(pSimuuiccRCnf->u.ucValue>>4),(pSimuuiccRCnf->u.ucValue&0x0F));
    }
    else if(0 == strcmp((char*)pSimuuiccRCnf->aucInsValue, "AUTH"))
    {
        parse_Hex2Str(AuthKey,&pSimuuiccRCnf->u.aucValue[0],NAS_AUTH_KEY_LTH);
        parse_Hex2Str(AuthOp,&pSimuuiccRCnf->u.aucValue[NAS_AUTH_KEY_LTH],NAS_AUTH_OP_LTH);
        AtcAp_StrPrintf_AtcRspBuf("\r\n+SIMUUICC:\"%s\",\"%s\",\"%s\"\r\n", (unsigned char*)pSimuuiccRCnf->aucInsValue,AuthKey,AuthOp);
    }
    AtcAp_SendDataInd(pRecvMsg);
}
#endif

void AtcAp_MsgProc_SimDataDownload_Ind(unsigned char* pRecvMsg)
{
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CMT:\"ESIM request\"\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}


void AtcAp_MsgProc_CCED_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CCED_CNF_STRU*    pCnf  = (ATC_MSG_CCED_CNF_STRU*)pRecvMsg;
    unsigned char                i = 0;
    const char                  *aucStrCell[3]    = { "servingcell", "neighbourcell intra", "neighbourcell inter" };
    unsigned char                aucSelectPlmn[7] = { 0 };
    char                         aucMCC[4] = { 0 };
    char                         aucMNC[4] = { 0 };

    if (pCnf->ucValue == 1)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CCED:LTE current cell:");
        if(0xFFFFFFFF != pCnf->tCellInfo.aCellInfo[0].ulPlmn)
        {
            AtcAp_MemSet(aucSelectPlmn, 0, 7);
            AtcAp_IntegerToPlmn(pCnf->tCellInfo.aCellInfo[0].ulPlmn, aucSelectPlmn);
            strncpy(aucMCC, (char*)aucSelectPlmn, 3);
            strncpy(aucMNC, (char*)aucSelectPlmn+3, 3);
            AtcAp_StrPrintf_AtcRspBuf((const char *)"%s,%s", aucMCC, aucMNC);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)",");
        }
        if(pCnf->tImsi.ucImsiLen > 0)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%s", pCnf->tImsi.aucImsi);
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)",,");
        }
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->ucRoamingFlg);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->ucBandInfo);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->ucDlBandWith);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[0].ulDlEarfcn);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[0].ulCell_ID);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[0].sRsrp);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[0].sRsrq);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[0].usTac);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[0].ucRxLev);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d\r\n", pCnf->tCellInfo.aCellInfo[0].usPhyCellId);
    }
    else if (pCnf->ucValue == 2)
    {
        for(i = 1; i < pCnf->tCellInfo.ucNum; i++)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CCED:LTE neighbor cell:");
            if(0xFFFFFFFF != pCnf->tCellInfo.aCellInfo[i].ulPlmn)
            {
                AtcAp_MemSet(aucSelectPlmn, 0, 7);
                AtcAp_IntegerToPlmn(pCnf->tCellInfo.aCellInfo[i].ulPlmn, aucSelectPlmn);
                strncpy(aucMCC, (char*)aucSelectPlmn, 3);
                strncpy(aucMNC, (char*)aucSelectPlmn+3, 3);
                AtcAp_StrPrintf_AtcRspBuf((const char *)"%s,%s", aucMCC, aucMNC);
            }
            else
            {
                AtcAp_StrPrintf_AtcRspBuf((const char *)",");
            }       
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].ulDlEarfcn);
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].ulCell_ID);
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].sRsrp);
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].sRsrq);
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].usTac);
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].ucRxLev);
            AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].usPhyCellId);
            if(pCnf->ucBSJFlg == 1)
            {
                AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].ucBandInd);
            }
        }
        if(pCnf->tCellInfo.ucNum > 1)
        {
            AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
        }
        else
        {
            return;
        }
    }
    else if(pCnf->ucValue == 8)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CCED:%d\r\n", pCnf->ucRssi);
    }
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CEMODE_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CEMODE_CNF_STRU*   pCnf = (ATC_MSG_CEMODE_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+CEMODE:%d\r\n", pCnf->ucCEMode);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QIACTEX_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QIACTEX_R_CNF_STRU*    pQiactRCnf;
    unsigned char                i;
    unsigned char                aucIpLocalAddr[16] = { 0 };
    unsigned char                ucIpv6addr_all[16] = { 0 };

    pQiactRCnf = (ATC_MSG_QIACTEX_R_CNF_STRU*)pRecvMsg;

    if (0 != pQiactRCnf->ucValidNum)
    {
        for (i = 0; i < pQiactRCnf->ucValidNum; i++)
        {
            AtcAp_StrPrintf_AtcRspBuf("\r\n+QIACTEX:%d,1,%d", pQiactRCnf->aucPdpDynamicInfo[i].ucCid, pQiactRCnf->aucPdpDynamicInfo[i].ucPdpType);
            switch(pQiactRCnf->aucPdpDynamicInfo[i].ucPdpType)
            {
                case D_PDP_TYPE_IPV4:
                    AtcAp_OutputAddr(4, pQiactRCnf->aucPdpDynamicInfo[i].aucPdpAddrValue + 8, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    break;    
                case D_PDP_TYPE_IPV6:
                    if(ATC_GET_IPV6ADDR_ALL(pQiactRCnf->aucPdpDynamicInfo[i].ucCid, ucIpv6addr_all) == ATC_AP_TRUE)
                    {
                        AtcAp_OutputAddr_IPv6(16, ucIpv6addr_all, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    }
                    else
                    {
                        aucIpLocalAddr[0] = 0xFE;
                        aucIpLocalAddr[1] = 0x80;
                        AtcAp_MemCpy(aucIpLocalAddr + 8, pQiactRCnf->aucPdpDynamicInfo[i].aucPdpAddrValue, 8);
                        
                        AtcAp_OutputAddr_IPv6(16, aucIpLocalAddr, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    }
                    break;
                case D_PDP_TYPE_IPV4V6:
                    AtcAp_OutputAddr(4, pQiactRCnf->aucPdpDynamicInfo[i].aucPdpAddrValue + 8, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    
                    if(ATC_GET_IPV6ADDR_ALL(pQiactRCnf->aucPdpDynamicInfo[i].ucCid, ucIpv6addr_all) == ATC_AP_TRUE)
                    {
                        AtcAp_OutputAddr_IPv6(16, ucIpv6addr_all, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    }
                    else
                    {
                        aucIpLocalAddr[0] = 0xFE;
                        aucIpLocalAddr[1] = 0x80;
                        AtcAp_MemCpy(aucIpLocalAddr + 8, pQiactRCnf->aucPdpDynamicInfo[i].aucPdpAddrValue, 8);
                        
                        AtcAp_OutputAddr_IPv6(16, aucIpLocalAddr, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    }
                    break;
                default:
                    AtcAp_OutputAddr(0, NULL, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
                    break;
            }
        }

        AtcAp_StrPrintf_AtcRspBuf("\r\n");
        AtcAp_SendDataInd(pRecvMsg);
    }
}

void at_QIACTEX_report_urc(uint32_t eventId, uint8_t cid)
{
    char *report_buf = AtcAp_Malloc(256);
    char ip4[XY_IP4ADDR_STRLEN];
    char ip6[XY_IP6ADDR_STRLEN];
    if (eventId == EVENT_PS_IPV4V6_VALID)/* IPv4v6 */
    {
        xy_get_Wan_Ipv4Addr(cid, ip4, XY_IP4ADDR_STRLEN);
        xy_get_Wan_Ipv6Addr(cid, ip6, XY_IP6ADDR_STRLEN);
        snprintf(report_buf, 256, "\r\n+QIACTEX: %d,0,%d,\"%s\",\"%s\"\r\n", cid, D_PDP_TYPE_IPV4V6, ip4, ip6);
    }
    else if (eventId == EVENT_PS_IPV4_VALID)/* v6 not use, ipv4 can use*/
    {
        xy_get_Wan_Ipv4Addr(cid, ip4, XY_IP4ADDR_STRLEN);
        snprintf(report_buf, 256, "\r\n+QIACTEX: %d,0,%d,\"%s\",\"\"\r\n", cid, D_PDP_TYPE_IPV4V6, ip4);
    }
    else if (eventId == EVENT_PS_IPV6_VALID)/* IPv6 Only */
    {
        xy_get_Wan_Ipv6Addr(cid, ip6, XY_IP6ADDR_STRLEN);
        snprintf(report_buf, 256, "\r\n+QIACTEX: %d,0,%d,\"%s\"\r\n", cid, D_PDP_TYPE_IPV6, ip6);
    }
    else if (eventId == EVENT_PS_INVALID)/* ipv4v6 all cann't use*/
    {
        snprintf(report_buf, 256, "\r\n+QIACTEX: %d,561\r\n", cid);
    }
    if(strlen(report_buf) != 0)
    {
        SendAtInd2User(report_buf, (int)(strlen(report_buf)), AT_FD_INVAILD);
    }

    AtcAp_Free(report_buf);

    dereg_data_call_status_cb(cid, EVENT_PS_VALID, at_QIACTEX_report_urc);
}

void AtcAp_MsgProc_QIACTEX_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_QIACTEX_IND_STRU*    pQiactexInd;
    unsigned char                ucIpv6addr_all[16] = { 0 };

    pQiactexInd = (ATC_MSG_QIACTEX_IND_STRU*)pRecvMsg;

    if(ATC_TRUE == pQiactexInd->ucInterfaceReqFlg)
    {
        return;
    }

    if(ATC_FALSE == pQiactexInd->ucViewMode || 0 != pQiactexInd->usResult)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+QIACTEX:%d,%d", pQiactexInd->tPdpDynamicInfo.ucCid, pQiactexInd->usResult);
    }
    else if(pQiactexInd->tPdpDynamicInfo.ucPdpType < D_PDP_TYPE_IPV6)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+QIACTEX:%d,%d,%d", pQiactexInd->tPdpDynamicInfo.ucCid, pQiactexInd->usResult, pQiactexInd->tPdpDynamicInfo.ucPdpType);
        AtcAp_OutputAddr(4, pQiactexInd->tPdpDynamicInfo.aucPdpAddrValue + 8, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
    }
    else if(pQiactexInd->tPdpDynamicInfo.ucPdpType >= D_PDP_TYPE_IPV6 && pQiactexInd->tPdpDynamicInfo.ucPdpType <= D_PDP_TYPE_IPV4V6)
    {
        if(ATC_GET_IPV6ADDR_ALL(pQiactexInd->tPdpDynamicInfo.ucCid, ucIpv6addr_all) == ATC_AP_TRUE)
        {
            AtcAp_StrPrintf_AtcRspBuf("\r\n+QIACTEX:%d,%d,%d", pQiactexInd->tPdpDynamicInfo.ucCid, pQiactexInd->usResult, pQiactexInd->tPdpDynamicInfo.ucPdpType);
            if(pQiactexInd->tPdpDynamicInfo.ucPdpType == D_PDP_TYPE_IPV4V6)
            {
                AtcAp_OutputAddr(4, pQiactexInd->tPdpDynamicInfo.aucPdpAddrValue + 8, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
            }
            AtcAp_OutputAddr_IPv6(16, ucIpv6addr_all, g_AtcApInfo.stAtRspInfo.aucAtcRspBuf);
        }
        else
        {
            xy_printf(0,ATC_AP_T,INFO_LOG,"AtcAp_MsgProc_QIACTEX_Ind wait for ipv6");
            reg_data_call_status_cb(pQiactexInd->tPdpDynamicInfo.ucCid, EVENT_PS_VALID, at_QIACTEX_report_urc);
            return;
        }
    }
    AtcAp_StrPrintf_AtcRspBuf("\r\n");
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QIDEACTEX_Ind(unsigned char* pRecvMsg)
{
    ATC_MSG_QIDEACTEX_IND_STRU*    pQideactexInd;
    unsigned char                ucIpv6addr_all[16] = { 0 };

    pQideactexInd = (ATC_MSG_QIDEACTEX_IND_STRU*)pRecvMsg;

    if(ATC_TRUE == pQideactexInd->ucInterfaceReqFlg)
    {
        return;
    }

    AtcAp_StrPrintf_AtcRspBuf("\r\n+QIDEACTEX:%d,%d\r\n", pQideactexInd->ucCid, pQideactexInd->usResult);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_ICCID_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QCCID_CNF_STRU*    pIccidCnf = (ATC_MSG_QCCID_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n*ICCID:%s\r\n", pIccidCnf->aucICCIDstring);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_BAND_R_Cnf(unsigned char* pRecvMsg)
{
    unsigned char             i;
    unsigned long            ulTDBandMap = 0;
    unsigned long            ulFDBandMap = 0;
    ATC_MSG_NBAND_T_CNF_STRU* pNbandTCnf;

    pNbandTCnf = (ATC_MSG_NBAND_T_CNF_STRU*)pRecvMsg;

    for(i = 0; i < pNbandTCnf->stSupportBandList.ucSupportBandNum; i++)
    {
        if(pNbandTCnf->stSupportBandList.aucSuppBand[i] >= 33
            && pNbandTCnf->stSupportBandList.aucSuppBand[i] <= 48)
        {
            ulTDBandMap = ulTDBandMap | (1 << (pNbandTCnf->stSupportBandList.aucSuppBand[i] - 33));
        }
        else
        {
            ulFDBandMap = ulFDBandMap | (1 << (pNbandTCnf->stSupportBandList.aucSuppBand[i] - 1));
        }
    }
    AtcAp_StrPrintf_AtcRspBuf("\r\n*BAND:5,0,0,%d,%d,0,0,0,0,0\r\n", ulTDBandMap, ulFDBandMap);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_BANDIND_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_BANDIND_R_CNF_STRU*   pCnf = (ATC_MSG_BANDIND_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n*BANDIND:0,%d,7\r\n", pCnf->ucBand);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QINDCFG_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QINDCFG_R_CNF_STRU*   pCnf = (ATC_MSG_QINDCFG_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+QINDCFG:\"all\",%d\r\n", pCnf->ucValue);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QCELLINFO_R_Cnf(unsigned char* pRecvMsg)
{    
    ATC_MSG_QCELL_R_CNF_STRU*    pCnf  = (ATC_MSG_QCELL_R_CNF_STRU*)pRecvMsg;
    unsigned char                i;
#if USR_CUSTOM10
    const char                  *aucStrCell[3]    = { "servingcell", "neighbourcell", "neighbourcell" };
#else
    const char                  *aucStrCell[3]    = { "servingcell", "neighbourcell intra", "neighbourcell inter" };
#endif
    unsigned char                aucSelectPlmn[7] = { 0 };
    char                         aucMCC[4] = { 0 };
    char                         aucMNC[4] = { 0 };
    
    for(i = 0; i < pCnf->tCellInfo.ucNum; i++)
    {
        if(pCnf->tCellInfo.aCellInfo[i].ucCellType > 2)
        {
            continue;
        }
#if USR_CUSTOM10
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QCELLINFO:\"%s\",\"LTE\"", aucStrCell[pCnf->tCellInfo.aCellInfo[i].ucCellType]);
        AtcAp_MemSet(aucSelectPlmn, 0, 7);
        AtcAp_IntegerToPlmn(pCnf->tCellInfo.aCellInfo[i].ulPlmn, aucSelectPlmn);
        strncpy(aucMCC, (char*)aucSelectPlmn, 3);
        strncpy(aucMNC, (char*)aucSelectPlmn+3, 3);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%s,%s", aucMCC, aucMNC);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%x", pCnf->tCellInfo.aCellInfo[i].usTac);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%x", pCnf->tCellInfo.aCellInfo[i].ulCell_ID);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].usPhyCellId);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",-,-");
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].ulDlEarfcn);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d,%d,%d", pCnf->tCellInfo.aCellInfo[i].sRsrp,
                                                             pCnf->tCellInfo.aCellInfo[i].sRssi,
                                                             pCnf->tCellInfo.aCellInfo[i].sSinr);
#else
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QCELLINFO:\"%s\",\"LTE\"", aucStrCell[pCnf->tCellInfo.aCellInfo[i].ucCellType]);
        AtcAp_MemSet(aucSelectPlmn, 0, 7);
        AtcAp_IntegerToPlmn(pCnf->tCellInfo.aCellInfo[i].ulPlmn, aucSelectPlmn);
        strncpy(aucMCC, (char*)aucSelectPlmn, 3);
        strncpy(aucMNC, (char*)aucSelectPlmn+3, 3);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%s,%s", aucMCC, aucMNC);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%x", pCnf->tCellInfo.aCellInfo[i].usTac);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%x", pCnf->tCellInfo.aCellInfo[i].ulCell_ID);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].usPhyCellId);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].ucRxLev);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].sRsrp);
        AtcAp_StrPrintf_AtcRspBuf((const char *)",%d", pCnf->tCellInfo.aCellInfo[i].ulDlEarfcn);
#endif
    }

    if(pCnf->tCellInfo.ucNum != 0)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
    }
        
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_MIPCALL_Ind(unsigned char* pRecvMsg)
{
#if VER_CM
    ATC_MSG_MIPCALL_CNF_STRU*    stMipcall = (ATC_MSG_MIPCALL_CNF_STRU*)pRecvMsg;
	char *report_buf = xy_malloc(256);
	char ip4[XY_IP4ADDR_STRLEN];
	char ip6[XY_IP6ADDR_STRLEN];

    xy_printf(0,ATC_AP_T,INFO_LOG,"at_MIPCALL_report_urc:%d,%d",stMipcall->ucCid,stMipcall->ucPdpType);

	if (stMipcall->ucPdpType == D_PDP_TYPE_IPV4V6)
	{
		xy_get_Wan_Ipv4Addr(stMipcall->ucCid, ip4, XY_IP4ADDR_STRLEN);
		xy_get_Wan_Ipv6Addr(stMipcall->ucCid, ip6, XY_IP6ADDR_STRLEN);
		snprintf(report_buf, 256, "\r\n+MIPCALL: %d,1,\"%s\",\"%s\"\r\n", stMipcall->ucCid, ip4, ip6);
	}
	else if (stMipcall->ucPdpType == D_PDP_TYPE_IPV4)
	{
		xy_get_Wan_Ipv4Addr(stMipcall->ucCid, ip4, XY_IP4ADDR_STRLEN);
		snprintf(report_buf, 256, "\r\n+MIPCALL: %d,1,\"%s\"\r\n", stMipcall->ucCid, ip4);
	}
	else if (stMipcall->ucPdpType == D_PDP_TYPE_IPV6)
	{
		xy_get_Wan_Ipv6Addr(stMipcall->ucCid, ip6, XY_IP6ADDR_STRLEN);
		snprintf(report_buf, 256, "\r\n+MIPCALL: %d,1,\"%s\"\r\n", stMipcall->ucCid, ip6);
	}
	else
	{
        return;
	}

    if(strlen(report_buf) != 0)
    {
        SendAtInd2User(report_buf, (int)(strlen(report_buf)), AT_FD_INVAILD);
    }
    if(stMipcall->ucPdpType >= 1
        && stMipcall->ucPdpType <= 3)
    {
        xy_set_cid_state_mipcallflg(stMipcall->ucCid, 1);
        task_creat_to_kill_reg(stMipcall->ucCid,EVENT_PS_VALID);
        reg_data_call_status_cb(stMipcall->ucCid, EVENT_PS_INVALID, at_MIPCALL_report_urc);
    }
    AtcAp_Free(report_buf);
#endif
}

void AtcAp_MsgProc_QDSIM_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QDSIM_R_CNF_STRU*   pCnf     = (ATC_MSG_QDSIM_R_CNF_STRU*)pRecvMsg;

    if(pCnf->ucSimFlg == ATC_AP_TRUE)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+QDSIM:%d,%d\r\n", pCnf->ucSimNum, pCnf->ucSimState);
    }
    else
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+QDSIM:%d\r\n", pCnf->ucSimNum);
    }
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QCFG_R_Cnf(unsigned char* pRecvMsg)
{
    unsigned char   ucIndex;
    ATC_MSG_QCFG_R_CNF_STRU* pQcfgRCnf;
    unsigned char   aucBand[9] = {0};
    unsigned char   ucFlg = 0;
    unsigned char   addr = 0;
    signed char   i;

    pQcfgRCnf = (ATC_MSG_QCFG_R_CNF_STRU*)pRecvMsg;

    for(i = 0; i < D_ATC_QCFG_MAX; i++)
    {
        if(pQcfgRCnf->ucFunc == ATC_Qcfg_Table[i].ucStrVal)
        {
            break;
        }
    }

#if USR_CUSTOM10
    if(pQcfgRCnf->ucFunc == D_ATC_QCFG_BAND)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+QCFG:\"%s\",", ATC_Qcfg_Table[i].pucStr);
        if(strlen(pQcfgRCnf->stBandInfo.aucGsmBand) == 0)
        {
            AtcAp_StrPrintf_AtcRspBuf("0x0,0x");
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf("0x%s,0x", pQcfgRCnf->stBandInfo.aucGsmBand);
        }
        
        for (i = 0; i < pQcfgRCnf->stBandInfo.mu8Num; i++)
        {
            ucIndex = (pQcfgRCnf->stBandInfo.mau8Band[i] % 8) == 0 ? pQcfgRCnf->stBandInfo.mau8Band[i] / 8 - 1 : (pQcfgRCnf->stBandInfo.mau8Band[i] / 8);
            addr = (pQcfgRCnf->stBandInfo.mau8Band[i] % 8) == 0 ?  7 : (pQcfgRCnf->stBandInfo.mau8Band[i] % 8 - 1);
            aucBand[ucIndex] = aucBand[ucIndex] | (0x01 << addr);
        }

        for(i = 8; i >= 0; i--)
        {
            if(aucBand[i] != 0 || ucFlg == 1)
            {
                if(ucFlg == 0)
                {
                    AtcAp_StrPrintf_AtcRspBuf("%x", aucBand[i]);
                }
                else
                {
                    AtcAp_StrPrintf_AtcRspBuf("%02x", aucBand[i]);
                }
                ucFlg = 1;
            }
        }
        
        if(strlen(pQcfgRCnf->stBandInfo.aucNbiotBand) == 0)
        {
            AtcAp_StrPrintf_AtcRspBuf(",0x0\r\n");
        }
        else
        {
            AtcAp_StrPrintf_AtcRspBuf(",0x%s\r\n", pQcfgRCnf->stBandInfo.aucNbiotBand);
        }
    }
    else 
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+QCFG:\"%s\",%d\r\n", ATC_Qcfg_Table[i].pucStr, pQcfgRCnf->ucValue);
    }
#else
    if(pQcfgRCnf->ucFunc == D_ATC_QCFG_BAND)
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+QCFG:%s,0x0,0x", ATC_Qcfg_Table[i].pucStr);
        for (i = 0; i < pQcfgRCnf->stBandInfo.mu8Num; i++)
        {
            ucIndex = (pQcfgRCnf->stBandInfo.mau8Band[i] % 8) == 0 ? pQcfgRCnf->stBandInfo.mau8Band[i] / 8 - 1 : (pQcfgRCnf->stBandInfo.mau8Band[i] / 8);
            addr = (pQcfgRCnf->stBandInfo.mau8Band[i] % 8) == 0 ?  7 : (pQcfgRCnf->stBandInfo.mau8Band[i] % 8 - 1);
            aucBand[ucIndex] = aucBand[ucIndex] | (0x01 << addr);
        }

        for(i = 8; i >= 0; i--)
        {
            if(aucBand[i] != 0 || ucFlg == 1)
            {
                if(ucFlg == 0)
                {
                    AtcAp_StrPrintf_AtcRspBuf("%x", aucBand[i]);
                }
                else
                {
                    AtcAp_StrPrintf_AtcRspBuf("%02x", aucBand[i]);
                }
                ucFlg = 1;
            }
        }
        AtcAp_StrPrintf_AtcRspBuf("\r\n");
    }
    else 
    {
        AtcAp_StrPrintf_AtcRspBuf("\r\n+QCFG:%s,%d\r\n", ATC_Qcfg_Table[i].pucStr, pQcfgRCnf->ucValue);
    }
#endif
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_CUSD_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_CUSD_R_CNF_STRU*   pCnf     = (ATC_MSG_CUSD_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf("\r\n+CUSD:%d\r\n", pCnf->ucValue);
    AtcAp_SendDataInd(pRecvMsg);
}

void AtcAp_MsgProc_QEHPLMN_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QEHPLMN_R_CNF_STRU*  pCnf =(ATC_MSG_QEHPLMN_R_CNF_STRU*) pRecvMsg;
    unsigned char                i,j;
    char                         aucPlmnStr[7];

    for(i = 0; i < 4; i++)
    {
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+QEHPLMN:%d,\"", i);

        for(j = 0; j < 15; j++)
        {
            if(0 == pCnf->aulPlmn[i][j])
            {
                break;
            }
            
            AtcAp_MemSet(aucPlmnStr, 0, 7);
            AtcAp_IntegerToPlmn(pCnf->aulPlmn[i][j], (unsigned char*)aucPlmnStr);
            AtcAp_StrPrintf_AtcRspBuf((const char *)"%s", aucPlmnStr);
            if(j < 14 && 0 != pCnf->aulPlmn[i][j+1])
            {
                AtcAp_StrPrintf_AtcRspBuf((const char *)",");
            }
        }
        
        AtcAp_StrPrintf_AtcRspBuf((const char *)"\"");
    }
    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n");
    
    AtcAp_SendDataInd(pRecvMsg);
}

#if USR_CUSTOM12
void AtcAp_MsgProc_CCID_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_QCCID_CNF_STRU*    pUccidCnf = (ATC_MSG_QCCID_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+CCID:%s\r\n", pUccidCnf->aucICCIDstring);

    AtcAp_SendDataInd(pRecvMsg);
}

#endif

void AtcAp_MsgProc_ECSIMCFG_R_Cnf(unsigned char* pRecvMsg)
{
    ATC_MSG_ECSIMCFG_R_CNF_STRU*    pEcsimCnf = (ATC_MSG_ECSIMCFG_R_CNF_STRU*)pRecvMsg;

    AtcAp_StrPrintf_AtcRspBuf((const char *)"\r\n+ECSIMCFG:\"SimSlot\",%d\r\n", pEcsimCnf->ucSimSlot);

    AtcAp_SendDataInd(pRecvMsg);
}
#ifdef SIB16_FEATURE
void uint64_into_char(uint64_t IntValue,unsigned char* pChar)
{
    unsigned char   i = 0;
    unsigned char   temp = 0;
    unsigned char   num = 0;
    while (IntValue != 0) 
    {
        pChar[i] = '0' + (char)(IntValue % 10);
        IntValue = IntValue / 10;
        i++;
    }
    pChar[i] = '\0';
    num = i;
    for(i = 0; i < num / 2; i++)
    {
        temp = pChar[i];
        pChar[i] = pChar[num - 1 - i];
        pChar[num - 1 - i] = temp;
    }
}

void AtcAp_MsgProc_QUTCTIME_Ind(unsigned char* pRecvMsg)
{
    unsigned char       aucUtcTime[30] = {0};
    ATC_MSG_QUTCTIME_IND_STRU*   pCnf = (ATC_MSG_QUTCTIME_IND_STRU*)pRecvMsg;
    if(pCnf->tSib16Info.ullGpsTime == 0)
    {
        aucUtcTime[0] = '0';
    }
    else
    {
        uint64_into_char(pCnf->tSib16Info.ullGpsTime, aucUtcTime);
    }
    AtcAp_StrPrintf_AtcRspBuf("\r\n+QUTCTIME:%d,%d/%d/%d,%d:%d:%d:%d,%s\r\n", pCnf->tSib16Info.ucState,
                                                                           pCnf->tSib16Info.year, pCnf->tSib16Info.month, pCnf->tSib16Info.day,
                                                                           pCnf->tSib16Info.hour, pCnf->tSib16Info.minute, pCnf->tSib16Info.second,
                                                                           pCnf->tSib16Info.millisecond * 10,
                                                                           aucUtcTime);

    AtcAp_SendDataInd(pRecvMsg);

}
#endif