get_sub_header_dirs(INCLUDE_ALI_DIRS ${CMAKE_CURRENT_LIST_DIR})

file(GLOB_RECURSE ALI_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/aliyun/*.c"
)

shorten_src_file_macro_without_warning(${ALI_SOURCES})

target_sources(application
    PRIVATE 
        ${ALI_SOURCES}
)

# 2M版本放PSRAM做压缩
if(SPACE_OPTIMIZATION)
    relocate_code(CODE_LOCATION PSRAM CODE_TARGET application SOURCES_LIST ${ALI_SOURCES})
endif()
