get_sub_header_dirs(INCLUDE_AUDIO_DIRS ${CMAKE_CURRENT_LIST_DIR})

file(GLOB_RECURSE AUDIO_C_SOURCES_PSRAM 
    # "${CMAKE_CURRENT_SOURCE_DIR}/audio/audio_core/hal/src/i2s_device.c"
)

file(GLOB_RECURSE AUDIO_C_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/audio/*.c"
)

file(GLOB_RECURSE AUDIO_CPP_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/audio/*.cpp"
)

file(GLOB_RECURSE exclude_files 
    "${CMAKE_CURRENT_SOURCE_DIR}/audio/audio_core/codec/minimp4/*.c"
)
list(REMOVE_ITEM AUDIO_C_SOURCES ${exclude_files})

if(NOT TTS)
    file(GLOB_RECURSE exclude_files "${CMAKE_CURRENT_SOURCE_DIR}/audio/xy_audio/src/ivTTS_al.c")
    list(REMOVE_ITEM AUDIO_C_SOURCES ${exclude_files})
endif()

shorten_src_file_macro(${AUDIO_C_SOURCES})
shorten_src_file_macro_without_warning(${AUDIO_CPP_SOURCES})

target_sources(application
    PRIVATE 
        ${AUDIO_C_SOURCES}
        ${AUDIO_CPP_SOURCES}
)

target_include_directories(application
    PUBLIC
        # ${INCLUDE_AUDIO_DIRS}
        # ${INCLUDE_AUDIO_DIRS}/inc
        ${PROJECT_SOURCE_DIR}/lib/aisound
)

relocate_code(CODE_LOCATION PSRAM CODE_TARGET application SOURCES_LIST ${AUDIO_C_SOURCES_PSRAM})