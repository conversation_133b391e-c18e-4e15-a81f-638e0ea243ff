#ifndef __ALSA_INSTANCE_H__
#define __ALSA_INSTANCE_H__

#include <stdint.h>
#include "alsa_os.h"
#include "alsa.h"
#include "cqueue.h"

typedef enum alsa_instance_type
{
    ALSA_INS_UNKOWN,
    ALSA_INS_PLAYBACK,
    ALSA_INS_CAPTURE,
    ALSA_INS_MAX,
} alsa_instance_type_t;

struct alsa_instance
{
    uint8_t instance_id;
    alsa_state_t state;
    alsa_instance_type_t type;
    uint8_t *buffer;
    uint32_t buffer_size;
    alsa_os_mutex_t lock;
    CQueue cqueue;
    uint8_t used;
    alsa_pcm_state_t pcm_state;
    void *user_arg;
#ifdef ALSA_SUPPORT_THRESHOLD
    uint8_t threshold_enable;
    uint32_t threshold;
#endif
#ifdef ALSA_MIXER_PRIORITY_EN
    uint8_t mixer_priority;
    bool mixer_fadeout;
#endif
};

typedef struct alsa_instance alsa_instance_t;
typedef int alsa_instance_id_t;
typedef unsigned char alsa_instance_count_t;
typedef unsigned char alsa_instance_bit_map_t;

typedef struct
{
    alsa_instance_count_t target_instance_count;
    alsa_instance_bit_map_t target_instance_bit_map;
    uint32_t target_size_array[ALSA_INSTANCE_COUNT];
} alsa_instance_info_t;

#ifdef __cplusplus
extern "C"
{
#endif

    void alsa_instance_init(void);

    alsa_instance_t *alsa_instance_open(alsa_instance_type_t type, uint32_t instance_buffer_size);

    int alsa_instance_close(alsa_instance_t *alsa_instance);

    /**
     * Not block. return real write length or -1.
     * **/
    int alsa_instance_write(alsa_instance_t *alsa_instance, uint8_t *buf, uint32_t len);

    /**
     * Not blcok. return real read length or -1.
     * **/
    int alsa_instance_read(alsa_instance_t *alsa_instance, uint8_t *buf, uint32_t len);

    int alsa_instance_flush(alsa_instance_t *alsa_instance);

    int alsa_instance_get_readable_length(alsa_instance_t *alsa_instance);

    int alsa_instance_get_writeable_length(alsa_instance_t *alsa_instance);

    int alsa_instance_id_check_avail(alsa_instance_id_t id);

    alsa_instance_id_t alsa_instance_get_id(alsa_instance_t *alsa_instance);

    alsa_instance_t *alsa_get_instance_by_id(alsa_instance_id_t id);

    int alsa_instance_id_bind_user_arg(alsa_instance_id_t id, void *user_arg);

    void *alsa_instance_get_user_arg_by_id(alsa_instance_id_t id);

    alsa_state_t alsa_instance_get_state(alsa_instance_t *alsa_instance);

    int alsa_instance_set_state(alsa_instance_t *alsa_instance, alsa_state_t state);

    int alsa_get_all_instance_info(alsa_instance_info_t *info, alsa_instance_type_t target_ins_type);

    int alsa_all_instance_sw_gain_process(alsa_instance_info_t *info, uint32_t len);

    int alsa_instance_set_threshold(alsa_instance_t *alsa_instance, uint32_t threshold);

    int alsa_instance_threshold_enable(alsa_instance_t *alsa_instance, uint8_t enable);

    int alsa_instance_set_mixer_priority(alsa_instance_id_t instance_id, uint8_t priority);

    bool alsa_instance_get_mixer_fadeout_state(alsa_instance_id_t instance_id);

    int alsa_instance_mixer_fadeout_state_reset(alsa_instance_id_t instance_id);

    int alsa_instance_lock(alsa_instance_t *alsa_instance, uint8_t lock);

#ifdef __cplusplus
}
#endif

#endif
