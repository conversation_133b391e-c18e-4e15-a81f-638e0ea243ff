/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "at_ctl.h"
#include "at_com.h"
#include "at_cmd_basic.h"
#include "at_fs.h"

#if ONET_LWM
#include "at_onenet.h"
#endif

#if DM_CMCC
#include "cmcc_dm.h"
#endif

#if DM_CTCC
#include "telecom_at.h"
#endif

#include "at_ping.h"


#if XY_PERF
#include "at_perf.h"
#endif

#if VER_MN316
#include "at_mn316_socket.h"
#include "at_mn316_basic.h"
#endif

#if AT_SOCK_QUEC
#include "at_socket_default.h"
#endif

#if AT_FTP_QUEC
#include "at_ftp_default.h"
#endif

#if CTWING_LWM
#include "at_ctwing.h"
#endif

#if CDP_LWM
#include "at_cdp.h"
#endif

#if XY_PPP
#include "at_ppp.h"
#endif

#if XY_CMUX
#include "at_cmux.h"
#endif

#include "xy_fota_api.h"
#include "at_fota.h"

#if XY_AUDIO
#include "at_audio_cmd.h"
#endif

#include "at_cmd_usr.h"

#if AT_FS_QUEC
#include "at_fs.h"
#endif

#if AT_MQTT_QUEC
#include "at_mqtt_default.h"
#endif

#if DMP_MQTT
#include "at_uni_mqtt.h"
#if NET_LOG
#include "at_uni_mqtt_netlog.h"
#endif
#endif

#if CTWING_MQTT
#include "ctmq_at.h"
#endif

#include "at_socket_ssl.h"

#if AT_HTTP_QUEC
#include "at_http.h"
#endif

#if DMP_LWM
#include "at_unilwm2m.h"
#endif

#if USR_CUSTOM11
#include "at_dev_auth.h"
#endif

#if (XY_GNSS1 || XY_GNSS2)
#include "gnss_at.h"
#endif

/*********中移AT命令*********/
#if VER_CM
#include "at_cm_basic.h"
#include "at_cm_socket.h"
#include "at_cmcc_fs.h"
#include "at_cm_ssl.h"
#include "at_cm_http.h"
#include "at_cm_mqtt.h"
#include "at_cm_fota.h"

#if CM_COT_ENABLE
#include "cot_onenet_cmd_proc.h"
#endif
#if COT_CMDMP
#include "cm_dmp_atcmd.h"
#endif

#endif

#if ATCTL_EN
struct at_serv_proc_e at_basic_req[] = {
	/********************************************************************************
	***************************SDK基础命令+独立功能+公有云******************************
	********************************************************************************/
	{"NV", at_NV_req},
	{"NRB", at_NRB_req},
	{"RESET", at_RESET_req},
	{"LOCK", at_LOCK_req},
	{"FORCEDL", at_FORCEDL_req},
	{"CCLK", at_CCLK_req},
	{"ATE", at_ATE_req},
	{"F", at_ANDF_req},
	{"V", at_ANDV_req},
	{"W", at_ANDW_req},
	{"ATZ", at_ATZ_req},
	{"ATV", at_ATV_req},
	{"ATQ", at_ATQ_req},
	{"XYACT", at_XYACT_req},

#if (LPUART_AT || UART_AT)
	{"IPR", at_IPR_req},
	{"IFC", at_IFC_req},
	{"C", at_ANDC_req},
	{"D", at_ANDD_req},
#if USR_CUSTOM8 || USR_CUSTOM14
	{"CBAUD", at_CBAUD_req},
	{"ATS24", at_ATS24_req},
#endif
#endif /* (LPUART_AT | UART_AT) */

	{"ATO", at_ATO_req},
	{"ATS0", at_ATS0_req},
	{"ATS3", at_ATS3_req},
	{"ATS4", at_ATS4_req},
	{"ATS5", at_ATS5_req},
	{"AUTHID", at_AUTHID_req},
	{"REGTEST", at_DEBUG_req},
	{"HEAPINFO", at_HEAPINFO_req},
	{"DEBUG", at_DEBUG_req},
	{"SRRC", at_SRRC_req},

	/****************************************** */
	/*打桩指令，勿删，zjt*/
	{"QIREGAPP", at_QIREGAPP_req},
	{"QCAMCFG", at_QCAMCFG_req},
	{"REQLIC", at_REQLIC_req},
	{"LIC", at_LIC_req},
	{"TYAUTH", at_TYAUTH_req},
	/****************************************** */
	{"NSET", at_NSET_req},

#if (LBS_ONEOS || LBS_AMAP)
	{"MLBSCFG", at_LBSCFG_req},
	{"MLBSLOC", at_LBSLOC_req},
#endif

#if (XY_GNSS1 || XY_GNSS2)
	{"GNSS", at_GNSS_req},
#endif

#if XY_TEST
	{"TEST", at_TEST_req},
	{"FLASHTEST", at_FLASHTEST_req},
	{"RTCTEST", at_RTCTEST_req},
	{"SOCKETTEST", at_SOCKETTEST_req},
#endif	

#if XY_PERF
	{"XYPERF", at_XYPERF_req},
#endif /* XY_PERF */


#if XY_PPP
	{"ATD", at_ATD_req},
	{"ATH", at_ATH_req},
	{"QPPPDROP", at_QPPPDROP_req},	
#endif /* XY_PPP */

 
#if XY_CMUX
	{"CMUX", at_CMUX_req},
#endif /* XY_CMUX */


#if XY_AUDIO
	{"MAUDPLCFG", at_MAUDPLCFG_req},
	{"MAUDPLFILE", at_MAUDPLFILE_req},
	{"MAUDPLSTOP", at_MAUDPLSTOP_req},
	{"MAUDPLPAUSE", at_MAUDPLPAUSE_req},
	{"MAUDPLRESUME", at_MAUDPLRESUME_req},
#if XY_AUDIO_RECORD
	{"MAUDRECCFG", at_MAUDRECCFG_req},
	{"MAUDRECFILE", at_MAUDRECFILE_req},
	{"MAUDRECSTOP", at_MAUDRECSTOP_req},
	{"MAUDRECPAUSE", at_MAUDRECPAUSE_req},
	{"MAUDRECRESUME", at_MAUDRECRESUME_req},
#endif /* XY_AUDIO_RECORD */
#if XY_TTS
	{"MTTSCFG", at_MTTSCFG_req},
	{"MTTSPLAY", at_MTTSPLAY_req},
	{"MTTSSTOP", at_MTTSSTOP_req},
#endif /* XY_TTS */
#endif /* XY_AUDIO */

	/*电信云*/
#if CTWING_LWM
	{"CTLWVER", at_CTLWVER_req},
	{"CTLWGETSRVFRMDNS", at_CTLWGETSRVFRMDNS_req},
	{"CTLWSETSERVER", at_CTLWSETSERVER_req},
	{"CTLWSETLT", at_CTLWSETLT_req},
	{"CTLWSETPSK", at_CTLWSETPSK_req},
	{"CTLWSETAUTH", at_CTLWSETAUTH_req},
	{"CTLWSETPCRYPT", at_CTLWSETPCRYPT_req},
	{"CTLWSETMOD", at_CTLWSETMOD_req},
	{"CTLWREG", at_CTLWREG_req},
	{"CTLWUPDATE", at_CTLWUPDATE_req},
	{"CTLWDTLSHS", at_CTLWDTLSHS_req},
	{"CTLWDEREG", at_CTLWDEREG_req},
	{"CTLWGETSTATUS", at_CTLWGETSTATUS_req},
	{"CTLWCFGRST", at_CTLWCFGRST_req},
	{"CTLWSESDATA", at_CTLWSESDATA_req},
	{"CTLWSEND", at_CTLWSEND_req},
	{"CTLWRECV", at_CTLWRECV_req},
	{"CTLWGETRECVDATA", at_CTLWGETRECVDATA_req},
	{"CTLWSETREGMOD", at_CTLWSETREGMOD_req},
#endif/* CTWING_LWM */

#if CTWING_MQTT
	{"CTMQCFG", at_CTMQCFG_req},
	{"CTMQWILL", at_CTMQWILL_req},
	{"CTMQTLS", at_CTMQTLS_req},
	{"CTMQTLSADD", at_CTMQTLSADD_req},
	{"CTMQTLSDEL", at_CTMQTLSDEL_req},
	{"CTMQCONN", at_CTMQCONN_req},
	{"CTMQCLOSE", at_CTMQCLOSE_req},
	{"CTMQSUB", at_CTMQSUB_req},
	{"CTMQUNSUB", at_CTMQUNSUB_req},
	{"CTMQPUB", at_CTMQPUB_req},
	{"CTMQPUBEX", at_CTMQPUBEX_req},
	{"CTMQREAD", at_CTMQREAD_req},
	{"CTMQCFRECV", at_CTMQCFRECV_req},
	{"CTMQSTAT", at_CTMQSTAT_req},
	{"CTMQFOTACTR", at_CTMQFOTACTR_req},
#endif //CTWING_MQTT

#if DM_CTCC
	{"ZDMSWITCH", at_ZDMSWITCH_req},
	{"ZUEDMACTIVE", at_ZUEDMACTIVE_req},
#endif 

#if CDP_LWM
	{"NCDP", at_NCDP_req},
	{"NMGS", at_QLWULDATA_req},
	{"NMGSEXT", at_NMGS_EXT_req},
	{"NMGR", at_NMGR_req},
	{"NNMI", at_NNMI_req},
	{"NSMI", at_NSMI_req},
	{"NQMGS", at_NQMGS_req},
	{"NQMGR", at_NQMGR_req},
	{"QLWSREGIND", at_QLWSREGIND_req},
	{"QREGSWT", at_QREGSWT_req},
	{"CDPUPDATE", at_CDPUPDATE_req},
	{"NMSTATUS", at_NMSTATUS_req},
	{"QLWULDATA", at_QLWULDATA_req},
	{"QLWULDATAEX", at_QLWULDATAEX_req},
	{"QLWULDATASTATUS", at_QLWULDATASTATUS_req},
	{"CDPRMLFT", at_CDPRMLFT_req},
	{"QSECSWT", at_QSECSWT_req},
	{"QSETPSK", at_QSETPSK_req},
	{"QRESETDTLS", at_QRESETDTLS_req},
	{"QDTLSSTAT", at_QDTLSSTAT_req},
	{"QLWFOTAIND", at_QLWFOTAIND_req},
	{"QLWEVTIND", at_QLWEVTIND_req},
	{"QCRITICALDATA", at_QCRITICALDATA_req},
	{"QSREGENABLE", at_QSREGENABLE_req},
#endif

/*中移云*/
#if ONET_LWM
	{"MIPLCONFIG", at_proc_miplconfig_req},
	{"MIPLCREATE", at_onenet_create_req},
	{"MIPLDELETE", at_proc_mipldel_req},
	{"MIPLOPEN", at_proc_miplopen_req},
	{"MIPLADDOBJ", at_proc_mipladdobj_req},
	{"MIPLDELOBJ", at_proc_mipldelobj_req},
	{"MIPLCLOSE", at_proc_miplclose_req},
	{"MIPLNOTIFY", at_proc_miplnotify_req},
	{"MIPLREADRSP", at_proc_miplread_req},
	{"MIPLWRITERSP", at_proc_miplwrite_req},
	{"MIPLEXECUTERSP", at_proc_miplexecute_req},
	{"MIPLOBSERVERSP", at_proc_miplobserve_req},
	{"MIPLVER", at_proc_miplver_req},
	{"MIPLDISCOVERRSP", at_proc_discover_req},
	{"MIPLPARAMETERRSP", at_proc_setparam_req},
	{"MIPLUPDATE", at_proc_update_req},
	{"ONETRMLFT", at_proc_rmlft_req},
#endif	

#if ANDLINK_LWM
	{"QLACONFIG", at_proc_qlaconfig_req},
	{"QLACFG", at_proc_qlacfg_req},
	{"QLAREG", at_proc_qlareg_req},
	{"QLAUPDATE", at_proc_qlaupdate_req},
	{"QLADEREG", at_proc_qladereg_req},
	{"QLAADDOBJ", at_proc_qlaaddobj_req},
	{"QLADELOBJ", at_proc_qladelobj_req},
	{"QLARDRSP", at_proc_qlardrsp_req},
	{"QLAWRRSP", at_proc_qlawrrsp_req},
	{"QLAEXERSP", at_proc_qlaexersp_req},
	{"QLAOBSRSP", at_proc_qlaobsrsp_req},
	{"QLANOTIFY", at_proc_qlanotify_req},
	{"QLARD", at_proc_qlard_req},
	{"QLASTATUS", at_proc_qlstatus_req},
	{"QLARECOVER", at_proc_qlarecover_req},
#endif

#if DM_CMCC
	{"XYDMP", at_XYDMP_req},
#endif 

	/*联通云*/
#if DMP_LWM
	{"UNILWVER", at_UNILWVER_req},
	{"UNILWCFG", at_UNILWCFG_req},
	{"UNILWREG", at_UNILWREG_req},
	{"UNILWUNREG", at_UNILWUNREG_req},
	{"UNILWSEND", at_UNILWSEND_req},
	{"UNILWUPDATE", at_UNILWUPDATE_req},
	{"UNILWSTATE", at_UNILWSTATE_req},
#endif

#if DMP_MQTT
	{"UNIKEYINFOM", at_UNIKEYINFOM_req},
	{"UNIDELKEYINFOM", at_UNIDELKEYINFOM_req},
	{"UNIKEYINFO", at_UNIKEYINFO_req},
	{"UNIDELKEYINFO", at_UNIDELKEYINFO_req},
	{"UNICERTINFO", at_UNICERTINFO_req},
	{"UNISHCERTINFO", at_UNISHCERTINFO_req},
	{"UNIDELCERTINFO", at_UNIDELCERTINFO_req},
	{"UNIAUTOREG", at_UNIAUTOREG_req},
	{"UNIMQTTCON", at_UNIMQTTCON_req},
	{"UNIMQTTDISCON", at_UNIMQTTDISCON_req},
	{"UNIMQTTSTATE", at_UNIMQTTSTATE_req},
	{"UNIMQTTSUB", at_UNIMQTTSUB_req},
	{"UNIMQTTPUB", at_UNIMQTTPUB_req},
	{"UNIPSMSET", at_UNIPSMSET_req},
	{"CUFOTACHK", at_CUFOTACHK_req},
	{"CUFOTAUPD", at_CUFOTAUPD_req},
#if NET_LOG
	{"NETLOGCFG", at_NETLOGCFG_req},
	{"UNINETLOG", at_UNINETLOG_req},
#endif /* NET_LOG */
	{"UNIAUTOREGCFG", at_UNIAUTOREGCFG_req},
#endif //DMP_MQTT

	/********************************************************************************
	******************************移远系列基础AT命令*********************************
	********************************************************************************/
	
#if VER_QUEC

#if AT_BASE_QUEC
	{"QSCLK", at_QSCLK_req},
	{"QPOWD", at_QPOWD_req},

	{"QCFG", at_QCFG_req},
	{"QURCCFG", at_QURCCFG_req},
	{"QIDNSGIP", at_QIDNSGIP_req},
	{"QIDNSCFG", at_QIDNSCFG_req},
	{"QLTS", at_QLTS_req},
	{"QADC", at_QADC_req},
	//{"ECADC", at_ECADC_req},
	{"CIPGSMLOC", at_CIPGSMLOC_req},
	{"QRFTESTMODE", at_QRFTESTMODE_req},
	{"QIGETERROR", at_QIGETERROR_req},
	{"QNETDEVCTL", at_QNETDEVCTL_req},
#endif  


#if AT_PING_QUEC
	{"QPING", at_QPING_req},
#endif

#if AT_NTP_QUEC
	{"QNTP", at_QNTP_req},
#endif


#if AT_SOCK_QUEC
	{"QICFG", at_QICFG_req},
	{"QISDE", at_QISDE_req},
	{"QIOPEN", at_QIOPEN_req},
	{"QICLOSE", at_QICLOSE_req},
	{"QISTATE", at_QISTATE_req},
	{"QISEND", at_QISEND_req},
	{"QIRD", at_QIRD_req},
	{"QISENDEX", at_QISENDEX_req},
	{"QISWTMD", at_QISWTMD_req},
#endif 


#if (AT_SSL_QUEC)
	{"QSSLCFG", at_QSSLCFG_req},

	{"QSSLOPEN", at_QSSLOPEN_req},
	{"QSSLSEND", at_QSSLSEND_req},
	{"QSSLRECV", at_QSSLRECV_req},
	{"QSSLCLOSE", at_QSSLCLOSE_req},
	{"QSSLSTATE", at_QSSLSTATE_req},
#endif

#if AT_HTTP_QUEC
	{"QHTTPCFG", at_QHTTPCFG_req},
	{"QHTTPURL", at_QHTTPURL_req},
	{"QHTTPGET", at_QHTTPGET_req},
	{"QHTTPGETEX", at_QHTTPGETEX_req},
	{"QHTTPPOST", at_QHTTPPOST_req},
	{"QHTTPPOSTFILE", at_QHTTPPOSTFILE_req},
	{"QHTTPREAD", at_QHTTPREAD_req},
	{"QHTTPREADFILE", at_QHTTPREADFILE_req},
	{"QHTTPSTOP", at_QHTTPSTOP_req},
#endif

#if AT_MQTT_QUEC
	{"QMTCFG", at_QMTCFG_req},
	{"QMTOPEN", at_QMTOPEN_req},
	{"QMTCLOSE", at_QMTCLOSE_req},
	{"QMTDISC", at_QMTDISC_req},
	{"QMTCONN", at_QMTCONN_req},
	{"QMTSUB", at_QMTSUB_req},
	{"QMTUNS", at_QMTUNS_req},
#if USR_CUSTOM10
	{"QMTPUB", at_QMTPUB_req},
#endif
	{"QMTPUBEX", at_QMTPUBEX_req},
	{"QMTRECV", at_QMTRECV_req},
#endif 

#if AT_FOTA_QUEC
	{"QFOTADL", at_QFOTADL_req},
#endif 


#if AT_FS_QUEC
	// 文件系统AT指令
	{"QFLDS", at_QFLDS_req},
	{"QFLST", at_QFLST_req},
	{"QFDEL", at_QFDEL_req},
	{"QFUPL", at_QFUPL_req},
	{"QFDWL", at_QFDWL_req},
	{"QFOPEN", at_QFOPEN_req},
	{"QFREAD", at_QFREAD_req},
	{"QFWRITE", at_QFWRITE_req},
	{"QFSEEK", at_QFSEEK_req},
	{"QFPOSITION", at_QFPOSITION_req},
	{"QFCLOSE", at_QFCLOSE_req},	
	//{"QFMKDIR", at_QFMKDIR_req},	
	//{"QFRMDIR", at_QFRMDIR_req},	
#endif

#if AT_FTP_QUEC
	{"QFTPCFG", at_QFTPCFG_req},
	{"QFTPOPEN", at_QFTPOPEN_req},
	{"QFTPCWD", at_QFTPCWD_req},
	{"QFTPPWD", at_QFTPPWD_req},
	{"QFTPPUT", at_QFTPPUT_req},
	{"QFTPGET", at_QFTPGET_req},
	{"QFTPSIZE", at_QFTPSIZE_req},
	{"QFTPDEL", at_QFTPDEL_req},
	{"QFTPMKDIR", at_QFTPMKDIR_req},
	{"QFTPRMDIR", at_QFTPRMDIR_req},
	{"QFTPLIST", at_QFTPLIST_req},
	{"QFTPNLST", at_QFTPNLST_req},
	{"QFTPMLSD", at_QFTPMLSD_req},
	{"QFTPMDTM", at_QFTPMDTM_req},
	{"QFTPRENAME", at_QFTPRENAME_req},
	{"QFTPLEN", at_QFTPLEN_req},
	{"QFTPSTAT", at_QFTPSTAT_req},
	{"QFTPCLOSE", at_QFTPCLOSE_req},
#endif
#endif

/********************************************************************************
******************************中移系列基础AT命令*********************************
********************************************************************************/
	/*中移产线命令，不得用VER_ZY圈定，函数内部不得用cmiot文件夹内的API或宏定义*/
	{"MUECONFIG", at_MUECONFIG_req},
	{"MADC", at_MADC_req},
	{"CMVERSION", at_CMVERSION_req},
	{"MREBOOT", at_MREBOOT_req},
	{"MPRODUCTMODE", at_MPRODUCTMODE_req},
#if AT_LED_CMCC
	{"MLED", at_MLED_req}, /*txy*/
#endif

#if VER_CM	
    {"MTSETID",      cm_at_mtsetid_req},
#else
    {"MTSETID",      at_MTSETID_req},		//产线写号	
#endif

#if VER_CM
    {"MDNSCFG", at_MDNSCFG_req}, /*MN316与307X共用*/
#if AT_BASE_CMCC
	/*txy*/
	{"MDIALUPCFG", at_MDIALUPCFG_req},
	{"MDIALUP", at_MDIALUP_req},
	{"MIPCALL", at_MIPCALL_req},

	{"MPOF", at_MPOF_req},
	{"CPOF", at_MPOF_req},
	{"TRB", at_TRB_req},
	{"MLPMCFG", at_MLPMCFG_req},
	{"MEMINFO", at_MEMINFO_req}, /*dgq*/

	{"MGPIO", at_MGPIO_req},		 /*dgq*/
	{"MCHIPINFO", at_MCHIPINFO_req}, /*dgq*/
	{"MPWMDATA", at_MPWMDATA_req},	 /*dgq*/
	{"MPWMCTRL", at_MPWMCTRL_req},	 /*dgq*/

	/*txy/zjt*/
	{"GMI", cm_at_cgmi_req},
	{"CGMI", cm_at_cgmi_req},
	{"CGMM", cm_at_cgmm_req},
	{"GMM", cm_at_cgmm_req},
	{"CGMR", cm_at_cgmr_req},
	{"GMR", cm_at_cgmr_req},
	{"ATI", cm_at_ati_req},
	{"MBSVER", cm_at_mbsver_req},
	{"MSWVER", cm_at_mswver_req},
	{"MHWVER", cm_at_mhwver_req},
	{"MWHWVER", cm_at_mwhwver_req},
	{"MCGSNW", cm_at_mcgsnw_req},

#endif

#if AT_PING_CMCC
	{"MPING", at_MPING_req},
#endif

#if AT_NTP_CMCC
	{"MNTP", at_MNTP_req},
#endif

#if AT_SOCK_CMCC
	{"MIPCFG", at_MIPCFG_req},
	{"MIPTKA", at_MIPTKA_req},
	{"MIPOPEN", at_MIPOPEN_req},
	{"MIPCLOSE", at_MIPCLOSE_req},
	{"MIPSEND", at_MIPSEND_req},
	{"MIPRD", at_MIPRD_req},
	{"MIPMODE", at_MIPMODE_req},
	{"MIPSTATE",   at_MIPSTATE_req},
	{"MIPSACK",    at_MIPSACK_req},
	{"MDNSGIP", at_MDNSGIP_req},
#endif

#if AT_SSL_CMCC
	{"MSSLCFG",             at_MSSLCFG_req},
	{"MSSLCERTWR",          at_MSSLCERTWR_req},
	{"MSSLKEYWR",           at_MSSLKEYWR_req},
	{"MSSLCERTRD",          at_MSSLCERTRD_req},
	{"MSSLLIST",            at_MSSLLIST_req},
	{"MSSLRM",              at_MSSLRM_req},
	{"MSSLCHECK",           at_MSSLCHECK_req},
	{"MSSLCIPHER",          at_MSSLCIPHER_req},
#endif

#if AT_HTTP_CMCC
    {"MHTTPCREATE", at_MHTTPCREATE_req},
    {"MHTTPCFG", at_MHTTPCFG_req},
    {"MHTTPDEL",   at_MHTTPDEL_req},
    {"MHTTPHEADER",   at_MHTTPHEADER_req},
    {"MHTTPCONTENT",   at_MHTTPCONTENT_req},
    {"MHTTPREQUEST", at_MHTTPREQUEST_req},
    {"MHTTPREAD",    at_MHTTPREAD_req},
    {"MHTTPTERM",   at_MHTTPTERM_req},
    {"MHTTPDLFILE",   at_MHTTPDLFILE_req},
#endif

#if AT_MQTT_CMCC  /*tmw/lc*/
	{"MQTTCFG", at_MQTTCFG_req},
	{"MQTTCONN", at_MQTTCONN_req},
	{"MQTTDISC", at_MQTTDISC_req},
	{"MQTTPUB", at_MQTTPUB_req},
	{"MQTTSUB", at_MQTTSUB_req},
	{"MQTTUNSUB", at_MQTTUNSUB_req},
	{"MQTTSTATE", at_MQTTSTATE_req},
	{"MQTTREAD", at_MQTTREAD_req},
        
#endif

#if AT_FOTA_CMCC  /*tmw*/
	{"MFWCFG", at_MFWCFG_req},
	{"MFWDLOAD", at_MFWDLOAD_req},
	{"MFWUPGRADE", at_MFWUPGRADE_req},
	{"MFWERASE", at_MFWERASE_req},
#endif


#if CM_AUTOREGISTER_ENABLE  /*电信自注册*/
	{"MPDSREGSWT", at_cm_mpdsergswt_event_handler},
	{"MPDSREGCFG", at_cm_mpdsegcfg_event_handler},
#endif


#if CM_COT_ENABLE  /*中移云*/
	{"MIPLCREATE", at_proxy_onenet_create_cmd},
	{"MIPLDELETE", at_proxy_onenet_delete_cmd},
	{"MIPLOPEN", at_proxy_onenet_open_cmd},
	{"MIPLADDOBJ", at_proxy_onenet_add_object_cmd},
	{"MIPLDELOBJ", at_proxy_onenet_delete_object_cmd},
	{"MIPLCLOSE", at_proxy_onenet_close_cmd},
	{"MIPLUPDATE", at_proxy_onenet_update_cmd},
	{"MIPLNOTIFY", at_proxy_onenet_notify_cmd},
	{"MIPLDISCOVERRSP", at_proxy_onenet_discover_response_cmd},
	{"MIPLREADRSP", at_proxy_onenet_read_response_cmd},
	{"MIPLWRITERSP", at_proxy_onenet_write_response_cmd},
	{"MIPLEXECUTERSP", at_proxy_onenet_execute_response_cmd},
	{"MIPLOBSERVERSP", at_proxy_onenet_observe_response_cmd},
	{"MIPLPARAMETERRSP", at_proxy_onenet_parameter_response_cmd},
	{"MIPLVER", at_proxy_onenet_version_cmd},
	{"MIPLCREATEEX", at_proxy_onenet_createex_cmd},
	{"MIPLDEVINFO", at_proxy_onenet_devinfo_cmd},
	{"MIPLNMI", at_proxy_onenet_urc_mode_cmd},
	{"MIPLMGR", at_proxy_onenet_urc_read_cmd},
	{"MIPLQMGR", at_proxy_onenet_urc_info_cmd},
	{"MIPLDTLSNAT", at_proxy_onenet_dtlsnat_cmd},
	{"MIPLCFG", at_proxy_onenet_miplcfg_cmd},
	{"MIPLSOTAPARAM", at_proxy_onenet_miplsotaparam_cmd},
	{"MIPLSOTAGET", at_proxy_onenet_miplsotaget_cmd},
	{"MIPLSOTARESULT", at_proxy_onenet_miplsotaresult_cmd},
	{"MIPLFOTAINIT", at_proxy_onenet_miplfotainit_cmd},
	{"MIPLFOTACFG", at_proxy_onenet_miplfotacfg_cmd},
#endif

#if COT_CMDMP  /*中移自注册*/
	{"MDMPCFG", cm_at_mdmpcfg_cmd},
	{"MDMPCFGEX", cm_at_mdmpcfgex_cmd},
#endif

#if AT_FS_CMCC  /**/
	{"MFCFG", at_MFCFG_req},
	{"MFSINFO", at_MFSINFO_req},
	{"MFLIST", at_MFLIST_req},
	{"MFSIZE", at_MFSIZE_req},
	{"MFPUT", at_MFPUT_req},
	{"MFGET", at_MFGET_req},
	{"MFOPEN", at_MFOPEN_req},
	{"MFREAD", at_MFREAD_req},
	{"MFWRITE", at_MFWRITE_req},
	{"MFSYNC", at_MFSYNC_req},
	{"MFSEEK", at_MFSEEK_req},
	{"MFTRUNC", at_MFTRUNC_req},
	{"MFCLOSE", at_MFCLOSE_req},
	{"MFDELETE", at_MFDELETE_req},
	{"MFMOVE", at_MFMOVE_req},
	{"MFCHECK", at_MFCHECK_req},		
#endif

#if AT_FTP_CMCC  /*尚未支持*/
	{"MFTPCFG", cm_at_ftp_cfg_cmd},
	{"MFTPCONN", cm_at_ftp_conn_cmd},
	{"MFTPDISC", cm_at_ftp_disconn_cmd},
	{"MFTPCWD", cm_at_ftp_cwd_cmd},
	{"MFTPPWD", cm_at_ftp_pwd_cmd},
	{"MFTPMKD", cm_at_ftp_mkd_cmd},
	{"MFTPLIST", cm_at_ftp_list_cmd},
	{"MFTPDEL", cm_at_ftp_del_cmd},
	{"MFTPRN", cm_at_ftp_rn_cmd},
	{"MFTPRETR", cm_at_ftp_retr_cmd},
	{"MFTPAPPE", cm_at_ftp_appe_cmd},
	{"MFTPSTOR", cm_at_ftp_stor_cmd},
	{"MFTPSTATE", cm_at_ftp_state_cmd},
#endif


#if CM_TTS_ENABLE  /**/
	{"MTTSCFG", cmiotTTSCFG},
	{"MTTSPLAY", cmiotTTSPLAY},
	{"MTTSSTOP", cmiotTTSSTOP},
#endif

#endif

/********************************************************************************
******************************特殊用户AT命令*********************************
********************************************************************************/

#if VER_MN316
	{"CPOF", at_CPOF_req},
	{"CMVER", at_CMVER_req},
	{"WORKLOCK", at_WORKLOCK_req},
	{"OFFTIME", at_OFFTIME_req},
	{"STANDBY", at_STANDBY_req},
	{"CMDNS", at_CMDNS_req},
	{"CMNTP", at_CMNTP_req},

	{"NATSPEED", at_NATSPEED_req},
	{"GPIO", at_GPIO_req},
	{"CMSYSCTRL", at_CMSYSCTRL_req},
	{"CMADC", at_CMADC_req},
	{"VBAT", at_VBAT_req},
	{"NPINGSTOP", at_QPINGSTOP_req},
	{"NPING", at_NPING_req},

	{"NFWUPD", at_NFWUPD_req}, /*FOTA*/
#if	AT_SOCK_MN316
	{"NSOCR", at_NSOCR_req},
	{"NSOST", at_NSOST_req},
	{"NSOSTF", at_NSOSTF_req},
	{"NSORF", at_NSORF_req},
	{"NSOCO", at_NSOCO_req},
	{"NSOSD", at_NSOSD_req},
	{"NSOCL", at_NSOCL_req},
	{"NSOCFG", at_NSOCFG_req},
	{"NQSOS", at_NQSOS_req},
#endif
#endif

#if USR_CUSTOM11
	{"SEAUTHSTA", at_SEAUTHSTA_req},
	{"SERAND", at_SERAND_req},
	{"SEFORMAT", at_SEFORMART_req},
	{"SEAUTHKEY", at_SEAUTHKEY_req},
	{"SERSTPKEY", at_SERSTPKEY_req},
    {"SEAUTH", at_SEAUTH_req},
#endif
#if USR_CUSTOM7
	{"CFGRI", at_CFGRI_req},
	{"WAKEUPLEN", at_WAKEUPLEN_req},
	{"WAKEUPINTERVAL", at_WAKEUPINTERVAL_req},
	{"HEARTINQUIRE", at_HEARTINQUIRE_req},
#endif
	{0, 0} // can not delete!!!
};
#else
struct at_serv_proc_e at_basic_req[] = {{0, 0}};
#endif /* ATCTL_EN */

at_cmd_t *g_at_basic_req = at_basic_req;


