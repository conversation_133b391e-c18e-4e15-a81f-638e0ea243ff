#pragma once

#include <stdint.h>

#define WAV_TAG(a, b, c, d) (((d) << 24) | ((c) << 16) | ((b) << 8) | (a))

typedef struct _wav_header{
	uint32_t riff;
	uint32_t size;
	uint32_t wave;
	uint32_t fmt;
	uint32_t fmt_size;
	uint16_t format;
	uint16_t channels;
	uint32_t sample_rate;
	uint32_t byte_rate;
	uint16_t block_align;
	uint16_t bits_per_sample;
	uint32_t data;
	uint32_t data_size;
}wav_header_t;

static void set_wav_header(wav_header_t * wav_header, uint32_t sample_rate, uint16_t channels, uint16_t bits_per_sample, uint32_t data_size)
{
    uint32_t fileSize = data_size + 44;

    wav_header->riff = WAV_TAG('R', 'I', 'F', 'F');
    wav_header->size = fileSize;
    wav_header->wave = WAV_TAG('W', 'A', 'V', 'E');
    wav_header->fmt = WAV_TAG('f', 'm', 't', ' ');
    wav_header->fmt_size = 16;
    wav_header->format = 1;
    wav_header->channels = channels;
    wav_header->sample_rate = sample_rate;
    wav_header->byte_rate = (sample_rate * channels * bits_per_sample) / 8;
    wav_header->block_align = (channels * bits_per_sample) / 8;
    wav_header->bits_per_sample = bits_per_sample;
    wav_header->data = WAV_TAG('d', 'a', 't', 'a'); 
    wav_header->data_size = data_size;
}
