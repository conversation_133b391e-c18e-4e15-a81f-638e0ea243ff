get_sub_header_dirs(INCLUDE_CJSON_DIRS ${CMAKE_CURRENT_LIST_DIR})

file(GLOB_RECURSE CJSON_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/cJSON/*.c"
)

shorten_src_file_macro_without_warning(${CJSON_SOURCES})

target_sources(application
    PRIVATE 
        ${CJSON_SOURCES}
)

# 2M版本放PSRAM做压缩
if(SPACE_OPTIMIZATION)
    relocate_code(CODE_LOCATION PSRAM CODE_TARGET application SOURCES_LIST ${CJSON_SOURCES})
endif()
