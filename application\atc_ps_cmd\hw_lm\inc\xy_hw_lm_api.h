﻿#ifndef _XY_HW_LM_API_H_
#define _XY_HW_LM_API_H_

#include "xy_hw_lm_interface.h"

typedef PsStartReq_Stru PsStartReq;

/**
 * @brief            SIM 卡鉴权请求，阻塞接口
 * @param pReq       SIM_AUTH_REQ消息结构体 
 * @param pCnf       SIM_AUTH_CNF消息结构体指针的指针,由调用方释放
 * @return           XY_ERR:异常执行, XY_OK:正常执行
 * @attention        XY_OK仅代表消息正常执行, 请求处理结果，需要从CNF消息中获取
 */
UINT8 xy_lm_SimAuthReq(SimAuthReq* pReq, SimAuthCnf **ppCnf);

/**
 * @brief            设置IMS APN，阻塞接口
 * @param pReq       SET_APN_REQ消息结构体 
 * @param pCnf       SET_APN_CNF消息结构体指针的指针,由调用方释放
 * @return           XY_ERR:异常执行, XY_OK:正常执行
 * @attention        XY_OK仅代表消息正常执行, 请求处理结果，需要从CNF消息中获取
 */
UINT8 xy_lm_SetApnReq(SetApnReq* pReq, SetApnCnf **ppCnf);

/**
 * @brief            PDP激活/去激活请求，阻塞接口
 * @param pReq       ACT_PDN_REQ消息结构体 
 * @param pCnf       ACT_PDN_CNF消息结构体指针的指针,由调用方释放
 * @return           XY_ERR:异常执行, XY_OK:正常执行
 * @attention        XY_OK仅代表消息正常执行, 请求处理结果，需要从CNF消息中获取
 */
UINT8 xy_lm_ActPdnReq(ActPdnReq* pReq, ActPdnCnf **ppCnf);

/**
 * @brief            SIM卡文件读取接口，阻塞接口
 * @param pReq       SIM_READ_REQ消息结构体 
 * @param pCnf       SIM_READ_CNF消息结构体指针的指针,该指针及内部data指针由调用方释放
 * @return           XY_ERR:异常执行, XY_OK:正常执行
 * @attention        XY_OK仅代表消息正常执行, 请求处理结果，需要从CNF消息中获取
 */
UINT8 xy_lm_SimReadReq(SimReadReq* pReq, SimReadCnf **ppCnf);

/**
 * @brief            PS驻网请求，非阻塞接口
 * @param pReq       START_PS_REQ消息结构体 
 * @return           XY_ERR:异常执行, XY_OK:正常执行
 */
UINT8 xy_lm_PsStartReq(PsStartReq* pReq);

/**
 * @brief            获取T3402定时器时长，阻塞接口
 * @param pCnf       GET_EMM_TIMER_LEN_CNF消息结构体指针的指针,由调用方释放
 * @return           XY_ERR:异常执行, XY_OK:正常执行
 * @attention        XY_OK仅代表消息正常执行, 请求处理结果，需要从CNF消息中获取
 */
UINT8 xy_lm_GetEmmTimerLenReq(TIME_LEN_CNF **ppCnf);

/**
 * @brief            紧急注册请求，阻塞接口
 * @param pReq       EMERGENCY_ATTACH_REQ消息结构体 
 * @param pCnf       EMERGENCY_ATTACH_CNF消息结构体指针的指针,由调用方释放
 * @return           XY_ERR:异常执行, XY_OK:正常执行
 * @attention        XY_OK仅代表消息正常执行, 请求处理结果，需要从CNF消息中获取
 */
UINT8 xy_lm_EmcAttachReq(EmcAttachReq* pReq, EmcAttachCnf **ppCnf);

/**
 * @brief            通知 MM 是否忽略 3411 定时器，非阻塞接口
 * @param pReq       M_REG_TIMER_CTL_IND消息结构体
 * @return           XY_ERR:异常执行, XY_OK:正常执行
 * @attention        XY_OK仅代表消息正常执行
 */
UINT8 xy_lm_MMIgnorl3411TimerInd(HimsMmIgnor3411TimerInd* pInd);

/**
 * @brief            AP 配置小区重选抑制的门限值，非阻塞接口
 * @param pReq       CELL_THRESH_ADJ消息结构体 
 * @return           XY_ERR:异常执行, XY_OK:正常执行
 * @attention        XY_OK仅代表消息正常执行
 */
UINT8 xy_lm_CellThreshAdjReq(CellThreshAdjReq* pReq);

/**
 * @brief            AP 配置邻区测量上报 RSRP/RSRQ，非阻塞接口
 * @param pReq       XXX_MEAS_RPT_ADJ消息结构体 
 * @return           XY_ERR:异常执行, XY_OK:正常执行
 * @attention        XY_OK仅代表消息正常执行
 */
UINT8 xy_lm_MeasRptAdjReq(MeasRptAdjReq* pReq);

/**
 * @brief            AP 配置邻区测量启动门限调整量，非阻塞接口
 * @param pReq       XXX_MEAS_TRIGGER_THRESH_ADJ
 * @return           XY_ERR:异常执行, XY_OK:正常执行
 * @attention        XY_OK仅代表消息正常执行
 */
UINT8 xy_lm_MeasTriggerThreshAdjReq(MeasTriggerThreshAdjReq* pReq);

/**
 * @brief            PDCP/RLC 拥塞状态上报门限配置，阻塞接口
 * @param pReq       L2_CONGEST_RT_SET_REQ消息结构体 
 * @param pCnf       L2_CONGEST_RT_SET_CNF消息结构体的指针的指针,由调用方释放
 * @return           XY_ERR:异常执行, XY_OK:正常执行
 * @attention        XY_OK仅代表消息正常执行, 请求处理结果，需要从CNF消息中获取
 */
UINT8 xy_lm_L2CongestReportSetReq(L2CongestReportSetReq* pReq, L2CongestReportSetCnf **ppCnf);

/**
 * @brief            空口误码上报门限配置请求，阻塞接口
 * @param pReq       BER_SET_REQ消息结构体 
 * @param pCnf       BER_SET_CNF消息结构体指针的指针,由调用方释放
 * @return           XY_ERR:异常执行, XY_OK:正常执行
 * @attention        XY_OK仅代表消息正常执行, 请求处理结果，需要从CNF消息中获取
 */
UINT8 xy_lm_BerSetReq(BerSetReq* pReq, BerSetCnf **ppCnf);

/**
 * @brief           主动上报处理函数注册
 * @return          void
 * @attention       需求方初始化时务必调函数注册，否侧主动上报无法获取
 */
void xy_lm_interface_register();

#endif
