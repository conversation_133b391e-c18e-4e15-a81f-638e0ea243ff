/*******************************************************************************
 *							 Include header files							   *
 ******************************************************************************/
#include "xy_at_api.h"
#include "xy_system.h"



int at_QIURC_req(char *at_buf, char **prsp_cmd)
{
	UNUSED_ARG(at_buf);
    UNUSED_ARG(prsp_cmd);
    return AT_END;
}

int at_QIMUX_req(char *at_buf, char **prsp_cmd)
{
	UNUSED_ARG(at_buf);
    UNUSED_ARG(prsp_cmd);
    return AT_END;
}

int at_QIHEAD_req(char *at_buf, char **prsp_cmd)
{
	UNUSED_ARG(at_buf);
    UNUSED_ARG(prsp_cmd);
    return AT_END;
}
//打桩指令
int at_QIREGAPP_req(char *at_buf, char **prsp_cmd)
{
	UNUSED_ARG(at_buf);
    UNUSED_ARG(prsp_cmd);
    return AT_END;
}

int at_QILOCIP_req(char *at_buf, char **prsp_cmd)
{
	UNUSED_ARG(at_buf);
    UNUSED_ARG(prsp_cmd);
    return AT_END;
}
//打桩指令
int at_QCAMCFG_req(char *at_buf, char **prsp_cmd)
{
	UNUSED_ARG(at_buf);
    UNUSED_ARG(prsp_cmd);
    return AT_END;
}

//打桩指令
int at_REQLIC_req(char *at_buf, char **prsp_cmd)
{
	UNUSED_ARG(at_buf);
    UNUSED_ARG(prsp_cmd);
    return AT_END;
}
//打桩指令
int at_LIC_req(char *at_buf, char **prsp_cmd)
{
	UNUSED_ARG(at_buf);
    UNUSED_ARG(prsp_cmd);
    return AT_END;
}
//打桩指令
int at_TYAUTH_req(char *at_buf, char **prsp_cmd)
{
	UNUSED_ARG(at_buf);
    UNUSED_ARG(prsp_cmd);
    return AT_END;
}

