#include "at_response.h"
#include "at_ctl.h"
#include "xy_at_api.h"
#include "otp_proc.h"
#include "pin_ctl.h"
#include "xy4100_ll_uart.h"
#include "at_passthrough.h"
#include "xy_system.h"

typedef struct 
{
    uint8_t atHandle;
    uint8_t source;
    uint8_t respId;
    uint8_t padding;
    uint32_t argLen;
    char arg[];
} appAtRespIpcMsg_t;

typedef struct atAppRespHandleList
{
    uint32_t source;
    appAtRespHandle respHandle;
    struct atAppRespHandleList *next;
} atAppRespHandleList_t;

osThreadId_t gAppAtRespHandle = NULL;
osMessageQueueId_t gAppRespMsgQueue = NULL;
atAppRespHandleList_t* gAppAtRespHandleList = NULL;

/* 判断是否需要执行urc缓存: 在支持urc缓存条件下，若当前at通道有命令正在处理或者该通道正处于透传状态，缓存URC信息 */
static bool checkAtUrcCacheCondition(int atHandle)
{
    if (g_softap_fac_nv->urc_cache == 1 && g_at_tty_ctx[atHandle].urcCache_mux != NULL)
    {
        if ((g_at_tty_ctx[atHandle].state & RCV_REQ_NOW) || AT_STATE_IS_PASSTHROUGH(&g_at_tty_ctx[atHandle].passthInfo))
        {
            return true;
        }
    }
    return false;
}

static void sendAtUrcWithHandleImpl(int atHandle, void *data, size_t size)
{
    if (checkAtUrcCacheCondition(atHandle))
    {
        at_add_urc_cache(atHandle, data, size);
    }
    else
    {
#if PS_TEST_MODE
        if (atHandle == AT_PS_TEST_FD)
        {
            extern void ps_test_recv_at(uint8_t* pURC_data, uint16_t ulDataLen);
            ps_test_recv_at(data, size);
        } else
#endif /* PS_TEST_MODE */
        at_posix_write(atHandle, data, size);
    }
}

static void sendAtURCImpl(void *data, uint32_t dataLen)
{
    if (g_softap_fac_nv->urc_cfg & (1 << AT_LPUART_FD))
    {
        sendAtUrcWithHandleImpl(AT_LPUART_FD, data, dataLen);
#if XY_CMUX
        sendAtUrcWithHandleImpl(AT_CMUX1_FD, data, dataLen);
        sendAtUrcWithHandleImpl(AT_CMUX2_FD, data, dataLen);
#endif   
    }
#if UART_AT
    if (g_softap_fac_nv->urc_cfg & (1 << AT_UART_FD))
    {
        sendAtUrcWithHandleImpl(AT_UART_FD, data, dataLen);
#if XY_CMUX
        sendAtUrcWithHandleImpl(AT_CMUX1_FD, data, dataLen);
        sendAtUrcWithHandleImpl(AT_CMUX2_FD, data, dataLen);
#endif   
    }
#endif /* UART_AT */

    if ((g_softap_fac_nv->urc_cfg & (1 << AT_USB_FD)) && is_usb_at_support())
    {
        sendAtUrcWithHandleImpl(AT_USB_FD, data, dataLen);
    }

    if ((g_softap_fac_nv->urc_cfg & (1 << AT_USB_MODEM_FD)) && is_usb_modem_support())
    {
        sendAtUrcWithHandleImpl(AT_USB_MODEM_FD, data, dataLen);
    }

#if PS_TEST_MODE
    extern void ps_test_recv_at(uint8_t* pURC_data, uint16_t ulDataLen);
    ps_test_recv_at(data, dataLen);
#endif /* PS_TEST_MODE */
}

void Sys_Up_URC()
{
	char up_urc[128] = {0};

    if (!GetFtCalibrationFlag())
    {
        sprintf(up_urc, "\r\n+WARNING:NO FT CALI\r\n");
        sysAtURC(up_urc, strlen(up_urc));
        debug_log_string(up_urc);
		xy_printf(0, PLATFORM_AP, INFO_LOG, "%s", up_urc);
    }

    if (!(Is_WakeUp_From_Dsleep() || Is_WakeUp_From_Sleep()))
    {
#if !VER_CM
        sprintf(up_urc, "\r\nRDY\r\n");
#if USR_CUSTOM10
        sprintf(up_urc, "\r\nAPP RDY\r\n");
#endif
        sysAtURC(up_urc, strlen(up_urc));
#endif
	}
    else if (Is_WakeUp_From_Dsleep())
    {
        xy_printf(0, PLATFORM_AP, WARN_LOG, "PS Wakeup from DEEPSLEEP by state:%d", gCpLpmDebugInfo->sleep_type);
    }

    if (p_SysUp_URC_Hook != NULL)
        p_SysUp_URC_Hook();

    snprintf(up_urc, 128, "\r\n+POWERON:%d,%d\r\n", Get_Boot_Reason(), Get_Boot_Sub_Reason());
    send_debug_str_to_ext(up_urc);
	debug_log_string(up_urc);
    xy_printf(0, PLATFORM_AP, INFO_LOG, "%s", up_urc);

    snprintf(up_urc, 128, "[Version] SoC: XY4100-%s, Hardware: %s, software: %s, vctrl:%d\r\n", SOC_VERTION, g_softap_fac_nv->hardver, get_sdk_version() ,(CORESYS_ADIF->ANA_BBPLL1_CNTL5 >> 4));
    xy_printf(0, PLATFORM_AP, INFO_LOG, "%s", up_urc);
    debug_log_string(up_urc);
}

void at_add_urc_cache(int ttyFd, char* urc, size_t size)
{
    if (g_at_tty_ctx[ttyFd].urcCache_size > AT_URC_CACHE_MAX_NUM)
    {
        xy_printf(0, PLATFORM_AP, WARN_LOG, "[at_add_urc_cache]urc cache num max, drop! tty:%d, size:%d", ttyFd, size);
        return;
    }

    char *temp = xy_malloc2(size);
    if (temp == NULL)
    {
        ATCTRL_NET_LOG("mem not engouh drop! tty:%d,size:%d", ttyFd, size);         
        xy_printf(0, PLATFORM_AP, WARN_LOG, "[at_add_urc_cache]mem not engouh drop! tty:%d, size:%d", ttyFd, size);		
		debug_assert(size < 1000);
        return;
    }
    else
    {
         xy_printf(0, PLATFORM_AP, INFO_LOG, "[at_add_urc_cache]tty:%d, size:%d", ttyFd, size);
    }
    
    xy_assert(urc != NULL);
    osMutexAcquire(g_at_tty_ctx[ttyFd].urcCache_mux, osWaitForever);
    if (g_at_tty_ctx[ttyFd].urcCache_data == NULL)
    {
        g_at_tty_ctx[ttyFd].urcCache_data = xy_malloc(sizeof(at_urc_cache_t));
        g_at_tty_ctx[ttyFd].urcCache_data->urc = temp;
        g_at_tty_ctx[ttyFd].urcCache_data->urc_size = size;
        memcpy(g_at_tty_ctx[ttyFd].urcCache_data->urc, urc, size);
        g_at_tty_ctx[ttyFd].urcCache_data->next = NULL;
        g_at_tty_ctx[ttyFd].urcCache_size++;
        osMutexRelease(g_at_tty_ctx[ttyFd].urcCache_mux);
        return;
    }

    at_urc_cache_t* tmp = g_at_tty_ctx[ttyFd].urcCache_data;
    while (tmp->next != NULL)
    {
        tmp = tmp->next;
    }

    at_urc_cache_t* new = xy_malloc(sizeof(at_urc_cache_t));
    new->urc = temp;
    memcpy(new->urc, urc, size);
    new->urc_size = size;
    new->next = NULL;
    tmp->next = new;
    g_at_tty_ctx[ttyFd].urcCache_size++;
    osMutexRelease(g_at_tty_ctx[ttyFd].urcCache_mux);
}

/*待当前AT请求的应答结果发送完毕后，立即调用该接口将缓存的URC发送出去*/
void at_send_urc_cache(int ttyFd)
{
    if (g_softap_fac_nv->urc_cache == 0 || g_at_tty_ctx[ttyFd].urcCache_mux == NULL)
    {
        return;
    }

	osMutexAcquire(g_at_tty_ctx[ttyFd].urcCache_mux, osWaitForever);
	if (g_at_tty_ctx[ttyFd].urcCache_data == NULL || (g_at_tty_ctx[ttyFd].state & RCV_REQ_NOW))
	{
		osMutexRelease(g_at_tty_ctx[ttyFd].urcCache_mux);
		return;
	}

	at_urc_cache_t* tmp = g_at_tty_ctx[ttyFd].urcCache_data;
    at_urc_cache_t* pre;
    while (tmp != NULL)
    {
        pre = tmp;
        tmp = tmp->next;
        
        if (pre->urc != NULL)
        {
            xy_printf(0,PLATFORM_AP, INFO_LOG, "[at_send_urc_cache]tty:%d, size:%d", ttyFd, pre->urc_size);
			debug_log_print("[at_send_urc_cache]tty:%d, size:%d\n", ttyFd, pre->urc_size); 
            at_posix_write(ttyFd, (char *)pre->urc, pre->urc_size);
            xy_free(pre->urc);
            g_at_tty_ctx[ttyFd].urcCache_size--;
        }
        xy_free(pre);
    }

    g_at_tty_ctx[ttyFd].urcCache_data = NULL;
    g_at_tty_ctx[ttyFd].urcCache_size = 0;
    osMutexRelease(g_at_tty_ctx[ttyFd].urcCache_mux);
    return;
}
/**
 * @brief 应用模块向指定AT通道输出标准URC主动上报字符串，但不得包括结果码
 * @param atHandle [IN] at通道标识,由使用者在AT命令解析函数中调用get_current_ttyFd()接口去获取当前句柄。@see @ref AT_TTY_FD 
 * @param str [IN] 待输出的字符串数据
 * @note URC数据可能会被缓存（AT命令正在处理或者处于透传模式中）
 * @warning 该接口仅用于标准URC主动上报发送,不得用于中间结果的发送，否则会造成外部MCU先收到OK再收到中间结果。
 */
void appAtURC(int atHandle, char *data)
{
	debug_assert(atHandle>=0 && atHandle<AT_FD_MAX);
    
    appAtRawURC(atHandle, data, strlen(data));
}

/**
 * @brief 应用模块向指定AT通道输出URC主动上报码流数据，但不得包括结果码
 * @param atHandle [IN] at通道标识,由使用者在AT命令解析函数中调用get_current_ttyFd()接口去获取当前句柄。@see @ref AT_TTY_FD 
 * @param data [IN] 待输出的字符串数据
 * @param dataLen [IN] 待输出的码流数据长度
 * @note URC码流数据可能会被缓存（AT命令正在处理或者处于透传模式中）
 * @warning URC的明文字符串优先使用appAtURC()
 * @warning 该接口仅用于码流的主动上报发送,不得用于中间结果的发送，否则会造成外部MCU先收到OK再收到中间结果。
 */
void appAtRawURC(int atHandle, char *data, uint32_t dataLen)
{
	debug_assert(atHandle>=0 && atHandle<AT_FD_MAX);
#if !USR_CUSTOM7
    Output_RI_Signal_Respective(atHandle);
#endif
    sendAtUrcWithHandleImpl(atHandle, data, dataLen);
}

/**
 * @brief 应用模块向指定AT通道直接写数据，通常为中间结果，但不得包括结果码。
 * @param atHandle [IN] at通道标识,由使用者在AT命令解析函数中调用get_current_ttyFd()接口去获取当前句柄。@see @ref AT_TTY_FD 
 * @param data [IN] 待输出的码流
 * @param dataLen [IN] 待输出的码流数据长度
 * @note 码流数据不会被缓存, 使用场景：输出中间结果，透传场景下输出特殊字符
 * @attention 仅用于中间结果的发送，不得用于URC或结果码的发送，直接写AT驱动
 */
void appAtWriteImmediately(int atHandle, char *data, uint32_t dataLen)
{
	debug_assert(atHandle>=0 && atHandle<AT_FD_MAX);

    at_posix_write(atHandle, data, dataLen); 
}

/**
 * @brief 应用模块向指定AT通道输出带结果码"OK""ERROR""NO CARRIER"的应答字符串。at_basic_req中注册函数内部严禁调用该API
 * @param atHandle [IN] at通道标识,由使用者在AT命令解析函数中调用get_current_ttyFd()接口去获取当前句柄。@see @ref AT_TTY_FD 
 * @param data [IN] 待输出的字符串数据
 * @note 函数会重置at上下文
 * @attention 用于携带结果码的AT命令发送，不得用于URC和中间结果的发送
 */
void appAtResp(int atHandle, char *data)
{
	debug_assert(atHandle>=0 && atHandle<AT_FD_MAX);

    at_send_to_tty(atHandle, data, strlen(data), true);
}

/**
 * @brief 向所有AT通道广播发送的URC主动上报。通常为BSP基础平台URC和3GPP的URC发送
 * @param data [IN] 待输出的URC数据
 * @param dataLen [IN] 待输出的URC数据长度
 * @note 非AT命令触发的主动上报信息，如3GPP的"+CTZEU"/"+POWERON:"
 */
void sysAtURC(void *data, uint32_t dataLen)
{
#if !USR_CUSTOM7
    Output_RI_Signal();
#endif
    xy_assert(osKernelIsRunningIdle() != osOK);

    sendAtURCImpl(data, dataLen);
}

void cloudAtURC(char *data)
{
    xy_assert(data != NULL);
    uint32_t size = strlen(data);
    void *tmp = data;

    /* 包含结果码断言 */
    debug_assert(Is_Result_AT_str(data) == 0);

    /* 检查是否需要添加"\r\n" */
    if (size < 2 || data[size - 2] != '\r' || data[size - 1] != '\n')
    {
        uint32_t symLen = strlen(AT_CR_LF);
        tmp = xy_malloc(size + symLen * 2 + 1); // 分配足够的内存来添加"\r\n"和终止符
        memcpy(tmp, AT_CR_LF, symLen);
        memcpy((char *)tmp + symLen, data, size);
        memcpy((char *)tmp + symLen + size, AT_CR_LF, symLen);
        *((char *)tmp + symLen + size + symLen) = '\0'; // 添加终止符
        size += symLen * 2;
    }

    sendAtURCImpl(tmp, size);

    if (tmp != data)
    {
        xy_free(tmp);
    }
}

appAtRespHandle appAtGetRespHandleBySource(uint32_t source)
{
    atAppRespHandleList_t* cur = gAppAtRespHandleList;
    while (cur != NULL)
    {
        if (cur->source == source)
        {
            return cur->respHandle;
        }
        else
        {
            cur = cur->next;
        }
    }
    return NULL;
}

int appAtRegRespHandle(uint32_t source, appAtRespHandle handle)
{
    int i = 0;
    atAppRespHandleList_t* cur = NULL;
    if (gAppAtRespHandleList == NULL)
    {
        cur = (atAppRespHandleList_t*)xy_malloc(sizeof(atAppRespHandleList_t));
        cur->source = source;
        cur->respHandle = handle;
        cur->next = NULL;
        gAppAtRespHandleList = cur;
        return XY_OK;
    }

    /* 检测对应source是否已存在 */
    cur = gAppAtRespHandleList;
    while (cur != NULL)
    {
        if (cur->source == source)
        {
            xy_printf(0, PLATFORM_AP, WARN_LOG, "[warning]source:%d has existed", source);
            return XY_ERR;
        }
        else
        {
            cur = cur->next;
        }
    }

    cur = (atAppRespHandleList_t *)xy_malloc(sizeof(atAppRespHandleList_t));
    cur->source = source;
    cur->respHandle = handle;
    cur->next = gAppAtRespHandleList;
    gAppAtRespHandleList = cur;
    return XY_OK;
}

void appAtRespHandleLoop()
{
    void *rcv_msg = NULL;
    appAtRespIpcMsg_t *msg = NULL;

    while (1)
    {
        osMessageQueueGet(gAppRespMsgQueue, &rcv_msg, NULL, osWaitForever);
        xy_assert(rcv_msg != NULL);
        msg = ((appAtRespIpcMsg_t *)rcv_msg);
        appAtRespHandle respHandle = appAtGetRespHandleBySource(msg->source);
        if (respHandle)
        {
            respHandle(msg->atHandle, msg->respId, msg->argLen, msg->arg);
            xy_printf(0, PLATFORM_AP, INFO_LOG, "appAtRespHandleLoop handle source:%d respId:%d done, and send to at:%d", msg->source, msg->respId, msg->atHandle);
        }
        else
        {
            xy_printf(0, PLATFORM_AP, WARN_LOG, "appAtRespHandleLoop source:%d not found resp callback", msg->source);
        }

        xy_free(msg);
    }
}

int appAtRespReport(uint8_t atHandle, uint8_t source, uint8_t respId, uint16_t argLen, void*arg)
{
    appAtRespIpcMsg_t* msg = xy_malloc(sizeof(appAtRespIpcMsg_t) + argLen);
    msg->atHandle = atHandle;
    msg->source = source;
    msg->respId = respId;
    msg->argLen = argLen;
    int ret = osOK;
    if (argLen)
    {
        memcpy(msg->arg, arg, argLen);
    }
    if ((ret = osMessageQueuePut(gAppRespMsgQueue, &msg, 0, osWaitForever)) != osOK)
    {
        xy_printf(0, PLATFORM_AP, WARN_LOG, "appAtRespReport put sockRspQueue error:%d!", ret);
        return XY_ERR;
    }
    
    return XY_OK;
}

void appAtRespInit()
{
    gAppRespMsgQueue = osMessageQueueNew(APP_AT_RESP_MSG_QLEN, sizeof(void *), NULL);
    osThreadAttr_t attr = {0};
    attr.name       = APP_AT_RESP_HANDLE_NAME;
    attr.stack_size = APP_AT_RESP_HANDLE_STACKSIZE;
    attr.priority   = APP_AT_RESP_HANDLE_PRIO;
    gAppAtRespHandle = osThreadNew((osThreadFunc_t)appAtRespHandleLoop, NULL, &attr);
}

application_init(appAtRespInit, APP_STARTUP_PRIORITY_LOW);

