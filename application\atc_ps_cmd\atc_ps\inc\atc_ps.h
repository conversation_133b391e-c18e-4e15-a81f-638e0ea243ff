#ifndef _ATC_PS_H_
#define _ATC_PS_H_

#if ATC_DSP
#include "ps_adapt.h"
#else
#include "xy_utils.h"
#include "xy_log.h"
#endif

#include "atc_ps_def.h"
#include "xy_atc_interface.h"
#include "atc_ps_stru.h"
#include "pack.h"
#include "xy_system.h"
#include "at_context.h"
#include "oss_nv.h"
#include "xy_wan_api.h"
#include "app_basic_config.h"
#include "ps_netif_api.h"

extern softap_fac_nv_t* g_softap_fac_nv;
extern unsigned short   g_WriteSDVolt;

extern const ST_ATC_AP_STR_TABLE              ATC_NConfig_Table[D_ATC_NCONFIG_MAX];
extern const ST_ATC_AP_STR_TABLE              ATC_UeConfig_Table[D_ATC_UECONFIG_MAX];
extern const ST_ATC_AP_STR_TABLE              ATC_Qcfg_Table[D_ATC_QCFG_MAX];
extern const ST_ATC_COMMAND_ANAL_TABLE        ATC_Plus_CommandTable[];
extern const ST_ATC_COMMAND_ANAL_TABLE        ATC_Star_CommandTable[];
extern const ST_ATC_COMMAND_ANAL_TABLE        ATC_Symbol_CommandTable[];
extern const ST_ATC_COMMAND_ANAL_TABLE        ATC_Single_CommandTable[];
extern const ST_ATC_AP_STR_EVENT_TABLE        ATC_CommandTestToPS_EventTable[];
#if (!AT_TEST_OFF)
extern const ST_ATC_COMMAND_TEST_RSP_TABLE    ATC_CommandTestRspTable[];
#endif
extern const ST_ATC_FAC_TABLE                 ATC_Fac_Table[D_ATC_FAC_NUM];
extern const ST_ATC_GLOBLE_ERROR_CAUSE_TABLE  PCmeErrorTextTbl[];
#if USR_CUSTOM2
extern const ST_ATC_STR_TABLE                 ATC_NUESTATS_Table[ATC_NUESTATS_MAX];
#endif
extern const ST_ATC_STR_TABLE                 ATC_MUESTATS_Table[6];
extern const ST_ATC_STR_TABLE                 ATC_PdpType_Table[9];
extern const ST_ATC_STR_TABLE                 ATC_CharSet_Table[4];
extern const unsigned int                     ATC_AP_PsRegEventIdMapTable[D_ATC_USER_REG_EVENT_TBL_SIZE][2];
#if VER_CM
extern const ST_ATC_AP_PLMN_NAME_TABLE        ATC_OperatonName_Table_ZY[12];
#else
extern const ST_ATC_AP_OPERTION_NAME_TABLE    ATC_OperatonName_Table[10];
#endif
extern const ST_ATC_AP_PLMN_NAME_TABLE        ATC_PlmnName_Table[13];

extern const ST_ATC_GLOBLE_ERROR_CAUSE_TABLE  PEmmCauseTextTbl[D_ATC_EMM_CAUSE_TBL_SIZE];
extern const ST_ATC_GLOBLE_ERROR_CAUSE_TABLE  PEsmCauseTextTbl[D_ATC_ESM_CAUSE_TBL_SIZE];
extern const unsigned char                    ATC_PinCodeTbl[3][8];

extern const ST_ATC_AP_DATA_IND_PROC_TABLE    AtcAp_DataIndProcTable[];

extern ST_ATC_AP_INFO                         g_AtcApInfo;
extern unsigned char                          g_SmsFormatMode;

extern volatile unsigned int*                 g_pRegEventId;
extern osMutexId_t                            g_pRegEventId_Mutex;

#define CheckUpperAlpha(c) ((c) >= 'A' && (c) <= 'Z')

#define CheckLowerAlpha(c) ((c) >= 'a' && (c) <= 'z')

#define Atc_CheckAlpha(c) (CheckUpperAlpha(c) || (CheckLowerAlpha(c)))

#define ATC_CMD_PREFIX               20

#define D_ATC_CME_ERROR_TBL_SIZE               92

#define AtcAp_Zalloc(ulSize) ({         \
    void *pMem;                         \
    pMem = xy_malloc(ulSize);           \
    memset(pMem, 0, ulSize);            \
    pMem;                               \
})

#if ATC_DSP
#define  AtcAp_Malloc(ulSize)                                  PsMalloc(ulSize,OSADP_MEM_CLEAN) 
#define  AtcAp_MallocWithClean(ulSize)                         PsMalloc(ulSize,OSADP_MEM_CLEAN)
#define  AtcAp_Free(pMem)                                      PsFree(pMem)
#define  AtcAp_MemCpy(pDest,pSrcr,ulrLen)                      OSAdpMemCpy(pDest, pSrcr, ulrLen)
#define  AtcAp_MemSet(pBuffer,ucData,ulBufferLen)              OSAdpMemSet(pBuffer,ucData,ulBufferLen)
#else
#define  AtcAp_MallocWithClean(ulSize)                         AtcAp_Zalloc(ulSize)
#define  AtcAp_Malloc(ulSize)                                  AtcAp_Zalloc(ulSize)
#define  AtcAp_Free(pMem)                                      { xy_free(pMem); pMem=NULL; }
#define  AtcAp_MemCpy(pDest,pSrcr,ulrLen)                      memcpy(pDest,pSrcr,ulrLen)
#define  AtcAp_MemSet(pBuffer,ucData,ulBufferLen)              memset(pBuffer,ucData,ulBufferLen)
#endif

#if ATC_DSP
#define AtcAp_PrintLog(dyn_id, src_id, lev, fmt, ...)          PrintLog(dyn_id, src_id,lev, fmt,##__VA_ARGS__)
#else
#define AtcAp_PrintLog(dyn_id, src_id, lev, fmt, ...)          xy_printf(dyn_id, ATC_AP_T,lev,fmt, ##__VA_ARGS__)
#define PS_TRACE
#define Unused_para(param)
#endif

/***************** atc_ap_main.c **********************************************/
extern void AtcAp_LinkList_AddNode(ST_ATC_AP_LINK_NODE** ppHead, ST_ATC_AP_LINK_NODE** ppLast, ST_ATC_AP_LINK_NODE* pNode);
extern void AtcAp_LinkList_RemoveNode(ST_ATC_AP_LINK_NODE** ppHead, ST_ATC_AP_LINK_NODE** ppLast, ST_ATC_AP_LINK_NODE* pNode);
extern void AtcAp_DelAppInterfaceResult(ST_ATC_AP_APP_INTERFACE_NODE* pNode);
extern void AtcAp_registerPSEventCallback(unsigned long eventId, xy_psEventCallback_t callback);
//extern unsigned char AtcAp_Delete_registerPSEventCallback(unsigned long eventId, xy_psEventCallback_t callback);
extern ST_ATC_AP_APP_INTERFACE_NODE* AtcAp_AddAppInterfaceInfo(unsigned long ulSemaphoreId, unsigned char ucAtCmdFlg);
extern ST_ATC_AP_APP_INTERFACE_NODE* AtcAp_GetAppInterfaceInfo_BySeqNum(unsigned char ucSeqNum);
extern ST_ATC_AP_APP_INTERFACE_NODE* AtcAp_GetAppInterfaceInfo_BySema(unsigned long ulSemaphore);
extern ST_ATC_AP_PS_EVENT_REGISTER_INFO* AtcAp_GetPsRegEventInfo(unsigned long eventId);
extern void AtcAp_AppInterfaceInfo_CmdRstProc(unsigned char ucSeqNum, unsigned ucResult);
extern void AtcAp_SendMsg2AtcAp(void* pMsg, ST_ATC_AP_MSG_INFO* pMsgInfo);
/*************** atc_ap_msg_proc.c  start *************************************/
extern unsigned char AtcAp_NormalMsgDistribute(unsigned char *pucRcvMsg);
extern void AtcAp_CascadeAtProc_NextAt();
extern void AtcAp_AtcDataReqListProc();
extern void  AtcAp_DataIndProc_RegEventInd(unsigned char* pRecvMsg, unsigned short usLen);
/*************** atc_ap_msg_proc.c  end *************************************/


/*************** atc_ap_cmd_proc.c start *************************************/
extern void AtcAp_MsgProc_AT_CMD_RST(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGSN_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CREG_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CEREG_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGATT_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CIMI_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGDCONT_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CFUN_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CESQ_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CSQ_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGPADDR_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGPADDR_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGACT_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CRTDCP_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_SIMST_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CEDRXS_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CPSMS_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGAPNRC_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGAPNRC_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CSCON_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NL2THP_R_Cnf(unsigned char* pRecvMsg);
#if USR_CUSTOM2
extern void AtcAp_MsgProc_NUESTATS_Cnf(unsigned char* pRecvMsg);
#endif
extern void AtcAp_MsgProc_NEARFCN_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NBAND_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NBAND_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NCONFIG_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NCONFIG_T_Cnf(unsigned char *pRecvMsg);
extern void AtcAp_MsgProc_NSET_R_Cnf(unsigned char* pRecvMsg);
#ifdef LCS_MOLR_ENABLE
extern void AtcAp_MsgProc_CMOLR_R_Cnf(unsigned char* pRecvMsg);
#endif
extern void AtcAp_MsgProc_CGDSCONT_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGDSCONT_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGSCONTRDP_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGSCONTRDP_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGTFT_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGTFTRDP_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGTFTRDP_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGEQOS_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGCMOD_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_COPS_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_COPS_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGEREP_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CCIOTOPT_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CEDRXRDP_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGEQOSRDP_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGEQOSRDP_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CTZR_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGCONTRDP_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGCONTRDP_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CEER_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CIPCA_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGAUTH_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NPOWERCLASS_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NPOWERCLASS_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NPTWEDRXS_R_Cnf(unsigned char* pRecvMsg);
#if !defined(_FLASH_OPTIMIZE_) || (USR_CUSTOM2)
extern void AtcAp_MsgProc_NCIDSTATUS_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NCIDSTATUS_R_Cnf(unsigned char* pRecvMsg);
#endif
extern void AtcAp_MsgProc_NGACTR_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NPOPB_R_Cnf(unsigned char* pRecvMsg);
//extern void AtcAp_MsgProc_NIPINFO_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NQPODCP_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NQPNPD_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CNEC_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NRNPDM_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NCPCDPR_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CEID_Cnf(unsigned char* pRecvMsg);
#ifdef LTE_SMS_FEATURE
extern void AtcAp_MsgProc_CSMS_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CSMS_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CMGC_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CMGW_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CMTI_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CMT_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CDS_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CDSI_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CMGR_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CMGL_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CMGF_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CMGF_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CPMS_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CPMS_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CPMS_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CSCA_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CSMP_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CNMI_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CMMS_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CMGS_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CMSS_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_SMS_PDU_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CSCB_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CSDH_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QCMGS_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QCMGR_Cnf(unsigned char* pRecvMsg);

#endif
extern void AtcAp_MsgProc_SIMST_Ind(unsigned char* pRecvMsg);
//extern void AtcAp_MsgProc_NPIN_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CPIN_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CLCK_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_PINSTATUS_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CPINR_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CRTDCP_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGAPNRC_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGEV_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CREG_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGREG_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CEREG_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CSCON_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NPTWEDRXP_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CEDRXP_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CCIOTOPTI_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_L2_THP_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NCCID_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_XYIPDNS_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_MALLOC_ADDR_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_IPSN_Ind(unsigned char* pRecvMsg);
#ifdef LCS_MOLR_ENABLE
extern void AtcAp_MsgProc_CMOLRE_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CMOLRG_Ind(unsigned char* pRecvMsg);
#endif
extern void AtcAp_MsgProc_PDNIPADDR_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NGACTR_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CSIM_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGLA_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CCHO_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CCHC_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CRSM_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_LOCALTIMEINFO_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NoCarrier_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CELLSRCH_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_PSINFO_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CSODCPR_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NSNPDR_Ind(unsigned char* pRecvMsg);
//extern void AtcAp_MsgProc_NIPINFO_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CNEC_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NRNPDM_Ind(unsigned char* pRecvMsg, unsigned char ucNrnpdmRepValue, unsigned char ucAtCid,unsigned short usLen,unsigned char* pucReportData);
extern void AtcAp_MsgProc_MNBIOTEVENT_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NPLMNS_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NLOCKF_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NCSG_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NCSG_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CACDC_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QICSGP_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QIACT_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QGSN_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_GSN_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QCCID_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGREG_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QSPN_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QENG_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QSIMDET_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QSIMSTAT_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QSIMSTAT_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QNWINFO_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QCSQ_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QCSQ_E_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QGDCNT_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QAUGDCNT_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CPOL_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CPOL_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QINISTAT_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CSCS_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CGSMS_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QCELL_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QPINC_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_MLOCKFREQ_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_MLOCKFREQ_T_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_MUESTATS_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_MEMMTIMER_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_MEMMTIMER_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_MUECONFIG_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_MWIFISCANCFG_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_MWIFISCANQUERY_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CNUM_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QIURC_PdpDeact_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QBLACKCELL_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QBLACKCELLCFG_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QSIMSWITCH_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_PSTEST_Info_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_PCTTESTINFO_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CIEV_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NPREEARFCN_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QSCLKEX_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_NWDRX_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_SIMUUICC_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_SimDataDownload_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QWIFISCAN_IND(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QWIFISCAN_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CCED_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CEMODE_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QIACTEX_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QIACTEX_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QIDEACTEX_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_ICCID_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_BAND_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_BANDIND_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QINDCFG_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QCELLINFO_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_MIPCALL_Ind(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QDSIM_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QCFG_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_CUSD_R_Cnf(unsigned char* pRecvMsg);
extern void AtcAp_MsgProc_QEHPLMN_R_Cnf(unsigned char* pRecvMsg);
#if USR_CUSTOM12
extern void AtcAp_MsgProc_CCID_Cnf(unsigned char* pRecvMsg);
#endif
extern void AtcAp_MsgProc_ECSIMCFG_R_Cnf(unsigned char* pRecvMsg);
#ifdef SIB16_FEATURE
extern void AtcAp_MsgProc_QUTCTIME_Ind(unsigned char* pRecvMsg);
#endif
/*************** atc_ap_cmd_proc.c  end *************************************/


/*****************************************************************/
/************************** Atc_analysis *************************/
/*****************************************************************/
extern  unsigned char ATC_Command_Analysis(unsigned char* pAtcDataReq);

/*****************************************************************/
/************************** Atc_cmd_basic ************************/
/*****************************************************************/
//extern  unsigned char ATC_CGMI_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern  unsigned char ATC_CGMR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGSN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CREG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CEREG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGATT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CIMI_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGDCONT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CFUN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CMEE_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CLAC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CESQ_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CSQ_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGPADDR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern  unsigned char ATC_CGMM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern  unsigned char ATC_CGDATA_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CSODCP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CRTDCP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGAPNRC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern  unsigned char ATC_CRC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern  unsigned char ATC_CMUX_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern  unsigned char ATC_S3_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern  unsigned char ATC_S4_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern  unsigned char ATC_S5_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern  unsigned char ATC_E_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern  unsigned char ATC_V_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_F_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_W_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_Z_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_BANDIND_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_BAND_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_ICCID_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CEDRXS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CPSMS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CSCON_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CCIOTOPT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CEDRXRDP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGEQOSRDP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CTZR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGCONTRDP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_NL2THP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_NSET_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
#ifdef LPP_MODE_ENABLE
extern  unsigned char ATC_CMOLR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
#endif

/*****************************************************************/
/************************** Atc_cmd_other ************************/
/*****************************************************************/
extern  unsigned char ATC_CGDSCONT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGSCONTRDP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGACT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGTFT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGTFTRDP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGEQOS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGCMOD_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGEREP_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CPWD_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CPIN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CLCK_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);

#ifdef LTE_SMS_FEATURE
/*****************************************************************/
/************************** Atc_cmd_sms **************************/
/*****************************************************************/
extern unsigned char ATC_CGSMS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CSMS_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CMGF_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CPMS_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CSCA_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CSMP_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CNMI_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CMGW_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CMGR_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CMGD_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CMGL_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CMGS_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CMSS_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CMGC_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CNMA_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CMMS_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CSCB_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CSDH_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QCMGS_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QCMGR_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
#endif
extern unsigned char ATC_COPS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);

//extern unsigned char ATC_NRB_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
#if USR_CUSTOM2
extern unsigned char ATC_NUESTATS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
#endif
extern unsigned char ATC_NEARFCN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NBAND_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NCONFIG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NCCID_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern unsigned char ATC_NPSMR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern unsigned char ATC_NMGS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern unsigned char ATC_NMGR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
///extern unsigned char ATC_NQMGS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern unsigned char ATC_NQMGR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern unsigned char ATC_QLWUDATAEX_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern unsigned char ATC_NCDP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NCSEARFCN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_RAI_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_NFPLMN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);

//extern unsigned char ATC_NATSPEED_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern unsigned char ATC_NSOCR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern unsigned char ATC_NSOST_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern unsigned char ATC_NPING_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//shao add for USAT
/*****************************************************************/
/************************** Atc_cmd_usat**************************/
/*****************************************************************/
extern  unsigned char ATC_CSIM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CCHO_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CCHC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CGLA_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern  unsigned char ATC_CRSM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CEER_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CIPCA_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CGAUTH_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CNMPSD_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CPINR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NPOWERCLASS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NPTWEDRXS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
//extern unsigned char ATC_NPIN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NTSETID_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
#if !defined(_FLASH_OPTIMIZE_) || (USR_CUSTOM2)
extern unsigned char ATC_NCIDSTATUS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
#endif
extern unsigned char ATC_NGACTR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NPOPB_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NIPINFO_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NQPODCP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NSNPD_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NQPNPD_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CNEC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NRNPDM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NCPCDPR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CEID_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_MNBIOTEVENT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CGPIAF_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NLOCKF_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NCSG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CACDC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_PINGXY_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_PSTEST_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QICSGP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QIACT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QIACTEX_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QIDEACT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QIDEACTEX_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QGSN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QCCID_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CGREG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QSPN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QENG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_GSN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QINISTAT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QSIMDET_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QSIMSTAT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CPOL_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_COPN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QNWINFO_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QCSQ_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QGDCNT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QAUGDCNT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CSCS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NITZ_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CTZU_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CGMR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_GMR_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QCELL_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QCELLEX_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QCFG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QPINC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_MLOCKFREQ_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_MCSEARFCN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_MUESTATS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_MPSRAT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_MPTWEDRXS_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_MEMMTIMER_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_MUECONFIG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_MWIFISCANCFG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_MWIFISCANSTART_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_MWIFISCANSTOP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_MWIFISCANQUERY_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QWIFISCAN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CNUM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_ATI_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_GMI_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CGMI_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_GMM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CGMM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_PSTESTMODE_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QBLACKCELL_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QBLACKCELLCFG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QSIMSWITCH_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_PCTTESTINFO_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NPREEARFCN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QSCLKEX_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_NWDRX_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
#ifdef SIMULATOR_UICC
extern unsigned char ATC_SIMUUICC_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
#endif
extern unsigned char ATC_NWSDVOLT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_SIMST_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CCED_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CEMODE_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QINDCFG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QCELLINFO_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CIDACT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QDSIM_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CUSD_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QEHPLMN_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
#if USR_CUSTOM12
extern unsigned char ATC_CCID_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QMUXCFG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QIFGCNT_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QPDPTIMER_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QPPPTIMER_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_CGATTEX_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_QIREGAPP_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern unsigned char ATC_A_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
#endif
extern unsigned char ATC_ECSIMCFG_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
#ifdef SIB16_FEATURE
extern unsigned char ATC_QUTCTIME_LTE_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
#endif
/*****************************atc_cmd_test_cmd  ******************************************/
extern void ATC_CGCONTRDP_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_CGPADDR_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_COPS_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_CPOL_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_CGAPNRC_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_CGTFTRDP_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_CGCMOD_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_CGEQOSRDP_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_CGDSCONT_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_CGSCONTRDP_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_CACDC_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_CPMS_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_MBAND_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_NCONFIG_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_NPOWERCLASS_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_QCSG_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_MLOCKFREQ_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_MUECONFIG_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
extern void ATC_QCFG_LTE_Test_Command(unsigned char *pCommandBuffer, unsigned char *pEventBuffer);
/*************************** atc_ap_com.c start ************************************/
extern void ATC_SendApDataReq(unsigned char ucReqType, unsigned char ucExternalFlg, unsigned long ulAppSemaId, unsigned short usDataLen, unsigned char* pucData, unsigned char ucAtChannelId);
extern void AtcAp_SendOkRsp();
extern unsigned char api_GetCmeeValue();
extern void AtcAp_SendCmeeErr(unsigned short usErrCode);
extern void AtcAp_SendErrorRsp();
extern void AtcAp_SendCmsErr(unsigned short usErrCode);
extern void AtcAp_AtCacheLogHandle(unsigned char* pbuffer ,unsigned short usAtLogLen, unsigned short usEvent);
extern void AtcAp_SendDataInd(unsigned char *pEventMsg);
extern void AtcAp_WriteStrPara_M(unsigned int uiFlg, unsigned char *pucPara );
extern void AtcAp_WriteIntPara_M(unsigned int uiFlg, unsigned int uiPara, unsigned char *pucAtcRspBuf);
extern void AtcAp_WriteIntPara(unsigned int uiFlg, unsigned int uiPara );
extern void AtcAp_WriteHexPara_M(unsigned int uiFlg, unsigned int uiPara, unsigned char* pRespBuff, unsigned char ucHexLen);
extern void AtcAp_Write4BitData(unsigned char ucData);
extern void AtcAp_WriteStrPara(unsigned          int uiFlg, unsigned char *pucPara );
extern void AtcAp_OutputAddr(unsigned char ucDataLen, unsigned char *pData, unsigned char *pucAtcRspBuf);
extern void AtcAp_FreeEventBuffer(unsigned char* pCmdEvent);
extern int AtcAp_SndDataReqToPs(unsigned char ucAtCmdFlg, unsigned char *pCodeStream, unsigned short usCodeStreamLen);
extern int AtcAp_SndDataReqToShm(unsigned char ucAtCmdFlg, unsigned char *pCodeStream, unsigned short usCodeStreamLen);
extern void AtcAp_Encode_UN_ATC_CMD_EVENT(UN_ATC_CMD_EVENT *pCmdEvent, unsigned char** ppCodeStream, unsigned short* pusLen);
extern unsigned short AtcAp_StrPrintf_AtcRspBuf(const char* FormatBuffer,...);
extern unsigned char AtcAp_CurrEventChk_IsWaitSmsPdu();
extern int AtcAp_Strncmp(unsigned char *pStr1, unsigned char *pStr2);
extern unsigned short AtcAp_StrPrintf(unsigned char *EditBuffer, const unsigned char *FormatBuffer,...);
extern void AtcAp_CGCONTRDP_Print(EPS_CGCONTRDP_DYNAMIC_INFO *ptPdpDynamicInfo, unsigned char bPdpType, unsigned char *pucAtcRspBuf);
extern void AtcAp_CSCA_ConvertScaByte2Str(unsigned char* pucScaData, unsigned char ucScaLen, unsigned char* pScaStr);
extern void AtcAp_ConvertByte2BitStr(unsigned char ucVal, unsigned char len, unsigned char* pBitStr);
extern void AtcAp_OutputAddr_IpDns(unsigned char ucV4V6Fg, unsigned char* pAddr_v4, unsigned char* pAddr_v6);

extern  unsigned char ATC_CharToBinary(unsigned char *pInputCharStr , unsigned char ucLength , unsigned char *pBinaryData, unsigned char ucSignedFlg);
extern  void ATC_Strncpy(unsigned char *pStr1, unsigned char *pStr2, unsigned short usCount);
extern  unsigned char ATC_ShortToBinary(unsigned char *pInputCharStr, unsigned char ucLength, unsigned short *pBinaryData);
extern  unsigned char ATC_UshortToBinary(unsigned char *pInputCharStr, unsigned char ucLength, unsigned short *pBinaryData);
extern void AtcAp_SendLongDataInd(unsigned char *pEventMsg, unsigned char **pBuffer, unsigned short usMaxLen);
extern unsigned short AtcAp_StrPrintf_AtcRspBuf(const char* FormatBuffer, ...);
extern const char* ATC_ConvertErrCode2Str(const ST_ATC_GLOBLE_ERROR_CAUSE_TABLE* pErrTab, unsigned char size, unsigned char ucErrCode);
extern  unsigned char ATC_PassWordStrCheck(unsigned char *pInputStr, unsigned char *pEventBuffer);
extern unsigned char ATC_ConvertIpAddrAndSubnetMask(unsigned char *pSrc, unsigned char ucLen, unsigned char aucDesc[], unsigned char* pucDescLen);
extern unsigned char ATC_ConvertPortRangeValue(unsigned char *pSrc, unsigned char ucLen, unsigned short ausDesc[], unsigned char* pucDescLen);
extern void AtcAp_TpduToScts(char* pTpdu, char* pScts);
extern unsigned char ATC_GET_IPV6ADDR_ALL(unsigned char ucCid, unsigned char* pucIpv6Addr);
extern void AtcAp_ConvertPlmn2NameStr(unsigned long ulPlmn, unsigned char ucFormat, unsigned char *pucPlmnName);
extern char* AtcAp_GetPlmnStrByName(unsigned char ucLongNameFlg, unsigned char *pucPlmnName);
extern unsigned char AtcAp_ConvertOperStr2Hex(unsigned char *pucOperStr, unsigned char* pucOper, unsigned char ucSize);
extern unsigned char ATC_ConvertDotSeptNumParams2Short(unsigned char *pSrc, unsigned char ucLen, unsigned char ucParamNum, unsigned short ausDesc[], unsigned char* pucDescLen);
extern unsigned char ATC_ConvertDotSeptNumParams2Char(unsigned char *pSrc, unsigned char ucLen, unsigned char ucParamNum, unsigned char ausDesc[], unsigned char* pucDescLen);
extern void ATC_CmdHeaderWithSpaceProc(char** pBuff, unsigned short* pusLen, unsigned short usMaxLen,unsigned char ucSmsDataFlg);
extern unsigned char ATC_NCONFIG_SET_IsStrChk(unsigned char ucType);
//shao add for USAT
extern void AtcAp_HexToAsc(unsigned short usLength,unsigned char *pOutData,unsigned char *pInputData);
extern void AtcAp_IntegerToPlmn(unsigned long ulInputData, unsigned char *pOutputData);
extern void AtcAp_ConvertTimeZone(unsigned char *pTimeZoneTime,unsigned char ucDayLightTime);
extern unsigned char AtcAp_RevertHexToDecimal(unsigned char ucHex);
extern void AtcAp_OutputTimeZone(unsigned char ucCtzrReport, LTE_NAS_LOCAL_TIME_STRU* pLocalTime);
extern void AtcAp_OutputTimeZone_XY(unsigned char ucCtzrReport, LTE_NAS_LOCAL_TIME_STRU* pLocalTime);
extern void AtcAp_ConvertInDotFormat(char* pStrBuff, unsigned char* pAddr, unsigned char ucLen);
extern void AtcAp_OutputLocalTime(LTE_NAS_LOCAL_TIME_STRU* pLocalTime);
extern void AtcAp_OutputUniversalTime(LTE_NAS_LOCAL_TIME_STRU* pLocalTime);
extern void AtcAp_OutputAddr(unsigned char ucDataLen, unsigned char *pData, unsigned char *pucAtcRspBuf);
extern void AtcAp_OutputAddr_IPv6(unsigned char ucDataLen, unsigned char *pData, unsigned char *pucAtcRspBuf);
extern void AtcAp_OutputAddr_IPv6ColonFormat(unsigned char *pData, unsigned char *pucAtcRspBuf);
extern void AtcAp_OutputPortRange(unsigned char ucDataLen, unsigned short *pData, unsigned char* pRespBuff);
extern void AtcAp_Build_NCell(API_CELL_LIST_STRU* pCellList);
extern unsigned short api_GetWriteSDVolt();
extern unsigned char ATC_HexToBinary(unsigned char *pInputCharStr, unsigned char ucLength, unsigned int *pBinaryData);
extern unsigned char Atc_HexStrToHexDigit_LongStr(unsigned char* src, int src_len, unsigned char* dst);
extern void AtcAp_SmsPdu_Analysis(ATC_AP_MSG_DATA_REQ_STRU* pAtcDataReq);
extern void AtcAp_SmsText_Analysis(ATC_AP_MSG_DATA_REQ_STRU* pAtcDataReq);
#ifdef LTE_SMS_FEATURE
extern void AtcAp_SmsQcmgsText_Analysis(ATC_AP_MSG_DATA_REQ_STRU* pDataReq, ST_ATC_QCMGS_PARAMETER* pEventParam);
#endif
#ifdef LCS_MOLR_ENABLE
extern unsigned short Lcs_MolrResult_OutputXML(unsigned char* pXmlData, unsigned short* pSize, LCS_SHAPE_DATA_STRU* pShapeData, LCS_VELOCITY_DATA_STRU* pVelData);
#endif
extern void xy_get_cid_state_info(unsigned char ucCid, ST_ATC_AP_CID_INFO* stCidInfo);
extern void xy_set_cid_state_mipcallflg( unsigned char ucCid, unsigned char ucMipcallFlg );
extern void xy_set_cid_state_info( unsigned char ucCid, unsigned char ucstate,unsigned char ucType );
extern void xy_clean_cid_state_info(  );
extern void urc_cid_state_handle(unsigned long eventId, void *param, int paramLen);

extern int AtcAp_SendCmdEventToPs();
extern unsigned char AtcAp_FindNextComma(unsigned char* pStrCmd);
extern unsigned char AtcAp_ParseQEHPLMNData(unsigned char* pucData, ST_ATC_QEHPLMN_PARAMETER* pParam);

extern void task_creat_to_kill_reg(uint8_t cid,uint32_t eventId);
extern void at_MIPCALL_report_urc(uint32_t eventId, uint8_t cid);
/*******************************************************************************
 *                             Type definitions                                *
 ******************************************************************************/
#define D_ATC_CMD_PREFIX_MAX_LEN       20

#define D_ATC_DIFF_VALUE ('a'-'A')

//指示AT后面的标识符，通常为+
#define D_ATC_CMD_SYMBOL_CHECK(a)      (a == '+' || a == '^' || a == '&' || a == '#' || a == '*')
 
#define D_ATC_PARAM_DIGIT_CHECK(c) ((c) >= '0' && (c) <= '9')

#define D_ATC_PARAM_HEX_LOW_CHECK(c) ((c) >= 'a' && (c) <= 'f')

#define D_ATC_PARAM_HEX_UP_CHECK(c) ((c) >= 'A' && (c) <= 'F')

#define D_ATC_PARAM_HEX_CHECK(c) (D_ATC_PARAM_DIGIT_CHECK(c) || D_ATC_PARAM_HEX_UP_CHECK(c) || D_ATC_PARAM_HEX_LOW_CHECK(c))

#define D_ATC_PARAM_UPPER_CHECK(c) ((c) >= 'A' && (c) <= 'Z')

#define D_ATC_PARAM_LOWER_CHECK(c) ((c) >= 'a' && (c) <= 'z')

#define D_ATC_PARAM_ALPHA_CHECK(c) (D_ATC_PARAM_UPPER_CHECK(c) || (D_ATC_PARAM_LOWER_CHECK(c)))

typedef enum 
{
    D_ATC_PARAM_PARSE_DEFAULT = 0,
    D_ATC_PARAM_PARSE_ESC,
    D_ATC_PARAM_PARSE_ZCOPY,
} D_ATC_PARAM_PARSE_FLAG_E;

/**
* @brief AT动作的种类，用全局g_req_type来记录
*/
typedef enum 
{
    D_ATC_CMD_INVALID = 0,
    D_ATC_CMD_REQ,     //AT+xxx=param
    D_ATC_CMD_ACTIVE,    //AT+xxx,not include param
    D_ATC_CMD_QUERY,    //AT+XXX?
    D_ATC_CMD_TEST,    //AT+XXX=?
    D_ATC_CMD_SYMBOL_REQ,
    D_ATC_CMD_SINGLE_REQ,
    D_ATC_CMD_AT_REQ,
} D_ATC_CMD_REQ_TYPE_E;

typedef enum 
{
    D_ATC_CMD_PLUS = 0,
    D_ATC_CMD_STAR,
    D_ATC_CMD_AND,
    D_ATC_CMD_NULL,
} D_ATC_CMD_SYMBOL;

typedef enum
{
    D_ATC_PARAM_FMT_INVALID = 0,//非法解析，%l、%h、%s的个数不合理
    D_ATC_PARAM_FMT_NORMAL,        //不含%l格式
    D_ATC_PARAM_FMT_POINT,        //解析的数据为明文字符串，解析格式为%s,需判断input_len == data_len
    D_ATC_PARAM_FMT_HEX,        //解析的数据为16进制hex码，解析格式为%h,需判断input_len * 2 == data_len
}D_ATC_PARAM_FMT_TYPE_E;

typedef struct
{
    char ch;          //转义字符 如\a 中的a
    char esc_ch;      //转义字符对应的ASCII码
} ST_ATC_ASCII_ESCAPE;

/* 该结构体仅仅是为了解析字符串类型参数后，进行长度合法性检错而设计的，没有功能上的价值 */
typedef struct
{
    int input_len;            //AT输入的数据长度参数解析后的赋值，即%l所对应的那个参数值；
    int data_len;            //AT命令中字符串参数最终的有效内容长度，即对应%s或%h解析后字符串有效长度值
    uint8_t point_flag;        //用于判断解析的参数类型是否为%p
    uint8_t data_type;    //用于识别%l的捆绑关系 参考@D_ATC_PARAM_FMT_TYPE_E
}ST_ATC_CMD_DATA_PARSE;

/* 整形变量取值范围 - 或 | 的解析结果结构体 */
typedef struct
{    uint8_t range_type;        //0：未定义范围；1：定义了上下限；2：定义了有效值枚举
    uint32_t min_limit;
    uint32_t max_limit;
    char *valid_value_start;
}ST_ATC_CMD_PARAM_RANG;

/**
 * @brief  按照fmt格式解析每个参数值，类似scanf格式
 * @param fmt  [IN] AT命令参数对应的格式化字符串,其中()表示必选参数，[]表示可选参数。具体参数类型有：
 *
 *       %d,%1d,%2d分别对应int，char，short整形参数,支持16进制和10进制两类数值;后面还可以添加()表示必选参数，[]表示可选参数；括号内部可以使用-来指定参数值的上下限；
 *       如%d(0-100)表示可选4字节整形参数，取值范围为0到100，若解析该参数时发现不在此范围，会报错。括号内可以使用|来指定离散取值，
 *       如%d(0|0x20|0x100)表示可选4字节整形参数，取值为0/0x20/0x100三个值中的一个，若解析该参数时发现不是这三个值中的一个，会报参数错。
 *
 *       %s，对应字符串类型的参数；中间可以携带数字指示字符串缓存的内存大小，如%10s，若真实字符串长度大于9，则会报参数错误；
 *      
 *       %p，对应长字符串类型的参数，将对应长字符串首地址赋值给对应的参数，以供解析方直接使用；可单独使用，也可搭配%l使用
 *
 *       %h，对应16进制字符串类型的参数，接口内部会将16进制码流字符串转换成码流；一般搭配%l使用
 *
 *       %l，用法同%d，指示%p或%h对应字符串的传输长度，通常用于长度合法性检查；最多只能有一个%l，优先配合%h使用，若无%h，则配合%p使用，
 *
 * @param buf  [IN] AT字符串参数首地址，如"2,5,cmnet"
 * @param va_args ... [IN/OUT] 每个参数的地址空间，参数个数与fmt中的个数保持一致
 * @note 
 * @warning   该接口类似scanf形式，通过灵活的fmt格式化，来达到参数检错和解析一步到位，简化具体AT命令的开发。
 */
unsigned char AtcAp_CmdParamPrase(char *fmt, unsigned char *buf, ...);

/**
 * @brief 获取AT请求命令的前缀和参数首地址，仅内部使用！
 * @param at_cmd [IN] at cmd data
 * @param at_prefix [OUT] 仅返回AT命令前缀中有效字符串，不携带头尾标识，如“NRB” “ATI” “AT” "WORKLOCK"等
 * @param type [OUT] 返回AT请求命令的类型，@see @ref AT_REQ_TYPE
 */
char *AtcAp_CmdGetPrefixAndType(char *at_cmd, char *at_prefix, uint8_t *type);

/**
 * @brief  不区分大小写的字符串比较，常用于AT命令参数解析时，字符串参数的识别；例如“IPV6” “ipv6”
 */
uint8_t AtcAp_StrCaseCmp(const char *s1, const char *s2);

/**
 * @brief  内部使用！不区分大小写的子父字符串比较，其中n表示仅匹配父字符串前n字节。常用于字符串头部的匹配
 */
uint8_t AtcAp_StrCaseCmpWithNum(const char *s1, const char *s2, int n);
#endif