#if PS_TEST_MODE
/***********
注册命令定时器
注册用例定时器
1、处理AT发送
2、处理AT回显
3、处理定时器超时
*********/
#include "pstest.h"
#include "pstest_api.h"
#include "atc_ps.h"
#include "at_context.h"

void urc_cesq_Callback(unsigned long eventId, void *param, int paramLen)
{
    UNUSED_ARG(eventId);
    ATC_MSG_CESQ_IND_STRU* pCesqInfo;
    xy_assert(paramLen == sizeof(ATC_MSG_CESQ_IND_STRU));

    pCesqInfo = (ATC_MSG_CESQ_IND_STRU*)param;
    g_PSTestDemoInfo.stTestCpInfo.rsrp = pCesqInfo->ucRsrp;
    g_PSTestDemoInfo.stTestCpInfo.rssi = pCesqInfo->ucRxlev;
    xy_printf(0,ATC_AP_T,WARN_LOG,"[urc_cesq_Callback]rsrp:%d,rssi:%d ", g_PSTestDemoInfo.stTestCpInfo.rsrp, g_PSTestDemoInfo.stTestCpInfo.rssi);
}

void ps_test_case_timer_callback(void)
{
    ST_TEST_EVENT_HEAD* pData;
    pData = (ST_TEST_EVENT_HEAD*)AtcAp_Malloc(sizeof(ST_TEST_EVENT_HEAD));

    pData->ucEventID = PS_TEST_TIME_OUT;
    
    Ps_Send_to_Test_Msg(pData,&g_PSTestDemoInfo.stTestReceiveMsgInfo);
    return;

}

void ps_test_at_timer_callback(void)
{
    ST_TEST_EVENT_HEAD* pData;
    pData = (ST_TEST_EVENT_HEAD*)AtcAp_Malloc(sizeof(ST_TEST_EVENT_HEAD));
    
    pData->ucEventID = PS_TEST_AT_TIME_OUT;

    Ps_Send_to_Test_Msg(pData,&g_PSTestDemoInfo.stTestReceiveMsgInfo);
    return;
}

void ps_test_sim_state_callback(void)
{
    ST_TEST_EVENT_HEAD* pData;
    pData = (ST_TEST_EVENT_HEAD*)AtcAp_Malloc(sizeof(ST_TEST_EVENT_HEAD));
        
    pData->ucEventID = START_PS_TEST_FLG;
    
    Ps_Send_to_Test_Msg(pData,&g_PSTestDemoInfo.stTestSendMsgInfo);
    return;
}

/********************ps_test_send_at*************
function:AT回显，全局处理
************************************************/

void ps_test_save_wait_atkey(  UCHAR* pWaitForATkey, USHORT usAtkeyLen)
{
    osMutexAcquire(g_PSTestDemoInfo.stTestWaitAtKey.mutex, osWaitForever);

    if(NULL == g_PSTestDemoInfo.stTestWaitAtKey.aucAtRspBuf)
    {
        g_PSTestDemoInfo.stTestWaitAtKey.aucAtRspBuf = (unsigned char*)AtcAp_Malloc(D_TEST_RSP_MAX_BUF_SIZE);
    }
    g_PSTestDemoInfo.stTestWaitAtKey.usRspLen = usAtkeyLen;
    memcpy(g_PSTestDemoInfo.stTestWaitAtKey.aucAtRspBuf,pWaitForATkey,usAtkeyLen);

    osMutexRelease(g_PSTestDemoInfo.stTestWaitAtKey.mutex);
}

void ps_test_clean_wait_atkey(  )
{
    osMutexAcquire(g_PSTestDemoInfo.stTestWaitAtKey.mutex, osWaitForever);

    if(g_PSTestDemoInfo.stTestWaitAtKey.aucAtRspBuf != NULL)
    {
        AtcAp_Free(g_PSTestDemoInfo.stTestWaitAtKey.aucAtRspBuf);
    }
    g_PSTestDemoInfo.stTestWaitAtKey.usRspLen = 0;
    osMutexRelease(g_PSTestDemoInfo.stTestWaitAtKey.mutex);
}

/********************ps_test_send_at*************
function:发送AT
pATdata：具体的AT内容
pWaitForATkey：具体的AT回显内容
timeout：超时时间（单位:s）
attention：阻塞的接口，等待时长timeout，等待AT回显的信号量

************************************************/

UCHAR ps_test_send_at_wait_urc(UCHAR* pATdata, UCHAR* pKeyStr, UCHAR* pWaitForInfo,USHORT timeout)
{
    USHORT ulAtLen = 0;
    USHORT ulAtkeyLen = 0;
    UCHAR  ucShareInfo = 0;

    ulAtLen = strlen(pATdata);
    ulAtkeyLen = strlen(pKeyStr); 

    xy_printf(0,ATC_AP_T,WARN_LOG,"[ps_test_send_at]send AT:%s ;wait AT:%s ;waitTime: %d ", pATdata, pKeyStr,timeout);
    ps_test_wait_time(1);

    ps_test_save_wait_atkey(pKeyStr,ulAtkeyLen);
    at_recv_from_ps_test(pATdata, ulAtLen);

    Ps_test_set_share_info(13);

    osTimerStart(g_PSTestDemoInfo.tTestAtTime, timeout * 1000);

    osSemaphoreAcquire(g_PSTestDemoInfo.ATSemaPhore, osWaitForever);
    if(osTimerIsRunning(g_PSTestDemoInfo.tTestAtTime))
    {
        osTimerStop(g_PSTestDemoInfo.tTestAtTime);
    }
    ps_test_clean_wait_atkey();

    ucShareInfo = Ps_test_get_share_info();
    xy_printf(0,ATC_AP_T,WARN_LOG,"[ps_test_send_at]ucShareInfo %d %d",ucShareInfo,ucShareInfo & PS_TEST_GET_RIGHT_RCV);
    if((ucShareInfo & PS_TEST_GET_RIGHT_RCV) > 0)
    {
        if(pWaitForInfo == NULL)
        {
            return Test_True;
        }
        osMutexAcquire(g_PSTestDemoInfo.stTestMSGRcv.mutex, osWaitForever);
        // if(NULL == g_PSTestDemoInfo.stTestMSGRcv.pRspBuf)
        // {
        //     xy_printf(0,ATC_AP_T,WARN_LOG,"[ps_test_send_at] NULL");
        // }
        if(NULL != g_PSTestDemoInfo.stTestMSGRcv.pRspBuf
            && NULL != strstr(g_PSTestDemoInfo.stTestMSGRcv.pRspBuf, pWaitForInfo ))
        {
            osMutexRelease(g_PSTestDemoInfo.stTestMSGRcv.mutex);
            return Test_True;
        }
        osMutexRelease(g_PSTestDemoInfo.stTestMSGRcv.mutex);
    }
    xy_printf(0,ATC_AP_T,WARN_LOG,"[ps_test_send_at]FALSE");
    return Test_False;
}

/********************ps_test_send_at*************
function:监控主动上报
pWaitForATkey：具体的AT回显内容
timeout：超时时间（单位:s）
attention：阻塞的接口，等待时长timeout，等待AT回显的信号量
************************************************/

UCHAR ps_test_wait_urc(UCHAR* pWaitForATkey, USHORT timeout)
{
    USHORT ulAtkeyLen = 0;
    UCHAR  ucShareInfo = 0;

    ulAtkeyLen = strlen(pWaitForATkey); 

    xy_printf(0,ATC_AP_T,WARN_LOG,"[ps_test_wait_aturc]wait AT:%s ;waitTime: %d ", pWaitForATkey,timeout);

    ps_test_save_wait_atkey(pWaitForATkey,ulAtkeyLen);

    Ps_test_set_share_info(9);

    osTimerStart(g_PSTestDemoInfo.tTestAtTime, timeout * 1000);

    osSemaphoreAcquire(g_PSTestDemoInfo.ATSemaPhore, osWaitForever);
    if(osTimerIsRunning(g_PSTestDemoInfo.tTestAtTime))
    {
        osTimerStop(g_PSTestDemoInfo.tTestAtTime);
    }

    ps_test_clean_wait_atkey();

    ucShareInfo = Ps_test_get_share_info();
    if(ucShareInfo & PS_TEST_GET_RIGHT_RCV)
    {
        return Test_True;
    }
    else
    {
        return Test_False;
    }
}
/********************ps_test_send_at*************
wait_time
************************************************/

UCHAR ps_test_wait_time(USHORT timeout)
{
    //xy_printf(0,ATC_AP_T,WARN_LOG,"[ps_test_wait_time]waitTime: %d ", timeout);
    osDelay(timeout * 1000);

    return Test_True;

}
/************************************************
记录问题点时间
******************************************/
void Ps_test_save_question_info(void* pMsg, ST_TEST_AP_QUEST_INFO* pMsgInfo)
{
    ST_TEST_AP_MSG_NODE* pAddNode;

    pAddNode = (ST_TEST_AP_MSG_NODE*)osMemoryAlloc(sizeof(ST_TEST_AP_MSG_NODE));
    pAddNode->ulMsg = (unsigned long)pMsg;
    pAddNode->next = NULL;

    osMutexAcquire(pMsgInfo->msgMutex, osWaitForever);
    pMsgInfo->usCnt++;
    
    if(NULL == pMsgInfo->last)
    {
        pMsgInfo->head = pAddNode;
        pMsgInfo->last = pAddNode;
    }
    else
    {
        pMsgInfo->last->next = pAddNode;
        pMsgInfo->last = pAddNode;
    }
    osMutexRelease(pMsgInfo->msgMutex);
}

void Ps_Test_Remove_question_info(unsigned long* pRecvMsg, ST_TEST_AP_QUEST_INFO* pMsgInfo)
{
    ST_TEST_AP_MSG_NODE* pCurrNode;
  
    osMutexAcquire(pMsgInfo->msgMutex, osWaitForever);
    
    if(NULL == pMsgInfo->head)
    {
        osMutexRelease(pMsgInfo->msgMutex);
        return;
    }

    pCurrNode = pMsgInfo->head; 
    *pRecvMsg = pCurrNode->ulMsg;

    pMsgInfo->head = pCurrNode->next;
    if(NULL == pMsgInfo->head)
    {
        pMsgInfo->last = NULL;
    }
    pMsgInfo->usCnt--;
    osMutexRelease(pMsgInfo->msgMutex);

    osMemoryFree(pCurrNode);
    
    return;
}


/********************ps_test_send_at*************
接收URC

************************************************/
void ps_test_recv_at(UCHAR* pURC_data, USHORT ulDataLen)
{
    ST_TEST_AT_URC_INFO* pData;
    UCHAR  ucShareInfo = 0;
    if(g_softap_fac_nv->ucPSTestDemoFlg != 1)
    {
        return;
    }

    pData = (ST_TEST_AT_URC_INFO*)AtcAp_Malloc(sizeof(ST_TEST_AT_URC_INFO));
    
    pData->ucEventID = PS_TEST_AT_MSG;
    
    pData->aucAtRspBuf = (unsigned char*)AtcAp_Malloc(ulDataLen+1);
    memcpy(pData->aucAtRspBuf,pURC_data,ulDataLen);
    pData->usRspLen = ulDataLen;

    Ps_Send_to_Test_Msg(pData,&g_PSTestDemoInfo.stTestReceiveMsgInfo);
    return;
}

/********************ps_test_send_at*************
 get net time

************************************************/

void ps_test_get_net_time()
{   
    xy_walltime_t stNetTimeInfo = {0};
    unsigned char*  pTimeBuffer;
    
    if(xy_get_GMT_time(&stNetTimeInfo) == 0)
    {
        xy_get_RTC_time(&stNetTimeInfo);
    }
    if(stNetTimeInfo.tm_year == 1970)
    {
        return;
    }

    pTimeBuffer = AtcAp_Malloc(46);
    sprintf(pTimeBuffer,"%d%d%d%d/%d%d/%d%d,%d%d:%d%d:%d%d",
        (stNetTimeInfo.tm_year / 1000), ((stNetTimeInfo.tm_year / 100) % 10 ),
        ((stNetTimeInfo.tm_year / 10) % 10), (stNetTimeInfo.tm_year % 10),
        (stNetTimeInfo.tm_mon / 10), (stNetTimeInfo.tm_mon % 10),
        (stNetTimeInfo.tm_mday / 10), (stNetTimeInfo.tm_mday % 10),
        (stNetTimeInfo.tm_hour / 10), (stNetTimeInfo.tm_hour % 10),
        (stNetTimeInfo.tm_min / 10), (stNetTimeInfo.tm_min % 10),
        (stNetTimeInfo.tm_sec / 10), (stNetTimeInfo.tm_sec % 10));
    PrintUserLog(USER_LOG, DEBUG_LOG, "[LocalTime]%s", pTimeBuffer);
    AtcAp_Free(pTimeBuffer);
}

void Ps_test_at_task_rls()
{
    osSemaphoreRelease(g_PSTestDemoInfo.ATSemaPhore);
}

UCHAR Ps_test_get_share_info()
{
    unsigned char   ucBitInfo = 0;

    osMutexAcquire(g_PSTestDemoInfo.stTestShareInfo.Mutex, osWaitForever);
    ucBitInfo = g_PSTestDemoInfo.stTestShareInfo.ucShareInfo;
    osMutexRelease(g_PSTestDemoInfo.stTestShareInfo.Mutex);
    
    return ucBitInfo;
}
void Ps_test_set_share_info(unsigned char ucBitValue )
{
    osMutexAcquire(g_PSTestDemoInfo.stTestShareInfo.Mutex, osWaitForever);
    g_PSTestDemoInfo.stTestShareInfo.ucShareInfo = ucBitValue;
    osMutexRelease(g_PSTestDemoInfo.stTestShareInfo.Mutex);
    return;
}

#endif
